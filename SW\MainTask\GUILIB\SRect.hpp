#ifndef _SRECT_
#define _SRECT_

struct sRect
{
    void Set(int x1, int y1, int x2, int y2)
    {
        Left = (short) x1;
        Top = (short) y1;
        Right = (short) x2;
        Bottom = (short) y2;
    }

    void Set(POINT ul, POINT br)
    {
        Left = ul.x;
        Top = ul.y;
        Right = br.x;
        Bottom = br.y;
    }
#if 0	
    PEGBOOL Contains(POINT Test) const;
    PEGBOOL Contains(int x, int y) const;
    PEGBOOL Contains(const SRect &Rect) const;
    PEGBOOL Overlap(const SRect &Rect) const;
    void MoveTo(int x, int y);
    void Shift(int xShift, int yShift);
    SRect operator &=(const SRect &Other);
    SRect operator |= (const SRect &Other);
    SRect operator &(const SRect &Rect) const;
    SRect operator ^= (const SRect &Rect);
    SRect operator +(const SRect &Point) const; 
    SRect operator ++(int);
    SRect operator += (int x);
    SRect operator --(int);
    SRect operator -= (int x);
    PEGBOOL operator != (const SRect &Rect) const;
    PEGBOOL operator == (const SRect &Rect) const;
#endif	
                     
    int Width(void) const {return (Right - Left + 1);}
    int Height(void) const { return (Bottom - Top + 1);}

    short Left;
    short Top;
    short Right;
    short Bottom;
};



#endif	// End of _SRECT_

