#ifndef __SETUP_VOYAGE_DATA_MNU_WND_HPP__
#define __SETUP_VOYAGE_DATA_MNU_WND_HPP__

#include "Wnd.hpp"

class CSetupVoyageDataMnuWnd : public CWnd {
private:
	int m_nSelNum;

public:
	CSetupVoyageDataMnuWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	void OnKeyEvent(int nKey, DWORD nFlags);
	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	void DrawSubMenu(int nSelNum);
	void SetFocus(int nFocus)   { m_nFocus = nFocus; }
	void SetSelNum(int nSelNum) { m_nSelNum = nSelNum; }
	int  GetSelNum()            { return m_nSelNum;    }

	int CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
};

#endif	// End of __SETUP_VOYAGE_DATA_MNU_WND_HPP__

