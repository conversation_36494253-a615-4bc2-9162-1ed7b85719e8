#include "Sentence.hpp"

#ifndef __LR2_HPP__
#define __LR2_HPP__
/******************************************************************************
*
* LR2 - Long-range Reply for function requests "B, C, E, and F"
*
* $--LR2,x,xxxxxxxxx,xxxxxxxx,hhmmss.ss,llll.ll,a,yyyyy.yy,a,x.x,T,x.x,N*hh<CR><LF>
*        | |         |        |         |       | |        | |   | |   |
*        1 2         3        4         5       6 7        8 9  10 11  12
*
* 1.     Sequence Number , 0 to 9
* 2.     MMSI of responder
* 3.     Date: ddmmyyyy, 8 digits
* 4.     UTC time of position
* 5.     Latitude, N/S(position co-ordinate, to 1 min.)
* 6.     'N' or 'S'
* 7.     Longitude, E/W (position co-ordinate, to 1 min.)
* 8.     'E' or 'W'
* 9.10.  Course over ground True, value to nearest degree
* 11.12. Speed over ground value to 0.1 knot
*
******************************************************************************/
class CLr2 : public CSentence {
    protected:
		
		int    m_nSeqNum;
		int    m_nMMSIResp;
		char   m_szDate[9];
		int    m_nDay;
		int    m_nMonth;
		int    m_nYear;
		char   m_szUTC[10];
		int    m_nUTCHour;
		int    m_nUTCMin;
		int    m_nUTCSec;
		double m_dblLat;
		double m_dblLon;
        double m_dblCourseDeg;
		double m_dblSpeedKnot;
    public:
        CLr2();
        CLr2(char *pszSentence);

		void Parse();
		void SetSentence(char *pszSentence);
		int  GetFormat() { return m_nFormat; }
		void GetPlainText(char *pszPlainText);

		int  GetSeqNum()        { return m_nSeqNum;         }
		int  GetMMSIResp()      { return m_nMMSIResp;       }
		void GetDate(char szDate[9]) { strcpy(szDate, m_szDate); }
		int  GetDay()           { return m_nDay;            }
		int  GetMonth()         { return m_nMonth;          }
		int  GetYear()          { return m_nYear;           }
		void GetUTC(char szUTC[10])  { strcpy(szUTC, m_szUTC);   }
		int  GetUTCHour()       { return m_nUTCHour;        }
		int  GetUTCMin()        { return m_nUTCMin;         }
		int  GetUTCSec()        { return m_nUTCSec;         }
		double GetLat()         { return m_dblLat;          }
		double GetLon()         { return m_dblLon;          }
		double GetCourseDeg()   { return m_dblCourseDeg;    }
		double GetSpeedKnot()   { return m_dblSpeedKnot;    }

};
		
#endif
