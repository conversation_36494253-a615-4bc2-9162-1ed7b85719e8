#ifndef _TIDELIB_CONFIG_H_
#define	_TIDELIB_CONFIG_H_
/********************************************************/
/************ <PERSON>UFF YOU MIGHT WANT TO CHANGE ************/
/********************************************************/
/* mgh Default location and name of configuration file */
/* mgh This is overridden the -config switch. */
#define winconfigFN "WXTide32.cfg"

/* mgh Default location and name of station index file */
/* mgh This is overridden the -indexfile switch. */
#define indexfile "harmonic.idx"

/* mgh Default location and name of user station index file */
/* mgh This is overridden the -userfile switch. */
#define userfile "userfile.idx"

/* Default location and name of harmonics file */
/* This is overridden by the HFILE environment variable or by the -hfile
   switch. */
#ifdef OS2
#define hfile "harmonic"
#else
#define hfile "harmonic"
#endif

/* Default location to show tides for */
/* This is overridden by the LOCATION environment variable or by the
   -location switch. */
#define deflocation "Baltimore (Fort McHenry), Maryland"

/* Define this to enable Dean's GIF code.  Requires Tom Boutell's
   GD gif-manipulating library, which is available from
   http://www.boutell.com/gd/
*/
/* #undef DEANGIF */

/*****************************************************************/
/************ STUFF YOU PROBABLY SHOULDN'T MESS WITH *************/
/*****************************************************************/

#define WXTIDE32VERSIONNUMBER "4.7"
#define WXTIDE32VERSIONMAJOR 4
#define WXTIDE32VERSIONMINOR 7
#define WXTIDE32VERSIONDATE "February 25,2007"
#define VERSION "1.6"
#define PATCHLEVEL 2

/* Define NO_LUDICROUS_SPEED to disable Jeff Dairiki's iterative
   approximation code.  With NO_LUDICROUS_SPEED, you get the old tried
   and true brute force code.

   If using Jeff's code, you can also define DO_BISECTION to back
   down to his merely warp-speed bisection code instead of the fancy
   modified Newton-Raphson root finder.
*/
/* #define NO_LUDICROUS_SPEED */
/* #undef DO_BISECTION */

/* Silly option to use /dev/urandom for -location random.  AFAIK, only
   Linux supports /dev/random and /dev/urandom.  Enabling this option
   will degrade the security of TCP/IP and will run down the supply of
   random numbers that may be needed by more important applications. */
/* #define URANDOM */
#endif
