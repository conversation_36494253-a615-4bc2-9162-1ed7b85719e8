#ifndef __CMG
#define __CMG

/* Standard Draw & Fill operators */
#define cmgOVER				0
#define cmgCOPY				cmgOVER
#define cmgXOR				1
#define cmgTRANSP			2
#define	cmgFILLSTYLE		3		/* Only for C-Map chart plotter			*/
#define cmgMASKPEN			4		/* Only for Windows Projects - DEBUG	*/
#define cmgTLINESTYLE		5		/* _|_|_|_ __|__|__ */
#define cmgPLUSLINESTYLE	6		/* -+-+-+- --+--+-- */


/* Special Defines for TLINESTYLE and PLUSLINESTYLE */
#define cmgTICK_STEP_2		2
#define cmgTICK_STEP_4		4
#define cmgTICK_STEP_8		8
#define cmgTICK_STEP_16		16

#define cmgTICK_LENGTH(l)				((l & 0x0f) << 8)

#define cmgTICK_STYLE(Step, Length)		(Length | Step)

#define cmgGET_TICK_LENGTH(Style)		(((Style) & 0x0F00) >> 8)
#define cmgGET_TICK_STEP(Style)			((Style) & 0x00FF)



/* Standard Colors */
#define cmgBLACK        0
#define cmgDARK_RED     1
#define cmgDARK_BROWN   2
#define cmgLIGHT_RED    3
#define cmgDARK_BLUE    4
#define cmgDARK_GREEN   5
#define cmgDARK_GRAY    6
#define cmgORANGE       7
#define cmgMAGENTA      8
#define cmgLIGHT_BROWN  9
#define cmgLIGHT_BLUE   10
#define cmgLIGHT_GREEN  11
#define cmgCYAN         12
#define cmgLIGHT_GRAY   13
#define cmgLIGHT_YELLOW 14
#define cmgWHITE        15

#define cmgPALE_GREEN	21
#define cmgPALE_GRAY	22
#define cmgPALE_BLUE	23
#define cmgMID_BROWN	24
#define cmgVEG_GREEN	25

#define cmgLNDARE_CONTOUR		27

/* ----- Terrestrial v5.x only: start ----- */
#define cmgRED_1		28
#define cmgRED_2		29
#define cmgRED_3		30
#define cmgRED_4		31
#define cmgRED_5		32
#define cmgPINK_1		33
#define cmgORANGE_1		34
#define cmgORANGE_2		35
#define cmgBROWN_1		36
#define cmgBROWN_2		37
#define cmgBROWN_3		38
#define cmgBROWN_4		39
#define cmgGREEN_1		40
#define cmgGREEN_2		41
#define cmgGREEN_3		42
#define cmgGREEN_4		43
#define cmgGREEN_5		44
#define cmgGREEN_6		45
#define cmgBLUE_1		46
#define cmgBLUE_2		47
#define cmgBLUE_3		48
#define cmgYELLOW_1		49
#define cmgYELLOW_2		50
#define cmgYELLOW_3		51
#define cmgYELLOW_4		52
#define cmgGRAY_1		53	
#define cmgGRAY_2		54
#define cmgGRAY_3		55
#define cmgGRAY_4		56
#define cmgGRAY_5		57
#define cmgGRAY_6		58
#define cmgGRAY_7		59
#define cmgGRAY_8		60
#define cmgGRAY_9		61
#define cmgGRAY_10		62
#define cmgBROWN_5		63

/* ----- Terrestrial v5.x only: end ----- */

/******************************/
/*    Dynamic  Coloring       */
/******************************/
#define cmgDEPBLU_0		70
#define cmgDEPBLU_1		71
#define cmgDEPBLU_2		72
#define cmgDEPBLU_3		73
#define cmgDEPBLU_4		74
#define cmgDEPBLU_5		75
#define cmgDEPBLU_6		76
#define cmgDEPBLU_7		77
#define cmgDEPBLU_8		78
#define cmgDEPBLU_9		79
#define cmgDEPBLU_10	80
#define cmgDEPBLU_11	81
#define cmgDEPBLU_12	82
#define cmgDEPBLU_13	83
#define cmgDEPBLU_14	84
#define cmgDEPBLU_15	85
#define cmgDEPBLU_16	86
#define cmgDEPBLU_17	87
#define cmgDEPBLU_18	88
#define cmgDEPBLU_19	89
#define cmgDEPBLU_20	90
#define cmgDEPBLU_21	91
#define cmgDEPBLU_22	92
#define cmgDEPBLU_23	93
#define cmgDEPBLU_24	94
#define cmgDEPBLU_25	95
#define cmgDEPBLU_26	96
#define cmgDEPBLU_27	97
#define cmgDEPBLU_28	98
#define cmgDEPBLU_29	99
#define cmgDEPBLU_30	100
#define cmgDEPBLU_31	101

#define cmgELVBRW_0		102
#define cmgELVBRW_1		103
#define cmgELVBRW_2		104
#define cmgELVBRW_3		105
#define cmgELVBRW_4		106
#define cmgELVBRW_5		107
#define cmgELVBRW_6		108
#define cmgELVBRW_7		109
#define cmgELVBRW_8		110
#define cmgELVBRW_9		111
#define cmgELVBRW_10	112
#define cmgELVBRW_11	113

#define cmgFILLTransp	127

/******************************/

#define cmgDEPTH_BLUE_0					cmgDEPBLU_0
#define cmgDEPTH_BLUE_1					cmgDEPBLU_3
#define cmgDEPTH_BLUE_2					cmgDEPBLU_6
#define cmgDEPTH_BLUE_3					cmgDEPBLU_9
#define cmgDEPTH_BLUE_4					cmgDEPBLU_12
#define cmgLOW_TIDAL_STREAM_COLOR		cmgLIGHT_YELLOW		
#define cmgMED_TIDAL_STREAM_COLOR		cmgORANGE
#define cmgHGH_TIDAL_STREAM_COLOR		cmgLIGHT_RED
#define cmgTIDAL_STREAM_CONTOUR_COLOR	cmgBLACK

/************************************/
/*             END                  */
/************************************/


#define cmgLOGICAL		0x80

                               /* Raw font constant for cmgSetDrawFont */
#define cmgDEFAULT_FONT ((const FontType *)0)

#define cmgLFIRST_FONT   cmgLDEFAULT_FONT
#define cmgLDEFAULT_FONT	0     /* Logic font constants to use with cmgSetFont */
#define cmgLSMALL_FONT		1
#define cmgLNORMAL_FONT		2
#define cmgLBIG_FONT		3
#define cmgLTYPE_12x16		4

#define cmgLTYPE_I			5
#define cmgLTYPE_S			6
#define cmgLTYPE_L			7
#define cmgLTYPE_H			8
#define cmgLTYPE_R			9
#define cmgLTYPE_A			10
#define cmgLTYPE_B			11
#define cmgLTYPE_C			12
#define cmgLGRID_FONT		13
#define cmgLLAST_FONT		cmgLGRID_FONT

#define cmgDATA_EXPLICIT   0   /* Font and Symbol data type */
#define cmgDATA_IMPLICIT   1
#define cmgDATA_TTFBITMAP  2

#define cmgCHAR_DEFAULT    0
#define cmgCHAR_NOSTYLE    cmgCHAR_DEFAULT
#define cmgCHAR_OUTLINE    1
#define cmgCHAR_BOLD       2
#define cmgCHAR_SHADOW     3

#define cmgCHAR_MODE_TRANSP	0
#define cmgCHAR_MODE_FADE	1

#define cmgICON_MEM_TYPE				0
#define cmgICON_CDG_TYPE				1
#define cmgICON_MEM_TRUE_COLOR_TYPE		2

#define CMG_DISABLE_LINK	0x8000		/* Disable draw of "Link point" when drawing a Wide Line */

typedef enum
   {
   cmg_NO_ERROR = 0,
   cmg_UNKNOWN_HARDWARE,
   cmg_GENERIC_ERROR
   } cmgErr ;

typedef enum
{
	CMG_UnknownBPP = 0,
	CMG_32BPP,
	CMG_24BPP,
	CMG_16BPP,
	CMG_8BPP
}eCMGColorDepth;

typedef struct
   {
   SWord dForeColor, dBackColor ; /* Drawing Color (Fore & Background       */
   SWord dForeOper,  dBackOper  ; /* Drawing Operation (Fore & Background)  */
   SWord dLineWidth;              /* Drawing line width                     */
   SWord dPattern ;               /* Drawing Pattern                        */
   } DrawStyleType ;

/* Extended DrawStyleType */
typedef struct
   {
   SWord iWideLine;

   DrawStyleType	iCenter;
   DrawStyleType	iMiddle;
   DrawStyleType	iContour;

   } DrawStyleTypeExt ;


typedef struct
   {
   SWord fForeColor, fBackColor ; /* Filling Color (Fore & Background       */
   SWord fForeOper,  fBackOper  ; /* Filling Operation (Fore & Background)  */
   SWord fPattern[16] ;           /* Fill Pattern (16x16)                   */
   } FillStyleType ;

typedef struct
   {
   SWord cMultX, cMultY ;         /* Base size multipliers (x & y)          */
   SWord cRotation ;              /* Rotation (in degrees)                  */
   SWord cStyle ;
   SWord cStyleColor ;
   SWord cStyleMode ;
   } CharStyleType ;

typedef struct
   {
   SWord ClipOn ;                       /* Non zero when clip is active     */
   SWord cLeft, cBottom, cRight, cTop ; /* clip rectangle in pixels         */
   } ClipType ;


typedef struct 
{
   SWord cLeft, cBottom, cRight, cTop ; /* rectangle in pixels         */
} ChartRegionType;


typedef struct
   {
   SWord OfsX ;                 /* application point offset           */
   SWord OfsY ;
   SWord Width ;                /* symbol size                        */
   SWord Height ;
   Byte DefType;                /* Implicit, explicit data definition */
   const void *Data ;           /* Pointer to font data               */
   }ExtSymType ;

typedef struct
   {
   Byte cWidth ;         /* Font width (x size) in pixels          */
   Byte cHeight ;        /* Font height (y size) in pixels         */
   Byte DefType;         /* Implicit, explicit data definition     */
   Byte SpecialAttrs;	 /* If implicit, additional text attributes */
   void *Data ;          /* Pointer to font data                   */
   } FontType ;

typedef struct
   {
   Byte ForeColor;
   Byte BackColor;
   Word DimX;
   Word DimY;
   Byte ColorPlanes;
   Word HotSpotX;
   Word HotSpotY;
   Byte DefType ;
   Byte CdgNum;
   Long Address;
   } IconType ;

typedef struct
   {
   Long TranspColor;
   const Byte *IconAddress;
   } TrueColorIconType ;

/*#if TTF_NEW_MANAGEMENT*/
typedef struct sSubSymbol
{
	SWord HotSpotX;
	SWord HotSpotY;
	SWord BackColor;
	SWord ForeColor;
	SWord ForeOper;
	SWord BackOper;
	Byte Code;
}SubSymbol;


typedef struct sTrueTypeSymbol
{
	Byte numSymbol;
	Byte CdgNum;
	SubSymbol Symbol[10];
	Byte FontName;
	Byte Factor;
}TrueTypeSymbol;
/*endif*/

typedef struct
   {
   Byte ForeColor;
   Byte BackColor;
   Word DimX;
   Word DimY;
   Byte ColorPlanes;
   Word HotSpotX;
   Word HotSpotY;
   Byte DefType ;
   Byte CdgNum;
   const Byte *Data ;
   } MemIconType ;


typedef struct
{
	SWord cMultX, cMultY ;         /* Base size multipliers (x & y)          */
} IconStyleType ;

/*
 *-------------------------------------------------------------------------
 *  CMG Proportional Font Structures and defines
 *-------------------------------------------------------------------------
 */
#define cmgPROPORTIONAL_FONT    0x80

typedef struct
{
	SByte A;
	Byte  SizeX;
	SByte C;
	Word  Offset;
} CharInfo_t;

typedef struct
{
	Word NumChar;
    Word FirstChar;
    Word BaseLine;
	Word InternalLeading;
	CharInfo_t *Table;
	Byte *Data;
} FontInfo_t;

typedef struct
{
	SWord SX;
	SWord SY;
	Word BL;				/* BaseLine			*/
	Word IL;				/* InternalLeading	*/
} TextExtentAndMetrics_t;

typedef struct
{
	Byte	FontFamily;
	Bool	Bold;
	Bool	Italic;
	Bool	Outline;
	Bool	Underline;
	Bool	Marker;
	Byte	FontSize;
} LogicalFont_t;

/*********True Type Section**********/
typedef enum
{
	fmAntiAliasing,
	fmMono,
	fmOutLineMono,		
	fmOutLineAliasing	
}tFontMode;

typedef struct BitmapFont_t
{
	Word rows;
	Word width;
	Byte* buffer;
	tFontMode mode;
}sBitmapFont;

typedef struct TTFBitmap_t
{
	SWord AdvanceX;					/* The distance between two successive character. */
	SWord AdvanceY;
	SWord Ascender;					/* The distance from the baseline to the highest/upper grid coordinate used to place an outline point. */
	SWord Descender;				/* The distance from the baseline to the lowest grid coordinate used to place an outline point. */
	SWord Left;						/* Offset X of single bitmap */
	SWord Top;						/* Offset Y from baseline to top of bitmap */
	SWord MaxPenX;
	SWord MaxPenY;
	Byte isFirstChar;
	Byte isLastChar;
	sBitmapFont *Bitmap;
} sTTFBitmap;

typedef enum
{
	CMG_COLOR_MODE_565_RGB = 0,		/* Available for CMG with 16/32 BitPerPixel. */
	CMG_COLOR_MODE_5551_RGBA,		/* Available for CMG with 16/32 BitPerPixel. */
	CMG_COLOR_MODE_8888_ARGB,		/* Available for CMG with 32 BitPerPixel. */
	CMG_COLOR_MODE_8888_ABGR,		/* Available for CMG with 32 BitPerPixel. */
	CMG_COLOR_MODE_8BIT_INDEXED		/* Available for CMG with 8/16/32 BitPerPixel. */
}eCMGPixelFormat;

typedef struct nFBAttrib
{
	Long Address;
	Pixel Height;
	Pixel Width;
	Pixel			ClipMinX;
	Pixel			ClipMinY;
	Pixel			ClipMaxX;
	Pixel			ClipMaxY;
	Long			CMGCustomSettings;
	eCMGPixelFormat	PixelFormat;
	Byte bpp;
}FBAttrib;

typedef enum
{
	is_Text=0,
	is_Symbol
}
eTextType;

typedef enum
{
	DefaultQuality=0,
	AntialiasedQuality
}
eTextQuality;

typedef struct TTFFont_t 
{ 
  /****Text Information****/
  SWord		lfHeight;					/*Font Height*/
  SWord		lfWidth;					/*Font Width*/
  Bool		lfBold;						/*Bold Style*/
  Bool		lfItalic;					/*Italic Style*/
  Bool		lfUnderline;				/*Underline Style*/
  Bool		lfOutline;					/*Outline Style*/
  eTextQuality	lfQuality;				/*Render quality: Mono or Anialiased*/
  SWord		lfRotation;					/*Rotation angle in degree*/
  SWord		lfFamily; 
  String	lfFaceName[32]; 
  Byte		lfFaceIndex;

  /****Symbol Information****/
  eTextType TextType;					/*Font Type : Text or Symbol */
  SWord	 	HotSpotX;					/*HotSpotX (in Font logical unit)*/
  SWord	 	HotSpotY;					/*HotSpotY (in Font logical unit)*/
  SLong		Divisor;					/*Divisor to enlarge or reduce size according with the screen resolution*/
} sTTFFont; 

/******************************************/
/*
 *-------------------------------------------------------------------------
 *  CMG functions prototypes
 *-------------------------------------------------------------------------
 */

#ifdef __cplusplus
extern "C"
   {
#endif

PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgInit( void );
PRE_EXPORT_H void      IN_EXPORT_H cmgClose(void);

/*
 *  DrawStyle
 */

PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetDrawStyle( const DrawStyleType *DrawStyle ) ;
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetDrawStyleV( SWord dForeColor, SWord dBackColor, SWord dForeOper, SWord dBackOper, SWord dLineWidth, SWord dPattern ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgGetDrawStyle( DrawStyleType *DrawStyle ) ;

PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetDrawStyleExt( const DrawStyleTypeExt *DrawStyle ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgGetDrawStyleExt( DrawStyleTypeExt *DrawStyle ) ;

PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetWideLineStyle( const DrawStyleType *ContourStyle, const DrawStyleType *MiddleStyle, const DrawStyleType *CenterStyle ) ;
PRE_EXPORT_H Bool	   IN_EXPORT_H cmgGetWideLineStyle( const DrawStyleType *ContourStyle, const DrawStyleType *MiddleStyle, const DrawStyleType *CenterStyle ) ;
PRE_EXPORT_H void	   IN_EXPORT_H cmgSetGrayMode( Bool OnOff );

/*
 *  Font & Char style
 */

PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetDrawFont( const FontType *Font ) ;
PRE_EXPORT_H FontType* IN_EXPORT_H cmgGetDrawFont( void ) ;
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetCharStyle( const CharStyleType *CharStyle ) ;
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetCharStyleV( SWord cMultX, SWord cMultY, SWord cRotation ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgGetCharStyle( CharStyleType *CharStyle ) ;

PRE_EXPORT_H void      IN_EXPORT_H cmgSetFont( SWord FontCode ) ;
PRE_EXPORT_H SWord     IN_EXPORT_H cmgGetFont( void ) ;

/* Note: The font size is in PICA POINT */
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetFontEx( LogicalFont_t *LogFont ) ;
PRE_EXPORT_H void	   IN_EXPORT_H cmgGetFontEx( LogicalFont_t *LogFont ) ;

PRE_EXPORT_H void IN_EXPORT_H  cmgGetTTFFont(sTTFFont *lf);
PRE_EXPORT_H void IN_EXPORT_H cmgSetTTFFont(sTTFFont *lf);

/*
 *  Clipping
 */
PRE_EXPORT_H cmgErr	   IN_EXPORT_H  cmgSetDrawingAreaSize( Pixel Width,Pixel Height);
PRE_EXPORT_H cmgErr    IN_EXPORT_H  cmgGetDrawingAreaSize( Pixel *Width,Pixel *Height);

PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetClipMode( const ClipType *ClipMode ) ;
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetClipRegion( SWord cLeft, SWord cBottom, SWord cRight, SWord cTop ) ;
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetClip( SWord Clip ) ;
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgGetClipMode( ClipType *ClipMode ) ;

/*
 *  Current Position (CP)
 */

PRE_EXPORT_H void      IN_EXPORT_H cmgMoveTo( Pixel x, Pixel y ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgGetXY(Pixel *x, Pixel *y) ;

/*
 *  Line / Poly system.
 */

PRE_EXPORT_H void      IN_EXPORT_H cmgPolyLineInit( void ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgPolyLineStart( void ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgPolyLineEnd( void ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgPolyLineFlush( void ) ;

PRE_EXPORT_H void      IN_EXPORT_H cmgLineTo( Pixel x, Pixel y ) ;

PRE_EXPORT_H void      IN_EXPORT_H cmgPolyFillInit( void ) ;
PRE_EXPORT_H Bool      IN_EXPORT_H cmgSetFillStyle( const FillStyleType *FillStyle ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgGetFillStyle(FillStyleType *FillStyle );
PRE_EXPORT_H void      IN_EXPORT_H cmgPolyFill( void ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgPolyFillFlush( Bool Init ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgPolyOpen( void ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgPolyClose( void ) ;

/*
 *  Symbols & Strings
 */

PRE_EXPORT_H void      IN_EXPORT_H cmgDrawStr( const String *s ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgDrawSym( SWord sCode ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgDrawExtSym( const ExtSymType *ExtSym ) ;

PRE_EXPORT_H void      IN_EXPORT_H cmgGetTextExtent(String *string, SWord *SX, SWord *SY, SWord *BL);
PRE_EXPORT_H void      IN_EXPORT_H cmgGetTextExtentAndMetrics(String *string, TextExtentAndMetrics_t *EM);
PRE_EXPORT_H void	   IN_EXPORT_H cmgGetTextABCWidths(String *string,SWord *A,SWord *B, SWord *C);

#if cmgUnicodeSupportEnabled
PRE_EXPORT_H void      IN_EXPORT_H cmgDrawUnicodeStr( const UnicodeString *s ) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgGetUnicodeTextExtent(UnicodeString *string, SWord *SX, SWord *SY, SWord *BL);
PRE_EXPORT_H void      IN_EXPORT_H cmgGetUnicodeTextExtentAndMetrics(UnicodeString *string, TextExtentAndMetrics_t *EM);
PRE_EXPORT_H void	   IN_EXPORT_H cmgGetUnicodeTextABCWidths(UnicodeString *string,SWord *A,SWord *B, SWord *C);
#endif

/*
 *  Icons
 */

PRE_EXPORT_H void      IN_EXPORT_H cmgIconInit( const IconType *Icon );
PRE_EXPORT_H void      IN_EXPORT_H cmgIconDraw( const IconType *Icon );
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetIconStyle( const IconStyleType *IconStyle ) ;
PRE_EXPORT_H cmgErr    IN_EXPORT_H cmgSetIconStyleV( SWord cMultX, SWord cMultY) ;
PRE_EXPORT_H void      IN_EXPORT_H cmgGetIconStyle( IconStyleType *IconStyle ) ;

/*
 *  Images
 */

PRE_EXPORT_H void IN_EXPORT_H cmgGetImage(void* buff, Pixel left, Pixel bottom, Pixel right, Pixel top);
PRE_EXPORT_H void IN_EXPORT_H cmgSetImage(void* buff, Pixel left, Pixel bottom, Pixel right, Pixel top);
PRE_EXPORT_H Long IN_EXPORT_H cmgGetImageSize( SWord Width, SWord Height );
PRE_EXPORT_H void IN_EXPORT_H cmgSetRasterPalette(Long* pPalette, Word NumOfPaletteEntry, void* PtrImg);
PRE_EXPORT_H void IN_EXPORT_H cmgSetRasterTranspColor(Long TranspColor, void* PtrImg);
PRE_EXPORT_H void IN_EXPORT_H cmgWriteRasterImage(Pixel Bottom,Pixel Left,void* PtrImg, Byte BlurFactor);

PRE_EXPORT_H void IN_EXPORT_H cmgGetPaletteEntryRGB(Word Index, Byte *Red, Byte *Green, Byte *Blue);
PRE_EXPORT_H void IN_EXPORT_H cmgGetCMGLogicalPaletteEntryRGB( Word Index, Byte *Red, Byte *Green, Byte *Blue );
PRE_EXPORT_H Word IN_EXPORT_H cmgGetNumPaletteEntry(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmgTestAndResetPaletteChangeStatus(void);
PRE_EXPORT_H eCMGColorDepth IN_EXPORT_H cmgGetColorDepth(void);
PRE_EXPORT_H cmgErr IN_EXPORT_H cmgSetRasterImage(void* PtrImg,Pixel Width, Pixel Height, Byte ColorDepth);
PRE_EXPORT_H void IN_EXPORT_H cmgResetRasterImage(void* PtrImg);

/*
 *  Screen Resolution
 */

PRE_EXPORT_H void IN_EXPORT_H cmgSetScreenRes(Double ResPixelPerM);
PRE_EXPORT_H Double IN_EXPORT_H cmgGetScreenRes(void);

/*
 *  Alpha Blend
 */

PRE_EXPORT_H void IN_EXPORT_H cmgSetAlphaBlend( Byte Alpha );

/*
 *  Frame Buffer Management
 */

PRE_EXPORT_H FBAttrib IN_EXPORT_H cmgSetFrameBufferAddr(FBAttrib* Attrib);
PRE_EXPORT_H FBAttrib IN_EXPORT_H cmgGetFrameBufferAddr(void);

PRE_EXPORT_H eCMGPixelFormat IN_EXPORT_H cmgGetFrameBufferPixelFormat(void);
PRE_EXPORT_H eCMGPixelFormat IN_EXPORT_H cmgSetFrameBufferPixelFormat(eCMGPixelFormat PixelFormat);


#ifdef __cplusplus
	}
#endif


#endif

