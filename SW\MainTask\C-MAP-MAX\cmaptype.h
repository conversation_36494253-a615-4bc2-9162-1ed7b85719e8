#ifndef __CMAPTYPE
#define __CMAPTYPE

#ifdef DEBUG_INCLUDES
#pragma message( "+++++++++ including cmaptype.h +++++++++" )
#endif

#ifndef ON
	#define ON         1
#endif

#ifndef SI
	#define SI         1
#endif

#ifndef YES
	#define YES        1
#endif

#ifndef TRUE
	#define TRUE       1
#endif

#ifndef OFF
	#define OFF        0
#endif

#ifndef NO
	#define NO         0
#endif

#ifndef FALSE
	#define FALSE      0
#endif

#ifndef NULL
	#define NULL      0
#endif


/* C-MAP library types */
#ifndef SByte
	#define SByte signed char
#endif

#ifndef Byte
	#define Byte unsigned char
#endif

#ifndef SWord
	#define SWord signed short int
#endif

#ifndef Word
	#define Word unsigned short int
#endif

#ifndef SLong
	#define SLong signed long
#endif

#ifndef Long
	#define Long unsigned long
#endif

#ifndef Float
	#define Float float
#endif

#ifndef Double
	#define Double double
#endif

#ifndef String
	#define String char
#endif
#ifndef UnicodeString
    #ifdef UNICODE_SUPPORT 
        /* this is for unicode */
    	#define UnicodeString Word
		#define UnsignedUnicodeString Word
    #else
        /* this is for ascii */
    	#define UnicodeString char
		#define UnsignedUnicodeString unsigned char
    #endif

    #ifdef CMG_UNICODE_SUPPORT 
        /* 1 if cmg supports unicode otherwise 0 */
    	#define cmgUnicodeSupportEnabled    1
    #else
        /* 1 if cmg supports unicode otherwise 0 */
    	#define cmgUnicodeSupportEnabled    0
    #endif

#endif

#ifndef Bool
	#define Bool Byte
#endif

typedef struct 
{
	/*Don't switch this fields*/
	Long	BytePtr; 
	Byte	BitPtr;
}sBitsPtr;

#ifndef BitsPtr
	#define BitsPtr Long
#endif

#ifndef Pixel
	#define Pixel SWord
#endif

#ifndef tCellPixel
	#define  tCellPixel SWord
#endif

#ifndef WINDOWS32BITDLL
	#define WINDOWS32BITDLL 1
#endif

#ifndef WINDOWS16BITDLL
	#define WINDOWS16BITDLL 2
#endif

#ifndef MAX_LONG
	#define MAX_LONG  (Long)(0xFFFFFFFF) 
#endif

#ifndef MAX_WORD
	#define MAX_WORD  (Word)(0xFFFF)
#endif

#ifndef MAX_SLONG
	#define MAX_SLONG (SLong)(0x7FFFFFFF)
#endif

#ifndef MAX_SWORD
	#define MAX_SWORD (SWord)(0x7FFF)
#endif

#ifndef MIN_SLONG
	#define MIN_SLONG (SLong)(0x80000000)
#endif

#ifndef MIN_SWORD
	#define MIN_SWORD (SWord)(0x8000)
#endif

#ifndef DLL_EXPORT
	#define PRE_EXPORT_H /* */
	#define IN_EXPORT_H /* */
	#define PRE_EXPORT_C /* */
	#define IN_EXPORT_C /* */
	#define CM_WIN32_EXPORT /* */
	#define CM_WIN16_EXPORT /* */
#else
	#if ( DLL_EXPORT==WINDOWS16BITDLL )
		#if (__BORLANDC__ < 0x500)
			#define PRE_EXPORT_H /* */
			#define IN_EXPORT_H _export far pascal
			#define PRE_EXPORT_C /* */
			#define IN_EXPORT_C _export far pascal
			#define CM_WIN32_EXPORT /* */
			#define CM_WIN16_EXPORT _export far pascal
		#else
			#define PRE_EXPORT_H __declspec(dllexport)
			#define IN_EXPORT_H far pascal
			#define PRE_EXPORT_C /* */
			#define IN_EXPORT_C far pascal
			#define CM_WIN32_EXPORT __declspec(dllexport)
			#define CM_WIN16_EXPORT far pascal
		#endif
	#elif ( DLL_EXPORT==WINDOWS32BITDLL )
		#if (__BORLANDC__ >= 0x500 || VB_EXPORT==1)
			#define PRE_EXPORT_H __declspec(dllexport)
			#define IN_EXPORT_H  _stdcall
			#define PRE_EXPORT_C  __declspec(dllexport) 
			#define IN_EXPORT_C  _stdcall
			#define CM_WIN32_EXPORT __declspec(dllexport)
			#define CM_WIN16_EXPORT  _stdcall
		#else
			#define PRE_EXPORT_H __declspec(dllexport)
			#define IN_EXPORT_H /* */
			#define PRE_EXPORT_C  __declspec(dllexport) 
			#define IN_EXPORT_C /* */
			#define CM_WIN32_EXPORT __declspec(dllexport)
			#define CM_WIN16_EXPORT /* */
		#endif
	#else
		#define PRE_EXPORT_H /* */
		#define IN_EXPORT_H /* */
		#define PRE_EXPORT_C /* */
		#define IN_EXPORT_C /* */
		#define CM_WIN32_EXPORT /* */
		#define CM_WIN16_EXPORT /* */
	#endif
#endif

#endif

