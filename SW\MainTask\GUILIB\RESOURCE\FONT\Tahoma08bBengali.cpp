/*...........................................................................*/
/*.                  File Name : Tahoma08bBengali.cpp                       .*/
/*.                                                                         .*/
/*.                       Date : 2014.04.10                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma08nArabic_Font;

ROMDATA PEGUSHORT Tahoma08bBengali1_offset_table[129] = {
0x0000,0x000d,0x001a,0x0027,0x0034,0x0041,0x0049,0x0054,0x0060,0x006c,0x0074,0x007c,0x0085,0x0092,0x009e,0x00ab,
0x00b5,0x00c2,0x00ca,0x00d4,0x00e1,0x00ea,0x00f3,0x0100,0x010d,0x011a,0x0127,0x0132,0x013b,0x0144,0x014d,0x0156,
0x015f,0x0167,0x016f,0x0177,0x0184,0x0191,0x019c,0x01a5,0x01ad,0x01b6,0x01bf,0x01c8,0x01d5,0x01de,0x01e7,0x01f0,
0x01f6,0x01ff,0x020a,0x0217,0x021e,0x0226,0x022e,0x0237,0x0240,0x0249,0x0252,0x025b,0x0262,0x026f,0x0278,0x0285,
0x0292,0x029a,0x02a3,0x02ab,0x02b4,0x02bd,0x02c6,0x02cf,0x02dc,0x02e9,0x02f6,0x02fe,0x030b,0x0318,0x0325,0x0332,
0x033f,0x034c,0x0359,0x035f,0x0366,0x036d,0x037a,0x0381,0x038e,0x039b,0x03a8,0x03b5,0x03c2,0x03cf,0x03dc,0x03e9,
0x03f6,0x0403,0x0410,0x041d,0x042a,0x0437,0x0444,0x0451,0x045e,0x046b,0x0478,0x0485,0x0492,0x049f,0x04ac,0x04b9,
0x04c6,0x04d3,0x04e0,0x04ed,0x04fa,0x0507,0x0514,0x0521,0x052e,0x053b,0x0548,0x0555,0x0562,0x056f,0x057c,0x0589,
0x0596};



ROMDATA PEGUBYTE Tahoma08bBengali1_data_table[2864] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x7f, 0xe3, 0xff, 0x00, 0x00, 0x00, 0x07, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0x00, 0x7f, 
0xe3, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xc7, 0xfe, 0x3f, 0xf0, 0x31, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x00, 
0x3f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf0, 

0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0x04, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x01, 0x60, 0x00, 0xbe, 0x0f, 0x0f, 0x80, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x00, 0x0f, 0x00, 0x00, 0x01, 0xe0, 0x00, 0xf0, 0x04, 0x04, 0x03, 
0xe0, 0x00, 0x00, 0x00, 0x11, 0xc0, 0x20, 0xf0, 0x08, 0x00, 0x01, 0xe0, 0x00, 0x08, 0x00, 0xf0, 
0x00, 0x10, 0x08, 0x00, 0x00, 0x20, 0xf0, 0x00, 0x7c, 0x1e, 0x00, 0x00, 0x90, 0x08, 0x00, 0x40, 
0x22, 0x01, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 0x10, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0xe0, 0x02, 0x01, 0x00, 
0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0x04, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0xe0, 0x00, 0x81, 0x0f, 0x80, 0x40, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x00, 0x10, 0x80, 0x00, 0x00, 0x10, 0x00, 0x08, 0x08, 0x08, 0x00, 
0x10, 0x00, 0x00, 0x00, 0x22, 0x20, 0x50, 0x08, 0x14, 0x00, 0xe0, 0x30, 0x00, 0x14, 0x01, 0x08, 
0x00, 0x10, 0x08, 0x00, 0x00, 0x31, 0x08, 0x00, 0x02, 0x01, 0x00, 0x01, 0x10, 0x08, 0x00, 0x40, 
0x22, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 0x10, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x70, 0x02, 0x01, 0x00, 
0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0x04, 0x02, 0x1a, 0x1a, 0x03, 0x40, 0x34, 0x03, 0xc3, 0x80, 
0xc0, 0x60, 0x19, 0x01, 0x90, 0x04, 0x61, 0x18, 0x1b, 0x1d, 0xc3, 0xc6, 0x96, 0x8f, 0xc5, 0xa0, 
0x40, 0x22, 0x01, 0x10, 0x08, 0xce, 0x00, 0x46, 0xc6, 0x33, 0x88, 0xc8, 0x44, 0xcc, 0xcc, 0xcf, 
0x02, 0x3e, 0x19, 0xc2, 0x30, 0x10, 0x50, 0xc4, 0x6c, 0x1f, 0x01, 0x99, 0xb0, 0x4c, 0x30, 0x05, 
0xcf, 0x10, 0x08, 0x71, 0x91, 0x90, 0x04, 0xcc, 0x2d, 0x16, 0x80, 0x87, 0x10, 0x08, 0x3c, 0x40, 
0x22, 0x01, 0x18, 0x89, 0x8c, 0x8c, 0x46, 0x60, 0xd0, 0x00, 0x80, 0x44, 0x02, 0x20, 0x10, 0x21, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x02, 0x01, 0x00, 
0x20, 0x10, 0x00, 0x07, 0x80, 0x3c, 0x01, 0xe1, 0xcf, 0x00, 0x78, 0x03, 0xc0, 0x00, 0x01, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0xc4, 0x02, 0x05, 0x05, 0x40, 0xa0, 0x0a, 0x04, 0x26, 0x40, 
0x20, 0x15, 0x8a, 0xb0, 0xab, 0x2e, 0xb3, 0xae, 0xc4, 0x82, 0x44, 0x21, 0x2a, 0x99, 0x4a, 0xb6, 
0x40, 0x22, 0x01, 0x10, 0x08, 0xb1, 0x0e, 0x49, 0x21, 0x53, 0x49, 0xd4, 0x24, 0x2c, 0x5c, 0x30, 
0x87, 0xc5, 0x2d, 0xa7, 0x71, 0x91, 0x38, 0x24, 0x22, 0x1d, 0x03, 0x49, 0x48, 0x82, 0x08, 0xc4, 
0x31, 0x10, 0x08, 0xa8, 0xa8, 0x48, 0xe4, 0x2a, 0x65, 0x36, 0x8d, 0x4c, 0x90, 0x08, 0x46, 0x40, 
0x22, 0x01, 0x04, 0x92, 0x45, 0x42, 0xa1, 0x51, 0x28, 0xd8, 0x80, 0x44, 0x02, 0x20, 0x10, 0x21, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x01, 0x00, 
0x20, 0x10, 0x03, 0x08, 0x40, 0x42, 0x02, 0x12, 0x10, 0x80, 0x84, 0x04, 0x20, 0x00, 0x59, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0xc8, 0x05, 0x80, 
0x78, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x40, 0x22, 0x01, 0x00, 0x1c, 0x00, 0xc4, 0x02, 0x1e, 0x1e, 0xa3, 0xdc, 0x3d, 0x85, 0xa4, 0x43, 
0xe1, 0xf3, 0x9b, 0x89, 0xb8, 0x8e, 0xf3, 0xbd, 0xdc, 0x9e, 0x45, 0xd7, 0x24, 0x96, 0x49, 0x2e, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x3c, 0x9f, 0x49, 0x27, 0x61, 0xc9, 0x94, 0xe4, 0x6c, 0xd8, 0x3d, 
0x85, 0xf7, 0x2f, 0xe5, 0x62, 0x12, 0x09, 0xe4, 0xe2, 0x3d, 0xa3, 0xc8, 0x64, 0x82, 0x39, 0x04, 
0x30, 0x90, 0x08, 0xf9, 0xb8, 0xc9, 0x14, 0xf2, 0x5d, 0x2e, 0x91, 0xc8, 0x90, 0x08, 0x9e, 0x40, 
0x22, 0x01, 0x0c, 0x94, 0x4d, 0xce, 0xe3, 0x91, 0xf9, 0x24, 0x80, 0x44, 0x02, 0x20, 0x10, 0x01, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x28, 0x01, 0xc0, 0x0c, 0x00, 0x00, 0x02, 0x01, 0x00, 
0x20, 0x10, 0x00, 0x8b, 0xc0, 0x5e, 0x02, 0xf2, 0xd7, 0x80, 0xbc, 0x05, 0xe0, 0x00, 0x39, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x20, 0x05, 0x83, 
0xa8, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x40, 0x22, 0x01, 0x00, 0x14, 0x00, 0x04, 0x02, 0x24, 0x24, 0x24, 0x84, 0x48, 0x87, 0xa4, 0x46, 
0x03, 0x00, 0x90, 0x99, 0x09, 0x88, 0x12, 0x04, 0x51, 0x90, 0xc5, 0xa4, 0x24, 0x92, 0x49, 0x22, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x52, 0x91, 0x49, 0x25, 0xd2, 0x49, 0x94, 0x8c, 0x8c, 0x84, 0x52, 
0x46, 0x48, 0x32, 0x06, 0x12, 0x32, 0x49, 0x24, 0x92, 0x23, 0x53, 0x08, 0x94, 0xc6, 0x41, 0x24, 
0x48, 0x90, 0x08, 0xc1, 0x09, 0x19, 0x14, 0x92, 0x63, 0x20, 0x92, 0x49, 0x90, 0x08, 0x8a, 0x40, 
0x22, 0x01, 0x11, 0x94, 0xcb, 0x49, 0x24, 0x93, 0x11, 0x04, 0x80, 0x44, 0x02, 0x20, 0x10, 0x01, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x08, 0x00, 0x40, 0x04, 0x00, 0x00, 0x02, 0x01, 0x00, 
0x20, 0x10, 0x01, 0x8b, 0x80, 0x5c, 0x02, 0xe2, 0xd7, 0x00, 0xb8, 0x05, 0xc0, 0x00, 0x09, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x60, 0x02, 0x86, 
0xac, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x40, 0x22, 0x01, 0x00, 0x1c, 0x00, 0xc4, 0x02, 0x24, 0x24, 0xa4, 0x88, 0x48, 0xc0, 0x24, 0xc4, 
0x02, 0x02, 0x92, 0xa9, 0x2a, 0xaf, 0xf3, 0xfd, 0x5f, 0x1f, 0x87, 0xe7, 0xbf, 0x9f, 0xcf, 0xea, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x7e, 0x9d, 0x4f, 0x27, 0x73, 0xf9, 0xf4, 0xf8, 0xfc, 0xfc, 0xff, 
0xf7, 0xf8, 0x3e, 0x06, 0x13, 0xe3, 0xf9, 0xfc, 0xfe, 0x1e, 0xd3, 0x29, 0xf4, 0x7c, 0x41, 0xfd, 
0xf8, 0x90, 0x08, 0xc1, 0xf9, 0xf9, 0xd4, 0xf2, 0x3e, 0x2e, 0x9f, 0xcf, 0x90, 0x08, 0x8c, 0x40, 
0x22, 0x01, 0x1f, 0x1d, 0x48, 0x4f, 0xe7, 0x92, 0xe1, 0x04, 0x80, 0x44, 0x02, 0x20, 0x10, 0x01, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x28, 0x00, 0x80, 0x06, 0x00, 0x00, 0x12, 0x01, 0x01, 
0x20, 0x10, 0x02, 0x88, 0x80, 0x44, 0x02, 0x22, 0x11, 0x00, 0x88, 0x04, 0x40, 0x00, 0x29, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0xa8, 0x00, 0x96, 
0x8e, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x40, 0x22, 0x01, 0x00, 0x00, 0x00, 0xc4, 0x02, 0x1f, 0x1f, 0x43, 0xe8, 0x3e, 0x83, 0xc3, 0x84, 
0x12, 0x0b, 0x0d, 0x18, 0xd1, 0x87, 0xc1, 0xf1, 0x8e, 0x0f, 0x03, 0xc3, 0x9b, 0x0d, 0x86, 0xcc, 
0x40, 0x22, 0x01, 0x10, 0x08, 0xaf, 0x0d, 0x86, 0xc3, 0x61, 0xb0, 0xe8, 0x70, 0x78, 0x78, 0xad, 
0xb5, 0xb7, 0x1d, 0xe1, 0xe1, 0xc1, 0xb0, 0xd8, 0x6c, 0x0e, 0x21, 0xd1, 0x68, 0x38, 0x38, 0xd8, 
0xf3, 0x10, 0x08, 0x78, 0xf0, 0xf0, 0xd8, 0x6c, 0x1c, 0x1b, 0x0d, 0x87, 0x10, 0x08, 0x82, 0x40, 
0x22, 0x01, 0x0e, 0x0d, 0x87, 0x86, 0xc3, 0x62, 0x08, 0xd8, 0x80, 0x44, 0x02, 0x20, 0x10, 0x01, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x10, 0x00, 0x80, 0x04, 0x00, 0x00, 0x12, 0x01, 0x01, 
0x20, 0x10, 0x01, 0x87, 0x00, 0x38, 0x01, 0xc1, 0xce, 0x00, 0x70, 0x03, 0x80, 0x00, 0x31, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x60, 0x03, 0x18, 
0x04, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x10, 

0x7f, 0xe3, 0xff, 0x00, 0x00, 0x00, 0x07, 0xfe, 0x04, 0x04, 0x00, 0x88, 0x08, 0x82, 0x40, 0x02, 
0x31, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 
0x00, 0x1f, 0xf8, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0x46, 0x7f, 
0xe3, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x10, 0x00, 0xff, 0xc7, 0xfe, 0x3f, 0xf0, 0x01, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 0x00, 0x00, 0x80, 0x04, 0x00, 0x03, 0x13, 0xff, 0x31, 
0x3f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf0, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 0x00, 0x86, 0x08, 0xe1, 0x80, 0x01, 
0xe0, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x07, 0x00, 0x00, 0x10, 0x00, 0x0f, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 0x00, 0x80, 0x08, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0x00, 0x31, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 


};

xFONTYY Tahoma08bBengali1 = {0x01, 16, 0, 16, 0, 0, 16, 179, 0x0d80, 0x0dff,
(PEGUSHORT *) Tahoma08bBengali1_offset_table, &Tahoma08nArabic_Font,
(PEGUBYTE *) Tahoma08bBengali1_data_table};


ROMDATA PEGUSHORT Tahoma08bBengali_offset_table[129] = {
0x0000,0x000d,0x0014,0x0021,0x002e,0x003b,0x0045,0x0052,0x0059,0x0062,0x006c,0x0076,0x007e,0x0085,0x0092,0x009f,
0x00a5,0x00ad,0x00ba,0x00c7,0x00ce,0x00d7,0x00e1,0x00ea,0x00f2,0x00fa,0x0102,0x0109,0x0112,0x011c,0x0126,0x012f,
0x0137,0x013f,0x0149,0x0151,0x0159,0x0163,0x016c,0x0174,0x017c,0x0184,0x0191,0x0199,0x01a3,0x01ab,0x01b5,0x01be,
0x01c6,0x01ce,0x01db,0x01e4,0x01f1,0x01fe,0x020b,0x0213,0x021b,0x0223,0x022a,0x0237,0x0244,0x024a,0x0250,0x025d,
0x0269,0x0276,0x027f,0x0289,0x028f,0x0295,0x02a2,0x02af,0x02bc,0x02c8,0x02d5,0x02e2,0x02ef,0x02fc,0x0302,0x0309,
0x0316,0x0323,0x0330,0x033d,0x034a,0x0357,0x0364,0x0371,0x037e,0x038b,0x0398,0x03a5,0x03b2,0x03bc,0x03c4,0x03d1,
0x03d9,0x03e2,0x03e9,0x03f0,0x03f7,0x0404,0x0411,0x0418,0x041e,0x0424,0x042d,0x0433,0x043a,0x0442,0x0448,0x0452,
0x0459,0x0461,0x0469,0x0470,0x0478,0x047f,0x0488,0x0490,0x0496,0x049c,0x04a0,0x04a9,0x04b6,0x04c3,0x04d0,0x04dd,
0x04ea};



ROMDATA PEGUBYTE Tahoma08bBengali_data_table[2528] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x20, 0x10, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x18, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x1e, 0x0f, 0x81, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x18, 0x1f, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x70, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe2, 0x20, 0x00, 0x00, 0x01, 0xff, 0x80, 0x00, 0x00, 0x03, 0x03, 0x00, 0xc0, 0x30, 0x00, 
0x03, 0xff, 0x1f, 0xf8, 0x00, 0x33, 0xff, 0x1f, 0xf8, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x07, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0x80, 0x07, 0xfe, 0x3f, 0xf1, 
0xff, 0x80, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0xff, 0xc0, 0x00, 0x00, 0x03, 0x00, 0x03, 0x30, 0x00, 
0x00, 0x00, 0x03, 0xff, 0x1f, 0xf8, 0x00, 0x06, 0x00, 0x7f, 0xe3, 0xff, 0x00, 0x02, 0x00, 0x10, 
0x00, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x00, 0x31, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 0x00, 0x07, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 
0xc7, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 

0x40, 0x21, 0xc0, 0x00, 0x00, 0x39, 0x00, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6d, 
0x82, 0x01, 0x10, 0x08, 0x71, 0xca, 0x01, 0x10, 0x08, 0x78, 0xf3, 0xff, 0x93, 0xdf, 0xff, 0xcb, 
0xbf, 0xff, 0xff, 0xff, 0xbc, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xfc, 0xff, 0xf7, 0x7f, 
0xf4, 0x02, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x00, 0x9f, 0xf4, 0x02, 0x20, 0x11, 
0x00, 0x97, 0xff, 0xff, 0xff, 0xd0, 0x08, 0x80, 0x40, 0x38, 0x00, 0xff, 0x80, 0x00, 0x78, 0x00, 
0x00, 0x00, 0x02, 0x01, 0x10, 0x08, 0x70, 0x06, 0x00, 0x40, 0x22, 0x01, 0x0e, 0x02, 0x70, 0x10, 
0x0e, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x7d, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x3f, 0xff, 0xf4, 0x02, 0x7f, 0xec, 0xd8, 0x00, 0x00, 0x80, 
0x44, 0x02, 0x00, 0x61, 0x80, 0x01, 0x86, 0x06, 0x1c, 0x70, 0x30, 0x7f, 0xff, 0x80, 0xb0, 0x00, 
0x00, 0x00, 0x33, 0x00, 0x0c, 0xa0, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x40, 0x20, 0x00, 0x07, 0x00, 0x29, 0x00, 0x80, 0x60, 0x1b, 0x18, 0x30, 0x06, 0x00, 0xc3, 0xec, 
0xe2, 0x01, 0x10, 0x08, 0xdb, 0x6a, 0x01, 0x10, 0x08, 0x6c, 0xda, 0x30, 0x7b, 0x33, 0x19, 0x86, 
0xd8, 0x30, 0x0d, 0x81, 0xb1, 0xbe, 0xc0, 0x38, 0x30, 0x30, 0x66, 0x00, 0x1a, 0xc6, 0x06, 0x60, 
0xc4, 0x02, 0x53, 0x30, 0x01, 0x80, 0x03, 0x31, 0xb0, 0x31, 0x00, 0x80, 0xc4, 0x02, 0x20, 0x11, 
0x00, 0x89, 0x8d, 0x99, 0x8c, 0x10, 0x08, 0x80, 0x40, 0x18, 0x00, 0x63, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x02, 0x01, 0x10, 0x08, 0xc0, 0x0e, 0x00, 0x40, 0x22, 0x01, 0x18, 0x02, 0xc0, 0x10, 
0x1b, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x06, 0x06, 0x04, 0x02, 0x36, 0x76, 0xce, 0x00, 0x00, 0x80, 
0x44, 0x02, 0x00, 0x60, 0xc0, 0x03, 0x4c, 0x06, 0x36, 0xdb, 0x9c, 0x06, 0x06, 0x00, 0x7f, 0x00, 
0x00, 0x03, 0x31, 0x80, 0x6d, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x40, 0x20, 0x00, 0x05, 0x00, 0x39, 0x00, 0x87, 0x61, 0xdb, 0x1e, 0x3b, 0x06, 0x82, 0xd0, 0xec, 
0x32, 0x01, 0x10, 0x08, 0xdb, 0x6a, 0x01, 0x10, 0x08, 0x0c, 0x1a, 0x7c, 0x6b, 0x3b, 0x0d, 0x87, 
0x98, 0x3f, 0x0e, 0xc7, 0xb1, 0xb6, 0xc0, 0x2c, 0x34, 0x30, 0x76, 0x07, 0x1a, 0xc6, 0xc3, 0xe3, 
0xc4, 0x02, 0x1f, 0x37, 0x07, 0x9b, 0x01, 0xb1, 0xb0, 0xf1, 0x00, 0x8f, 0xc4, 0x02, 0x20, 0x11, 
0x00, 0x9d, 0x8f, 0x8f, 0x9f, 0x10, 0x08, 0x80, 0x40, 0x3e, 0x00, 0x63, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x02, 0x01, 0x10, 0x09, 0x80, 0x0c, 0x00, 0x40, 0x22, 0x01, 0x30, 0x03, 0x80, 0x10, 
0x1b, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x06, 0x86, 0x04, 0x02, 0x36, 0x0e, 0xc3, 0x00, 0x00, 0x80, 
0x44, 0x02, 0x1c, 0x70, 0x68, 0xe3, 0x5b, 0xb6, 0xb6, 0x19, 0x86, 0x1e, 0x3e, 0x30, 0x30, 0x0c, 
0x02, 0x73, 0x31, 0x80, 0x62, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x40, 0x20, 0x00, 0x07, 0x00, 0x01, 0x00, 0x95, 0x64, 0x5b, 0x3b, 0x77, 0x37, 0xca, 0xf9, 0x6d, 
0xda, 0x01, 0x10, 0x08, 0x18, 0x72, 0x01, 0x10, 0x09, 0x9b, 0x3c, 0xb6, 0x0b, 0x0b, 0x19, 0xb6, 
0x1c, 0x35, 0x26, 0xc9, 0xb0, 0x34, 0xd8, 0xed, 0xbe, 0x36, 0x76, 0x66, 0x84, 0xc7, 0xc6, 0x64, 
0xc4, 0x02, 0x33, 0x66, 0x8d, 0x9b, 0x47, 0xb3, 0xb1, 0xb1, 0x00, 0x8a, 0xc4, 0x02, 0x20, 0x11, 
0x00, 0x95, 0x8d, 0x8d, 0x83, 0x10, 0x08, 0x80, 0x40, 0x06, 0x00, 0x63, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x02, 0x01, 0x10, 0x09, 0x80, 0x0c, 0x00, 0x40, 0x22, 0x01, 0x30, 0x03, 0x80, 0x10, 
0x0c, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x37, 0xc6, 0xc4, 0x02, 0x76, 0x16, 0xdd, 0x80, 0x00, 0x80, 
0x44, 0x02, 0x36, 0x30, 0x6d, 0x31, 0x9b, 0x37, 0x9e, 0x1f, 0xb9, 0x36, 0x66, 0x18, 0x37, 0x0c, 
0x64, 0xdb, 0x31, 0xb0, 0x64, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x40, 0x20, 0x00, 0x00, 0x00, 0x39, 0x00, 0x89, 0xe2, 0x7b, 0x03, 0x0b, 0x30, 0xc9, 0x1b, 0x7d, 
0x5a, 0x01, 0x10, 0x08, 0x18, 0x62, 0x01, 0x10, 0x08, 0xcd, 0x98, 0xf6, 0x7b, 0x1b, 0x1d, 0xb7, 
0xda, 0x39, 0x26, 0xc5, 0xf0, 0x36, 0xc8, 0xcd, 0x86, 0x32, 0x06, 0x76, 0x9c, 0xc2, 0xc7, 0x66, 
0xc4, 0x02, 0x03, 0x1e, 0x8f, 0x8d, 0xa5, 0xf0, 0xf1, 0xf1, 0x00, 0x88, 0xc4, 0x02, 0x20, 0x11, 
0x00, 0x81, 0x9d, 0x99, 0x9e, 0x10, 0x08, 0x80, 0x40, 0x3c, 0x00, 0x63, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x02, 0x01, 0x10, 0x09, 0x80, 0x0c, 0x00, 0x40, 0x22, 0x01, 0x30, 0x03, 0x80, 0x10, 
0x06, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x30, 0xc6, 0x44, 0x02, 0x1e, 0x3f, 0xd5, 0x80, 0x00, 0x80, 
0x44, 0x02, 0x36, 0x19, 0xcd, 0xb3, 0x5b, 0x11, 0x86, 0x1b, 0x2d, 0x6e, 0x1e, 0x0c, 0x35, 0x18, 
0xb4, 0x3a, 0x31, 0xf6, 0x38, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x40, 0x20, 0x00, 0x06, 0x00, 0x29, 0x00, 0x86, 0x61, 0x9b, 0x3c, 0x73, 0x18, 0xc5, 0x18, 0xec, 
0x5a, 0x01, 0x10, 0x09, 0xbe, 0xe2, 0x01, 0x10, 0x08, 0xcd, 0x98, 0x70, 0x07, 0x03, 0x03, 0x98, 
0xda, 0x02, 0x1c, 0xc3, 0xb3, 0x7e, 0xc8, 0xcc, 0xc6, 0x32, 0x06, 0x30, 0x83, 0xc0, 0xc0, 0xe6, 
0xc4, 0x02, 0x03, 0x06, 0x83, 0x8e, 0x23, 0xb0, 0x70, 0x71, 0x00, 0x88, 0xc4, 0x02, 0x20, 0x11, 
0x00, 0x81, 0x83, 0x81, 0x87, 0x10, 0x08, 0x80, 0x40, 0x0e, 0x00, 0x63, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x02, 0x01, 0x10, 0x09, 0x80, 0x0c, 0x00, 0x40, 0x22, 0x01, 0x30, 0x03, 0x80, 0x10, 
0x06, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x18, 0xc6, 0x44, 0x02, 0x0e, 0x0e, 0xc5, 0x80, 0x00, 0x80, 
0x44, 0x02, 0x36, 0x58, 0x45, 0xb3, 0x4d, 0x19, 0x86, 0x1b, 0x0d, 0x7e, 0x6e, 0x06, 0x31, 0x30, 
0x38, 0x66, 0x30, 0xbd, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x40, 0x20, 0x00, 0x01, 0x00, 0x39, 0x00, 0x80, 0x60, 0x1b, 0x06, 0x01, 0x8f, 0x83, 0xf0, 0x6c, 
0xf2, 0x01, 0x10, 0x08, 0xdb, 0x62, 0x01, 0x10, 0x08, 0x78, 0xf0, 0x30, 0x03, 0x03, 0x01, 0x8f, 
0x8c, 0x01, 0x80, 0xc1, 0xb1, 0xb0, 0x70, 0x78, 0x7c, 0x1c, 0x06, 0x1f, 0x00, 0xc0, 0xc0, 0x60, 
0xc4, 0x02, 0x03, 0x06, 0x01, 0x87, 0xc0, 0x30, 0x30, 0x31, 0x00, 0x80, 0xc4, 0x02, 0x20, 0x11, 
0x00, 0x81, 0x81, 0x81, 0x81, 0x90, 0x08, 0x80, 0x40, 0x03, 0x00, 0x63, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x82, 0x01, 0x10, 0x08, 0xe0, 0x0c, 0x00, 0x40, 0x22, 0x01, 0x1c, 0x02, 0xe0, 0x10, 
0x0c, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x0f, 0x83, 0x84, 0x02, 0x06, 0x06, 0xcf, 0x00, 0x00, 0x80, 
0x44, 0x02, 0x1c, 0x70, 0x26, 0x71, 0x87, 0x8f, 0x06, 0x0e, 0x1e, 0x06, 0x36, 0x06, 0x1e, 0x60, 
0x60, 0x78, 0x30, 0x3f, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x40, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00, 0x80, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x01, 0x10, 0x08, 0x00, 0x02, 0x01, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x81, 0x00, 0x80, 0x04, 0x02, 0x20, 0x11, 
0x00, 0x80, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 0x43, 0x00, 0x00, 0x63, 0x00, 0x00, 0x30, 0x60, 
0x30, 0x00, 0xf2, 0x01, 0x10, 0x08, 0x60, 0x07, 0x00, 0x40, 0x22, 0x01, 0x0c, 0x02, 0x60, 0x10, 
0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x31, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x04, 0x02, 0x30, 0x0c, 0x06, 0x1c, 0x38, 0x80, 
0x44, 0x02, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x30, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x7f, 0xe0, 0x00, 0x00, 0x00, 0x01, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0xff, 0x1f, 0xf8, 0x00, 0x03, 0xff, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x07, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x81, 0xff, 0x80, 0x07, 0xfe, 0x3f, 0xf1, 
0xff, 0x80, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0xff, 0xc3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 
0x70, 0x10, 0x63, 0xff, 0x1f, 0xf8, 0x00, 0x03, 0x00, 0x7f, 0xe3, 0xff, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x00, 0x01, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 0x00, 0x07, 0xfe, 0x30, 0x1c, 0x01, 0x86, 0x0c, 0xff, 
0xc7, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x78, 
0x40, 0x38, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x06, 0x0e, 0xba, 0x74, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xcc, 
0x58, 0x0c, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x03, 0x0b, 0xae, 0x5c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x36, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma08bBengali_Font = {0x01, 16, 0, 16, 0, 0, 16, 158, 0x0980, 0x09ff,
(PEGUSHORT *) Tahoma08bBengali_offset_table, &Tahoma08bBengali1, 
(PEGUBYTE *) Tahoma08bBengali_data_table};



