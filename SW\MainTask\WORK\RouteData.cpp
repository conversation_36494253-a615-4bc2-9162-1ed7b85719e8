/*...........................................................................*/
/*.                  File Name : RouteData.cpp                              .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "datatype.h"
#if 0	// Temp
#include "datatype.hpp"
#endif
#include "CpuAddr.h"
#include "SysConst.h"
#include "KeyConst.h"
#include "AllConst.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "SysLib.h"

#include "SysGPIO.h"

#include "KeyBD.hpp"
#include "DataBack.hpp"
#if 0	// Temp
#include "MainWin.hpp"
#endif

#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
  #include "SdCardDRV.h"
#endif

#include "RouteData.hpp"

//===========================================================================
extern cKEYBD      *G_pKeyBD;
extern cDataBack   *G_pDataBack;
#if 0	// Temp
extern cMainWin    *G_pMainWin;
#endif
//===========================================================================

//===========================================================================
xROUTE  *cRouteData::m_pRouteData  = NULL;
//===========================================================================

cRouteData::cRouteData(void)
{
}
cRouteData::~cRouteData(void)
{
}
void  cRouteData::GetMemory(void)
{
      m_pRouteData = new xROUTE[ROUTE_ALL_SIZE];

      ClearAllData();
}

void  cRouteData::ClearAllRouteData(void)
{
      int   i;

      for (i = 0;i < ROUTE_ALL_SIZE;i++)
           ClearOneRouteData(i);
}
void  cRouteData::ClearOneRouteData(int nRouteNo)
{
      int   i;
      UCHAR vName[ROUTE_NAME_SIZE * 2];

      memset(vName,' ',ROUTE_NAME_SIZE);
      SetRouteName(nRouteNo,vName);

      SetTotalPnts(nRouteNo,0);

      for (i = 0;i < ROUTE_PNT_SIZE_PER_ROUTE;i++)
           m_pRouteData[nRouteNo].vAllSlot[i] = 0;
}

void  cRouteData::SetRouteName(int nRouteNo,UCHAR *pName)
{
      memmove(m_pRouteData[nRouteNo].vName,pName,ROUTE_NAME_SIZE);

      cRouteData::MakeRouteNameAligned((char *)m_pRouteData[nRouteNo].vName);

      m_pRouteData[nRouteNo].vName[ROUTE_NAME_SIZE - 1] = 0x00;
}
UCHAR *cRouteData::GetRouteName(int nRouteNo)
{
      return(m_pRouteData[nRouteNo].vName);
}
void  cRouteData::SetTotalPnts(int nRouteNo,int nTotalPnts)
{
      m_pRouteData[nRouteNo].nTotalPnts = nTotalPnts;
}
int   cRouteData::GetTotalPnts(int nRouteNo)
{
      return(m_pRouteData[nRouteNo].nTotalPnts);
}
void  cRouteData::CalcTotalPnts(int nRouteNo)
{
      int   i;
      int   nTotalPnts;

      nTotalPnts = 0;

      for (i = 0;i < ROUTE_PNT_SIZE_PER_ROUTE;i++)
           if (m_pRouteData[nRouteNo].vAllSlot[i])
               ++nTotalPnts;

      SetTotalPnts(nRouteNo,nTotalPnts);
}
int   cRouteData::CalcTotalRoutes(void)
{
      int   i;
      int   nTotalRoutes = 0;

      for (i = 0;i < ROUTE_ALL_SIZE;i++)
          {
           if (cRouteData::GetTotalPnts(i) > 0)
               nTotalRoutes++;
          }

      return(nTotalRoutes);
}
void  cRouteData::SetWayPntNoInRouteSlot(int nRouteNo,int nSlotNo,int nWptNo)
{
      m_pRouteData[nRouteNo].vAllSlot[nSlotNo] = nWptNo;
}
int   cRouteData::GetWayPntNoInRouteSlot(int nRouteNo,int nSlotNo)
{
      return(m_pRouteData[nRouteNo].vAllSlot[nSlotNo]);
}
int   cRouteData::AppendWayPntNoInRouteSlot(int nRouteNo,int nWptNo)
{
      if (m_pRouteData[nRouteNo].nTotalPnts >= ROUTE_PNT_SIZE_PER_ROUTE)
          return(0);

      SetWayPntNoInRouteSlot(nRouteNo,m_pRouteData[nRouteNo].nTotalPnts,nWptNo);
      ++m_pRouteData[nRouteNo].nTotalPnts;

      return(1);
}

int   cRouteData::GetTotalSameWayPntNoInRoute(int nRouteNo,int nPointNo,int nWptNo)
{
      int   i;
      int   nTotal;

      if (nWptNo < 1)
          return(0);

      nTotal = 0;

      for (i = 0;i < ROUTE_PNT_SIZE_PER_ROUTE;i++)
           if (m_pRouteData[nRouteNo].vAllSlot[i] == nWptNo)
               ++nTotal;

      return(nTotal);
}
int   cRouteData::FindSlotNoByWayPntNo(int nRouteNo,int nWptNo)
{
      int   i;

      for (i = 0;i < ROUTE_PNT_SIZE_PER_ROUTE;i++)
           if (m_pRouteData[nRouteNo].vAllSlot[i] == nWptNo)
               return(i);

      return(ROUTE_NOT_FOUND_SLOT);
}
int   cRouteData::FindSlotNoBySequenceNo(int nRouteNo,int nSeqNo,int nRouteDir)
{
      int   i;
      int   nCount = 0;

      if (nRouteDir == ROUTE_NAV_DIR_FORWARD)
         {
          for (i = 0;i < ROUTE_PNT_SIZE_PER_ROUTE;i++)
               if (m_pRouteData[nRouteNo].vAllSlot[i])
                  {
                   if (nCount >= nSeqNo)
                       return(i);
                   ++nCount;
                  }
         }
      else
         {
          for (i = (ROUTE_PNT_SIZE_PER_ROUTE - 1);i >= 0;i--)
               if (m_pRouteData[nRouteNo].vAllSlot[i])
                  {
                   if (nCount >= nSeqNo)
                       return(i);
                   ++nCount;
                  }
         }

      return(ROUTE_NOT_FOUND_SLOT);
}
int   cRouteData::GetLastRouteSlotNo(int nRouteNo)
{
      return(m_pRouteData[nRouteNo].nTotalPnts - 1);
}
int   cRouteData::IsWayPntInRouteData(int nWptNo)
{
      int  nRouteNo,nSlotNo;

      for (nRouteNo = 0;nRouteNo < ROUTE_ALL_SIZE;nRouteNo++)
          {
           for (nSlotNo = 0;nSlotNo < m_pRouteData[nRouteNo].nTotalPnts;nSlotNo++)
               {
                if (m_pRouteData[nRouteNo].vAllSlot[nSlotNo] == nWptNo)
                    return(1);
               }
          }
      return(0);
}
int   cRouteData::IsPassedRouteSlotNo(int nRouteNo,int nCheckSlotNo,int nRunSlotNo,int nRouteDir)
{
      if (nRouteDir == ROUTE_NAV_DIR_FORWARD)
         {
          if (nCheckSlotNo < nRunSlotNo)
              return(1);
         }
      else
         {
          if (nCheckSlotNo > nRunSlotNo)
              return(1);
         }

      return(0);
}
int   cRouteData::CanSkipRouteSlotNo(int nRouteNo,int nRunSlotNo,int nRouteDir)
{
      if (nRouteDir == ROUTE_NAV_DIR_FORWARD)
         {
          if (nRunSlotNo != GetLastRouteSlotNo(nRouteNo))
              return(1);
         }
      else
         {
          if (nRunSlotNo != 0)
              return(1);
         }

      return(0);
}
int   cRouteData::RemoveContinuousWptNo(int nRouteNo)
{
      int  nSlotNo;
      int  nWptNo;
      int  nResult;

      nResult = 0;
      nSlotNo = 0;
      nWptNo  = m_pRouteData[nRouteNo].vAllSlot[nSlotNo];

      while (nWptNo && nSlotNo < (ROUTE_PNT_SIZE_PER_ROUTE - 1))
            {
             if (nWptNo == m_pRouteData[nRouteNo].vAllSlot[nSlotNo + 1])
                {
                 LeftShiftSlotData(nRouteNo,nSlotNo);
                }
             else
                {
                 ++nSlotNo;
                }
             nWptNo  = m_pRouteData[nRouteNo].vAllSlot[nSlotNo];
            }

     cRouteData::CalcTotalPnts(nRouteNo);

      return(nResult);
}
void  cRouteData::LeftShiftSlotData(int nRouteNo,int nSlotNo)
{
      int  i;

      for (i = nSlotNo;i < (ROUTE_PNT_SIZE_PER_ROUTE - 1);i++)
           m_pRouteData[nRouteNo].vAllSlot[i] = m_pRouteData[nRouteNo].vAllSlot[i + 1];

      m_pRouteData[nRouteNo].vAllSlot[ROUTE_PNT_SIZE_PER_ROUTE - 1] = 0;
}
int   cRouteData::CheckNameValid(int nRouteNo)
{
      int  i,nError = 0;

      for (i = 0;i < ROUTE_NAME_SIZE;i++)
           if (m_pRouteData[nRouteNo].vName[i] != 0x00 && (m_pRouteData[nRouteNo].vName[i] < 0x20 || m_pRouteData[nRouteNo].vName[i] >= 0x7f))
              {
               m_pRouteData[nRouteNo].vName[i] = ' ';
               nError = 1;
              }
      m_pRouteData[nRouteNo].vName[ROUTE_NAME_SIZE - 1] = 0x00;

      return(nError);

}
int   cRouteData::CheckSameNameExist(char *pRouteName)
{
      int  nRouteNo;
      char vTextX[ROUTE_NAME_SIZE * 2];
      char vTextY[ROUTE_NAME_SIZE * 2];

      vTextX[ROUTE_NAME_SIZE + 0] = 0x00;
      vTextY[ROUTE_NAME_SIZE + 0] = 0x00;

      memmove(vTextY,pRouteName,ROUTE_NAME_SIZE);

      for (nRouteNo = 0;nRouteNo < ROUTE_ALL_SIZE;nRouteNo++)
          {
           if (GetTotalPnts(nRouteNo) > 0)
              {
               memmove(vTextX,m_pRouteData[nRouteNo].vName,ROUTE_NAME_SIZE);

               if (strcmp(vTextX,vTextY) == 0)
                   return(1);
              }
          }

      return(0);
}
int   cRouteData::FindFirstEmptyRoute(void)
{
      int  nRouteNo;

      for (nRouteNo = 0;nRouteNo < ROUTE_ALL_SIZE;nRouteNo++)
          {
           if (GetTotalPnts(nRouteNo) < 1)
               return(nRouteNo);
          }
      return(ROUTE_NOT_FOUND_ROUTE);
}
char *cRouteData::MakeNormalRouteName(int nRouteNo)
{
      static char vName[ROUTE_NAME_SIZE * 2];
      char  vText[32];

      memset(vName,' ',sizeof(vName) - 1);
      sprintf(vText,"ROUTE%02d",nRouteNo);

      memmove(vName,vText,strlen(vText));
      cRouteData::MakeRouteNameAligned(vName);
      vName[ROUTE_NAME_SIZE - 1] = 0x00;
      return(vName);
}
char *cRouteData::MakeEmptyRouteName(void)
{
      static char vName[ROUTE_NAME_SIZE * 2];
      char  vText[32];

      memset(vName,' ',sizeof(vName) - 1);
      strcpy(vText,"ROUTE--");

      memmove(vName,vText,strlen(vText));
      cRouteData::MakeRouteNameAligned(vName);
      vName[ROUTE_NAME_SIZE - 1] = 0x00;
      return(vName);
}
void  cRouteData::MakeRouteNameAligned(char *pRouteName)
{
      char  vTextX[ROUTE_NAME_SIZE * 2];
      int   nLen;

      memmove(vTextX,pRouteName,ROUTE_NAME_SIZE);
      vTextX[ROUTE_NAME_SIZE - 1] = 0x00;

      FullAllTrimStr(vTextX);
      nLen = strlen(vTextX);
      memset(&vTextX[nLen],' ',ROUTE_NAME_SIZE - nLen);

      vTextX[ROUTE_NAME_SIZE - 1] = 0x00;

      memmove(pRouteName,vTextX,ROUTE_NAME_SIZE);
}

void  cRouteData::SaveAllBackData(UCHAR *pBackData)
{
      memmove(pBackData,m_pRouteData,sizeof(xROUTE) * ROUTE_ALL_SIZE);
}
void  cRouteData::LoadAllBackData(UCHAR *pBackData)
{
      int  nRouteNo,nSlotNo;

      memmove(m_pRouteData,pBackData,sizeof(xROUTE) * ROUTE_ALL_SIZE);

      for (nRouteNo = 0;nRouteNo < ROUTE_ALL_SIZE;nRouteNo++)
          {
           CheckNameValid(nRouteNo);

           if (m_pRouteData[nRouteNo].nTotalPnts < 0 || m_pRouteData[nRouteNo].nTotalPnts > ROUTE_PNT_SIZE_PER_ROUTE)
               m_pRouteData[nRouteNo].nTotalPnts = 0;

           for (nSlotNo = 0;nSlotNo < m_pRouteData[nRouteNo].nTotalPnts;nSlotNo++)
               {
                if (m_pRouteData[nRouteNo].vAllSlot[nSlotNo] < WAYPNT_DATA_START || m_pRouteData[nRouteNo].vAllSlot[nSlotNo] >= WAYPNT_POINT_SIZE)
                    m_pRouteData[nRouteNo].vAllSlot[nSlotNo] = 0;
               }
           for (nSlotNo = m_pRouteData[nRouteNo].nTotalPnts;nSlotNo < ROUTE_PNT_SIZE_PER_ROUTE;nSlotNo++)
                m_pRouteData[nRouteNo].vAllSlot[nSlotNo] = 0;

           RemoveContinuousWptNo(nRouteNo);
          }
}

void  cRouteData::ClearAllData(void)
{
      ClearAllRouteData();
      
      ClearRouteMenu();
}
void  cRouteData::ClearRouteMenu(void)
{
}
void  cRouteData::ClearFactoryReset(void)
{
}

int   cRouteData::SaveData(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
      BACK16 vDummy[16];

      pTemp = pBackData;

      memmove(pTemp, vDummy          ,sizeof(vDummy         )); pTemp += sizeof(vDummy         );

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
int   cRouteData::RestData(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
      BACK16 vDummy[16];

      pTemp = pBackData;

      memmove( vDummy            ,pTemp,sizeof(vDummy          )); pTemp += sizeof(vDummy          );

      TestData();

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
void  cRouteData::TestData(void)
{
}

int   cRouteData::SaveRouteMenu(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
      BACK16 vDummy[16];

      pTemp = pBackData;

      memmove(pTemp, vDummy                  ,sizeof(vDummy                  )); pTemp += sizeof(vDummy                  );

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
int   cRouteData::RestRouteMenu(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
      BACK16 vDummy[16];

      pTemp = pBackData;

      memmove( vDummy                  ,pTemp,sizeof(vDummy                  )); pTemp += sizeof(vDummy                  );

      TestRouteMenu();

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
void  cRouteData::TestRouteMenu(void)
{
}

#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
int   cRouteData::LoadNewSamRouteData(UCHAR *pFileName)
{
      FHANDLE hHandle;
      int     nError;
      DWORD   dReadSize;
      char    vHeadData[128];
      char    vFileName[128];

      if (CheckExistSamDataDirName(1) == 0)
          return(0);

      strcpy(vFileName,GetAppendedSamDataDirName((const char *)pFileName));

      hHandle = OsfOpen((char *)vFileName,OSF_READ_ONLY);
      if (hHandle < OSF_NO_ERROR)
          return(0);

      nError = OsfRead(hHandle,vHeadData,SAM_ALL_DATA_HEAD_LENTH,&dReadSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      if (strncmp(vHeadData,SAM_ROUTE_DATA_HEAD_STR,SAM_ALL_DATA_HEAD_LENTH) != 0)
         {
          OsfClose(hHandle);
          return(0);
         }

      nError = OsfRead(hHandle,m_pRouteData,sizeof(xROUTE) * ROUTE_ALL_SIZE,&dReadSize);

      OsfClose(hHandle);

      if (nError < OSF_NO_ERROR)
         {
          ClearAllRouteData();
          return(0);
         }

      return(1);
}
int   cRouteData::SaveNewSamRouteData(UCHAR *pFileName)
{
      FHANDLE hHandle;
      int     nError;
      int     i,k;
      DWORD   dWriteSize;
      char    vFileName[128];

      if (CheckExistSamDataDirName(1) == 0)
          return(0);

      strcpy(vFileName,GetAppendedSamDataDirName((const char *)pFileName));

      hHandle = OsfOpen((char *)vFileName,OSF_READ_WRITE | OSF_CREATE_ALWAYS);
      if (hHandle < OSF_NO_ERROR)
          return(0);

      nError = OsfWrite(hHandle,SAM_ROUTE_DATA_HEAD_STR,SAM_ALL_DATA_HEAD_LENTH,&dWriteSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      nError = OsfWrite(hHandle,m_pRouteData,sizeof(xROUTE) * ROUTE_ALL_SIZE,&dWriteSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      OsfClose(hHandle);

      return(1);
}
int   cRouteData::GetRouteDataFileNames(SAMDATAFILE *pSamDataFiles,int nCounts)
{
      OSFDOSDirEntry xDirEntry;
      char    vDirName[256];
      char    vFileName[256];
      char    vFullName[256];
      char    vHeadName[SAM_ALL_DATA_HEAD_LENTH * 2];
      FHANDLE hDirHandle;
      FHANDLE hFileHandle;
      int     nError;
      int     nSizeX;
      DWORD   dReadSize;
      int     nFoundFiles = 0;

      if (!SdCardGetCardInsertStatus())
          return(0);

      if (SdCardGetCardChangStatus())
         {
          SdCardSetCardChangStatus(0);
          OsfResetDisk(SDCARD_MOUNT_NO);
          OsfMountDisk(SDCARD_MOUNT_NO);
         }

      strcpy(vDirName,GetAppendedSamDataDirName("*"));

      hDirHandle = OsfFindFirstEx(vDirName,&xDirEntry,vFileName,sizeof(vFileName));
      if (hDirHandle < OSF_NO_ERROR)
          return(0);

      while (1)
            {
             strcpy(vFullName,GetAppendedSamDataDirName(vFileName));

             hFileHandle = OsfOpen((char *)vFullName,OSF_READ_ONLY);
             if (hFileHandle >= OSF_NO_ERROR)
                {
                 memset(vHeadName,0x00,SAM_ALL_DATA_HEAD_LENTH);

                 nError = OsfRead(hFileHandle,vHeadName,SAM_ALL_DATA_HEAD_LENTH,&dReadSize);

                 if (nError >= OSF_NO_ERROR && (strncmp(vHeadName,SAM_ROUTE_DATA_HEAD_STR,SAM_ALL_DATA_HEAD_LENTH) == 0))
                    {
                     nSizeX = 0;

                     if (nFoundFiles < nCounts)
                        {
                         strcpy(pSamDataFiles->vFileName,vFileName);
                         pSamDataFiles->nPointCnt = nSizeX;
                         pSamDataFiles->nFileType = SAM_DATA_FILE_TYPE_NEW;
                         strcpy(pSamDataFiles->vTimeDate,GetTimeChrStr(xDirEntry.CreateDateTime.Hour,xDirEntry.CreateDateTime.Minute,GetTimeTimeFormat()));
                         strcat(pSamDataFiles->vTimeDate," ");
                         strcat(pSamDataFiles->vTimeDate,GetDateChrStr(xDirEntry.CreateDateTime.Year1980 + 1980,xDirEntry.CreateDateTime.Month,xDirEntry.CreateDateTime.Day,GetTimeDateFormat()));

                         pSamDataFiles->nYear   = xDirEntry.CreateDateTime.Year1980 + 1980;
                         pSamDataFiles->nMonth  = xDirEntry.CreateDateTime.Month;
                         pSamDataFiles->nDay    = xDirEntry.CreateDateTime.Day;
                         pSamDataFiles->nHour   = xDirEntry.CreateDateTime.Hour;
                         pSamDataFiles->nMinute = xDirEntry.CreateDateTime.Minute;

                         ++nFoundFiles;
                         ++pSamDataFiles;
                        }
                    }

                 OsfClose(hFileHandle);
                }

             nError = OsfFindNextEx(hDirHandle,&xDirEntry,vFileName,sizeof(vFileName));
             if (nError < OSF_NO_ERROR)
                 break;
            }

      OsfFindClose(hDirHandle);

      return(nFoundFiles);
}
#endif

