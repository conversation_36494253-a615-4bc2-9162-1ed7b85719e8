/*...........................................................................*/
/*.                  File Name : SYSPLL.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysPLL.h"

#include <string.h>

//=============================================================================
static xSYS_CLOCK *G_pSysCLKP = (xSYS_CLOCK *)CLKPWR_PHSY_BASE_ADDR;
//=============================================================================

void  SysInitPLLs(void)
{
      volatile DWORD dLoopDelay;

  #if (ARM_FCLK != 132000000)
      SysSetPLLBCLK();
      SysSetPLLCPU0();
      SysSetPLLCPU1();

      SysDoPLLChange();
//    SysDelayLoop(0x02FFFFF);

//    for(dLoopDelay = 0;dLoopDelay < 0x02FFFFF;dLoopDelay++);
      dLoopDelay = 0x02FFFFF;
      while (dLoopDelay--);

      while (SysIsPLLStable());

//    for(dLoopDelay = 0;dLoopDelay < 0x02FFFFF;dLoopDelay++);
      dLoopDelay = 0x02FFFFF;
      while (dLoopDelay--);
  #endif
}
void  SysDoPLLChange(void)
{
      G_pSysCLKP->dPWRMODE |= (1 << 15);          // CHGPLL=1
}
DWORD SysIsPLLStable(void)
{
      return(G_pSysCLKP->dPWRMODE & (1 << 15));
}
void  SysSetPLLCPU0(void)
{
      DWORD dTempX;

      dTempX = ((CPU_PLL0_PDIV) << 18) |       // PDIV
               ((CPU_PLL0_MDIV) <<  8) |       // MDIV
               ((CPU_PLL0_SDIV) <<  0);        // SDIV

      G_pSysCLKP->dPLLSETREG[0] = dTempX;
}
void  SysSetPLLCPU1(void)
{
      DWORD dTempX;

      dTempX = ((CPU_PLL1_PDIV) << 18) |       // PDIV
               ((CPU_PLL1_MDIV) <<  8) |       // MDIV
               ((CPU_PLL1_SDIV) <<  0);        // SDIV

      G_pSysCLKP->dPLLSETREG[1] = dTempX;
}
void  SysSetPLLSdRamDIV(void)
{
}
void  SysSetPLLBCLK(void)
{
      DWORD dTempX;

      dTempX = ((HCLK_FACT - 1)       <<  8) |       // CLKDIV2CPU0
               ((PLL_CLKSELCPU0_PLL0) <<  4) |       // PLL0
               ((FCLK_FACT - 1)       <<  0);        // CLKDIV1CPU0

      G_pSysCLKP->dCLKMODEREG0 = dTempX;

      dTempX = ((PCLK_FACT - 1)       << 12) |       // CLKDIVPCLK
               ((BCLK_FACT - 1)       <<  8) |       // CLKDIVBCLK
               ((PLL_CLKSELMCLK_PLL0) <<  4) |       // PLL0
//             ((PLL_CLKSELMCLK_FCLK) <<  4) |       // FCLK
               ((MCLK_FACT - 1)       <<  0);        // CLKDIVMCLK

      G_pSysCLKP->dCLKMODEREG1 = dTempX;
}
void  SysSetPLL1PowerDownMode(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysCLKP->dCLKMODEREG0;

      if (nDisableEnableMode)
          dTempX = dTempX |  (1 << 30);
      else
          dTempX = dTempX & ~(1 << 30);

      G_pSysCLKP->dCLKMODEREG0 = dTempX;
}
void  SysSetPLL2PowerDownMode(int nDisableEnableMode)
{
}
void  SysSetPLL0DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV)
{
      G_pSysCLKP->dPLLSETREG[0] = (dPDIV << 18) | (dMDIV << 8) | (dSDIV);
}
void  SysSetPLL1DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV)
{
      G_pSysCLKP->dPLLSETREG[1] = (dPDIV << 18) | (dMDIV << 8) | (dSDIV);
}
void  SysSetPLL2DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV)
{
}

