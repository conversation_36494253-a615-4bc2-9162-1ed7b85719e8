#include "Wnd.hpp"

#ifndef __VIEW_INPUT_DATA_WND_HPP__
#define __VIEW_INPUT_DATA_WND_HPP__

#define PPS_INDI_W	20
#define PPS_INDI_H	20

class CViewInputDataWnd : public CWnd 
{
	protected:
		int  m_nLineNum;
		BOOL m_bPause;
		BOOL m_bGpsMode;
		double m_lfCurHDOP;
		int m_nCurFixMode;

	public:
		enum{
			GPS_MD_NONE = 0,
			GPS_MD_AUTO_NOT_FIXED,
			GPS_MD_AUTO_2D,
			GPS_MD_AUTO_3D,
			GPS_MD_MAN_NOT_FIXED,
			GPS_MD_MAN_2D,
			GPS_MD_MAN_3D,
			MAX_GPS_MD
		};
		
	private:
		void DrawSatNo(int nX, int nY, int nSatNo, BOOL bUse);
		void DrawSigBar(int nX, int nY, int nSigBarH, int nMaxSigBarW, int nMaxSigVal, int nCurSigVal,BOOL bUse);
		void DrawSigBackGround(int nX, int nY, int nBackW, int nBackH);
		void DrawGNSSName(int nX, int nY, BYTE *pStrName, int nTotSatNum);
		void DrawLeftGNSS();
		void DrawRightGNSS();
		void DrawDescriptionArea(int nX, int nY, int nW, int nH, double lfHDOP, int nGPSMode);
		void DrawCenterLine();
		void DrawGNSSSigWnd();
		
	public:
		CViewInputDataWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		virtual void OnKeyEvent(int nKey, DWORD nFlags);
		virtual void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		virtual void OnActivate();
		virtual void OnTimer(); 

		void UpdateDescriptionArea();
		void DrawFunctionBtn();
		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		void SetStart()           { m_bPause = 0;      }
		void SetPause()           { m_bPause = 1;      }
		BOOL IsPause()            { return m_bPause;   }
		void PrintSentence(BYTE *pszSentence);

		BOOL IsGpsMode() { return m_bGpsMode; }
};

#endif	// End of __VIEW_INPUT_DATA_WND_HPP__
