#ifndef __LONG_RANGE_MESSAGE_HPP__
#define __LONG_RANGE_MESSAGE_HPP__

#include <stdio.h>
#include <string.h>

class CLRMsg {
public:

	CLRMsg() {
		m_bAcked    = 0;
		m_bAutoMode = 0;
		m_bReplyOK  = 0;
		m_nSeqNum   = 0;
		m_chControlFlag = '0';
		m_nYear = 0; m_nMonth = 0; m_nDay = 0;
		m_nHour = 0; m_nMin = 0; m_nSec = 0;
		m_dwRequester = MMSI_NULL_VALUE;
	}

	void  SetAck(BOOL bAck=1) { m_bAcked = bAck; }
	BOOL  GetAck() { return m_bAcked; }
	void  SetMode(BOOL bAuto=1) { m_bAutoMode = bAuto; }
	BOOL  GetMode() { return m_bAutoMode; }
	void  SetReply(BOOL bOK=1) { m_bReplyOK = bOK; }
	BOOL  GetReply() { return m_bReplyOK; }
	void  SetSeqNum(int nSeqNum) { m_nSeqNum = nSeqNum; }
	int   GetSeqNum() { return m_nSeqNum; }
	void  SetControlFlag(char chControlFlag) { m_chControlFlag = chControlFlag; }
	char  GetControlFlag() { return m_chControlFlag; }
	void  SetDate(int nYear, int nMonth, int nDay) { m_nYear = nYear; m_nMonth = nMonth; m_nDay = nDay; }
	void  SetTime(int nHour, int nMin, int nSec) { m_nHour = nHour; m_nMin = nMin; m_nSec = nSec; }
	void  GetDate(char *pszDate) { sprintf(pszDate, "%02d-%02d", m_nMonth, m_nDay); }
	void  GetTime(char *pszTime) { sprintf(pszTime, "%02d:%02d", m_nHour, m_nMin); }
	void  SetFunction(char *pszFunc) { strcpy(m_szFunction, pszFunc); }
	void  GetFunction(char *pszFunc) { strcpy(pszFunc, m_szFunction); }
	void  SetRequester(DWORD dwMMSI) { m_dwRequester = dwMMSI; }
	DWORD GetRequester() { return m_dwRequester; }
	void  SetName(char *pszName) { strcpy(m_szName, pszName); }
	void  GetName(char *pszName) { strcpy(pszName, m_szName); }

    /* Add LR sentence element : HSI 2012.05.01 */
	void  SetLRFSentence(char *pszSentence,int seq) { strcpy(LRMdata[seq].LRFSentence, pszSentence); }
	void  SetLRISentence(char *pszSentence,int seq) { strcpy(LRMdata[seq].LRISentence, pszSentence); }
	char * GetLRFSentence(int seq) { return LRMdata[seq].LRFSentence; }
	char * GetLRISentence(int seq) { return LRMdata[seq].LRISentence; }

protected:
	BOOL  m_bAcked;
	BOOL  m_bAutoMode;
	BOOL  m_bReplyOK;
	int   m_nSeqNum;
	char  m_chControlFlag;
	int   m_nYear, m_nMonth, m_nDay;
	int   m_nHour, m_nMin, m_nSec;
	DWORD m_dwRequester;
	char  m_szFunction[28];
	char  m_szName[24];
	
	struct LRMSentence 	// Add LR sentence element : HSI 2012.05.01
	{
		
		char LRFSentence[128];
		char LRISentence[128];

	}LRMdata[10]; 
	
};

#endif //:~ __LONG_RANGE_MESSAGE_HPP__
