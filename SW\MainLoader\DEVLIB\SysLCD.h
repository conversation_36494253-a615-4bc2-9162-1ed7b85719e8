/*...........................................................................*/
/*.                  File Name : SYSLCD.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.04                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSLCD_H__
#define  __SYSLCD_H__

//=============================================================================
#if defined(__POLLUX__)
    #define LCD_I2C_GPIO_PORT   (GPIOA_PHSY_BASE_ADDR)
    #define LCD_I2C_CLCK_BIT                       29
    #define LCD_I2C_DATA_BIT                       28
    #define LCD_TYPE_BIT                            0

    #define LCD_ON_BIT                              2
    #define LCD_SEN_BIT                             3
    #define LCD_RST_BIT                             4

    #define LCD_PWR_BIT                             3  // 4.3 "
#else                           // SPICA
  #if defined(__N500_MODEL__)
//  #define LCD_ON_BIT                             14  // PORT-D
    #define LCD_ON_BIT                             13  // PORT-D
  #else
    #define LCD_ON_BIT                              2  // ALIVE
  #endif
    #define LCD_LVDS_PWR_DOWN_BIT                  30  // PORT-C
    #define LCD_BACK_LIGHT_BIT                     29  // PORT-C
#endif
//-----------------------------------------------------------------------------
#define LCD_I2C_DELAY_0                         0
#define LCD_I2C_DELAY_X                      1000
#define LCD_I2C_DELAY_Y                      2000
#define LCD_I2C_DELAY_Z                      3000
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SysInitLCD(void);
void  SysSetLcdOn(void);
void  SysSetLcdOff(void);
void  SysSetLcdSEN(int nBitData,DWORD dDelay);
void  SysSetLcdRST(int nBitData,DWORD dDelay);
void  SysSetLcdPWM(int nPercent);
void  SysSetLedPWMbyPercent(int nPercent);
void  SysSetLedPWMbyDutyVal(int nDutyVal);
int   SysWriteDataToLcdI2C(UCHAR bAddr,UCHAR bData);
void  SysSetLcdSDA(int nBitData,DWORD dDelay);
void  SysSetLcdCLK(int nBitData,DWORD dDelay);

#ifdef  __SPICA__
void  SysSetLcdLvdsPwrDownMode(int nMode);
void  SysSetLcdBackLightMode(int nMode);
#endif

#ifdef  __cplusplus
}
#endif

#endif

