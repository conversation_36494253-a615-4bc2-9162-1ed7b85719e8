/*...........................................................................*/
/*.                  File Name : MyriadPro12bGrk.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY MyriadPro12bRus_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro12bGrk_offset_table[129] = {
0x0000,0x0003,0x0006,0x0009,0x000c,0x000f,0x0012,0x0015,0x0018,0x001b,0x001e,0x0022,0x0025,0x0028,0x002b,0x002e,
0x0031,0x0036,0x003d,0x0047,0x004b,0x0055,0x0061,0x0067,0x006a,0x0076,0x0079,0x0085,0x0091,0x0097,0x00a1,0x00aa,
0x00b1,0x00bb,0x00c3,0x00cc,0x00d6,0x00e1,0x00e5,0x00ee,0x00f8,0x0105,0x010f,0x0118,0x0123,0x012d,0x0136,0x0139,
0x0142,0x014a,0x0153,0x015f,0x0168,0x0173,0x017e,0x0183,0x018c,0x0195,0x019c,0x01a5,0x01a9,0x01b1,0x01ba,0x01c3,
0x01cb,0x01d4,0x01db,0x01e2,0x01eb,0x01f4,0x01f8,0x0200,0x0208,0x0211,0x0219,0x0220,0x0229,0x0232,0x023b,0x0242,
0x024b,0x0252,0x025a,0x0265,0x026d,0x0278,0x0283,0x0288,0x0290,0x0299,0x02a1,0x02ac,0x02af,0x02b2,0x02b5,0x02b8,
0x02bb,0x02be,0x02c1,0x02c4,0x02c7,0x02ca,0x02cd,0x02d0,0x02d3,0x02d6,0x02d9,0x02dc,0x02df,0x02e2,0x02e5,0x02e8,
0x02eb,0x02ee,0x02f1,0x02f4,0x02f7,0x02fa,0x02fd,0x0300,0x0303,0x0306,0x0309,0x030c,0x030f,0x0312,0x0315,0x0318,
0x031b};



ROMDATA PEGUBYTE MyriadPro12bGrk_data_table[1900] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x66, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x02, 0xc0, 0x00, 0x00, 0x00, 0x04, 0x00, 0x07, 0x00, 0x03, 0xc0, 0x07, 0x80, 0x03, 0x80, 0x00, 
0x60, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1b, 0x56, 0xe0, 0x1b, 0xf6, 0xc3, 0x6c, 0x31, 0xe0, 0x6c, 
0x16, 0x3c, 0x6a, 0x38, 0x3e, 0x1f, 0x8e, 0x0f, 0xcf, 0xe6, 0x18, 0x3c, 0x33, 0x1c, 0x70, 0x38, 
0xe3, 0x8c, 0xfe, 0x0f, 0x0f, 0xf3, 0xe0, 0x7f, 0x3f, 0xd8, 0x21, 0xf1, 0xc7, 0x66, 0xc1, 0xe1, 
0x88, 0x10, 0xe0, 0x60, 0xe1, 0xb5, 0x00, 0x0f, 0x80, 0x07, 0xc0, 0x07, 0xc0, 0x07, 0xc0, 0x00, 
0x70, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x1b, 0x36, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x34, 0xe0, 0x1b, 0xf6, 0xc3, 0x6c, 0x37, 0xf0, 0x6e, 
0x76, 0xfe, 0x66, 0x38, 0x3f, 0x9f, 0x8e, 0x0f, 0xcf, 0xe6, 0x18, 0xfe, 0x33, 0x38, 0x70, 0x38, 
0xe3, 0x8c, 0xfe, 0x3f, 0x8f, 0xf3, 0xf0, 0x7f, 0x3f, 0xdc, 0xe7, 0xf8, 0xee, 0x66, 0xc7, 0xf1, 
0x9e, 0x70, 0xc0, 0xc0, 0xc3, 0x35, 0x00, 0x19, 0x80, 0x06, 0x00, 0x03, 0x80, 0x06, 0xc0, 0x00, 
0x38, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x1b, 0x36, 0x08, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0xb0, 0x1b, 0x06, 0xc3, 0x6c, 0x26, 0x30, 0x67, 
0x74, 0xc6, 0x00, 0x6c, 0x31, 0x98, 0x1b, 0x0c, 0x01, 0xc6, 0x18, 0xc6, 0x33, 0x70, 0xd8, 0x28, 
0xb3, 0xcc, 0x00, 0x31, 0x8c, 0x33, 0x18, 0x38, 0x06, 0x0e, 0xc6, 0xd8, 0x6c, 0x66, 0xc6, 0x31, 
0x87, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x80, 0x03, 0x80, 0x03, 0x00, 0x0c, 0x60, 0x00, 
0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x01, 0xb0, 0x03, 0x00, 0xc3, 0x0c, 0x0c, 0x18, 0x03, 
0x41, 0x83, 0x18, 0x6c, 0x31, 0x98, 0x1b, 0x0c, 0x01, 0x86, 0x19, 0x83, 0x33, 0x60, 0xd8, 0x6d, 
0xb3, 0xcc, 0x00, 0x60, 0xcc, 0x33, 0x18, 0x18, 0x06, 0x06, 0x8c, 0xcc, 0x7c, 0x66, 0xcc, 0x19, 
0x83, 0x41, 0xb1, 0xe7, 0xe3, 0x33, 0x8d, 0x99, 0x1c, 0xc7, 0xc3, 0xc6, 0x1f, 0x8c, 0x66, 0x67, 
0x18, 0x63, 0x63, 0x0e, 0x1c, 0x3f, 0xc7, 0x03, 0xc7, 0xff, 0x99, 0xc5, 0xc7, 0x3b, 0x36, 0x31, 
0x8c, 0x67, 0x1c, 0x33, 0x98, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x01, 0xb0, 0xc3, 0xf0, 0xff, 0x0c, 0x0c, 0x18, 0x03, 
0xc1, 0x83, 0x18, 0x6c, 0x3f, 0x18, 0x1b, 0x0f, 0xc3, 0x87, 0xf9, 0xbb, 0x33, 0xe0, 0xd8, 0x6d, 
0xb3, 0x6c, 0x7c, 0x60, 0xcc, 0x33, 0x18, 0x0e, 0x06, 0x07, 0x8c, 0xcc, 0x38, 0x66, 0xcc, 0x19, 
0x83, 0xc3, 0xf3, 0xe7, 0xf3, 0x31, 0x9f, 0x9b, 0x8c, 0xc6, 0xe7, 0xcc, 0x1f, 0xcf, 0xe6, 0x6e, 
0x3c, 0x63, 0x73, 0x1e, 0x3e, 0x3f, 0xcf, 0x87, 0x8f, 0xff, 0x98, 0xc9, 0xe3, 0x33, 0x36, 0x60, 
0xcc, 0x63, 0x3e, 0x31, 0xb0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0xb8, 0xc3, 0xf0, 0xff, 0x0c, 0x0c, 0x18, 0x01, 
0x81, 0x83, 0x18, 0xee, 0x3f, 0x18, 0x31, 0x8f, 0xc7, 0x07, 0xf9, 0xbb, 0x33, 0xe1, 0xdc, 0x6d, 
0x33, 0x6c, 0x7c, 0x60, 0xcc, 0x33, 0xf0, 0x0c, 0x06, 0x03, 0x0c, 0xcc, 0x38, 0x3f, 0x8c, 0x19, 
0x81, 0x86, 0x33, 0xe6, 0x33, 0x31, 0xb1, 0x9b, 0xc6, 0xcc, 0x67, 0xcc, 0x18, 0xcf, 0xe6, 0x7c, 
0x3c, 0x63, 0x33, 0x30, 0x63, 0x19, 0x98, 0xcc, 0x18, 0xc6, 0x18, 0xdb, 0x33, 0xb3, 0x36, 0x6c, 
0xcc, 0x63, 0x63, 0x31, 0xb6, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf8, 0xc3, 0x00, 0xc3, 0x0c, 0x0c, 0x18, 0x01, 
0x81, 0x82, 0x18, 0xfe, 0x31, 0x98, 0x31, 0x8c, 0x06, 0x06, 0x19, 0x83, 0x33, 0x71, 0x8c, 0x65, 
0x33, 0x3c, 0x00, 0x60, 0xcc, 0x33, 0xe0, 0x18, 0x06, 0x03, 0x0c, 0xcc, 0x7c, 0x1f, 0x8c, 0x11, 
0x81, 0x86, 0x31, 0xe6, 0x33, 0x31, 0xb1, 0x98, 0xc6, 0x8c, 0x63, 0xcc, 0x18, 0xcc, 0x66, 0x78, 
0x76, 0x63, 0x36, 0x30, 0x63, 0x19, 0x98, 0xcc, 0x18, 0xc6, 0x18, 0xdb, 0x31, 0xe3, 0x36, 0x6c, 
0xcc, 0x63, 0x63, 0x31, 0xb6, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf8, 0x03, 0x00, 0xc3, 0x0c, 0x06, 0x30, 0x01, 
0x80, 0xc6, 0x18, 0xfe, 0x31, 0x98, 0x31, 0x8c, 0x0e, 0x06, 0x18, 0xc6, 0x33, 0x31, 0x8c, 0x67, 
0x33, 0x3c, 0x00, 0x31, 0x8c, 0x33, 0x00, 0x30, 0x06, 0x03, 0x07, 0xf8, 0x6c, 0x06, 0x06, 0x31, 
0x81, 0x86, 0x33, 0x06, 0x33, 0x31, 0xb1, 0x98, 0xc6, 0x8c, 0x66, 0x0c, 0x18, 0xcc, 0x46, 0x6c, 
0x66, 0x63, 0x1e, 0x30, 0x63, 0x19, 0x98, 0xcc, 0x18, 0xc6, 0x18, 0xdb, 0x30, 0xe3, 0x36, 0x6c, 
0xcc, 0x63, 0x63, 0x31, 0xb6, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x03, 0x18, 0x03, 0xf0, 0xc3, 0x0c, 0x07, 0xf0, 0x01, 
0x81, 0xef, 0x1d, 0xc6, 0x3f, 0x18, 0x7f, 0xcf, 0xdf, 0xe6, 0x18, 0xfe, 0x33, 0x3b, 0x8c, 0x67, 
0x33, 0x1c, 0xfe, 0x3f, 0x8c, 0x33, 0x00, 0x7f, 0x86, 0x03, 0x03, 0xf0, 0xce, 0x06, 0x0f, 0x79, 
0x81, 0x83, 0xfb, 0xf6, 0x33, 0xbf, 0x1f, 0xdf, 0x83, 0x87, 0xc7, 0xef, 0x18, 0xc7, 0xc7, 0x6e, 
0x66, 0x7f, 0x9c, 0x3c, 0x3e, 0x19, 0x9f, 0x8e, 0x0f, 0x87, 0x1f, 0x8f, 0xe0, 0xc1, 0xfc, 0x3f, 
0x8e, 0x7e, 0x3e, 0x3f, 0x1f, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x07, 0x1c, 0x03, 0xf0, 0xc3, 0x0c, 0x03, 0xc0, 0x01, 
0x81, 0xef, 0x1d, 0x87, 0x3e, 0x18, 0x7f, 0xcf, 0xdf, 0xe6, 0x18, 0x78, 0x33, 0x1f, 0x0e, 0x66, 
0x33, 0x0c, 0xfe, 0x1e, 0x0c, 0x33, 0x00, 0x7f, 0x86, 0x03, 0x00, 0xc1, 0xc7, 0x06, 0x0f, 0x79, 
0x81, 0x83, 0x99, 0xe6, 0x33, 0x9e, 0x1c, 0xdb, 0x03, 0x03, 0x83, 0xc7, 0xd8, 0xc3, 0x87, 0x67, 
0xe7, 0x7d, 0x1c, 0x1f, 0x1c, 0x31, 0x9f, 0x07, 0xc7, 0x03, 0x0f, 0x07, 0xc1, 0xe0, 0xf8, 0x33, 
0x0e, 0x3c, 0x1c, 0x1e, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x03, 0x00, 0x00, 0x01, 0x80, 0xc0, 0x00, 0x00, 
0x00, 0x60, 0x00, 0x03, 0x00, 0x00, 0x18, 0x01, 0x80, 0x00, 0x00, 0x03, 0x01, 0xe0, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x03, 0x00, 0x00, 0x01, 0x80, 0xc0, 0x00, 0x00, 
0x00, 0x60, 0x00, 0x06, 0x00, 0x00, 0x18, 0x01, 0x80, 0x00, 0x00, 0x03, 0x03, 0x30, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x18, 0x03, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 
0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x03, 0x07, 0x38, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 


};

xFONTYY MyriadPro12bGrk_Font = {0x01, 19, 0, 19, 0, 0, 19, 100, 0x0374, 0x03f3,
(PEGUSHORT *) MyriadPro12bGrk_offset_table, &MyriadPro12bRus_Font,
(PEGUBYTE *) MyriadPro12bGrk_data_table};


