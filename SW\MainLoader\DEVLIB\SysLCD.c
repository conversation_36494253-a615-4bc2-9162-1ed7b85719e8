/*...........................................................................*/
/*.                  File Name : SYSLCD.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.04                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysGPIO.h"
#include "SysAlive.h"
#include "SysPWM.h"
#include "SysLCD.h"

#include <string.h>

void  SysInitLCD(void)
{
#if defined(__POLLUX__)
/*
      if (SysGetDeviceType() == DEVICE_TYPE_04_3)
         {
          SysSetALIVExBitData(3,1);
          SysSetALIVExBitData(4,1);
         }
      else
*/
         {
      #if !defined(__N430_MODEL__)
          SysSetLcdRST(1,LCD_I2C_DELAY_Z * 10);
          SysSetLcdSEN(0,LCD_I2C_DELAY_Z * 10);
//        SysSetLcdOn();
      #endif
         }
#else                           // SPICA
//    SysSetLcdOn();
      SysSetLcdLvdsPwrDownMode(1);
#endif
}
void  SysSetLcdOn(void)
{
#if defined(__POLLUX__)
      HWORD wPeriod;

      SysSetALIVExBitData(LCD_ON_BIT,1);

      if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
         {
          wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wPeriod + 1);  // L=100%
         }
      else
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
#else                           // SPICA
      HWORD wPeriod;
      int   nNavisType = SysGetNavisModelType();

      SysSetLcdLvdsPwrDownMode(1);
      SysSetLcdBackLightMode(1);

  #if defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR, LCD_ON_BIT, 1);
  #else
      SysSetALIVExBitData(LCD_ON_BIT,1);
  #endif

      wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);

      if (nNavisType == NAVIS_TYPE_700)
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wPeriod + 1);  // L=100%
      else
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
#endif
}
void  SysSetLcdOff(void)
{
#if defined(__POLLUX__)
      HWORD wPeriod;

      SysSetALIVExBitData(LCD_ON_BIT,0);

      if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
         {
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
         }
      else
         {
          wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wPeriod + 1);  // L=100%
         }
#else                           // SPICA
      HWORD wPeriod;
      int   nNavisType = SysGetNavisModelType();

      SysSetLcdLvdsPwrDownMode(0);
      SysSetLcdBackLightMode(0);

  #if defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR, LCD_ON_BIT, 0);
  #else
      SysSetALIVExBitData(LCD_ON_BIT,0);
  #endif

      wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);

      if (nNavisType == NAVIS_TYPE_700)
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
      else
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wPeriod + 1);  // L=100%
#endif
}
void  SysSetLcdSEN(int nBitData,DWORD dDelay)
{
#if defined(__POLLUX__)
  #if !defined(__N430_MODEL__)
      SysSetALIVExBitData(LCD_SEN_BIT,nBitData);
  #endif
#else                           // SPICA
#endif
}
void  SysSetLcdRST(int nBitData,DWORD dDelay)
{
#if defined(__POLLUX__)
  #if !defined(__N430_MODEL__)
      SysSetALIVExBitData(LCD_RST_BIT,nBitData);
  #endif
#else                           // SPICA
#endif
}
void  SysSetLcdPWM(int nPercent)
{
#if defined(__POLLUX__)
      HWORD wPeriod;
      HWORD wDuty;

      if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
         {
          wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
          wDuty   = nPercent * wPeriod / 100;
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wDuty);
         }
      else
         {
          wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
          wDuty   = (100 - nPercent) * wPeriod / 100;
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wDuty);
         }
#else                           // SPICA
      HWORD wPeriod;
      HWORD wDuty;
      int   nNavisType = SysGetNavisModelType();

      if (nNavisType == NAVIS_TYPE_700)
         {
          wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
          wDuty   = nPercent * wPeriod / 100;
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wDuty);
         }
      else
         {
          wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
          wDuty   = (100 - nPercent) * wPeriod / 100;
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wDuty);
         }
#endif
}
void  SysSetLedPWMbyPercent(int nPercent)
{
#if defined(__POLLUX__)
      HWORD wPeriod;
      HWORD wDuty;

      wPeriod = SysGetPWMPeriod(PWM_KEY_CHANNEL);
      wDuty   = (100 - nPercent) * wPeriod / 100;
//    if (nPercent == 0)
//        wDuty = wPeriod - 1;
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,wDuty);
#else                           // SPICA
      HWORD wPeriod;
      HWORD wDuty;

      wPeriod = SysGetPWMPeriod(PWM_KEY_CHANNEL);
      wDuty   = (100 - nPercent) * wPeriod / 100;
//    if (nPercent == 0)
//        wDuty = wPeriod - 1;
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,wDuty);
#endif
}
void  SysSetLedPWMbyDutyVal(int nDutyVal)
{
#if defined(__POLLUX__)
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,nDutyVal);
#else                           // SPICA
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,nDutyVal);
#endif
}
int   SysWriteDataToLcdI2C(UCHAR bAddr,UCHAR bData)
{
#if defined(__POLLUX__)
  #if !defined(__N430_MODEL__)
      HWORD wDataX;
      HWORD wMaskX;

      wDataX = bAddr;
      wDataX = (wDataX << 8) & 0x0f00;
      wDataX = (wDataX | bData);         // xxx0AAAADDDDDDDD
                                         //     321076543210
      wMaskX = 0x1000;
      SysSetLcdSEN(0,LCD_I2C_DELAY_Z);                // SEN = L

      while (wMaskX)
            {
             if (wDataX & wMaskX)
                 SysSetLcdSDA(1,LCD_I2C_DELAY_X);     // DATA = H
             else
                 SysSetLcdSDA(0,LCD_I2C_DELAY_X);     // DATA = L

             SysSetLcdCLK(1,LCD_I2C_DELAY_Y);         // CLCK = H
             SysSetLcdCLK(0,LCD_I2C_DELAY_Y);         // CLCK = L
             SysSetLcdSDA(0,LCD_I2C_DELAY_X);         // DATA = L

             wMaskX >>= 1;
            }

      SysDelayLoop(LCD_I2C_DELAY_Z);
      SysSetLcdSEN(1,LCD_I2C_DELAY_0);                // SEN = H
  #endif

      return(1);
#else                           // SPICA
      return(1);
#endif
}
void  SysSetLcdSDA(int nBitData,DWORD dDelay)
{
#if defined(__POLLUX__)
  #if !defined(__N430_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)LCD_I2C_GPIO_PORT,LCD_I2C_DATA_BIT,nBitData);
      SysDelayLoop(dDelay);
  #endif
#else                           // SPICA
#endif
}
void  SysSetLcdCLK(int nBitData,DWORD dDelay)
{
#if defined(__POLLUX__)
  #if !defined(__N430_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)LCD_I2C_GPIO_PORT,LCD_I2C_CLCK_BIT,nBitData);
      SysDelayLoop(dDelay);
  #endif
#else                           // SPICA
#endif
}

#ifdef  __SPICA__
void  SysSetLcdLvdsPwrDownMode(int nMode)
{
  #if !defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR,LCD_LVDS_PWR_DOWN_BIT,nMode);
  #endif
}
void  SysSetLcdBackLightMode(int nMode)
{
  #if !defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR,LCD_BACK_LIGHT_BIT,nMode);
  #endif
}
#endif
