#include "Wnd.hpp"

#ifndef __ALARM_LIST_WND_HPP__
#define __ALARM_LIST_WND_HPP__

class CAlarmListWnd : public CWnd {
	protected:
		int  m_nCurSel;
		int  m_nStartViewPos;
		int  m_nCurAlarmID;
		BOOL m_bAckable;

	public:
		CAlarmListWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		
		void DrawFuncBtn();	
		void DrawAlarmListScrollBar();
		void DrawAlarmList();
		void DrawWnd(BOOL bRedraw = 1);

		int GetCurSelAlarmID();
		void SetFocus(int nFocus)   { m_nFocus = nFocus;   }
		void SetCurSel(int nCurSel) { m_nCurSel = nCurSel; }
		int  GetCurSel() { return m_nCurSel; }
		void SendAck();
};

#endif

