/*##################################################################
  FILE    : TECH_VER.H

  USE     :	Technologies Library version File.
  PROJECT : C-Map Library.

  AUTHOR  : GE[080109].
  UPDATED : 
  ##################################################################

*/


#ifndef __TECH_VER__
	#define __TECH_VER__

/*****************************************************************************
  Constants definition section.
 *****************************************************************************/


#define LIB_CRYPT_VERSION         		1   	/* Version number.							*/
#define LIB_CRYPT_SUBVERSION      		36  	/* Sub version number.						*/
#define LIB_CRYPT_BUILD           		0  		/* Build number.							*/

#define LIB_CRYPT_STATUS         		'B'  	/* 'R' - Released.							*/
												/* 'A' - Alpha (not in SSafe/Documented).	*/
                        	           			/* 'B' - Beta version.						*/
												/* 'X' - Critical bug inside.				*/
												/* 'M' - Modified.							*/

#define LIB_CRYPT_DAY            		20
#define LIB_CRYPT_MONTH          		2
#define LIB_CRYPT_YEAR         			2008

/**
 * @defgroup INFO Information
 * @{
 */

String* LibTechGetVersion(void);

/**
 * @}
 */


/*****************************************************************************
  END of Code.
 *****************************************************************************/


#endif /* #ifndef __TECH_VER__ */

