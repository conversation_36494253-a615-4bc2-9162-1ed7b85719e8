#ifndef __AIS_ENCODER_HPP__
#define __AIS_ENCODER_HPP__

#include "../type.hpp"
#include "../const.h"

class CAISEncoder 
{
	private:
		UCHAR *m_pStrEnSixBitASCII;
		int m_nEnSixBitASCIISize;

	public:
		enum{
			AIS_ENCODER_ERR_TYPE_NONE = 0,
			AIS_ENCODER_ERR_TYPE_INV_SRC,
			AIS_ENCODER_ERR_TYPE_INV_DST,
			AIS_ENCODER_ERR_TYPE_SRC_UNDERFLOW,
			AIS_ENCODER_ERR_TYPE_INV_DATA,
			MAX_AIS_ENCODER_ERR_TYPE	
		};
		
	public:
		CAISEncoder();
		~CAISEncoder();

	public:
		UCHAR CvtEnSixBitBinData2ASCII(UCHAR code);
		UCHAR CvtStdASCII2SixBitASCII(UCHAR code);
		BOOL EncodeBoolean(int nValue, int nStartBitIdx, int nNumOfBits, UCHAR *pDst);
		BOOL EncodeSpare(int nStartBitIdx, int nNumOfBits, UCHAR *pDst);
		BOOL EncodeUintWithScale(int nValue, int nStartBitIdx, int nNumOfBits, UCHAR *pDst, BOOL bScaled, int nScaleVal);
		BOOL EncodeString(UCHAR *pSrc,UCHAR *pDst,int nStartBitIdx, int nMsgSrcLen);
		int CreateAisMsg12(UCHAR *pEnTgt,int nEnTgtLen, UCHAR *pMsgSrc, int nMsgSrcLen, int nRptIndi, int nSrcMMSI, int nSeqNo, int nDstMMSI,int bRetrans, int Spare);
		int CreateAisMsg14(UCHAR *pEnTgt,int nEnTgtLen, UCHAR *pMsgSrc, int nMsgSrcLen, int nRptIndi, int nSrcMMSI, int Spare, int *pBitStuffingCnt);
};

#endif	// End of __AIS_ENCODER_HPP__

