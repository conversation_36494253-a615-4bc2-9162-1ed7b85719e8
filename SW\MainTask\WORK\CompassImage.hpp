/*...........................................................................*/
/*.                  File Name : CompassImage.hpp                           .*/
/*.                                                                         .*/
/*.                       Date : 2008.12.28                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "datatype.h"
//#include "datatype.hpp"
#include "Wnd.hpp"

#ifndef  __COMPASSIMAGE_HPP
#define  __COMPASSIMAGE_HPP

//=============================================================================
#define  COMPASS_IMAGE_WIDTH_32x32                               32
#define  COMPASS_IMAGE_HEIGHT_32x32                              32
#define  COMPASS_IMAGE_WIDTH_48x48                               48
#define  COMPASS_IMAGE_HEIGHT_48x48                              48
//=============================================================================

//===========================================================================
class cCompassImage
{
   private:
      //cObject *m_pCreator;
      int      m_nImageWidth;
      int      m_nImageHeight;
      UCHAR   *m_pImageDataP;

	  CWnd *m_pParent;
	  cSCREEN *m_pScreen;

   public:
      cCompassImage(CWnd *pParent, cSCREEN *pScreen);
      virtual ~cCompassImage(void);
      void  DrawCompassImage(int nPrevCrsUpVal,int nWinScrnLastX,int nWinScrnStartY);
};
//===========================================================================

#endif

