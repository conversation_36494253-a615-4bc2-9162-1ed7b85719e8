/*...........................................................................*/
/*.                  File Name : AU1100.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2003.01.17                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef __AU1100_H__
#define __AU1100_H__

#define CP0_INDEX $0
#define CP0_RANDOM $1
#define CP0_ENTRYLO0 $2
#define CP0_ENTRYLO1 $3
#define CP0_CONF $3
#define CP0_CONTEXT $4
#define CP0_PAGEMASK $5
#define CP0_WIRED $6
#define CP0_INFO $7
#define CP0_BADVADDR $8
#define CP0_COUNT $9
#define CP0_ENTRYHI $10
#define CP0_COMPARE $11
#define CP0_STATUS $12
#define CP0_CAUSE $13
#define CP0_EPC $14
#define CP0_PRID $15
#define CP0_CONFIG $16
#define CP0_LLADDR $17
#define CP0_WATCHLO $18
#define CP0_WATCHHI $19
#define CP0_XCONTEXT $20
#define CP0_FRAMEMASK $21
#define CP0_DIAGNOSTIC $22
#define CP0_DEBUG $23
#define CP0_DEPC $24
#define CP0_PERFORMANCE $25
#define CP0_ECC $26
#define CP0_CACHEERR $27
#define CP0_TAGLO $28
#define CP0_TAGHI $29
#define CP0_ERROREPC $30
#define CP0_DESAVE $31

#define __STR__(X)  #X

#if !defined(_ASSEMBLER_)
static inline BYTE Au1000ReadByte(DWORD Port)
{
      return (*(volatile BYTE *)Port);
}
static inline HWORD Au1000ReadWord(DWORD Port)
{
      return (*(volatile HWORD *)Port);
}
static inline DWORD Au1000ReadLong(DWORD Port)
{
      return (*(volatile DWORD *)Port);
}
void static inline Au1000WriteByte(DWORD Register,BYTE Value)
{
      *(volatile BYTE *)(Register) = Value;
}
void static inline Au1000WriteWord(DWORD Register,HWORD Value)
{
      *(volatile HWORD *)(Register) = Value;
}
void static inline Au1000WriteLong(DWORD Register,DWORD Value)
{
      *(volatile DWORD *)(Register) = Value;
}

#define Read32BitCP0Register(Source)                         \
({ int __res;                                                \
        __asm__ __volatile__(                                \
        ".set\tpush\n\t"                                     \
        ".set\treorder\n\t"                                  \
        "mfc0\t%0,"__STR__(Source)"\n\t"                     \
        "nop\n\t"                                            \
        ".set\tpop"                                          \
        : "=r" (__res));                                     \
        __res;})

#define Write32BitCP0Register(Register,Value)                \
        __asm__ __volatile__(                                \
        "mtc0\t%0,"__STR__(Register)"\n\t"                   \
        "nop\n\t"                                            \
        "nop"                                                \
        : : "r" (Value));

#endif

#define AU1100_SDRAM_CONTROLLER                    0xb4000000
        #define MEM_SDMODE0                            0x0000
        #define MEM_SDMODE1                            0x0004
        #define MEM_SDMODE2                            0x0008
        #define MEM_SDADDR0                            0x000c
        #define MEM_SDADDR1                            0x0010
        #define MEM_SDADDR2                            0x0014
        #define MEM_SDREFCFG                           0x0018
        #define MEM_SDPRECMD                           0x001c
        #define MEM_SDAUTOREF                          0x0020
        #define MEM_SDWRMD0                            0x0024
        #define MEM_SDWRMD1                            0x0028
        #define MEM_SDWRMD2                            0x002c
        #define MEM_SDSLEEP                            0x0030
        #define MEM_SDSMCKE                            0x0034

        #define MEM_STCFG0                             0x1000
        #define MEM_STTIME0                            0x1004
        #define MEM_STADDR0                            0x1008
        #define MEM_STCFG1                             0x1010
        #define MEM_STTIME1                            0x1014
        #define MEM_STADDR1                            0x1018
        #define MEM_STCFG2                             0x1020
        #define MEM_STTIME2                            0x1024
        #define MEM_STADDR2                            0x1028
        #define MEM_STCFG3                             0x1030
        #define MEM_STTIME3                            0x1034
        #define MEM_STADDR3                            0x1038


#define AU1100_GPIO2_BASE                          0xb1700000
        #define GPIO2_DIR                              0x0000
        #define GPIO2_OUTPUT                           0x0008
        #define GPIO2_PINSTATE                         0x000c
        #define GPIO2_INTEN                            0x0010
        #define GPIO2_ENABLE                           0x0014

#define AU1100_CLOCK_CONTROLLER                    0xb1900000
        #define SYS_TOYTRIM                            0x0000
        #define SYS_TOYWRITE                           0x0004
        #define SYS_TOYMATCH0                          0x0008
        #define SYS_TOYMATCH1                          0x000c
        #define SYS_TOYMATCH2                          0x0010
        #define SYS_CNTRCTRL                           0x0014
        #define SYS_TOYREAD                            0x0040
        #define SYS_RTCTRIM                            0x0044
        #define SYS_RTCWRITE                           0x0048
        #define SYS_RTCMATCH0                          0x004c
        #define SYS_RTCMATCH1                          0x0050
        #define SYS_RTCMATCH2                          0x0054
        #define SYS_RTCREAD                            0x0058

        #define SYS_FREQCTRL0                          0x0020
        #define SYS_FREQCTRL1                          0x0024
        #define SYS_CLKSRC                             0x0028
        #define SYS_CPUPLL                             0x0060
        #define SYS_AUXPLL                             0x0064

#define AU1100_POWER_MANAGEMENT                    0xb1900000
        #define SYS_SCRATCH0                           0x0018
        #define SYS_SCRATCH1                           0x001c
        #define SYS_PINFUNC                            0x002c
        #define SYS_WAKEMSK                            0x0034
        #define SYS_ENDIAN                             0x0038
        #define SYS_POWERCTRL                          0x003c
        #define SYS_WAKESRC                            0x005c
        #define SYS_SLPPWR                             0x0078
        #define SYS_SLEEP                              0x007c

        #define SYS_TRIOUTRD                           0x0100
        #define SYS_TRIOUTCLR                          0x0100
        #define SYS_OUTPUTRD                           0x0108
        #define SYS_OUTPUTSET                          0x0108
        #define SYS_OUTPUTCLR                          0x010c
        #define SYS_PINSTATERD                         0x0110
        #define SYS_PININPUTEN                         0x0110

#define AU1100_INT_CONTROLLER0                     0xb0400000
#define AU1100_INT_CONTROLLER1                     0xb1800000
        #define IC_CFG0RD                              0x0040
        #define IC_CFG0SET                             0x0040
        #define IC_CFG0CLR                             0x0044
        #define IC_CFG1RD                              0x0048
        #define IC_CFG1SET                             0x0048
        #define IC_CFG1CLR                             0x004c
        #define IC_CFG2RD                              0x0050
        #define IC_CFG2SET                             0x0050
        #define IC_CFG2CLR                             0x0054
        #define IC_REQ0INT                             0x0054
        #define IC_SRCRD                               0x0058
        #define IC_SRCSET                              0x0058
        #define IC_SRCCLR                              0x005c
        #define IC_REQ1INT                             0x005c
        #define IC_ASSIGNRD                            0x0060
        #define IC_ASSIGNSET                           0x0060
        #define IC_ASSIGNCLR                           0x0064
        #define IC_WAKERD                              0x0068
        #define IC_WAKESET                             0x0068
        #define IC_WAKECLR                             0x006c
        #define IC_MASKRD                              0x0070
        #define IC_MASKSET                             0x0070
        #define IC_MASKCLR                             0x0074
        #define IC_RISINGRD                            0x0078
        #define IC_RISINGCLR                           0x0078
        #define IC_FALLINGRD                           0x007C
        #define IC_FALLINGCLR                          0x007c
        #define IC_TESTBIT                             0x0080

#define AU1100_AC97_CONTROLLER                     0xb0000000
        #define AC97_CONFIG                            0x0000
        #define AC97_STATUS                            0x0004
        #define AC97_DATA                              0x0008
        #define AC97_CMMD                              0x000c
        #define AC97_CMMDRESP                          0x000c
        #define AC97_ENABLE                            0x0010

#define AU1100_USBH_CONTROLLER                     0xb0100000
        #define USBH_ENABLE                           0x7fffc
                #define USBH_ENABLE_RD              (1 <<  4)
                #define USBH_ENABLE_CE              (1 <<  3)
                #define USBH_ENABLE_E               (1 <<  2)
                #define USBH_ENABLE_C               (1 <<  1)
                #define USBH_ENABLE_BE              (1 <<  0)

#define AU1100_IRDA_CONTROLLER                     0xb0300000
        #define IRDA_RNGPTRSTAT                        0x0000 
        #define IRDA_RNGBSADRH                         0x0004 
        #define IRDA_RNGBSADRL                         0x0008 
        #define IRDA_RINGSIZE                          0x000C 
        #define IRDA_RNGPROMPT                         0x0010 
        #define IRDA_RNGADRCMP                         0x0014 
        #define IRDA_INTCLEAR                          0x0018 
        #define IRDA_CONFIG1                           0x0020 
        #define IRDA_SIRFLAGS                          0x0024 
        #define IRDA_STATUSEN                          0x0028 
        #define IRDA_RDPHYCFG                          0x002C 
        #define IRDA_WRPHYCFG                          0x0030 
        #define IRDA_MAXPKTLEN                         0x0034 
        #define IRDA_RXBYTECNT                         0x0038 
        #define IRDA_CONFIG2                           0x003C 
                #define IRDA_CONFIG2_IE             (1 <<  8)
                #define IRDA_CONFIG2_CS_MASK        (3 <<  2)
                #define IRDA_CONFIG2_CS_40MHZ       (0 <<  2)
                #define IRDA_CONFIG2_CS_48MHZ       (1 <<  2)
                #define IRDA_CONFIG2_CS_56MHZ       (2 <<  2)
                #define IRDA_CONFIG2_CS_64MHZ       (3 <<  2)
                #define IRDA_CONFIG2_P              (1 <<  1)
                #define IRDA_CONFIG2_MI             (1 <<  0)
        #define IRDA_ENABLE                            0x0040 

#define AU1100_UART_CONTROLLER0                    0xb1100000
#define AU1100_UART_CONTROLLER1                    0xb1200000
#define AU1100_UART_CONTROLLER2                    0xb1300000
#define AU1100_UART_CONTROLLER3                    0xb1400000
        #define UART_RXDATA                            0x0000
        #define UART_TXDATA                            0x0004
        #define UART_INTEN                             0x0008
        #define UART_INTCAUSE                          0x000c
        #define UART_FIFOCTRL                          0x0010
        #define UART_LINECTRL                          0x0014
        #define UART_MDMCTRL                           0x0018
        #define UART_LINESTAT                          0x001c
        #define UART_MDMSTAT                           0x0020
        #define UART_CLKDIV                            0x0028
        #define UART_ENABLE                            0x0100
        #define UART_INTEN_RIE                      (1 <<  0)
        #define UART_INTEN_TIE                      (1 <<  1)
        #define UART_INTEN_LIE                      (1 <<  2)
        #define UART_INTEN_MIE                      (1 <<  3)
        #define UART_INTCAUSE_IP                    (1 <<  0)
        #define UART_LINESTAT_DR                    (1 <<  0)
        #define UART_LINESTAT_OE                    (1 <<  1)
        #define UART_LINESTAT_PE                    (1 <<  2)
        #define UART_LINESTAT_FE                    (1 <<  3)
        #define UART_LINESTAT_BI                    (1 <<  4)
        #define UART_LINESTAT_TT                    (1 <<  5)
        #define UART_LINESTAT_TE                    (1 <<  6)
        #define UART_LINESTAT_RF                    (1 <<  7)
        #define UART_ENABLE_CE                      (1 <<  0)
        #define UART_ENABLE_E                       (1 <<  1)

#define AU1100_DMA_CONTROLLER0                     0xb4002000
#define AU1100_DMA_CONTROLLER1                     0xb4002100
#define AU1100_DMA_CONTROLLER2                     0xb4002200
#define AU1100_DMA_CONTROLLER3                     0xb4002300
#define AU1100_DMA_CONTROLLER4                     0xb4002400
#define AU1100_DMA_CONTROLLER5                     0xb4002500
#define AU1100_DMA_CONTROLLER6                     0xb4002600
#define AU1100_DMA_CONTROLLER7                     0xb4002700
        #define DMA_MODEREAD                           0x0000
        #define DMA_MODESET                            0x0000
        #define DMA_MODECLR                            0x0004
        #define DMA_PERADDR                            0x0008
        #define DMA_BUF0ADDR                           0x000c
        #define DMA_BUF0SIZE                           0x0010
        #define DMA_BUF1ADDR                           0x0014
        #define DMA_BUF1SIZE                           0x0018

#define AU1100_MAC0_BASE	                         0xB0500000
        #define MAC_CONTROL		 					               0x0000
                #define MAC_CONTROL_RA 		          (1 << 31)
                #define MAC_CONTROL_EM		          (1 << 30)
                									                             // 29-24 Reserved
                #define MAC_CONTROL_DO		          (1 << 23)
                #define MAC_CONTROL_LM 		        (0x3 << 21)  // 00 Loopback operating mode
                									                             // b00 - Normal Mode
                									                             // b01 - Internal Loopback
                									                             // b10 - External Loopback
                									                             // b11 - Reserved
								#define MAC_CONTROL_F  		          (1 << 20)  // 0  Full Duplex Mode (Full-1 Half-0)
								#define MAC_CONTROL_PM    	        (1 << 19)  // 0  Pass All Multicast
								#define MAC_CONTROL_PR 			        (1 << 18)  // 1  Promiscuous Mode (PR-1)
								#define MAC_CONTROL_IF 			        (1 << 17)  // 0  Inverse Filtering
								#define MAC_CONTROL_PB 			        (1 << 16)  // 0  Pass Bad Frames
								#define MAC_CONTROL_HO 			        (1 << 15)  // 0  Hash Only Filtering Mode
                									                             // 14 Reserved
								#define MAC_CONTROL_HP 			        (1 << 13)  // 0  Hash Perfect (Imperfect-1 Perf-0)
                #define MAC_CONTROL_LC	            (1 << 12)  // 0  Deferral Check
                #define MAC_CONTROL_DB	            (1 << 11)  // 0  Disable Broadcast Frames
                #define MAC_CONTROL_DR	            (1 << 10)  // 0  Disable Retry
                									                             // 09 Reserved
                #define MAC_CONTROL_AP	            (1 <<  8)  // 0  Automatic  Pad Stripping
                #define MAC_CONTROL_BL	          (0x3 <<  6)  // 00 Back Off Limit
                #define MAC_CONTROL_DC              (1 <<  5)	 // Deferral Check
                									                             // 04 Reserved
                #define MAC_CONTROL_TE              (1 <<  3)  // 0  Transmitter Enable
                #define MAC_CONTROL_RE              (1 <<  2)  // 0  Receiver Enable
									                                             // 01-00 Reserved

        #define MAC_ADDRHIGH                           0x0004
        #define MAC_ADDRLOW	                           0x0008
        #define MAC_HASHHIGH						               0x000C
        #define MAC_HASHLOW							               0x0010
        #define MAC_MIICTRL							               0x0014
                			    				                             // 31-16 Reserved
                #define MAC_MIICTRL_PHYADDR      (0x1f << 11)  // 00000 5-bit Phy Address
                #define MAC_MIICTRL_MIIREG       (0x1f <<  6)  // 00000 5-bit MII Register
                			    				                             // 05-02 Reserved
                #define	MAC_MIICTRL_MW		          (1 <<  1)  // 0 MII Write  (1-Write 0-Read)
                #define	MAC_MIICTRL_MB  	          (1 <<  0)  // 0 MII Busy

        #define MAC_MIIDATA							               0x0018
			    					                                           // 31-16 Reserved
                #define MAC_MIIDATA_DATA       (0xffff <<  0)  // FFFF 16-bit MII Data
        #define MAC_FLOWCTRL						               0x001C
                #define MAC_FLOWCTRL_PT			   (0xffff << 16)
			    					                                           // 15-03 Reserved
                #define MAC_FLOWCTRL_PC			        (1 <<  2)
                #define MAC_FLOWCTRL_FE			        (1 <<  1)
                #define MAC_FLOWCTRL_FB			        (1 <<  0)
        #define MAC_VLAN1  							               0x0020
			    					                                           // 31-16 Reserved
                #define MAC_VLAN1_VL1TAG       (0xffff <<  0)
        #define MAC_VLAN2	  						               0x0024
			    					                                           // 31-16 Reserved
                #define MAC_VLAN2_VL2TAG       (0xffff <<  0)

#define AU1100_MACEN_BASE  	                       0xB0520000
        #define MACEN_MAC0                             0x0000
			    					                                           // 31-07 Reserved
                #define MACEN_MAC0_JP               (1 <<  6)
                #define MACEN_MAC0_E2               (1 <<  5)
                #define MACEN_MAC0_E1               (1 <<  4)
                #define MACEN_MAC0_C                (1 <<  3)
                #define MACEN_MAC0_TS               (1 <<  2)
                #define MACEN_MAC0_E0               (1 <<  1)
                #define MACEN_MAC0_CE               (1 <<  0)

#define AU1100_MACDMA0_BASE                        0xB4004000
        #define TX0_STAT                               0x0000
        #define TX0_ADDR                               0x0004
        #define TX0_LEN                                0x0008
        #define TX1_STAT                               0x0010
        #define TX1_ADDR                               0x0014
        #define TX1_LEN                                0x0018
        #define TX2_STAT                               0x0020
        #define TX2_ADDR                               0x0024
        #define TX2_LEN                                0x0028
        #define TX3_STAT                               0x0030
        #define TX3_ADDR                               0x0034
        #define TX3_LEN                                0x0038
                #define TXn_STAT_PR	       	        (1 << 31)
                                                               // 30-14 reserved
                #define TXn_STAT_CC	       	      (0xf << 10)
                #define TXn_STAT_LO	       	        (1 <<  9)
                #define TXn_STAT_DF	       	        (1 <<  8)
                #define TXn_STAT_UR	       	        (1 <<  7)
                #define TXn_STAT_EC	       	        (1 <<  6)
                #define TXn_STAT_LC	       	        (1 <<  5)
                #define TXn_STAT_ED	       	        (1 <<  4)
                #define TXn_STAT_LS	       	        (1 <<  3)
                #define TXn_STAT_NC	       	        (1 <<  2)
                #define TXn_STAT_JT	       	        (1 <<  1)
                #define TXn_STAT_FA	       	        (1 <<  0)

                #define TXn_ADDR_ADDR       (0x7ffffff <<  5)
                                                               // 04-04 should be zero
                #define TXn_ADDR_CB               (0x3 <<  2)
                #define TXn_ADDR_DN                 (1 <<  1)
                #define TXn_ADDR_EN                 (1 <<  0)

                                                               // 31-14 should be zero
                #define TXn_LEN_LEN            (0x3fff <<  0)

        #define RX0_STAT                               0x0100
        #define RX0_ADDR                               0x0104
        #define RX1_STAT                               0x0110
        #define RX1_ADDR                               0x0114
        #define RX2_STAT                               0x0120
        #define RX2_ADDR                               0x0124
        #define RX3_STAT                               0x0130
        #define RX3_ADDR                               0x0134
                #define RXn_STAT_MI	       	        (1 << 31)  // Missed Frame
                #define RXn_STAT_PF	       	        (1 << 30)  // Packet Filter
                #define RXn_STAT_FF	       	        (1 << 29)  // Filtering Fail
                #define RXn_STAT_BF	       	        (1 << 28)  // Broadcast Frame
                #define RXn_STAT_MF	       	        (1 << 27)  // MultiCast Frame
                #define RXn_STAT_UC 	     	        (1 << 26)  // Unsupprted Control Frame
                #define RXn_STAT_CF 	     	        (1 << 25)  // Control Frame
                #define RXn_STAT_LE 	     	        (1 << 24)  // Length Error
                #define RXn_STAT_V2	       	        (1 << 23)  // VLAN 2 Frame Check
                #define RXn_STAT_V1	       	        (1 << 22)  // VLAN 1 Frame Check
                #define RXn_STAT_CR        	        (1 << 21)  // CRC Error
                #define RXn_STAT_DB 	     	        (1 << 20)  // Dribble Bit
                #define RXn_STAT_ME	      	        (1 << 19)  // MII Error (MII_Rxer)
                #define RXn_STAT_FT	       	        (1 << 18)  // Frame Type (1-Ethernet 0-802.3)
                #define RXn_STAT_CS	       	        (1 << 17)  // Collision Seen
                #define RXn_STAT_FL	       	        (1 << 16)  // Frame Too Long
                #define RXn_STAT_RF	       	        (1 << 15)  // Runt Frame
                #define RXn_STAT_WT	       	        (1 << 14)  // Watch Dog Timeout
                #define RXn_STAT_L		         (0x3fff <<  0)  // 14 bit Frame Length

                #define RXn_ADDR_ADDR       (0x7ffffff <<  5)
                                                               // 04-04 should be zero
                #define RXn_ADDR_CB               (0x3 <<  2)
                #define RXn_ADDR_DN                 (1 <<  1)
                #define RXn_ADDR_EN                 (1 <<  0)

#define AU1100_SD0_BASE                            0xB0600000
#define AU1100_SD1_BASE                            0xB0680000
        #define SD_TXPORT                              0x0000
        #define SD_RXPORT                              0x0004
        #define SD_CONFIG                              0x0008
                #define SD_CONFIG_SI                (1 << 31)
                #define SD_CONFIG_CD                (1 << 30)
                #define SD_CONFIG_RF                (1 << 29)
                #define SD_CONFIG_RA                (1 << 28)
                #define SD_CONFIG_RH                (1 << 27)
                #define SD_CONFIG_TA                (1 << 26)
                #define SD_CONFIG_TE                (1 << 25)
                #define SD_CONFIG_TH                (1 << 24)
                #define SD_CONFIG_WC                (1 << 22)
                #define SD_CONFIG_RC                (1 << 21)
                #define SD_CONFIG_SC                (1 << 20)
                #define SD_CONFIG_DT                (1 << 19)
                #define SD_CONFIG_DD                (1 << 18)
                #define SD_CONFIG_RAT               (1 << 17)
                #define SD_CONFIG_CR                (1 << 16)
                #define SD_CONFIG_I                 (1 << 15)
                #define SD_CONFIG_RO                (1 << 14)
                #define SD_CONFIG_RU                (1 << 13)
                #define SD_CONFIG_TO                (1 << 12)
                #define SD_CONFIG_TU                (1 << 11)
                #define SD_CONFIG_NE                (1 << 10)
                #define SD_CONFIG_DE                (1 <<  9)
                #define SD_CONFIG_DIV_MASK       (0x1ff << 0)
        #define SD_ENABLE                              0x000c
                #define SD_ENABLE_R                 (1 <<  1)
                #define SD_ENABLE_CE                (1 <<  0)
        #define SD_CONFIG2                             0x0010
                #define SD_CONFIG2_RW               (1 <<  9)
                #define SD_CONFIG2_WB               (1 <<  8)
                #define SD_CONFIG2_DC               (1 <<  4)
                #define SD_CONFIG2_DF               (1 <<  3)
                #define SD_CONFIG2_FF               (1 <<  1)
                #define SD_CONFIG2_EN               (1 <<  0)
        #define SD_BLKSIZE                             0x0014
                #define SD_BLKSIZE_BC_SHIFT               16
                #define SD_BLKSIZE_BC_MASK      (0x1ff << SD_BLKSIZE_BC_SHIFT)
                #define SD_BLKSIZE_BS_MASK      (0x7ff <<  0)
        #define SD_STATUS                              0x0018
                #define SD_STATUS_SI                (1 << 31)
                #define SD_STATUS_CD                (1 << 30)
                #define SD_STATUS_RF                (1 << 29)
                #define SD_STATUS_RA                (1 << 28)
                #define SD_STATUS_RH                (1 << 27)
                #define SD_STATUS_TA                (1 << 26)
                #define SD_STATUS_TE                (1 << 25)
                #define SD_STATUS_TH                (1 << 24)
                #define SD_STATUS_WC                (1 << 22)
                #define SD_STATUS_RC                (1 << 21)
                #define SD_STATUS_SC                (1 << 20)
                #define SD_STATUS_DT                (1 << 19)
                #define SD_STATUS_DD                (1 << 18)
                #define SD_STATUS_RAT               (1 << 17)
                #define SD_STATUS_CR                (1 << 16)
                #define SD_STATUS_I                 (1 << 15)
                #define SD_STATUS_RO                (1 << 14)
                #define SD_STATUS_RU                (1 << 13)
                #define SD_STATUS_TO                (1 << 12)
                #define SD_STATUS_TU                (1 << 11)
                #define SD_STATUS_NE                (1 << 10)
                #define SD_STATUS_D3                (1 <<  7)
                #define SD_STATUS_CF                (1 <<  6)
                #define SD_STATUS_DB                (1 <<  5)
                #define SD_STATUS_CB                (1 <<  4)
                #define SD_STATUS_DCRCW_MASK     (0x07 <<  0)
                        #define SD_STATUS_DCRCW_NO_ERROR   2
                        #define SD_STATUS_DCRCW_TX_ERROR   5
                        #define SD_STATUS_DCRCW_NO_CRC_RES 7
        #define SD_DEBUG                               0x001c
        #define SD_CMD                                 0x0020
                #define SD_CMD_RT_SHIFT                   16
                #define SD_CMD_RT_MASK           (0xff << SD_CMD_RT_SHIFT)
                #define SD_CMD_CI_SHIFT                    8
                #define SD_CMD_CI_MASK           (0xff << SD_CMD_CI_SHIFT)
                #define SD_CMD_CT_SHIFT                    4
                #define SD_CMD_CT_MASK           (0x0f << SD_CMD_CT_SHIFT)
                        #define SD_CMD_CT_0000          0x00
                        #define SD_CMD_CT_0001          0x01
                        #define SD_CMD_CT_0010          0x02
                        #define SD_CMD_CT_0011          0x03
                        #define SD_CMD_CT_0100          0x04
                        #define SD_CMD_CT_0111          0x07
                #define SD_CMD_RY                   (1 <<  1)
                #define SD_CMD_GO                   (1 <<  0)
        #define SD_CMDARG                              0x0024
        #define SD_RESP3                               0x0028
        #define SD_RESP2                               0x002c
        #define SD_RESP1                               0x0030
        #define SD_RESP0                               0x0034
        #define SD_TIMEOUT                             0x0038
                #define SD_TIMEOUT_TMAX_MASK  (0x1fffff <<  0)

#define AU1100_KSEG_MASK	    	                   0xE0000000
#define AU1100_KSEG0_BASE		                       0x80000000
#define AU1100_KSEG1_BASE		                       0xA0000000
#define AU1100_KSEG2_BASE		                       0xC0000000
#define AU1100_KSEG3_BASE		                       0xE0000000

#define AU1100_CACHE_SIZE                              0x4000
#define AU1100_LINE_SIZE                                 0x20
#define AU1100_ICACHE_INVALID                            0x00
#define AU1100_DCACHE_INVALID                            0x01

#define DMA_DAH_MASK                             (0x0f << 20)
#define DMA_DID_BIT                                        16
#define DMA_DID_MASK                    (0x0f << DMA_DID_BIT)
#define DMA_BE                                        (1<<13)
#define DMA_DR                                        (1<<12)
#define DMA_TS8                                       (1<<11)
#define DMA_DW_BIT                                          9
#define DMA_DW_MASK                      (0x03 << DMA_DW_BIT)
#define DMA_DW8                             (0 << DMA_DW_BIT)
#define DMA_DW16                            (1 << DMA_DW_BIT)
#define DMA_DW32                            (2 << DMA_DW_BIT)
#define DMA_NC                                         (1<<8)
#define DMA_IE                                         (1<<7)
#define DMA_HALT                                       (1<<6)
#define DMA_GO                                         (1<<5)
#define DMA_AB                                         (1<<4)
#define DMA_D1                                         (1<<3)
#define DMA_BE1                                        (1<<2)
#define DMA_D0                                         (1<<1)
#define DMA_BE0                                        (1<<0)
#define DMA_COUNT_MASK                                 0xffff
#define DMA_HALT_POLL                                  0x5000

#endif

