/*=============================================================================
File         : TIDESPRD.H
Info         : Tidal prediction and sunrise/sunset info, header file
Created by   : MI[21Jul97]
Modified by  : MI[24Nov97]
Notes        : This is the merge of ASTRO.H, TIDES.H
=============================================================================*/

#ifndef __TIDESPRD__
#define __TIDESPRD__

#include "cm/dbase.h"

#ifdef DEBUG_INCLUDES
	#pragma message( "+++++++++ including tidesprd.h +++++++++" )
#endif

#define NEW_CODE

typedef struct 
{ 
	SWord Year;
	Byte Month,Day;
} YMD_t;

typedef struct 
{ 
	SWord Hour;
	SWord Min;
} hhmm_t;

typedef struct 
{ 
	hhmm_t TwiLightStart;
	hhmm_t SunRise;
	hhmm_t SunSet;
	hhmm_t TwiLightEnd;
} AstroVal_t;

typedef struct 
{
	Float direction;
	Float speed;
} sStreamResult;

#define MaxStreamValues 289 
#define MaxStreamEvents 50
#define MAX_HIGH_LOW	6

typedef enum neStreamEvent
{
  eNoStreamEvent=0,
  eEbb,       
  eEbbFloodTransition,  
  eFlood,      
  eFloodEbbTransition            
} eStreamEvent;

typedef struct nsStreamEventData_t
{
	Float CurrentSpeed[MaxStreamValues];
	Float CurrentDirection[MaxStreamValues];
	Float EventTimes[MaxStreamEvents];
	Float StartTime;
	Float EndTime;
	Float MainEbbDirection;
	Float MainFloodDirection;
	Float DeltaT;
	SLong NoStreamValues;
	SByte StreamType;
	eStreamEvent Events[MaxStreamEvents];
	SLong NoEvents;
#ifdef NEW_CODE
	Bool bHasEbbFloodDirection;
#endif
} sStreamEventData_t;

typedef struct nsShiftData
{
	Float m_MaxTimeAdjust;
	Float m_MinTimeAdjust;
	Float m_MBFTimeAdjust;
	Float m_FloodTimeAdjust;
	Float m_MBETimeAdjust;
	Float m_EbbTimeAdjust;
	Float m_FloodSpeedRatio;
	Float m_EbbSpeedRatio;
	Float m_FloodDir;
	Float m_EbbDir;
}sShiftData;

typedef enum neTYPE_OF_ELEMENT
{
	eNON_HARMONIC_ELEMENT=0,
	eHARMONIC_ELEMENT
}eTYPE_OF_ELEMENT;

typedef struct nsNonHarmonicKey
{
	Byte cdgNum;
	Long DBPtr;
	Word PredDate;
}sNonHarmonicKey;
typedef struct nsHarmonicKey
{
	Byte cdgNum;
	Long DBPtr;
}sHarmonicKey;

typedef struct nsNonHarmonicData
{
	Byte NoHWLW;	/*bit 0-3 NoHW*/
								/*bit 4-7 NoLW*/
	Float HW[MAX_HIGH_LOW];
	Float LW[MAX_HIGH_LOW];
}sNonHarmonicData;
#define sHarmonicData sStreamEventData_t 

typedef struct nsNonHarmonicElement
{
	sNonHarmonicKey key;
	sNonHarmonicData info;
}sNonHarmonicElement;
typedef struct nsHarmonicElement
{
	sHarmonicKey key;
	sHarmonicData info;
}sHarmonicElement;


#define FLOAT_MAX         3.402823466e+38F        /* max value */
#define FLOAT_MIN         1.175494351e-38F        /* min positive value */


typedef enum neTIDAL_STREAM_STATION_TYPE
{
	eNON_HARMONIC=1,
	eHARMONIC_PRIMARY_PUMPING,
	eHARMONIC_PRIMARY_ROTARY,
	eHARMONIC_SECONDARY
}eTIDAL_STREAM_STATION_TYPE;
typedef enum neDATE_TIME_VALIDITY_CHECK
{
	eDATE_TIME_CHECK=0,
	eDATE_TIME_NOT_CHECK
}eDATE_TIME_VALIDITY_CHECK;



#ifdef __cplusplus
extern "C" {
#endif
PRE_EXPORT_H void  IN_EXPORT_H cmGetAstro( Word CdgNum, Long DBPtr, Double Lat, Double Lon, YMD_t *Date, AstroVal_t *A );
PRE_EXPORT_H SWord IN_EXPORT_H cmStartPrediction( Word CdgNum, Long DBPtr, YMD_t *Date, Double Dt );
PRE_EXPORT_H Float IN_EXPORT_H cmGetNextPredict( void );
PRE_EXPORT_H Float IN_EXPORT_H cmGetPrevPredict( void );
PRE_EXPORT_H Float IN_EXPORT_H cmQuickTidePredict( Word CdgNum, Long DBPtr, YMD_t *Date, Double Dt, Double Hr );
PRE_EXPORT_H void  IN_EXPORT_H cmGetSunTimes( Double Lat, Double Lon, YMD_t *Date, Double tz, AstroVal_t *A);
PRE_EXPORT_H void IN_EXPORT_H cmSetStreamDateAndTime(Bool mode, YMD_t *date, hhmm_t *time);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetStreamPrediction(sObjInfo* ObjInfo, YMD_t* date, hhmm_t* time, sStreamResult* result);

#ifdef __cplusplus
}
#endif

#endif
