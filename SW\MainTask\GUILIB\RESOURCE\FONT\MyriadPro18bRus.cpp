/*...........................................................................*/
/*.                  File Name : MyriadPro18bRus.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma18bTai_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro18bRus_offset_table[250] = {
0x0000,0x000d,0x001e,0x002a,0x0038,0x0045,0x004c,0x0054,0x005e,0x0075,0x008c,0x009e,0x00ad,0x00b2,0x00c0,0x00d0,
0x00e0,0x00ee,0x00fc,0x0108,0x0119,0x0126,0x013c,0x0149,0x015a,0x016b,0x017a,0x0189,0x019d,0x01ae,0x01bf,0x01cf,
0x01dd,0x01eb,0x01f8,0x0206,0x0219,0x0228,0x0239,0x0248,0x025e,0x0274,0x0285,0x0299,0x02a7,0x02b5,0x02cb,0x02d9,
0x02e6,0x02f4,0x0301,0x030b,0x0319,0x0326,0x0338,0x0344,0x0352,0x0360,0x036d,0x037a,0x038b,0x0399,0x03a7,0x03b5,
0x03c3,0x03ce,0x03d9,0x03e6,0x03f7,0x0403,0x0411,0x041e,0x0432,0x0446,0x0456,0x0469,0x0476,0x0482,0x0495,0x04a2,
0x04a7,0x04b4,0x04c2,0x04cc,0x04d7,0x04e1,0x04e8,0x04f1,0x04f9,0x050d,0x0521,0x052f,0x053c,0x0541,0x054e,0x055c,
0x0561,0x0566,0x056b,0x0570,0x0575,0x057a,0x057f,0x0584,0x0589,0x058e,0x0593,0x0598,0x059d,0x05a2,0x05a7,0x05ac,
0x05b1,0x05b6,0x05bb,0x05c0,0x05c5,0x05ca,0x05cf,0x05d4,0x05d9,0x05de,0x05e3,0x05e8,0x05ed,0x05f2,0x05f7,0x05fc,
0x0601,0x0606,0x060b,0x0610,0x0615,0x061a,0x061f,0x0624,0x0629,0x062e,0x0633,0x0638,0x063d,0x0642,0x0647,0x064c,
0x0658,0x0662,0x0667,0x066c,0x0671,0x0676,0x067b,0x0680,0x0685,0x068a,0x068f,0x0694,0x0699,0x069e,0x06a3,0x06a8,
0x06ad,0x06b2,0x06b7,0x06bc,0x06c1,0x06c6,0x06cb,0x06d0,0x06d5,0x06da,0x06df,0x06e4,0x06e9,0x06ee,0x06f3,0x06f8,
0x06fd,0x0702,0x0707,0x070c,0x0711,0x0716,0x071b,0x0720,0x0725,0x072a,0x072f,0x0734,0x0739,0x073e,0x0743,0x0748,
0x074d,0x0752,0x0757,0x075c,0x0761,0x0766,0x076b,0x0770,0x0775,0x077a,0x077f,0x0784,0x0789,0x078e,0x0793,0x0798,
0x079d,0x07a2,0x07a7,0x07ac,0x07b1,0x07b6,0x07bb,0x07c0,0x07c5,0x07d1,0x07d6,0x07db,0x07e0,0x07e5,0x07ea,0x07ef,
0x07f4,0x07f9,0x07fe,0x0803,0x0808,0x080d,0x0812,0x0817,0x081c,0x0821,0x0826,0x082b,0x0830,0x0835,0x083a,0x083f,
0x0844,0x0849,0x084e,0x0853,0x0858,0x085d,0x0862,0x0867,0x086c,0x0871,
};


ROMDATA PEGUBYTE MyriadPro18bRus_data_table[6775] = {
0x10, 0x40, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x04, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x03, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x8c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x38, 0xe0, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xfc, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x10, 0x40, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x83, 0xc0, 0x01, 0xe0, 0x00, 0x00, 0x18, 0x63, 0x06, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe7, 0xff, 0xe1, 0xff, 0x80, 0x3f, 0x07, 0xe3, 0xc3, 0xc0, 0x78, 0x7f, 0xf0, 0x03, 0xc1, 
0xe0, 0x0f, 0xff, 0x81, 0xe1, 0xf0, 0x1e, 0x0f, 0x78, 0x1e, 0x07, 0xc0, 0x7f, 0xf9, 0xfe, 0x07, 
0xfe, 0x0f, 0xfe, 0x3f, 0xf1, 0xe1, 0xe1, 0xe1, 0xf8, 0x3c, 0x0f, 0x1e, 0x07, 0x8f, 0x0f, 0x87, 
0xff, 0x1f, 0x03, 0xe3, 0xc0, 0xf0, 0x07, 0x00, 0xff, 0xfc, 0xff, 0x00, 0x07, 0xef, 0xff, 0xf0, 
0x3c, 0x07, 0x80, 0x7c, 0x3e, 0x78, 0x1e, 0x3c, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xef, 0xf0, 
0x03, 0xc0, 0x0f, 0x3c, 0x00, 0x7c, 0x03, 0xc0, 0x78, 0x00, 0xff, 0x00, 0x00, 0x1f, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x30, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0xcf, 0xfc, 0x01, 0xc0, 0x00, 0x00, 0x3c, 0xf7, 0x8f, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x7f, 0xe0, 0x07, 0x00, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe7, 0xff, 0xe1, 0xff, 0x81, 0xfe, 0x1f, 0xe3, 0xc3, 0xc0, 0x78, 0x7f, 0xf0, 0x03, 0xc1, 
0xe0, 0x0f, 0xff, 0x81, 0xe1, 0xe0, 0x1e, 0x1e, 0x78, 0x1e, 0x07, 0xc0, 0x7f, 0xf9, 0xff, 0x87, 
0xfe, 0x0f, 0xfe, 0x3f, 0xf0, 0xf1, 0xe3, 0xc7, 0xfe, 0x3c, 0x1f, 0x1e, 0x0f, 0x8f, 0x0f, 0x07, 
0xff, 0x1f, 0x03, 0xe3, 0xc0, 0xf0, 0x3f, 0xe0, 0xff, 0xfc, 0xff, 0xc0, 0x3f, 0xef, 0xff, 0x78, 
0x38, 0x1f, 0xf0, 0x3c, 0x3c, 0x78, 0x1e, 0x3c, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xef, 0xf0, 
0x03, 0xc0, 0x0f, 0x3c, 0x00, 0xff, 0x83, 0xc1, 0xfe, 0x03, 0xff, 0x00, 0x00, 0x3f, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x8f, 0xfc, 0x03, 0x80, 0x00, 0x00, 0x3c, 0x63, 0x0f, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x7f, 0xe0, 0x0e, 0x00, 0x0f, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xfe, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe7, 0xff, 0xe1, 0xff, 0x87, 0xfe, 0x3f, 0xe3, 0xc3, 0xc0, 0x78, 0x7f, 0xf0, 0x03, 0xc1, 
0xe0, 0x0f, 0xff, 0x81, 0xe3, 0xc0, 0x0e, 0x1e, 0x78, 0x1e, 0x0f, 0xe0, 0x7f, 0xf9, 0xff, 0xc7, 
0xfe, 0x0f, 0xfe, 0x3f, 0xf0, 0x71, 0xe3, 0x87, 0xff, 0x3c, 0x3f, 0x1e, 0x1f, 0x8f, 0x1e, 0x07, 
0xff, 0x1f, 0x87, 0xe3, 0xc0, 0xf0, 0x7f, 0xf0, 0xff, 0xfc, 0xff, 0xe0, 0xff, 0xcf, 0xff, 0x78, 
0x78, 0x7f, 0xfc, 0x1e, 0x78, 0x78, 0x1e, 0x3c, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xef, 0xf0, 
0x03, 0xc0, 0x0f, 0x3c, 0x00, 0xff, 0xc3, 0xc3, 0xff, 0x07, 0xff, 0x00, 0x00, 0x78, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xfc, 0x07, 0x00, 0x00, 0x00, 0x18, 0x00, 0x06, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x7f, 0xe0, 0x1c, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xfe, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x00, 0x78, 0x01, 0xe0, 0x07, 0x80, 0x78, 0x23, 0xc3, 0xc0, 0x78, 0x78, 0xf0, 0x03, 0xc1, 
0xe0, 0x00, 0xf0, 0x01, 0xe7, 0x80, 0x0f, 0x1c, 0x78, 0x1e, 0x0e, 0xe0, 0x78, 0x01, 0xe1, 0xe7, 
0x80, 0x0f, 0x1e, 0x3c, 0x00, 0x79, 0xe7, 0x84, 0x0f, 0x3c, 0x3f, 0x1e, 0x1f, 0x8f, 0x3c, 0x07, 
0x8f, 0x1f, 0x87, 0xe3, 0xc0, 0xf0, 0x78, 0xf8, 0xf0, 0x3c, 0xf1, 0xf0, 0xf0, 0x00, 0xf0, 0x3c, 
0x78, 0x77, 0xbc, 0x1e, 0x78, 0x78, 0x1e, 0x3c, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xf0, 
0x03, 0xc0, 0x0f, 0x3c, 0x00, 0x03, 0xe3, 0xc7, 0x87, 0x8f, 0x8f, 0x00, 0x00, 0xe0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x00, 0x78, 0x01, 0xe0, 0x0f, 0x00, 0x78, 0x03, 0xc3, 0xc0, 0x78, 0x78, 0xf0, 0x03, 0xc1, 
0xe0, 0x00, 0xf0, 0x01, 0xef, 0x00, 0x07, 0x1c, 0x78, 0x1e, 0x0e, 0xe0, 0x78, 0x01, 0xe1, 0xe7, 
0x80, 0x0f, 0x1e, 0x3c, 0x00, 0x3d, 0xef, 0x00, 0x0f, 0x3c, 0x7f, 0x1e, 0x3f, 0x8f, 0x78, 0x07, 
0x8f, 0x1f, 0x86, 0xe3, 0xc0, 0xf0, 0xf0, 0x78, 0xf0, 0x3c, 0xf0, 0xf1, 0xe0, 0x00, 0xf0, 0x3c, 
0x70, 0xf7, 0x9e, 0x0f, 0x70, 0x78, 0x1e, 0x3c, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xf0, 
0x03, 0xc0, 0x0f, 0x3c, 0x00, 0x01, 0xe3, 0xc7, 0x87, 0x8f, 0x0f, 0x0f, 0x80, 0xe6, 0x07, 0xfc, 
0x3f, 0xc3, 0xff, 0x03, 0x83, 0xc7, 0x8f, 0x0f, 0x07, 0x8f, 0x9e, 0x3e, 0x78, 0xf1, 0xff, 0x8f, 
0x87, 0x8f, 0x0f, 0x03, 0xc0, 0xff, 0xf3, 0x8f, 0x00, 0xfd, 0xff, 0xf8, 0x78, 0x1f, 0xe1, 0xe1, 
0xef, 0x0f, 0x3c, 0x79, 0xe3, 0xcf, 0x1e, 0x3c, 0xf1, 0xfc, 0x01, 0xe0, 0x1e, 0x3c, 0x00, 0x78, 
0x1e, 0x0f, 0x00, 0x7f, 0x80, 0x0e, 0x03, 0xdc, 0x1f, 0xe0, 0x3c, 0x1e, 0x3c, 0x3c, 0x0f, 0x1f, 
0xf8, 0x03, 0xc7, 0x80, 0x1e, 0xf0, 0xf1, 0xe0, 0x78, 0x3d, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x7f, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x00, 0x7b, 0xc1, 0xe0, 0x0e, 0x00, 0x7c, 0x03, 0xc3, 0xc0, 0x78, 0x78, 0xff, 0x03, 0xc1, 
0xfe, 0x00, 0xf7, 0x81, 0xee, 0x00, 0x07, 0x38, 0x78, 0x1e, 0x1e, 0xf0, 0x7f, 0xc1, 0xe1, 0xc7, 
0x80, 0x0f, 0x1e, 0x3c, 0x00, 0x1d, 0xee, 0x00, 0x1e, 0x3c, 0x7f, 0x1e, 0x3f, 0x8f, 0x70, 0x07, 
0x8f, 0x1d, 0xce, 0xe3, 0xc0, 0xf0, 0xe0, 0x3c, 0xf0, 0x3c, 0xf0, 0xf1, 0xc0, 0x00, 0xf0, 0x1c, 
0x70, 0xe7, 0x8e, 0x0f, 0xf0, 0x78, 0x1e, 0x3c, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xff, 
0x03, 0xfe, 0x0f, 0x3f, 0xe0, 0x00, 0xf3, 0xcf, 0x03, 0xcf, 0x0f, 0x3f, 0xc0, 0xdf, 0x87, 0xfe, 
0x3f, 0xc3, 0xff, 0x0f, 0xe1, 0xe7, 0x9e, 0x7f, 0xc7, 0x8f, 0x9e, 0x3e, 0x79, 0xe1, 0xff, 0x8f, 
0x8f, 0x8f, 0x0f, 0x0f, 0xf0, 0xff, 0xf3, 0xbf, 0x81, 0xf9, 0xff, 0xb8, 0x70, 0x7f, 0xf8, 0xf3, 
0xcf, 0x0f, 0x3c, 0x79, 0xe3, 0xcf, 0x1e, 0x3c, 0xf1, 0xfc, 0x01, 0xe0, 0x1e, 0x3c, 0x01, 0xfe, 
0x1e, 0x3f, 0xc1, 0xff, 0x80, 0x3f, 0x83, 0xfe, 0x1f, 0xe0, 0xfe, 0x7f, 0x3c, 0x3c, 0x0f, 0x1f, 
0xf8, 0x03, 0xc7, 0x80, 0x1f, 0xf8, 0xf3, 0xc0, 0x3c, 0x39, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x7f, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe0, 0x7f, 0xf1, 0xe0, 0x1f, 0xfc, 0x3f, 0x03, 0xc3, 0xc0, 0x78, 0x78, 0xff, 0xc3, 0xff, 
0xff, 0x80, 0xff, 0xe1, 0xfe, 0x00, 0x03, 0xb8, 0x78, 0x1e, 0x1c, 0xf0, 0x7f, 0xf1, 0xff, 0x87, 
0x80, 0x0f, 0x1e, 0x3f, 0xf0, 0x0f, 0xfc, 0x00, 0xfc, 0x3c, 0xef, 0x1e, 0x77, 0x8f, 0xf0, 0x07, 
0x8f, 0x1d, 0xce, 0xe3, 0xff, 0xf1, 0xe0, 0x3c, 0xf0, 0x3c, 0xf1, 0xe3, 0xc0, 0x00, 0xf0, 0x1e, 
0xe1, 0xe7, 0x8f, 0x07, 0xe0, 0x78, 0x1e, 0x3e, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xff, 
0xc3, 0xff, 0x8f, 0x3f, 0xf8, 0x7f, 0xf3, 0xff, 0x03, 0xc7, 0x8f, 0x3f, 0xe1, 0xff, 0xc7, 0x8f, 
0x3f, 0xc3, 0xff, 0x1f, 0xf0, 0xe7, 0x9c, 0x3f, 0xe7, 0x9f, 0x9e, 0x7e, 0x79, 0xe1, 0xff, 0x8f, 
0x8f, 0x8f, 0x0f, 0x1f, 0xf8, 0xff, 0xf3, 0xff, 0xc7, 0xf9, 0xff, 0xbc, 0x70, 0xff, 0xfc, 0x73, 
0x8f, 0x0f, 0x3c, 0x79, 0xe3, 0xcf, 0x1e, 0x3c, 0xf1, 0xfc, 0x01, 0xe0, 0x1e, 0x3c, 0x00, 0xff, 
0x1e, 0x7f, 0xe3, 0xc7, 0x80, 0x7f, 0xc3, 0xff, 0x1f, 0xe3, 0xfe, 0x7f, 0x3c, 0x3c, 0x0f, 0x1f, 
0xf8, 0x03, 0xc7, 0x80, 0x1f, 0xf8, 0xf3, 0xc0, 0x3c, 0x79, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x7f, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe0, 0x7f, 0xf9, 0xe0, 0x1f, 0xfc, 0x3f, 0xc3, 0xc3, 0xc0, 0x78, 0x78, 0xff, 0xe3, 0xff, 
0xff, 0xc0, 0xff, 0xe1, 0xff, 0x00, 0x03, 0xb8, 0x78, 0x1e, 0x1c, 0x70, 0x7f, 0xf9, 0xff, 0x87, 
0x80, 0x0f, 0x1e, 0x3f, 0xf0, 0x3f, 0xff, 0x00, 0xf0, 0x3c, 0xef, 0x1e, 0x77, 0x8f, 0xf8, 0x07, 
0x8f, 0x1d, 0xce, 0xe3, 0xff, 0xf1, 0xe0, 0x3c, 0xf0, 0x3c, 0xff, 0xe3, 0xc0, 0x00, 0xf0, 0x0e, 
0xe1, 0xe7, 0x8f, 0x07, 0xc0, 0x78, 0x1e, 0x1f, 0xfe, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xff, 
0xe3, 0xff, 0xcf, 0x3f, 0xfc, 0x7f, 0xf3, 0xff, 0x03, 0xc3, 0xff, 0x00, 0xf1, 0xf3, 0xc7, 0x8f, 
0x3c, 0x03, 0x8f, 0x1c, 0x70, 0x77, 0xb8, 0x21, 0xe7, 0x9f, 0x9e, 0x7e, 0x7b, 0xc1, 0xe7, 0x8f, 
0x8f, 0x8f, 0x0f, 0x1e, 0x78, 0xf0, 0xf3, 0xe3, 0xc7, 0x80, 0x3c, 0x3c, 0xf0, 0xf7, 0xbc, 0x7f, 
0x8f, 0x0f, 0x3c, 0x79, 0xe3, 0xcf, 0x1e, 0x3c, 0xf0, 0x3f, 0xc1, 0xfe, 0x1e, 0x3f, 0xc0, 0x0f, 
0x1e, 0x78, 0xe3, 0xc7, 0x80, 0x71, 0xc3, 0xcf, 0x1e, 0x03, 0xc0, 0xf0, 0x3c, 0x3c, 0x0f, 0x1e, 
0x7f, 0x83, 0xc7, 0xf8, 0x1e, 0x3c, 0xf7, 0x80, 0x1c, 0x71, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe0, 0x78, 0x79, 0xe0, 0x1f, 0xfc, 0x0f, 0xe3, 0xc3, 0xc0, 0x78, 0x78, 0xf1, 0xe3, 0xff, 
0xe3, 0xc0, 0xf0, 0xf1, 0xff, 0x80, 0x03, 0xb0, 0x78, 0x1e, 0x3c, 0x78, 0x78, 0x7d, 0xff, 0xe7, 
0x80, 0x0e, 0x1e, 0x3f, 0xf0, 0x7f, 0xff, 0x80, 0xfe, 0x3d, 0xef, 0x1e, 0xf7, 0x8f, 0xfc, 0x07, 
0x8f, 0x1d, 0xcc, 0xf3, 0xff, 0xf1, 0xe0, 0x3c, 0xf0, 0x3c, 0xff, 0xc3, 0xc0, 0x00, 0xf0, 0x0e, 
0xe1, 0xe7, 0x8f, 0x07, 0xe0, 0x78, 0x1e, 0x1f, 0xfe, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xf1, 
0xf3, 0xc3, 0xcf, 0x3c, 0x3e, 0x7f, 0xf3, 0xff, 0x03, 0xc0, 0xff, 0x07, 0xf1, 0xe1, 0xe7, 0x8e, 
0x3c, 0x03, 0x8f, 0x1c, 0x78, 0x77, 0xb8, 0x01, 0xe7, 0xbf, 0x9e, 0xfe, 0x7b, 0x81, 0xe7, 0x8f, 
0xdf, 0x8f, 0xff, 0x3c, 0x3c, 0xf0, 0xf3, 0xc1, 0xef, 0x00, 0x3c, 0x1c, 0xe1, 0xe7, 0x9e, 0x3f, 
0x0f, 0x0f, 0x3c, 0x79, 0xe3, 0xcf, 0x1e, 0x3c, 0xf0, 0x3f, 0xe1, 0xff, 0x9e, 0x3f, 0xf0, 0x07, 
0x9f, 0xf0, 0xf3, 0xc7, 0x80, 0x71, 0xe3, 0xc7, 0x9e, 0x07, 0x80, 0xf8, 0x3c, 0x3c, 0x0f, 0x1e, 
0x7f, 0xc3, 0xff, 0xfe, 0x1e, 0x3c, 0xf7, 0x00, 0x1e, 0x71, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x00, 0x78, 0x3d, 0xe0, 0x1e, 0x00, 0x03, 0xe3, 0xc3, 0xc0, 0x78, 0x78, 0xf0, 0xf3, 0xc1, 
0xe1, 0xe0, 0xf0, 0xf1, 0xe7, 0xc0, 0x01, 0xf0, 0x78, 0x1e, 0x3f, 0xf8, 0x78, 0x3d, 0xe1, 0xf7, 
0x80, 0x0e, 0x1e, 0x3c, 0x00, 0x79, 0xe7, 0x80, 0x1e, 0x3d, 0xcf, 0x1e, 0xe7, 0x8f, 0x3e, 0x07, 
0x8f, 0x3c, 0xfc, 0xf3, 0xc0, 0xf1, 0xe0, 0x3c, 0xf0, 0x3c, 0xff, 0x03, 0xc0, 0x00, 0xf0, 0x07, 
0xc1, 0xe7, 0x8f, 0x07, 0xe0, 0x78, 0x1e, 0x07, 0xde, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xf0, 
0xf3, 0xc1, 0xef, 0x3c, 0x1e, 0x00, 0xf3, 0xcf, 0x03, 0xc3, 0xff, 0x1f, 0xf1, 0xe1, 0xe7, 0xfe, 
0x3c, 0x03, 0x8f, 0x3f, 0xf8, 0x3f, 0xf0, 0x1f, 0x87, 0xb7, 0x9e, 0xde, 0x7f, 0x01, 0xe7, 0x8f, 
0xdf, 0xcf, 0xff, 0x3c, 0x3c, 0xf0, 0xf3, 0xc1, 0xef, 0x00, 0x3c, 0x1e, 0xe1, 0xe7, 0x9e, 0x1e, 
0x0f, 0x0f, 0x3c, 0x79, 0xe3, 0xcf, 0x1e, 0x3c, 0xf0, 0x3f, 0xf1, 0xff, 0x9e, 0x3f, 0xf8, 0xff, 
0x9f, 0xf0, 0xf0, 0xff, 0x80, 0xff, 0xe3, 0xc7, 0x9e, 0x07, 0xfc, 0x7e, 0x3c, 0x3c, 0x0f, 0x1e, 
0x7f, 0xe3, 0xff, 0xff, 0x1e, 0x3c, 0xfe, 0x00, 0x1e, 0x71, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x00, 0x78, 0x3d, 0xe0, 0x0e, 0x00, 0x00, 0xf3, 0xc3, 0xc0, 0x78, 0x70, 0xf0, 0xf3, 0xc1, 
0xe1, 0xe0, 0xf0, 0xf1, 0xe3, 0xc0, 0x01, 0xf0, 0x78, 0x1e, 0x3f, 0xf8, 0x78, 0x3d, 0xe0, 0xf7, 
0x80, 0x1e, 0x1e, 0x3c, 0x00, 0xf1, 0xe3, 0xc0, 0x0f, 0x3f, 0xcf, 0x1f, 0xe7, 0x8f, 0x1e, 0x07, 
0x8f, 0x3c, 0xfc, 0xf3, 0xc0, 0xf1, 0xe0, 0x38, 0xf0, 0x3c, 0xf0, 0x03, 0xc0, 0x00, 0xf0, 0x07, 
0xc0, 0xe7, 0x8e, 0x0f, 0xf0, 0x78, 0x1e, 0x00, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xf0, 
0xf3, 0xc1, 0xef, 0x3c, 0x1e, 0x00, 0xe3, 0xc7, 0x03, 0x83, 0xcf, 0x3c, 0xf1, 0xe1, 0xe7, 0xfe, 
0x3c, 0x03, 0x8f, 0x3f, 0xf8, 0x7f, 0xf8, 0x1f, 0x87, 0xb7, 0x9e, 0xde, 0x7f, 0xc1, 0xe7, 0x9c, 
0xdb, 0xcf, 0xff, 0x3c, 0x3c, 0xf0, 0xf3, 0xc1, 0xef, 0x00, 0x3c, 0x0e, 0xe1, 0xe7, 0x9e, 0x1f, 
0x0f, 0x0f, 0x1f, 0xf9, 0xe3, 0xcf, 0x1e, 0x3c, 0xf0, 0x3c, 0x79, 0xe3, 0xde, 0x3c, 0x3c, 0xff, 
0x9f, 0xf0, 0xf0, 0xff, 0x80, 0xff, 0xe3, 0xc7, 0x9e, 0x07, 0xfc, 0x3f, 0x3c, 0x3c, 0x0f, 0x1e, 
0x78, 0xf3, 0xff, 0x87, 0x9e, 0x3c, 0xff, 0x80, 0x0e, 0xe1, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x00, 0x78, 0x3d, 0xe0, 0x0f, 0x00, 0x00, 0xf3, 0xc3, 0xc0, 0x78, 0xf0, 0xf0, 0xf3, 0xc1, 
0xe1, 0xe0, 0xf0, 0xf1, 0xe3, 0xc0, 0x00, 0xe0, 0x78, 0x1e, 0x3f, 0xf8, 0x78, 0x3d, 0xe0, 0xf7, 
0x80, 0x3e, 0x1e, 0x3c, 0x00, 0xf1, 0xe3, 0xc0, 0x0f, 0x3f, 0x8f, 0x1f, 0xc7, 0x8f, 0x1e, 0x0f, 
0x0f, 0x3c, 0xf8, 0xf3, 0xc0, 0xf0, 0xf0, 0x78, 0xf0, 0x3c, 0xf0, 0x01, 0xe0, 0x00, 0xf0, 0x03, 
0x80, 0xf7, 0x9e, 0x0e, 0xf0, 0x78, 0x1e, 0x00, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xf0, 
0xf3, 0xc1, 0xef, 0x3c, 0x1e, 0x01, 0xe3, 0xc7, 0x87, 0x87, 0x8f, 0x78, 0xf0, 0xe1, 0xe7, 0x87, 
0xbc, 0x07, 0x0f, 0x3f, 0xf8, 0xff, 0xfc, 0x01, 0xe7, 0xf7, 0x9f, 0xde, 0x7f, 0xc1, 0xe7, 0x9c, 
0xfb, 0xcf, 0x0f, 0x3c, 0x3c, 0xf0, 0xf3, 0xc1, 0xef, 0x00, 0x3c, 0x0f, 0xc1, 0xe7, 0x9e, 0x3f, 
0x0f, 0x0f, 0x1f, 0xf9, 0xe3, 0xcf, 0x1e, 0x3c, 0xf0, 0x3c, 0x79, 0xe3, 0xde, 0x3c, 0x3c, 0x07, 
0x9e, 0x70, 0xe1, 0xe7, 0x80, 0xff, 0xe3, 0xc7, 0x9e, 0x07, 0x80, 0x07, 0xbc, 0x3c, 0x0f, 0x1e, 
0x78, 0xf3, 0xc7, 0x87, 0x9e, 0x3c, 0xff, 0x80, 0x0f, 0xe1, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x00, 0x78, 0x3d, 0xe0, 0x07, 0x80, 0x20, 0xf3, 0xc3, 0xc0, 0xf1, 0xf0, 0xf1, 0xe3, 0xc1, 
0xe3, 0xc0, 0xf0, 0xf1, 0xe1, 0xe0, 0x01, 0xe0, 0x78, 0x1e, 0x78, 0x3c, 0x78, 0x79, 0xe1, 0xf7, 
0x80, 0x3c, 0x1e, 0x3c, 0x00, 0xe1, 0xe1, 0xc4, 0x1f, 0x3f, 0x0f, 0x1f, 0x87, 0x8f, 0x0f, 0x0f, 
0x0f, 0x3c, 0xf8, 0xf3, 0xc0, 0xf0, 0xf8, 0xf8, 0xf0, 0x3c, 0xf0, 0x01, 0xf0, 0x00, 0xf0, 0x07, 
0x80, 0xf7, 0xbc, 0x1e, 0x78, 0x78, 0x1e, 0x00, 0x1e, 0x78, 0x78, 0x79, 0xe1, 0xe1, 0xe0, 0xf1, 
0xe3, 0xc3, 0xcf, 0x3c, 0x3c, 0x03, 0xc3, 0xc7, 0x87, 0x87, 0x0f, 0x78, 0xf0, 0xf3, 0xc7, 0x87, 
0xbc, 0x07, 0x0f, 0x1c, 0x00, 0xe7, 0x9c, 0x41, 0xe7, 0xe7, 0x9f, 0x9e, 0x79, 0xe3, 0xc7, 0x9c, 
0xfb, 0xcf, 0x0f, 0x1e, 0x78, 0xf0, 0xf3, 0xe3, 0xc7, 0x80, 0x3c, 0x0f, 0xc0, 0xf7, 0xbc, 0x7f, 
0x8f, 0x0f, 0x0f, 0x79, 0xe3, 0xcf, 0x1e, 0x3c, 0xf0, 0x3c, 0x79, 0xe3, 0xde, 0x3c, 0x3c, 0x0f, 
0x1e, 0x79, 0xe1, 0xe7, 0x80, 0x70, 0x03, 0xc7, 0x9e, 0x03, 0xc0, 0x87, 0xbc, 0x3c, 0x0f, 0x3c, 
0x78, 0xf3, 0xc7, 0x87, 0x9e, 0x3c, 0xf3, 0xc0, 0x07, 0xe1, 0xe1, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe0, 0x78, 0x3d, 0xe0, 0x07, 0xfe, 0x7f, 0xe3, 0xc3, 0xc7, 0xf3, 0xe0, 0xff, 0xe3, 0xc1, 
0xff, 0xc0, 0xf0, 0xf1, 0xe1, 0xe0, 0x07, 0xc0, 0x7f, 0xfe, 0x78, 0x3c, 0x7f, 0xf9, 0xff, 0xe7, 
0x80, 0xff, 0xff, 0x3f, 0xf1, 0xe1, 0xe1, 0xe7, 0xfe, 0x3f, 0x0f, 0x1f, 0x87, 0x8f, 0x0f, 0x3e, 
0x0f, 0x3c, 0x78, 0xf3, 0xc0, 0xf0, 0x7f, 0xf0, 0xf0, 0x3c, 0xf0, 0x00, 0xff, 0xc0, 0xf0, 0x3f, 
0x00, 0x7f, 0xfc, 0x3c, 0x7c, 0x7f, 0xff, 0x00, 0x1e, 0x7f, 0xff, 0xf9, 0xff, 0xff, 0xf0, 0xff, 
0xe3, 0xff, 0xcf, 0x3f, 0xfc, 0xff, 0xc3, 0xc3, 0xff, 0x0f, 0x0f, 0x7f, 0xf0, 0x7f, 0xc7, 0x87, 
0xbc, 0x1f, 0xff, 0x9f, 0xf1, 0xe7, 0x9e, 0x7f, 0xc7, 0xe7, 0x9f, 0x9e, 0x78, 0xe7, 0xc7, 0x9c, 
0xf1, 0xcf, 0x0f, 0x1f, 0xf8, 0xf0, 0xf3, 0xff, 0xc7, 0xf8, 0x3c, 0x07, 0xc0, 0xff, 0xfc, 0x73, 
0x8f, 0xff, 0x80, 0x79, 0xff, 0xff, 0x1f, 0xff, 0xf8, 0x3f, 0xf1, 0xff, 0x9e, 0x3f, 0xf9, 0xff, 
0x1e, 0x7f, 0xe3, 0xc7, 0x80, 0x7f, 0xc3, 0xc7, 0x9e, 0x03, 0xfe, 0xff, 0xbc, 0x3c, 0x0f, 0x7c, 
0x7f, 0xe3, 0xc7, 0xff, 0x1e, 0x3c, 0xf1, 0xc0, 0x07, 0xc1, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe0, 0x78, 0x79, 0xe0, 0x01, 0xfe, 0x7f, 0xc3, 0xc3, 0xc7, 0xe3, 0xc0, 0xff, 0x83, 0xc1, 
0xff, 0x00, 0xf0, 0xf1, 0xe0, 0xe0, 0x07, 0x80, 0x7f, 0xfe, 0x70, 0x1c, 0x7f, 0xf1, 0xff, 0xc7, 
0x80, 0xff, 0xff, 0x3f, 0xf1, 0xe1, 0xe1, 0xef, 0xfc, 0x3e, 0x0f, 0x1f, 0x07, 0x8f, 0x07, 0x3e, 
0x0f, 0x3c, 0x70, 0xf3, 0xc0, 0xf0, 0x3f, 0xe0, 0xf0, 0x3c, 0xf0, 0x00, 0x3f, 0xe0, 0xf0, 0x3e, 
0x00, 0x3f, 0xf0, 0x3c, 0x3c, 0x7f, 0xff, 0x00, 0x1e, 0x7f, 0xff, 0xf9, 0xff, 0xff, 0xf0, 0xff, 
0xc3, 0xff, 0x8f, 0x3f, 0xf8, 0xff, 0x03, 0xc1, 0xfe, 0x0f, 0x0f, 0x3f, 0xf0, 0x3f, 0x87, 0xff, 
0x3c, 0x1f, 0xff, 0x8f, 0xf1, 0xc7, 0x8e, 0x7f, 0xc7, 0xc7, 0x9f, 0x1e, 0x78, 0xf7, 0x87, 0x9c, 
0x71, 0xcf, 0x0f, 0x0f, 0xf0, 0xf0, 0xf3, 0xff, 0x83, 0xf8, 0x3c, 0x07, 0x80, 0x7f, 0xf8, 0xf3, 
0xcf, 0xff, 0x80, 0x79, 0xff, 0xff, 0x1f, 0xff, 0xf8, 0x3f, 0xe1, 0xff, 0x1e, 0x3f, 0xf1, 0xfe, 
0x1e, 0x3f, 0xc3, 0xc7, 0x80, 0x3f, 0xc3, 0xc7, 0x1e, 0x01, 0xfe, 0xff, 0x3c, 0x3c, 0x0f, 0x78, 
0x7f, 0xc3, 0xc7, 0xfe, 0x1e, 0x3c, 0xf1, 0xe0, 0x07, 0xc1, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7f, 0xe0, 0x78, 0x79, 0xe0, 0x00, 0x7d, 0x1f, 0x03, 0xc3, 0xcf, 0xc3, 0x80, 0xfe, 0x03, 0xc1, 
0xfc, 0x00, 0xf0, 0xf1, 0xe0, 0xf0, 0x0f, 0x00, 0x7f, 0xfe, 0xf0, 0x1e, 0x7f, 0x81, 0xff, 0x07, 
0x80, 0xff, 0xff, 0x3f, 0xf3, 0xc1, 0xe0, 0xf3, 0xf0, 0x3e, 0x0f, 0x1f, 0x07, 0x8f, 0x07, 0xb8, 
0x0f, 0x38, 0x70, 0xf3, 0xc0, 0xf0, 0x07, 0x00, 0xf0, 0x3c, 0xf0, 0x00, 0x0f, 0xc0, 0xf0, 0x38, 
0x00, 0x07, 0x80, 0x78, 0x1e, 0x7f, 0xff, 0x00, 0x1e, 0x7f, 0xff, 0xf9, 0xff, 0xff, 0xf0, 0xfe, 
0x03, 0xfc, 0x0f, 0x3f, 0xc0, 0x7c, 0x03, 0xc0, 0x78, 0x1e, 0x0f, 0x1e, 0x70, 0x0e, 0x07, 0xfc, 
0x3c, 0x1f, 0xff, 0x83, 0xe3, 0xc7, 0x8f, 0x1f, 0x07, 0xc7, 0x9f, 0x1e, 0x78, 0xff, 0x07, 0x9c, 
0x71, 0xcf, 0x0f, 0x03, 0xc0, 0xf0, 0xf3, 0xde, 0x00, 0xfc, 0x3c, 0x07, 0x80, 0x1f, 0xe1, 0xe1, 
0xef, 0xff, 0x80, 0x79, 0xff, 0xff, 0x1f, 0xff, 0xf8, 0x3f, 0x81, 0xfc, 0x1e, 0x3f, 0x80, 0xf8, 
0x1e, 0x0f, 0x07, 0x87, 0x80, 0x0f, 0x83, 0xcf, 0x1e, 0x00, 0x7c, 0x7c, 0x3c, 0x3c, 0x0f, 0x70, 
0x7f, 0x03, 0xc7, 0xf8, 0x1e, 0x3c, 0xf1, 0xf0, 0x07, 0xc1, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x03, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xe0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1c, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x07, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xe0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1c, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xe0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1c, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xe0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1c, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x18, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY MyriadPro18bRus_Font = {0x01, 24, 6, 25, 3, 1, 29, 271, 0x0401, 0x04f9,
(PEGUSHORT *) MyriadPro18bRus_offset_table,&Tahoma18bTai_Font,
(PEGUBYTE *) MyriadPro18bRus_data_table};

