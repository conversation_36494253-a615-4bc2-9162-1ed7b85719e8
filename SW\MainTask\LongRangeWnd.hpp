#include "Wnd.hpp"
#include "CheckCtrl.hpp"
#include "ComboCtrl.hpp"

#ifndef __LONG_RANGE_WND_HPP__
#define __LONG_RANGE_WND_HPP__

class CLongRangeWnd : public CWnd {
protected:
	enum {
		FOCUS_SHIP_ID = 0,
		FOCUS_UTC,
		FOCUS_POSITION,
		FOCUS_COG,
		FOCUS_SOG,
		FOCUS_DEST_ETA,
		FOCUS_DRAUGHT,
		FOCUS_SHIP_CARGO,
		FOCUS_SHIP_DIM,
		FOCUS_PERSONS,
		FOCUS_EN_BROADCAST,
		FOCUS_MODE,
#ifdef EN_61993_ED3
		FOCUS_LR1_CH,
		FOCUS_LR2_CH,
#endif	// End of (EN_61993_ED3)
		MAX_FOCUS
	};

	CCheckCtrl *m_pShipIDCheck;
	CCheckCtrl *m_pUTCCheck;
	CCheckCtrl *m_pPositionCheck;
	CCheckCtrl *m_pCOGCheck;
	CCheckCtrl *m_pSOGCheck;
	CCheck<PERSON>trl *m_pDestETACheck;
	CCheckCtrl *m_pDraughtCheck;
	CCheckCtrl *m_pShipCargoCheck;
	CCheckCtrl *m_pShipDimCheck;
	CCheckCtrl *m_pPersonsCheck;
	CCheckCtrl *m_pEnBroadCastCheck;
	CComboCtrl *m_pModeCombo;
#ifdef EN_61993_ED3
	CComboCtrl *m_pLR1Ch;
	CComboCtrl *m_pLR2Ch;
#endif	// End of (EN_61993_ED3)

	int         m_nFocus;

public:
	CLongRangeWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
	~CLongRangeWnd();

	void OnKeyEvent(int nKey, DWORD nFlags);
	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	int  GetFocus()           { return m_nFocus; }
	void InitLongRangeSetting();
	void ComboCollapse() {
		m_pModeCombo->Collapse();
	}
};

#endif

