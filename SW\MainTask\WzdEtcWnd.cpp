
#include <stdio.h>
#include "WndMgr.hpp"
#include "WzdEtcWnd.hpp"
#include "DocMgr.hpp"
#include "Ship.hpp"
#include "keybd.hpp"
#include "const.h"
#include "NMEA/VSD.hpp"
#include "NMEA/Sentence.hpp"
#include "String.hpp"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Font.h"
#include "Uart.hpp"

#define WZD_ETC_X					5
#define WZD_ETC_Y					(WND_BACK_Y_POS + 5)
#define WZD_ETC_W					(WND_BACK_W - 5*2)
#define WZD_ETC_H					(WND_BACK_H - 5*2)
#define MAX_WZD_ETC_ROWS			10
#define WZD_ETC_ROW_H				(WZD_ETC_H/MAX_WZD_ETC_ROWS)
#define WZD_ETC_COL1_X				WZD_ETC_X
#define WZD_ETC_COL2_X				(WZD_ETC_X + 220)
#define WZD_ETC_CTRL_H				30
#define WZD_ETC_LAN_CMB_W			170


extern CShip   *g_pOwnShip;
extern CDocMgr *g_pDocMgr;
extern CWndMgr *g_pWndMgr;
extern cUART   *G_pUart3;


/*********************************************************************************************************/
// Name		: CWzdEtcWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CWzdEtcWnd::CWzdEtcWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID): CWnd(pScreen, pCaption, dWndID) 
{
	m_nFocus = FOCUS_LANG;
	CreateControls(pScreen);
}

/*********************************************************************************************************/
// Name		: CreateControls
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::CreateControls(cSCREEN *pScreen)
{
	m_pCmbLang = new CUniComboCtrl(pScreen);

	m_pCmbLang->Create(WZD_ETC_X + (WZD_ETC_W - WZD_ETC_LAN_CMB_W)/2, WZD_ETC_Y + WZD_ETC_ROW_H*4+ (WZD_ETC_ROW_H - WZD_ETC_CTRL_H)/2, WZD_ETC_LAN_CMB_W, WZD_ETC_CTRL_H, 10,TRUE,&NewGulLim18bCJK);
	for(int i = 0; i<LANG_SIZE; i++)
	{
		m_pCmbLang->Add((const HWORD *)STR_LANG_TYPE[i]);
	}

	// Use Hangul Ship Name
	m_pUseHanShipName	  = new CCheckCtrl(pScreen);
	m_pUseHanShipName->Create(	WZD_ETC_X + (WZD_ETC_W - WZD_ETC_LAN_CMB_W)/2, 
								WZD_ETC_Y + WZD_ETC_ROW_H*5 + (WZD_ETC_ROW_H - WZD_ETC_CTRL_H)/2, 
								(const BYTE **)STR_USE_HAN_SHIP_NAME);
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
			if( m_nFocus == FOCUS_LANG && m_pCmbLang->IsExpand() )
			{
				m_pCmbLang->OnKeyEvent(nKey, nFlags);
				break;
			}

			if(m_pCmbLang->GetCurSel() == LANG_KOR)
			{
				if( m_nFocus > 0 ) 
				{
					m_nFocus--;
				}	
			}
			else
			{
				m_nFocus = FOCUS_LANG;
			}
			
			DrawWnd(TRUE);
			break;
			
		case KBD_SCAN_CODE_DOWN:
			if( m_nFocus == FOCUS_LANG && m_pCmbLang->IsExpand() )
			{
				m_pCmbLang->OnKeyEvent(nKey, nFlags);
				break;
			}

			if(m_pCmbLang->GetCurSel() == LANG_KOR)
			{
				if( m_nFocus < FOCUS_LAST ) 
				{
					m_nFocus++;
				}	
			}
			else
			{
				m_nFocus = FOCUS_LANG;
			}
			DrawWnd(TRUE);
			break;
			
		case KBD_SCAN_CODE_FUNC2:   // NEXT
			if(m_pCmbLang->IsExpand() == FALSE)
			{
				ComboCollapse();
				SaveEtcData();
				g_pWndMgr->SetActiveWnd(WID_WZD_PWD_SET_WND);
				break;
			}
			break;
			
		default:
			switch( m_nFocus )
			{
				case FOCUS_LANG:
					m_pCmbLang->OnKeyEvent(nKey, nFlags);
					if(m_pCmbLang->IsExpand() == FALSE)
					{
						DrawWnd(TRUE);
					}						
					break;

				case FOCUS_USE_HAN_SHIP_NAME:
					if(g_pDocMgr->GetLangMode() == LANG_KOR)
					{
						m_pUseHanShipName->OnKeyEvent(nKey, nFlags);
					}						
					break;
			}
			break;
	}
}

/*********************************************************************************************************/
// Name		: OnCursorEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::OnCursorEvent(int nState)
{
	switch( m_nFocus )
	{
#if 0	
		case FOCUS_DEST:
			m_pDestEdit->OnCursorEvent(nState);
			break;
		
		case FOCUS_ETA_TIME:
			m_pETATimeEdit->OnCursorEvent(nState);
			break;
			
		case FOCUS_PERSONS:
			m_pPersonsEdit->OnCursorEvent(nState);
			break;
			
		case FOCUS_SHIP_STATUS:
			break;
			
		case FOCUS_DRAUGHT:
			m_pDraught->OnCursorEvent(nState);
			break;
#endif			
		default:
			break;
	}
}

/*********************************************************************************************************/
// Name		: InitWzdEtcWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::InitWzdEtcWnd()
{
	if(m_pCmbLang != NULL)
	{
		m_pCmbLang->SetCurSel(g_pDocMgr->GetLangMode());
		m_pCmbLang->Collapse();
	}

	if(m_pCmbLang->GetCurSel() == LANG_KOR)
	{
		m_pUseHanShipName->SetCheck(g_pDocMgr->GetUseHanShipNameFlag());
	}
	else
	{
		m_pUseHanShipName->SetCheck(FALSE);
	}
}

/*********************************************************************************************************/
// Name		: OnActivate
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::OnActivate()
{
	InitWzdEtcWnd();
	DrawWnd(TRUE);
}

/*********************************************************************************************************/
// Name		: DrawControls
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::DrawControls()
{
	m_pCmbLang->DrawWnd();

	if( m_pCmbLang->IsExpand() )
	{
		m_pCmbLang->DrawWnd();
	}

	if(m_pCmbLang->GetCurSel() == LANG_KOR && (m_pCmbLang->IsExpand() == FALSE))
	{
		m_pUseHanShipName->DrawWnd();
	}
}

/*********************************************************************************************************/
// Name		: DrawFuncBtn
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::DrawFuncBtn()
{
	int nLangMode = g_pDocMgr->GetLangMode();
	EraseButton(0);
	DrawButton(1, (BYTE *)FK_NEXT[nLangMode]);
	EraseButton(2);
	DrawButton(3, (BYTE *)FK_FINISH[nLangMode]);
}


/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::DrawWnd(BOOL bRedraw)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	COLORT txtClr;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 0;
			break;	

		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 3;
			break;
	}
	CWnd::DrawWnd(bRedraw);

	if( bRedraw )
	{
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

#ifdef MULTI_LANG
		txtClr = COLORSCHEME[m_nScheme].crFore;
		pUniCodeStr = (HWORD *)STR_LANG_MODE[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		
		nXPos = WZD_ETC_COL1_X + (WZD_ETC_W - nStrW)/2;
		nYPos = WZD_ETC_Y +  WZD_ETC_ROW_H*3 + (WZD_ETC_ROW_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
#endif
		
		m_pScreen->SetFont(pOldFont);
	}

	DrawFuncBtn();

	switch( m_nFocus )
	{
		case FOCUS_LANG:
			m_pCmbLang->SetFocus(TRUE);
			m_pUseHanShipName->SetFocus(FALSE);
			break;

		case FOCUS_USE_HAN_SHIP_NAME:
			if(m_pCmbLang->GetCurSel() == LANG_KOR)
			{
				m_pCmbLang->SetFocus(FALSE);
				m_pUseHanShipName->SetFocus(TRUE);
			}
			else
			{
				m_pCmbLang->SetFocus(TRUE);
				m_pUseHanShipName->SetFocus(FALSE);
			}
			break;
	}
	DrawControls();
}



/*********************************************************************************************************/
// Name		: SaveEtcData
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdEtcWnd::SaveEtcData()
{
	g_pDocMgr->SetLangMode(m_pCmbLang->GetCurSel());

	if(g_pDocMgr->GetLangMode() == LANG_KOR)
	{
		g_pDocMgr->SetUseHANShipName(m_pUseHanShipName->GetCheck());
	}
	else
	{
		g_pDocMgr->SetUseHANShipName(FALSE);
	}

	// Save Setting Value
	g_pDocMgr->SaveSettingValue();
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CWzdEtcWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);

	if( nResult == AL_YES )
	{
	}

	return nResult;
}

