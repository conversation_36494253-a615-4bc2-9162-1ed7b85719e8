#include "LongRangeMsgWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "DblList.h"
#include "ship.hpp"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Font.h"
#include "TargetListWnd.hpp"

#define LRM_LIST_X_POS		10
#define LRM_LIST_Y_POS		54

#define LRM_LIST_W			580
#define LRM_LIST_H			370

#define LRM_LIST_ITEM_H	37
#define LRM_LIST_COL_W1	60
#define LRM_LIST_COL_W2	70
#define LRM_LIST_COL_W3	80	
#define LRM_LIST_COL_W4	80
#define LRM_LIST_COL_W5	290

#define LRM_LIST_COL1_X LRM_LIST_X_POS
#define LRM_LIST_COL2_X (LRM_LIST_COL1_X + LRM_LIST_COL_W1)
#define LRM_LIST_COL3_X (LRM_LIST_COL2_X + LRM_LIST_COL_W2)
#define LRM_LIST_COL4_X (LRM_LIST_COL3_X + LRM_LIST_COL_W3)
#define LRM_LIST_COL5_X (LRM_LIST_COL4_X + LRM_LIST_COL_W4)

#define SCROLL_BAR_W			20
#define SCROLL_BAR_X_POS		(LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + LRM_LIST_COL_W3 + LRM_LIST_COL_W4 + LRM_LIST_COL_W5 - SCROLL_BAR_W)
#define SCROLL_BAR_TOP_POS		(LRM_LIST_Y_POS + LRM_LIST_ITEM_H)
#define SCROLL_BAR_BTM_POS		(LRM_LIST_Y_POS + LRM_LIST_ITEM_H*6 - 1)
#define SCROLL_BAR_HEIGHT		(LRM_LIST_ITEM_H*6);

#define SCROLL_TRIANGLE_W		5
#define SCROLL_TRIANGLE_H		5
#define SCROLL_BAR_AREA_X_POS	SCROLL_BAR_X_POS + 2
#define SCROLL_BAR_AREA_Y_POS	SCROLL_BAR_TOP_POS + SCROLL_TRIANGLE_H + 10
#define SCROLL_BAR_AREA_W		(SCROLL_BAR_W -6)
#define SCROLL_BAR_AREA_H		(SCROLL_BAR_BTM_POS - SCROLL_BAR_TOP_POS +1 - SCROLL_TRIANGLE_H*2 - 10)


extern CDocMgr           *g_pDocMgr;
extern CDblList<CLRMsg>  *g_pLRMsg;
extern CShip             *g_pOwnShip;

CLongRangeMsgWnd::CLongRangeMsgWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	m_nStartViewPos = 0;
	m_nCurSel       = 0;
}

void CLongRangeMsgWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
	case KBD_SCAN_CODE_UP:
		if( m_nCurSel > 0 )
		{
			m_nCurSel--;

			if( m_nCurSel < m_nStartViewPos )
				m_nStartViewPos--;
			
			DrawWnd(0);
		}
		break;
	case KBD_SCAN_CODE_DOWN:
		if( m_nCurSel < g_pLRMsg->GetSize() - 1 )
		{
			m_nCurSel++;

			if( m_nCurSel >= m_nStartViewPos + MSG_CNT_PER_PAGE )
				m_nStartViewPos++;

			DrawWnd(0);
		}
		break;
	default:
		break;
	}
}

void CLongRangeMsgWnd::OnActivate()
{
	m_nStartViewPos = 0;
	m_nCurSel       = 0;
}

void CLongRangeMsgWnd::DrawWnd(BOOL bRedraw/*TRUE*/)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	int nYOffset = 0;
	HWORD *pUniCodeStr = NULL;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 0;
			break;
			
		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 2;
			break;
	}

	CWnd::DrawWnd(bRedraw);

	if( bRedraw )
	{
		m_pScreen->Rect(LRM_LIST_X_POS,
			            LRM_LIST_Y_POS,
			            LRM_LIST_X_POS + LRM_LIST_W - 1,
			            LRM_LIST_Y_POS + LRM_LIST_ITEM_H - 1,
			            COLORSCHEME[m_nScheme].crFore);

		
		m_pScreen->Line(LRM_LIST_X_POS + LRM_LIST_COL_W1,
			            LRM_LIST_Y_POS,
			            LRM_LIST_X_POS + LRM_LIST_COL_W1,
			            LRM_LIST_Y_POS + LRM_LIST_ITEM_H - 1,
			            COLORSCHEME[m_nScheme].crFore);

		m_pScreen->Line(LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2,
			            LRM_LIST_Y_POS,
			            LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2,
			            LRM_LIST_Y_POS + LRM_LIST_ITEM_H - 1,
			            COLORSCHEME[m_nScheme].crFore);

		m_pScreen->Line(LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + LRM_LIST_COL_W3,
			            LRM_LIST_Y_POS,
			            LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + LRM_LIST_COL_W3,
			            LRM_LIST_Y_POS + LRM_LIST_ITEM_H - 1,
			            COLORSCHEME[m_nScheme].crFore);

		m_pScreen->Line(LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + LRM_LIST_COL_W3 + LRM_LIST_COL_W4,
			            LRM_LIST_Y_POS,
			            LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + LRM_LIST_COL_W3 + LRM_LIST_COL_W4,
			            LRM_LIST_Y_POS + LRM_LIST_ITEM_H - 1,
			            COLORSCHEME[m_nScheme].crFore);
	

		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_ACK[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_ACK[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = LRM_LIST_X_POS + (LRM_LIST_COL_W1 - nStrW)/2;
		nYPos = LRM_LIST_Y_POS + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_LR_MODE[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_LR_MODE[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = LRM_LIST_X_POS + LRM_LIST_COL_W1 + (LRM_LIST_COL_W2 - nStrW)/2;
		nYPos = LRM_LIST_Y_POS + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_DATE[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_DATE[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + (LRM_LIST_COL_W3 - nStrW)/2;
		nYPos = LRM_LIST_Y_POS + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_TIME[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_TIME[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + LRM_LIST_COL_W3 + (LRM_LIST_COL_W4 - nStrW)/2;
		nYPos = LRM_LIST_Y_POS + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_REQUESTER[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_REQUESTER[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = LRM_LIST_X_POS + LRM_LIST_COL_W1 + LRM_LIST_COL_W2 + LRM_LIST_COL_W3 + LRM_LIST_COL_W4 + (LRM_LIST_COL_W5 - nStrW)/2;
		nYPos = LRM_LIST_Y_POS + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		m_pScreen->SetFont(pOldFont);
	}

	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
	DrawButton(1, (BYTE *)FK_DELETE[nLangMode]);
	
	EraseButton(2);
	EraseButton(3);

	DrawLRMsgList();

	DrawScrollBar();
	
	m_pScreen->Rect(LRM_LIST_X_POS,
		            LRM_LIST_Y_POS,
		            LRM_LIST_X_POS + LRM_LIST_W - 1,
		            LRM_LIST_Y_POS + LRM_LIST_H - 1,
		            COLORSCHEME[m_nScheme].crFore);

	m_pScreen->Line(LRM_LIST_X_POS,
		            LRM_LIST_Y_POS + LRM_LIST_ITEM_H*(MSG_CNT_PER_PAGE + 1),
		            LRM_LIST_X_POS + LRM_LIST_W - 1,
		            LRM_LIST_Y_POS + LRM_LIST_ITEM_H*(MSG_CNT_PER_PAGE + 1),
		            COLORSCHEME[m_nScheme].crFore);

	DrawCountStat();
}

void CLongRangeMsgWnd::DrawLRMsgList()
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	int nXOffset = 0, nYOffset = 0;
	HWORD *pUniCodeStr = NULL;
	COLORT bkClr;
	COLORT txtClr;
	int nLangMode = g_pDocMgr->GetLangMode();

	CLRMsg oLRMsg;
	BYTE   szMsg[100];
	BYTE   szDate[12];
	BYTE   szTime[12];
	int    nEndViewPos = m_nStartViewPos + MSG_CNT_PER_PAGE;
	
	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &MyriadPro24bEng;
			nXOffset = 3;
			nYOffset = 2;
			break;

		default:
			pFont = &MyriadPro24bEng;
			nXOffset = 3;
			nYOffset = 2;
			break;
	}

	if( nEndViewPos > g_pLRMsg->GetSize() )
	{
		nEndViewPos = g_pLRMsg->GetSize();
	}		

	pOldFont = m_pScreen->SetFont(pFont);
	nFontH = pFont->uHeight;
	
	for( int i=m_nStartViewPos; i<nEndViewPos; i++ )
	{
		oLRMsg = g_pLRMsg->GetAt(i);
		oLRMsg.GetDate((char *)szDate);
		oLRMsg.GetTime((char *)szTime);
		sprintf((char *)szMsg, " %-4s   %s   %s   %09d ", (oLRMsg.GetMode())?"AUTO":"MAN", szDate, szTime, oLRMsg.GetRequester());
		
		if( m_nCurSel == i ) 
		{
			bkClr = COLORSCHEME[m_nScheme].crFore;
			txtClr = COLORSCHEME[m_nScheme].crBack;
			
			m_pScreen->FillRect(LRM_LIST_X_POS,
				                LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1),
				                LRM_LIST_X_POS + LRM_LIST_W - 1,
				                LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 2),
				                bkClr);

			// Mode
			sprintf((char *)szMsg,"%-4s",(oLRMsg.GetMode())?"AUTO":"MAN");
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL2_X + (LRM_LIST_COL_W2 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

			// Date
			sprintf((char *)szMsg,"%s",szDate);
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL3_X + (LRM_LIST_COL_W3 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
			

			// Time
			sprintf((char *)szMsg,"%s",szTime);
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL4_X + (LRM_LIST_COL_W4 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
			

			// Requester
			sprintf((char *)szMsg,"%09d",oLRMsg.GetRequester());
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL5_X + (LRM_LIST_COL_W5 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
			
			DrawFunction(&oLRMsg);

			// ���õ� ���� ACK���� �ʾ����� OK/CANCEL�� ACK�� �� �ֵ��� ��ư ǥ��
			if( oLRMsg.GetAck() )
			{
				EraseButton(2);
				EraseButton(3);
			}
			else
			{
#ifdef RUSSIA_CERT
				DrawButton(2, (BYTE *)FK_OK[nLangMode]);
				DrawButton(3, (BYTE *)FK_CANCEL[nLangMode]);
#else
				DrawButton(2, (BYTE *)FK_RESPONSE[nLangMode]);
				DrawButton(3, (BYTE *)FK_REJECT[nLangMode]);
#endif				
			}
		}
		else
		{
			bkClr = COLORSCHEME[m_nScheme].crBack;
			txtClr = COLORSCHEME[m_nScheme].crFore;
			
			m_pScreen->FillRect(LRM_LIST_X_POS,
				                LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1),
				                LRM_LIST_X_POS + LRM_LIST_W - 1,
				                LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 2),
				                bkClr);
			
			// Mode
			sprintf((char *)szMsg,"%-4s",(oLRMsg.GetMode())?"AUTO":"MAN");
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL2_X + (LRM_LIST_COL_W2 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

			// Date
			sprintf((char *)szMsg,"%s",szDate);
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL3_X + (LRM_LIST_COL_W3 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
			

			// Time
			sprintf((char *)szMsg,"%s",szTime);
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL4_X + (LRM_LIST_COL_W4 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
			

			// Requester
			sprintf((char *)szMsg,"%09d",oLRMsg.GetRequester());
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szMsg,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_COL5_X + (LRM_LIST_COL_W5 - nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
		}
		
		if( oLRMsg.GetAck() )
		{
			pUniCodeStr = ConvertCharStrToUniStr((CHAR *)CHECK_STR,nLangMode);
			nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
			nXPos = LRM_LIST_X_POS + (LRM_LIST_COL_W1-nStrW)/2;
			nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H * (i - m_nStartViewPos + 1) + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
			m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
		}

	}
	m_pScreen->SetFont(pOldFont);
}

void CLongRangeMsgWnd::DrawFunction(CLRMsg *pLRMsg)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	int nYOffset = 0;
	HWORD *pUniCodeStr = NULL;
	HWORD strUniPad[30];;
	HWORD wStrUniTmp[30];
	int nLangMode = g_pDocMgr->GetLangMode();
	
	char szFunc[28];
	char szName[24];
	char szTemp[100];

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &MyriadPro24bEng;
			nYOffset = 2;
			break;

		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 2;
			break;
	}
	
	pLRMsg->GetFunction(szFunc);
	pLRMsg->GetName(szName);

	m_pScreen->FillRect(LRM_LIST_X_POS + 1,
						LRM_LIST_Y_POS + LRM_LIST_ITEM_H*6 + 1,
						LRM_LIST_X_POS + LRM_LIST_W - 2,
						LRM_LIST_Y_POS + LRM_LIST_ITEM_H*10 - 2,
						COLORSCHEME[m_nScheme].crBack);
	
	pOldFont = m_pScreen->SetFont(pFont);
	nFontH = pFont->uHeight;

	UniStrCpy(strUniPad,ConvertCharStrToUniStr((CHAR *)"..",nLangMode));

	sprintf(szTemp, "MMSI Requester     : %09d", pLRMsg->GetRequester());
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);

	nXPos = LRM_LIST_X_POS + 5;
	nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H*6 + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
				
	sprintf(szTemp, "Name of Requester  : %s", szName);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);

	if(CTargetListWnd::GetStringFitToWidth(pUniCodeStr,wStrUniTmp,m_pScreen,pFont,LRM_LIST_W-5,strUniPad))
	{
		nXPos = LRM_LIST_X_POS + 5;
		nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H*7 + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)wStrUniTmp);
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)wStrUniTmp,COLORSCHEME[m_nScheme].crFore);
	}	

	sprintf(szTemp, "Function Requester : %s", szFunc);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);

	if(CTargetListWnd::GetStringFitToWidth(pUniCodeStr,wStrUniTmp,m_pScreen,pFont,LRM_LIST_W-5,strUniPad))
	{
		nXPos = LRM_LIST_X_POS + 5;
		nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H*8 + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)wStrUniTmp);
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)wStrUniTmp,COLORSCHEME[m_nScheme].crFore);
	}

	if( pLRMsg->GetMode() ) // AUTO MODE
	{
		sprintf(szTemp, "Reply : AUTO");
	}		
	else if( pLRMsg->GetAck() )
	{
		if( pLRMsg->GetReply() )
			sprintf(szTemp, "Reply : OK");
		else
			sprintf(szTemp, "Reply : CANCEL");
	}
	else
	{
		sprintf(szTemp, "Reply : Select OK or CANCEL");
	}
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
	nXPos = LRM_LIST_X_POS + 5;
	nYPos = LRM_LIST_Y_POS + LRM_LIST_ITEM_H*9 + (LRM_LIST_ITEM_H - nFontH)/2 + nYOffset;
	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	m_pScreen->SetFont(pOldFont);
}

void CLongRangeMsgWnd::DrawScrollBar()
{
	int nTotMsgCnt = 0;
	int nScrollBarH = 0/*, nCurPos = 0*/;
	int nLeft = 0, nRight = 0, nTop = 0, nBottom = 0;

	m_pScreen->FillRect(SCROLL_BAR_X_POS,SCROLL_BAR_TOP_POS,SCROLL_BAR_X_POS + SCROLL_BAR_W -1,SCROLL_BAR_BTM_POS,COLORSCHEME[m_nScheme].crBack);
	m_pScreen->Triangle(SCROLL_BAR_X_POS + (SCROLL_BAR_W - SCROLL_TRIANGLE_W)/2, SCROLL_BAR_TOP_POS + 5,SCROLL_TRIANGLE_W,SCROLL_TRIANGLE_H,FALSE, COLORSCHEME[m_nScheme].crFore);
	m_pScreen->Triangle(SCROLL_BAR_X_POS + (SCROLL_BAR_W - SCROLL_TRIANGLE_W)/2, SCROLL_BAR_BTM_POS - SCROLL_TRIANGLE_H - 5,SCROLL_TRIANGLE_W,SCROLL_TRIANGLE_H,TRUE, COLORSCHEME[m_nScheme].crFore);
	m_pScreen->Rect(SCROLL_BAR_X_POS,SCROLL_BAR_TOP_POS,SCROLL_BAR_X_POS + SCROLL_BAR_W -1,SCROLL_BAR_BTM_POS,COLORSCHEME[m_nScheme].crFore);

	if(g_pLRMsg != NULL)
	{
		nTotMsgCnt = g_pLRMsg->GetSize();

		//G_pUart3->OutputDbgMsg("[CMsgListWnd::DrawRxMsgScrollBar]Tot=%d, Cur=%d, StarViewPos=%d\r\n",nTotRxMsgCnt,m_nCurSel,m_nRxStartViewPos);
		if(nTotMsgCnt > 0)
		{

			nScrollBarH = SCROLL_BAR_AREA_H/nTotMsgCnt;
			if(nScrollBarH == 0)
			{
				nScrollBarH = 1;
			}

			nLeft = SCROLL_BAR_AREA_X_POS;
			nTop = SCROLL_BAR_AREA_Y_POS + nScrollBarH*m_nCurSel;
			nRight = SCROLL_BAR_AREA_X_POS + SCROLL_BAR_AREA_W;
			nBottom = nTop + nScrollBarH -1;

			if(nTop >(SCROLL_BAR_AREA_Y_POS + SCROLL_BAR_AREA_H -1))
			{
				nTop = SCROLL_BAR_AREA_Y_POS + SCROLL_BAR_AREA_H -1; 
			}

			if(nBottom >(SCROLL_BAR_AREA_Y_POS + SCROLL_BAR_AREA_H - 10))
			{
				nBottom = (SCROLL_BAR_AREA_Y_POS + SCROLL_BAR_AREA_H - 10); 
			}
			//G_pUart3->OutputDbgMsg("[CMsgListWnd::DrawRxMsgScrollBar]H=%d, Left=%d, Top=%d, Right=%d, Bottom=%d\r\n",nScrollBarH,nLeft,nRight,nTop,nBottom);
			m_pScreen->FillRect(nLeft, nTop,nRight,nBottom,COLORSCHEME[m_nScheme].crFore);
		}
	}
}

/* Modify LR sentence element : HSI 2012.05.01 */
void CLongRangeMsgWnd::AckMessage(int bOK)
{
	CLRMsg oLRMsg;

	if( m_nCurSel >= 0 && m_nCurSel < g_pLRMsg->GetSize() )
	{
		oLRMsg = g_pLRMsg->GetAt(m_nCurSel);

		// �̹� ����� �޽����� ���ؼ��� �������� ����.
		if( oLRMsg.GetAck() ) return;
				
		CLri lri;
		CLrf lrf;

		char szSentence[128];
/*
		lri.SetSeqNumber(oLRMsg.GetSeqNum());
		lri.SetControlFlag(oLRMsg.GetControlFlag());
		lri.SetMMSIReq(oLRMsg.GetRequester());
		lri.SetMMSIDest(g_pOwnShip->GetMMSI());
		lri.MakeSentence(szSentence);
		//g_pDocMgr->SendCommand(szSentence);

		lrf.SetSeqNumber(oLRMsg.GetSeqNum());
		lrf.SetMMSIReq(oLRMsg.GetRequester());
		oLRMsg.GetName(szTemp);
		lrf.SetNameOfRequester(szTemp);
		oLRMsg.GetFunction(szTemp);
		lrf.SetFunctionRequest(szTemp);
		lrf.SetReplyStatus(bOK?'2':'4');
		lrf.MakeSentence(szSentence);
		//g_pDocMgr->SendCommand(szSentence);
*/

		/* Modify the LR message cancel : HSI 2012.06.20 */
		if(bOK)
		{

			if(oLRMsg.GetMode() == 0)
			{

				strcpy(szSentence,oLRMsg.GetLRISentence(oLRMsg.GetSeqNum()));
				lri.MakeSentence((BYTE *)szSentence);
				g_pDocMgr->SendCommand((BYTE *)szSentence);

				strcpy(szSentence,oLRMsg.GetLRFSentence(oLRMsg.GetSeqNum()));
				lrf.MakeSentence((BYTE *)szSentence);
				g_pDocMgr->SendCommand((BYTE *)szSentence);

			}

			(*g_pLRMsg)[m_nCurSel].SetReply(bOK);

		}
		
		(*g_pLRMsg)[m_nCurSel].SetAck(1);
		DrawWnd();
	}
}

void CLongRangeMsgWnd::DeleteCurMsg()
{
	int nSize = g_pLRMsg->GetSize();

	if( nSize > 0 )
	{
		if( m_nCurSel >= 0 && m_nCurSel < nSize )
		{
			g_pLRMsg->RemoveAt(m_nCurSel);

			if( m_nCurSel >= nSize - 1 )
				m_nCurSel = nSize - 1;
		}
	}
}

int CLongRangeMsgWnd::GetMsgCount()
{
	return g_pLRMsg->GetSize();
}

int CLongRangeMsgWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);
	
	if( nResult == AL_YES ) {
		DeleteCurMsg();
	}
	
	return nResult;
}
