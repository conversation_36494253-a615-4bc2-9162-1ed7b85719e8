#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "ComboCtrl.hpp"

#ifndef __SET_TP_PARAMETER_WND_HPP__
#define __SET_TP_PARAMETER_WND_HPP__

class CSetTpParameterWnd : public CWnd {
private:
	enum {
		FOCUS_VSWR = 0,
		FOCUS_COUNT
	};

	CEditCtrl  *m_pVswr;

	int m_nFocus;
	int m_nParamCountForSaving;

	BOOL DrawSaveMessageBox(int nSaveCount);

public:
	CSetTpParameterWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	virtual void OnKeyEvent(int nKey, DWORD nFlags);
	virtual void OnCursorEvent(int nState);
	virtual void OnActivate();
	virtual void OnTimer();

	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	void SystemReset();

	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	void SetVswr(int nVswr) {
		BYTE szVswr[4];
		sprintf((char *)szVswr, "%d", nVswr);
		m_pVswr->SetText(szVswr);
		m_pVswr->DrawWnd();
	}
};

#endif


