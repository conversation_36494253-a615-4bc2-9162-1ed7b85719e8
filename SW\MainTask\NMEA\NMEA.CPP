#include "NMEA.hpp"
#include "DocMgr.hpp"
extern CDocMgr  *g_pDocMgr;

CNmea::CNmea()
{
	abk = new CAbk();
	abm = new CAbm();
	aca = new CAca();
	ack = new CAck();
	acs = new CAcs();
	air = new CAir();
	alr = new CAlr();
	alf = new CAlf();	// IEC62923-2(BAM) applied
	bbm = new CBbm();
	lr1 = new CLr1();
	lr2 = new CLr2();
	lr3 = new CLr3();
	lrf = new CLrf();
	lri = new CLri();
	ssd = new CSsd();
	syc = new CSyc();
	txt = new CTxt();
	vdm = new CVdm();
	vdo = new CVdo();
	vsd = new CVsd();
	test = new CTest();
	gsa = new CGsa();
	gsv = new CGsv();
	spw = new CSpw();
}

CNmea::~CNmea()
{
	delete abk;
	delete abm;
	delete aca;
	delete acs;
	delete air;
	delete alr;
	delete bbm;
	delete lr1;
	delete lr2;
	delete lr3;
	delete lrf;
	delete lri;
	delete ssd;
	delete syc;
	delete txt;
	delete vdm;
	delete vdo;
	delete vsd;
	delete test;
	delete gsa;
	delete gsv;
	delete spw;
	delete alf;
}

int CNmea::SetSentence(char *pszSentence)
{
	int nLen = strlen(pszSentence);

	if( nLen >= MAX_NMEA_LEN || nLen < 5 ) {
		m_pCurParser = NULL;
		return CSentence::NMEA_UNKNOWN;
	}
	
	if( FORMAT_COMP(pszSentence, "ABK") == 0 ) {
		abk->SetSentence(pszSentence);
		m_pCurParser = abk;
		return CSentence::NMEA_ABK;
	} else if( FORMAT_COMP(pszSentence, "ABM") == 0 ) {
		abm->SetSentence(pszSentence);
		m_pCurParser = abm;
		return CSentence::NMEA_ABM;
	} else if( FORMAT_COMP(pszSentence, "ACA") == 0 ) {
		aca->SetSentence(pszSentence);
		m_pCurParser = aca;
		return CSentence::NMEA_ACA;
	} else if( FORMAT_COMP(pszSentence, "ACK") == 0 ) {
		ack->SetSentence(pszSentence);
		m_pCurParser = ack;
		return CSentence::NMEA_ACK;
	} else if( FORMAT_COMP(pszSentence, "ACS") == 0 ) {
		acs->SetSentence(pszSentence);
		m_pCurParser = acs;
		return CSentence::NMEA_ACS;
	} else if( FORMAT_COMP(pszSentence, "AIR") == 0 ) {
		air->SetSentence(pszSentence);
		m_pCurParser = air;
		return CSentence::NMEA_AIR;
	} else if( FORMAT_COMP(pszSentence, "ALR") == 0 ) {
		alr->SetSentence(pszSentence);
		m_pCurParser = alr;
		return CSentence::NMEA_ALR;
	} else if( FORMAT_COMP(pszSentence, "BBM") == 0 ) {
		bbm->SetSentence(pszSentence);
		m_pCurParser = bbm;
		return CSentence::NMEA_BBM;
	} else if( FORMAT_COMP(pszSentence, "LR1") == 0 ) {
		lr1->SetSentence(pszSentence);
		m_pCurParser = lr1;
		return CSentence::NMEA_LR1;
	} else if( FORMAT_COMP(pszSentence, "LR2") == 0 ) {
		lr2->SetSentence(pszSentence);
		m_pCurParser = lr2;
		return CSentence::NMEA_LR2;
	} else if( FORMAT_COMP(pszSentence, "LR3") == 0 ) {
		lr3->SetSentence(pszSentence);
		m_pCurParser = lr3;
		return CSentence::NMEA_LR3;
	} else if( FORMAT_COMP(pszSentence, "LRF") == 0 ) {
		lrf->SetSentence(pszSentence);
		m_pCurParser = lrf;
		return CSentence::NMEA_LRF;
	} else if( FORMAT_COMP(pszSentence, "LRI") == 0 ) {
		lri->SetSentence(pszSentence);
		m_pCurParser = lri;
		return CSentence::NMEA_LRI;
	} else if( FORMAT_COMP(pszSentence, "SSD") == 0 ) {
		ssd->SetSentence(pszSentence);
		m_pCurParser = ssd;
		return CSentence::NMEA_SSD;
	} else if( FORMAT_COMP(pszSentence, "SYC") == 0 ) {
		syc->SetSentence(pszSentence);
		m_pCurParser = syc;
		return CSentence::NMEA_SYC;
	} else if( FORMAT_COMP(pszSentence, "TXT") == 0 ) {
		txt->SetSentence(pszSentence);
		m_pCurParser = txt;
		return CSentence::NMEA_TXT;
	} else if( FORMAT_COMP(pszSentence, "VDM") == 0 ) {
		vdm->SetSentence(pszSentence);
		m_pCurParser = vdm;
		return CSentence::NMEA_VDM;
	} else if( FORMAT_COMP(pszSentence, "VDO") == 0 ) {
		vdo->SetSentence(pszSentence);
		m_pCurParser = vdo;
		return CSentence::NMEA_VDO;
	} else if( FORMAT_COMP(pszSentence, "VSD") == 0 ) {
		vsd->SetSentence(pszSentence);
		m_pCurParser = vsd;
		return CSentence::NMEA_VSD;
	} else if( pszSentence[0] == '%' ) {
		test->SetSentence(pszSentence);
		m_pCurParser = test;
		return CSentence::NMEA_TEST;
	}
	else if( FORMAT_COMP(pszSentence, "GSA") == 0 )
	{
		gsa->SetSentence(pszSentence);
		m_pCurParser = gsa;
		return CSentence::NMEA_GSA;
	}
	else if( FORMAT_COMP(pszSentence, "GSV") == 0 )
	{
		gsv->SetSentence(pszSentence);
		m_pCurParser = gsv;
		return CSentence::NMEA_GSV;
	}
	else if( FORMAT_COMP(pszSentence, "SPW") == 0 )
	{
		spw->SetSentence(pszSentence);
		m_pCurParser = spw;
		return CSentence::NMEA_SPW;
	}
	else if(( FORMAT_COMP(pszSentence, "ALF") == 0) && (g_pDocMgr->GetEnBAMAlert() == TRUE)) { // IEC62923-2(BAM) applied
		alf->SetSentence(pszSentence);
		m_pCurParser = alf;
		return CSentence::NMEA_ALF;
	}

	m_pCurParser = NULL;
	return CSentence::NMEA_UNKNOWN;
}

void CNmea::Parse()
{
	if( m_pCurParser ) m_pCurParser->Parse();
}

void CNmea::GetPlainText(char *pszPlainText)
{
	if( m_pCurParser ) m_pCurParser->GetPlainText(pszPlainText);
}
