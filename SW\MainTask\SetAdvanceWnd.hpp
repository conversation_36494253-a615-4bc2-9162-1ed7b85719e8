#ifndef __SET_ADVANCED_WND_HPP__
#define __SET_ADVANCED_WND_HPP__

#include "Wnd.hpp"
#include "CheckCtrl.hpp"
#ifdef EN_61993_ED3
#include "UniComboCtrl.hpp"
#endif	// End of (EN_61993_ED3)
#include "ComboCtrl.hpp"
#include "EditCtrl.hpp"
#include "Const.h"

class CSetAdvancedWnd : public CWnd {
	private:
		enum {
			CONTROL_START = 0,
			FOCUS_LAT,
			FOCUS_LON,
			FOCUS_LOCAL_TIME,
		    FOCUS_SHOW_TEST_SART,
#ifdef EN_61993_ED3
			FOCUS_PILOT_PORT,
			FOCUS_EN_EXT_EPFS,
			FOCUS_EN_HEADING,
			FOCUS_EN_ROT,
			FOCUS_EN_RECV_MODE,
			FOCUS_EN_ALR14,
#endif	// End of (EN_61993_ED3)
			CONTROL_COUNT				
		};

		BOOL	m_bSupervisorMode;
		int		m_nLangMode;

		CEditCtrl	*m_pLatEdit;
		CEditCtrl	*m_pLonEdit;
		CEditCtrl	*m_pLocalTime;
		CCheckCtrl	*m_pShowTestSART;
#ifdef EN_61993_ED3
		CUniComboCtrl *m_pPilotPort;
		CCheckCtrl	*m_pEnExtEPFS;
		CCheckCtrl	*m_pEnExtHeading;
		CCheckCtrl	*m_pEnExtROT;
		CCheckCtrl	*m_pEnSilentMode;
		CCheckCtrl	*m_pEnAlr14;
#endif	// End of (EN_61993_ED3)

	public:
		CSetAdvancedWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
		void OnActivate();
		void OnKeyEvent(int nKey, DWORD nFlags);
		void OnCursorEvent(int nState);
		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		void InitAdvancedSetting();
		BOOL CheckLat(BYTE *pszData);
		BOOL CheckLon(BYTE *pszData);
		LONG StrLatToLongLat(BYTE *pszLat);
		LONG StrLonToLongLon(BYTE *pszLon);
		void LongLatToStrLat(LONG lLat, BYTE *pszLat);
		void LongLonToStrLon(LONG lLon, BYTE *pszLon);
};

#endif	// End of __SET_ADVANCED_WND_HPP__


