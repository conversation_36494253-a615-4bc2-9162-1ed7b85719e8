#include "GFC.h"

/*
** Author: <PERSON>
** Internet: <EMAIL>
**
** You can use it any way you like as long as you don't try to sell it.
**
** Any attempt to sell GFC in source code form must have the permission
** of the original author. You can produce commercial executables with
** GFC but you can't sell GFC.
**
** Copyright, 1998, <PERSON>
**
** $Workfile: CEarthCoordinate.cpp $
** $Revision: 4 $
** $Modtime: 2/07/98 10:34a $
*/

CEarthCoordinate::CEarthCoordinate( void )
{
   m_X_CoordinateInMeters = 0.0;
   m_Y_CoordinateInMeters = 0.0;
   m_Z_CoordinateInMeters = 0.0;
}

CEarthCoordinate::CEarthCoordinate( const CEarthCoordinate& source )
{
   Copy( source );
}

CEarthCoordinate::~CEarthCoordinate( void )
{
   m_X_CoordinateInMeters = 0.0;
   m_Y_CoordinateInMeters = 0.0;
   m_Z_CoordinateInMeters = 0.0;
}

void CEarthCoordinate::Copy( const CEarthCoordinate& source )
{
   m_X_CoordinateInMeters = source.m_X_CoordinateInMeters;
   m_Y_CoordinateInMeters = source.m_Y_CoordinateInMeters;
   m_Z_CoordinateInMeters = source.m_Z_CoordinateInMeters;
}

void CEarthCoordinate::Get( double& x_coordinate, double& y_coordinate, double& z_coordinate ) const
{
   x_coordinate = m_X_CoordinateInMeters;
   y_coordinate = m_Y_CoordinateInMeters;
   z_coordinate = m_Z_CoordinateInMeters;
}

double CEarthCoordinate::GetXCoordinateInMeters( void ) const
{
   return( m_X_CoordinateInMeters );
}

double CEarthCoordinate::GetYCoordinateInMeters( void ) const
{
   return( m_Y_CoordinateInMeters );
}

double CEarthCoordinate::GetZCoordinateInMeters( void ) const
{
   return( m_Z_CoordinateInMeters );
}

void CEarthCoordinate::Set( double x_coordinate, double y_coordinate, double z_coordinate )
{
   m_X_CoordinateInMeters = x_coordinate;
   m_Y_CoordinateInMeters = y_coordinate;
   m_Z_CoordinateInMeters = z_coordinate;
}

void CEarthCoordinate::SetXCoordinateInMeters( double x_coordinate )
{
   m_X_CoordinateInMeters = x_coordinate;
}

void CEarthCoordinate::SetYCoordinateInMeters( double y_coordinate )
{
   m_Y_CoordinateInMeters = y_coordinate;
}

void CEarthCoordinate::SetZCoordinateInMeters( double z_coordinate )
{
   m_Z_CoordinateInMeters = z_coordinate;
}

CEarthCoordinate& CEarthCoordinate::operator=( const CEarthCoordinate& source )
{
   Copy( source );
   return( *this );
}
