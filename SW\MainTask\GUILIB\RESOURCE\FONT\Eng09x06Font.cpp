/*...........................................................................*/
/*.                  File Name : Eng09x06Font.cpp                           .*/
/*.                                                                         .*/
/*.                       Date : 2008.08.13                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA HWORD Eng09x06Font_Offset_Table[257] = {
0x0000,0x0006,0x000c,0x0012,0x0018,0x001e,0x0024,0x002a,0x0030,0x0036,0x003c,0x0042,0x0048,0x004e,0x0054,0x005a,
0x0060,0x0066,0x006c,0x0072,0x0078,0x007e,0x0084,0x008a,0x0090,0x0096,0x009c,0x00a2,0x00a8,0x00ae,0x00b4,0x00ba,
0x00c0,0x00c6,0x00cc,0x00d2,0x00d8,0x00de,0x00e4,0x00ea,0x00f0,0x00f6,0x00fc,0x0102,0x0108,0x010e,0x0114,0x011a,
0x0120,0x0126,0x012c,0x0132,0x0138,0x013e,0x0144,0x014a,0x0150,0x0156,0x015c,0x0162,0x0168,0x016e,0x0174,0x017a,
0x0180,0x0186,0x018c,0x0192,0x0198,0x019e,0x01a4,0x01aa,0x01b0,0x01b6,0x01bc,0x01c2,0x01c8,0x01ce,0x01d4,0x01da,
0x01e0,0x01e6,0x01ec,0x01f2,0x01f8,0x01fe,0x0204,0x020a,0x0210,0x0216,0x021c,0x0222,0x0228,0x022e,0x0234,0x023a,
0x0240,0x0246,0x024c,0x0252,0x0258,0x025e,0x0264,0x026a,0x0270,0x0276,0x027c,0x0282,0x0288,0x028e,0x0294,0x029a,
0x02a0,0x02a6,0x02ac,0x02b2,0x02b8,0x02be,0x02c4,0x02ca,0x02d0,0x02d6,0x02dc,0x02e2,0x02e8,0x02ee,0x02f4,0x02fa,
0x0300,0x0306,0x030c,0x0312,0x0318,0x031e,0x0324,0x032a,0x0330,0x0336,0x033c,0x0342,0x0348,0x034e,0x0354,0x035a,
0x0360,0x0366,0x036c,0x0372,0x0378,0x037e,0x0384,0x038a,0x0390,0x0396,0x039c,0x03a2,0x03a8,0x03ae,0x03b4,0x03ba,
0x03c0,0x03c6,0x03cc,0x03d2,0x03d8,0x03de,0x03e4,0x03ea,0x03f0,0x03f6,0x03fc,0x0402,0x0408,0x040e,0x0414,0x041a,
0x0420,0x0426,0x042c,0x0432,0x0438,0x043e,0x0444,0x044a,0x0450,0x0456,0x045c,0x0462,0x0468,0x046e,0x0474,0x047a,
0x0480,0x0486,0x048c,0x0492,0x0498,0x049e,0x04a4,0x04aa,0x04b0,0x04b6,0x04bc,0x04c2,0x04c8,0x04ce,0x04d4,0x04da,
0x04e0,0x04e6,0x04ec,0x04f2,0x04f8,0x04fe,0x0504,0x050a,0x0510,0x0516,0x051c,0x0522,0x0528,0x052e,0x0534,0x053a,
0x0540,0x0546,0x054c,0x0552,0x0558,0x055e,0x0564,0x056a,0x0570,0x0576,0x057c,0x0582,0x0588,0x058e,0x0594,0x059a,
0x05a0,0x05a6,0x05ac,0x05b2,0x05b8,0x05be,0x05c4,0x05ca,0x05d0,0x05d6,0x05dc,0x05e2,0x05e8,0x05ee,0x05f4,0x05fa,
0x0600 };

ROMDATA PEGUBYTE Eng09x06Font_Data_Table[1728] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0xc0, 0x40, 0x10, 0x00, 0x08, 
0x20, 0x02, 0x08, 0x00, 0x82, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x05, 0x00, 0x40, 0x00, 0x20, 0x23, 0x00, 0x20, 0x21, 0x00, 0x00, 0xa2, 0x02, 0x10, 
0xa0, 0x00, 0x00, 0x80, 0x84, 0x00, 0x20, 0x00, 0x20, 0x21, 0x00, 0x00, 0x00, 0x00, 0x40, 0x13, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 

0x00, 0xc4, 0x10, 0x41, 0x04, 0x00, 0x00, 0x41, 0x28, 0x21, 0xc1, 0xcc, 0x60, 0x30, 0x3f, 0x08, 
0x20, 0x02, 0x08, 0x00, 0x82, 0x08, 0x01, 0x3f, 0x01, 0xc5, 0x0a, 0x11, 0x83, 0x04, 0x08, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x38, 0x43, 0x9f, 0x09, 0xf1, 0x9f, 0x38, 0xe0, 0x00, 0x08, 0x02, 0x0e, 
0x38, 0xe7, 0x8e, 0x71, 0xf7, 0xce, 0x44, 0xe1, 0xd1, 0x41, 0x14, 0x4e, 0x78, 0xe7, 0x8f, 0x7d, 
0x14, 0x51, 0x45, 0x17, 0xce, 0x00, 0xe1, 0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x80, 0x40, 0x40, 
0x88, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x40, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x01, 0x11, 0x0e, 0x01, 0xe0, 
0x00, 0x00, 0x00, 0x3f, 0x00, 0x03, 0x1e, 0x18, 0xa7, 0xc0, 0x00, 0x43, 0x80, 0x90, 0x00, 0x00, 
0x30, 0x62, 0x8e, 0x28, 0xa0, 0x0e, 0x10, 0x44, 0x8a, 0x10, 0x42, 0x8a, 0x01, 0x41, 0x04, 0x29, 
0x42, 0x80, 0x04, 0x41, 0x0a, 0x28, 0x47, 0x0c, 0x10, 0x42, 0x8a, 0x28, 0x47, 0x80, 0x20, 0x24, 
0x8a, 0x20, 0x21, 0x00, 0x60, 0xa2, 0x02, 0x30, 0xa0, 0x00, 0x01, 0x00, 0x8c, 0x00, 0x44, 0x0a, 

0x00, 0x2a, 0x28, 0xa2, 0x8a, 0x04, 0x10, 0xe1, 0x16, 0x71, 0xc3, 0xcc, 0x70, 0x70, 0x01, 0x08, 
0x20, 0x02, 0x08, 0x00, 0x82, 0x08, 0x03, 0xbf, 0x01, 0x45, 0x0a, 0x3d, 0x94, 0x84, 0x10, 0x41, 
0x04, 0x00, 0x00, 0x01, 0x44, 0xc4, 0x42, 0x19, 0x02, 0x01, 0x45, 0x13, 0x0c, 0x10, 0x01, 0x11, 
0x45, 0x14, 0x51, 0x49, 0x04, 0x11, 0x44, 0x40, 0x92, 0x41, 0xb4, 0x51, 0x45, 0x14, 0x50, 0x11, 
0x14, 0x51, 0x45, 0x10, 0x48, 0x00, 0x22, 0x80, 0x00, 0x04, 0x00, 0x04, 0x01, 0x4d, 0x40, 0x00, 
0x08, 0x10, 0x00, 0x00, 0x58, 0xd0, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0xa3, 0x84, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x4e, 0x45, 0x11, 0x0a, 0x4a, 0x13, 
0x80, 0x00, 0x07, 0x00, 0x38, 0x04, 0x82, 0x38, 0xae, 0x80, 0x00, 0xc2, 0x80, 0xa2, 0x4e, 0x80, 
0x10, 0x41, 0x04, 0x10, 0x43, 0xd1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 
0x00, 0x11, 0x7c, 0x00, 0x00, 0x01, 0x12, 0x12, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x40, 0x00, 0x00, 
0x00, 0x10, 0x42, 0x8a, 0x39, 0x41, 0x04, 0x49, 0x42, 0x80, 0x04, 0x81, 0x12, 0x48, 0x04, 0x00, 

0x00, 0x25, 0x97, 0x5d, 0x64, 0x06, 0x31, 0xf1, 0x09, 0xf9, 0xc7, 0xff, 0x78, 0xf0, 0x01, 0x08, 
0x20, 0x02, 0x08, 0x00, 0x82, 0x08, 0x03, 0x9f, 0x01, 0xc5, 0x1f, 0x50, 0x25, 0x08, 0x20, 0x25, 
0x44, 0x00, 0x00, 0x02, 0x44, 0x40, 0x44, 0x29, 0xe4, 0x02, 0x45, 0x13, 0x0c, 0x21, 0xf0, 0x81, 
0x05, 0x14, 0x50, 0x45, 0x04, 0x10, 0x44, 0x40, 0x94, 0x41, 0x56, 0x51, 0x45, 0x14, 0x50, 0x11, 
0x14, 0x51, 0x29, 0x10, 0x88, 0x00, 0x24, 0x40, 0x00, 0xe5, 0x8e, 0x34, 0xe1, 0x13, 0x58, 0xc1, 
0x89, 0x11, 0xa5, 0x8e, 0x65, 0x35, 0x8f, 0x71, 0x24, 0x51, 0x45, 0x17, 0xc4, 0x39, 0x17, 0xc4, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x8b, 0x38, 0xa1, 0x0c, 0x02, 0xd1, 
0x8a, 0x00, 0x0f, 0xc0, 0x28, 0x41, 0x0e, 0x40, 0xae, 0x80, 0x00, 0x43, 0x94, 0xa2, 0x44, 0x84, 
0x28, 0xa2, 0x8a, 0x28, 0x45, 0x10, 0x79, 0xe7, 0x9e, 0x38, 0xe3, 0x8e, 0x25, 0x23, 0x8e, 0x38, 
0xe3, 0x8a, 0xcd, 0x14, 0x51, 0x45, 0x13, 0xd2, 0x38, 0xe3, 0x8e, 0x38, 0xe3, 0x4c, 0x38, 0xe3, 
0x8e, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x04, 0x3c, 0x00, 0x00, 0x01, 0x27, 0x12, 

0x00, 0xc2, 0x44, 0x08, 0x94, 0x5f, 0x7c, 0x41, 0x08, 0x71, 0xcf, 0xff, 0x7d, 0xf0, 0x00, 0x09, 
0xe7, 0xf3, 0x7f, 0xfc, 0xff, 0x78, 0x3f, 0xdf, 0x00, 0x00, 0x0a, 0x38, 0x42, 0x00, 0x20, 0x23, 
0x9f, 0x01, 0xf0, 0x04, 0x44, 0x40, 0x82, 0x48, 0x17, 0x84, 0x38, 0xf0, 0x00, 0x40, 0x00, 0x42, 
0x35, 0x17, 0x90, 0x45, 0xe7, 0x93, 0x7c, 0x40, 0x98, 0x41, 0x55, 0x51, 0x79, 0x17, 0x8e, 0x11, 
0x14, 0x55, 0x10, 0xa1, 0x08, 0xfc, 0x20, 0x00, 0x00, 0x16, 0x51, 0x4d, 0x13, 0x93, 0x64, 0x40, 
0x8a, 0x11, 0x56, 0x51, 0x65, 0x36, 0x50, 0x21, 0x24, 0x51, 0x29, 0x10, 0x88, 0x3a, 0x0f, 0xff, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x08, 0x29, 0xf0, 0x0a, 0x02, 0x93, 
0x94, 0x78, 0x0d, 0x40, 0x39, 0xf7, 0x82, 0x00, 0xe6, 0x8c, 0x10, 0x40, 0x0a, 0x4a, 0x8b, 0x00, 
0x28, 0xa2, 0x8a, 0x28, 0xa5, 0x10, 0x41, 0x04, 0x10, 0x10, 0x41, 0x04, 0x25, 0xa4, 0x51, 0x45, 
0x14, 0x44, 0x95, 0x14, 0x51, 0x44, 0xa2, 0x54, 0x04, 0x10, 0x41, 0x04, 0x1f, 0xd2, 0x45, 0x14, 
0x51, 0x10, 0x41, 0x04, 0x39, 0xc3, 0x0c, 0x30, 0xc3, 0x00, 0x4d, 0x24, 0x92, 0x49, 0x24, 0x92, 

0x00, 0x04, 0x07, 0x08, 0x96, 0xc6, 0x30, 0x47, 0xc6, 0x73, 0xe7, 0xcc, 0x78, 0xf0, 0x00, 0x08, 
0x20, 0x00, 0x00, 0x20, 0x82, 0x00, 0x27, 0xce, 0x00, 0x00, 0x1f, 0x04, 0x85, 0x40, 0x20, 0x25, 
0x44, 0x30, 0x00, 0x08, 0x44, 0x41, 0x01, 0x7c, 0x14, 0x44, 0x44, 0x13, 0x0c, 0x21, 0xf0, 0x84, 
0x55, 0xf4, 0x50, 0x45, 0x04, 0x11, 0x44, 0x40, 0x94, 0x41, 0x14, 0xd1, 0x41, 0x55, 0x01, 0x11, 
0x14, 0x55, 0x28, 0x42, 0x08, 0x00, 0x20, 0x00, 0x00, 0xf4, 0x50, 0x45, 0xf1, 0x0d, 0x44, 0x40, 
0x8c, 0x11, 0x54, 0x51, 0x58, 0xd4, 0x1e, 0x21, 0x24, 0x55, 0x11, 0x31, 0x04, 0x39, 0x17, 0xc4, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x43, 0x9c, 0x38, 0x40, 0x0a, 0x02, 0xd0, 
0x14, 0x0b, 0xef, 0x40, 0x00, 0x40, 0x1e, 0x01, 0x92, 0x8c, 0x20, 0x03, 0x8a, 0x59, 0x77, 0x44, 
0x7d, 0xf7, 0xdf, 0x7c, 0xad, 0xcf, 0x79, 0xe7, 0x9e, 0x10, 0x41, 0x04, 0x75, 0xa4, 0x51, 0x45, 
0x14, 0x4a, 0xa5, 0x14, 0x51, 0x44, 0xe2, 0x53, 0x3c, 0xf3, 0xcf, 0x3c, 0xfb, 0x10, 0x7d, 0xf7, 
0xdf, 0x10, 0x41, 0x04, 0x45, 0x24, 0x92, 0x49, 0x24, 0x9f, 0x75, 0x24, 0x92, 0x48, 0xe4, 0x8e, 

0x00, 0x04, 0x04, 0x08, 0xe5, 0x44, 0x10, 0x43, 0x81, 0x71, 0xc3, 0xcc, 0x70, 0x70, 0x00, 0x08, 
0x20, 0x00, 0x00, 0x20, 0x82, 0x00, 0x2f, 0xce, 0x00, 0x00, 0x0a, 0x79, 0x34, 0x80, 0x10, 0x41, 
0x04, 0x10, 0x03, 0x10, 0x44, 0x42, 0x11, 0x09, 0x14, 0x44, 0x44, 0x23, 0x04, 0x10, 0x01, 0x00, 
0x55, 0x14, 0x51, 0x49, 0x04, 0x11, 0x44, 0x44, 0x92, 0x41, 0x14, 0x51, 0x41, 0x24, 0x81, 0x11, 
0x12, 0x95, 0x44, 0x44, 0x08, 0x00, 0x20, 0x00, 0x01, 0x14, 0x51, 0x45, 0x01, 0x01, 0x44, 0x44, 
0x8a, 0x11, 0x54, 0x51, 0x40, 0x14, 0x01, 0x25, 0x22, 0x95, 0x28, 0xd2, 0x04, 0x00, 0xa3, 0x84, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe4, 0x08, 0x45, 0xf1, 0x06, 0x02, 0x10, 
0x0a, 0x08, 0x0d, 0xc0, 0x00, 0x40, 0x00, 0x01, 0x02, 0x80, 0x18, 0x00, 0x14, 0xbd, 0x22, 0xc8, 
0x45, 0x14, 0x51, 0x45, 0xff, 0x04, 0x41, 0x04, 0x10, 0x10, 0x41, 0x04, 0x25, 0x64, 0x51, 0x45, 
0x14, 0x51, 0xcd, 0x14, 0x51, 0x44, 0x43, 0xd1, 0x45, 0x14, 0x51, 0x45, 0x1b, 0x4e, 0x41, 0x04, 
0x10, 0x10, 0x41, 0x04, 0x45, 0x24, 0x92, 0x49, 0x24, 0x80, 0x79, 0x24, 0x92, 0x48, 0x47, 0x04, 

0x00, 0x02, 0x44, 0x08, 0x95, 0x40, 0x00, 0x41, 0x09, 0x70, 0x81, 0xc0, 0x60, 0x30, 0x00, 0x08, 
0x20, 0x00, 0x00, 0x20, 0x82, 0x00, 0x2f, 0xc4, 0x00, 0x00, 0x0a, 0x10, 0x33, 0x40, 0x08, 0x80, 
0x00, 0x20, 0x03, 0x00, 0x38, 0xe7, 0xce, 0x08, 0xe3, 0x84, 0x38, 0xc0, 0x08, 0x08, 0x02, 0x04, 
0x39, 0x17, 0x8e, 0x71, 0xf4, 0x0f, 0x44, 0xe3, 0x11, 0x7d, 0x14, 0x4e, 0x40, 0xd4, 0x5e, 0x10, 
0xe1, 0x0a, 0x44, 0x47, 0xce, 0x00, 0xe0, 0x1f, 0x00, 0xf7, 0x8e, 0x3c, 0xe1, 0x0e, 0x44, 0xe3, 
0x09, 0x39, 0x54, 0x4e, 0x40, 0x14, 0x1e, 0x18, 0xd1, 0x0a, 0x44, 0x17, 0xc2, 0x00, 0x40, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x1f, 0x00, 0x41, 0x0a, 0x01, 0xe0, 
0x00, 0x00, 0x07, 0x80, 0x01, 0xf0, 0x00, 0x00, 0x02, 0x80, 0x38, 0x00, 0x00, 0x0a, 0x75, 0xd2, 
0x45, 0x14, 0x51, 0x45, 0x19, 0xc2, 0x79, 0xe7, 0x9e, 0x38, 0xe3, 0x8e, 0x39, 0x63, 0x8e, 0x38, 
0xe3, 0x80, 0xf8, 0xe3, 0x8e, 0x38, 0x42, 0x1f, 0x3c, 0xf3, 0xcf, 0x3c, 0xf7, 0x84, 0x38, 0xe3, 
0x8e, 0x10, 0x41, 0x04, 0x39, 0x23, 0x0c, 0x30, 0xc3, 0x04, 0x40, 0xe3, 0x8e, 0x39, 0x84, 0x18, 

0x00, 0x01, 0x84, 0x08, 0x94, 0x40, 0x00, 0x00, 0x06, 0x00, 0x00, 0xc0, 0x40, 0x10, 0x00, 0x08, 
0x20, 0x00, 0x00, 0x00, 0x82, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 
0x00, 0x00, 0x00, 0x01, 0x10, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 

};

xFONTYY Eng09x06Font = {0x01, 9, 0, 9, 0, 0,  9, 192, 0x0000, 0x00ff,
(PEGUSHORT *)Eng09x06Font_Offset_Table, NULL,
(PEGUBYTE *)Eng09x06Font_Data_Table};

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

