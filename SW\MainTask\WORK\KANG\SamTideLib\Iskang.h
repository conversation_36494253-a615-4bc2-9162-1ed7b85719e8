FILE* OpenCfgOut(void);
void CloseCfgOut();
int IskangCfgPrint(const char *fstring, ...);
int IskangCfgIntOut(const char *pStrVarName, int nData);
int IskangCfgFloatOut(const char *pStrVarName, float fData);
int IskangCfgStrOut(const char *pStrVarName, const char *pData);

FILE* OpenStListOut(void);
void CloseStListOut();
int IskangStListPrint(const char *fstring, ...);

FILE* OpenIdxOut(void);
void CloseIdxOut();
int IskangIdxPrint(const char *fstring, ...);
//int IskangIdxIntOut(const char *pStrVarName, int nData);
//int IskangIdxFloatOut(const char *pStrVarName, float fData);
//int IskangIdxStrOut(const char *pStrVarName, const char *pData);

FILE* OpenTcdIndexOut(void);
void CloseTcdIndexOut();
int IskangTcdIndexPrint(const char *fstring, ...);
int IskangTcdIndexIntOut(const char *pStrVarName, int nData);
int IskangTcdIndexFloatOut(const char *pStrVarName, float fData);
int IskangTcdIndexStrOut(const char *pStrVarName, const char *pData);
int IskangTcdIndexArrayStr(const char *pData);

FILE* OpenTcdRecordOut(void);
void CloseTcdRecordOut();
int IskangTcdRecordPrint(const char *fstring, ...);

FILE* OpenMscOut(void);
void CloseMscOut();
int IskangMscPrint(const char *fstring, ...);
int IskangMscArrayStr(const char *pData);
