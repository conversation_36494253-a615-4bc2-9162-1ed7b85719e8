/*...........................................................................*/
/*.                  File Name : SYSDPC.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSDPC_H__
#define  __SYSDPC_H__

//=============================================================================
#define  DPC_03_5_VCLK_FREQUENCY      24500000   //  3.5"  : 24.50 MHz

#define  DPC_04_3_VCLK_FREQUENCY  9200000   //  4.3"  :  9.20 MHz
#define  DPC_05_0_VCLK_FREQUENCY      10211538   //  5.0"  : 10.21 MHz (12 = 13 - 1)

#if  CPU_PLL0_FREQUENCY == 533000000
     #define  DPC_05_6_VCLK_FREQUENCY      19035714   //  5.6"  : 25.00 MHz  (27 = 28 - 1)  at PLL0 == 533 MHz
#endif

#if  CPU_PLL0_FREQUENCY == 528000000
     #define  DPC_05_6_VCLK_FREQUENCY      18857142  //  5.6"  : 25.00 MHz (27 = 28 - 1)   at PLL0 == 528 MHz
#endif

#if  CPU_PLL0_FREQUENCY == 700000000
     #define  DPC_05_6_VCLK_FREQUENCY      25000000  //  5.6"  : 25.00 MHz
#endif

#if  CPU_PLL0_FREQUENCY == 600000000
     #define  DPC_05_6_VCLK_FREQUENCY      25000000  //  5.6"  : 25.00 MHz
#endif

#define  DPC_06_5_VCLK_FREQUENCY      25000000   //  6.5"  : 25.00 MHz

#define  DPC_07_0_VCLK_FREQUENCY      33300000   //  7.0"  : 33.30 MHz
#define  DPC_08_4_VCLK_FREQUENCY      39000000   //  8.4"  : 39.00 MHz
#define  DPC_10_4_VCLK_FREQUENCY      25000000   // 10.4"  : 25.00 MHz
#define  DPC_12_1_VCLK_FREQUENCY      38500000   // 12.1"  : 38.50 MHz 37.0 38.5 40.0
#define  DPC_12_X_VCLK_FREQUENCY      38000000   // 12.1"  : 38.50 MHz 37.0 38.5 40.0
#define  DPC_15_1_VCLK_FREQUENCY      64000000   // 15.1"  : 75.00  67 Hz
//#define  DPC_N500_VCLK_FREQUENCY      30000000   //  N500  : 30.00 MHz
#define  DPC_N500_VCLK_FREQUENCY      27272727   //  N500  : 27.27 MHz

#define  DPC_800_VCLK_FREQUENCY       25000000   //   800  : 25.00 MHz
#define  DPC_5100_VCLK_FREQUENCY      25000000   //  5100  : 25.00 MHz
#define  DPC_650_VCLK_FREQUENCY       25000000   //   650  : 25.00 MHz

#define  LCD_5100_VCLK_FREQUENCY      25000000   //  5100  : 27.00 MHz
#define  LCD_12_1_VCLK_FREQUENCY      38000000   // 12.1"  : 38.50 MHz 37.0 38.5 40.0
//#define  LCD_15_1_VCLK_FREQUENCY      60000000   // 15.1"  : 75.00
#define  LCD_15_1_VCLK_FREQUENCY      56000000   // 15.1"  : 75.00

//-----------------------------------------------------------------------------
#define  DPC_03_5_HSYNC_SWIDTH              30
#define  DPC_04_3_HSYNC_SWIDTH              41
#define  DPC_05_0_HSYNC_SWIDTH              30
#define  DPC_05_6_HSYNC_SWIDTH              10
#define  DPC_06_5_HSYNC_SWIDTH              54
#define  DPC_07_0_HSYNC_SWIDTH              64
#define  DPC_08_4_HSYNC_SWIDTH              10     // 256
#define  DPC_10_4_HSYNC_SWIDTH              41
#define  DPC_12_1_HSYNC_SWIDTH              64
#define  DPC_12_X_HSYNC_SWIDTH             140
#define  DPC_15_1_HSYNC_SWIDTH             120    // Samsung 1024x768 TFT
#define  DPC_N500_HSYNC_SWIDTH              48

#define  DPC_650_HSYNC_SWIDTH               54
#define  DPC_800_HSYNC_SWIDTH               54
#define  DPC_5100_HSYNC_SWIDTH              54

#define  LCD_5100_HSYNC_SWIDTH              96  // 16 // 54
#define  LCD_12_1_HSYNC_SWIDTH             120  //  64
#define  LCD_15_1_HSYNC_SWIDTH             120  // 64

#define  DPC_03_5_HSYNC_FRONT_PORCH          8
#define  DPC_04_3_HSYNC_FRONT_PORCH          2 //8
#define  DPC_05_0_HSYNC_FRONT_PORCH          8
#define  DPC_05_6_HSYNC_FRONT_PORCH         16
#define  DPC_06_5_HSYNC_FRONT_PORCH          1
#define  DPC_07_0_HSYNC_FRONT_PORCH        120
#define  DPC_08_4_HSYNC_FRONT_PORCH        123     // 8
#define  DPC_10_4_HSYNC_FRONT_PORCH          8
#define  DPC_12_1_HSYNC_FRONT_PORCH          8
#define  DPC_12_X_HSYNC_FRONT_PORCH         56
#define  DPC_15_1_HSYNC_FRONT_PORCH        100    // Samsung 1024x768 TFT
#define  DPC_N500_HSYNC_FRONT_PORCH         40

#define  DPC_650_HSYNC_FRONT_PORCH           1
#define  DPC_800_HSYNC_FRONT_PORCH           1
#define  DPC_5100_HSYNC_FRONT_PORCH          1

#define  LCD_5100_HSYNC_FRONT_PORCH         13 // 45 //  1
#define  LCD_12_1_HSYNC_FRONT_PORCH         56 // 45 //  8
#define  LCD_15_1_HSYNC_FRONT_PORCH        100 //  23 // 24

#define  DPC_03_5_HSYNC_BACK_PORCH          45
#define  DPC_04_3_HSYNC_BACK_PORCH           2 // 4
#define  DPC_05_0_HSYNC_BACK_PORCH          45
#define  DPC_05_6_HSYNC_BACK_PORCH         134
#define  DPC_06_5_HSYNC_BACK_PORCH         104
#define  DPC_07_0_HSYNC_BACK_PORCH          56
#define  DPC_08_4_HSYNC_BACK_PORCH         123     // 45
#define  DPC_10_4_HSYNC_BACK_PORCH          45
#define  DPC_12_1_HSYNC_BACK_PORCH          45
#define  DPC_12_X_HSYNC_BACK_PORCH          64
#define  DPC_15_1_HSYNC_BACK_PORCH         140    // Samsung 1024x768 TFT
#define  DPC_N500_HSYNC_BACK_PORCH          88

#define  DPC_650_HSYNC_BACK_PORCH          104
#define  DPC_800_HSYNC_BACK_PORCH          104
#define  DPC_5100_HSYNC_BACK_PORCH         104

#define  LCD_5100_HSYNC_BACK_PORCH          51 // 45 //90 //104
#define  LCD_12_1_HSYNC_BACK_PORCH          64 // 45
#define  LCD_15_1_HSYNC_BACK_PORCH         100 // 136 //45

#define  DPC_03_5_HSYNC_ACTIVE_MODE          0
#define  DPC_04_3_HSYNC_ACTIVE_MODE          0
#define  DPC_05_0_HSYNC_ACTIVE_MODE          0
#define  DPC_05_6_HSYNC_ACTIVE_MODE          0
#define  DPC_06_5_HSYNC_ACTIVE_MODE          0
#define  DPC_07_0_HSYNC_ACTIVE_MODE          0
#define  DPC_08_4_HSYNC_ACTIVE_MODE          0
#define  DPC_10_4_HSYNC_ACTIVE_MODE          0
#define  DPC_12_1_HSYNC_ACTIVE_MODE          0
#define  DPC_12_X_HSYNC_ACTIVE_MODE          0
#define  DPC_15_1_HSYNC_ACTIVE_MODE          0
#define  DPC_N500_HSYNC_ACTIVE_MODE          0

#define  DPC_650_HSYNC_ACTIVE_MODE           0
#define  DPC_800_HSYNC_ACTIVE_MODE           0
#define  DPC_5100_HSYNC_ACTIVE_MODE          0

#define  LCD_5100_HSYNC_ACTIVE_MODE          0
#define  LCD_12_1_HSYNC_ACTIVE_MODE          0
#define  LCD_15_1_HSYNC_ACTIVE_MODE          0

#define  DPC_03_5_VSYNC_SWIDTH              10
#define  DPC_04_3_VSYNC_SWIDTH              10
#define  DPC_05_0_VSYNC_SWIDTH              10
#define  DPC_05_6_VSYNC_SWIDTH               1
#define  DPC_06_5_VSYNC_SWIDTH              18
#define  DPC_07_0_VSYNC_SWIDTH               2
#define  DPC_08_4_VSYNC_SWIDTH              10    // 10
#define  DPC_10_4_VSYNC_SWIDTH              10
#define  DPC_12_1_VSYNC_SWIDTH              10
#define  DPC_12_X_VSYNC_SWIDTH               6
#define  DPC_15_1_VSYNC_SWIDTH              24    // Samsung 1024x768 TFT
#define  DPC_N500_VSYNC_SWIDTH               3

#define  DPC_650_VSYNC_SWIDTH               18
#define  DPC_800_VSYNC_SWIDTH               18
#define  DPC_5100_VSYNC_SWIDTH              18

#define  LCD_5100_VSYNC_SWIDTH               2 // 18
#define  LCD_12_1_VSYNC_SWIDTH               6 // 10
#define  LCD_15_1_VSYNC_SWIDTH              24 // 10

#define  DPC_03_5_VSYNC_FRONT_PORCH          4
#define  DPC_04_3_VSYNC_FRONT_PORCH          2 // 4
#define  DPC_05_0_VSYNC_FRONT_PORCH          4
#define  DPC_05_6_VSYNC_FRONT_PORCH         32
#define  DPC_06_5_VSYNC_FRONT_PORCH         16
#define  DPC_07_0_VSYNC_FRONT_PORCH          9
#define  DPC_08_4_VSYNC_FRONT_PORCH          9    // 4
#define  DPC_10_4_VSYNC_FRONT_PORCH          4
#define  DPC_12_1_VSYNC_FRONT_PORCH          4
#define  DPC_12_X_VSYNC_FRONT_PORCH         37
#define  DPC_15_1_VSYNC_FRONT_PORCH         10    // Samsung 1024x768 TFT
#define  DPC_N500_VSYNC_FRONT_PORCH         13

#define  DPC_650_VSYNC_FRONT_PORCH          16
#define  DPC_800_VSYNC_FRONT_PORCH          16
#define  DPC_5100_VSYNC_FRONT_PORCH         16

#define  LCD_5100_VSYNC_FRONT_PORCH         16  // 24
#define  LCD_12_1_VSYNC_FRONT_PORCH         14  //  4
#define  LCD_15_1_VSYNC_FRONT_PORCH         10  // 4

#define  DPC_03_5_VSYNC_BACK_PORCH           1
#define  DPC_04_3_VSYNC_BACK_PORCH           1 // 1
#define  DPC_05_0_VSYNC_BACK_PORCH           1
#define  DPC_05_6_VSYNC_BACK_PORCH          11
#define  DPC_06_5_VSYNC_BACK_PORCH          15
#define  DPC_07_0_VSYNC_BACK_PORCH          34
#define  DPC_08_4_VSYNC_BACK_PORCH           9   // 1
#define  DPC_10_4_VSYNC_BACK_PORCH           1
#define  DPC_12_1_VSYNC_BACK_PORCH           1
#define  DPC_12_X_VSYNC_BACK_PORCH          23
#define  DPC_15_1_VSYNC_BACK_PORCH          30    // Samsung 1024x768 TFT
#define  DPC_N500_VSYNC_BACK_PORCH          32

#define  DPC_650_VSYNC_BACK_PORCH           15
#define  DPC_800_VSYNC_BACK_PORCH           15
#define  DPC_5100_VSYNC_BACK_PORCH          15

#define  LCD_5100_VSYNC_BACK_PORCH           4  //  9
#define  LCD_12_1_VSYNC_BACK_PORCH          30  //  1
#define  LCD_15_1_VSYNC_BACK_PORCH          20  //  1

#define  DPC_03_5_VSYNC_ACTIVE_MODE          0
#define  DPC_04_3_VSYNC_ACTIVE_MODE          0
#define  DPC_05_0_VSYNC_ACTIVE_MODE          0
#define  DPC_05_6_VSYNC_ACTIVE_MODE          0
#define  DPC_06_5_VSYNC_ACTIVE_MODE          0
#define  DPC_07_0_VSYNC_ACTIVE_MODE          0
#define  DPC_08_4_VSYNC_ACTIVE_MODE          0
#define  DPC_10_4_VSYNC_ACTIVE_MODE          0
#define  DPC_12_1_VSYNC_ACTIVE_MODE          0
#define  DPC_12_X_VSYNC_ACTIVE_MODE          0
#define  DPC_15_1_VSYNC_ACTIVE_MODE          0
#define  DPC_N500_VSYNC_ACTIVE_MODE          0

#define  LCD_5100_VSYNC_ACTIVE_MODE          0
#define  LCD_12_1_VSYNC_ACTIVE_MODE          0
#define  LCD_15_1_VSYNC_ACTIVE_MODE          0

#define  DPC_03_5_DELAY_RGB                  0
#define  DPC_04_3_DELAY_RGB                  0
#define  DPC_05_0_DELAY_RGB                  0
#define  DPC_05_6_DELAY_RGB                  0
#define  DPC_06_5_DELAY_RGB                  0
#define  DPC_07_0_DELAY_RGB                  0
#define  DPC_08_4_DELAY_RGB                  0
#define  DPC_10_4_DELAY_RGB                  0
#define  DPC_12_1_DELAY_RGB                  0
#define  DPC_12_X_DELAY_RGB                  0
#define  DPC_15_1_DELAY_RGB                  0
#define  DPC_N500_DELAY_RGB                  0

#define  LCD_5100_DELAY_RGB                  0
#define  LCD_12_1_DELAY_RGB                  0
#define  LCD_15_1_DELAY_RGB                  0

#define  DPC_03_5_DELAY_DE                   7
#define  DPC_04_3_DELAY_DE                   7
#define  DPC_05_0_DELAY_DE                   7
#define  DPC_05_6_DELAY_DE                   7
#define  DPC_06_5_DELAY_DE                   7
#define  DPC_07_0_DELAY_DE                   7
#define  DPC_08_4_DELAY_DE                   7
#define  DPC_10_4_DELAY_DE                   7
#define  DPC_12_1_DELAY_DE                   7
#define  DPC_12_X_DELAY_DE                   7
#define  DPC_15_1_DELAY_DE                   7
#define  DPC_N500_DELAY_DE                   7

#define  LCD_5100_DELAY_DE                   7
#define  LCD_12_1_DELAY_DE                   7
#define  LCD_15_1_DELAY_DE                   7

#define  DPC_03_5_DELAY_VS                   7
#define  DPC_04_3_DELAY_VS                   7
#define  DPC_05_0_DELAY_VS                   7
#define  DPC_05_6_DELAY_VS                   7
#define  DPC_06_5_DELAY_VS                   7
#define  DPC_07_0_DELAY_VS                   7
#define  DPC_08_4_DELAY_VS                   7
#define  DPC_10_4_DELAY_VS                   7
#define  DPC_12_1_DELAY_VS                   7
#define  DPC_12_X_DELAY_VS                   7
#define  DPC_15_1_DELAY_VS                   7
#define  DPC_N500_DELAY_VS                   7

#define  LCD_5100_DELAY_VS                   7
#define  LCD_12_1_DELAY_VS                   7
#define  LCD_15_1_DELAY_VS                   7

#define  DPC_03_5_DELAY_HS                   7
#define  DPC_04_3_DELAY_HS                   7
#define  DPC_05_0_DELAY_HS                   7
#define  DPC_05_6_DELAY_HS                   7
#define  DPC_06_5_DELAY_HS                   7
#define  DPC_07_0_DELAY_HS                   7
#define  DPC_08_4_DELAY_HS                   7
#define  DPC_10_4_DELAY_HS                   7
#define  DPC_12_1_DELAY_HS                   7
#define  DPC_12_X_DELAY_HS                   7
#define  DPC_15_1_DELAY_HS                   7
#define  DPC_N500_DELAY_HS                   7

#define  LCD_5100_DELAY_HS                   7
#define  LCD_12_1_DELAY_HS                   7
#define  LCD_15_1_DELAY_HS                   7

#define  DPC_03_5_DELAY_REV                 12
#define  DPC_04_3_DELAY_REV                 12
#define  DPC_05_0_DELAY_REV                 12
#define  DPC_05_6_DELAY_REV                 12
#define  DPC_06_5_DELAY_REV                 12
#define  DPC_07_0_DELAY_REV                 12
#define  DPC_08_4_DELAY_REV                 12
#define  DPC_10_4_DELAY_REV                 12
#define  DPC_12_1_DELAY_REV                 12
#define  DPC_12_X_DELAY_REV                 12
#define  DPC_15_1_DELAY_REV                 12
#define  DPC_N500_DELAY_REV                 12

#define  LCD_5100_DELAY_REV                 12
#define  LCD_12_1_DELAY_REV                 12
#define  LCD_15_1_DELAY_REV                 12

#define  DPC_03_5_DELAY_SP                  12
#define  DPC_04_3_DELAY_SP                  12
#define  DPC_05_0_DELAY_SP                  12
#define  DPC_05_6_DELAY_SP                  12
#define  DPC_06_5_DELAY_SP                  12
#define  DPC_07_0_DELAY_SP                  12
#define  DPC_08_4_DELAY_SP                  12
#define  DPC_10_4_DELAY_SP                  12
#define  DPC_12_1_DELAY_SP                  12
#define  DPC_12_X_DELAY_SP                  12
#define  DPC_15_1_DELAY_SP                  12
#define  DPC_N500_DELAY_SP                  12

#define  LCD_5100_DELAY_SP                  12
#define  LCD_12_1_DELAY_SP                  12
#define  LCD_15_1_DELAY_SP                  12

#define  DPC_03_5_DELAY_LP                  12
#define  DPC_04_3_DELAY_LP                  12
#define  DPC_05_0_DELAY_LP                  12
#define  DPC_05_6_DELAY_LP                  12
#define  DPC_06_5_DELAY_LP                  12
#define  DPC_07_0_DELAY_LP                  12
#define  DPC_08_4_DELAY_LP                  12
#define  DPC_10_4_DELAY_LP                  12
#define  DPC_12_1_DELAY_LP                  12
#define  DPC_12_X_DELAY_LP                  12
#define  DPC_15_1_DELAY_LP                  12
#define  DPC_N500_DELAY_LP                  12

#define  LCD_5100_DELAY_LP                  12
#define  LCD_12_1_DELAY_LP                  12
#define  LCD_15_1_DELAY_LP                  12
//-----------------------------------------------------------------------------
#define  DPC_FORMAT_RGB555               0   // RGB555 Format
#define  DPC_FORMAT_RGB565               1   // RGB565 Format
#define  DPC_FORMAT_RGB666               2   // RGB666 Format
#define  DPC_FORMAT_RGB888               3   // RGB888 Format
#define  DPC_FORMAT_MRGB555A             4   // MRGB555A Format
#define  DPC_FORMAT_MRGB555B             5   // MRGB555B Format
#define  DPC_FORMAT_MRGB565              6   // MRGB565 Format
#define  DPC_FORMAT_MRGB666              7   // MRGB666 Format
#define  DPC_FORMAT_MRGB888A             8   // MRGB888A Format
#define  DPC_FORMAT_MRGB888B             9   // MRGB888B Format
#define  DPC_FORMAT_CCIR656             10   // ITU-R BT.656 / 601(8-bit)
#define  DPC_FORMAT_CCIR601A            12   // ITU-R BT.601A
#define  DPC_FORMAT_CCIR601B            13   // ITU-R BT.601B
#define  DPC_FORMAT_4096COLOR            1   // 4096 Color Format
#define  DPC_FORMAT_16GRAY               3   // 16 Level Gray Format
//-----------------------------------------------------------------------------
#define  DPC_PCLKMODE_DYNAMIC            0    // PCLK is provided only when CPU has access to registers of this module.
#define  DPC_PCLKMODE_ALWAYS             1    // PCLK is always provided for this module.

#define  DPC_BCLKMODE_DISABLE            0    // BCLK is disabled.
#define  DPC_BCLKMODE_DYNAMIC            2    // BCLK is provided only when this module requests it.
#define  DPC_BCLKMODE_ALWAYS             3    // BCLK is always provided for this module.
//-----------------------------------------------------------------------------
#define  DPC_CLKSRCSEL_PLL0              0
#define  DPC_CLKSRCSEL_PLL1              1
#define  DPC_CLKSRCSEL_SVCLK             2
#define  DPC_CLKSRCSEL_PVCLK_NORM        3
#define  DPC_CLKSRCSEL_PVCLK_INVT        4
#define  DPC_CLKSRCSEL_AVCLK             5
#define  DPC_CLKSRCSEL_SVCLK_INVT        6
#define  DPC_CLKSRCSEL_CLKGEN0           7
//-----------------------------------------------------------------------------
#define  DPC_VCLK2_DIVIDER                   1

//#define  DPC_VCLK_SOURCE    DPC_CLKSRCSEL_PLL0  // SysGetVclkSourceByDevice
#define  DPC_VCLK2_SOURCE   DPC_CLKSRCSEL_CLKGEN0

#if  DPC_VCLK_SOURCE == DPC_CLKSRCSEL_PLL0
#endif

#if  DPC_VCLK_SOURCE == DPC_CLKSRCSEL_PLL1
#endif

#if  DPC_VCLK_SOURCE == DPC_CLKSRCSEL_XTI
#endif

#define  DPC_VCLK2_FREQUENCY   (DPC_VCLK_FREQUENCY / DPC_VCLK2_DIVIDER)
//-----------------------------------------------------------------------------
#define  DPC_OUTCLKDELAY_0_0NS           0
#define  DPC_OUTCLKDELAY_0_5NS           1
#define  DPC_OUTCLKDELAY_1_0NS           2
#define  DPC_OUTCLKDELAY_1_5NS           3
#define  DPC_OUTCLKDELAY_2_0NS           4
#define  DPC_OUTCLKDELAY_2_5NS           5
#define  DPC_OUTCLKDELAY_3_0NS           6
#define  DPC_OUTCLKDELAY_3_5NS           7
//-----------------------------------------------------------------------------
#define  DPC_03_5_VCLK_FREQUENCY      24500000   //  3.5"  : 24.50 MHz
#define  DPC_DITHER_BYPASS                   0   // Bypass mode.
#define  DPC_DITHER_4BIT                     1   // 8 bit -> 4 bit mode.       
#define  DPC_DITHER_5BIT                     2   // 8 bit -> 5 bit mode.
#define  DPC_DITHER_6BIT                     3   // 8 bit -> 6 bit mode.
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

//=============================================================================
void  SysInitDPC(void);
void  SysSetDPcEnableMode(int nDisableEnableMode);
int   SysGetDPcEnableMode(void);
void  SysSetDPcDelay(HWORD wDelayRGB,HWORD wDelayHS,HWORD wDelayVS,HWORD wDelayDE,HWORD wDelayLP,HWORD wDelaySP,HWORD wDelayREV);
void  SysSetDPcDither(HWORD wDitherR,HWORD wDitherG,HWORD wDitherB);
void  SysSetDpcMode(HWORD wFormat,int nInterlace,int nInvertField,int nRGbMode,int nSwapRB,HWORD wYCorder,int nClipYC,int nEmbeddedSync,HWORD wClock,int nInvertClock);
void  SysSetDPcHSync(HWORD wAVWidth,HWORD wHSW,HWORD wHFP,HWORD wHBP,int nInvHSync);
void  SysSetDPcVSync(HWORD wAVHeight,HWORD wVSW,HWORD wVFP,HWORD wVBP,int nInvVSync,HWORD wEAVHeight,HWORD wEVSW,HWORD wEVFP,HWORD wEVBP);
void  SysSetDPcVSyncOffset(HWORD wVSSOffset,HWORD wVSEOffset,HWORD wEVSSOffset,HWORD wEVSEOffset);
//-----------------------------------------------------------------------------
void  SysSetDPcClockPClkMode(DWORD dPclkMode);
DWORD SysGetDPcClockPClkMode(void);
void  SysSetDPcClockSource(int nIndex,DWORD dClkSrc);
DWORD SysGetDPcClockSource(int nIndex);
void  SysSetDPcClockDivisor(int nIndex,DWORD dDivisor);
DWORD SysGetDPcClockDivisor(int nIndex);
void  SysSetDPcClockOutInv(int nIndex,int nOutClkInv);
void  SysSetDPcClockOutEnb(int nOutClkEnb);
void  SysSetDPcClockDivisorEnable(int nDisableEnableMode);
void  SysSetDPcClockOutDelay(int nIndex,DWORD dDelay);
//=============================================================================
DWORD SysGetClockDivisorByDevice(int nDeviceType);
DWORD SysGetVclkSourceByDevice(int nDeviceType);
DWORD SysGetVclkFrequency(int nDeviceType);
DWORD SysGetFormatByDevice(int nDeviceType);
void  SysGetHSyncByDevice(int nDeviceType,HWORD *pAVWidth,HWORD *pHSW,HWORD *pHFP,HWORD *pHBP,HWORD *pInvHSync);
void  SysGetVSyncByDevice(int nDeviceType,HWORD *pAVHeight,HWORD *pVSW,HWORD *pVFP,HWORD *pVBP,HWORD *pInvVSync);
void  SysGetVSyncOffsetByDevice(int nDeviceType,HWORD *pVSSOffset,HWORD *pVSEOffset,HWORD *pEVSSOffset,HWORD *pEVSEOffset);
void  SysGetDelayByDevice(int nDeviceType,HWORD *pDelayRGB,HWORD *pDelayHS,HWORD *pDelayVS,HWORD *pDelayDE,HWORD *pDelayLP,HWORD *pDelaySP,HWORD *pDelayREV);
//=============================================================================

#ifdef  __cplusplus
}
#endif

#endif

