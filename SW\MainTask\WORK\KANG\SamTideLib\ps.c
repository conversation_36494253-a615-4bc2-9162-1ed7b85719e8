/*  Ps  Generate tide graphs in PostScript[tm] format.
    $** Id: ps.c,v 1.3 1996/02/19 06:02:23 jackg Exp jackg $

    Copyright (C) 1996  <PERSON>.
    Also starring:  <PERSON>.

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
*/

/* TO DO: */
/* Produce output for either 8.5"x11" or A4 sized paper. */
/* Embellish output, make calendar. */

#include "everythi.h"

/* these should be command line params */
#define TIDE_LINE_WIDTH 1
#define MARK_LINE_WIDTH 1
#define MARK_LINE_DASH "[ 0.03125 inch 0.03125 inch ]" /* .03125 = 1/32 inch */
#define MARK_LABEL_FONT "Helvetica-Narrow"

#define LOCATION_FONT "AvantGarde-Book"
#define LOCATION_FONT_SIZE "1.3 ptsPerFoot mul"

#define MAX_LABEL 256
static char label_buf[MAX_LABEL];

/* Convert from x,y,slope to bezier curve. Based on info from
   parametric curves chapter in "Computer Graphics, Principles and
    Practice", Foley, van Dam, Feiner, and Hughes. */
static void
output_bezier(int P1x, double P1y,
double P1slope,
	      int P4x, double P4y, double P4slope)
{
    /* compute the timestep */
    int T = P4x - P1x;

    /* compute the first control point (P2) */
    double P2x = (1.0/3.0) * T + P1x;
    double P2y = (P1slope / 3.0) * T + P1y;

    /* second control point */
    double P3x = (-1.0/3.0) * T + P4x;
    double P3y = (P4slope/-3.0) * T + P4y;

    /* output the PostScript(tm) */
    printf("%d %f moveto\n", P1x, P1y);
    printf("%f %f %f %f %d %f curveto\n",
	   P2x, P2y, P3x, P3y, P4x, P4y);
}

/* output postscript variable declarations used in scale calculation */
static void
output_scale_vars(int hours)
{
    /* the canonical constant */
    printf("/inch { 72 mul} def\n");

    /* US Letter */
    /* NOTE A4 */
    printf("/pageHeight { 8.5 inch } def \n");
    printf("/pageWidth { 11 inch } def \n");
    printf("/sideMargin { 2 inch } def\n");
    printf("/bottomMargin { 2 inch } def\n");
    printf("/topMargin { 3 inch } def\n");

    /* these change with paper orientation and paper size */
    /* NOTE A4 */
    printf("/graphWidth { pageWidth sideMargin 2 mul sub } def\n");
    printf("/graphHeight { pageHeight bottomMargin topMargin add sub } def\n");

    /* width */
    printf("/ptsPerHour { graphWidth %d div } def\n", hours);
    printf("/ptsPerSecond { ptsPerHour 3600 div } def\n");

    /* height, should be based on amplitude */
    printf("/amplitude %f def\n", amplitude * 2);
    printf("/ptsPerFoot { graphHeight amplitude div } def\n");
    printf("/mllw %f def\n", DATUM);

    /* default transformation matrix */
    printf("/defaultMatrix matrix currentmatrix def\n");
}

/* routines used for placing strings */
static void
output_string_routines(void)
{
    /* charheight */
    /* from McGilton, Campione, "PostScript By Example" */
    /* stack = char */
    /* return = lly ury */
    printf("/charheight {\ngsave\n");
    printf("newpath\n");
    printf("0 0 moveto false charpath flattenpath\n");
    printf("pathbbox exch pop\n");
    printf("3 -1 roll pop grestore\n");
    printf("} def\n");

    /* center_show */
    /* from McGilton, Campione, "PostScript By Example" */
    /* stack = x y (string) */
    printf("/center_show {\n");
    printf("dup stringwidth pop 2 div\n");
    printf("4 -1 roll exch sub\n");
    printf("3 -1 roll moveto show\n");
    printf("} def\n");


    printf("/right_show {\n");
    printf("dup stringwidth pop\n");
    printf("4 -1 roll exch sub\n");
    printf("3 -1 roll moveto show\n");
    printf("} def\n");

    /* HACK!! */
    /* this is ugly, but I suck so I only know how */
    /* to do fonts in an unscaled world */
    printf("/mark_label_show {\n");
    printf("matrix currentmatrix exch defaultMatrix setmatrix\n");
    printf("-90 rotate\n");
    printf("/Helvetica-Narrow findfont .66 ptsPerFoot mul scalefont setfont\n");
    printf("dup stringwidth pop 2 div .66 ptsPerFoot mul add neg\n");
    printf("(1) charheight exch pop 2 div neg rmoveto show\n");
    printf("setmatrix\n");
    printf("} def\n");

    /* HACK!!! */
    /* almost the same as above, but a different font and no magic offsets */
    printf("/location_show {\n");
    printf("matrix currentmatrix exch defaultMatrix setmatrix\n");
    printf("-90 rotate\n");
    printf("/%s findfont %s scalefont setfont\n",
	   LOCATION_FONT, LOCATION_FONT_SIZE);
    printf("dup stringwidth pop 2 div neg\n");
    printf("0 rmoveto show\n");
    printf("setmatrix\n");
    printf("} def\n");

}

/* output postscript scale and translations statements */
static void
set_origin_and_scale(void)
{
    /* rotate for landscape graph */
    printf("-90 rotate\n");

    /* set origin at 0 seconds, 0 tide height */
    /* -x translate compensates for rotate */
    /* x origin */
    printf("pageWidth sideMargin sub neg\n");
    /* y origin */
    printf("graphHeight 2 div bottomMargin add mllw ptsPerFoot mul sub\n");
    printf("translate\n");

    /* Set the units to seconds, feet */
    printf("ptsPerSecond ptsPerFoot scale\n");

    /* Hack. The linewidth get's scaled too. Hack it here */
    printf("0.1 setlinewidth\n");
}

/* set the correct transformation matrix and line style */
static void
stroke_line(int width, char *dash)
{
    /* push the CTM */
    printf("matrix currentmatrix\n");
    /* set CTM to default */
    printf("defaultMatrix setmatrix\n");
    /* set the line parameters */
    printf("%d setlinewidth\n", width);
    if (dash != NULL) {
	printf("%s 0 setdash\n", dash);
    } else {
	printf("[ 1 0 ] 0 setdash\n");
    }
    /* stroke me, stroke me (STROKE STROKE) */
    printf("stroke\n");
    /* restore the CTM */
    printf("setmatrix\n");
}

/*****************************************/
/* Marks */

static void
draw_mark_line(int mark, int hours)
{
    printf("0 %d moveto\n", mark);
    printf("%d 3600 mul %d lineto\n", hours, mark);
    if (mark != 0) {
	stroke_line(MARK_LINE_WIDTH, MARK_LINE_DASH);
    } else {
	stroke_line(MARK_LINE_WIDTH, NULL);
    }
}

static void
draw_mark_labels(int low, int high)
{
    int mark;

#if 0    
    printf("/%s findfont\n", MARK_LABEL_FONT);
    printf("%d 0.66 mul scalefont\n", hinc); 
    /* record char size */
    printf("(1) charheight exch sub /charSize exch def\n");
#endif    

    for (mark = low; mark < high; mark += hinc) {
	sprintf(label_buf, "%d", mark);
	/* HACK x location is hacked */
	printf("%d %d moveto\n", 0, mark);
	printf("(%s) mark_label_show\n", label_buf);
    }
}
     
static void
draw_marks(int hours)
{
    int low, high, mark;

    low = (int)(-ceil(amplitude) + DATUM);
    high = (int)(ceil(amplitude) + DATUM);
    
    /* x is in units of seconds */
    for (mark = 0; mark >= low; mark -= hinc) {
	draw_mark_line(mark, hours);
    }
    for (mark = hinc; mark < high; mark += hinc) {
	draw_mark_line(mark, hours);
    }

    draw_mark_labels(low, high);

}

#define SECONDS_PER_MINUTE 60
#define SECONDS_PER_HOUR (SECONDS_PER_MINUTE*60)
#define MINUTES_PER_HOUR 60
#define MINUTES_PER_POINT 60
#define HOURS_PER_DAY 24
#define MINUTES_PER_DAY (HOURS_PER_DAY*MINUTES_PER_HOUR)

static void
draw_location(double y, int hours)
{
    printf("%d %f moveto\n", (hours/2) * SECONDS_PER_HOUR, y);
    printf("(%s) location_show\n", location);
}

/* Output a bezier curve path from faketime to faketime + hours */
void
tide2ps(int hours)
{
  int point, seconds;
  double samps[3];
  struct point {
      double height;
      double slope;
  } *points;

  points =
      (struct point *)malloc(sizeof(struct point) *
			     (((hours*MINUTES_PER_HOUR)/MINUTES_PER_POINT)+1));
  assert(points != NULL);

/*  puts (location);
  if (mark && !iscurrent)
    printf ("Mark level:  %f\n", marklev);
*/
  
  /* Initialize the amplitude. */
  happy_new_year (yearoftimet (faketime));

  /* calculate the points of the curve */
  for (point=0; point < ((hours*MINUTES_PER_HOUR)/MINUTES_PER_POINT) + 1;
       point++) {
      double slope;
      
      samps[0] = time2atide(faketime - SECONDS_PER_MINUTE);
      samps[1] = time2atide(faketime);
      samps[2] = time2atide(faketime + SECONDS_PER_MINUTE);

      /* slope in feet per second */
      slope = (samps[2] - samps[0]) / 120;

      points[point].height = samps[1];
      points[point].slope = slope;

      faketime += MINUTES_PER_POINT * SECONDS_PER_MINUTE;
  }

  /* spit out the PostScript(tm) */
  printf("%%!PS\n");
  output_scale_vars(hours);
  output_string_routines();
  set_origin_and_scale();
  draw_marks(hours);
  /* y location is HACKed */
  draw_location(amplitude + DATUM + 2.0, hours);
  
  /* output the curves */
  seconds = 0;
  for (point=0; point < (hours*MINUTES_PER_HOUR)/MINUTES_PER_POINT; point++) {
      output_bezier(seconds, points[point].height, points[point].slope,
		    seconds + (MINUTES_PER_POINT * SECONDS_PER_MINUTE),
		    points[point+1].height, points[point+1].slope);
      seconds += MINUTES_PER_POINT * SECONDS_PER_MINUTE;
  }
  stroke_line(TIDE_LINE_WIDTH, NULL);
  printf("showpage\n");
  free (points);
}
