/*...........................................................................*/
/*.                  File Name : MyriadPro18bGrk.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY MyriadPro18bRus_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro18bGrk_offset_table[129] = {
0x0000,0x0005,0x000a,0x000f,0x0014,0x0019,0x001e,0x0023,0x0028,0x002d,0x0032,0x0039,0x003e,0x0043,0x0048,0x004d,
0x0052,0x0059,0x0063,0x0073,0x007a,0x0089,0x009c,0x00a5,0x00aa,0x00bd,0x00c2,0x00d5,0x00e9,0x00f4,0x0104,0x0112,
0x011e,0x012e,0x013b,0x0149,0x015a,0x016b,0x0172,0x0181,0x0191,0x01a5,0x01b6,0x01c4,0x01d5,0x01e5,0x01f3,0x01f8,
0x0206,0x0213,0x0222,0x0235,0x0244,0x0256,0x0268,0x0270,0x027f,0x028d,0x0298,0x02a6,0x02ad,0x02ba,0x02c8,0x02d6,
0x02e2,0x02f0,0x02fb,0x0306,0x0314,0x0322,0x0329,0x0336,0x0342,0x0350,0x035c,0x0367,0x0375,0x0383,0x0391,0x039c,
0x03aa,0x03b5,0x03c2,0x03d3,0x03df,0x03f0,0x0402,0x040a,0x0417,0x0425,0x0432,0x0444,0x0449,0x044e,0x0453,0x0458,
0x045d,0x0462,0x0467,0x046c,0x0471,0x0476,0x047b,0x0480,0x0485,0x048a,0x048f,0x0494,0x0499,0x049e,0x04a3,0x04a8,
0x04ad,0x04b2,0x04b7,0x04bc,0x04c1,0x04c6,0x04cb,0x04d0,0x04d5,0x04da,0x04df,0x04e4,0x04e9,0x04ee,0x04f3,0x04f8,
0x04fd,
};


ROMDATA PEGUBYTE MyriadPro18bGrk_data_table[4000] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x04, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe7, 0x0e, 0x70, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x04, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x27, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x80, 0x1c, 0x01, 0xc0, 0x70, 0x1c, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0xf0, 0x00, 0x07, 
0xf8, 0x00, 0x00, 0x30, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0x80, 0x1e, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x76, 0xfc, 0xf8, 0x00, 0x3b, 
0xff, 0x77, 0x81, 0xee, 0xf0, 0x38, 0x1c, 0x00, 0x3b, 0x00, 0x37, 0x03, 0xc0, 0x27, 0x20, 0x7c, 
0x07, 0xf8, 0x1f, 0xf8, 0x1f, 0x01, 0xff, 0x8f, 0xff, 0x3c, 0x0f, 0x00, 0x78, 0x0f, 0x1e, 0x1f, 
0x03, 0xe0, 0x1f, 0x03, 0xe3, 0xe0, 0xf1, 0xff, 0xe0, 0x1c, 0x03, 0xff, 0xf3, 0xfc, 0x00, 0x7f, 
0xf1, 0xff, 0xf8, 0x01, 0x80, 0xfe, 0x07, 0xc3, 0xe7, 0x9e, 0x78, 0x07, 0x80, 0x3c, 0xc0, 0x0c, 
0x03, 0x80, 0x1c, 0x03, 0xc0, 0xe0, 0x99, 0x00, 0x00, 0x1f, 0xe0, 0x00, 0x03, 0xfc, 0x00, 0x07, 
0xfc, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x82, 0x20, 0x03, 0x00, 0x1c, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x26, 0x5c, 0xf8, 0x00, 0x3b, 
0xff, 0x77, 0x81, 0xee, 0xf0, 0x38, 0xff, 0x80, 0x3b, 0xc0, 0x77, 0x0f, 0xf8, 0x76, 0x70, 0x7c, 
0x07, 0xfe, 0x1f, 0xf8, 0x1f, 0x01, 0xff, 0x8f, 0xff, 0x3c, 0x0f, 0x03, 0xfe, 0x0f, 0x1e, 0x1e, 
0x03, 0xe0, 0x1f, 0x03, 0xe3, 0xf0, 0xf1, 0xff, 0xe0, 0xff, 0x83, 0xff, 0xf3, 0xff, 0x00, 0x7f, 
0xf1, 0xff, 0xfe, 0x03, 0x83, 0xff, 0x83, 0xc3, 0xc7, 0x9e, 0x78, 0x1f, 0xf0, 0x3c, 0x70, 0x1c, 
0x03, 0x00, 0x38, 0x03, 0x80, 0xe1, 0xfb, 0x80, 0x00, 0x3f, 0xf0, 0x00, 0x07, 0xf8, 0x00, 0x07, 
0xfc, 0x00, 0x01, 0xfe, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x01, 0xfe, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
0x39, 0xc7, 0x70, 0x07, 0x00, 0x1c, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x06, 0x19, 0xfc, 0x00, 0x33, 
0xff, 0x67, 0x81, 0xec, 0xf0, 0x39, 0xff, 0xc0, 0x33, 0xe1, 0xf7, 0x3f, 0xfc, 0x26, 0x20, 0xfe, 
0x07, 0xff, 0x1f, 0xf8, 0x3f, 0x81, 0xff, 0x8f, 0xfe, 0x3c, 0x0f, 0x07, 0xff, 0x8f, 0x1e, 0x3c, 
0x07, 0xf0, 0x1f, 0x87, 0xe3, 0xf0, 0xf1, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0xf3, 0xff, 0x80, 0x7f, 
0xf1, 0xff, 0xff, 0x0f, 0x87, 0xff, 0xc1, 0xe7, 0x87, 0x9e, 0x78, 0x7f, 0xf8, 0x3c, 0x78, 0x7c, 
0x07, 0x00, 0x30, 0x03, 0x00, 0xc0, 0xb1, 0x00, 0x00, 0x3c, 0x70, 0x00, 0x07, 0x88, 0x00, 0x00, 
0xf8, 0x00, 0x03, 0xcf, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
0x10, 0x82, 0x20, 0x06, 0x00, 0x38, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0xdc, 0x00, 0x33, 
0xc0, 0x67, 0x81, 0xec, 0xf0, 0x39, 0xe3, 0xe0, 0x30, 0xf1, 0xe7, 0x7c, 0x3e, 0x00, 0x00, 0xee, 
0x07, 0x87, 0x9e, 0x00, 0x3b, 0x81, 0xe0, 0x00, 0x1e, 0x3c, 0x0f, 0x0f, 0x87, 0xcf, 0x1e, 0x78, 
0x07, 0xf0, 0x1f, 0x87, 0xe3, 0xf8, 0xf0, 0x00, 0x01, 0xe3, 0xe3, 0xc0, 0xf3, 0xc7, 0xc0, 0x3c, 
0x00, 0x1e, 0x0f, 0x8f, 0x8f, 0x79, 0xe1, 0xe7, 0x87, 0x9e, 0x78, 0xf8, 0x7c, 0x3c, 0x3c, 0x7c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x70, 0x00, 0x07, 0xc0, 0x00, 0x01, 
0xe0, 0x00, 0x03, 0x87, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xdc, 0x00, 0x03, 
0xc0, 0x07, 0x81, 0xe0, 0xf0, 0x03, 0xc1, 0xe0, 0x00, 0x73, 0xc0, 0x78, 0x1e, 0x0f, 0x00, 0xee, 
0x07, 0x87, 0x9e, 0x00, 0x3b, 0x81, 0xe0, 0x00, 0x3c, 0x3c, 0x0f, 0x0f, 0x03, 0xcf, 0x1e, 0x78, 
0x07, 0x70, 0x1f, 0x86, 0xe3, 0xf8, 0xf0, 0x00, 0x03, 0xc1, 0xe3, 0xc0, 0xf3, 0xc3, 0xc0, 0x1e, 
0x00, 0x1e, 0x07, 0x9e, 0x0e, 0x79, 0xe0, 0xf7, 0x07, 0x9e, 0x78, 0xf0, 0x3c, 0x3c, 0x1c, 0xf0, 
0x0e, 0x70, 0x7c, 0x73, 0xc1, 0xe3, 0xc7, 0x81, 0xce, 0x78, 0x73, 0xc3, 0xc3, 0xe0, 0x0f, 0x81, 
0xc1, 0xcf, 0x03, 0x87, 0x9e, 0x3c, 0x7c, 0x3c, 0x1e, 0x1e, 0xf0, 0xf3, 0xc0, 0x0f, 0x03, 0xff, 
0xe0, 0xf8, 0x01, 0xe0, 0x7f, 0xff, 0xf3, 0xc7, 0x81, 0x1c, 0x1e, 0x1e, 0xf3, 0x9e, 0x1c, 0x0e, 
0x0f, 0x1e, 0x3c, 0x0f, 0x03, 0xc7, 0x87, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xde, 0x00, 0x03, 
0xc0, 0x07, 0x81, 0xe0, 0xf0, 0x03, 0x80, 0xf0, 0x00, 0x3b, 0x80, 0xf0, 0x0f, 0x0f, 0x01, 0xef, 
0x07, 0x87, 0x1e, 0x00, 0x7b, 0xc1, 0xe0, 0x00, 0x78, 0x3c, 0x0f, 0x1e, 0x01, 0xef, 0x1e, 0xf0, 
0x0f, 0x78, 0x1d, 0xce, 0xe3, 0xfc, 0xf0, 0x00, 0x03, 0x80, 0xf3, 0xc0, 0xf3, 0xc3, 0xc0, 0x0f, 
0x00, 0x1e, 0x03, 0xdc, 0x0e, 0x78, 0xe0, 0xff, 0x07, 0x9e, 0x79, 0xe0, 0x1e, 0x3c, 0x1e, 0xe0, 
0x3f, 0x71, 0xfe, 0x77, 0xf1, 0xe3, 0xc3, 0x87, 0xee, 0x78, 0xe1, 0xe3, 0xc3, 0xf0, 0x3f, 0xc3, 
0x81, 0xdf, 0xc7, 0x87, 0x9e, 0x3c, 0xf0, 0x3c, 0x1e, 0x1e, 0xf0, 0xf1, 0xf8, 0x3f, 0xc7, 0xff, 
0xc3, 0xfe, 0x07, 0xf1, 0xff, 0xff, 0xf3, 0xc3, 0x87, 0x3f, 0x0f, 0x1c, 0xf3, 0x8e, 0x3c, 0x0f, 
0x0f, 0x1e, 0x1c, 0x3f, 0xc3, 0xc3, 0x8f, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x9e, 0x07, 0x03, 
0xff, 0x07, 0xff, 0xe0, 0xf0, 0x07, 0x80, 0xf0, 0x00, 0x3b, 0x00, 0xf0, 0x0f, 0x0f, 0x01, 0xcf, 
0x07, 0xfe, 0x1e, 0x00, 0x73, 0xc1, 0xff, 0x80, 0xf8, 0x3f, 0xff, 0x1e, 0xf9, 0xef, 0x1f, 0xe0, 
0x0f, 0x78, 0x1d, 0xce, 0xe3, 0xdc, 0xf0, 0xff, 0xc7, 0x80, 0xf3, 0xc0, 0xf3, 0xc7, 0x80, 0x0f, 
0x00, 0x1e, 0x01, 0xd8, 0x1e, 0x78, 0xf0, 0x7e, 0x07, 0x9e, 0x79, 0xe0, 0x1e, 0x3c, 0x0e, 0xc0, 
0x7f, 0xf3, 0xfe, 0x7f, 0xf1, 0xe3, 0xc3, 0xcf, 0xfe, 0x7b, 0xc1, 0xe3, 0xc7, 0xf8, 0x7f, 0xc7, 
0x81, 0xff, 0xc7, 0xff, 0x9e, 0x3c, 0xf0, 0x3c, 0x1e, 0x1e, 0x70, 0xf0, 0xf8, 0x7f, 0xe7, 0xff, 
0xc7, 0xff, 0x0f, 0xe3, 0xff, 0xff, 0xf3, 0xc3, 0xcf, 0xff, 0xcf, 0x1c, 0xf3, 0x8f, 0x38, 0x07, 
0x0f, 0x1e, 0x1e, 0x7f, 0xe3, 0xc3, 0xce, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x8e, 0x07, 0x03, 
0xff, 0x07, 0xff, 0xe0, 0xf0, 0x07, 0x80, 0xf0, 0x00, 0x3f, 0x00, 0xf0, 0x0f, 0x0f, 0x01, 0xc7, 
0x07, 0xfe, 0x1e, 0x00, 0x71, 0xc1, 0xff, 0x80, 0xf0, 0x3f, 0xff, 0x1e, 0xf9, 0xef, 0x1f, 0xe0, 
0x0e, 0x78, 0x1d, 0xce, 0xe3, 0xde, 0xf0, 0xff, 0xc7, 0x80, 0xf3, 0xc0, 0xf3, 0xff, 0x80, 0x07, 
0x80, 0x1e, 0x01, 0xf8, 0x1e, 0x78, 0xf0, 0x7c, 0x03, 0x9e, 0x71, 0xe0, 0x1e, 0x3c, 0x0f, 0xc0, 
0x79, 0xf3, 0xc0, 0x7c, 0x79, 0xe3, 0xc3, 0xcf, 0x3e, 0x7b, 0xe1, 0xe3, 0xcf, 0x3c, 0x78, 0x07, 
0x01, 0xf1, 0xe7, 0xff, 0x9e, 0x3d, 0xe0, 0x7e, 0x1e, 0x1e, 0x78, 0xe1, 0xf8, 0x79, 0xe1, 0xe7, 
0x87, 0x8f, 0x1e, 0x03, 0xcf, 0x07, 0x83, 0xc3, 0xcf, 0x73, 0xc7, 0x38, 0xf3, 0x8f, 0x39, 0xe7, 
0x0f, 0x1e, 0x1e, 0x79, 0xe3, 0xc3, 0xce, 0x79, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x8f, 0x0f, 0x83, 
0xff, 0x07, 0xff, 0xe0, 0xf0, 0x07, 0x80, 0xf0, 0x00, 0x1e, 0x00, 0xf0, 0x0f, 0x0f, 0x03, 0xc7, 
0x87, 0xff, 0x9e, 0x00, 0xf1, 0xe1, 0xff, 0x81, 0xe0, 0x3f, 0xff, 0x1e, 0xf9, 0xef, 0x1f, 0xf0, 
0x1e, 0x38, 0x1d, 0xcc, 0xf3, 0xce, 0xf0, 0xff, 0xc7, 0x80, 0xf3, 0xc0, 0xf3, 0xff, 0x00, 0x07, 
0x80, 0x1e, 0x00, 0xf8, 0x1e, 0x78, 0xf0, 0x7e, 0x03, 0xde, 0xf1, 0xe0, 0x1e, 0x3c, 0x07, 0xc0, 
0xf0, 0xf1, 0xfc, 0x78, 0x79, 0xe3, 0xc3, 0xde, 0x1e, 0x78, 0xf0, 0xf3, 0x9e, 0x1e, 0x3f, 0x8f, 
0x01, 0xe1, 0xe7, 0xff, 0x9e, 0x3f, 0xc0, 0x7e, 0x1e, 0x1e, 0x78, 0xe3, 0xc0, 0xf0, 0xf1, 0xe7, 
0x8f, 0x07, 0x9c, 0x07, 0x87, 0x07, 0x83, 0xc3, 0xde, 0x71, 0xe7, 0xb8, 0xf3, 0x8f, 0x79, 0xe7, 
0x8f, 0x1e, 0x1e, 0xf0, 0xf3, 0xc3, 0xde, 0x79, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x07, 0x03, 
0xc0, 0x07, 0x81, 0xe0, 0xf0, 0x07, 0x80, 0xf0, 0x00, 0x1e, 0x00, 0xf0, 0x0e, 0x0f, 0x03, 0xff, 
0x87, 0x87, 0xde, 0x00, 0xe1, 0xe1, 0xe0, 0x03, 0xe0, 0x3c, 0x0f, 0x1e, 0x01, 0xef, 0x1f, 0xf0, 
0x1e, 0x3c, 0x3c, 0xfc, 0xf3, 0xcf, 0xf0, 0x00, 0x07, 0x80, 0xf3, 0xc0, 0xf3, 0xfc, 0x00, 0x07, 
0x00, 0x1e, 0x00, 0xf0, 0x1e, 0x78, 0xe0, 0x7e, 0x01, 0xff, 0xe1, 0xe0, 0x1c, 0x3c, 0x07, 0x80, 
0xf0, 0xf1, 0xfc, 0x78, 0x79, 0xe3, 0xc3, 0xde, 0x1e, 0x78, 0x78, 0xf3, 0x9e, 0x1e, 0x3f, 0x8f, 
0x01, 0xe1, 0xe7, 0x87, 0x9e, 0x3f, 0x80, 0xff, 0x1e, 0x1e, 0x39, 0xe7, 0x80, 0xf0, 0xf1, 0xe7, 
0x8f, 0x07, 0xbc, 0x07, 0x87, 0x87, 0x83, 0xc3, 0xde, 0x71, 0xe3, 0xf0, 0xf3, 0x8f, 0x79, 0xe7, 
0x8f, 0x1e, 0x1e, 0xf0, 0xf3, 0xc3, 0xde, 0x79, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x07, 0x03, 
0xc0, 0x07, 0x81, 0xe0, 0xf0, 0x07, 0x80, 0xe0, 0x00, 0x1e, 0x00, 0x70, 0x0e, 0x0f, 0x03, 0xff, 
0x87, 0x83, 0xde, 0x00, 0xe0, 0xf1, 0xe0, 0x03, 0xc0, 0x3c, 0x0f, 0x0e, 0x03, 0xcf, 0x1e, 0x78, 
0x1c, 0x3c, 0x3c, 0xfc, 0xf3, 0xc7, 0xf0, 0x00, 0x07, 0x80, 0xe3, 0xc0, 0xf3, 0xc0, 0x00, 0x0e, 
0x00, 0x1e, 0x00, 0xf0, 0x0e, 0x79, 0xe0, 0xff, 0x00, 0xff, 0xc0, 0xe0, 0x1c, 0x3c, 0x07, 0x80, 
0xf0, 0xf1, 0xfc, 0x78, 0x79, 0xe3, 0xc3, 0xde, 0x1e, 0x78, 0x78, 0x77, 0x1e, 0x1e, 0x3f, 0x8f, 
0x01, 0xe1, 0xe3, 0x87, 0x9e, 0x3f, 0xc0, 0xe7, 0x1e, 0x1e, 0x39, 0xc7, 0x80, 0xf0, 0xf1, 0xe7, 
0x8f, 0x07, 0xbc, 0x07, 0x87, 0x87, 0x83, 0xc3, 0xde, 0x71, 0xe3, 0xf0, 0xf3, 0x8f, 0x79, 0xe7, 
0x8f, 0x1e, 0x1e, 0xf0, 0xf3, 0xc3, 0xde, 0x79, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x80, 0x03, 
0xc0, 0x07, 0x81, 0xe0, 0xf0, 0x03, 0xc1, 0xe0, 0x00, 0x1e, 0x00, 0x38, 0x1c, 0x0f, 0x03, 0xff, 
0x87, 0x83, 0xde, 0x01, 0xe0, 0xf1, 0xe0, 0x07, 0x80, 0x3c, 0x0f, 0x0f, 0x03, 0xcf, 0x1e, 0x3c, 
0x3c, 0x1c, 0x3c, 0xf8, 0xf3, 0xc7, 0xf0, 0x00, 0x03, 0xc1, 0xe3, 0xc0, 0xf3, 0xc0, 0x00, 0x1e, 
0x00, 0x1e, 0x00, 0xf0, 0x0f, 0x79, 0xe0, 0xef, 0x00, 0x1e, 0x00, 0x70, 0x38, 0x3c, 0x07, 0x80, 
0xf0, 0xf3, 0xc0, 0x78, 0x79, 0xe3, 0xc3, 0x9e, 0x1e, 0x78, 0x78, 0x7f, 0x1e, 0x1e, 0x78, 0x0f, 
0x01, 0xe1, 0xe3, 0x87, 0x1e, 0x3d, 0xe0, 0xe7, 0x1e, 0x1e, 0x3d, 0xc7, 0x80, 0xf0, 0xf1, 0xc7, 
0x8f, 0x07, 0xbc, 0x07, 0x87, 0x87, 0x83, 0xc3, 0x9e, 0x71, 0xc1, 0xe0, 0xf3, 0x8e, 0x79, 0xe7, 
0x8f, 0x1e, 0x1c, 0xf0, 0xf3, 0xc3, 0x9e, 0x79, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x07, 0x80, 0x03, 
0xc0, 0x07, 0x81, 0xe0, 0xf0, 0x03, 0xe3, 0xe0, 0x00, 0x1e, 0x00, 0x1c, 0x38, 0x0f, 0x07, 0x83, 
0xc7, 0x87, 0xde, 0x01, 0xc0, 0xf1, 0xe0, 0x0f, 0x80, 0x3c, 0x0f, 0x0f, 0x87, 0x8f, 0x1e, 0x3c, 
0x3c, 0x1e, 0x3c, 0xf8, 0xf3, 0xc3, 0xf0, 0x00, 0x03, 0xe3, 0xe3, 0xc0, 0xf3, 0xc0, 0x00, 0x3c, 
0x00, 0x1e, 0x00, 0xf0, 0x07, 0xff, 0xc1, 0xe7, 0x80, 0x1e, 0x00, 0x38, 0x70, 0x3c, 0x07, 0x80, 
0x79, 0xf3, 0xc1, 0x78, 0x79, 0xe3, 0xc7, 0x8f, 0x3e, 0x78, 0xf8, 0x7e, 0x0f, 0x3c, 0x78, 0x2f, 
0x81, 0xe1, 0xe3, 0xcf, 0x1e, 0x3c, 0xf1, 0xe7, 0x9f, 0x3e, 0x1f, 0x83, 0xc0, 0x79, 0xe1, 0xc7, 
0x8f, 0x8f, 0x3e, 0x03, 0xcf, 0x07, 0x83, 0xc7, 0x8f, 0x73, 0xc1, 0xe0, 0x7b, 0x9e, 0x39, 0xe7, 
0x0f, 0x1e, 0x3c, 0x79, 0xe3, 0xc7, 0x8e, 0x79, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x07, 0x80, 0x03, 
0xff, 0x07, 0x81, 0xe0, 0xf0, 0x01, 0xff, 0xc0, 0x00, 0x1e, 0x00, 0xfe, 0x7f, 0x0f, 0x87, 0x83, 
0xc7, 0xff, 0x9e, 0x03, 0xff, 0xf9, 0xff, 0x8f, 0xff, 0x3c, 0x0f, 0x07, 0xff, 0x8f, 0x1e, 0x1e, 
0x3c, 0x1e, 0x3c, 0x78, 0xf3, 0xc3, 0xf1, 0xff, 0xe1, 0xff, 0xc3, 0xc0, 0xf3, 0xc0, 0x00, 0x7f, 
0xf8, 0x1e, 0x00, 0xf0, 0x03, 0xff, 0x83, 0xc7, 0xc0, 0x1e, 0x01, 0xfc, 0xfe, 0x3c, 0x07, 0x80, 
0x7f, 0xfb, 0xff, 0x78, 0x79, 0xf1, 0xff, 0x8f, 0xff, 0x7f, 0xf0, 0x3e, 0x0f, 0xfc, 0x7f, 0xe7, 
0xc1, 0xe1, 0xe1, 0xfe, 0x1f, 0x3c, 0xf1, 0xc7, 0x9f, 0xff, 0x1f, 0x83, 0xf0, 0x7f, 0xe3, 0xc7, 
0x8f, 0xff, 0x1f, 0x03, 0xff, 0x07, 0xc1, 0xff, 0x8f, 0xff, 0x83, 0xf0, 0x7f, 0xfc, 0x3f, 0xff, 
0x0f, 0x8f, 0xfc, 0x7f, 0xe1, 0xff, 0x8f, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x03, 0x80, 0x03, 
0xff, 0x07, 0x81, 0xe0, 0xf0, 0x00, 0xff, 0x80, 0x00, 0x1e, 0x00, 0xfe, 0x7f, 0x07, 0x87, 0x01, 
0xc7, 0xff, 0x1e, 0x03, 0xff, 0xf9, 0xff, 0x9f, 0xff, 0x3c, 0x0f, 0x03, 0xfe, 0x0f, 0x1e, 0x1f, 
0x38, 0x1e, 0x3c, 0x70, 0xf3, 0xc1, 0xf1, 0xff, 0xe0, 0xff, 0x83, 0xc0, 0xf3, 0xc0, 0x00, 0x7f, 
0xf8, 0x1e, 0x00, 0xf0, 0x00, 0xfe, 0x03, 0xc3, 0xc0, 0x1e, 0x01, 0xfc, 0xfe, 0x3c, 0x07, 0x80, 
0x3f, 0x79, 0xff, 0x78, 0x78, 0xf0, 0xfe, 0x07, 0xef, 0x7f, 0xe0, 0x3c, 0x07, 0xf8, 0x3f, 0xe3, 
0xfd, 0xe1, 0xe0, 0xfc, 0x0f, 0x3c, 0x79, 0xc3, 0x9f, 0xef, 0x1f, 0x01, 0xfe, 0x3f, 0xc3, 0xc7, 
0x8f, 0xfe, 0x0f, 0xf1, 0xfe, 0x03, 0xc0, 0xfe, 0x07, 0xff, 0x03, 0xf0, 0x3f, 0xf8, 0x1f, 0xfe, 
0x07, 0x87, 0xf0, 0x3f, 0xc0, 0xfe, 0x07, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x03, 0xc0, 0x03, 
0xff, 0x07, 0x81, 0xe0, 0xf0, 0x00, 0x1c, 0x00, 0x00, 0x1e, 0x00, 0xfe, 0x7f, 0x07, 0x8f, 0x01, 
0xe7, 0xfc, 0x1e, 0x03, 0xff, 0xf9, 0xff, 0x9f, 0xff, 0x3c, 0x0f, 0x00, 0x78, 0x0f, 0x1e, 0x0f, 
0x78, 0x0f, 0x38, 0x70, 0xf3, 0xc1, 0xf1, 0xff, 0xe0, 0x1c, 0x03, 0xc0, 0xf3, 0xc0, 0x00, 0x7f, 
0xf8, 0x1e, 0x00, 0xf0, 0x00, 0x78, 0x07, 0x81, 0xe0, 0x1e, 0x01, 0xfc, 0xfe, 0x3c, 0x07, 0x80, 
0x1c, 0x38, 0x7c, 0x78, 0x78, 0xf0, 0x78, 0x03, 0x87, 0x7b, 0xc0, 0x3c, 0x01, 0xe0, 0x0f, 0x81, 
0xfd, 0xe1, 0xe0, 0x70, 0x0f, 0x3c, 0x7f, 0xc3, 0xdd, 0xc7, 0x0f, 0x00, 0xfe, 0x0f, 0x03, 0xc7, 
0x8f, 0x78, 0x07, 0xf0, 0x78, 0x01, 0xc0, 0x78, 0x01, 0xfc, 0x07, 0xf8, 0x0f, 0xe0, 0x0e, 0x1c, 
0x07, 0x83, 0xc0, 0x0f, 0x00, 0x78, 0x03, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 
0x0f, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x07, 0x78, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 
0x0f, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x0f, 0x3c, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
0x0f, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x0e, 0x3c, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 
0x0f, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x1e, 0x1e, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x1c, 0x1e, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 



};

xFONTYY MyriadPro18bGrk_Font = {0x01, 24, 6, 25, 3, 1, 29, 160, 0x0374, 0x03f3,
(PEGUSHORT *) MyriadPro18bGrk_offset_table,&MyriadPro18bRus_Font,
(PEGUBYTE *) MyriadPro18bGrk_data_table};

