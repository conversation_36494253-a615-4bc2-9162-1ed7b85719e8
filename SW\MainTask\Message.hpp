#include <stdio.h>
#include <stdlib.h>
#include "const.h"
#include "_AllStringY.hpp"

#ifndef __MESSAGE_HPP__
#define __MESSAGE_HPP__

class CMessage {
private:
	BOOL  m_bRead;
	int   m_nMsgType;
	int   m_nVdmMsgType;

	BOOL  m_bMsgSelected;	// 230302 IEC 61993-2:2018 6.11.3.3
	BOOL  m_bMsgLangMode;
	DWORD m_dFrom;
	DWORD m_dTo;
	int   m_nYear, m_nMonth, m_nDay;
	int   m_nHour, m_nMinute, m_nSecond;
	BYTE  m_szMessage[MAX_EN_MESSAGE_LEN];

public:
	enum {
		MSG_TEXT_BROAD = 0,
		MSG_TEXT_ADDR,
		MSG_SAFETY_BROAD,
		MSG_SAFETY_ADDR
	};
	
	CMessage() {
		m_bRead = 0;
	}
	~CMessage() {}

	void SetReadStatus(BOOL bRead) { m_bRead    = bRead;          }
	void SetMsgType(int nMsgType)  { m_nMsgType = nMsgType;       }
	void SetVdmMsgType(int nVdmMsgType)  { m_nVdmMsgType = nVdmMsgType;}
	void SetRetained(BOOL bMsgSelected)	{m_bMsgSelected = bMsgSelected;}
	void SetCnLangMode(BOOL bMsgLangMode)  { m_bMsgLangMode = bMsgLangMode;  }  // Han
	void SetFrom(DWORD dMMSI)      { m_dFrom    = dMMSI;          }
	void SetTo(DWORD dMMSI)        { m_dTo      = dMMSI;          }
	void SetMessage(BYTE *pszMsg)  { strcpy((char *)m_szMessage, (char *)pszMsg); }
	void SetDate(int year, int month, int day) {
		m_nYear  = year;
		m_nMonth = month;
		m_nDay   = day;
	}
	void SetTime(int hour, int minute, int second) {
		m_nHour   = hour;
		m_nMinute = minute;
		m_nSecond = second;
	}

	BOOL  GetReadStatus() { return m_bRead;    }
	int   GetMsgType()    { return m_nMsgType; }
	int   GetVdmMsgType() { return m_nVdmMsgType; }
	BOOL  GetRetained()   { return m_bMsgSelected; }
	char  GetCnLangMode() { return m_bMsgLangMode; } // Han
	DWORD GetFrom()       { return m_dFrom;    }
	DWORD GetTo()         { return m_dTo;      }
	void  GetMessage(BYTE *pszMsg) { strcpy((char *)pszMsg, (char *)m_szMessage); }
	void  GetFromString(BYTE *pszFrom) {
		if( m_dFrom == 0 ) strcpy((char *)pszFrom, STR_BROADCAST);
		else sprintf((char *)pszFrom, "%09d", m_dFrom);
	}
	void  GetToString(BYTE *pszTo) {
		if( m_dTo == 0 ) strcpy((char *)pszTo, STR_BROADCAST);
		else sprintf((char *)pszTo, "%09d", m_dTo);
	}
	void  GetMsgTypeString(BYTE *pszMsgType) {
		switch( m_nMsgType ) {
		case MSG_TEXT_BROAD:
			strcpy((char *)pszMsgType, (m_bMsgSelected) ? "BR TEXT(S)" : "BR TEXT");
			break;
		case MSG_TEXT_ADDR:
			strcpy((char *)pszMsgType, (m_bMsgSelected) ? "AD TEXT(S)" : "AD TEXT");
			break;
		case MSG_SAFETY_BROAD:
			strcpy((char *)pszMsgType, (m_bMsgSelected) ? "BR SAFETY(S)" : "BR SAFETY");
			break;
		case MSG_SAFETY_ADDR:
			strcpy((char *)pszMsgType, (m_bMsgSelected) ? "AD SAFETY(S)" : "AD SAFETY");
			break;
		}
	}
	void  GetDate(int *year, int *month, int *day) {
		*year  = m_nYear;
		*month = m_nMonth;
		*day   = m_nDay;
	}
	void  GetTime(int *hour, int *minute, int *second) {
		*hour   = m_nHour;
		*minute = m_nMinute;
		*second = m_nSecond;
	}
	void  GetTimeString(BYTE *pszTime) {
		if( m_nMonth  == UTC_MONTH_NULL_VALUE || 
			m_nDay    == UTC_DAY_NULL_VALUE   ||
			m_nHour   >= UTC_HOUR_NULL_VALUE  ||
			m_nMinute >= UTC_MINUTE_NULL_VALUE )
			strcpy((char *)pszTime, "__-__ __:__");
		else
			sprintf((char *)pszTime, "%02d-%02d %02d:%02d", m_nMonth, m_nDay, m_nHour, m_nMinute);
	}
	BOOL IsSameMessage(const CMessage &oMsg) {
	// 230302 IEC 61993-2:20198 6.11.3.3
	// Safety related messages having identical source and content shall replace existing messages that they duplicate.
	#ifdef EN_61993_ED3
		if( m_dFrom == oMsg.m_dFrom && strcmp((char *)m_szMessage, (char *)oMsg.m_szMessage) == 0 )
			return 1;
		else
			return 0;
	#else
		int nTimeSec1 = m_nHour * HOUR_TO_SEC + m_nMinute * MIN_TO_SEC + m_nSecond;
		int nTimeSec2 = oMsg.m_nHour * HOUR_TO_SEC + oMsg.m_nMinute * MIN_TO_SEC + oMsg.m_nSecond;
		int nDiffTime = abs(nTimeSec2 - nTimeSec1);

		if( nDiffTime < 60 && m_dFrom == oMsg.m_dFrom && strcmp((char *)m_szMessage, (char *)oMsg.m_szMessage) == 0 )
			return 1;
		else
			return 0;
	#endif
	}
};

#endif
