#include <stdio.h>
#include "PasswordCheckWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Font.h"

#define PWD_CHK_WND_X_POS		10
#define PWD_CHK_WND_Y_POS		54

#define PWD_CHK_WND_W			580
#define PWD_CHK_WND_H			370

#define PWD_CHK_WND_ROW_H		37

#define PWD_CHK_WND_CAP_X_POS		(PWD_CHK_WND_X_POS + 10)
#define PWD_CHK_WND_CTRL_X_POS		(PWD_CHK_WND_X_POS + 50)
#define PWD_CHK_WND_CTRL_W			130
#define PWD_CHK_WND_CTRL_H			30

extern CDocMgr *g_pDocMgr;

/*********************************************************************************************************/
// Name		: CPasswordCheckWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CPasswordCheckWnd::CPasswordCheckWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	m_nFocus        = 0;
	m_nPasswordType = PASSWORD_USER;
	
	m_pPassword     = new CEditCtrl(pScreen);
	m_pPassword->Create( PWD_CHK_WND_CTRL_X_POS, 
						 PWD_CHK_WND_Y_POS + PWD_CHK_WND_ROW_H*4 + (PWD_CHK_WND_ROW_H - PWD_CHK_WND_CTRL_H)/2, 
						 PWD_CHK_WND_CTRL_W, 
						 PWD_CHK_WND_CTRL_H, 6, 1, 1);
}

/*********************************************************************************************************/
// Name		: CPasswordCheckWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordCheckWnd::DrawWnd(BOOL bRedraw)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 2;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 4;
			break;
			
		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 4;
			break;
	}
	
	CWnd::DrawWnd(bRedraw);
	
	if( bRedraw )
	{
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;
		nXPos = PWD_CHK_WND_CAP_X_POS;
		nYPos = PWD_CHK_WND_Y_POS + PWD_CHK_WND_ROW_H*3 + (PWD_CHK_WND_ROW_H - nFontH)/2 + nYOffset;
		
		if(m_nPasswordType == PASSWORD_MASTER)
		{
			pUniCodeStr = (HWORD *)STR_ENTER_MASTER_PWD[nLangMode];
		}
		else
		{
			pUniCodeStr = (HWORD *)STR_ENTER_PASSWORD[nLangMode];
		}			
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
		m_pScreen->SetFont(pOldFont);
		
		DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
		EraseButton(1);
		EraseButton(2);
		EraseButton(3);
	}

	m_pPassword->SetEditMode(1);
	m_pPassword->SetFocus(1);
	m_pPassword->DrawWnd();
}

/*********************************************************************************************************/
// Name		: OnCursorEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordCheckWnd::OnCursorEvent(int nState)
{
	m_pPassword->OnCursorEvent(nState);
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordCheckWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
		case KBD_SCAN_CODE_ENT:
			break;
			
		default:
			m_pScreen->FillRect(PWD_CHK_WND_X_POS,
								PWD_CHK_WND_Y_POS + PWD_CHK_WND_ROW_H*7,
								PWD_CHK_WND_X_POS + PWD_CHK_WND_W-1,
								PWD_CHK_WND_Y_POS + PWD_CHK_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);
			m_pPassword->OnKeyEvent(nKey, nFlags);
			break;
	}
}

/*********************************************************************************************************/
// Name		: DrawErrorMsg
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordCheckWnd::DrawErrorMsg()
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0, nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
		pFont = &NewGulLim18bCJK;
		nYOffset = 2;
		break;

		case LANG_RUS:
		pFont = &MyriadPro24bRus;
		nYOffset = 4;
		break;

		default:
		pFont = &MyriadPro24bEng;
		nYOffset = 4;
		break;
	}
	
	pOldFont = m_pScreen->SetFont(pFont);
	nFontH = pFont->uHeight;


	pUniCodeStr = (HWORD *)STR_UNI_WRONG_PWD[nLangMode];
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);

	nXPos = PWD_CHK_WND_X_POS + (PWD_CHK_WND_W - nStrW)/2;
	nYPos = PWD_CHK_WND_Y_POS + PWD_CHK_WND_ROW_H*7 + (PWD_CHK_WND_ROW_H - pFont->uHeight)/2;

	m_pScreen->FillRect(PWD_CHK_WND_X_POS,
						PWD_CHK_WND_Y_POS + PWD_CHK_WND_ROW_H*7,
						PWD_CHK_WND_X_POS + PWD_CHK_WND_W-1,
						PWD_CHK_WND_Y_POS + PWD_CHK_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);

	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crWarn);

	m_pScreen->SetFont(pOldFont);

}

/*********************************************************************************************************/
// Name		: IsPasswordOK
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
BOOL CPasswordCheckWnd::IsPasswordOK()
{
	BYTE  szPassword[8];
	DWORD dPassword, dUserPassword;

	m_pPassword->GetText(szPassword);
	sscanf((char *)szPassword, "%d", &dPassword);

	dUserPassword = g_pDocMgr->GetUserPassword();

	switch( m_nPasswordType )
	{
		case PASSWORD_USER:
			if(dPassword == dUserPassword) 
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(0);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetManufacturerPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
			
		case PASSWORD_USER_MASTER:
			if( (dPassword == dUserPassword) || (dPassword == g_pDocMgr->GetUserMasterPassword())) 
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(0);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetManufacturerPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
			
		case PASSWORD_MASTER:
			if( dPassword == g_pDocMgr->GetMasterPassword() ) 
			{
				g_pDocMgr->SetLoginWithUserPassword(0);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetManufacturerPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
			
		case PASSWORD_MANUFACTURER:
			if( dPassword == g_pDocMgr->GetManufacturerPassword() ) 
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
		
		case PASSWORD_GBCODE:
			if( dPassword == g_pDocMgr->GetGBCodePassword() ) 
			{
				g_pDocMgr->SetLoginWithGBCodePassword(1);
				return 1;
			}
			else
			{
				return 0;
			}
			break;

		case PASSWORD_MASTER_OR_USER:
			if(dPassword == dUserPassword) 
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(0);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetMasterPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(0);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetManufacturerPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(0);
				g_pDocMgr->SetLoginWithMasterPassword(0);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
	}

	return 0;
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CPasswordCheckWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);
	
	/*switch( nResult )
	{
		case AL_YES:
			break;
		
		case AL_NO:
			break;
	}*/
	
	return nResult;
}
