{"files.associations": {"initializer_list": "cpp", "vector": "cpp", "xstring": "cpp", "xtree": "cpp", "xutility": "cpp", "algorithm": "cpp", "cmath": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "cwchar": "cpp", "exception": "cpp", "ios": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "map": "cpp", "memory": "cpp", "new": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "utility": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocinfo": "cpp", "xlocnum": "cpp", "xmemory": "cpp", "xmemory0": "cpp", "xstddef": "cpp", "xtr1common": "cpp", "cctype": "cpp", "compare": "cpp", "concepts": "cpp", "optional": "cpp"}, "python.autoComplete.extraPaths": ["${workspaceFolder}/sources/poky/bitbake/lib", "${workspaceFolder}\\sources\\poky\\meta\\lib"], "python.analysis.extraPaths": ["${workspaceFolder}/sources/poky/bitbake/lib", "${workspaceFolder}\\sources\\poky\\meta\\lib"], "[python]": {"diffEditor.ignoreTrimWhitespace": false, "editor.formatOnType": true, "editor.wordBasedSuggestions": "off", "files.trimTrailingWhitespace": false}, "[shellscript]": {"files.eol": "\n", "files.trimTrailingWhitespace": false}}