/*...........................................................................*/
/*.                  File Name : SYSPWM.C                                    .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.06                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysPWM.h"

#include <string.h>

//=============================================================================
static xSYS_PWM *G_pSysPWM = (xSYS_PWM *)PWM_PHSY_BASE_ADDR;
//=============================================================================

void  SysInitPWM(int nLcdOffMode)
{
#if defined(__POLLUX__)
      HWORD dLcdFreq = 269;
      HWORD dLcdDvdr = PWM0_OUT_FREQUENCY / dLcdFreq;
//    HWORD dKeyFreq = 269;
//    HWORD dKeyFreq = 22999;
      HWORD dKeyFreq =  99;
      HWORD dKeyDvdr = PWM1_OUT_FREQUENCY / dKeyFreq;
      HWORD dBuzFreq = 3000;
      HWORD dBuzDvdr = PWM2_OUT_FREQUENCY / dBuzFreq;

      SysSetPWMClockSource(PWM_CLKSRCSEL_PLL0);
      SysSetPWMClockDivisor(PWM_CLK_DIV);
      SysSetPWMClockPClkMode(PWM_PCLKMODE_ALWAYS);
      SysSetPWMClockDivisorEnable(1);

      SysSetPWMPreScale(PWM_LCD_CHANNEL,PWM0_PRE_SCALER);
      SysSetPWMPeriod(PWM_LCD_CHANNEL,dLcdDvdr);            // 5.6" LCD PWM Freq = 100 -- 300 Hz
      SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr / 2);     // dutycycle/period = 50%
      if (nLcdOffMode)
         {
          if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
          else
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr + 1); // L=100%
         }
      else
         {
          if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr + 1); // L=100%
          else
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
         }
      SysSetPWMPolarity(PWM_LCD_CHANNEL,1);

      SysSetPWMPreScale(PWM_KEY_CHANNEL,PWM1_PRE_SCALER);
      SysSetPWMPeriod(PWM_KEY_CHANNEL,dKeyDvdr);
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,dKeyDvdr / 2);     // dutycycle/period = 50%
      SysSetPWMPolarity(PWM_KEY_CHANNEL,1);

  #if !defined(__N430_MODEL__)
//    SysSetPWMPreScale(PWM_BUZ_CHANNEL,PWM2_PRE_SCALER);
//    SysSetPWMPeriod(PWM_BUZ_CHANNEL,dBuzDvdr);
//    SysSetPWMDutyCycle(PWM_BUZ_CHANNEL,dBuzDvdr / 2);     // dutycycle/period = 50%
//    SysSetPWMPolarity(PWM_BUZ_CHANNEL,1);
  #endif
#else                           // SPICA
      HWORD dLcdFreq = 269;
      HWORD dLcdDvdr = PWM0_OUT_FREQUENCY / dLcdFreq;
      HWORD dKeyFreq =  99;
      HWORD dKeyDvdr = PWM1_OUT_FREQUENCY / dKeyFreq;
      HWORD dBuzFreq = 3000;
      HWORD dBuzDvdr = PWM2_OUT_FREQUENCY / dBuzFreq;
      DWORD dPwm0Out = PWM0_OUT_FREQUENCY;
      DWORD dPwm0Pre = PWM0_PRE_SCALER;
      int   nNavisType = SysGetNavisModelType();

      if (nNavisType == NAVIS_TYPE_3800 && SysCheckLCD3800Mitsubishi())
         {
          dPwm0Out = PWM_IN_FREQUENCY / 4;
//        dLcdFreq = 400;                       // 400 Hz
//        dLcdDvdr = dPwm0Out / dLcdFreq;
          dLcdDvdr = dPwm0Out /  400;
          if (dLcdDvdr > 1023)
              dLcdDvdr = 1023;

//        dPwm0Pre = (PWM_IN_FREQUENCY / dPwm0Out);
//        dPwm0Pre =  20;                       // 400 Hz
          dPwm0Pre =   4;                       //2001 Hz
//        dPwm0Pre =   1;                       //8000 Hz
         }

      SysSetPWMClockSource(PWM_CLKSRCSEL_PLL1);
      SysSetPWMClockDivisor(PWM_CLK_DIV);
      SysSetPWMClockPClkMode(PWM_PCLKMODE_ALWAYS);
      SysSetPWMClockDivisorEnable(1);

//    SysSetPWMPreScale(PWM_LCD_CHANNEL,PWM0_PRE_SCALER);
      SysSetPWMPreScale(PWM_LCD_CHANNEL,dPwm0Pre);
      SysSetPWMPeriod(PWM_LCD_CHANNEL,dLcdDvdr);            // 5.6" LCD PWM Freq = 100 -- 300 Hz
      SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr / 2);     // dutycycle/period = 50%
      if (nLcdOffMode)
         {
          if (nNavisType == NAVIS_TYPE_700)
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
          else
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr + 1); // L=100%
         }
      else
         {
          if (nNavisType == NAVIS_TYPE_700)
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr + 1); // L=100%
          else
              SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
         }
      if (nNavisType == NAVIS_TYPE_3800 && SysCheckLCD3800Mitsubishi())
          SysSetPWMPolarity(PWM_LCD_CHANNEL,0);
      else
          SysSetPWMPolarity(PWM_LCD_CHANNEL,1);

      SysSetPWMPreScale(PWM_KEY_CHANNEL,PWM1_PRE_SCALER);
      SysSetPWMPeriod(PWM_KEY_CHANNEL,dKeyDvdr);
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,dKeyDvdr / 2);     // dutycycle/period = 50%
      SysSetPWMPolarity(PWM_KEY_CHANNEL,1);

//    SysSetPWMPreScale(PWM_BUZ_CHANNEL,PWM2_PRE_SCALER);
//    SysSetPWMPeriod(PWM_BUZ_CHANNEL,dBuzDvdr);
//    SysSetPWMDutyCycle(PWM_BUZ_CHANNEL,dBuzDvdr / 2);     // dutycycle/period = 50%
//    SysSetPWMPolarity(PWM_BUZ_CHANNEL,1);
#endif
}
void  SysSetPWMClockPClkMode(DWORD dPclkMode)
{
#if defined(__POLLUX__)
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKENB;

      dTempX = dTempX & ~(1 <<  3);
      dTempX = dTempX |  (dPclkMode <<  3);

      G_pSysPWM->dCLKENB = dTempX;
#else                           // SPICA
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKENB;

      dTempX = dTempX & ~(1 <<  3);
      dTempX = dTempX |  (dPclkMode <<  3);

      G_pSysPWM->dCLKENB = dTempX;
#endif
}
void  SysSetPWMClockDivisorEnable(int nDisableEnableMode)
{
#if defined(__POLLUX__)
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKENB;

      if (nDisableEnableMode)
          dTempX = dTempX |  (1 <<  2);
      else
          dTempX = dTempX & ~(1 <<  2);

      G_pSysPWM->dCLKENB = dTempX;
#else                           // SPICA
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKENB;

      if (nDisableEnableMode)
          dTempX = dTempX |  (1 <<  2);
      else
          dTempX = dTempX & ~(1 <<  2);

      G_pSysPWM->dCLKENB = dTempX;
#endif
}
void  SysSetPWMClockSource(DWORD dClkSrc)
{
#if defined(__POLLUX__)
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKGEN;

      dTempX = dTempX & ~(3 <<  1);
      dTempX = dTempX |  (dClkSrc <<  1);

      G_pSysPWM->dCLKGEN = dTempX;
#else                           // SPICA
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKGEN;

      dTempX = dTempX & ~(7 <<  2);
      dTempX = dTempX |  (dClkSrc <<  2);

      G_pSysPWM->dCLKGEN = dTempX;
#endif
}
void  SysSetPWMClockDivisor(DWORD dDivisor)
{
#if defined(__POLLUX__)
      DWORD dTempX;

      if (dDivisor > 0)
          dDivisor--;

      dTempX = G_pSysPWM->dCLKGEN;

      dTempX = dTempX & ~(0x3f <<  4);
      dTempX = dTempX |  (dDivisor <<  4);

      G_pSysPWM->dCLKGEN = dTempX;
#else                           // SPICA
      DWORD dTempX;

      if (dDivisor > 0)
          dDivisor--;

      dTempX = G_pSysPWM->dCLKGEN;

      dTempX = dTempX & ~(0x3f <<  5);
      dTempX = dTempX |  (dDivisor <<  5);

      G_pSysPWM->dCLKGEN = dTempX;
#endif
}
void  SysSetPWMPreScale(int nIndex,HWORD wPreScaler)
{
#if defined(__POLLUX__)
      HWORD wTempX;
      int   nChannel,nShift;

      nChannel = (nIndex / 2);
      nShift   = (nIndex & 1) * 8;

      wTempX = G_pSysPWM->xLayer[nChannel].wPREPOL;

      wTempX = wTempX & ~(0x7f << nShift);
      wTempX = wTempX |  ((wPreScaler - 1) << nShift);

      G_pSysPWM->xLayer[nChannel].wPREPOL = wTempX;
#else                           // SPICA
      HWORD wTempX;
      int   nChannel,nShift;

      nChannel = (nIndex / 2);
      nShift   = (nIndex & 1) * 8;

      wTempX = G_pSysPWM->xLayer[nChannel].wPREPOL;

      wTempX = wTempX & ~(0x7f << nShift);
      wTempX = wTempX |  ((wPreScaler - 1) << nShift);

      G_pSysPWM->xLayer[nChannel].wPREPOL = wTempX;
#endif
}
void  SysSetPWMPolarity(int nIndex,int nByPassMode)
{
#if defined(__POLLUX__)
      HWORD wTempX;
      int   nChannel,nShift;

      nChannel = (nIndex / 2);

      if (nIndex & 1)
          nShift = 15;
      else
          nShift =  7;

      wTempX = G_pSysPWM->xLayer[nChannel].wPREPOL;

      wTempX = wTempX & ~(1 << nShift);

      if (nByPassMode)
          wTempX = wTempX |  (1 << nShift);
      else
          wTempX = wTempX & ~(1 << nShift);

      G_pSysPWM->xLayer[nChannel].wPREPOL = wTempX;
#else                           // SPICA
      HWORD wTempX;
      int   nChannel,nShift;

      nChannel = (nIndex / 2);

      if (nIndex & 1)
          nShift = 15;
      else
          nShift =  7;

      wTempX = G_pSysPWM->xLayer[nChannel].wPREPOL;

      wTempX = wTempX & ~(1 << nShift);

      if (nByPassMode)
          wTempX = wTempX |  (1 << nShift);
      else
          wTempX = wTempX & ~(1 << nShift);

      G_pSysPWM->xLayer[nChannel].wPREPOL = wTempX;
#endif
}
void  SysSetPWMPeriod(int nIndex,HWORD wPeriod)
{
#if defined(__POLLUX__)
      int   nChannel;

      nChannel = (nIndex / 2);

      G_pSysPWM->xLayer[nChannel].wPERIOD[nIndex % 2] = wPeriod;
#else                           // SPICA
      int   nChannel;

      nChannel = (nIndex / 2);

      G_pSysPWM->xLayer[nChannel].wPERIOD[nIndex % 2] = wPeriod;
#endif
}
HWORD SysGetPWMPeriod(int nIndex)
{
#if defined(__POLLUX__)
      int   nChannel;

      nChannel = (nIndex / 2);

      return(G_pSysPWM->xLayer[nChannel].wPERIOD[nIndex % 2]);
#else                           // SPICA
      int   nChannel;

      nChannel = (nIndex / 2);

      return(G_pSysPWM->xLayer[nChannel].wPERIOD[nIndex % 2]);
#endif
}
void  SysSetPWMDutyCycle(int nIndex,HWORD wDuty)
{
#if defined(__POLLUX__)
      int   nChannel;

      nChannel = (nIndex / 2);

      G_pSysPWM->xLayer[nChannel].wDUTY[nIndex % 2] = wDuty;
#else                           // SPICA
      int   nChannel;

      nChannel = (nIndex / 2);

      G_pSysPWM->xLayer[nChannel].wDUTY[nIndex % 2] = wDuty;
#endif
}
HWORD SysGetPWMDutyCycle(int nIndex)
{
#if defined(__POLLUX__)
      int   nChannel;

      nChannel = (nIndex / 2);

      return(G_pSysPWM->xLayer[nChannel].wDUTY[nIndex % 2]);
#else                           // SPICA
      int   nChannel;

      nChannel = (nIndex / 2);

      return(G_pSysPWM->xLayer[nChannel].wDUTY[nIndex % 2]);
#endif
}

