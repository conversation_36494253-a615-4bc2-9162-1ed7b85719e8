/*...........................................................................*/
/*.                  File Name : MEMPCX.HPP                                 .*/
/*.                                                                         .*/
/*.                       Date : 2004.10.16                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "screen.hpp"

#ifndef  __MEMPCX_HPP
#define  __MEMPCX_HPP

//-----------------------------------------------------------------------------
typedef  struct  __PACK__ {
        BYTE  Red;
        BYTE  Green;
        BYTE  Blue;
}COLOR_REGISTER;
//-----------------------------------------------------------------------------
typedef  struct  __PACK__ {
        BYTE  Header;
        BYTE  Version;
        BYTE  Encode;
        BYTE  BitPerPix;
        WORD  X1;
        WORD  Y1;
        WORD  X2;
        WORD  Y2;
        WORD  Hres;
        WORD  Vres;
}PCX_HEADER;
//-----------------------------------------------------------------------------
typedef  struct  __PACK__ {
        BYTE  Vmode;
        BYTE  NumOfPlanes;
        WORD  BytesPerLine;
        WORD  PalInfo;
        WORD  ScannerX;
        WORD  ScannerY;
        BYTE  Unused[54];
}PCX_INFO;
//-----------------------------------------------------------------------------
typedef  struct  __PACK__ {
        PCX_HEADER      PcxHeader;
        COLOR_REGISTER  Palettes[16];
        PCX_INFO        PCXInfo;
}PCX_FILE;
//-----------------------------------------------------------------------------

class cMEMPCX
{
   private:
      cSCREEN *m_pScreen;
	  COLORT *m_pvScreen;

   public:

   public:
      cMEMPCX(cSCREEN *pScreen);
      virtual ~cMEMPCX(void);
   public:
      int   GetPcxScreen(BYTE *pPcxData);
      int   GetPcxScreenMono(BYTE *pPcxData);
      int   GetPcxScreen65535(BYTE *pPcxData);
	  int   GetPcxScreen65535x640(BYTE *pPcxData);
	  int GetPcxScreen16Bit800(BYTE *pPcxData);	
      BYTE *PcxSetData(BYTE *pTarget,void *pSource,int nSize);
};

#endif

