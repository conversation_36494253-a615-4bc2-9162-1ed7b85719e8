/*...........................................................................*/
/*.                  File Name : SDCARDDRV.C                                .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.14                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysGPIO.h"
#include "SysSDCD.h"
#include "SdCardDRV.h"

#include <string.h>
#include <stdlib.h>

//=============================================================================
static xSDCARD *G_pSdCARD = NULL;
//=============================================================================

//=============================================================================
void  SdCardSetSdCardPtr(void *pSdCard)
{
      G_pSdCARD = (xSDCARD *)pSdCard;
}
void *SdCardGetSdCardPtr(void)
{
      return(G_pSdCARD);
}
//=============================================================================
DWORD SdCardReadBlockData(DWORD dBlockNo,void *pReadAddr,DWORD dReadSize)
{
      DWORD dSdStatus;

      dSdStatus = SysSdCardReadBlockData(dBlockNo,pReadAddr,dReadSize);
      if (dSdStatus == SDCD_STATUS_NOERROR)
          return(SDCARD_ERROR_NO_ERROR);
      return(SDCARD_ERROR_GENERAL);
}
DWORD SdCardWriteBlockData(DWORD dBlockNo,void *pWritedAddr,DWORD dWriteSize)
{
      DWORD dSdStatus;

      dSdStatus = SysSdCardWriteBlockData(dBlockNo,pWritedAddr,dWriteSize);
      if (dSdStatus == SDCD_STATUS_NOERROR)
          return(SDCARD_ERROR_NO_ERROR);
      return(SDCARD_ERROR_GENERAL);
}
DWORD SdCardSetUpAndDetect(void)
{
      DWORD dSdStatus;

      dSdStatus = SysSdCardSetUpAndDetect();
      if (dSdStatus == SDCD_STATUS_NOERROR)
          return(SDCARD_ERROR_NO_ERROR);
      return(SDCARD_ERROR_GENERAL);
}
DWORD SdCardSendSelectCard(void)
{
      DWORD dStatus;

      dStatus = SysSendSdCardCommand(SDCD_CMD_SEL_DESELECT_CARD_07,SysGetSdCardRCA());
      return(dStatus);
}
DWORD SdCardSendPullUpMode(int nDisableEnableMode)
{
      DWORD dStatus;

      dStatus = SysSendSdCardAppCommand(SDCD_ACMD_SET_CLR_CARD_DETECT_42,nDisableEnableMode);
      return(dStatus);
}
DWORD SdCardSendBusWidth(DWORD dBusWidth)
{
      DWORD dStatus;
      DWORD dWidthX;

      if (dBusWidth == SDCD_DATA_WIDTH_4BIT)
          dWidthX = 4;
      else
          dWidthX = 1;
      dStatus = SysSendSdCardAppCommand(SDCD_ACMD_SET_BUS_WIDTH_06,dWidthX >> 1);
      if (dStatus != SDCD_STATUS_NOERROR)
          return(dStatus);
      SysSetSdCdDataBusWidth(dBusWidth);
      return(SDCD_STATUS_NOERROR);
}
DWORD SdCardSendBlockLength(DWORD dBlockLength)
{
      DWORD dStatus;
	
      dStatus = SysSendSdCardCommand(SDCD_CMD_SET_BLOCK_LEN_16,dBlockLength);
      if (dStatus != SDCD_STATUS_NOERROR)
          return(dStatus);
      SysSetSdCdBlockSize(dBlockLength);
      return(SDCD_STATUS_NOERROR);
}
//=============================================================================
DWORD SdCardGetStartingHead(int nPartitionNo)
{
      return(G_pSdCARD->m_xMBR[nPartitionNo].bStartHead);
}
DWORD SdCardGetStartingSect(int nPartitionNo)
{
      return(G_pSdCARD->m_xMBR[nPartitionNo].bStartSector & 0x3f);
}
DWORD SdCardGetStartingCyli(int nPartitionNo)
{
      DWORD dTemp;

      dTemp = G_pSdCARD->m_xMBR[nPartitionNo].bStartSector & 0xc0;
      dTemp = dTemp << 2;
      dTemp = dTemp + G_pSdCARD->m_xMBR[nPartitionNo].bStartCyl;
      return(dTemp);
}
DWORD SdCardGetEndingHead(int nPartitionNo)
{
      return(G_pSdCARD->m_xMBR[nPartitionNo].bEndHead);
}
DWORD SdCardGetEndingSect(int nPartitionNo)
{
      return(G_pSdCARD->m_xMBR[nPartitionNo].bEndSector & 0x3f);
}
DWORD SdCardGetEndingCyli(int nPartitionNo)
{
      DWORD dTemp;

      dTemp = G_pSdCARD->m_xMBR[nPartitionNo].bEndSector & 0xc0;
      dTemp = dTemp << 2;
      dTemp = dTemp + G_pSdCARD->m_xMBR[nPartitionNo].bEndCyl;
      return(dTemp);
}
DWORD SdCardGetRelativeSectors(int nPartitionNo)
{
      DWORD dTemp;

      dTemp = G_pSdCARD->m_xMBR[nPartitionNo].bRelSector3;
      dTemp = (dTemp << 8) + G_pSdCARD->m_xMBR[nPartitionNo].bRelSector2;
      dTemp = (dTemp << 8) + G_pSdCARD->m_xMBR[nPartitionNo].bRelSector1;
      dTemp = (dTemp << 8) + G_pSdCARD->m_xMBR[nPartitionNo].bRelSector0;
      return(dTemp);
}
DWORD SdCardGetTotalSectors(int nPartitionNo)
{
      DWORD dTemp;

      dTemp = G_pSdCARD->m_xMBR[nPartitionNo].bTotSector3;
      dTemp = (dTemp << 8) + G_pSdCARD->m_xMBR[nPartitionNo].bTotSector2;
      dTemp = (dTemp << 8) + G_pSdCARD->m_xMBR[nPartitionNo].bTotSector1;
      dTemp = (dTemp << 8) + G_pSdCARD->m_xMBR[nPartitionNo].bTotSector0;
      return(dTemp);
}
DWORD SdCardGetLBAbyCHS(int nPartitionNo,int nCyl,int nHead,int nSector)
{
      DWORD dLBA;
      DWORD dNoHead;

      dNoHead = G_pSdCARD->m_xMBR[nPartitionNo].bEndHead + 1;
      dLBA = (nCyl * dNoHead + nHead) * G_pSdCARD->m_xMBR[nPartitionNo].bEndSector;
      dLBA = dLBA + nSector - 1;
      return(dLBA);
}
int   SdCardGetWriteBlockSizeFromCSD(void)
{
      int  nSize;

      nSize = 512;
//    if (G_pSdCARD->m_xCSD.bWriteBlLen ==  9) nSize =  512;
//    if (G_pSdCARD->m_xCSD.bWriteBlLen == 10) nSize = 1024;
//    if (G_pSdCARD->m_xCSD.bWriteBlLen == 11) nSize = 2048;
      return(nSize);
}
int   SdCardGetBlocksPerSectorFromCSD(void)
{
      return(G_pSdCARD->m_xCSD.bEraseSectSize + 1);
}
int   SdCardGetReadBlockSizeFromCSD(void)
{
      int  nSize;

      nSize = 512;
//    if (G_pSdCARD->m_xCSD.bReadBlLen ==  9) nSize =  512;
//    if (G_pSdCARD->m_xCSD.bReadBlLen == 10) nSize = 1024;
//    if (G_pSdCARD->m_xCSD.bReadBlLen == 11) nSize = 2048;
      return(nSize);
}
//=============================================================================
void  SdCardSetCardChangStatus(int nChanged)
{
      G_pSdCARD->m_nCardChanged = nChanged;
//    if (nChanged)
//        SysSetSdCardLastChangeStatus(1);
}
int   SdCardGetCardChangStatus(void)
{
      return(G_pSdCARD->m_nCardChanged);
}
void  SdCardSetCardInsertStatus(int nInserted)
{
      G_pSdCARD->m_nCardInserted = nInserted;
}
int   SdCardGetCardInsertStatus(void)
{
      return(G_pSdCARD->m_nCardInserted);
}
//=============================================================================

