/*...........................................................................*/
/*.                  File Name : SYSGPIO.H                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSGPIO_H__
#define  __SYSGPIO_H__

//=============================================================================
#define  GPIO_MODE_GPIO                                0
#define  GPIO_MODE_ALT1                                1
#define  GPIO_MODE_ALT2                                2
#define  GPIO_MODE_NONE                                3
#define  GPIO_GPIO_IN                                  0    // input-mode
#define  GPIO_GPIO_OUT                                 1    // output-mode
#define  GPIO_EVNT_LOW                                 0    // Low
#define  GPIO_EVNT_HIGH                                1    // High
#define  GPIO_EVNT_FALL                                2    // Falling Edge
#define  GPIO_EVNT_RISE                                3    // Rising  Edge
#define  GPIO_INT_DISABLE                              0    // Interrupt Disable
#define  GPIO_INT_ENABLE                               1    // Interrupt Enable
#define  GPIO_PULL_DISABLE                             0    // Pull-Up   Disable
#define  GPIO_PULL_ENABLE                              1    // Pull-Up   Enable
//-----------------------------------------------------------------------------
#define  TCH_INT_BIT_NO                         31    // PORT-C
#define  SPD_INT_BIT_NO                         26    // PORT-C

#define  LCD_VGP_ON_BIT_ON                      15    // PORT-D
#define  LCD_STB_ON_BIT_ON                      14    // PORT-D
#define  LCD_BL_ON_BIT_ON                       13    // PORT-D

#define  VID_INT_BIT_NO                          5    // PORT-D
#define  LAN_INT_BIT_NO                          4    // PORT-D
#define  CAN_INT_BIT_NO                          3    // PORT-D

#define  RX_LED_BIT_NO				       2	// PORT-D	
#define  TX_LED_BIT_NO					27	// PORT-C

#define  SD1_WP_BIT_NO                           7    // PORT-C
#define  SD1_CD_BIT_NO                           6    // PORT-D

#define  SD0_WP_BIT_NO                           2    // PORT-D
#define  SD0_CD_BIT_NO                           1    // PORT-D

#define  PWR_KEY_SN_BIT_NO                       3    // PORT-C
#define  PWR_VPASS_SN_BIT_NO                     4    // PORT-C

#define  SPI_FRM0_BIT_NO                         7    // PORT-C
#define  SPI_CLK0_BIT_NO                         8    // PORT-C
#define  SPI_RXD0_BIT_NO                         9    // PORT-C
#define  SPI_TXD0_BIT_NO                        10    // PORT-C

#define  FPGA_TDO_BIT_NO                        30    // PORT-B
#define  FPGA_TCK_BIT_NO                        12    // PORT-C
#define  FPGA_TMS_BIT_NO                        13    // PORT-C
#define  FPGA_TDI_BIT_NO                        14    // PORT-C

#define  LVDS_PWR_DN_BIT_NO                     30    // PORT-C
#define  EXT_ALM_OVR_BIT_NO                     28    // PORT-C
#define  EXT_ALM_DRB_BIT_NO                     27    // PORT-C
#define  PWR_5V_CHK_BIT_NO                      25    // PORT-C

#define  SYS_CNFG5_BIT_NO                       22    // PORT-C
#define  SYS_CNFG4_BIT_NO                       21    // PORT-C
#define  SYS_CNFG3_BIT_NO                       20    // PORT-C
#define  SYS_CNFG2_BIT_NO                       19    // PORT-C
#define  SYS_CNFG1_BIT_NO                       18    // PORT-C
#define  SYS_CNFG0_BIT_NO                       17    // PORT-C

#define  FISH_50V_SHDN                          31    // PORT-B

#define  BZR_GPIO_BIT_NO                         6    // PORT-B
#define  RMC_PPM_IN_BIT_NO                       0    // PORT-B  (��a��w��e?)

#define  PWR_GPIO_BIT_NO                         3    // PORT-C

#define  LED_DIMMER_BIT_NO				 6	// PORT-D	
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SysSetGPIOA(void);
void  SysSetGPIOB(void);
void  SysSetGPIOC(void);
void  SysSetGPIOD(void);
void  SysSetGPIOE(void);

void  SysSetGPIOx(xSYS_GPIO *pSysGPIO,UCHAR vMode[32][8]);
void  SysSetGPIOxBitData(xSYS_GPIO *pSysGPIO,int nBitNo,int nBitData);
DWORD SysGetGPIOxBitData(xSYS_GPIO *pSysGPIO,int nBitNo);
void  SysSetGPIOxBitMode(xSYS_GPIO *pSysGPIO,int nBitNo,int nBitInOut);
DWORD SysGetGPIOxData(xSYS_GPIO *pSysGPIO);
DWORD SysGetGPIOA(void);
DWORD SysGetGPIOB(void);
DWORD SysGetGPIOC(void);
DWORD SysGetGPIOD(void);
DWORD SysGetGPIOE(void);

DWORD SysGetGPIOxDetectBitData(xSYS_GPIO *pSysGPIO,int nBitNo);
void  SysClearGPIOxDetectBitData(xSYS_GPIO *pSysGPIO,int nBitNo);
void  SysSetSPI0FRM(int nBitData);
void  SysSetSPI0CLK(int nBitData);
void  SysSetSPI0TXD(int nBitData);
DWORD SysGetSPI0RXD(void);

void  SysSetFpgaTCK(int nBitData);
void  SysSetFpgaTMS(int nBitData);
void  SysSetFpgaTDI(int nBitData);
DWORD SysGetFpgaTDO(void);

#ifdef  __cplusplus
}
#endif

#endif

