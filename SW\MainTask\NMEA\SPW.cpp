#include "SPW.hpp"

CSpw::CSpw() : CSentence()
{
}
    
CSpw::CSpw(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CSpw::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_SPW;
}

/******************************************************************************
 * 
 * 
 * SPW - 
 *
 * $--SPW,A,xx,a,b,c,d,*hh<CR><LF>
 *        |  | | | | |
 *        1  2 3 4 5 6
 *
 * 1. 'A'
 * 2. Code
 * 3. CS-a
 * 4. CS-b
 * 5. CS-c
 * 6. CS-d
 *
 ******************************************************************************/
void CSpw::Parse()
{
	GetFieldString(2, (char *)m_szCodeCCC);
	m_szCodeCCC[2] = 0x00;
	m_szCodeCCC[3] = 0x00;
}

void CSpw::GetPlainText(char *pszPlainText)
{
	pszPlainText[0] = '\0';
}
