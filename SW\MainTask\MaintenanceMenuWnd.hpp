#include "Wnd.hpp"

#ifndef __MAINTENANCE_MENU_WND_HPP__
#define __MAINTENANCE_MENU_WND_HPP__

class CMaintenanceMenuWnd : public CWnd {
private:
	int m_nSelNum;

public:
	CMaintenanceMenuWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	void DrawWnd(BOOL bRedraw=1/*TRUE*/);
	void DrawSubMenu(int nSelNum);
	void OnKeyEvent(int nKey, DWORD nFlags=0);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	
	void SetFocus(int nFocus)   { m_nFocus = nFocus; }
	void SetSelNum(int nSelNum) { m_nSelNum = nSelNum; }
	int  GetSelNum()            { return m_nSelNum;    }

	void ProgramDownload();
	void UpdateTransponderPrg();
	void GlobalPassWord();
	void SystemReset();
};

#endif
