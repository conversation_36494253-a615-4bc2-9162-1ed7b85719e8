#pragma once


#include <vector>
#include "SamNavConst.h"
#include <string>

using namespace std;



class Nav2DInterfaceImpl;

namespace Navionics_2D
{
	class Core2D;

	class ImageBuffer;
}

namespace Navionics
{
	class NavMutex;
}

class SamyungSearcher;

class ObjectFormatter;

class NavDisplay
{
public:

	NavDisplay (unsigned int inWidth, unsigned int inHeight, unsigned int inCacheByteSize);

	~NavDisplay ();

	bool FileSystemAdded(std::string& Path,unsigned char* p_Key,int KeyLength);

	bool FileSystemRemoved(std::string& Path);

	const char*	GetNvsLibraryVersion();

	int		GetNvsChartInfo(xCHARTFILEINFO* pChartData,int nDataSize);

	bool	IsValidPosition(int center_x,int center_y,float range);

	bool	NvsConvertNavMMToDegrees(int *pinMMLat,int *pinMMLon,double *poutDegLat,double *poutDegLon);

	bool	NvsConvertDegreesToNavMM(double *pinDegLat,double *pinDegLon,int *poutMMLat,int *poutMMLon);

	//////////////////////////////////////////////////////////////////////////////////
	//!	SetPosMM
	//!	
	//!	output: 
	//!	
	//!	pDownLat:		Bottom Coordinate in MM of rect
	//!	pLeftLon:		Left Coordinate in MM of rect
	//!	pUpLat:		top Coordinate in MM of rect
	//!	pRightLon:	right Coordinate in MM of rect
	//!	
	//////////////////////////////////////////////////////////////////////////////////
	bool SetPosMM(int center_x,int center_y,float range,
		float i_RotationAngle,int i_RotationPivot_x,int i_RotationPivot_y,int *pDownLat,int *pLeftLon,int *pUpLat,int *pRightLon); 

	//////////////////////////////////////////////////////////////////////////////////
	//!	SetPixelPos
	//!	
	//!	output: 
	//!	
	//!	pDownLat:		Bottom Coordinate in MM of rect
	//!	pLeftLon:		Left Coordinate in MM of rect
	//!	pUpLat:		top Coordinate in MM of rect
	//!	pRightLon:	right Coordinate in MM of rect
	//!	
	//////////////////////////////////////////////////////////////////////////////////
	bool SetPixelPos(int center_x,int center_y,float range,
		float i_RotationAngle,int i_RotationPivot_x,int i_RotationPivot_y,int *pDownLat,int *pLeftLon,int *pUpLat,int *pRightLon); 

	//////////////////////////////////////////////////////////////////////////////////
	//!	GetLatLonByPixelPos
	//!	
	//!	input:
	//!	
	//!	nPixelX,nPixelY:	pixel position
	//!	
	//!	output: 
	//!	
	//!	pLat:		Y Coordinate in MM of input point
	//!	pLon:		X Coordinate in MM of input point
	//!
	//////////////////////////////////////////////////////////////////////////////////
	bool GetLatLonByPixelPos(int nPixelY,int nPixelX,int *pLat,int *pLon);

	//////////////////////////////////////////////////////////////////////////////////
	//!	GetPixelPosByLatLon
	//!
	//!	input:
	//!	
	//!	nLat:		Y Coordinate in MM of input point
	//!	nLon:		X Coordinate in MM of input point
	//!
	//!	output:
	//!
	//!	pPixelY,pPixelX:	pixel position
	//!
	//////////////////////////////////////////////////////////////////////////////////
	bool GetPixelPosByLatLon(int nLat,int nLon,int *pPixelY,int *pPixelX);

	//////////////////////////////////////////////////////////////////////////////////
	//!	ScreenUpdateNeeded
	//!
	//!	encoding:
	//!	if true, the image returned by NavDisplay::Draw() contains an updated image to be put on screen
	//!
	//////////////////////////////////////////////////////////////////////////////////
	bool	ScreenUpdateNeeded(int& o_IsCompleted);

	//////////////////////////////////////////////////////////////////////////////////
	//!	Draw method.
	//!
	//!	return:
	//!
	//!	pointer to the image to be put on the screen.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	void* Draw();

	//////////////////////////////////////////////////////////////////////////////////
	//!	AbortNvsDrawChart
	//!
	//!	idle
	//!
	//////////////////////////////////////////////////////////////////////////////////
	void AbortNvsDrawChart();

	//////////////////////////////////////////////////////////////////////////////////
	//!	SetWidgetWindowSize
	//!
	//!	Set the portion of the returned image buffer that is copied on the screen.
	//!	The inputs are in pixel position.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	bool SetWidgetWindowSize(int LowerLeft_x,int LowerLeft_y,int UpperRight_x,int UpperRight_y);
 	
	void  SetNvsDepthUnit(int nUnit);

	bool  SetNvsRotationAngle(int nDegree,bool nChartRedrawMode);

	void  SetNvsPaletteMode(int nPaletteMode);

	void  SetNvsMixingLevelsMode(bool nMode);

	//////////////////////////////////////////////////////////////////////////////////
	//!	SetNvsAntiClutterMode
	//!
	//!	Disable the minor texts on the cartography to be displayed.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	void  SetNvsAntiClutterMode(bool nMode);

	void  SetNvsValueAddedDataMode(bool nMode);

	void  SetNvsChartBoundariedMode(bool nMode);

	void  SetNvsTextIconSize(int nSizeMode);

	//////////////////////////////////////////////////////////////////////////////////
	//!	SetNvsTextNameMode
	//!
	//!	Disable all texts on the cartography to be displayed.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	void  SetNvsTextNameMode(bool nMode);

	void  SetNvsNavAidsLightMode(int nMode);

	void  SetNvsNavAidsSymbolMode(int nMode);

	void  SetNvsSpotSoundingMode(bool nMode);

	void  SetNvsSoundingMaxValue(int nValue,int nDepthUnit);

	void  SetNvsTidalStreamMode(bool nMode);

	void  SetNvsSecurityNavMode(bool nMode,int nValue,int nDepthUnit);

	void  SetNvsAttentionAreasMode(bool nMode);

	void  SetNvsWaterFeaturesMode(bool nMode);

	void  SetNvsLandFeaturesMode(bool nMode);

	void  SetNvsObjectDepthMode(bool nMode);

	void  SetNvsLandElevationMode(bool nMode);

	//////////////////////////////////////////////////////////////////////////////////
	//!	FindNvsQuickInfo
	//!
	//!	input:
	//!
	//!	nMercX,nMercY:	search center expressed in Mercator Meters
	//!fRange:			search radius expressed in Nautical Miles
	//!	nDataSize:		Maximum number of objects to be returned
	//!	
	//!	Output:
	//!	
	//!pObjectData:     vector of xOBJECTINFO to be filled with query results 
	//!						(already allocated  with nDataSize elements by caller)
	//!
	//!	Return:
	//!	
	//!number of objects filled in pObjectData.
	//!	
	//////////////////////////////////////////////////////////////////////////////////
	int   FindNvsQuickInfo(int nMercX,int nMercY,float fRange,xOBJECTINFO *pObjectData,int nDataSize);

	//////////////////////////////////////////////////////////////////////////////////
	//!	FindNvsPortName
	//!
	//!	Find all the ports (optionally by name) in a given radius
	//!
	//!	input:
	//!
	//!	nMercX,nMercY:	search center expressed in Mercator Meters
	//!	fRange:			search radius expressed in Nautical Miles
	//!	nDataSize:		Maximum number of objects to be returned
	//!	pNameToFind:		Optional token string to find the ports by name (void string means no filter)
	//!
	//!	Output:
	//!
	//!	pNameData:		vector of xPORTNAME to be filled with port objects found
	//!					(already allocated  with nSize elements by caller)
	//!
	//!	Return:
	//!
	//!	number of objects filled in pNameData.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	int  FindNvsPortName(int nMercX,int nMercY,float fRange,char *pNameToFind,xPORTNAME *pNameData,int nDataSize);

	//////////////////////////////////////////////////////////////////////////////////
	//!	FindNvsTide
	//!
	//!	Find all the tide station in a given radius
	//!
	//!	input:
	//!
	//!	nMercX,nMercY:	search center expressed in Mercator Meters
	//!	fRange:			search radius expressed in Nautical Miles
	//!	nDataSize:		Maximum number of objects to be returned
	//!
	//!	Output:
	//!
	//!	pNameData:		vector of xPORTNAME to be filled with tide objects found
	//!					(already allocated  with nSize elements by caller)
	//!
	//!	Return:
	//!
	//!	number of objects filled in pNameData.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	int  FindNvsTide(int nMercX,int nMercY,float fRange,xPORTNAME *pNameData,int nDataSize);

	//////////////////////////////////////////////////////////////////////////////////
	//!	FindNvsPortService
	//!
	//!	Find all the tide station in a given radius
	//!
	//!	input:
	//!
	//!	nMercX,nMercY:		search center expressed in Mercator Meters
	//!	fRange:				search radius expressed in Nautical Miles
	//!	nDataSize:			Maximum number of objects to be returned
	//!	nCategoryString:	String of Service / object category to be searched 
	//!						(one of the category strings returned by GetNvsCategoryLabels())
	//!
	//!	Output:
	//!	
	//!	pNameData:		vector of xPORTNAME to be filled with port objects found containing / belonging to the given  Service / object category 
	//!						(already allocated  with nSize elements by caller)
	//!
	//!	Return:
	//!
	//!	number of objects filled in pNameData.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	int FindNvsPortService(char* nCategoryString,int nMercX,int nMercY,float fRange,xPORTNAME *pNameData,int nDataSize);
	
	/////////////
	//
	//									WARNING!
	//
	//		THIS METHOD HAS BEEN KEPT ONLY FOR TESTING PURPOSES, IT WILL BE REMOVED SOON
	//							PLEASE USE THE METHOD ABOVE INSTEAD
	//
	int FindNvsPortService(int nObjID,int nMercX,int nMercY,float fRange,xPORTNAME *pNameData,int nDataSize);
	//
	//////////////////////////////////////////////////////////////////////////////////

	//////////////////////////////////////////////////////////////////////////////////
	//!		GetNvsTideData
	//!
	//!	Returns the tide sample of the a tide station given its URL.
	//!
	//!	input:
	//!
	//!	pURL:							Object URL of tide station.
	//!	nUTCYear,nUTCMonth,nUTCDay:		UTC date required.
	//!	nPointPerHour:					Samples per Hour required.
	//!
	//!	Output:
	//!
	//!	pData:							Vector of tide samples.
	//!									(already allocated with 24*nPointPerHour elements by caller)
	//!	
	//!	Return:
	//!	
	//!	number of objects filled in pData (should always equal to 24*nPointPerHour, 0 means tide info cannot be retrieved). 
	//!	
	//////////////////////////////////////////////////////////////////////////////////
	int  GetNvsTideData(char* pURL,int nUTCYear,int nUTCMonth, int nUTCDay,float* pData,int nPointPerHour);

	//////////////////////////////////////////////////////////////////////////////////
	//!		GetNvsObjectDetailedInfo
	//!
	//!	It create and populate a tree structure (composed by xOBJECTDETAIL nodes) of detailed information
	//! of a object given its URL.
	//!
	//!	input:
	//!
	//!	pURL:			Object URL of object the user require detailed information.
	//!	Category:		Obsolete / not used.
	//!
	//!	Output:
	//!	
	//!	Return:
	//!	
	//!	Pointer to a xOBJECTDETAIL structure which is the father node of the info tree.
	//!	
	//////////////////////////////////////////////////////////////////////////////////
	xOBJECTDETAIL* GetNvsObjectDetailedInfo(char* pURL,int Category);

	//////////////////////////////////////////////////////////////////////////////////
	//!		GetNvsCategoryLabels
	//!
	//!	input:
	//!
	//!	nSize:			size of outLabel input vector
	//!
	//!	Output:
	//!	
	//!	outLabel:     vector of xCATEGORYLABEL to be filled with category info 
	//!						(already allocated  with nSize elements by caller)
	//!	
	//!	Return:
	//!	
	//!	number of objects filled in outLabel.
	//!
	//////////////////////////////////////////////////////////////////////////////////
	int GetNvsCategoryLabels( xCATEGORYLABEL* outLabel,int nSize); 
	
	
	void  SetNvsDemoMode(bool nMode);


public:
	
	unsigned char ToDepthInMeters(int value, int DepthUnit);

	unsigned int m_Width;

	unsigned int m_Height;

	struct FileInfo
	{
		std::string	mFileName;

		std::string	mChartName;

		std::string	mEditionDate;

		std::string	mEditionNumber;
	};

	struct DataSet
	{
		std::string BasePath;
		std::vector<FileInfo> OpenedNv2s;
	};

	FileInfo				m_BaseMapInfo;

	std::vector<DataSet> m_MountedDataSets;

	double m_west;
	double m_south;
	double m_east;
	double m_north;

	int dumped;

	bool m_SomethingChanged;

	bool m_FrameCompleted;

	int m_CurrentDepthUnit;

	unsigned char m_CurrentSafetyDepthMeterValue;

	int m_CurrentSpeedUnit;

	int m_CurrentPaletteMode;

	bool	m_AntiClutterEnabled;

	bool	m_AnnotationsEnabled;

	std::string m_CurrentLanguage;

	Nav2DInterfaceImpl*  m_Nav2DInterfaceImpl;

	Navionics_2D::Core2D* m_Core2D;

	SamyungSearcher*		m_searcher;

	ObjectFormatter*		m_ObjFormatter;

	std::string				m_version;

	friend class Nav2DInterfaceImpl;
};
