#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "ComboCtrl.hpp"
#include "UniComboCtrl.hpp"

#ifndef __VOYAGE_DATA_WND_HPP__
#define __VOYAGE_DATA_WND_HPP__

#define MAX_SHIP_TYPE_ITEM_CNT  70
#define MAX_SHIP_TYPE_STR  	70

class CVoyageDataWnd : public CWnd {
	protected:
		enum {
			FOCUS_DEST = 0,
			FOCUS_ETA_TIME,
			FOCUS_SHIP_CARGO_TYPE,
			FOCUS_SHIP_STATUS,
			FOCUS_DRAUGHT,
			FOCUS_PERSONS,
			FOCUS_LAST = FOCUS_PERSONS
		};

		CEditCtrl  *m_pDestEdit;
		CEditCtrl  *m_pETATimeEdit;
		CUniComboCtrl *m_pShipCargoType;
		CEditCtrl  *m_pPersonsEdit;
		CEditCtrl  *m_pDraught;
		CUniComboCtrl *m_pShipStatusCombo;

		int         m_nFocus;

		HWORD		*m_pUniStrShipType[MAX_SHIP_TYPE_ITEM_CNT];
		
		BOOL CheckETA(BYTE *pszETA);
#ifdef EN_61993_ED3		
		void ChangeExtAntPosToNormalAntPos();
#endif	// End of (EN_61993_ED3)

	private:
		void InitShipTypeItem();
		void InitNavStatusItem();
		void ReloadShipTypeItem();
		void ReloadNavStatusItem();
		int ConvertShipTypeNumToCmbIdx(int nShipType);
		int ConvertShipTypeCmbIdxToNum(int nIdx);
	
	public:
		CVoyageDataWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void OnCursorEvent(int nState);
		virtual void OnActivate();
		void SetEditMode(int nMode);
		void SetControls(int nMode);
		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		void InitVoyageData();
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		int  GetFocus()           { return m_nFocus; }
		void ComboCollapse() {
			m_pShipStatusCombo->Collapse();
			m_pShipCargoType->Collapse();
		}
};

#endif

