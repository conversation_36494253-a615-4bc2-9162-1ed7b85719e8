#ifndef __AIS_COLOR_H__
#define __AIS_COLOR_H__

// 2 Bytes Color
#ifndef RGB
#define RGB(r,g,b)		((COLORT)(((COLORT)((BYTE)r>>3))<<11) | (((COLORT)((BYTE)g>>2))<<5) | ((BYTE)b>>3))
#endif //~ RGB

const COLORT WHITE					= RGB(0xFF, 0xFF, 0xFF);
const COLORT BLACK					= RGB(0x00, 0x00, 0x00);
const COLORT RED					= RGB(0xFF, 0x00, 0x00);
const COLORT GREEN					= RGB(0x00, 0x80, 0x00);
const COLORT BLUE					= RGB(0x00, 0x00, 0xFF);
const COLORT YELLOW					= RGB(0xFF, 0xFF, 0x00);
const COLORT MAGENTA				= RGB(0xFF, 0x00, 0xFF);
const COLORT CYAN					= RGB(0x00, 0xFF, 0xFF);
const COLORT ORANGE					= RGB(0xFF, 0xA5, 0x00);
const COLORT PINK					= RGB(0xFF, 0xC0, 0xCB);
const COLORT GRAY					= RGB(0x80, 0x80, 0x80);

const COLORT LIGHTBLUE				= RGB(0xAD, 0xD8, 0xE6);
const COLORT LIGHTGREEN				= RGB(0x90, 0xEE, 0x90);
const COLORT LIGHTCYAN				= RGB(0xE0, 0xFF, 0xFF);
const COLORT LIGHTPINK				= RGB(0xFF, 0xB6, 0xC1);
const COLORT LIGHTYELLOW			= RGB(0xFF, 0xFF, 0xE0);
const COLORT LIGHTGREY				= RGB(0xA0, 0xA0, 0xA0);

const COLORT DARKRED				= RGB(0x8B, 0x00, 0x00);
const COLORT DARKGREEN				= RGB(0x00, 0x64, 0x00);
const COLORT DARKBLUE				= RGB(0x00, 0x00, 0x8B);
const COLORT DARKMAGENTA			= RGB(0x8B, 0x00, 0x8B);
const COLORT DARKCYAN				= RGB(0x00, 0x8B, 0x8B);
const COLORT DARKGRAY				= RGB(0xA9, 0xA9, 0xA9);

const COLORT YELLOWBROWN            = RGB(0x99, 0x66, 0x33);

const COLORT ALICEBLUE				= RGB(0xF0, 0xF8, 0xFF);
const COLORT AZURE					= RGB(0xF0, 0xFF, 0xFF);
const COLORT BLANCHEDALMOND			= RGB(0xFF, 0xEB, 0xCD);
const COLORT BURLYWOOD				= RGB(0xDE, 0xB8, 0x87);
const COLORT CORAL					= RGB(0xFF, 0x7F, 0x50);
const COLORT DARKOLIVEGREEN			= RGB(0x55, 0x6B, 0x2F);
const COLORT DARKSALMON				= RGB(0xE9, 0x96, 0x7A);
const COLORT DARKTURQUOISE			= RGB(0x00, 0xCE, 0xD1);
const COLORT DIMGRAY				= RGB(0x69, 0x69, 0x69);
const COLORT FORESTGREEN			= RGB(0x22, 0x8B, 0x22);
const COLORT GOLD					= RGB(0xFF, 0xD7, 0x00);
const COLORT GREENYELLOW			= RGB(0xAD, 0xFF, 0x2F);
const COLORT INDIGO					= RGB(0x4B, 0x00, 0x82);
const COLORT LAVENDERBLUSH			= RGB(0xFF, 0xF0, 0xF5);
const COLORT LIGHTCORAL				= RGB(0xF0, 0x80, 0x80);
const COLORT LIGHTSKYBLUE			= RGB(0x87, 0xCE, 0xFA);
const COLORT LIME					= RGB(0x00, 0xFF, 0x00);
const COLORT MAROON					= RGB(0x80, 0x00, 0x00);
const COLORT MEDIUMPURPLE			= RGB(0x93, 0x70, 0xDB);
const COLORT MEDIUMTURQUOISE		= RGB(0x48, 0xD1, 0xCC);
const COLORT MISTYROSE				= RGB(0xFF, 0xE4, 0xE1);
const COLORT OLDLACE				= RGB(0xFD, 0xF5, 0xE6);
const COLORT ORANGERED				= RGB(0xFF, 0x45, 0x00);
const COLORT PALETURQUOISE			= RGB(0xAF, 0xEE, 0xEE);
const COLORT PERU					= RGB(0xCD, 0x85, 0x3F);
const COLORT PURPLE					= RGB(0x80, 0x00, 0x80);
const COLORT SADDLEBROWN			= RGB(0x8B, 0x45, 0x13);
const COLORT SEASHELL				= RGB(0xFF, 0xF5, 0xEE);
const COLORT SLATEBLUE				= RGB(0x6A, 0x5A, 0xCD);
const COLORT STEELBLUE				= RGB(0x46, 0x82, 0xB4);
const COLORT TOMATO					= RGB(0xFF, 0x63, 0x47);

const COLORT ANTIQUEWHITE			= RGB(0xFA, 0xEB, 0xD7);
const COLORT BEIGE					= RGB(0xF5, 0xF5, 0xDC);
const COLORT CADETBLUE				= RGB(0x5F, 0x9E, 0xA0);
const COLORT CORNFLOWERBLUE			= RGB(0x64, 0x95, 0xED);
const COLORT DARKORANGE				= RGB(0xFF, 0x8C, 0x00);
const COLORT DARKSEAGREEN			= RGB(0x8F, 0xBC, 0x8B);
const COLORT DARKVIOLET				= RGB(0x94, 0x00, 0xD3);
const COLORT DODGERBLUE				= RGB(0x1E, 0x90, 0xFF);
const COLORT FUCHSIA				= RGB(0xFF, 0x00, 0xFF);
const COLORT GOLDENROD				= RGB(0xDA, 0xA5, 0x20);
const COLORT HONEYDEW				= RGB(0xF0, 0xFF, 0xF0);
const COLORT IVORY					= RGB(0xFF, 0xFF, 0xF0);
const COLORT LAWNGREEN				= RGB(0x7C, 0xFC, 0x00);
const COLORT LIGHTSLATEGRAY			= RGB(0x77, 0x88, 0x99);
const COLORT LIMEGREEN				= RGB(0x32, 0xCD, 0x32);
const COLORT MEDIUMAQUAMARINE		= RGB(0x66, 0xCD, 0xAA);
const COLORT MEDIUMSEAGREEN			= RGB(0x3C, 0xB3, 0x71);
const COLORT MEDIUMVIOLETRED		= RGB(0xC7, 0x15, 0x85);
const COLORT MOCCASIN				= RGB(0xFF, 0xE4, 0xB5);
const COLORT OLIVE					= RGB(0x80, 0x80, 0x00);
const COLORT ORCHID					= RGB(0xDA, 0x70, 0xD6);
const COLORT PALEVIOLETRED			= RGB(0xDB, 0x70, 0x93);
const COLORT SALMON					= RGB(0xFA, 0x80, 0x72);
const COLORT SIENNA					= RGB(0xA0, 0x52, 0x2D);
const COLORT SLATEGRAY				= RGB(0x70, 0x80, 0x90);
const COLORT TAN					= RGB(0xD2, 0xB4, 0x8C);
const COLORT TURQUOISE				= RGB(0x40, 0xE0, 0xD0);
const COLORT WHITESMOKE				= RGB(0xF5, 0xF5, 0xF5);

const COLORT AQUA					= RGB(0x00, 0xFF, 0xFF);
const COLORT BISQUE					= RGB(0xFF, 0xE4, 0xC4);
const COLORT BLUEVIOLET				= RGB(0x8A, 0x2B, 0xE2);
const COLORT CHARTREUSE				= RGB(0x7F, 0xFF, 0x00);
const COLORT CORNSILK				= RGB(0xFF, 0xF8, 0xDC);
const COLORT DARKKHAKI				= RGB(0xBD, 0xB7, 0x6B);
const COLORT DARKORCHID				= RGB(0x99, 0x32, 0xCC);
const COLORT DARKSLATEBLUE			= RGB(0x48, 0x3D, 0x8B);
const COLORT DEEPPINK				= RGB(0xFF, 0x14, 0x93);
const COLORT FIREBRICK				= RGB(0xB2, 0x22, 0x22);
const COLORT GAINSBORO				= RGB(0xDC, 0xDC, 0xDC);
const COLORT HOTPINK				= RGB(0xFF, 0x69, 0xB4);
const COLORT KHAKI					= RGB(0xF0, 0xE6, 0x8C);
const COLORT LEMONCHIFFON			= RGB(0xFF, 0xFA, 0xCD);
const COLORT LIGHTGOLDENRODYELLOW	= RGB(0xFA, 0xFA, 0xD2);
const COLORT LIGHTSALMON			= RGB(0xFF, 0xA0, 0x7A);
const COLORT LIGHTSTEELBLUE			= RGB(0xB0, 0xC4, 0xDE);
const COLORT LINEN					= RGB(0xFA, 0xF0, 0xE6);
const COLORT MEDIUMBLUE				= RGB(0x00, 0x00, 0xCD);
const COLORT MEDIUMSLATEBLUE		= RGB(0x7B, 0x68, 0xEE);
const COLORT MIDNIGHTBLUE			= RGB(0x19, 0x19, 0x70);
const COLORT NAVAJOWHITE			= RGB(0xFF, 0xDE, 0xAD);
const COLORT OLIVEDRAB				= RGB(0x6B, 0x8E, 0x23);
const COLORT PALEGOLDENROD			= RGB(0xEE, 0xE8, 0xAA);
const COLORT PAPAYAWHIP				= RGB(0xFF, 0xEF, 0xD5);
const COLORT PLUM					= RGB(0xDD, 0xA0, 0xDD);
const COLORT ROSYBROWN				= RGB(0xBC, 0x8F, 0x8F);
const COLORT SANDYBROWN				= RGB(0xF4, 0xA4, 0x60);
const COLORT SILVER					= RGB(0xC0, 0xC0, 0xC0);
const COLORT SNOW					= RGB(0xFF, 0xFA, 0xFA);
const COLORT TEAL					= RGB(0x00, 0x80, 0x80);
const COLORT VIOLET					= RGB(0xEE, 0x82, 0xEE);

const COLORT AQUAMARINE				= RGB(0x7F, 0xFF, 0xD4);
const COLORT BROWN					= RGB(0xA5, 0x2A, 0x2A);
const COLORT CHOCOLATE				= RGB(0xD2, 0x69, 0x1E);
const COLORT CRIMSON				= RGB(0xDC, 0x14, 0x3C);
const COLORT DARKGOLDENROD			= RGB(0xB8, 0x86, 0x0B);
const COLORT DARKSLATEGRAY			= RGB(0x2F, 0x4F, 0x4F);
const COLORT DEEPSKYBLUE			= RGB(0x00, 0xBF, 0xFF);
const COLORT FLORALWHITE			= RGB(0xFF, 0xFA, 0xF0);
const COLORT GHOSTWHITE				= RGB(0xF8, 0xF8, 0xFF);
const COLORT INDIANRED				= RGB(0xCD, 0x5C, 0x5C);
const COLORT LAVENDER				= RGB(0xE6, 0xE6, 0xFA);
const COLORT LIGHTSEAGREEN			= RGB(0x20, 0xB2, 0xAA);
const COLORT MEDIUMORCHID			= RGB(0xBA, 0x55, 0xD3);
const COLORT MEDIUMSPRINGGREEN		= RGB(0x00, 0xFA, 0x9A);
const COLORT MINTCREAM				= RGB(0xF5, 0xFF, 0xFA);
const COLORT NAVY					= RGB(0x00, 0x00, 0x80);
const COLORT PALEGREEN				= RGB(0x98, 0xFB, 0x98);
const COLORT PEACHPUFF				= RGB(0xFF, 0xDA, 0xB9);
const COLORT POWDERBLUE				= RGB(0xB0, 0xE0, 0xE6);
const COLORT ROYALBLUE				= RGB(0x41, 0x69, 0xE1);
const COLORT SEAGREEN				= RGB(0x2E, 0x8B, 0x57);
const COLORT SKYBLUE				= RGB(0x87, 0xCE, 0xEB);
const COLORT SPRINGGREEN			= RGB(0x00, 0xFF, 0x7F);
const COLORT THISTLE				= RGB(0xD8, 0xBF, 0xD8);
const COLORT WHEAT					= RGB(0xF5, 0xDE, 0xB3);
const COLORT YELLOWGREEN			= RGB(0x9A, 0xCD, 0x32);

const COLORT BRIGHTBLUE			    = RGB(0, 0, 255);
const COLORT LBGRAY					= RGB(208, 208, 208);
const COLORT FBBLUE					= RGB(71, 159, 208);
const COLORT BTNBACK				= RGB(23,23,23);
const COLORT NIGHTBACK				= RGB(31,31,31);

const COLORT MNU_NIGHT_UP_LINE		= RGB(121,121,121);
const COLORT MNU_NIGHT_DN_LINE		= RGB(0,0,0);

const COLORT MNU_DAY_UP_LINE		= RGB(134,134,134);
const COLORT MNU_DAY_DN_LINE		= RGB(255,255,255);

const COLORT ALARM_TXT_CLR1			= RGB(255,64,0);
const COLORT ALARM_TXT_CLR2			= RGB(204,51,0);
const COLORT ALARM_TITLE_BACK		= RGB(27,77,137);

const COLORT SIG_WND_BACK_DAY_CLR	= RGB(27,77,137);
const COLORT SIG_WND_BACK_NIGHT_CLR	= RGB(27,77,137);
const COLORT SIG_GRP_BACK_DAY_CLR	= RGB(27,77,137);
const COLORT SIG_GRP_BACK_NIGHT_CLR	= RGB(27,77,137);
const COLORT SIG_LINE_DAY_CLR		= RGB(27,77,137);	
const COLORT SIG_LINE_NIGHT_CLR		= RGB(27,77,137);

const COLORT ALARM_ORANGE_YELLOW    = RGB(0xFF,0x99,0x00);

#define CLR_BIT_MAP_TRANS			RGB(255,0,255)

#endif //~ __AIS_COLOR_H__
