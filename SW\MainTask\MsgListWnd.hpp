#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "Message.hpp"

#ifndef __MSG_LIST_WND_H__
#define __MSG_LIST_WND_H__

class CMsgListWnd : public CWnd {
protected:
	CEditCtrl *m_pMsgEdit;
	CEditCtrl *m_pCNMsgEdit;
	RECT       m_rectMsgEdit;
	int        m_nTextPosY;
	int        m_nMode;
	int        m_nCurSel;
	int        m_nRxStartViewPos;
	int        m_nTxStartViewPos;

public:
	enum {
		RX_MODE = 0,
		TX_MODE
	};
	
	CMsgListWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
	~CMsgListWnd();

	int  UpdateUnReadRxMsgCnt();
	void DrawRxMsgScrollBar();
	void DrawTxMsgScrollBar();

	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	void OnKeyEvent(int nKey, DWORD nFlags);
	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	void SetMode(int nMode=RX_MODE);
	int  GetMsgCount();

	void DrawRxMessageList();
	void DrawTxMessageList();
	void UpdateMsgBodyContents();
	
	int      GetMode() { return m_nMode; }
	CMessage GetCurMessage();
};

#endif
