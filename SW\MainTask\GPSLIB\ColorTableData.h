/*...........................................................................*/
/*.                  File Name : ColorTableData.h                           .*/
/*.                                                                         .*/
/*.                       Date : 2008.09.25                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __COLORTABLEDATA_H__
#define  __COLORTABLEDATA_H__

//=============================================================================
#define  CHART_LAYER_PAL_SIZE               256
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SetBasicPalData(void);
void  SetSonarPalData(int nSonarPal);
void  SetRadarPalData(int nRadarPal);
void  SetOverlayPalData(int nOverlayPal);
void  SetCmapPalData(int nCmapPal);
UCHAR *SetMapLayerPalData(int nSonarPal,int nCmapPal,int nRadarPal,int nOverlayPal);
UCHAR *GetMapLayerPalData(void);

#ifdef  __cplusplus
}
#endif

#endif

