#============================================================================
#
#         makefile.mak
#
#============================================================================

CC = mipsisa32-elf-gcc
LD = mipsisa32-elf-ld
OC = mipsisa32-elf-objcopy
OD = mipsisa32-elf-objdump
#============================================================================

#============================================================================
ENDIAN  = EB
#----------------------------------------------------------------------------
#ENDIAN  = EL
#============================================================================

ifeq ($(ENDIAN),EL)
OFORMAT      = elf32-littlemips
LD_LIB_PATH1 = //d/COMPILERs/redhat/mips3264-020217/H-i686-pc-cygwin/lib/gcc-lib/mipsisa32-elf/2.96-mips3264-020217/el
LD_LIB_PATH2 = //d/COMPILERs/redhat/mips3264-020217/H-i686-pc-cygwin/mipsisa32-elf/lib/el
LD_LIB_PATH3 = ./
LD_LIB_PATH4 = ./lib
else
OFORMAT      = elf32-bigmips
LD_LIB_PATH1 = /cygdrive/c/redhat/mips3264-020217/H-i686-pc-cygwin/lib/gcc-lib/mipsisa32-elf/2.96-mips3264-020217
LD_LIB_PATH2 = /cygdrive/c/redhat/mips3264-020217/H-i686-pc-cygwin/mipsisa32-elf/lib
LD_LIB_PATH3 = ./
LD_LIB_PATH4 = ./lib
endif

#============================================================================
IMAGENAME = mkdmain
#============================================================================

#============================================================================
# Directories
#============================================================================

ROOT      = .
SRCDIR    = $(ROOT)
VPATH     = $(SRCDIR)
BINDIR    = $(ROOT)

#============================================================================
# Image file names and map, disassembly file
#============================================================================

IMAGE_BIN = $(IMAGENAME).bin
IMAGE_REC = $(IMAGENAME).rec
IMAGE_ELF = $(IMAGENAME).elf
IMAGE_MAP = $(IMAGENAME).map
IMAGE_DIS = $(IMAGENAME).dis

#============================================================================
# Compiler and linker options
#============================================================================

INCLUDE   = -I ./
# \
#            -I //d/COMPILERs/redhat/mips3264-020217/H-i686-pc-cygwin/mipsisa32-elf/include   \
#            -I //d/COMPILERs/redhat/mips3264-020217/H-i686-pc-cygwin/lib/gcc-lib/mipsisa32-elf/2.96-mips3264-020217/include

CFLAGS    = -Wimplicit -Wformat -msoft-float -static -mips32 -mcpu=r4kc -O1 -$(ENDIAN) -D$(ENDIAN) -I./ \
            -D__CPU_AU1100__ -fno-signed-char -G 0

LD_SCRIPT = $(ROOT)/link_AU1100.xn
LD_OPTS   = -T $(LD_SCRIPT) -o $(IMAGE_ELF) -Map $(IMAGE_MAP) --oformat $(OFORMAT)  \
            -L $(LD_LIB_PATH1)                                                      \
            -L $(LD_LIB_PATH2)                                                      \
            -L $(LD_LIB_PATH3)                                                      \
            -L $(LD_LIB_PATH4)                                                      \
            -$(ENDIAN) -static                                                      \
            --wrap malloc                                                           \
            --wrap free                                                             \
            --wrap calloc                                                           \
            --wrap abort                                                            \
            --wrap close                                                            \
            --wrap fstat                                                            \
            --wrap isatty                                                           \
            --wrap lseek                                                            \
            --wrap read                                                             \
            --wrap sbrk                                                             \
            --wrap write
LD_LIBS   = -lsdfile_red -lm -lc -lgcc -lz -lc

#============================================================================
# Files to be compiled
#============================================================================

.c.o:
	$(CC) -c $(CFLAGS) $< -o $@

.cpp.o:
	$(CC) -c $(CFLAGS) $< -o $@

.S.o:
	$(CC) -c $(CFLAGS) -D_ASSEMBLER_ $< -o $@

BASE_HDR = archdefs.h mips.h sysdefs.h au1100.h comlib.h sysconst.h syslib.h

comlib.o             : $(BASE_HDR) comlib.h               comlib.c
syslib.o             : $(BASE_HDR) syslib.h               syslib.c

keybd.o              : $(BASE_HDR) keybd.hpp              keybd.cpp
lcdcon.o             : $(BASE_HDR) lcdcon.hpp             lcdcon.cpp
lcd1100.o            : $(BASE_HDR) lcdcon.hpp             lcd1100.hpp    lcd1100.cpp
lcd1100c.o           : $(BASE_HDR) lcdcon.hpp             lcd1100c.hpp   lcd1100c.cpp
screen.o             : $(BASE_HDR) screen.hpp             screen.cpp
sysintr.o            : $(BASE_HDR) sysintr.hpp            sysintr.cpp
time.o               : $(BASE_HDR) sysintr.hpp            time.cpp
uart.o               : $(BASE_HDR) uart.hpp               uart.cpp
buzzer.o             : $(BASE_HDR) buzzer.hpp             buzzer.cpp
ship.o               : $(BASE_HDR) ship.hpp               ship.cpp
mempcx.o             : $(BASE_HDR) mempcx.hpp             mempcx.cpp
sendpcx.o            : $(BASE_HDR) sendpcx.hpp            sendpcx.cpp
downprg.o            : $(BASE_HDR) downprg.hpp            downprg.cpp
sendprg.o            : $(BASE_HDR) sendprg.hpp            sendprg.cpp
flash.o              : $(BASE_HDR) flash.hpp              flash.cpp
Wnd.o                : $(BASE_HDR) Wnd.hpp                Wnd.cpp
WndMgr.o             : $(BASE_HDR) WndMgr.hpp             WndMgr.cpp
TargetListWnd.o      : $(BASE_HDR) TargetListWnd.hpp      TargetListWnd.cpp
TargetPlotWnd.o      : $(BASE_HDR) TargetPlotWnd.hpp      TargetPlotWnd.cpp
TargetInfoWnd.o      : $(BASE_HDR) TargetInfoWnd.hpp      TargetInfoWnd.cpp
OwnShipWnd.o         : $(BASE_HDR) OwnShipWnd.hpp         OwnShipWnd.cpp
SRMWnd.o             : $(BASE_HDR) SRMWnd.hpp             SRMWnd.cpp
MenuWnd.o            : $(BASE_HDR) MenuWnd.hpp            MenuWnd.cpp
MsgMenuWnd.o         : $(BASE_HDR) MsgMenuWnd.hpp         MsgMenuWnd.cpp
MsgMenuWnd.o         : $(BASE_HDR) MsgMenuWnd.hpp         MsgMenuWnd.cpp
InitSetupWnd.o       : $(BASE_HDR) InitSetupWnd.hpp       InitSetupWnd.cpp
FavorMsgWnd.o        : $(BASE_HDR) FavorMsgWnd.hpp        FavorMsgWnd.cpp
InterrogationWnd.o   : $(BASE_HDR) InterrogationWnd.hpp   InterrogationWnd.cpp
AlarmListWnd.o       : $(BASE_HDR) AlarmListWnd.hpp       AlarmListWnd.cpp
StatusListWnd.o      : $(BASE_HDR) StatusListWnd.hpp      StatusListWnd.cpp
ShoreStationWnd.o    : $(BASE_HDR) ShoreStationWnd.hpp    ShoreStationWnd.cpp
StaticDataWnd.o      : $(BASE_HDR) StaticDataWnd.hpp      StaticDataWnd.cpp
VoyageDataWnd.o      : $(BASE_HDR) VoyageDataWnd.hpp      VoyageDataWnd.cpp
RegionalWnd.o        : $(BASE_HDR) RegionalWnd.hpp        RegionalWnd.cpp
RegionalViewWnd.o    : $(BASE_HDR) RegionalViewWnd.hpp    RegionalViewWnd.cpp
LongRangeWnd.o       : $(BASE_HDR) LongRangeWnd.hpp       LongRangeWnd.cpp
LongRangeMsgWnd.o    : $(BASE_HDR) LongRangeMsgWnd.hpp    LongRangeMsgWnd.cpp
AntPosWnd.o          : $(BASE_HDR) AntPosWnd.hpp          AntPosWnd.cpp
SystemSetupWnd.o     : $(BASE_HDR) SystemSetupWnd.hpp     SystemSetupWnd.cpp
PortWnd.o            : $(BASE_HDR) PortWnd.hpp            PortWnd.cpp
DisplayWnd.o         : $(BASE_HDR) DisplayWnd.hpp         DisplayWnd.cpp
BuzzerWnd.o          : $(BASE_HDR) BuzzerWnd.hpp          BuzzerWnd.cpp
PasswordSetWnd.o     : $(BASE_HDR) PasswordSetWnd.hpp     PasswordSetWnd.cpp
PasswordCheckWnd.o   : $(BASE_HDR) PasswordCheckWnd.hpp   PasswordCheckWnd.cpp
SetEtcWnd.o          : $(BASE_HDR) SetEtcWnd.hpp          SetEtcWnd.cpp
InitializeWnd.o      : $(BASE_HDR) InitializeWnd.hpp      InitializeWnd.cpp
MaintenanceMenuWnd.o : $(BASE_HDR) MaintenanceMenuWnd.hpp MaintenanceMenuWnd.cpp
ProgramVersionWnd.o  : $(BASE_HDR) ProgramVersionWnd.hpp  ProgramVersionWnd.cpp
KeyTestWnd.o         : $(BASE_HDR) KeyTestWnd.hpp         KeyTestWnd.cpp
LcdTestWnd.o         : $(BASE_HDR) LcdTestWnd.hpp         LcdTestWnd.cpp
ViewInputData.o      : $(BASE_HDR) ViewInputData.hpp      ViewInputData.cpp
SecurityLogWnd.o     : $(BASE_HDR) SecurityLogWnd.hpp     SecurityLogWnd.cpp
TpTestMenuWnd.o      : $(BASE_HDR) TpTestMenuWnd.hpp      TpTestMenuWnd.cpp
TxRxTestWnd.o        : $(BASE_HDR) TxRxTestWnd.hpp        TxRxTestWnd.cpp
ReceiverTestWnd.o    : $(BASE_HDR) ReceiverTestWnd.hpp    ReceiverTestWnd.cpp
SetTpParameterWnd.o  : $(BASE_HDR) SetTpParameterWnd.hpp  SetTpParameterWnd.cpp
ProgramUploadWnd.o   : $(BASE_HDR) ProgramUploadWnd.hpp   ProgramUploadWnd.cpp
EditCtrl.o           : $(BASE_HDR) EditCtrl.hpp           EditCtrl.cpp
ComboCtrl.o          : $(BASE_HDR) ComboCtrl.hpp          ComboCtrl.cpp
CheckCtrl.o          : $(BASE_HDR) CheckCtrl.hpp          CheckCtrl.cpp
Regional.o           : $(BASE_HDR) Regional.hpp           Regional.cpp
CnStrWnd.o           : $(BASE_HDR) CnStrWnd.hpp           CnStrWnd.cpp
RSSListWnd.o         : $(BASE_HDR) RSSListWnd.hpp         RSSListWnd.cpp
NMEA/Decoder.o       : $(BASE_HDR) NMEA/Decoder.hpp       NMEA/Decoder.cpp
NMEA/NMEA.o          : $(BASE_HDR) NMEA/NMEA.hpp          NMEA/NMEA.cpp
NMEA/Sentence.o      : $(BASE_HDR) NMEA/Sentence.hpp      NMEA/Sentence.cpp
NMEA/ABK.o           : $(BASE_HDR) NMEA/ABK.hpp           NMEA/ABK.cpp
NMEA/ABM.o           : $(BASE_HDR) NMEA/ABM.hpp           NMEA/ABM.cpp
NMEA/ACA.o           : $(BASE_HDR) NMEA/ACA.hpp           NMEA/ACA.cpp
NMEA/ACK.o           : $(BASE_HDR) NMEA/ACK.hpp           NMEA/ACK.cpp
NMEA/ACS.o           : $(BASE_HDR) NMEA/ACS.hpp           NMEA/ACS.cpp
NMEA/AIR.o           : $(BASE_HDR) NMEA/AIR.hpp           NMEA/AIR.cpp
NMEA/ALR.o           : $(BASE_HDR) NMEA/ALR.hpp           NMEA/ALR.cpp
NMEA/BBM.o           : $(BASE_HDR) NMEA/BBM.hpp           NMEA/BBM.cpp
NMEA/LR1.o           : $(BASE_HDR) NMEA/LR1.hpp           NMEA/LR1.cpp
NMEA/LR2.o           : $(BASE_HDR) NMEA/LR2.hpp           NMEA/LR2.cpp
NMEA/LR3.o           : $(BASE_HDR) NMEA/LR3.hpp           NMEA/LR3.cpp
NMEA/LRF.o           : $(BASE_HDR) NMEA/LRF.hpp           NMEA/LRF.cpp
NMEA/LRI.o           : $(BASE_HDR) NMEA/LRI.hpp           NMEA/LRI.cpp
NMEA/SSD.o           : $(BASE_HDR) NMEA/SSD.hpp           NMEA/SSD.cpp
NMEA/SYC.o           : $(BASE_HDR) NMEA/SYC.hpp           NMEA/SYC.cpp
NMEA/TXT.o           : $(BASE_HDR) NMEA/TXT.hpp           NMEA/TXT.cpp
NMEA/VDM.o           : $(BASE_HDR) NMEA/VDM.hpp           NMEA/VDM.cpp
NMEA/VDO.o           : $(BASE_HDR) NMEA/VDO.hpp           NMEA/VDO.cpp
NMEA/VSD.o           : $(BASE_HDR) NMEA/VSD.hpp           NMEA/VSD.cpp
NMEA/SPW.o           : $(BASE_HDR) NMEA/SPW.hpp           NMEA/SPW.cpp
NMEA/TEST.o          : $(BASE_HDR) NMEA/TEST.hpp          NMEA/TEST.cpp
GFC/CEarthCoordinate.o : $(BASE_HDR) GFC/GFC.h              GFC/CEarthCoordinate.cpp
GFC/CPolarCoordinate.o : $(BASE_HDR) GFC/GFC.h              GFC/CPolarCoordinate.cpp
GFC/CEarth.o           : $(BASE_HDR) GFC/CEarth.hpp         GFC/CEarth.cpp
CHART/maplib.o         : $(BASE_HDR) CHART/maptypes.hpp     CHART/maplib.hpp CHART/maplib.cpp
CHART/mapdata.o        : $(BASE_HDR) CHART/maptypes.hpp     CHART/maplib.hpp CHART/mapdata.hpp CHART/mapdata.cpp
CHART/mapdraw.o        : $(BASE_HDR) CHART/maptypes.hpp     CHART/maplib.hpp CHART/mapdraw.hpp CHART/mapdraw.cpp
SMapLib/CodeSet.o      : $(BASE_HDR) SMapLib/CodeSet.h      SMapLib/CodeSet.cpp
SMapLib/SMapCanvas.o   : $(BASE_HDR) SMapLib/SMapCanvas.h   SMapLib/SMapCanvas.cpp
SMapLib/SMapDataBase.o : $(BASE_HDR) SMapLib/SMapDataBase.h SMapLib/SMapDataBase.cpp
SMapLib/SMapDataNCE.o  : $(BASE_HDR) SMapLib/SMapDataNCE.h  SMapLib/SMapDataNCE.cpp
SMapLib/SMapDraw.o     : $(BASE_HDR) SMapLib/SMapDraw.h     SMapLib/SMapDraw.cpp
SMapLib/SMapDrawMKD.o  : $(BASE_HDR) SMapLib/SMapDrawMKD.h  SMapLib/SMapDrawMKD.cpp
SMapLib/SMapFunc.o     : $(BASE_HDR) SMapLib/SMapFunc.h     SMapLib/SMapFunc.cpp
SMapLib/SMapManager.o  : $(BASE_HDR) SMapLib/SMapManager.h  SMapLib/SMapManager.cpp
SMapLib/SMapMarkMgr.o  : $(BASE_HDR) SMapLib/SMapMarkMgr.h  SMapLib/SMapMarkMgr.cpp
Fonts/UniCode16Font.o  : $(BASE_HDR) Fonts/UniCode16Font.cpp
DocMgr.o             : $(BAES_HDR) DocMgr.hpp             DocMgr.cpp
main.o               : $(BASE_HDR) main.hpp               main.cpp

OBJS   =  start.o              \
          comlib.o             \
          syslib.o             \
          keybd.o              \
          lcdcon.o             \
          lcd1100.o            \
          lcd1100c.o           \
          screen.o             \
          sysintr.o            \
          time.o               \
          uart.o               \
          buzzer.o             \
          ship.o               \
		  mempcx.o             \
		  sendpcx.o            \
		  downprg.o            \
		  sendprg.o            \
		  flash.o              \
          Wnd.o                \
          WndMgr.o             \
		  TargetListWnd.o      \
		  TargetPlotWnd.o      \
		  TargetInfoWnd.o      \
		  OwnShipWnd.o         \
		  SRMWnd.o             \
		  MenuWnd.o            \
		  MsgMenuWnd.o         \
		  InitSetupWnd.o       \
		  MsgListWnd.o         \
		  FavorMsgWnd.o        \
		  InterrogationWnd.o   \
		  AlarmListWnd.o       \
		  StatusListWnd.o      \
		  StaticDataWnd.o      \
		  ShoreStationWnd.o	   \
		  VoyageDataWnd.o      \
		  RegionalWnd.o        \
		  RegionalViewWnd.o    \
		  LongRangeWnd.o       \
		  LongRangeMsgWnd.o    \
		  AntPosWnd.o          \
		  SystemSetupWnd.o     \
		  PortWnd.o            \
		  DisplayWnd.o         \
		  BuzzerWnd.o          \
		  PasswordSetWnd.o     \
		  PasswordCheckWnd.o   \
		  SetEtcWnd.o          \
		  InitializeWnd.o      \
		  MaintenanceMenuWnd.o \
		  ProgramVersionWnd.o  \
		  KeyTestWnd.o         \
		  LcdTestWnd.o         \
		  ViewInputDataWnd.o   \
		  SecurityLogWnd.o     \
		  TpTestMenuWnd.o      \
		  TxRxTestWnd.o        \
		  ReceiverTestWnd.o    \
		  SetTpParameterWnd.o  \
		  ProgramUploadWnd.o   \
		  EditCtrl.o           \
		  ComboCtrl.o          \
		  CheckCtrl.o          \
		  Regional.o           \
		  CnStrWnd.o           \
		  RSSListWnd.o         \
		  lib/sdcard.o         \
		  NMEA/Decoder.o       \
		  NMEA/NMEA.o          \
		  NMEA/Sentence.o      \
		  NMEA/ABK.o           \
		  NMEA/ABM.o           \
		  NMEA/ACA.o           \
		  NMEA/ACK.o           \
		  NMEA/ACS.o           \
		  NMEA/AIR.o           \
		  NMEA/ALR.o           \
		  NMEA/BBM.o           \
		  NMEA/LR1.o           \
		  NMEA/LR2.o           \
		  NMEA/LR3.o           \
		  NMEA/LRF.o           \
		  NMEA/LRI.o           \
		  NMEA/SSD.o           \
		  NMEA/SYC.o           \
		  NMEA/TXT.o           \
		  NMEA/VDM.o           \
		  NMEA/VDO.o           \
		  NMEA/VSD.o           \
		  NMEA/SPW.o           \
		  NMEA/TEST.o          \
		  GFC/CEarthCoordinate.o \
		  GFC/CPolarCoordinate.o \
		  GFC/CEarth.o           \
		  CHART/maplib.o         \
		  CHART/mapdata.o        \
		  CHART/mapdraw.o        \
		  SMapLib/CodeSet.o      \
		  SMapLib/SMapCanvas.o   \
		  SMapLib/SMapDataBase.o \
		  SMapLib/SMapDataNCE.o  \
		  SMapLib/SMapDraw.o     \
		  SMapLib/SMapDrawMKD.o  \
		  SMapLib/SMapFunc.o     \
		  SMapLib/SMapManager.o  \
		  SMapLib/SMapMarkMgr.o  \
		  Fonts/F09N_Tahoma.o     \
		  Fonts/F14B_Dotum_Han.o  \
		  Fonts/F16B_Simsun_Chi.o \
		  Fonts/UniCode16Font.o   \
		  DocMgr.o               \
          main.o
          
#============================================================================
# Rules   
#============================================================================
          
rebuild: clean all

all: $(OBJS)
	$(LD) $(LD_OPTS) $(LD_LIBS) $(OBJS) $(LD_LIBS)
	$(OC) -O binary $(IMAGE_ELF) $(IMAGE_BIN)

clean :
	del $(BINDIR)\*.o
	del $(BINDIR)\NMEA\*.o
	del $(BINDIR)\GFC\*.o
	del $(BINDIR)\CHART\*.o
	del $(BINDIR)\SMapLib\*.o
	del $(BINDIR)\Fonts\*.o
	del $(BINDIR)\$(IMAGENAME).*
#del $(BINDIR)\depend.mk

