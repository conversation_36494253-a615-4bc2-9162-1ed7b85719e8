/*...........................................................................*/
/*.                  File Name : SYSTIMER.H                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSTIMER_H__
#define  __SYSTIMER_H__

//=============================================================================
#define  TIMER1_MUL_FACTOR                                              (4)
//-----------------------------------------------------------------------------
#define  TIMER0_CLKDIV_VALUE                                            (1)
#define  TIMER0_TCLKDIV_VALUE                                           (2)
//-----------------------------------------------------------------------------
#define  TIMER0_MATCH_COUNTER           (CPU_PLL0_FREQUENCY / ONE_SEC_TICK)
#define  TIMER1_MATCH_COUNTER           (CPU_PLL0_FREQUENCY / TIMER0_TCLKDIV_VALUE * TIMER1_MUL_FACTOR)
#define  TIMER2_MATCH_COUNTER           (CPU_PLL0_FREQUENCY / ONE_SEC_TICK)
//-----------------------------------------------------------------------------
#define  TIMER1_TICKS_PER_SEC           (CPU_PLL0_FREQUENCY / TIMER0_TCLKDIV_VALUE)
//-----------------------------------------------------------------------------
#define  WATCH_DOG_TIMER_PHSY_BASE_ADDR             (TIMER3_PHSY_BASE_ADDR)
#define  WATCH_DOG_TIMER_MATCH_VALUE              (CPU_PLL0_FREQUENCY / 20)
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SysInitTimer(xSYS_TIMER *pSysTimer,int nRunMode,int nIntMode,int nWatchDogMode,DWORD dMATCH);
void  SysTimerDelayMicroSec(DWORD dDelayMicro);
void  SysTimerDelayMiliSec(DWORD dDelayMili);
DWORD SysGetTimerCounter(xSYS_TIMER *pSysTimer);
void  SysClearTimerIntPending(xSYS_TIMER *pSysTimer);

#ifdef  __cplusplus
}
#endif

#endif


