/*...........................................................................*/
/*.                  File Name : SYSMCUS.H                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSMCUS_H__
#define  __SYSMCUS_H__

//=============================================================================
#define  MCUS_FPGA_BUS_NO                              1
#define  MCUS_LAN_BUS_NO                               3

#define  MCUS_BUS_WIDTH_08                             0    //  8 Bits
#define  MCUS_BUS_WIDTH_16                             1    // 16 Bits

#define  MCUS_TACS_0CYCLES                             3
#define  MCUS_TACS_1CYCLES                             0
#define  MCUS_TACS_2CYCLES                             1
#define  MCUS_TACS_3CYCLES                             2

#define  MCUS_TCOS_0CYCLES                             3
#define  MCUS_TCOS_1CYCLES                             0
#define  MCUS_TCOS_2CYCLES                             1
#define  MCUS_TCOS_3CYCLES                             2

#define  MCUS_TCOH_0CYCLES                             3
#define  MCUS_TCOH_1CYCLES                             0
#define  MCUS_TCOH_2CYCLES                             1
#define  MCUS_TCOH_3CYCLES                             2

#define  MCUS_TCAH_0CYCLES                             3
#define  MCUS_TCAH_1CYCLES                             0
#define  MCUS_TCAH_2CYCLES                             1
#define  MCUS_TCAH_3CYCLES                             2

#define  MCUS_BURST_DISABLE                            0
#define  MCUS_BURST_04BYTES                            1
#define  MCUS_BURST_08BYTES                            2
#define  MCUS_BURST_16BYTES                            3

#define  MCUS_WAIT_DISABLE                             0
#define  MCUS_WAIT_ENABLE                              1

#define  MCUS_WAIT_POL_HIGH                            0
#define  MCUS_WAIT_POL_LOW                             1
//=============================================================================
#define  MCUS_IDE_CS_NO                            10
#define  MCUS_NAND_CS_NO                           11
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SysInitNAND(void);
void  SysInitMCUS(int nBitWidth);
void  SysSetMCUsCommon(int nNo,DWORD dBusWidth,DWORD dTACS,DWORD dTCOS,DWORD dTACC,DWORD dTSACC,DWORD dTCOH,DWORD dTCAH,DWORD dBurstMode,DWORD dWaitMode,DWORD dWaitPol);
void  SysSetMCUsBit1Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData);
void  SysSetMCUsBit2Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData);
void  SysSetMCUsBit4Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData);
void  SysSetMCUsBit8Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData);

#ifdef  __cplusplus
}
#endif

#endif

