#include "Sentence.hpp"

#ifndef __ABK_HPP__
#define __ABK_HPP__

/******************************************************************************
 * 
 * ABK - Addressed and binary broadcast acknowledgement
 * 
 * $--ABK,xxxxxxxxx,a,x.x,x,x*hh<CR><LF>
 *        |         |  |  | |
 *        1         2  3  4 5
 *
 * 1. MMSI of the addressed destination AIS unit
 * 2. AIS channel of reception
 * 3. ITU-R M. 1371 message ID
 * 4. Message Sequence Number
 * 5. Type of acknowledgement
 *
 ******************************************************************************/
class CAbk : public CSentence {
protected:
	int  m_nMMSI;
	char m_chChannel;
	int  m_nMsgID;
	int  m_nMsgSeqNum;
	int  m_nAckType;
    
public:
    CAbk();
    CAbk(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);
	int  MakeSentence(BYTE *pszSentence) { return 0; }
	
	int  GetMMSI()    { return m_nMMSI; }
	char GetChannel() { return m_chChannel; }
	int  GetMsgID()   { return m_nMsgID;    }
	int  GetAckType() { return m_nAckType;  }
};

#endif

