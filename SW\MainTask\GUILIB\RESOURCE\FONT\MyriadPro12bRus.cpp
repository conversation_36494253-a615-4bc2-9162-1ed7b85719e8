/*...........................................................................*/
/*.                  File Name : MyriadPro12bRus.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma12bTai_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro12bRus_offset_table[257] = {
0x0000,0x0003,0x000b,0x0016,0x001d,0x0026,0x002e,0x0032,0x0037,0x003d,0x004b,0x0059,0x0064,0x006d,0x0070,0x0079,
0x0083,0x008d,0x0096,0x009f,0x00a6,0x00b0,0x00b8,0x00c5,0x00cd,0x00d7,0x00e1,0x00ea,0x00f4,0x0101,0x010b,0x0116,
0x0120,0x0129,0x0132,0x013a,0x0143,0x014f,0x0158,0x0162,0x016c,0x017a,0x0188,0x0193,0x01a0,0x01a9,0x01b2,0x01c0,
0x01c9,0x01d1,0x01da,0x01e2,0x01e8,0x01f1,0x01f9,0x0204,0x020b,0x0214,0x021d,0x0225,0x022d,0x0238,0x0241,0x024a,
0x0253,0x025c,0x0263,0x026a,0x0272,0x027d,0x0285,0x028e,0x0296,0x02a2,0x02ae,0x02b8,0x02c4,0x02cc,0x02d3,0x02df,
0x02e7,0x02ea,0x02f2,0x02fb,0x0301,0x0308,0x030f,0x0313,0x0319,0x031e,0x032a,0x0337,0x0340,0x0348,0x034b,0x0353,
0x035c,0x035f,0x0362,0x0365,0x0368,0x036b,0x036e,0x0371,0x0374,0x0377,0x037a,0x037d,0x0380,0x0383,0x0386,0x0389,
0x038c,0x038f,0x0392,0x0395,0x0398,0x039b,0x039e,0x03a1,0x03a4,0x03a7,0x03aa,0x03ad,0x03b0,0x03b3,0x03b6,0x03b9,
0x03bc,0x03bf,0x03c2,0x03c5,0x03c8,0x03cb,0x03ce,0x03d1,0x03d4,0x03d7,0x03da,0x03dd,0x03e0,0x03e3,0x03e6,0x03e9,
0x03ec,0x03f3,0x03f9,0x03fc,0x03ff,0x0402,0x0405,0x0408,0x040b,0x040e,0x0411,0x0414,0x0417,0x041a,0x041d,0x0420,
0x0423,0x0426,0x0429,0x042c,0x042f,0x0432,0x0435,0x0438,0x043b,0x043e,0x0441,0x0444,0x0447,0x044a,0x044d,0x0450,
0x0453,0x0456,0x0459,0x045c,0x045f,0x0462,0x0465,0x0468,0x046b,0x046e,0x0471,0x0474,0x0477,0x047a,0x047d,0x0480,
0x0483,0x0486,0x0489,0x048c,0x048f,0x0492,0x0495,0x0498,0x049b,0x049e,0x04a1,0x04a4,0x04a7,0x04aa,0x04ad,0x04b0,
0x04b3,0x04b6,0x04b9,0x04bc,0x04bf,0x04c2,0x04c5,0x04c8,0x04cb,0x04ce,0x04d5,0x04d8,0x04db,0x04de,0x04e1,0x04e4,
0x04e7,0x04ea,0x04ed,0x04f0,0x04f3,0x04f6,0x04f9,0x04fc,0x04ff,0x0502,0x0505,0x0508,0x050b,0x050e,0x0511,0x0514,
0x0517,0x051a,0x051d,0x0520,0x0523,0x0526,0x0529,0x052c,0x052f,0x0532,0x0535,0x0538,0x053b,0x053e,0x0541,0x0544,
0x0547};



ROMDATA PEGUBYTE MyriadPro12bRus_data_table[3211] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0xc0, 0x00, 0x70, 0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0xc0, 0x00, 0x60, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xdf, 0xf1, 0xf8, 0x78, 0x79, 0x98, 0x31, 0xf8, 0x0c, 0x60, 0x7f, 0xc6, 0x38, 0xe1, 0xb0, 
0xc3, 0x83, 0xf1, 0xf0, 0xfc, 0xfe, 0x7e, 0x73, 0x39, 0xe3, 0x0c, 0xc3, 0x31, 0xcf, 0xe3, 0x8e, 
0x30, 0xc1, 0xe1, 0xfe, 0x7c, 0x07, 0xff, 0xf8, 0xe0, 0xc1, 0xc7, 0x61, 0x98, 0xc6, 0x31, 0x98, 
0xc6, 0x78, 0x0c, 0x0c, 0x60, 0x3e, 0x18, 0x78, 0x1f, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0xbf, 0x83, 
0x00, 0x00, 0xd9, 0x98, 0x00, 0x00, 0x01, 0xfc, 0x0c, 0x04, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xdf, 0xf1, 0xf9, 0xf8, 0xf1, 0x98, 0x31, 0xf8, 0x0c, 0x60, 0x7f, 0xc6, 0x70, 0x63, 0xb0, 
0xc3, 0x83, 0xf1, 0xfc, 0xfc, 0xfe, 0x7e, 0x33, 0x33, 0xf3, 0x1c, 0xc7, 0x33, 0x8f, 0xe3, 0x8e, 
0x30, 0xc7, 0xf1, 0xfe, 0x7e, 0x1f, 0xbf, 0xd8, 0xc3, 0xf0, 0xee, 0x61, 0x98, 0xc6, 0x31, 0x98, 
0xc6, 0x78, 0x0c, 0x0c, 0x60, 0x3f, 0x18, 0xfc, 0x3f, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0xbf, 0x86, 
0x00, 0x00, 0xd9, 0x98, 0x00, 0x00, 0x01, 0xfc, 0x18, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe1, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x03, 0x01, 0x81, 0x81, 0x81, 0x98, 0x31, 0x98, 0x0c, 0x60, 0x0c, 0x06, 0xe0, 0x73, 0x30, 
0xc6, 0xc3, 0x01, 0x8c, 0xc0, 0xc6, 0x60, 0x1b, 0x60, 0x33, 0x3c, 0xcf, 0x37, 0x0c, 0x62, 0x8b, 
0x30, 0xc6, 0x31, 0x86, 0x63, 0x18, 0x06, 0x1c, 0xc6, 0xd8, 0x6c, 0x61, 0x98, 0xc6, 0x31, 0x98, 
0xc6, 0x18, 0x0c, 0x0c, 0x60, 0x03, 0x18, 0xcc, 0x63, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x03, 0xe1, 0x83, 0x01, 0x81, 0x98, 0x31, 0x9f, 0x0c, 0x7e, 0x0f, 0xc6, 0xc0, 0x37, 0x30, 
0xc6, 0xc3, 0xe1, 0x8c, 0xc0, 0xc6, 0x60, 0x1b, 0x60, 0x33, 0x3c, 0xcf, 0x36, 0x0c, 0x66, 0xdb, 
0x30, 0xcc, 0x19, 0x86, 0x63, 0x30, 0x06, 0x0d, 0xcc, 0xcc, 0x7c, 0x61, 0x98, 0xc6, 0x31, 0x98, 
0xc6, 0x1f, 0x0f, 0x8c, 0x7c, 0x01, 0x99, 0x86, 0x63, 0x3e, 0x36, 0x1f, 0x1f, 0x3f, 0x0c, 0x3b, 
0x73, 0x8c, 0xe6, 0x73, 0x3b, 0xf1, 0x8e, 0x63, 0x0e, 0x1f, 0xcd, 0x81, 0xff, 0xb9, 0xc7, 0xc7, 
0x3b, 0x19, 0x99, 0x99, 0x99, 0x9b, 0xc0, 0x60, 0x66, 0x03, 0x8c, 0x70, 0x3c, 0x06, 0x1b, 0x8f, 
0x8f, 0x3c, 0xc6, 0x19, 0xf8, 0x19, 0x80, 0xdc, 0x67, 0x1c, 0xec, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0f, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xc3, 0xf1, 0x83, 0xf0, 0xe1, 0x98, 0x31, 0x9f, 0x8f, 0xff, 0x0f, 0xe7, 0x80, 0x3e, 0x30, 
0xc6, 0xc3, 0xf1, 0xf8, 0xc0, 0xc6, 0x7e, 0x0f, 0xc1, 0xe3, 0x6c, 0xdb, 0x3c, 0x0c, 0x66, 0xdb, 
0x3f, 0xcc, 0x19, 0x86, 0x63, 0x30, 0x06, 0x0d, 0x8c, 0xcc, 0x38, 0x61, 0x98, 0xc6, 0x31, 0x98, 
0xc6, 0x1f, 0x8f, 0xcc, 0x7e, 0x1f, 0x9f, 0x86, 0x3f, 0x1f, 0x3f, 0x1f, 0x9f, 0x3f, 0x1e, 0x1b, 
0x67, 0xcc, 0xe6, 0x73, 0x63, 0xf3, 0xde, 0x63, 0x1f, 0x1f, 0xcf, 0xc3, 0xdf, 0x99, 0x8f, 0xe3, 
0xf3, 0x19, 0x99, 0x99, 0x99, 0x9b, 0xc0, 0x60, 0x66, 0x07, 0xcd, 0xf8, 0xfc, 0x0f, 0x1f, 0x8f, 
0x9e, 0x78, 0xc6, 0x19, 0xf8, 0x19, 0x80, 0xfe, 0x6c, 0x0c, 0xcc, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0f, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xc3, 0x39, 0x83, 0xf0, 0x71, 0x98, 0x31, 0x98, 0xcf, 0xe1, 0x8c, 0x67, 0xe0, 0x1e, 0x30, 
0xce, 0xe3, 0x19, 0xf8, 0xc0, 0xc6, 0x7e, 0x3f, 0xf1, 0xe3, 0x6c, 0xdb, 0x3f, 0x0c, 0x66, 0xd3, 
0x3f, 0xcc, 0x19, 0x86, 0x7e, 0x30, 0x06, 0x07, 0x8c, 0xcc, 0x38, 0x61, 0x9f, 0xc6, 0x31, 0x98, 
0xc6, 0x18, 0xcc, 0x6c, 0x63, 0x1f, 0x9f, 0x86, 0x3f, 0x0f, 0x31, 0x9f, 0x98, 0x33, 0x3f, 0x0f, 
0xc3, 0xcd, 0xe6, 0xf3, 0xc3, 0x33, 0xde, 0x7f, 0x31, 0x98, 0xcc, 0x66, 0x06, 0x19, 0x9b, 0x31, 
0xe3, 0x19, 0x99, 0x99, 0x99, 0x98, 0xf8, 0x7c, 0x67, 0xc3, 0xef, 0x8c, 0xcc, 0x1f, 0x98, 0xcc, 
0x3e, 0x60, 0xc6, 0x19, 0x9f, 0x1f, 0xf0, 0xc6, 0x78, 0x0c, 0xcc, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x03, 0x19, 0x83, 0x00, 0x19, 0x98, 0x31, 0x98, 0xcc, 0x61, 0x8c, 0x66, 0x60, 0x1c, 0x30, 
0xcf, 0xe3, 0x19, 0x8c, 0xc1, 0xc6, 0x60, 0x33, 0x30, 0x33, 0xcc, 0xf3, 0x33, 0x0c, 0x66, 0x53, 
0x30, 0xcc, 0x19, 0x86, 0x7c, 0x30, 0x06, 0x07, 0x0c, 0xcc, 0x7c, 0x61, 0x8f, 0xc6, 0x31, 0x98, 
0xc6, 0x18, 0xcc, 0x6c, 0x63, 0x01, 0x99, 0x86, 0x33, 0x1f, 0x31, 0x9f, 0xd8, 0x33, 0x3f, 0x1f, 
0xe3, 0xcd, 0x66, 0xb3, 0xe3, 0x33, 0x56, 0x7f, 0x31, 0x98, 0xcc, 0x66, 0x06, 0x0d, 0x9b, 0x31, 
0xe3, 0x19, 0xf9, 0x99, 0x99, 0x98, 0xfe, 0x7f, 0x67, 0xf3, 0xef, 0x8c, 0x7c, 0x1f, 0x98, 0xcc, 
0x3e, 0x38, 0xc6, 0x19, 0x9f, 0xdf, 0xfc, 0xc6, 0x7c, 0x06, 0x8c, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x03, 0x19, 0x81, 0x80, 0x19, 0x98, 0x33, 0x98, 0xcc, 0x63, 0x8c, 0x66, 0x70, 0x1c, 0x30, 
0xcf, 0xe3, 0x19, 0x8c, 0xc1, 0x86, 0x60, 0x33, 0x38, 0x33, 0xcc, 0xf3, 0x33, 0x9c, 0x66, 0x73, 
0x30, 0xc6, 0x31, 0x86, 0x60, 0x18, 0x06, 0x07, 0x06, 0xd8, 0x6c, 0x61, 0x80, 0xc6, 0x31, 0x98, 
0xc6, 0x18, 0xcc, 0x6c, 0x63, 0x03, 0x19, 0xcc, 0x63, 0x33, 0x31, 0x98, 0xd8, 0x73, 0x30, 0x1b, 
0x70, 0x6f, 0x67, 0xb3, 0x73, 0x33, 0x76, 0x63, 0x31, 0x98, 0xcc, 0x66, 0x06, 0x0f, 0x1b, 0x31, 
0xe3, 0x18, 0xd9, 0x99, 0x99, 0x98, 0xc6, 0x63, 0x66, 0x30, 0xed, 0xcc, 0x7c, 0x18, 0x18, 0xcc, 
0x30, 0x0c, 0xc6, 0x19, 0x98, 0xd9, 0x8c, 0xc6, 0x6e, 0x07, 0x8c, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xc3, 0x19, 0x81, 0xf9, 0xf1, 0x99, 0xe7, 0x1f, 0x8c, 0x7f, 0x0c, 0x66, 0x30, 0x78, 0x3f, 
0xdc, 0x63, 0xf1, 0xf8, 0xc3, 0xff, 0x7e, 0x63, 0x1f, 0xf3, 0x8c, 0xe3, 0x31, 0xb8, 0x66, 0x73, 
0x30, 0xc7, 0xf1, 0x86, 0x60, 0x1f, 0x86, 0x1e, 0x07, 0xf8, 0xce, 0x7f, 0xc0, 0xc7, 0xff, 0x9f, 
0xff, 0x1f, 0x8f, 0xcc, 0x7e, 0x3f, 0x18, 0xfc, 0x63, 0x3f, 0x1f, 0x1f, 0xd8, 0xff, 0x9e, 0x33, 
0x37, 0xee, 0x67, 0x33, 0x36, 0x33, 0x76, 0x63, 0x1f, 0x18, 0xcf, 0xc3, 0xc6, 0x0f, 0x0f, 0xe3, 
0x73, 0xfc, 0x19, 0xff, 0x9f, 0xfc, 0xfc, 0x7e, 0x67, 0xe7, 0xcc, 0xf8, 0xcc, 0x0f, 0x18, 0xcc, 
0x1f, 0x7c, 0xc6, 0x1b, 0x1f, 0xd9, 0xfc, 0xc6, 0x66, 0x07, 0x8f, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xc3, 0x39, 0x80, 0x79, 0xe1, 0x99, 0xc6, 0x1f, 0x0c, 0x7e, 0x0c, 0x66, 0x38, 0x70, 0x3f, 
0xd8, 0x73, 0xe1, 0xf0, 0xc3, 0xff, 0x7e, 0x63, 0x1b, 0xc3, 0x8c, 0xe3, 0x31, 0xf0, 0x66, 0x63, 
0x30, 0xc3, 0xc1, 0x86, 0x60, 0x07, 0x86, 0x1c, 0x03, 0xe1, 0xc7, 0x7f, 0xc0, 0xc7, 0xff, 0x9f, 
0xff, 0x1f, 0x0f, 0x8c, 0x7c, 0x3c, 0x18, 0x78, 0xe3, 0x1b, 0x0e, 0x1f, 0x18, 0xff, 0x8f, 0x33, 
0x33, 0x8e, 0x67, 0x33, 0x3e, 0x33, 0x66, 0x63, 0x0e, 0x18, 0xcd, 0x81, 0xe6, 0x07, 0x07, 0xc7, 
0x3b, 0xfc, 0x19, 0xff, 0x9f, 0xfc, 0xf8, 0x7c, 0x67, 0xc3, 0x8c, 0x70, 0xcc, 0x07, 0x99, 0xcc, 
0x0f, 0x38, 0xc6, 0x1b, 0x1f, 0x19, 0xf0, 0xc6, 0x67, 0x03, 0x8f, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x06, 0x03, 0x00, 
0x00, 0x0c, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 
0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x0e, 0x03, 0x00, 
0x00, 0x0c, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x1c, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY MyriadPro12bRus_Font = {0x01, 19, 0, 19, 0, 0, 19, 169, 0x0400, 0x04ff,
(PEGUSHORT *) MyriadPro12bRus_offset_table, &Tahoma12bTai_Font,
(PEGUBYTE *) MyriadPro12bRus_data_table};


