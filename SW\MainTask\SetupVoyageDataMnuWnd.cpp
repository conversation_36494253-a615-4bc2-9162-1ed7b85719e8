#include <stdio.h>
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "AllConst.h"
#include "Resource.h"
#include "Font.h"
#include "SetupVoyageDataMnuWnd.hpp"


extern CDocMgr *g_pDocMgr;

/*********************************************************************************************************/
// Name		: CSetupVoyageDataMnuWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CSetupVoyageDataMnuWnd::CSetupVoyageDataMnuWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID): CWnd(pScreen, pCaption, dWndID)
{
	m_nSelNum = 1;
}

/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CSetupVoyageDataMnuWnd::DrawWnd(BOOL bRedraw)
{
	int nLangMode = g_pDocMgr->GetLangMode();

	CWnd::DrawWnd(bRedraw);
	
	DrawSubMenu(m_nSelNum);

	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
	DrawButton(1, (BYTE *)FK_EXIT[nLangMode]);
	EraseButton(2);
	EraseButton(3);
}

/*********************************************************************************************************/
// Name		: DrawSubMenu
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CSetupVoyageDataMnuWnd::DrawSubMenu(int nSelNum)
{
	int nLangMode = g_pDocMgr->GetLangMode();
	int i = 0;
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0, nYOffset = 0;
	HWORD *pUniCodeStr = NULL;
	UCHAR *pArrBmp = NULL;
	COLORT clrTxt;
	COLORT clrUpLine;
	COLORT clrDnLine;

	if(m_pScreen != NULL)
	{

		// Draw Menu
		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:
				pFont = &NewGulLim18bCJK;
				nYOffset = 2;
				break;

			case LANG_RUS:
				pFont = &MyriadPro30bRus;
				nYOffset = 2;
				break;	

			default:
				pFont = &MyriadPro30bEng;
				nYOffset = 6;
				break;
		}
		
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		for(i = 0; i < MAX_MENU_BAR; i++)
		{
			if(m_nScheme == CS_DAY_BRIGHT)
			{
				clrUpLine = MNU_DAY_UP_LINE;
				clrDnLine = MNU_DAY_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrDayOn;
					clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
				}
				else
				{
					clrTxt = COLORSCHEME[m_nScheme].crFore;
					pArrBmp = G_BmpArrDayOff;
				}
			}
			else
			{
				clrUpLine = MNU_NIGHT_UP_LINE;
				clrDnLine = MNU_NIGHT_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrNightOn;
					clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
				}
				else
				{
					clrTxt = COLORSCHEME[m_nScheme].crFore;
					pArrBmp = G_BmpArrNightOff;
				}
			}
			
			if(nSelNum == (i+1))
			{
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crLetterIcon);	
				
			}
			else
			{
									
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crBack);	
			}

			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							clrUpLine);
			
			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							clrDnLine);

			nXPos = BASE_WND_AREA_X_POS + 5;
			nYPos = WND_BACK_Y_POS + i*MNU_BAR_H + (MENU_ITEM_H - nFontH)/2 + nYOffset;

			switch(i)
			{
				case 0:
					pUniCodeStr = (HWORD *)MNU_SETUP_BASIC_VOYAGE_DATA[nLangMode];
					break;

				case 1:
					pUniCodeStr = (HWORD *)MNU_SETUP_CARGO_TYPE[nLangMode];
					break;
			}

			switch(i)
			{
				case 0:
				case 1:
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);	

					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;
			}
		}

		m_pScreen->SetFont(pOldFont);
	}
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CSetupVoyageDataMnuWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
		case KBD_SCAN_CODE_LEFT:
			if( m_nSelNum > 1 ) 
			{
				m_nSelNum--;
			}				
			else
			{
				m_nSelNum = 2;
			}				
			DrawWnd(0);
			break;
		
		case KBD_SCAN_CODE_DOWN:
		case KBD_SCAN_CODE_RIGHT:
			if( m_nSelNum < 2) 
			{
				m_nSelNum++;
			}				
			else
			{
				m_nSelNum = 1;
			}				
			DrawWnd(0);
			break;
	}
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CSetupVoyageDataMnuWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);

	if( nResult == AL_YES )
	{
	}

	return nResult;
}
