/*...........................................................................*/
/*.                  File Name : OSFILE.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.03.15                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#define  __USE_TYPE_DEF__        1

#include "FileLibType.h"
#include "/cygdrive/c/GNUARM-4.2.2/Job/F-Project/POLLUX/UVIS/MainTask/DEVLIB/DEVICE/SysSDCD.h"
#include "/cygdrive/c/GNUARM-4.2.2/Job/F-Project/POLLUX/UVIS/MainTask/DEVLIB/DEVICE/SdCardDRV.h"
#include "rtfiles.h"
#include "osfile.h"

//=============================================================================
typedef struct { 
   char   Label[12];
   char   DriveLetter;
   char   Reserved[1];
   WORD   DeviceFlags;
   DWORD  SerialNumber;
   DWORD  FirstPhysicalSector;
   UINT   FATType;
   UINT   FATCount;
   UINT   MaxDirEntries;
   UINT   BytesPerSector;
   UINT   SectorsPerCluster;
   DWORD  TotalClusters;
   DWORD  BadClusters;
   DWORD  FreeClusters;
   DWORD  Files;
   DWORD  FileChains;
   DWORD  FreeChains;
   DWORD  LargestFreeChain;
} xDISKINFO;
//=============================================================================

#include <string.h>
#include <stdlib.h>

FHANDLE OsfOpen(const char *pFileName,DWORD dFlags)
{
        int  nDrvMountNo = OsfGetDriveMountNo(pFileName);

        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
//          RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(nDrvMountNo);
            RTFRawMount(nDrvMountNo);
           }
        return(RTFOpen(pFileName,dFlags));
}
int     OsfClose(FHANDLE hFile)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFClose(hFile));
}
int     OsfRead(FHANDLE hFile,void *pDataPtr,DWORD dLength,DWORD *pReadSize)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFRead(hFile,pDataPtr,dLength,pReadSize));
}
int     OsfWrite(FHANDLE hFile,const void *pDataPtr,DWORD dLength,DWORD *pWrittenSize)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFWrite(hFile,pDataPtr,dLength,pWrittenSize));
}
long    OsfSeek(FHANDLE hFile,DWORD dOffset,int nWhence)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFSeek(hFile,dOffset,nWhence));
}

int     OsfCheckDir(const char *pDirName)
{
        FHANDLE hFile;

        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        hFile = OsfOpen(pDirName,RTF_READ_ONLY | RTF_OPEN_DIR);
        if (hFile >= OSF_NO_ERROR)
           {
            OsfClose(hFile);
            return(OSF_NO_ERROR);
           }
        return(OSF_ERROR_GENERAL);
}
int     OsfCreateDir(const char *pDirName)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFCreateDir(pDirName));
}
int     OsfRemoveDir(const char *pDirName)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFRemoveDir(pDirName));
}

FHANDLE OsfFindFirstEx(const char *pNamePattern,OSFDOSDirEntry *pDirEntry,char *pFileName,DWORD dMaxLength)
{
//      return(RTFFindFirstEx(pNamePattern,0,OSF_ATTR_DIR,(RTFDOSDirEntry *)pDirEntry,pFileName,dMaxLength));
//        return(RTFFindFirstEx(pNamePattern,OSF_ATTR_DIR | OSF_ATTR_ARCHIVE,0,(RTFDOSDirEntry *)pDirEntry,pFileName,dMaxLength));
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFFindFirstEx(pNamePattern,0,0,(RTFDOSDirEntry *)pDirEntry,pFileName,dMaxLength));
}
int     OsfFindNextEx(FHANDLE hFile,OSFDOSDirEntry *pDirEntry,char *pFileName,DWORD dMaxLength)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFFindNextEx(hFile,(RTFDOSDirEntry *)pDirEntry,pFileName,dMaxLength));
}
int     OsfFindClose(FHANDLE hFile)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetSdCardDriveStr());
            RTFRawMediaChanged(SDCARD_MOUNT_NO);
            RTFRawMount(SDCARD_MOUNT_NO);
           }
        return(RTFFindClose(hFile));
}

int     OsfRename(const char *pFileName,const char *pNewName)
{
        int  nDrvMountNo = OsfGetDriveMountNo(pFileName);

        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetDrvNameStrByMountNo(nDrvMountNo));
            RTFRawMediaChanged(nDrvMountNo);
            RTFRawMount(nDrvMountNo);
           }
        return(RTFRename(pFileName,pNewName));
}
int     OsfFDelete(const char *pFileName)
{
        int  nDrvMountNo = OsfGetDriveMountNo(pFileName);

        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetDrvNameStrByMountNo(nDrvMountNo));
            RTFRawMediaChanged(nDrvMountNo);
            RTFRawMount(nDrvMountNo);
           }
        return(RTFDelete(pFileName));
}

int     OsfMountDisk(int nDiskNo)
{
        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(1);
            RTFCloseAll();
            RTFResetDisk(OsfGetDrvNameStrByMountNo(nDiskNo));
            RTFRawMediaChanged(nDiskNo);
           }
        return(RTFRawMount(SDCARD_MOUNT_NO));
}
int     OsfResetDisk(int nDiskNo)
{
        RTFCloseAll();
        RTFResetDisk(OsfGetDrvNameStrByMountNo(nDiskNo));
        RTFRawMediaChanged(nDiskNo);
        return(OSF_NO_ERROR);
}
int     OsfCommitAll(int nDiskNo)
{
        char  vDirName[16];

        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetDrvNameStrByMountNo(nDiskNo));
            RTFRawMediaChanged(nDiskNo);
            RTFRawMount(nDiskNo);
           }
        vDirName[0] = OsfGetDrvNameCharByMountNo(nDiskNo);
        strcpy(&vDirName[1],":\\");
        return(RTFCommitAll(vDirName));
}
int     OsfCloseAll(void)
{
        return(RTFCloseAll());
}
int     OsfRawMediaChanged(int nDiskNo)
{
        return(RTFRawMediaChanged(nDiskNo));
}
int     OsfGetDiskFreeSpace(int nDiskNo,QWORD *pDiskFreeSpace)
{
        xDISKINFO xDiskInfo;
        char  vDriveName[16];
        int   nError;

        if (SdCardGetCardChangStatus())
           {
            SdCardSetCardChangStatus(0);
            RTFCloseAll();
            RTFResetDisk(OsfGetDrvNameStrByMountNo(nDiskNo));
            RTFRawMediaChanged(nDiskNo);
            RTFRawMount(nDiskNo);
           }
        vDriveName[0] = OsfGetDrvNameCharByMountNo(nDiskNo);
        strcpy(&vDriveName[1],":\\");
        nError = RTFGetDiskInfoEx(vDriveName,(RTFDiskInfo *)&xDiskInfo,RTF_DI_BASIC_INFO | RTF_DI_FREE_SPACE);
        if (nError >= OSF_NO_ERROR)
           {
            *pDiskFreeSpace = (QWORD)xDiskInfo.BytesPerSector    * 
                              (QWORD)xDiskInfo.SectorsPerCluster * 
                              (QWORD)xDiskInfo.FreeClusters;
            return(OSF_NO_ERROR);
           }
        return(OSF_ERROR_GENERAL);
}
void    OsfGetFileDateTime(OSFDOSDateTime *pDosDateTime,int *pYear,int *pMonth,int *pDay,int *pHour,int *pMin,int *pSec)
{
        RTFDOSDateTime xDateTime;
//      UCHAR *pDateTime;
//      UCHAR  bTempData;

        memmove(&xDateTime,pDosDateTime,sizeof(xDateTime));
        #if defined(EB)
            pDateTime = (UCHAR *)&xDateTime;
            bTempData = pDateTime[1];
            pDateTime[1] = pDateTime[0];
            pDateTime[0] = bTempData;
            bTempData = pDateTime[3];
            pDateTime[3] = pDateTime[2];
            pDateTime[2] = bTempData;
        #endif
        *pYear  = xDateTime.Year1980 + 1980;
        *pMonth = xDateTime.Month;
        *pDay   = xDateTime.Day;
        *pHour  = xDateTime.Hour;
        *pMin   = xDateTime.Minute;
        *pSec   = xDateTime.Second2 * 2;
}
//============================================================================
char    OsfGetSdCardDriveChar(void)
{
        return('C');
}
char   *OsfGetSdCardDriveStr(void)
{
        static char vDrvName[4];

       vDrvName[0] = OsfGetSdCardDriveChar();
       vDrvName[1] = ':';
       vDrvName[2] = '\\';
       vDrvName[3] = 0x00;
       return(vDrvName);
}
char    OsfGetUsbDiskDriveChar(void)
{
        return('D');
}
char   *OsfGetUsbDiskDriveStr(void)
{
        static char vDrvName[4];

       vDrvName[0] = OsfGetUsbDiskDriveChar();
       vDrvName[1] = ':';
       vDrvName[2] = '\\';
       vDrvName[3] = 0x00;
       return(vDrvName);
}
void    OsfAppendDriveName(int nDiskNo,char *pFileName)
{
      char vDrvName[1024];

      if (pFileName[1] != ':')
         {
          if (nDiskNo == SDCARD_MOUNT_NO)
              strcpy(vDrvName,OsfGetSdCardDriveStr());
          else
              strcpy(vDrvName,OsfGetUsbDiskDriveStr());
          strcat(vDrvName,pFileName);
          strcpy(pFileName,vDrvName);
         }
}
int     OsfGetDriveMountNo(const char *pFileName)
{
        if (pFileName[1] == ':')
           {
            if (pFileName[0] == 'D' || pFileName[0] == 'd')
                return(USB_DISK_MOUNT_NO);
           }
        return(SDCARD_MOUNT_NO);
}
char    OsfGetDrvNameCharByMountNo(int nDiskNo)
{
       if (nDiskNo == USB_DISK_MOUNT_NO)
           return(OsfGetSdCardDriveChar());
       return(OsfGetUsbDiskDriveChar());
}
char   *OsfGetDrvNameStrByMountNo(int nDiskNo)
{
       if (nDiskNo == USB_DISK_MOUNT_NO)
           return(OsfGetUsbDiskDriveStr());
       return(OsfGetSdCardDriveStr());
}
//============================================================================

