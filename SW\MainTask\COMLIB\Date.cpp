/*...........................................................................*/
/*.                  File Name : Date.cpp                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.18                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

#include "AllConst.h"
#include "ComLib.h"
#include "SysLib.h"
#include "GpsLib.h"
#include "Date.hpp"

/////////////////////////////////////////////////////////////////////////////
xSYSDATE  cDATE::m_xUTCDate    = {2009, 1, 1};
xSYSTIME  cDATE::m_xUTCTime    = { 0, 0, 0};
int       cDATE::m_nLocalTimeS = LOCAL_TIME_ZONE_PLUS;
int       cDATE::m_nLocalTimeH = 0;
int       cDATE::m_nLocalTimeM = 0;
/////////////////////////////////////////////////////////////////////////////

cDATE::cDATE(void)
{
}
cDATE::~cDATE(void)
{
}
void  cDATE::InitAllData(void)
{
}
void  cDATE::SetUTCDate(int nYear,int nMonth,int nDay)
{
      xSYSDATE xLocalDate;
      xSYSTIME xLocalTime;

      m_xUTCDate.nYear  = nYear;
      m_xUTCDate.nMonth = nMonth;
      m_xUTCDate.nDay   = nDay;

      SysSetUTCDate(nYear,nMonth,nDay);
      SetSystemDateUTC(nYear,nMonth,nDay);

      cDATE::GetLocalDateTime(&xLocalDate,&xLocalTime);

      SetSystemDateLOC(xLocalDate.nYear,xLocalDate.nMonth,xLocalDate.nDay);
      SetSystemTimeLOC(xLocalTime.nHour,xLocalTime.nMinute,xLocalTime.nSecond);

      SysSetLOCDate(xLocalDate.nYear,xLocalDate.nMonth,xLocalDate.nDay);
      SysSetLOCTime(xLocalTime.nHour,xLocalTime.nMinute,xLocalTime.nSecond);
}
void  cDATE::SetUTCDate(xSYSDATE *pDate)
{
      m_xUTCDate = *pDate;
      cDATE::SetUTCDate(m_xUTCDate.nYear,m_xUTCDate.nMonth,m_xUTCDate.nDay);
}
void  cDATE::GetUTCDate(int *pYear,int *pMonth,int *pDay)
{
      *pYear  = m_xUTCDate.nYear;
      *pMonth = m_xUTCDate.nMonth;
      *pDay   = m_xUTCDate.nDay;
}
void  cDATE::GetUTCDate(xSYSDATE *pDate)
{
      *pDate = m_xUTCDate;
}
void  cDATE::IncrementDate(void)
{
      IncrementDate(&m_xUTCDate);
}
void  cDATE::IncrementDate(xSYSDATE *pDate)
{
      ++pDate->nDay;
      if (pDate->nDay > 31 || (pDate->nDay == 31 && Is31DayInMonth(pDate->nMonth) == 0))
         {
          pDate->nDay = 1;
          ++pDate->nMonth;
          if (pDate->nMonth > 12)
             {
              pDate->nMonth = 1;
              ++pDate->nYear;
             }
         }
      if (pDate->nDay > 28 && pDate->nMonth == 2)
         {
          if (!IsLeapYear(pDate->nYear) || pDate->nDay > 29)
             {
              pDate->nDay = 1;
              ++pDate->nMonth;
             }
         }
}
void  cDATE::DecrementDate(void)
{
      DecrementDate(&m_xUTCDate);
}
void  cDATE::DecrementDate(xSYSDATE *pDate)
{
      --pDate->nDay;
      if (pDate->nDay < 1)
         {
          --pDate->nMonth;
          if (pDate->nMonth < 1)
             {
              pDate->nMonth = 12;
              --pDate->nYear;
             }
          if (Is31DayInMonth(pDate->nMonth))
              pDate->nDay = 31;
          else
             {
              if (pDate->nMonth == 2)
                 {
                  if (IsLeapYear(pDate->nYear))
                      pDate->nDay = 29;
                  else
                      pDate->nDay = 28;
                 }
              else
                  pDate->nDay = 30;
             }
         }
}
void  cDATE::AddDate(int nDays)
{
      AddDate(&m_xUTCDate,nDays);
}
void  cDATE::AddDate(xSYSDATE *pDate,int nDays)
{
      while (nDays-- > 0)
             IncrementDate(pDate);
}
void  cDATE::SubDate(int nDays)
{
      SubDate(&m_xUTCDate,nDays);
}
void  cDATE::SubDate(xSYSDATE *pDate,int nDays)
{
      while (nDays-- > 0)
             DecrementDate(pDate);
}
int   cDATE::IsLeapYear(int nYear)
{
      if ((nYear / 400 * 400) == nYear)
          return(1);
      if ((nYear / 100 * 100) == nYear)
          return(0);
      if ((nYear / 4   * 4  ) == nYear)
          return(1);
      return(0);
}
int   cDATE::Is31DayInMonth(int nMonth)
{
      if (nMonth ==  1 || nMonth ==  3 || nMonth ==  5 ||
          nMonth ==  7 || nMonth ==  8 || nMonth == 10 || nMonth == 12)
          return(1);
      return(0);
}
int   cDATE::IsValidDate(int nYear,int nMonth,int nDay)
{
      if (nYear < 1 || nYear > 9999 || nMonth < 1 || nMonth > 12 || nDay < 1 || nDay > 31)
          return(0);
      if (nDay > 28)
         {
          if (nMonth == 2)
             {
              if (!IsLeapYear(nYear) || (nDay != 29))
                  return(0);
             }
          else
             {
              if (nDay > 30)
                 {
                  if (!Is31DayInMonth(nMonth))
                      return(0);
                 }
             }
         }
      return(1);
}
int   cDATE::GetAllDays(int nYear,int nMonth,int nDay)
{
	    int  nTotal,i;

      nTotal = (nYear - 1) * 365 +
               (nYear - 1) / 400 -
               (nYear - 1) / 100 +
               (nYear - 1) /   4 + nDay;
      for (i = 1;i < nMonth;i++)
          {
           if (i == 2)
              {
               if (IsLeapYear(nYear))
                   nTotal += 29;
               else
                   nTotal += 28;
              }
           else
              {
               if (Is31DayInMonth(i))
                   nTotal += 31;
               else
                   nTotal += 30;
              }
          }
      return(nTotal);
}
int   cDATE::GetDaysCount(int nYear,int nMonth,int nDay)
{
	    int  nTotal,i;

      nTotal = nDay;
      for (i = 1;i < nMonth;i++)
          {
           if (i == 2)
              {
               if (IsLeapYear(nYear))
                   nTotal += 29;
               else
                   nTotal += 28;
              }
           else
              {
               if (Is31DayInMonth(i))
                   nTotal += 31;
               else
                   nTotal += 30;
              }
          }
      return(nTotal);
}
int   cDATE::GetDayOfWeek(int nYear,int nMonth,int nDay)
{
      int  nTotal;

      nTotal = cDATE::GetAllDays(nYear,nMonth,nDay);

      return(nTotal % 7);
}

void  cDATE::SetUTCTime(int nHour,int nMinute,int nSecond)
{
      m_xUTCTime.nHour  = nHour;
      m_xUTCTime.nMinute= nMinute;
      m_xUTCTime.nSecond= nSecond;

      SysSetUTCTime(nHour,nMinute,nSecond);
      SetSystemTimeUTC(nHour,nMinute,nSecond);
}
void  cDATE::SetUTCTime(xSYSTIME *pTime)
{
      m_xUTCTime = *pTime;
      cDATE::SetUTCTime(m_xUTCTime.nHour,m_xUTCTime.nMinute,m_xUTCTime.nSecond);
}
void  cDATE::GetUTCTime(int *pHour,int *pMinute,int *pSecond)
{
      *pHour  = m_xUTCTime.nHour;
      *pMinute= m_xUTCTime.nMinute;
      *pSecond= m_xUTCTime.nSecond;
}
void  cDATE::GetUTCTime(xSYSTIME *pTime)
{
      *pTime = m_xUTCTime;
}
void  cDATE::SetLocalTimeZone(int nLocalOffset)
{
      if (nLocalOffset < 0)
         {
          nLocalOffset  = -nLocalOffset;
          m_nLocalTimeS = LOCAL_TIME_ZONE_MINUS;
         }
      else
          m_nLocalTimeS = LOCAL_TIME_ZONE_PLUS;

      GetHourMinByTimeDiffValue(nLocalOffset,&m_nLocalTimeH,&m_nLocalTimeM);
}
void  cDATE::GetLocalTimeZone(int *pSign,int *pHour,int *pMinute)
{
      *pSign  = m_nLocalTimeS;
      *pHour  = m_nLocalTimeH;
      *pMinute= m_nLocalTimeM;
}
void  cDATE::GetLocalDateTime(xSYSDATE *pLocalDate,xSYSTIME *pLocalTime)
{
      xSYSDATE xDate;
      xSYSTIME xTime;

      cDATE::GetUTCDate(&xDate);
      cDATE::GetUTCTime(&xTime);

      if (m_nLocalTimeS == LOCAL_TIME_ZONE_PLUS)
         {
          xTime.nMinute += m_nLocalTimeM;
          if (xTime.nMinute >= 60)
             {
              xTime.nMinute -= 60;
              ++xTime.nHour;
             }

          xTime.nHour += m_nLocalTimeH;
          if (xTime.nHour >= 24)
             {
              xTime.nHour -= 24;
              IncrementDate(&xDate);
             }
         }
      else
         {
          xTime.nMinute -= m_nLocalTimeM;
          if (xTime.nMinute < 0)
             {
              xTime.nMinute += 60;
              --xTime.nHour;
             }

          xTime.nHour -= m_nLocalTimeH;
          if (xTime.nHour < 0)
             {
              xTime.nHour += 24;
              DecrementDate(&xDate);
             }
         }

      *pLocalDate = xDate;
      *pLocalTime = xTime;
}
void  cDATE::GetLocalDateTimeByUTC(xSYSDATE *pLocalDate,xSYSTIME *pLocalTime)
{
      xSYSDATE xDate;
      xSYSTIME xTime;

      xDate = *pLocalDate;
      xTime = *pLocalTime;

      if (m_nLocalTimeS == LOCAL_TIME_ZONE_PLUS)
         {
          xTime.nMinute += m_nLocalTimeM;
          if (xTime.nMinute >= 60)
             {
              xTime.nMinute -= 60;
              ++xTime.nHour;
             }

          xTime.nHour += m_nLocalTimeH;
          if (xTime.nHour >= 24)
             {
              xTime.nHour -= 24;
              IncrementDate(&xDate);
             }
         }
      else
         {
          xTime.nMinute -= m_nLocalTimeM;
          if (xTime.nMinute < 0)
             {
              xTime.nMinute += 60;
              --xTime.nHour;
             }

          xTime.nHour -= m_nLocalTimeH;
          if (xTime.nHour < 0)
             {
              xTime.nHour += 24;
              DecrementDate(&xDate);
             }
         }

      *pLocalDate = xDate;
      *pLocalTime = xTime;
}
void  cDATE::GetUTCDateTimeByLocal(xSYSDATE *pLocalDate,xSYSTIME *pLocalTime)
{
      xSYSDATE xDate;
      xSYSTIME xTime;

      xDate = *pLocalDate;
      xTime = *pLocalTime;

      if (m_nLocalTimeS == LOCAL_TIME_ZONE_PLUS)
         {
          xTime.nMinute -= m_nLocalTimeM;
          if (xTime.nMinute < 0)
             {
              xTime.nMinute += 60;
              --xTime.nHour;
             }

          xTime.nHour -= m_nLocalTimeH;
          if (xTime.nHour < 0)
             {
              xTime.nHour += 24;
              DecrementDate(&xDate);
             }
         }
      else
         {
          xTime.nMinute += m_nLocalTimeM;
          if (xTime.nMinute >= 60)
             {
              xTime.nMinute -= 60;
              ++xTime.nHour;
             }

          xTime.nHour += m_nLocalTimeH;
          if (xTime.nHour >= 24)
             {
              xTime.nHour -= 24;
              IncrementDate(&xDate);
             }
         }

      *pLocalDate = xDate;
      *pLocalTime = xTime;
}
void  cDATE::GetAddedDateTime(xSYSDATE *pDate,xSYSTIME *pTime,int nDays,int nHours,int nMins,int nSecs)
{
      pTime->nHour   += nHours;
      pTime->nMinute += nMins;
      pTime->nSecond += nSecs;

      while (pTime->nSecond >= 60)
            {
             pTime->nSecond -= 60;
             ++pTime->nMinute;
            }

      while (pTime->nMinute >= 60)
            {
             pTime->nMinute -= 60;
             ++pTime->nHour;
            }

      while (pTime->nHour >= 24)
            {
             pTime->nHour -= 24;
             ++nDays;
            }

      AddDate(pDate,nDays);
}
DWORD cDATE::GetTotalSecondsByDateTime(xSYSDATE xDate,xSYSTIME xTime)
{
      DWORD dTotalSecs;

      if (xDate.nYear < 2000)
         {
          xDate.nYear = 2000;
          xDate.nMonth= 1;
          xDate.nDay  = 1;
         }

      dTotalSecs = GetSecTime(xDate.nYear,xDate.nMonth,xDate.nDay,xTime.nHour,xTime.nMinute,xTime.nSecond);

      return(dTotalSecs);
}
DWORD cDATE::GetDiffSecondsByDateTime(xSYSDATE xDateC,xSYSTIME xTimeC,xSYSDATE xDateP,xSYSTIME xTimeP)
{
      DWORD dTotalSecsC;
      DWORD dTotalSecsP;

      dTotalSecsC = GetTotalSecondsByDateTime(xDateC,xTimeC);
      dTotalSecsP = GetTotalSecondsByDateTime(xDateP,xTimeP);

      return(dTotalSecsC - dTotalSecsP);
}

CHAR *cDATE::GetKoreanDateString(xSYSDATE *pDate,int nYearLen)
{
      static CHAR vTemp[16];

      if (nYearLen == 4)
          sprintf(vTemp,"%04d.%02d.%02d",pDate->nYear,pDate->nMonth,pDate->nDay);
      else
          sprintf(vTemp,"%02d.%02d.%02d",pDate->nYear % 100,pDate->nMonth,pDate->nDay);

      return(vTemp);
}
CHAR *cDATE::GetKoreanTimeString(xSYSTIME *pTime,int nModeT,int nModeS)
{
      static CHAR vTemp[16];
      CHAR vText[16];

      sprintf(vTemp,"%02d:%02d",pTime->nHour,pTime->nMinute);
      if (nModeS)
         {
          sprintf(vText,":%02d",pTime->nSecond);
          strcat(vTemp,vText);
         }

      return(vTemp);
}
SECTIME cDATE::GetSecTime(void)
{
      xSYSDATE xLocalDate;
      xSYSTIME xLocalTime;

      GetLocalDateTime(&xLocalDate,&xLocalTime);

      return(GetSecTime(xLocalDate.nYear,xLocalDate.nMonth,xLocalDate.nDay,xLocalTime.nHour,xLocalTime.nMinute,xLocalTime.nSecond));
}
SECTIME cDATE::GetSecTime(int nYear,int nMonth,int nDay,int nHour,int nMin,int nSec)
{
      DWORD dBaseDays;
      DWORD dCurrDays;
      DWORD dSecTime;

      dBaseDays = GetAllDays(BASE_DATE_YEAR,BASE_DATE_MONTH,BASE_DATE_DAY);
      dCurrDays = GetAllDays(nYear,nMonth,nDay);

      if (dCurrDays < dBaseDays || nYear > BASE_DATE_LAST)
          return(SEC_TIME_NULL);

      dSecTime = (dCurrDays - dBaseDays) * 24 * 3600;

      dSecTime += (nHour * 3600);
      dSecTime += (nMin  *   60);
      dSecTime += nSec;

      return(dSecTime);
}
CHAR *cDATE::GetSecDateString(SECTIME dSecTime,int nYearLen)
{
      // 2005.12.01 12:34:56
      static CHAR vTemp[24];
      int  nHour,nMin,nSec;
      DWORD dCurrDays,dCurrSecs;
      DWORD dTemp;
      xSYSDATE xDate;

      if (dSecTime == SEC_TIME_NULL)
         {
          if (nYearLen == 4)
              strcpy(vTemp,"                   ");
          else
              strcpy(vTemp,"                 ");
         }
      else
         {
          dCurrDays = dSecTime / (24 * 3600);
          dCurrSecs = dSecTime % (24 * 3600);

          xDate.nYear = BASE_DATE_YEAR;
          xDate.nMonth= BASE_DATE_MONTH;
          xDate.nDay  = BASE_DATE_DAY;

          AddDate(&xDate,dCurrDays);

          nHour = dCurrSecs / 3600;
          dTemp = dCurrSecs % 3600;
          nMin  = dTemp / 60;
          nSec  = dTemp % 60;

          sprintf(vTemp,"%04d.%02d.%02d %02d:%02d:%02d",xDate.nYear,xDate.nMonth,xDate.nDay,nHour,nMin,nSec);
         }

      return(vTemp);
}
CHAR *cDATE::GetSecTimeString(SECTIME dSecTime)
{
      // 12:34:56
      static CHAR vTemp[24];
      int  nHour,nMin,nSec;
      DWORD dTemp;

      if (dSecTime > (99 * 3600 + 59 * 60 + 59))
          dSecTime = (99 * 3600 + 59 * 60 + 59);

      nHour = dSecTime / 3600;
      dTemp = dSecTime % 3600;

      nMin  = dTemp / 60;
      nSec  = dTemp % 60;

      sprintf(vTemp,"%02d:%02d:%02d",nHour,nMin,nSec);

      return(vTemp);
}

void  cDATE::AddTimeHourMin(xSYSDATE *pDate,xSYSTIME *pTime,int nHour,int nMin)
{
      pTime->nMinute += nMin;
      while (pTime->nMinute >= 60)
            {
             pTime->nMinute -= 60;
             ++pTime->nHour;
            }

      pTime->nHour += nHour;
      while (pTime->nHour >= 24)
            {
             pTime->nHour -= 24;
             IncrementDate(pDate);
            }
}
void  cDATE::SubTimeHourMin(xSYSDATE *pDate,xSYSTIME *pTime,int nHour,int nMin)
{
      pTime->nMinute -= nMin;
      while (pTime->nMinute <  0)
            {
             pTime->nMinute += 60;
             --pTime->nHour;
            }

      pTime->nHour -= nHour;
      while (pTime->nHour <  0)
            {
             pTime->nHour += 24;
             DecrementDate(pDate);
            }
}
void  cDATE::AddSubTimeHourMin(xSYSDATE *pDate,xSYSTIME *pTime,int nHour,int nMin)
{
      pTime->nMinute += nMin;

      while (pTime->nMinute >= 60)
            {
             pTime->nMinute -= 60;
             ++pTime->nHour;
            }

      while (pTime->nMinute <  0)
            {
             pTime->nMinute += 60;
             --pTime->nHour;
            }

      pTime->nHour += nHour;

      while (pTime->nHour >= 24)
            {
             pTime->nHour -= 24;
             IncrementDate(pDate);
            }

      while (pTime->nHour <  0)
            {
             pTime->nHour += 24;
             DecrementDate(pDate);
            }
}

void  cDATE::ClearAllData(void)
{
      m_nLocalTimeS = LOCAL_TIME_ZONE_PLUS;
      m_nLocalTimeH = 0;
      m_nLocalTimeM = 0;
}

