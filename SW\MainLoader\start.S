/*.***********************************************************************.*/
/*.                  File Name : start.S                                  .*/
/*.                                                                       .*/
/*.                       Date : 2008.05.24                               .*/
/*.                                                                       .*/
/*.                    Version : 1.00                                     .*/
/*.                                                                       .*/
/*.***********************************************************************.*/

#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"

/***************************************************************************/
/*	Definitions																														 */
/***************************************************************************/

/***************************************************************************/
#define  MAIN_LOADER_VER                 "1.0.0"
#define  SET_CRIT_SECT                      0
/***************************************************************************/

/***************************************************************************/
/*	Public variables																											 */
/***************************************************************************/
      .global __G_MainBooterMsg
      .global _start
	    .global call_main
      .global SysGetStartOfHeapMemory
      .global SysGetLastOfHeapMemory
      .global SysGetStartOfFreeMemory
      .global SysEnableICache
      .global SysDisableICache
      .global SysDisableDCache
      .global SysEnableDCache
      .global SysDisableAlignFault
      .global SysEnableAlignFault
      .global SysDisableMMU
      .global SysEnableMMU
      .global SysSetFastBusMode
      .global SysSetAsyncBusMode
#ifdef  __POLLUX__
      .global SysSetTTBase
#else                           // SPICA
      .global SysSetTTBase0
      .global SysSetTTBase1
      .global SysSetTTBaseControl
#endif
      .global SysSetDomain
      .global SysInvalidateICache
      .global SysInvalidateICacheMVA
      .global SysPrefetchICacheMVA
      .global SysInvalidateDCache
      .global SysInvalidateDCacheMVA
      .global SysInvalidateIDCache
      .global SysCleanDCacheMVA
      .global SysCleanInvalidateDCacheMVA
      .global SysCleanDCacheIndex
      .global SysCleanInvalidateDCacheIndex
      .global SysCleanAllDCache926
      .global SysCleanFlushAllDCache926
      .global SysWaitForInterrupt
      .global SysInvalidateTLB
      .global SysInvalidateITLB
      .global SysInvalidateITLBMVA
      .global SysInvalidateDTLB
      .global SysInvalidateDTLBMVA
      .global SysSetDCacheLockdownBase
      .global SysSetICacheLockdownBase
      .global SysSetDTLBLockdown
      .global SysSetITLBLockdown
      .global SysSetProcessID
      .global SysSetResetVectorLow
      .global SysSetResetVectorHigh
#ifdef  __SPICA__
      .global SysSetNormalVectorBaseAddr
      .global SysSetMonitorVectorBaseAddr
      .global SysGetInterruptStatusRegister
      .global SysSetPeriPortReMapRegister
#endif
      .global SysEnableIRQ
      .global SysDisableIRQ
      .global SysEnableFIQ
      .global SysDisableFIQ
      .global SysSaveStatusRegInCPU
      .global SysRestStatusRegInCPU

	    .extern main
	    .extern __bss_beg__
	    .extern __bss_end__
	    .extern __stack_end__
	    .extern __data_beg__
	    .extern __data_end__
	    .extern __data+beg_src__

/***************************************************************************/
/*	Static variables																											 */
/***************************************************************************/

/***************************************************************************/
/*	Static functions																											 */
/***************************************************************************/

/***************************************************************************/
/*	Public functions																											 */
/***************************************************************************/

			.text
	    .code 32
/***************************************************************************/
start:
_start:
_mainCRTStartup:
/***************************************************************************/
    	b     reset
	    ldr	  pc,_undefined_instruction
	    ldr	  pc,_software_interrupt
	    ldr	  pc,_prefetch_abort
	    ldr	  pc,_data_abort
	    ldr	  pc,_not_used
	    ldr	  pc,_irq
	    ldr	  pc,_fiq

/***************************************************************************/
_undefined_instruction:
	    .word IsrHandlerUNDEF
_software_interrupt:
      .word IsrHandlerSWI
_prefetch_abort:
      b     .                    //.word IsrHandlerABORT
_data_abort:
      b     .                    //.word IsrHandlerABORT
_not_used:
      b     .                    //.word IsrHandlerABORT
_irq:
      .word IsrHandlerIRQ
_fiq:
   	  .word IsrHandlerFIQ
_direct:
      .word direct
/***************************************************************************/

/***************************************************************************/

	    .balignl 16,0xdeadbeef

/***************************************************************************/
__G_MainBooterMsg:
/***************************************************************************/
      .string  "MAIN Loader 1.0"
/***************************************************************************/

/***************************************************************************/
reset:
/***************************************************************************/
	    /*
	     * set the cpu to SVC32 mode
	     */
 
	    mrs	  r0,cpsr
	    bic	  r0,r0,#ARM_MODE_MASK                          // r0 = r0 & (~0x1f) = r0 & 0xffffffe0
	    orr	  r0,r0,#(ARM_ALL_INT_DISABLE | ARM_SVC_MODE)   // r0 = r0 | 0xd3
	    msr	  cpsr,r0
      nop
      nop
      nop
      nop

      bl	  CpuInitCrit
      nop
      nop
      nop
      nop

#ifdef  __SPICA__
  #ifdef FLOAT_MODE_HARD
      bl    EnableVFP
      nop
      nop
      nop
      nop
  #endif
#endif

      bl    InitStacks
      nop
      nop
      nop
      nop

/***************************************************************************/
ClearBSS:
/***************************************************************************/
	    mov   a2,#0			       // Fill value
	    mov		fp,a2			       // Null frame pointer
	    mov		r7,a2			       // Null frame pointer for Thumb

	    ldr		r1,_BSS_BEGIN		 // Start of memory block
	    ldr		r3,_BSS_END		   // End of memory block
	    subs	r3,r3,r1         // Length of block
	    beq		ClearBSSend
	    mov		r2,#0
ClearBSSloop:
	    strb	r2,[r1],#1
	    subs	r3,r3,#1
	    bgt		ClearBSSloop
ClearBSSend:
      nop
      nop

/***************************************************************************/
CopyRomDataToRam:
/***************************************************************************/
/*
//    ldr		r1,_DATA_BEGIN		  // Start of memory block
//    ldr		r2,_DATA_BEGIN_SRC	// End of memory block
//    ldr		r3,_DATA_END
//    subs	r3,r3,r1		      // Length of block
//    beq		CopyRomDataToRamEnd
CopyRomDataToRamLoop:
//    ldrb	r4,[r2],#1
//    strb	r4,[r1],#1
//    subs	r3,r3,#1
//    bgt		CopyRomDataToRamLoop
*/
CopyRomDataToRamEnd:
      nop
      nop
      nop

	    .align 2

1001:
/***************************************************************************/
call_main:
/***************************************************************************/
      mov   fp,#0            // no previous frame, so fp=0
      mov   a1,#0            // set argc to 0
      mov   a2,#0            // set argv to NUL
      bl    main             // call main

/***************************************************************************/
direct:
/***************************************************************************/
	    nop
	    nop
	    nop
	    nop

/***************************************************************************/
CpuInitCrit:
/***************************************************************************/
	    /*
	     * flush v4 I/D caches
	     */
    	mov	  r0,#0
	    mcr	  p15,0,r0,c7,c7,0	    // flush v3/v4 cache
	    mcr	  p15,0,r0,c8,c7,0	    // flush v4 TLB

	    /*
	     * disable MMU stuff and caches
	     */
#ifdef  __POLLUX__
	    mrc	  p15,0,r0,c1,c0,0
	    bic	  r0,r0, #0x00002300	  // clear bits 13, 9:8 (--V- --RS)
	    bic	  r0,r0, #0x00000087	  // clear bits 7, 2:0 (B--- -CAM)
	    orr	  r0,r0, #0x00000002	  // set bit 2 (A) Align
	    orr	  r0,r0, #0x00001000	  // set bit 12 (I) I-Cache
	    mcr	  p15,0,r0,c1,c0,0
	    mov	  pc,lr
#else                           // SPICA
  #if SET_CRIT_SECT == 1
	    mrc	  p15,0,r0,c1,c0,0
	    bic	  r0,r0, #ARM_R1_VE     // Interrupt vectors are fixed
	    bic	  r0,r0, #ARM_R1_V      // Vector Base Address Registers determine the address range
	    bic	  r0,r0, #ARM_R1_R      // ROM protection disabled
	    bic	  r0,r0, #ARM_R1_S      // MMU protection disabled
	    bic	  r0,r0, #ARM_R1_B      // Little-endian memory system
	    bic	  r0,r0, #ARM_R1_C      // Data cache disabled
	    bic	  r0,r0, #ARM_R1_M      // MMU disabled
	    orr	  r0,r0, #ARM_R1_A      // Strict alignment fault checking enabled
	    mcr	  p15,0,r0,c1,c0,0
	    nop
	    nop

	    mrc	  p15,0,r0,c1,c0,0
	    orr	  r0,r0, #ARM_R1_I      // Instruction Cache enabled
	    mcr	  p15,0,r0,c1,c0,0
	    nop
	    nop

      mov   r0, #0
      mcr   p15,0,r0,c8,c7,0      // flush both TLB
      mcr   p15,0,r0,c7,c5,0      // invalidate instruction cache
      mcr   p15,0,r0,c7,c6,0      // invalidate data cache
	    nop
	    nop

      ldr   r0,=CPU_IORW_PHSY_BASE_ADDR  // Base Addres : 0xC0000000
	    orr	  r0,r0, #0x13                 // Size (0x13) : 256 MB
      mcr   p15,0,r0,c15,c2,4            // Peripheral ReMap
	    nop
	    nop

	    mrc	  p15,0,r0,c1,c0,0
	    orr	  r0,r0, #ARM_R1_Z      // Program flow prediction enabled
	    mcr	  p15,0,r0,c1,c0,0
	    nop
	    nop
	#endif

	    nop
	    nop
	    nop
	    nop
	    nop
	    nop

	    mov	  pc,lr
#endif


#ifdef  __SPICA__
/***************************************************************************/
EnableVFP:
/***************************************************************************/

      mrc   p15,0,r0,c1,c0,2
      orr   r0,r0,#(0xf << 20)          // enable full access for p10,11
      mcr   p15,0,r0,c1,c0,2            // Access Control Register = r1
      mov   r0,#0
      mcr   p15,0,r0,c7,c5,4            // flush prefetch buffer because of FMXR below and
      nop                               // CP 10 & 11 were only just enabled


//    fmrx  r0,FPSID

//    mov   r0,#VFP_ENABLE
//    fmxr  FPEXC,r0
//    mov   pc,lr

      .word 0xeef00a10       // fmrx  r0,fpsid
      .word 0xe3a00101       // mov   r0,#0x40000000
      .word 0xeee80a10       // fmxr  fpexc,r0

      mov   pc,lr
#endif


/***************************************************************************/
InitStacks:
/***************************************************************************/
	    /* Setup a stack for each mode - note that this only sets up a usable stack
	     * for system/user, SWI and IRQ modes.   Also each mode is setup with
	     * interrupts initially disabled.
	     */
	    /*   Stack  Memory
	     *
	     *   +---------------------------------------+
	     *   |                                       |
	     *   |                                       |
	     *   |       SVC Mode Stack : 0x0017bf00     |
	     *   +---------------------------------------+ <--- SVC-SP
	     *   |                                       |
	     *   |                                       |
	     *   |       SYS Mode Stack : 0x00002000     |
	     *   +---------------------------------------+ <--- SYS-SP
	     *   |                                       |
	     *   |                                       |
	     *   |       IRQ Mode Stack : 0x00002000     |
	     *   +---------------------------------------+ <--- IRQ-SP
	     *   |                                       |
	     *   |                                       |
	     *   |       FIQ Mode Stack : 0x00000080     |
	     *   +---------------------------------------+ <--- FIQ-SP
	     *   |                                       |
	     *   |                                       |
	     *   |       ABT Mode Stack : 0x00000040     |
	     *   +---------------------------------------+ <--- ABT-SP
	     *   |                                       |
	     *   |                                       |
	     *   |       UND Mode Stack : 0x00000040     |
	     *   +---------------------------------------+ <--- UND-SP : STACK_POINTER
       *
       */
      ldr   r0,_STACK_POINTER
      msr   CPSR_c,#(ARM_UND_MODE | ARM_ALL_INT_DISABLE)
      mov   sp,r0
      sub   r0,r0,#UND_STACK_SIZE

      msr   CPSR_c,#(ARM_ABT_MODE | ARM_ALL_INT_DISABLE)
      mov   sp,r0
      sub   r0,r0,#ABT_STACK_SIZE

      msr   CPSR_c,#(ARM_FIQ_MODE | ARM_ALL_INT_DISABLE)
      mov   sp,r0
      sub   r0,r0,#FIQ_STACK_SIZE

      msr   CPSR_c,#(ARM_IRQ_MODE | ARM_ALL_INT_DISABLE)
      mov   sp,r0
      sub   r0,r0,#IRQ_STACK_SIZE

      msr   CPSR_c,#(ARM_SYS_MODE | ARM_ALL_INT_DISABLE)
      mov   sp, r0
      sub   r0,r0,#SYS_STACK_SIZE

      msr   CPSR_c,#(ARM_SVC_MODE | ARM_ALL_INT_DISABLE)
      mov   sp,r0
      sub   r0,r0,#SVC_STACK_SIZE

	    /*
	     * USER mode has not be initialized.
	     */

	    mov	  pc,lr

     .pool

	    .align 4

/***************************************************************************/
_TEXT_BEGIN:
        .word   __begin_of_text__
_TEXT_END:
        .word   __end_of_text__
/***************************************************************************/
_DATA_BEGIN:
	      .word	  __data_beg__
_DATA_BEGIN_SRC:
	      .word	  __data_beg_src__
_DATA_END:
	      .word	  __data_end__
/***************************************************************************/
_BSS_BEGIN:
	      .word   __bss_beg__
_BSS_END:
        .word   __bss_end__
/***************************************************************************/
_HEAP_BEGIN:
	      .word	  __heap_begin__
_HEAP_END:
	      .word	  __heap_end__
/***************************************************************************/
_STACK_POINTER:
	      .word	  __StackPointer__
/***************************************************************************/
_FREE_MEM_START:
	      .word	  __StartOfFreeRam__
/***************************************************************************/


/***************************************************************************
 *
 *	__main,	_gccmain
 *..........................................................................
 *
 *	-------------
 *	Description	:
 *	-------------
 *
 *	Dummy	functions	called by	main() function.
 *
 *	GNU-gcc	2.8.1	:	main() calls __main
 *	GNU-gcc	2.9		:	main() calls __gccmain
 *
 *	-------------
 *	Return values	:
 *	---------------
 *
 *	None
 *
 ***************************************************************************/

/***************************************************************************/
__main:
/***************************************************************************/
	    mov	  pc,lr

/***************************************************************************/
__gccmain:
/***************************************************************************/
	    mov	  pc,lr

/****************************************************************************
long  SysGetStartOfHeapMemory(void);
****************************************************************************/
SysGetStartOfHeapMemory:
	    ldr	  r0,_HEAP_BEGIN
	    mov	  pc,lr

/****************************************************************************
long  SysGetLastOfHeapMemory(void);
****************************************************************************/
SysGetLastOfHeapMemory:
	    ldr	  r0,_HEAP_END
	    mov	  pc,lr

/****************************************************************************
long  SysGetStartOfFreeMemory(void);
****************************************************************************/
SysGetStartOfFreeMemory:
	    ldr	  r0,_FREE_MEM_START
	    mov	  pc,lr

/****************************************************************************
void  SysDisableICache(void);
****************************************************************************/
SysDisableICache:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      bic   r0,r0,#ARM_R1_I
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c7,c7,0    // Invalidate Entire I&D Cache
      nop
      nop
      nop
      nop
      nop

      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      bic   r0,r0,#ARM_R1_I
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysEnableICache(void);
****************************************************************************/
SysEnableICache:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      orr   r0,r0,#ARM_R1_I
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c7,c7,0    // Invalidate Entire I&D Cache
      nop
      nop
      nop
      nop
      nop

      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      orr   r0,r0,#ARM_R1_I
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysDisableDCache(void);
****************************************************************************/
SysDisableDCache:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      bic   r0,r0,#ARM_R1_C
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c7,c7,0    // Invalidate Entire I&D Cache
      nop
      nop
      nop
      nop
      nop

      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      bic   r0,r0,#ARM_R1_C
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysEnableDCache(void);
****************************************************************************/
SysEnableDCache:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      orr   r0,r0,#ARM_R1_C
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c7,c7,0    // Invalidate Entire I&D Cache
      nop
      nop
      nop
      nop
      nop

      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      orr   r0,r0,#ARM_R1_C
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysDisableAlignFault(void);
****************************************************************************/
SysDisableAlignFault:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      bic   r0,r0,#ARM_R1_A
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      bic   r0,r0,#ARM_R1_A
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysEnableAlignFault(void);
****************************************************************************/
SysEnableAlignFault:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      orr   r0,r0,#ARM_R1_A
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      orr   r0,r0,#ARM_R1_A
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif


/****************************************************************************
void  SysDisableMMU(void);
****************************************************************************/
SysDisableMMU:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      bic   r0,r0,#ARM_R1_M
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      bic   r0,r0,#ARM_R1_M
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysEnableMMU(void);
****************************************************************************/
SysEnableMMU:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      orr   r0,r0,#ARM_R1_M
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      mrc   p15,0,r0,c1,c0,0    // Read CP15 register 1 into r0
      orr   r0,r0,#ARM_R1_M
      mcr   p15,0,r0,c1,c0,0    // Write cp15 register 1
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetFastBusMode(void);     FCLK:HCLK = 1:1
****************************************************************************/
SysSetFastBusMode:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      bic   r0,r0,#(ARM_R1_iA | ARM_R1_nF)
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetAsyncBusMode(void);    FCLK:HCLK = 1:2
****************************************************************************/
SysSetAsyncBusMode:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      orr   r0,r0,#(ARM_R1_nF | ARM_R1_iA)
      mcr   p15,0,r0,c1,c0,0
      nop
      nop
      nop
      nop
      nop
	    mov	  pc,lr
#else                           // SPICA
      nop
      nop
	    mov	  pc,lr
#endif

#ifdef  __POLLUX__
/****************************************************************************
void  SysSetTTBase(DWORD dBase);
****************************************************************************/
SysSetTTBase:
      mcr   p15,0,r0,c2,c0,0
	    mov	  pc,lr
#else                           // SPICA
/****************************************************************************
void  SysSetTTBase0(DWORD dBase);
****************************************************************************/
SysSetTTBase0:
      mcr   p15,0,r0,c2,c0,0
	    mov	  pc,lr

SysSetTTBase1:
      mcr   p15,0,r0,c2,c0,1
	    mov	  pc,lr

SysSetTTBaseControl:
      mcr   p15,0,r0,c2,c0,2
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetDomain(DWORD dDomain);
****************************************************************************/
SysSetDomain:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c3,c0,0
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c3,c0,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateICache(void);
****************************************************************************/
SysInvalidateICache:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c5,0
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c7,c5,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateICacheMVA(DWORD dMVA);
****************************************************************************/
SysInvalidateICacheMVA:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c5,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c7,c5,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysPrefetchICacheMVA(DWORD dMVA);
****************************************************************************/
SysPrefetchICacheMVA:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c13,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c7,c13,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateDCache(void);
****************************************************************************/
SysInvalidateDCache:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c6,0
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c7,c6,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateDCacheMVA(DWORD dMVA);
****************************************************************************/
SysInvalidateDCacheMVA:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c6,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c7,c6,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateIDCache(void);
****************************************************************************/
SysInvalidateIDCache:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c7,0
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c7,c7,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysCleanDCacheMVA(DWORD dMVA);
****************************************************************************/
SysCleanDCacheMVA:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c10,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c7,c10,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysCleanInvalidateDCacheMVA(DWORD dMVA);
****************************************************************************/
SysCleanInvalidateDCacheMVA:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c14,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c7,c14,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysCleanDCacheIndex(DWORD nIndex);
****************************************************************************/
SysCleanDCacheIndex:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c10,2
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c7,c10,2
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysCleanInvalidateDCacheIndex(DWORD nIndex);
****************************************************************************/
SysCleanInvalidateDCacheIndex:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c14,2
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c7,c14,2
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysCleanAllDCache926(void);
****************************************************************************/
SysCleanAllDCache926:
#ifdef  __POLLUX__
      mrc   p15,0,pc,c7,c10,3
      bne   SysCleanAllDCache926
	    mov	  pc,lr
#else                           // SPICA
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysCleanFlushAllDCache926(void);
****************************************************************************/
SysCleanFlushAllDCache926:
#ifdef  __POLLUX__
      mrc   p15,0,pc,c7,c14,3
      bne   SysCleanFlushAllDCache926
	    mov	  pc,lr
#else                           // SPICA
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysWaitForInterrupt(void);
****************************************************************************/
SysWaitForInterrupt:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c7,c0,4
	    mov	  pc,lr
#else                           // SPICA
      nop
      nop
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateTLB(void);
****************************************************************************/
SysInvalidateTLB:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c8,c7,0
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c8,c7,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateITLB(void);
****************************************************************************/
SysInvalidateITLB:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c8,c5,0
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c8,c5,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateITLBMVA(DWORD dMVA);
****************************************************************************/
SysInvalidateITLBMVA:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c8,c5,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c8,c5,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateDTLB(void);
****************************************************************************/
SysInvalidateDTLB:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c8,c6,0
	    mov	  pc,lr
#else                           // SPICA
      mov   r0,#0
      mcr   p15,0,r0,c8,c6,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysInvalidateDTLBMVA(DWORD dMVA);
****************************************************************************/
SysInvalidateDTLBMVA:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c8,c6,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c8,c6,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetDCacheLockdownBase(DWORD dBase);
****************************************************************************/
SysSetDCacheLockdownBase:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c9,c0,0
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c9,c0,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetICacheLockdownBase(DWORD dBase);
****************************************************************************/
SysSetICacheLockdownBase:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c9,c0,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c9,c0,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetDTLBLockdown(DWORD dBase);
****************************************************************************/
SysSetDTLBLockdown:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c10,c0,0
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c10,c0,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetITLBLockdown(DWORD dBase);
****************************************************************************/
SysSetITLBLockdown:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c10,c0,1
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c10,c0,1
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetProcessID(DWORD dID);
****************************************************************************/
SysSetProcessID:
#ifdef  __POLLUX__
      mcr   p15,0,r0,c13,c0,0
	    mov	  pc,lr
#else                           // SPICA
      mcr   p15,0,r0,c13,c0,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetResetVectorLow(void);
****************************************************************************/
SysSetResetVectorLow:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      bic   r0,r0,#ARM_R1_V
      mcr   p15,0,r0,c1,c0,0
	    mov	  pc,lr
#else                           // SPICA
      mrc   p15,0,r0,c1,c0,0
      bic   r0,r0,#ARM_R1_V
      mcr   p15,0,r0,c1,c0,0
	    mov	  pc,lr
#endif

/****************************************************************************
void  SysSetResetVectorHigh(void);
****************************************************************************/
SysSetResetVectorHigh:
#ifdef  __POLLUX__
      mrc   p15,0,r0,c1,c0,0
      orr   r0,r0,#ARM_R1_V
      mcr   p15,0,r0,c1,c0,0
	    mov	  pc,lr
#else                           // SPICA
      mrc   p15,0,r0,c1,c0,0
      orr   r0,r0,#ARM_R1_V
      mcr   p15,0,r0,c1,c0,0
	    mov	  pc,lr
#endif

#ifdef  __SPICA__
/****************************************************************************
void  SysSetNormalVectorBaseAddr(DWORD dBaseAddr);
****************************************************************************/
SysSetNormalVectorBaseAddr:
      mcr   p15,0,r0,c12,c0,0
	    mov	  pc,lr
#endif

#ifdef  __SPICA__
/****************************************************************************
void  SysSetMonitorVectorBaseAddr(DWORD dBaseAddr);
****************************************************************************/
SysSetMonitorVectorBaseAddr:
      mcr   p15,0,r0,c12,c0,1
	    mov	  pc,lr
#endif


#ifdef  __SPICA__
/****************************************************************************
DWORD SysGetInterruptStatusRegister(void);
****************************************************************************/
SysGetInterruptStatusRegister:
      mrc   p15,0,r0,c12,c1,0
	    mov	  pc,lr
#endif

#ifdef  __SPICA__
/****************************************************************************
void  SysSetPeriPortReMapRegister(DWORD dBaseAddr);
****************************************************************************/
SysSetPeriPortReMapRegister:
      mcr   p15,0,r0,c15,c2,4
	    mov	  pc,lr
#endif


/****************************************************************************
void  SysEnableIRQ(void);
****************************************************************************/
SysEnableIRQ:
	    stmdb sp!,{R0}                              // push r0
	    mrs	  r0,cpsr
	    bic	  r0,r0,#ARM_IRQ_DISABLE                // r0 = r0 & ARM_IRQ_DISABLE
	    msr	  cpsr,r0
	    ldmia	sp!,{R0}                              // pop r0
	    mov	  pc,lr

/****************************************************************************
void  SysDisableIRQ(void);
****************************************************************************/
SysDisableIRQ:
	    stmdb sp!,{R0}                              // push r0
	    mrs	  r0,cpsr
	    orr	  r0,r0,#ARM_IRQ_DISABLE                // r0 = r0 | ARM_IRQ_DISABLE
	    msr	  cpsr,r0
	    ldmia	sp!,{R0}                              // pop r0
	    mov	  pc,lr

/****************************************************************************
void  SysEnableFIQ(void);
****************************************************************************/
SysEnableFIQ:
	    stmdb sp!,{R0}                              // push r0
	    mrs	  r0,cpsr
	    bic	  r0,r0,#ARM_FIQ_DISABLE                // r0 = r0 & ARM_FIQ_DISABLE
	    msr	  cpsr,r0
	    ldmia	sp!,{R0}                              // pop r0
	    mov	  pc,lr

/****************************************************************************
void  SysDisableFIQ(void);
****************************************************************************/
SysDisableFIQ:
	    stmdb sp!,{R0}                              // push r0
	    mrs	  r0,cpsr
	    orr	  r0,r0,#ARM_FIQ_DISABLE                // r0 = r0 | ARM_FIQ_DISABLE
	    msr	  cpsr,r0
	    ldmia	sp!,{R0}                              // pop r0
	    mov	  pc,lr

/****************************************************************************
DWORD SysSaveStatusRegInCPU(void);
****************************************************************************/
SysSaveStatusRegInCPU:
	    stmdb sp!,{R1}                              // push r1
	    mrs	  r0,cpsr
	    orr	  r1,r0,#ARM_IRQ_DISABLE                // r1 = r1 | ARM_IRQ_DISABLE
	    msr	  cpsr,r1
	    ldmia	sp!,{R1}                              // pop r1
	    mov	  pc,lr

/****************************************************************************
void  SysRestStatusRegInCPU(DWORD dStatusReg);
****************************************************************************/
SysRestStatusRegInCPU:
	    msr	  cpsr,r0
	    mov	  pc,lr

