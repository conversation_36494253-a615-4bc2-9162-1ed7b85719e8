/*...........................................................................*/
/*.                  File Name : UART.CPP                                   .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#if defined(_EN_DBG_MSG_)
	#include <stdio.h>
	#include <string.h>
	#include <stdarg.h> 
#endif

#include "type.hpp"
#include "sysconst.h"
#include "syslib.h"
#include "comlib.h"
#include "uart.hpp"

cUART::cUART(DWORD dBaseAddr,int Speed,BYTE Parity,int DataBit,int StopBit,int TxInt,int RxInt)
{
	m_dBaseAddr = dBaseAddr;
	m_nRxHead = 0;
	m_nRxTail = 0;
	m_nTxHead = 0;
	m_nTxTail = 0;
	m_nRxTemp = 0;
	m_nSending= 0;
	m_pRxBuff = new BYTE[UART_RX_BUFF_SIZE];
	m_pTxBuff = new BYTE[UART_TX_BUFF_SIZE];
#if defined(_EN_DBG_MSG_)	
	m_pDbgMsgBuf = new BYTE[UART_DBG_MSG_BUF_SIZE];
#endif

	m_pSysUART = (xSYS_UART *)dBaseAddr;

	Parity = GetUartParaParity(Parity);
	DataBit = GetUartParaDataBit(DataBit);
	StopBit = GetUartParaStopBit(StopBit);

	SysSetUART(m_pSysUART,Speed,Parity,DataBit,StopBit,TxInt,RxInt,UART_TX_FIFO_TRG_LEVEL_12,UART_RX_FIFO_TRG_LEVEL_1);
}

cUART::~cUART(void)
{
	delete [] m_pRxBuff;
	delete [] m_pTxBuff;
#if defined(_EN_DBG_MSG_)	
	delete [] m_pDbgMsgBuf;
#endif	
}

void cUART::SetUartPara(int Speed,BYTE Parity,int DataBit,int StopBit,int TxInt,int RxInt)
{
	Parity = GetUartParaParity(Parity);
	DataBit = GetUartParaDataBit(DataBit);
	StopBit = GetUartParaStopBit(StopBit);

	SysSetUartBaudRate(m_pSysUART,Speed,Parity,DataBit,StopBit);
}
int cUART::GetComData(void)
{
	int  Data;

	if (m_nRxHead != m_nRxTail)
	{
		Data = m_pRxBuff[m_nRxTail];
		++m_nRxTail;
		if (m_nRxTail >= UART_RX_BUFF_SIZE)
			m_nRxTail = 0;
	}
	else
	{
		Data = -1;
	}		
	return(Data);
}

void cUART::PutComData(BYTE Data)
{
	m_pTxBuff[m_nTxHead] = Data;
	++m_nTxHead;
	if (m_nTxHead >= UART_TX_BUFF_SIZE)
	{
		m_nTxHead  = 0;
	}		
}

int cUART::ReadComData(BYTE *pData,int nSize)
{
	int  nData;
	int  nCount;

	nCount = 0;
	while (nSize--)
	{
		nData = GetComData();
		if (nData == -1)
			break;
		*pData++ = (BYTE)nData;
		nCount++;
	}
	return(nCount);
}
void  cUART::WriteComData(const BYTE *pData,int nSize)
{
	while (nSize--)
		PutComData(*pData++);

	if (IsSendingData() == 0)
	{
		m_nSending = 1;
		SysSetUartTxIntEnable(m_pSysUART);
		SysSetUartSyncPendClear(m_pSysUART);
	}
}

#if defined(_EN_DBG_MSG_)	
void cUART::OutputDbgMsg(const char *format,...)
{
	va_list arg;
	va_start(arg, format);
	memset(m_pDbgMsgBuf,0x00,UART_DBG_MSG_BUF_SIZE);
	vsprintf((char *)m_pDbgMsgBuf,(char *)format, arg);
	WriteComData(m_pDbgMsgBuf,strlen((char *)m_pDbgMsgBuf));
	va_end(arg);
}
#endif

int cUART::IsSendingData(void)
{
	return(m_nSending);
}
void  cUART::RunRxIntHandler(void)
{
	HWORD wTempX;
	int   nCount;
	UCHAR bData;

	nCount = m_pSysUART->wFSTATUS & 0x000f;
	if (m_pSysUART->wFSTATUS & (1 << 8))
		nCount = 16;                                  // Rx FIFO Full

	while (nCount > 0)
	{
		bData = m_pSysUART->wRHB;

		m_pRxBuff[m_nRxHead] = bData;

		++m_nRxHead;
		if (m_nRxHead >= UART_RX_BUFF_SIZE)
			m_nRxHead  = 0;

		if (m_nRxHead == m_nRxTail)
		{
			++m_nRxTail;
			if (m_nRxTail >= UART_RX_BUFF_SIZE)
				m_nRxTail  = 0;
		}
		--nCount;
	}

	wTempX = m_pSysUART->wINTCON & 0xfff0;
	m_pSysUART->wINTCON = wTempX | (1 << 1);          // Rx Interrupt Flag Clear
}
void  cUART::RunTxIntHandler(void)
{
	HWORD wTempX;
	int   nCount;

	if (m_nTxTail == m_nTxHead)
		m_nSending = 0;

	nCount = 16 - ((m_pSysUART->wFSTATUS >> 4) & 0x000f);

	while (nCount > 0 && (m_nTxTail != m_nTxHead))
	{
		m_pSysUART->wTHB = m_pTxBuff[m_nTxTail];

		++m_nTxTail;
		
		if (m_nTxTail >= UART_TX_BUFF_SIZE)
			m_nTxTail  = 0;

		--nCount;
	}

	if (m_nTxTail == m_nTxHead)
	{
		m_nSending = 0;
		SysSetUartTxIntDisable(m_pSysUART);            // Disable Tx Interrupt
	}

	wTempX = m_pSysUART->wINTCON & 0xfff0;
	m_pSysUART->wINTCON = wTempX | (1 << 0);           // Tx Interrupt Flag Clear
}

void  cUART::RunUartIsrHandler(void)
{
	HWORD wIntStatus;
	HWORD wTempX;
	int   nCount;
	UCHAR bData;

	wIntStatus = m_pSysUART->wINTCON;

	if (wIntStatus & (1 << 3))                      // Modem
	{
		wTempX = m_pSysUART->wINTCON & 0xfff0;
		m_pSysUART->wINTCON = wTempX | (1 << 3);    // Md Interrupt Flag Clear
	}

	if (wIntStatus & (1 << 2))                      // Error
	{
		wTempX = m_pSysUART->wINTCON & 0xfff0;

		m_pSysUART->wINTCON = wTempX | (1 << 2);      // Er Interrupt Flag Clear

		if ((m_pSysUART->wFSTATUS & (1 << 8)) 
			&& !(wIntStatus & (1 << 1))
		)
		{
			for (nCount = 0;nCount < 16;nCount++)
			{
				bData = m_pSysUART->wRHB;

				m_pRxBuff[m_nRxHead] = bData;

				++m_nRxHead;
				if (m_nRxHead >= UART_RX_BUFF_SIZE)
					m_nRxHead = 0;
			}
		}

		wTempX = m_pSysUART->wESTATUS;                // Clear Error Status
		wTempX = m_pSysUART->wFSTATUS;                // Clear FIFO  Status
	}

	if ((wIntStatus & (1 << 1))          ||           // Rx
		(m_pSysUART->wFSTATUS & 0x010f))
		RunRxIntHandler();

	if (wIntStatus & (1 << 0))                        // Tx
		RunTxIntHandler();
}

BYTE  cUART::GetUartParaParity(BYTE Parity)
{
      if (Parity == 'O' || Parity == 'o')  return(UART_PARITY_ODD);
      if (Parity == 'E' || Parity == 'e')  return(UART_PARITY_EVEN);
//    if (Parity == 'N' || Parity == 'n')  return(UART_PARITY_NONE);

      return(UART_PARITY_NONE);
}
int   cUART::GetUartParaDataBit(int DataBit)
{
      if (DataBit == 5) return(UART_DATA_BIT_5);
      if (DataBit == 6) return(UART_DATA_BIT_6);
      if (DataBit == 7) return(UART_DATA_BIT_7);
//    if (DataBit == 8) return(UART_DATA_BIT_8);

      return(UART_DATA_BIT_8);
}
int   cUART::GetUartParaStopBit(int StopBit)
{
      if (StopBit == 2) return(UART_STOP_BIT_2);
//    if (StopBit == 1) return(UART_STOP_BIT_1);

      return(UART_STOP_BIT_1);
}
