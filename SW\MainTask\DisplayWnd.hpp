#include "Wnd.hpp"
#include "CheckCtrl.hpp"

#ifndef __DISPLAY_WND_HPP__
#define __DISPLAY_WND_HPP__

//#define EN_LED_BRIGHTNESS

class CDisplayWnd : public CWnd {
private:
	enum {
#ifdef EN_LED_BRIGHTNESS
		FOCUS_BRIGHTNESS = 0,
		FOCUS_CONTRAST,
		FOCUS_INVERSE,
		FOCUS_CONTRAST_OFFSET
#else
		FOCUS_BRIGHTNESS = 0,
		FOCUS_INVERSE
#endif
	};

	CCheckCtrl *m_pLCDReverseMode;
	int         m_nBrightness;     /* 1~10(x 10) % */
	int         m_nContrast;       /* 1~10(x 10) % */
	int         m_nContrastOffset; /* -100 ~ 200   */
	BOOL        m_bInverse;
	BOOL        m_bShowContrastOffset;

	int   m_nOldScheme;
	int   m_nColoredOldScheme;
	int   m_nFocus;
	float m_fScale;
	float m_fOffsetScale;

	int   m_nMasterPassword;

public:
	CDisplayWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	void OnKeyEvent(int nKey, DWORD nFlags);
	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	void SetLCDRestore();
	void LoadSettingValue();
	int  SetLcdBrightLvl(int nBright);
	void ResetMenu() { m_bShowContrastOffset = 0; m_nMasterPassword = 0; }
};

#endif

