/*...........................................................................*/
/*.                  File Name : SYSMCUD.H                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.03                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSMCUD_H__
#define  __SYSMCUD_H__

//=============================================================================
#if defined(__POLLUX__)
    #define  MCUD_DIC_NORMAL                 0   // Normal driving
    #define  MCUD_DIC_WEAK                   1   // Weak driving

    #define  MCUD_DISDLL_ENABLE              0   // Enable
    #define  MCUD_DISDLL_DISABLE             1   // Disable

    #define  MCUD_SDRTYPE_DDR                1   // Normal DDR-SDRAM

    #define  MCUD_SDRBUSBW_16BIT             2   // 16-Bit Bus-Width

    #define  MCUD_SDRBW_08BIT                1   //  8-Bit Data-Bus
    #define  MCUD_SDRBW_16BIT                2   // 16-Bit Data-Bus

    #define  MCUD_SDRCAP_064MBIT             0   //  64MBit
    #define  MCUD_SDRCAP_128MBIT             1   // 128MBit
    #define  MCUD_SDRCAP_256MBIT             2   // 256MBit
    #define  MCUD_SDRCAP_512MBIT             3   // 512MBit

    #define  MCUD_LATENCY_2_0                1   // 2   cycle
    #define  MCUD_LATENCY_2_5                2   // 2.5 cycle
    #define  MCUD_LATENCY_3_0                3   // 3   cycle

    #define  MCUD_RDLAT_2_0                  1   // 2   cycle
    #define  MCUD_RDLAT_2_5                  2   // 2.5 cycle
    #define  MCUD_RDLAT_3_0                  3   // 3   cycle

    #define  MCUD_DELAY_0_0                  0   // No delay
    #define  MCUD_DELAY_0_5                  1   // 0.5 ns delay
    #define  MCUD_DELAY_1_0                  2   // 1.0 ns delay
    #define  MCUD_DELAY_1_5                  3   // 1.5 ns delay
    #define  MCUD_DELAY_2_0                  4   // 2.0 ns delay
    #define  MCUD_DELAY_2_5                  5   // 2.5 ns delay
    #define  MCUD_DELAY_3_0                  6   // 3.0 ns delay
    #define  MCUD_DELAY_3_5                  7   // 3.5 ns delay
#else                           // SPICA
    #define  MCUD_DISDLL_ENABLE              0   // Enable
    #define  MCUD_DISDLL_DISABLE             1   // Disable

    #define  MCUD_SDRTYPE_SDRAM              0   // SDRAM
    #define  MCUD_SDRTYPE_DDR                1   // DDR
    #define  MCUD_SDRTYPE_DDR2               2   // DDR2
    #define  MCUD_SDRTYPE_MDDR               3   // Mobile-DDR

    #define  MCUD_SDRBUSBW_16BIT             1   // 16-Bit Bus-Width

    #define  MCUD_SDRBW_08BIT                1   //  8-Bit Data-Bus
    #define  MCUD_SDRBW_16BIT                2   // 16-Bit Data-Bus

    #define  MCUD_SDRCAP_256MBIT             2   //  256MBit
    #define  MCUD_SDRCAP_512MBIT             3   //  512MBit
    #define  MCUD_SDRCAP_1024MBIT            4   // 1024MBit

    #define  MCUD_LATENCY_2_0                2   // 2   cycle
    #define  MCUD_LATENCY_3_0                3   // 3   cycle
    #define  MCUD_LATENCY_4_0                4   // 4   cycle
    #define  MCUD_LATENCY_5_0                5   // 5   cycle
    #define  MCUD_LATENCY_6_0                6   // 6   cycle

    #define  MCUD_ADD_LATENCY_0              0
    #define  MCUD_ADD_LATENCY_1              1
    #define  MCUD_ADD_LATENCY_2              2
    #define  MCUD_ADD_LATENCY_3              3
    #define  MCUD_ADD_LATENCY_4              4

    #define  MCUD_DLY_LATENCY_1              0
    #define  MCUD_DLY_LATENCY_2              1
    #define  MCUD_DLY_LATENCY_3              2
    #define  MCUD_DLY_LATENCY_4              3
    #define  MCUD_DLY_LATENCY_5              4

    #define  MCUD_DIC_NORMAL                 0   // Normal driving
    #define  MCUD_DIC_WEAK                   1   // Weak driving

    #define  MCUD_RTT_ODT                    0   // ODT disable
    #define  MCUD_RTT_075                    1   //  75 ohm
    #define  MCUD_RTT_150                    2   // 150 ohm
    #define  MCUD_RTT_050                    3   //  50 ohm

    #define  MCUD_NDQS_ENABLE                0
    #define  MCUD_NDQS_DISABLE               1

    #define  MCUD_DLL_ENABLE                 0
    #define  MCUD_DLL_DISABLE                1

    #define  MCUD_TIME_TMRD                  3
    #define  MCUD_TIME_TRP                   6
    #define  MCUD_TIME_TRCD                  6
    #define  MCUD_TIME_TRAS                 18
    #define  MCUD_TIME_TWR                   6
    #define  MCUD_TIME_TRFC                 51
    #define  MCUD_TIME_TWTR                  3
    #define  MCUD_TIME_TRTP                  3

    #define  MCUD_TIME_REFRESH             256

    #define  MCUD_PWR_DOWN_DISABLE           0
    #define  MCUD_PWR_DOWN_ENABLE            1

    #define  MCUD_ODT_DISABLE                0
    #define  MCUD_ODT_150_OHM                1
    #define  MCUD_ODT_75_OHM                 2
    #define  MCUD_ODT_50_OHM                 3

    #define  MCUD_PHYZQ_PULLUP               2    // The immediate control code for Pull-up   : 0 ~ 7 (2 is recommended)
    #define  MCUD_PHYZQ_PULLDN               5    // The immediate control code for Pull-down : 0 ~ 7 (5 is recommended)

    #define  MCUD_FAST_CHANNEL_MLC           0    // MLC 
    #define  MCUD_FAST_CHANNEL_VIP           1    // VIP
    #define  MCUD_FAST_CHANNEL_DISABLE      16    // Disable

    #define  MCUD_FAST_CHANNEL_00     MCUD_FAST_CHANNEL_MLC
    #define  MCUD_FAST_CHANNEL_01 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_02 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_03 MCUD_FAST_CHANNEL_DISABLE

    #define  MCUD_FAST_CHANNEL_04     MCUD_FAST_CHANNEL_VIP
    #define  MCUD_FAST_CHANNEL_05 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_06 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_07 MCUD_FAST_CHANNEL_DISABLE

    #define  MCUD_FAST_CHANNEL_08 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_09 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_10 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_11 MCUD_FAST_CHANNEL_DISABLE

    #define  MCUD_FAST_CHANNEL_12 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_13 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_14 MCUD_FAST_CHANNEL_DISABLE
    #define  MCUD_FAST_CHANNEL_15 MCUD_FAST_CHANNEL_DISABLE

    #define  MCUD_SLOW_CHANNEL_CPUI          0    // CPU instruction
    #define  MCUD_SLOW_CHANNEL_CPUD          1    // CPU data
    #define  MCUD_SLOW_CHANNEL_GRP3D         2    // 3D Graphic Engine
    #define  MCUD_SLOW_CHANNEL_MPEGTSP       3    // MPEG-TS Demux
    #define  MCUD_SLOW_CHANNEL_UHC           4    // USB Host Controller
    #define  MCUD_SLOW_CHANNEL_CSC           5    // Color Space Converter
    #define  MCUD_SLOW_CHANNEL_MPEG          6    // MPEG Engine
    #define  MCUD_SLOW_CHANNEL_ROTATOR       7    // Rotator
    #define  MCUD_SLOW_CHANNEL_SCALER        8    // Scaler
    #define  MCUD_SLOW_CHANNEL_H264          9    // H.264 Engine
    #define  MCUD_SLOW_CHANNEL_VLC          10    // VLC
    #define  MCUD_SLOW_CHANNEL_DEINTERLACE  11    // Deinterlace
    #define  MCUD_SLOW_CHANNEL_DMAGROUP0    12    // DMA Group 0
    #define  MCUD_SLOW_CHANNEL_DMAGROUP1    13    // DMA Group 1
    #define  MCUD_SLOW_CHANNEL_DMAGROUP2    14    // DMA Group 2
    #define  MCUD_SLOW_CHANNEL_DMAGROUP3    15    // DMA Group 3
    #define  MCUD_SLOW_CHANNEL_DISABLE      16    // Disable

    #define  MCUD_SLOW_CHANNEL_00   MCUD_SLOW_CHANNEL_DMAGROUP0
    #define  MCUD_SLOW_CHANNEL_01        MCUD_SLOW_CHANNEL_CPUI
    #define  MCUD_SLOW_CHANNEL_02        MCUD_SLOW_CHANNEL_CPUD
    #define  MCUD_SLOW_CHANNEL_03       MCUD_SLOW_CHANNEL_GRP3D

    #define  MCUD_SLOW_CHANNEL_04   MCUD_SLOW_CHANNEL_DMAGROUP1
    #define  MCUD_SLOW_CHANNEL_05     MCUD_SLOW_CHANNEL_MPEGTSP
    #define  MCUD_SLOW_CHANNEL_06         MCUD_SLOW_CHANNEL_UHC
    #define  MCUD_SLOW_CHANNEL_07         MCUD_SLOW_CHANNEL_CSC

    #define  MCUD_SLOW_CHANNEL_08   MCUD_SLOW_CHANNEL_DMAGROUP2
    #define  MCUD_SLOW_CHANNEL_09        MCUD_SLOW_CHANNEL_MPEG
    #define  MCUD_SLOW_CHANNEL_10     MCUD_SLOW_CHANNEL_ROTATOR
    #define  MCUD_SLOW_CHANNEL_11      MCUD_SLOW_CHANNEL_SCALER

    #define  MCUD_SLOW_CHANNEL_12   MCUD_SLOW_CHANNEL_DMAGROUP3
    #define  MCUD_SLOW_CHANNEL_13        MCUD_SLOW_CHANNEL_H264
    #define  MCUD_SLOW_CHANNEL_14         MCUD_SLOW_CHANNEL_VLC
    #define  MCUD_SLOW_CHANNEL_15 MCUD_SLOW_CHANNEL_DEINTERLACE
#endif
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

//=============================================================================
void  SysInitMCUD(void);
void  SysSetMCUdSDRType(HWORD wType);
void  SysSetMCUdSDRBusWidth(HWORD wBusWidth);
void  SysSetMCUdSDRDataWidth(HWORD wDataWidth);
void  SysSetMCUdSDRCapacity(HWORD wCapacity);
void  SysSetMCUdCasLatency(HWORD wCycle);
void  SysSetMCUdReadLatency(HWORD wCycle);
void  SysSetMCUdMDS(HWORD wMDS);
void  SysSetMCUdPASR(HWORD wPASR);
void  SysSetMCUdDIC(HWORD wDIC);
void  SysSetMCUdDLLEnable(int nDisableEnableMode);
void  SysSetMCUdApplyModeSetting(void);
int   SysGetMCUdBusyModeSetting(void);
void  SysSetMCUdMRD(HWORD wClocks);
void  SysSetMCUdRP(HWORD wClocks);
void  SysSetMCUdRCD(HWORD wClocks);
void  SysSetMCUdRC(HWORD wClocks);
void  SysSetMCUdRAS(HWORD wClocks);
void  SysSetMCUdWR(HWORD wClocks);
void  SysSetMCUdRefreshPeriod(HWORD wPeriod);
void  SysSetMCUdDisplayBlockMode(int nDisableEnableMode);
void  SysSetMCUdClockDelay(HWORD wDelay);
void  SysSetMCUdDQSDelay(HWORD wDQS0OUT,HWORD wDQS1OUT,HWORD wDQS0IN,HWORD wDQS1IN);
//=============================================================================
HWORD SysSetMCUdSetValue(HWORD wOldValue,HWORD wBitValue,int nMSB,int nLSB);
HWORD SysGetMCUdGetValue(HWORD wValue,int nMSB,int nLSB);
//=============================================================================
HWORD SysGetSDRDataWidthByDevice(int nDeviceType);
HWORD SysGetCasLatencyByDevice(int nDeviceType);
HWORD SysGetReadLatencyByDevice(int nDeviceType);
HWORD SysGetRefreshPeriodByDevice(int nDeviceType);
//=============================================================================

#ifdef  __cplusplus
}
#endif

#endif

