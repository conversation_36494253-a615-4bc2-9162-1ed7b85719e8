/*...........................................................................*/
/*.                  File Name : MyriadPro10bGrk.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY MyriadPro10bRus_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro10bGrk_offset_table[129] = {
0x0000,0x0003,0x0006,0x0009,0x000c,0x000f,0x0012,0x0015,0x0018,0x001b,0x001e,0x0022,0x0025,0x0028,0x002b,0x002e,
0x0031,0x0035,0x003b,0x0044,0x0048,0x0050,0x005a,0x005f,0x0062,0x006c,0x006f,0x0079,0x0084,0x008a,0x0093,0x009b,
0x00a1,0x00a9,0x00b0,0x00b8,0x00c1,0x00ca,0x00ce,0x00d6,0x00de,0x00e9,0x00f2,0x00f9,0x0102,0x010b,0x0113,0x0116,
0x011e,0x0125,0x012d,0x0137,0x013f,0x0149,0x0153,0x0157,0x015f,0x0167,0x016d,0x0175,0x0179,0x0180,0x0188,0x0190,
0x0197,0x019e,0x01a4,0x01aa,0x01b2,0x01b9,0x01bd,0x01c4,0x01cb,0x01d3,0x01da,0x01e0,0x01e8,0x01f0,0x01f8,0x01fe,
0x0206,0x020c,0x0213,0x021c,0x0223,0x022c,0x0236,0x023b,0x0242,0x024a,0x0251,0x025b,0x025e,0x0261,0x0264,0x0267,
0x026a,0x026d,0x0270,0x0273,0x0276,0x0279,0x027c,0x027f,0x0282,0x0285,0x0288,0x028b,0x028e,0x0291,0x0294,0x0297,
0x029a,0x029d,0x02a0,0x02a3,0x02a6,0x02a9,0x02ac,0x02af,0x02b2,0x02b5,0x02b8,0x02bb,0x02be,0x02c1,0x02c4,0x02c7,
0x02ca};



ROMDATA PEGUBYTE MyriadPro10bGrk_data_table[1440] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x35, 0xb3, 0x00, 0xbe, 0xb0, 0xec, 0x27, 0xc1, 0xa1, 0xa7, 
0x89, 0x46, 0x0f, 0x8f, 0x8c, 0x3e, 0xfe, 0x63, 0x1f, 0x19, 0x9c, 0x61, 0xc6, 0x39, 0x9f, 0x8f, 
0x1f, 0xcf, 0x83, 0xf9, 0xfe, 0x19, 0xf9, 0xcf, 0x9b, 0x0f, 0x0d, 0x82, 0x38, 0x60, 0xe3, 0xae, 
0x00, 0x3c, 0x00, 0x38, 0x07, 0x80, 0x0e, 0x00, 0x06, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x6d, 0x86, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x17, 0x80, 0xb0, 0xb0, 0xec, 0x0c, 0x61, 0x33, 0x8c, 
0xc3, 0x0f, 0x0c, 0xcc, 0x1e, 0x30, 0x0c, 0x63, 0x31, 0x99, 0x98, 0xf1, 0xce, 0x39, 0x80, 0x19, 
0x98, 0xcc, 0xc1, 0x80, 0x67, 0x3b, 0xfc, 0xcd, 0x9b, 0x19, 0x8d, 0xce, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x66, 0x00, 0x60, 0x03, 0x00, 0x1b, 0x00, 0x03, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x80, 0x30, 0x30, 0xcc, 0x18, 0x30, 0x1a, 0x18, 
0x60, 0x0f, 0x0c, 0xcc, 0x1a, 0x30, 0x1c, 0x63, 0x71, 0xd9, 0xb0, 0xb1, 0x4a, 0x39, 0x80, 0x30, 
0xd8, 0xcc, 0xc1, 0xc0, 0x61, 0xb6, 0x66, 0x79, 0x9b, 0x30, 0xcc, 0x6c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x66, 0x00, 0x30, 0x06, 0x00, 0x31, 0x80, 0x03, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x05, 0x86, 0x30, 0x30, 0xcc, 0x18, 0x30, 0x1e, 0x18, 
0x63, 0x0d, 0x0c, 0xcc, 0x12, 0x30, 0x18, 0x63, 0x60, 0xd9, 0xe0, 0xb3, 0x4b, 0x3d, 0x80, 0x30, 
0xd8, 0xcc, 0xc0, 0xc0, 0x61, 0xe6, 0x66, 0x79, 0x9b, 0x30, 0xcc, 0x78, 0xfc, 0x7b, 0xe3, 0x33, 
0x7e, 0x64, 0xcc, 0x78, 0xf6, 0x1f, 0x31, 0xb3, 0x73, 0x8c, 0xd9, 0x98, 0x7c, 0xff, 0x3c, 0x38, 
0xff, 0xf6, 0x64, 0xce, 0x6d, 0xb6, 0x30, 0xcc, 0xdf, 0x19, 0xb1, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x0c, 0xc6, 0x3e, 0x3f, 0xcc, 0x18, 0x30, 0x0c, 0x18, 
0x63, 0x19, 0x8f, 0x8c, 0x33, 0x3e, 0x38, 0x7f, 0x6e, 0xd9, 0xe1, 0x9b, 0x6b, 0x35, 0x8f, 0x30, 
0xd8, 0xcc, 0xc0, 0x60, 0x60, 0xe6, 0x66, 0x31, 0xdb, 0x30, 0xcc, 0x39, 0x8c, 0xc3, 0x33, 0x33, 
0xc6, 0x6c, 0xec, 0xdd, 0x8c, 0x19, 0xbf, 0xb3, 0x63, 0x8c, 0xd9, 0x8e, 0xc6, 0x66, 0x66, 0x43, 
0x10, 0xc6, 0x79, 0xb6, 0xcd, 0xbc, 0x18, 0xcc, 0xf1, 0x99, 0xe0, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x0c, 0xc6, 0x30, 0x30, 0xcc, 0x18, 0x30, 0x0c, 0x18, 
0x63, 0x19, 0x8c, 0xcc, 0x33, 0x30, 0x30, 0x63, 0x60, 0xd9, 0xb1, 0x9b, 0x73, 0x35, 0x80, 0x30, 
0xd8, 0xcf, 0x80, 0xc0, 0x60, 0xc6, 0x66, 0x78, 0xfe, 0x30, 0xcc, 0x31, 0x8c, 0x63, 0x33, 0x33, 
0xc6, 0x64, 0x6d, 0x8c, 0xcc, 0x19, 0xb1, 0xb3, 0xc6, 0xcc, 0xd9, 0x30, 0xc6, 0x66, 0x66, 0xc3, 
0x18, 0xc6, 0x79, 0xb6, 0xcd, 0xbc, 0xd8, 0xcc, 0xf1, 0x99, 0xe6, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xc0, 0x30, 0x30, 0xcc, 0x18, 0x30, 0x0c, 0x08, 
0x43, 0x1f, 0x8c, 0xcc, 0x33, 0x30, 0x70, 0x63, 0x60, 0xd9, 0xb9, 0x9b, 0x33, 0x33, 0x80, 0x30, 
0xd8, 0xcc, 0x01, 0xc0, 0x60, 0xc3, 0xfc, 0x78, 0x18, 0x10, 0x8c, 0x31, 0x8d, 0x83, 0x33, 0x33, 
0xc6, 0x66, 0x69, 0x8f, 0x0c, 0x19, 0xb1, 0xb3, 0x66, 0xcc, 0xcd, 0x30, 0xc6, 0x66, 0x66, 0xc3, 
0x18, 0xc6, 0x79, 0xb3, 0x8d, 0xbc, 0xd8, 0xcc, 0xf1, 0x99, 0xe6, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x0c, 0xe0, 0x30, 0x30, 0xcc, 0x0c, 0x60, 0x0c, 0x0c, 
0xc3, 0xb9, 0xcc, 0xcc, 0x61, 0xb0, 0x60, 0x63, 0x31, 0x99, 0x9b, 0x1b, 0x33, 0x33, 0x80, 0x19, 
0x98, 0xcc, 0x01, 0x80, 0x60, 0xc1, 0xf8, 0xcc, 0x18, 0x19, 0x8c, 0x31, 0x8d, 0x93, 0x33, 0xb2, 
0xc6, 0x66, 0x39, 0x8f, 0x2e, 0x19, 0x9b, 0x3b, 0x66, 0xcc, 0xce, 0x38, 0xc6, 0x66, 0x66, 0xe3, 
0x18, 0xc6, 0x4f, 0xe3, 0x8f, 0xec, 0xd8, 0xec, 0xb1, 0x99, 0x66, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x18, 0x60, 0x3f, 0x30, 0xcc, 0x07, 0xc0, 0x0c, 0x1c, 
0xe3, 0x30, 0xcf, 0x8c, 0x7f, 0xbf, 0xfe, 0x63, 0x1f, 0x19, 0x8f, 0x0f, 0x33, 0x31, 0x9f, 0x8f, 
0x18, 0xcc, 0x03, 0xf8, 0x60, 0xc0, 0x61, 0xce, 0x18, 0x3d, 0xcc, 0x30, 0xf6, 0xf3, 0x33, 0x1e, 
0x7b, 0x7c, 0x30, 0xf1, 0xe7, 0x99, 0x8e, 0x33, 0x3c, 0x6f, 0x46, 0x1e, 0x7c, 0xc6, 0x7c, 0x79, 
0xf0, 0xe3, 0xc7, 0xc3, 0x83, 0xc7, 0xf0, 0xc7, 0x9f, 0x0f, 0x3f, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x60, 0x30, 0x00, 0x01, 0x81, 0x80, 0x00, 0x00, 0x0c, 0x00, 0x06, 0x00, 0x00, 0x60, 0x18, 
0x00, 0x00, 0x01, 0x86, 0xc1, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x60, 0x30, 0x00, 0x01, 0x81, 0x80, 0x00, 0x00, 0x0c, 0x00, 0x06, 0x00, 0x00, 0x60, 0x18, 
0x00, 0x00, 0x01, 0x86, 0xc1, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x60, 0x30, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 
0x00, 0x00, 0x01, 0x8c, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY MyriadPro10bGrk_Font = {0x01, 16, 0, 16, 0, 0, 16, 90, 0x0374, 0x03f3,
(PEGUSHORT *) MyriadPro10bGrk_offset_table, &MyriadPro10bRus_Font,
(PEGUBYTE *) MyriadPro10bGrk_data_table};


