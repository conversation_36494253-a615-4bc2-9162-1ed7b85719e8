#include "Sentence.hpp"

#ifndef __SPW_HPP__
#define __SPW_HPP__

/******************************************************************************
 * 
 * SPW - 
 *
 * $--SPW,A,xx,a,b,c,d,*hh<CR><LF>
 *        |  | | | | |
 *        1  2 3 4 5 6
 *
 * 1. 'A'
 * 2. Code
 * 3. CS-a
 * 4. CS-b
 * 5. CS-c
 * 6. CS-d
 *
 ******************************************************************************/
class CSpw : public CSentence {
	protected:
		BYTE m_szCodeCCC[32];

    public:
        CSpw();
        CSpw(char *pszSentence);

		void Parse();
		void SetSentence(char *pszSentence);
		int  GetFormat() { return m_nFormat; }
		void GetPlainText(char *pszPlainText);

		void GetCodeCCC(BYTE *pszCodeCCC) { strcpy((char *)pszCodeCCC, (char *)m_szCodeCCC); }
};

#endif

