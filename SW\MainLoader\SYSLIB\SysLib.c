/*...........................................................................*/
/*.                  File Name : SYSLIB.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysTimer.h"
#include "SysMLC.h"
#include "SysLCD.h"
#include "SysGPIO.h"
#include "SysAlive.h"
#include "SysLib.h"

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

//========================================================================
static MEMBLOCK G_AllMemBlock;
//========================================================================
static int      G_nCpuType    = CPU_TYPE_POLLUX;
static int      G_nDeviceType = DEVICE_TYPE_04_3;
static int      G_nNavisModelType = NAVIS_TYPE_800;
#ifdef  __SPICA__
static int      G_nSGP330ScrnResMode = SGP330_SCRN_MODE_640x480;
#endif
//========================================================================
volatile DWORD  G_dSystemTickCounter = 0;
//========================================================================
static xSYSDATE G_xUTCDate = {2008, 1, 1};
static xSYSTIME G_xUTCTime = { 0, 0, 0};
static xSYSDATE G_xLOCDate = {2008, 1, 1};
static xSYSTIME G_xLOCTime = { 0, 0, 0};
//========================================================================
UCHAR  G_cSysAlphaBlendValue= 0xff;
DWORD  G_dSysAlphaBlendValue= 0xff000000;
int    G_nSysBrightPercntVal= 100;        //  10, 20, 30, 40, 50, 60, 70, 80, 90,100
int    G_nSysBrightFactorVal= 256;        //  51, 77, 90,102,115,128,154,179,205,256
//========================================================================
UCHAR *G_pSysPaletteDataUCHAR = NULL;
HWORD *G_pSysPaletteDataHWORD = NULL;
int    G_nSysPaletteDataSize  = 0;
UCHAR  G_vMapPaletteDataUCHAR[256 * 3] = {0x00,};
HWORD  G_vMapPaletteDataHWORD[256 * 1] = {0x00,};
int    G_nNavis5100AModelMode = 0;
//========================================================================

//========================================================================
void  SysInitFreeMemory(void)
{
      DWORD dBuffer;
      DWORD dSize;
      MEMLINK *pBuffer;

      dBuffer = SysGetStartOfFreeMemory();
      dSize = RAM_TOTAL_SIZE - 4 * 1024;
      dSize = dSize - (dBuffer - RAM_START_ADDRESS);
      G_AllMemBlock.pStart = (void *)dBuffer;
      G_AllMemBlock.dSize  = dSize;
      if ((dBuffer % MEMORY_ALIGN) != 0)
         {
          dSize -= (MEMORY_ALIGN - dBuffer % MEMORY_ALIGN);
          dBuffer = dBuffer + (MEMORY_ALIGN - dBuffer % MEMORY_ALIGN);
         }
      if ((dSize % MEMORY_ALIGN) != 0)
          dSize -= (dSize % MEMORY_ALIGN);
      pBuffer = (MEMLINK *)dBuffer;
      pBuffer->pPrev = NULL;
      pBuffer->pNext = NULL;
      pBuffer->dSize = dSize - MEMLINK_SIZE;
      pBuffer->wFixed= 1;
      pBuffer->wUsed = 0;
      G_AllMemBlock.pMemLink = pBuffer;
}
DWORD SysGetAllFreeMemorySize(void)
{
      MEMLINK *pLink;
      DWORD  dTotalMem;

      dTotalMem = 0;
      pLink = G_AllMemBlock.pMemLink;
      while (pLink != NULL)
            {
             if (!pLink->wUsed)
                 dTotalMem += pLink->dSize;
             pLink = pLink->pNext;
            }
      return(dTotalMem);
}
DWORD SysGetMaxFreeMemorySize(void)
{
      MEMLINK *pLink;
      DWORD  dMaxMem;

      dMaxMem = 0;
      pLink = G_AllMemBlock.pMemLink;
      while (pLink != NULL)
            {
             if (!pLink->wUsed)
                 if (dMaxMem < pLink->dSize)
                     dMaxMem = pLink->dSize;
             pLink = pLink->pNext;
            }
      return(dMaxMem);
}
int   SysMakeMemoryTree(DWORD *pMemTree)
{
      MEMLINK *pLink;
      int   nNodeSize;

      nNodeSize = 0;
      pLink = G_AllMemBlock.pMemLink;
      while (pLink != NULL)
            {
             *pMemTree++ = (DWORD)pLink->pNext;
             *pMemTree++ = (DWORD)pLink->pPrev;
             *pMemTree++ = (DWORD)pLink->dSize;
             *pMemTree++ = (DWORD)pLink->wUsed;
             pLink = pLink->pNext;
             ++nNodeSize;
            }
      return(nNodeSize);
}
int   SysCheckMemoryTree(DWORD *pErrorMem,MEMLINK *pMemLink)
{
      MEMLINK *pLink;
      DWORD dStartMem;
      DWORD dLastMem;
      DWORD dPrev,dNext,dLink;
      int   nError;

      dStartMem = (DWORD)G_AllMemBlock.pStart;
      dLastMem  = (DWORD)G_AllMemBlock.pStart + G_AllMemBlock.dSize;
      nError = 0;
      pLink = G_AllMemBlock.pMemLink;
      while (pLink != NULL)
            {
             dPrev = (DWORD)(pLink->pPrev);
             dNext = (DWORD)(pLink->pNext);
             dLink = (DWORD)(pLink);
             if ((dNext != 0 && (dNext < dStartMem || dNext >= dLastMem)) ||
                 (dPrev != 0 && (dPrev < dStartMem || dPrev >= dLastMem)) ||
                 (dLink != 0 && (dLink < dStartMem || dLink >= dLastMem)))
                {
                 *pErrorMem = (DWORD)pLink;
                 *pMemLink  = *pLink;
                 nError = 1;
                 break;
                }
             pLink = pLink->pNext;
            }
      return(nError);
}
int   SysGetNumberOfFragment(void)
{
      MEMLINK *pLink;
      int   nFragCnt;

      nFragCnt = 0;
      pLink = G_AllMemBlock.pMemLink;
      while (pLink != NULL)
            {
             if (pLink->wUsed == 0)
                 ++nFragCnt;
             pLink = pLink->pNext;
            }
      return(nFragCnt);
}
DWORD SysGetLastFreeAddress(DWORD *pBlockSize)
{
      MEMLINK *pLink;
      DWORD  dLastAddr = 0;

      pLink = G_AllMemBlock.pMemLink;
      while (pLink != NULL)
            {
             dLastAddr   = (DWORD)pLink;
             *pBlockSize = pLink->dSize;
             pLink = pLink->pNext;
            }
      return(dLastAddr);
}
int   SysRemoveMemFragment(void)
{
      return(0);
}
//========================================================================
void  *__wrap_malloc(DWORD dSize)
{
      MEMLINK *pLink;
      MEMLINK *pTemp;
#if  __USE_RTOS_MODE__
      DWORD dStatusReg;
#endif

#if  __USE_RTOS_MODE__
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif
      dSize = (dSize + MEMORY_ALIGN - 1) / MEMORY_ALIGN * MEMORY_ALIGN;
      if (dSize < MEMORY_ALIGN)
          dSize = MEMORY_ALIGN;
      pLink = G_AllMemBlock.pMemLink;
      while (pLink != NULL)
            {
             if ((pLink->dSize >= dSize) && !pLink->wUsed)
                {
                 if ((pLink->dSize - dSize) > MEMLINK_SIZE)
                    {
                     pTemp = (MEMLINK *)((char *)pLink + MEMLINK_SIZE + dSize);
                     pTemp->pNext = pLink->pNext;
                     pTemp->pPrev = pLink;
                     pLink->pNext = pTemp;
                     pTemp->wFixed= 0;
                     pTemp->dSize = pLink->dSize - dSize - MEMLINK_SIZE;
                     pTemp->wUsed = 0;
                     pLink->dSize = dSize;
                    }
                 pLink->wUsed = 1;
                 break;
                }
             pLink = pLink->pNext;
            }
#if  __USE_RTOS_MODE__
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif
      if (pLink == NULL)
          return(NULL);
      return((char *)pLink + MEMLINK_SIZE);
}
void  __wrap_free(void *pMemAddr)
{
      MEMLINK *pLink;
      MEMLINK *pTemp;
      MEMLINK *pPrev;
#if  __USE_RTOS_MODE__
      DWORD dStatusReg;
#endif

#if  __USE_RTOS_MODE__
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif
      pMemAddr = (char *)pMemAddr - MEMLINK_SIZE;
      pLink = G_AllMemBlock.pMemLink;
      pPrev = NULL;
      while (pLink != NULL)
            {
             if (pLink == pMemAddr)
                {
                 pLink->wUsed = 0;
                 pTemp = pLink->pNext;
                 if ((pTemp != NULL) && !pTemp->wUsed && !pTemp->wFixed)
                    {
                     pLink->dSize += pTemp->dSize + MEMLINK_SIZE;
                     pLink->pNext = pTemp->pNext;
                    }
                 if (!pLink->wFixed && (pPrev != NULL) && !pPrev->wUsed)
                    {
                     pPrev->dSize += pLink->dSize + MEMLINK_SIZE;
                     pPrev->pNext = pLink->pNext;
                    }
                 break;
                }
             pPrev = pLink;
             pLink = pLink->pNext;
            }
#if  __USE_RTOS_MODE__
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif
}
void  *__wrap_calloc(DWORD nItems,DWORD nSize)
{
	    void *pMem;

	    pMem = malloc(nItems * nSize);
	    memset(pMem,0x00,nItems * nSize);
	    return(pMem);
}
void  __wrap_abort(void)
{
      while (1);
}
int   __wrap_open(char *Path,int Flags)
{
      return(-1);
}
int   __wrap_close(int file)
{
      return(-1);
}
int   __wrap_fstat(int file,struct stat *st)
{
      st->st_mode = S_IFCHR;
      return(0);
}
int   __wrap_isatty(int file)
{
      return(1);
}
int   __wrap_lseek(int file,int ptr,int dir)
{
      return(0);
}
int   __wrap_read(int file,char *ptr,int len)
{
      return(0);
}
caddr_t __wrap_sbrk(int incr)
{
      static char *heap_end = NULL;
      char *prev_heap_end;
#if  __USE_RTOS_MODE__
      DWORD dStatusReg;
#endif

#if  __USE_RTOS_MODE__
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif
      incr = incr + 15;
      incr = incr & 0xfffffff0;
      if (heap_end == NULL)
          heap_end = (char *)SysGetStartOfHeapMemory();
      prev_heap_end = heap_end;
      if ((heap_end + incr) >= (char *)SysGetLastOfHeapMemory())
          abort();
      heap_end += incr;
#if  __USE_RTOS_MODE__
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif
      return((caddr_t)prev_heap_end);
}
int   __wrap_write(int file,char *ptr,int len)
{
      return(len);
}
int   __wrap_fputc(int character,void *stream)
{
      return(0);
}
int   __wrap_fputs(const char *str,void *stream)
{
      return(0);
}
int   __wrap_puts(const char *str)
{
      __wrap_printf(str);
      return(0);
}
int   __wrap_printf(const char *format,...)
{
/*
   static BYTE *pMem = (BYTE *)0x83000004;
   BYTE Temp[512];
   va_list ArgPtr;
   int ret,Len;

   va_start(ArgPtr,format);
   ret = vsprintf(Temp,format,ArgPtr);
   va_end(ArgPtr);
   Len = strlen(Temp);
   memmove(pMem,Temp,Len);pMem += Len;
   *(DWORD *)0x83000000 = (DWORD)pMem;
   return(ret);
//*/
   return(0);
}
//========================================================================
int   __wrap__open_r(void *REENT,const char *file,int flags,int mode)
{
      return(0);
}
int   __wrap__close_r(void *REENT,int FD)
{
      return(0);
}
int   __wrap__fstat_r(void *REENT,int FD,struct stat *PSTAT)
{
      PSTAT->st_mode = S_IFCHR;
      return(0);
}
off_t __wrap__lseek_r(void *REENT,int FD,off_t POS,int WHENCE)
{
      return(0);
}
long  __wrap__read_r(void *REENT,int FD,void *BUF,size_t CNT)
{
      return(0);
}
char *__wrap__sbrk_r(void *REENT, size_t INCR)
{
      return(__wrap_sbrk(INCR));
}
long  __wrap__write_r(void *REENT,int FD, const void *BUF, size_t CNT)
{
      return(CNT);
}
//========================================================================
void  SysCheckCpuType(void)
{
#ifdef  __POLLUX__
//    volatile UCHAR *pID = (volatile UCHAR *)0xc001f800;
//
//    SysSetCpuType(CPU_TYPE_SPICA);
//    if (pID[16] == 'M' &&
//        pID[17] == 'A' &&
//        pID[18] == 'G' &&
//        pID[19] == 'I' &&
//        pID[20] == 'C' &&
//        pID[21] == 'E' &&
//        pID[22] == 'Y' &&
//        pID[23] == 'E' &&
//        pID[24] == 'S')
//        SysSetCpuType(CPU_TYPE_POLLUX);

      SysSetCpuType(CPU_TYPE_POLLUX);
#else                           // SPICA
      SysSetCpuType(CPU_TYPE_SPICA);
#endif
}
void  SysSetCpuType(int nCpuType)
{
      G_nCpuType = nCpuType;
}
int   SysGetCpuType(void)
{
      return(G_nCpuType);
}
void  SysCheckDeviceType(void)
{
#if defined(__POLLUX__)

  #if defined(__N430_MODEL__)
      SysSetDeviceType(DEVICE_TYPE_04_3);

      return;
  #endif

  #if defined(__SCR_800x600__) || defined(__SCR_640x480__) || defined(__SCR_1024x768__)
    #if defined(__SCR_800x600__)
        SysSetDeviceType(DEVICE_TYPE_08_4);
    #endif
    #if defined(__SCR_640x480__)
        SysSetDeviceType(DEVICE_TYPE_06_5);
    #endif
    #if defined(__SCR_1024x768__)
        SysSetDeviceType(DEVICE_TYPE_15_1);
    #endif
  #else
      if (G_nCpuType == CPU_TYPE_POLLUX)
         {
          SysSetDeviceType(DEVICE_TYPE_05_0);
    #ifdef  __POLLUX__
          if (SysGetGPIOC() & (1 << LCD_TYPE_BIT))
              SysSetDeviceType(DEVICE_TYPE_05_6);
    #endif
         }
      else
         {
          SysSetDeviceType(DEVICE_TYPE_08_4);
         }
  #endif
#else                           // SPICA
//    SysSetDeviceType(DEVICE_TYPE_NAVIS);
      SysSetDeviceType(DEVICE_TYPE_06_5);
#endif
}
void  SysSetDeviceType(int nDeviceType)
{
      G_nDeviceType = nDeviceType;
}
int   SysGetDeviceType(void)
{
      return(G_nDeviceType);
}
void  SysCheckNavisModelType(void)
{
#if defined(__POLLUX__)
#else                           // SPICA
      DWORD dMaskX;
      DWORD dTempC;
      DWORD dTempX;
      DWORD dTempY;
      DWORD dTempZ;
      int   nTypeX;
      xSYS_GPIO *pSysGPIO = (xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR;
      xSYS_GPIO *pSysCNFG = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

  #if defined(__N500_MODEL__)
      SysSetNavisModelType(NAVIS_TYPE_700);
      return;
  #endif

      if (pSysCNFG->dPAD & (1 << 17))
         {
          SysSetNavisModelType(NAVIS_TYPE_330);
          return;
         }

      dMaskX = (1 << 10) | (1 <<  9) | (1 <<  8);

      pSysGPIO->dOUTENB &= (~dMaskX);     // input

      SysDelayMiliSec(5);

      dTempC = 0;
      dTempZ = 0;
      dTempY = 0;
      while (dTempC < 10000)
            {
             dTempX = (pSysGPIO->dPAD >> 8) & 0x00000007;

             if (dTempX == dTempY)
                 ++dTempZ;
             else
                {
                 dTempZ = 0;
                 dTempY = dTempX;
                }

             if (dTempZ >= 500)
                 break;

             ++dTempC;
            }

      if (dTempZ < 500)
          dTempX = 6;

      nTypeX = NAVIS_TYPE_700;

      if (dTempX == 1)  nTypeX = NAVIS_TYPE_700;
      if (dTempX == 2)  nTypeX = NAVIS_TYPE_800;
      if (dTempX == 3)
         {
          if (pSysCNFG->dPAD & (1 << 19))
              SysSetNavis5100AModelMode(1);
          nTypeX = NAVIS_TYPE_5100;
         }
      if (dTempX == 4)  nTypeX = NAVIS_TYPE_1200;
      if (dTempX == 5)  nTypeX = NAVIS_TYPE_3800;
//    if (dTempX == 6)  nTypeX = NAVIS_TYPE_330;

      SysSetNavisModelType(nTypeX);

      pSysGPIO->dOUTENB |= dMaskX;     // output
#endif
}
void  SysSetNavisModelType(int nNavisType)
{
      G_nNavisModelType = nNavisType;
}
int   SysGetNavisModelType(void)
{
      return(G_nNavisModelType);
}
#ifdef  __SPICA__
void  SysSetSGP330ScrnMode(int nScrnMode)
{
      G_nSGP330ScrnResMode = nScrnMode;
}
int   SysGetSGP330ScrnMode(void)
{
      return(G_nSGP330ScrnResMode);
}
#endif
DWORD SysGetRadarScreenAddress(void)
{
#if defined(__POLLUX__)
      return(CHART_LAYER_BASE_ADDR);
#else                           // SPICA
      return(RADAR_LAYER_BASE_ADDR);
#endif
}
DWORD SysGetChartScreenAddress(void)
{
      return(CHART_LAYER_BASE_ADDR);
}
DWORD SysGetMenuScreenAddress(void)
{
      return(MENU_LAYER_BASE_ADDR);
}
DWORD SysGetScreenAddress(void)
{
      return(MENU_LAYER_BASE_ADDR);
}
DWORD SysGetScreenWidth(void)
{
      return(SysGetScreenWidthByDevice(G_nDeviceType));
}
DWORD SysGetScreenHeight(void)
{
      return(SysGetScreenHeightByDevice(G_nDeviceType));
}
DWORD SysGetScreenWidthByDevice(int nDeviceType)
{
#if defined(__POLLUX__)
      DWORD dTempX = DVC_05_6_SCRN_WIDTH;

      if (nDeviceType == DEVICE_TYPE_03_5) dTempX = DVC_03_5_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_04_3) dTempX = DVC_04_3_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_05_0) dTempX = DVC_05_0_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_05_6) dTempX = DVC_05_6_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_06_5) dTempX = DVC_06_5_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_07_0) dTempX = DVC_07_0_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_08_4) dTempX = DVC_08_4_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_10_4) dTempX = DVC_10_4_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_12_1) dTempX = DVC_12_1_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_15_1) dTempX = DVC_15_1_SCRN_WIDTH;
      return(dTempX);
#else                           // SPICA
      DWORD dTempX = NVS_800_SCRN_WIDTH;

      if (G_nNavisModelType == NAVIS_TYPE_330 )
         {
          dTempX = NVS_3800_SCRN_WIDTH;

          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   dTempX = NVS_5100_SCRN_WIDTH;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   dTempX = NVS_1200_SCRN_WIDTH;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  dTempX = NVS_3800_SCRN_WIDTH;

          return(dTempX);
         }

      if (G_nNavisModelType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
          return(NVS_1200_SCRN_WIDTH);

      if (G_nNavisModelType == NAVIS_TYPE_800 )  dTempX = NVS_800_SCRN_WIDTH ;
      if (G_nNavisModelType == NAVIS_TYPE_5100)  dTempX = NVS_5100_SCRN_WIDTH;
      if (G_nNavisModelType == NAVIS_TYPE_1200)  dTempX = NVS_1200_SCRN_WIDTH;
      if (G_nNavisModelType == NAVIS_TYPE_3800)  dTempX = NVS_3800_SCRN_WIDTH;
      if (G_nNavisModelType == NAVIS_TYPE_330 )  dTempX = NVS_330_SCRN_WIDTH ;
      if (G_nNavisModelType == NAVIS_TYPE_700 )  dTempX = NVS_700_SCRN_WIDTH ;
      if (G_nNavisModelType == NAVIS_TYPE_650 )  dTempX = NVS_650_SCRN_WIDTH ;

      return(dTempX);
#endif
}
DWORD SysGetScreenHeightByDevice(int nDeviceType)
{
#if defined(__POLLUX__)
      DWORD dTempX = DVC_05_6_SCRN_HEIGHT;

      if (nDeviceType == DEVICE_TYPE_03_5) dTempX = DVC_03_5_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_04_3) dTempX = DVC_04_3_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_05_0) dTempX = DVC_05_0_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_05_6) dTempX = DVC_05_6_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_06_5) dTempX = DVC_06_5_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_07_0) dTempX = DVC_07_0_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_08_4) dTempX = DVC_08_4_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_10_4) dTempX = DVC_10_4_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_12_1) dTempX = DVC_12_1_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_15_1) dTempX = DVC_15_1_SCRN_HEIGHT;
      return(dTempX);
#else                           // SPICA
      DWORD dTempX = NVS_800_SCRN_HEIGHT;

      if (G_nNavisModelType == NAVIS_TYPE_330 )
         {
          dTempX = NVS_3800_SCRN_HEIGHT;

          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   dTempX = NVS_5100_SCRN_HEIGHT;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   dTempX = NVS_1200_SCRN_HEIGHT;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  dTempX = NVS_3800_SCRN_HEIGHT;

          return(dTempX);
         }

      if (G_nNavisModelType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
          return(NVS_1200_SCRN_HEIGHT);

      if (G_nNavisModelType == NAVIS_TYPE_800 )  dTempX = NVS_800_SCRN_HEIGHT ;
      if (G_nNavisModelType == NAVIS_TYPE_5100)  dTempX = NVS_5100_SCRN_HEIGHT;
      if (G_nNavisModelType == NAVIS_TYPE_1200)  dTempX = NVS_1200_SCRN_HEIGHT;
      if (G_nNavisModelType == NAVIS_TYPE_3800)  dTempX = NVS_3800_SCRN_HEIGHT;
      if (G_nNavisModelType == NAVIS_TYPE_330 )  dTempX = NVS_330_SCRN_HEIGHT ;
      if (G_nNavisModelType == NAVIS_TYPE_700 )  dTempX = NVS_700_SCRN_HEIGHT ;
      if (G_nNavisModelType == NAVIS_TYPE_650 )  dTempX = NVS_650_SCRN_HEIGHT ;

      return(dTempX);
#endif
}
//========================================================================
void  SysDelayLoop(volatile DWORD dDelayCnt)
{
      while (dDelayCnt) dDelayCnt--;
}
void  SysDelayMicroSec(DWORD dDelayMicro)
{
      SysTimerDelayMicroSec(dDelayMicro);
}
void  SysDelayMiliSec(DWORD dDelayMili)
{
      SysTimerDelayMiliSec(dDelayMili);
}
DWORD SysGetSystemTimer(void)
{
      return(G_dSystemTickCounter);
}
DWORD SysIncSystemTimer(void)
{
      ++G_dSystemTickCounter;
      return(G_dSystemTickCounter);
}
DWORD SysCalcTickToMili(DWORD dTick)
{
      return(CALC_TICK_TO_MILI(dTick));
}
DWORD SysCalcMiliToTick(DWORD dMili)
{
      return(CALC_MILI_TO_TICK(dMili));
}
DWORD SysGetDiffTimeTick(DWORD dTime)
{
      DWORD dTemp;

      dTemp = SysGetSystemTimer();
      if (dTemp >= dTime)
          dTemp -= dTime;
      else
          dTemp += (0xffffffff - dTime);
      return(dTemp);
}
DWORD SysGetDiffTimeMili(DWORD dTime)
{
      return(CALC_TICK_TO_MILI(SysGetDiffTimeTick(dTime)));
}
//========================================================================
void  SysSetUTCDate(int nYear,int nMonth,int nDay)
{
      G_xUTCDate.nYear  = nYear;
      G_xUTCDate.nMonth = nMonth;
      G_xUTCDate.nDay   = nDay;
}
void  SysGetUTCDate(int *pYear,int *pMonth,int *pDay)
{
      *pYear  = G_xUTCDate.nYear;
      *pMonth = G_xUTCDate.nMonth;
      *pDay   = G_xUTCDate.nDay;
}
void  SysSetUTCTime(int nHour,int nMinute,int nSecond)
{
      G_xUTCTime.nHour  = nHour;
      G_xUTCTime.nMinute= nMinute;
      G_xUTCTime.nSecond= nSecond;
}
void  SysGetUTCTime(int *pHour,int *pMinute,int *pSecond)
{
      *pHour  = G_xUTCTime.nHour;
      *pMinute= G_xUTCTime.nMinute;
      *pSecond= G_xUTCTime.nSecond;
}
void  SysSetLOCDate(int nYear,int nMonth,int nDay)
{
      G_xLOCDate.nYear  = nYear;
      G_xLOCDate.nMonth = nMonth;
      G_xLOCDate.nDay   = nDay;
}
void  SysGetLOCDate(int *pYear,int *pMonth,int *pDay)
{
      *pYear  = G_xLOCDate.nYear;
      *pMonth = G_xLOCDate.nMonth;
      *pDay   = G_xLOCDate.nDay;
}
void  SysSetLOCTime(int nHour,int nMinute,int nSecond)
{
      G_xLOCTime.nHour  = nHour;
      G_xLOCTime.nMinute= nMinute;
      G_xLOCTime.nSecond= nSecond;
}
void  SysGetLOCTime(int *pHour,int *pMinute,int *pSecond)
{
      *pHour  = G_xLOCTime.nHour;
      *pMinute= G_xLOCTime.nMinute;
      *pSecond= G_xLOCTime.nSecond;
}
//========================================================================
void  SysSetAlphaBlendValue(UCHAR bAlpha)
{
      G_cSysAlphaBlendValue = bAlpha;
      G_dSysAlphaBlendValue = ((DWORD)G_cSysAlphaBlendValue) << 24;
}
UCHAR SysGetAlphaBlendValue(void)
{
      return(G_cSysAlphaBlendValue);
}
void  SysSetOriginAlphaBlendValue(void)
{
      SysSetAlphaBlendValue(0xff);
}
void  SysSetBrightValue(int nPercent)
{
      static int vFactorTable[] = { 0, 51, 77, 90,102,115,128,154,179,205,256};

      nPercent = nPercent / 10 * 10;
      if (nPercent >= 10 && nPercent <= 100)
         {
          G_nSysBrightPercntVal = nPercent;
          G_nSysBrightFactorVal = vFactorTable[nPercent / 10];
         }
}
int   SysGetBrightValue(void)
{
      return(G_nSysBrightPercntVal);
}
void  SysSetPaletteData(int nSize,DWORD *pColorTable)
{
      int   i;

      G_nSysPaletteDataSize = nSize;
      if (G_pSysPaletteDataUCHAR != NULL) free(G_pSysPaletteDataUCHAR);
      if (G_pSysPaletteDataHWORD != NULL) free(G_pSysPaletteDataHWORD);
      G_pSysPaletteDataUCHAR = (UCHAR *)malloc(G_nSysPaletteDataSize * sizeof(UCHAR) * 3);
      G_pSysPaletteDataHWORD = (HWORD *)malloc(G_nSysPaletteDataSize * sizeof(HWORD) * 1);
      for (i = 0;i < nSize;i++)
          {
           G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 0] = (((pColorTable[i] & 0x00ff0000) >> 16) * G_nSysBrightFactorVal / 256) & 0x000000ff;
           G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 1] = (((pColorTable[i] & 0x0000ff00) >>  8) * G_nSysBrightFactorVal / 256) & 0x000000ff;
           G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 2] = (((pColorTable[i] & 0x000000ff) >>  0) * G_nSysBrightFactorVal / 256) & 0x000000ff;
           G_pSysPaletteDataHWORD[i] = ((G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 0] >> 3) << 11) |
                                       ((G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 1] >> 2) <<  5) |
                                       ((G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 2] >> 3) <<  0);
          }
}
//========================================================================
void  SysRunSystemPowerOff(void)
{
#if defined(__POLLUX__)
      SysSetALIVExBitData(1,0);
#else                           // SPICA
      SysSetALIVExBitData(1,0);
#endif
}
void  SysRunSystemPowerOn(void)
{
#if defined(__POLLUX__)
      SysSetALIVExBitData(1,1);
#else                           // SPICA
      SysSetALIVExBitData(1,1);
#endif
}
//========================================================================
void  SysSetNavis5100AModelMode(int nMode)
{
      G_nNavis5100AModelMode = nMode;
}
int   SysGetNavis5100AModelMode(void)
{
      return(G_nNavis5100AModelMode);
}
//========================================================================
int   SysCheckLCD3800Mitsubishi(void)
{
      xSYS_GPIO *pSysCNFG = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

      if (G_nNavisModelType == NAVIS_TYPE_3800 && pSysCNFG->dPAD & (1 << 20))
          return(1);

      return(0);
}
//========================================================================

