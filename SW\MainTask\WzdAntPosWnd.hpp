#ifndef __WIZARD_ANTENNA_POS_WND_HPP__
#define __WIZARD_ANTENNA_POS_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "ComboCtrl.hpp"
#include "CheckCtrl.hpp"

class CWzdAntPosWnd : public CWnd {

public:
	enum{
		IN_EPFS_TYPE_GPS = 0,
		IN_EPFS_TYPE_GLONASS,
		IN_EPFS_TYPE_BEIDOU,
		IN_EPFS_TYPE_GALILEO,
		IN_EPFS_TYPE_GPS_GLONASS,
		IN_EPFS_TYPE_GPS_BEIDOU,
		IN_EPFS_TYPE_GPS_GALILEO,
		MAX_IN_EPFS_TYPE
	};

	enum{
		EXT_EPFS_TYPE_UNDEF = 0,
		EXT_EPFS_TYPE_GPS,
		EXT_EPFS_TYPE_GLONASS,
		EXT_EPFS_TYPE_GPS_GLONASS,
		EXT_EPFS_TYPE_LORAN_C,
		EXT_EPFS_TYPE_CHAYKA,
		EXT_EPFS_TYPE_INT_NAV_SYS,
		EXT_EPFS_TYPE_SURVEYED,
		EXT_EPFS_TYPE_GALILEO,
		EXT_EPFS_TYPE_NOT_USED_09,
		EXT_EPFS_TYPE_NOT_USED_10,
		EXT_EPFS_TYPE_NOT_USED_11,
		EXT_EPFS_TYPE_NOT_USED_12,
		EXT_EPFS_TYPE_NOT_USED_13,
		EXT_EPFS_TYPE_NOT_USED_14,
		EXT_EPFS_TYPE_INTERNAL_GNSS,
		MAX_EXT_EPFS_TYPE
	};

protected:
	enum {
		FOCUS_IN_A = 0,
		FOCUS_IN_B,
		FOCUS_IN_C,
		FOCUS_IN_D,
		FOCUS_IN_EPFS,
		FOCUS_USE_SBAS,
		
		FOCUS_EX_A,
		FOCUS_EX_B,
		FOCUS_EX_C,
		FOCUS_EX_D,
		
#ifdef EN_61993_ED3
		FOCUS_LAST_CONTROL = FOCUS_EX_D,
#else	// Else of (EN_61993_ED3)
		FOCUS_EX_EPFS,
		FOCUS_LAST_CONTROL = FOCUS_EX_EPFS,
#endif	// End of (EN_61993_ED3)
		FOCUS_COUNT
	};


	CEditCtrl  *m_pINAEdit;
	CEditCtrl  *m_pINBEdit;
	CEditCtrl  *m_pINCEdit;
	CEditCtrl  *m_pINDEdit;
	CEditCtrl  *m_pEXAEdit;
	CEditCtrl  *m_pEXBEdit;
	CEditCtrl  *m_pEXCEdit;
	CEditCtrl  *m_pEXDEdit;

	CComboCtrl *m_pCboInEPFS;

#ifndef EN_61993_ED3
	CComboCtrl *m_pCboExEPFS;
#endif	// End of (EN_61993_ED3)
	CCheckCtrl *m_pChkEnSBAS;

	CWnd       *m_paControls[FOCUS_COUNT];
	BOOL        m_bEditMode;

public:
	CWzdAntPosWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	virtual void OnKeyEvent(int nKey, DWORD nFlags);
	virtual void OnCursorEvent(int nState);
	virtual void OnActivate();
	void DrawFuncBtn();
	virtual void DrawWnd(BOOL bRedraw = TRUE);

	void DrawControls();
	void SetFocus(int nFocus);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	void ComboCollapse();
	BOOL IsEditMode();
	void InitAntPos();
	void ChangeAntPos();

	void SendEPFSCommand();
	void SendSBASCommand(bool bEnable);
};

#endif	// End of __WIZARD_ANTENNA_POS_WND_HPP__


