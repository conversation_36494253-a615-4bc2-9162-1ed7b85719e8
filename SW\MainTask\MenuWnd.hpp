#include "Wnd.hpp"

#ifndef __MENU_WND_H__
#define __MENU_WND_H__

class CMenuWnd : public CWnd {
private:
	int m_nSelNum;

#ifdef _EN_POLICE_MODE_
	BOOL m_bPrevTransmitterStat;
#endif

	int m_nCurSpecialKeyCnt;
	int m_nSpecialKeyBuf[4];

public:
	CMenuWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	void InitSpecialKey();
	BOOL IsSpecialKey();
	void DrawWnd(BOOL bRedraw=1/*TRUE*/);
	void DrawSubMenu(int nSelNum);
	void OnKeyEvent(int nKey, DWORD nFlags=0);
	void OnActivate();
	void SetFocus(int nFocus)   { m_nFocus = nFocus; }
	void SetSelNum(int nSelNum) { m_nSelNum = nSelNum; }
	int  GetSelNum()            { return m_nSelNum;    }

#ifdef _EN_POLICE_MODE_
	BOOL GetPrevTransmitterStat() { return m_bPrevTransmitterStat; }
#endif // End of _EN_POLICE_MODE_
};

#endif
