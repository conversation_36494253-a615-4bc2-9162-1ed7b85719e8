#ifndef  __FACTORY_RESET__
#define  __FACTORY_RESET__

#include "type.hpp"
#include "sysconst.h"
#include "screen.hpp"
#include "keybd.hpp"
#include "uart.hpp"
#include "time.hpp"
#include "flash.hpp"
#include "DataBack.hpp"
#include "wnd.hpp"
#include "osfile.h"

class CFactoryReset
{
	private:
		cSCREEN *m_pScreen;
		cKEYBD  *m_pKeyBD;
		cFLASH  *m_pFlash;

		COLORT m_ForeClr;
		COLORT m_BackClr;
		COLORT m_WarnClr;

		int m_nSelMnuIdx;
		DWORD m_dwTmpTimeTick;

	private:
		enum{
			FR_MKD,
			FR_TRANS,
			FR_RF,
			FR_ALL,
			FR_EXIT,
			MAX_FR_MNU
		};
		
	public:
		CFactoryReset(cSCREEN *pScreen,cKEYBD *pKeyBD);
		virtual ~CFactoryReset(void);

	public:
		int StartFactoryReset(void);

	private:
		void InitVar();	
		
		int ClearMKDSetup(cLCDCON *pLcdCtrl);
		void ResetAndSyncTPBackupData();
		int ClearTransponderSetup(cLCDCON *pLcdCtrl);
		int ClearRFSetup(cLCDCON *pLcdCtrl);
		int ClearAllSetup(cLCDCON *pLcdCtrl);
		void ClearScreen(COLORT clr);
		void DrawStatus(BOOL bRedraw, BYTE *pMsg = NULL);
		void DrawMenu(BOOL bRedraw);
		void DrawFunctionDesc(BOOL bRedraw);
		void DrawTitle(BOOL bRedraw);
		void DrawWnd(BOOL bRedraw);
};

#endif	// End Of  __FACTORY_RESET__

