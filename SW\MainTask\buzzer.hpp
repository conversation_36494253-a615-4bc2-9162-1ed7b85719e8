/*...........................................................................*/
/*.                  File Name : BUZZER.HPP                                 .*/
/*.                                                                         .*/
/*.                       Date : 2004.02.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"

#ifndef  __BUZZER_HPP
#define  __BUZZER_HPP

//-----------------------------------------------------------------------------
#define  BUZZER_OFF                                        0
#define  BUZZER_CONTINUE                                   1
#define  BUZZER_SHORT                                      2
#define  BUZZER_LONG                                       3
#define  BUZZER_SHORT_LONG                                 4
#define  BUZZER_LONG_SHORT                                 5
#ifdef EN_61993_ED3
#define  BUZZER_TWO_TIME_SHORT                             6
#endif	// End of (EN_61993_ED3)
//-----------------------------------------------------------------------------
#define  BUZZER_CONTINUE_ON_TICK       CALC_MILI_TICK(10000)
#define  BUZZER_SHORT_ON_TICK          CALC_MILI_TICK(  200)
#define  BUZZER_SHORT_OFF_TICK         CALC_MILI_TICK(  200)
#define  BUZZER_LONG_ON_TICK           CALC_MILI_TICK(  400)
#define  BUZZER_LONG_OFF_TICK          CALC_MILI_TICK(  400)
#define  BUZZER_SHORT_LONG_ON_TICK     CALC_MILI_TICK(  200)
#define  BUZZER_SHORT_LONG_OFF_TICK    CALC_MILI_TICK(  300)
#define  BUZZER_LONG_SHORT_ON_TICK     CALC_MILI_TICK(  300)
#define  BUZZER_LONG_SHORT_OFF_TICK    CALC_MILI_TICK(  200)
#define  BUZZER_KEY_TONE_ON_TICK       CALC_MILI_TICK(  100)
//-----------------------------------------------------------------------------
#define  BUZZER_PORT_OFF                                   0
#define  BUZZER_PORT_ON                                    1
#define  BUZZER_PORT_BIT                           (1 << 29)
//-----------------------------------------------------------------------------
#define  LED_OFF                                           0
#define  LED_ON                                            1
#define  LED_BLINK                                         2
#define  LED_ON_TRIGGER                                    3
//-----------------------------------------------------------------------------
#define  LED_CONTINUE_ON_TICK          CALC_MILI_TICK(10000)
#define  LED_BLINK_ON_TICK             CALC_MILI_TICK(  200)
#define  LED_BLINK_OFF_TICK            CALC_MILI_TICK(  200)
//-----------------------------------------------------------------------------
#define  LED_PORT_OFF                                      0
#define  LED_PORT_ON                                       1
#define  TX_LED_PORT_BIT                           (1 <<  0)
#define  RX_LED_PORT_BIT                           (1 <<  1)
#define  PWR_LED_PORT_BIT                          (1 <<  4)
//-----------------------------------------------------------------------------
#define  BUZZER_BEEP_MODE_OFF                              0
#define  BUZZER_BEEP_MODE_ON                               1
//-----------------------------------------------------------------------------

class cBUZZER
{
   private:
	    static DWORD m_dBuzzerMode;    // BUZZER_OFF,...,BUZZER_LONG_SHORT
	    static DWORD m_dBuzzerTick;
	    static DWORD m_dBuzzerOffOn;   // 0=off,1=on
	    static DWORD m_dKeyToneTick;
	    static DWORD m_dTxLedMode;     // SYS_LED_OFF,...,LED_ON_TRIGGER
	    static DWORD m_dTxLedTick;
	    static DWORD m_dTxLedOffOn;    // 0=off,1=on
	    static DWORD m_dRxLedMode;     // SYS_LED_OFF,...,LED_ON_TRIGGER
	    static DWORD m_dRxLedTick;
	    static DWORD m_dRxLedOffOn;    // 0=off,1=on
	    static DWORD m_dPwrLedMode;    // SYS_LED_OFF,...,LED_ON_TRIGGER
	    static DWORD m_dPwrLedTick;
	    static DWORD m_dPwrLedOffOn;   // 0=off,1=on
	    static int   m_nBeepMode;
#ifdef EN_61993_ED3
		static int   m_nBzrCnt;
#endif	// End of (EN_61993_ED3)
   public:

   public:
      cBUZZER(void);
      virtual ~cBUZZER(void);
   public:
      DWORD GetBuzzerMode(void);
      void  SetBuzzerMode(DWORD dMode);
      DWORD GetTxLedMode(void);
      void  SetTxLedMode(DWORD dMode);
      DWORD GetRxLedMode(void);
      void  SetRxLedMode(DWORD dMode);
      DWORD GetPwrLedMode(void);
      void  SetPwrLedMode(DWORD dMode);
      static void  SetKeyToneOn(void);
      static void  SetBuzzerPort(void);
      static void  SetTxLedPort(void);
      static void  SetRxLedPort(void);
      static void  SetPwrLedPort(void);
      static void  SetBuzzerBeepOff(void);
      static void  SetBuzzerBeepOn(void);
      static void  RunIntHandler(void);
};

#endif

