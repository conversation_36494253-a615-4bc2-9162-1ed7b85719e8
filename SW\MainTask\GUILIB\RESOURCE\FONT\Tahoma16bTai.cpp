/*...........................................................................*/
/*.                  File Name : Tahoma16bTai.cpp                           .*/
/*.                                                                         .*/
/*.                       Date : 2009.06.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY WinInwa18bMiy_Font;

//...........................................................................
ROMDATA PEGUSHORT Tahoma16bAdd_Font_offset_table[257] = {
0x0000,0x0010,0x001c,0x002c,0x0039,0x0049,0x0056,0x0066,0x0073,0x0084,0x008f,0x00a0,0x00ad,0x00be,0x00cb,0x00dc,
0x00e9,0x00fa,0x0107,0x0118,0x0125,0x0134,0x013f,0x014e,0x0159,0x0168,0x0173,0x0182,0x018d,0x019c,0x01a6,0x01b5,
0x01be,0x01d0,0x01dc,0x01ee,0x01fb,0x020d,0x021a,0x022c,0x0239,0x024b,0x0258,0x026a,0x0277,0x0280,0x0288,0x0291,
0x0298,0x02aa,0x02b6,0x02c8,0x02d4,0x02e6,0x02f2,0x0301,0x0306,0x0315,0x031c,0x032b,0x0332,0x0341,0x0348,0x035f,
0x0372,0x0389,0x039c,0x03b3,0x03c6,0x03d7,0x03e4,0x03f5,0x0402,0x0413,0x0420,0x0431,0x043e,0x0450,0x045c,0x046e,
0x047a,0x048c,0x0498,0x04aa,0x04b6,0x04c5,0x04d2,0x04e1,0x04ee,0x04ff,0x0509,0x051a,0x0524,0x0535,0x053f,0x0550,
0x055a,0x0567,0x0570,0x057d,0x0586,0x0593,0x059c,0x05a9,0x05b2,0x05bf,0x05c8,0x05d7,0x05df,0x05ee,0x05f6,0x0605,
0x060d,0x061c,0x0625,0x0636,0x0643,0x0654,0x0661,0x0672,0x067f,0x0690,0x069d,0x06ae,0x06bb,0x06cb,0x06d6,0x06e6,
0x06f1,0x0708,0x0719,0x0730,0x0741,0x0758,0x0769,0x0780,0x0791,0x07a8,0x07b9,0x07c8,0x07d3,0x07e2,0x07ed,0x07fc,
0x0807,0x0815,0x081e,0x082c,0x0835,0x0843,0x084c,0x0859,0x0861,0x0872,0x087d,0x0889,0x0891,0x08a3,0x08b5,0x08c6,
0x08d8,0x08e8,0x08f4,0x0904,0x0910,0x0920,0x092c,0x093c,0x0948,0x0958,0x0964,0x0974,0x0980,0x0990,0x099c,0x09ac,
0x09b8,0x09c8,0x09d4,0x09e4,0x09f0,0x0a00,0x0a0c,0x0a1c,0x0a28,0x0a37,0x0a42,0x0a51,0x0a5c,0x0a6b,0x0a76,0x0a85,
0x0a90,0x0a9f,0x0aaa,0x0ab9,0x0ac4,0x0ad3,0x0ade,0x0aed,0x0af8,0x0b01,0x0b07,0x0b10,0x0b15,0x0b27,0x0b33,0x0b45,
0x0b51,0x0b63,0x0b6f,0x0b81,0x0b8d,0x0b9f,0x0bab,0x0bbd,0x0bc9,0x0bdb,0x0be7,0x0bf9,0x0c07,0x0c19,0x0c27,0x0c39,
0x0c47,0x0c59,0x0c67,0x0c79,0x0c87,0x0c98,0x0ca5,0x0cb6,0x0cc3,0x0cd6,0x0ce4,0x0cf7,0x0d05,0x0d18,0x0d26,0x0d39,
0x0d47,0x0d5a,0x0d68,0x0d77,0x0d82,0x0d91,0x0d9c,0x0dab,0x0db6,0x0dc5,0x0dd0,0x0de2,0x0df4,0x0e06,0x0e18,0x0e2a,
0x0e3c};



ROMDATA PEGUBYTE Tahoma16bAdd_Font_data_table[11400] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 
0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xc0, 0x00, 0x00, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 
0x00, 0x00, 0x01, 0x80, 0x03, 0x00, 0x30, 0x00, 0xc0, 0x03, 0xc0, 0x1f, 0x00, 0x72, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x30, 0x03, 0x00, 0x0c, 0x00, 0x3c, 0x00, 0xf8, 0x07, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x18, 0x03, 0x00, 0x18, 0x00, 0xf0, 0x0f, 0x80, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x30, 0x00, 0x30, 0x01, 0x80, 
0x03, 0x00, 0x07, 0x80, 0x1f, 0x00, 0x39, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x0e, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xee, 0x01, 0xdc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0x06, 0x00, 0x60, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x1c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x30, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x07, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x07, 0x00, 0x00, 0x1c, 0x01, 0xc0, 0x00, 0x03, 0x80, 0x70, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x80, 0x00, 0x00, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x01, 0xdc, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x0d, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 
0x00, 0x00, 0x01, 0x00, 0x06, 0x00, 0x18, 0x00, 0x60, 0x00, 0x80, 0x1b, 0x00, 0x4e, 0x01, 0xc4, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x60, 0x01, 0x80, 0x06, 0x00, 0x08, 0x00, 0xd8, 0x04, 0xe0, 
0x1c, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x00, 0x30, 0x01, 0x80, 0x0c, 0x00, 0x20, 0x0d, 0x80, 0x4e, 0x03, 0x88, 0x00, 0x00, 0x00, 0x36, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x20, 0x00, 0x60, 0x00, 0xc0, 
0x01, 0x80, 0x01, 0x00, 0x1b, 0x00, 0x27, 0x00, 0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x00, 0x03, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x0d, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x42, 0x00, 0x00, 0x0e, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x0e, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xee, 0x01, 0xdc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x33, 0x0c, 0x00, 0xc0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x1c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x40, 0x03, 0x00, 0x39, 0x00, 0x77, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x0c, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x07, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0x07, 0x00, 0xd0, 0x1c, 0x01, 0xc0, 0x00, 0x03, 0x80, 0x70, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x07, 0x20, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x01, 0xdc, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x00, 0x00, 0x03, 0x80, 0x04, 0x00, 0x38, 0x00, 0x20, 0x03, 0x80, 0x02, 0x00, 0x38, 0x02, 0x38, 
0x03, 0x80, 0x00, 0x00, 0x42, 0x00, 0x40, 0x04, 0x20, 0x02, 0x00, 0x42, 0x00, 0x10, 0x04, 0x20, 
0x23, 0x80, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x71, 0x00, 0x00, 0x0e, 
0x00, 0x20, 0x03, 0x80, 0x04, 0x00, 0xe0, 0x01, 0x00, 0x38, 0x04, 0x70, 0x0e, 0x00, 0x00, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x70, 0x00, 0x40, 0x01, 0xc0, 
0x00, 0x80, 0x07, 0x00, 0x02, 0x00, 0x1c, 0x00, 0x8e, 0x00, 0x70, 0x00, 0x00, 0x01, 0x80, 0x00, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x07, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x18, 0x1c, 0x00, 0x00, 0x0c, 0x0e, 0x00, 
0x00, 0x01, 0x07, 0x00, 0x00, 0x0e, 0x23, 0x80, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x01, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x1c, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x60, 0x0f, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3c, 0x00, 0x00, 0x0e, 0x00, 0x70, 0x0f, 0xe0, 0x00, 0x00, 0x0e, 0x00, 0x0e, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xee, 0x01, 0xdc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x33, 0x08, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3f, 0x87, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x1c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0xc0, 0x06, 0x00, 0x27, 0x00, 0x77, 0x00, 
0xfc, 0x00, 0x30, 0x03, 0xf0, 0x00, 0xc0, 0x08, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x07, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x07, 0x00, 0x60, 0x1c, 0x01, 0xc0, 0x00, 0x03, 0x80, 0x70, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0xe0, 0x06, 0x00, 0x1f, 0x80, 0x77, 0x00, 0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x01, 0xdc, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x10, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0x00, 0xe0, 0x03, 0x80, 
0x60, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x00, 0x06, 0xc0, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x06, 0xc0, 0x04, 0x00, 0x6c, 0x00, 0x00, 
0x06, 0xc0, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x3c, 0x00, 0x20, 0x03, 0xc0, 
0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x8e, 0x00, 0x00, 0x1b, 
0x00, 0x00, 0x06, 0xc0, 0x00, 0x01, 0xb0, 0x02, 0x00, 0x6c, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0xd8, 0x00, 0x00, 0x03, 0x60, 
0x00, 0x00, 0x0d, 0x80, 0x04, 0x00, 0x36, 0x00, 0x00, 0x00, 0xd8, 0x00, 0x00, 0x01, 0x01, 0x00, 
0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0x81, 0x00, 0x00, 0x08, 0xe1, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x10, 0x1c, 0x00, 0x00, 0x04, 0x0e, 0x00, 
0x00, 0x02, 0x07, 0x00, 0x00, 0x11, 0xc3, 0x80, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x23, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x77, 0x00, 
0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x10, 0x04, 0x40, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xf0, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0xe0, 
0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x70, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x07, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x70, 0x00, 
0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 
0x03, 0x80, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x03, 0x80, 
0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x03, 0x87, 0xc0, 0x00, 0x03, 0x80, 0x00, 0x00, 0x03, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x06, 0x00, 
0x00, 0x00, 0x03, 0x1f, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x01, 0x80, 0x00, 0x07, 0xff, 0x07, 0xb8, 0x3f, 0xf8, 0x3c, 0x01, 0xff, 0xc1, 0xe0, 0x00, 0x7e, 
0x40, 0x30, 0xff, 0xc0, 0x1d, 0xe3, 0xff, 0x00, 0x07, 0x8f, 0xfc, 0x00, 0x1e, 0x3f, 0xf0, 0x00, 
0x78, 0xff, 0xc0, 0x01, 0xe3, 0xff, 0xc0, 0x00, 0xff, 0xf0, 0x00, 0x3f, 0xfc, 0x00, 0x0f, 0xff, 
0x00, 0x07, 0xff, 0xc0, 0x01, 0xff, 0xf0, 0x78, 0x0f, 0xc4, 0x00, 0x07, 0xf3, 0xf9, 0xe0, 0x0f, 
0xe7, 0xf3, 0xc0, 0x1f, 0xcf, 0xe7, 0x80, 0x3f, 0x9f, 0xcf, 0x00, 0x7f, 0x3f, 0x9e, 0x00, 0xfe, 
0x38, 0x7f, 0x77, 0x7f, 0x1f, 0xbc, 0x01, 0xfc, 0x7e, 0xf0, 0x07, 0xf1, 0xfb, 0xc0, 0x1f, 0xc0, 
0x79, 0xfc, 0x03, 0xc7, 0xf0, 0x0f, 0x1f, 0xc0, 0x3c, 0x7e, 0x00, 0xfc, 0x00, 0x60, 0x1f, 0x80, 
0x3f, 0x00, 0x70, 0x07, 0xe0, 0x0f, 0xc0, 0x00, 0x01, 0xf8, 0x7c, 0x0e, 0x07, 0xe1, 0xf0, 0x00, 
0x1f, 0x87, 0xc0, 0x00, 0x7e, 0x1f, 0x00, 0x00, 0x0f, 0xc0, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 
0xfc, 0x00, 0x00, 0x03, 0xf0, 0x00, 0x01, 0xff, 0x80, 0x0c, 0x1f, 0xf8, 0x01, 0xc1, 0xff, 0xc0, 
0x1c, 0x3f, 0xf8, 0x00, 0x07, 0xff, 0x00, 0x00, 0xff, 0xe0, 0x00, 0x07, 0xc8, 0x38, 0x1f, 0x20, 
0x00, 0x7c, 0x81, 0x81, 0xf2, 0x22, 0x07, 0xc8, 0x70, 0x7f, 0xfc, 0x10, 0xff, 0xf8, 0x21, 0xff, 
0xf0, 0x43, 0xff, 0xe0, 0x83, 0xf8, 0xf8, 0x00, 0x0f, 0xe3, 0xe0, 0x00, 0x3f, 0x8f, 0x80, 0x00, 
0xfe, 0x3e, 0x00, 0x03, 0xf8, 0xf8, 0x77, 0x0f, 0xc3, 0xe0, 0x01, 0xf8, 0x7c, 0x00, 0x7e, 0x7f, 
0x1e, 0x03, 0x00, 0x7e, 0x7f, 0x1e, 0x00, 0x60, 0x7e, 0x7f, 0x1e, 0x07, 0x70, 0x7e, 0x7f, 0x1e, 
0x01, 0xc0, 0x7e, 0x7f, 0x1e, 0x00, 0x00, 0x7f, 0x3f, 0x0e, 0x1f, 0xcf, 0xce, 0xe7, 0xf1, 0xf0, 
0xe0, 0xff, 0xf1, 0xb1, 0xff, 0xe0, 0x03, 0xff, 0xc0, 0x07, 0x80, 0x04, 0x01, 0x10, 0x04, 0x40, 
0x10, 0x0f, 0x0f, 0xff, 0x03, 0xff, 0xc0, 0x3e, 0x00, 0x7f, 0xf8, 0x01, 0x80, 0x00, 0x00, 0x18, 
0x01, 0xb0, 0x01, 0x80, 0x1b, 0x00, 0x18, 0x01, 0xb0, 0x01, 0x80, 0x1b, 0x00, 0x18, 0x01, 0xb0, 
0x01, 0x80, 0x1b, 0x00, 0x18, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x18, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x18, 0x00, 0x00, 0x7f, 0xf8, 0x00, 0x1f, 0xfe, 0x0d, 0x87, 0xff, 0x80, 0x01, 0xff, 
0xe0, 0xd8, 0x7f, 0xf8, 0x36, 0x1f, 0xfe, 0x0d, 0x87, 0xff, 0x83, 0x61, 0xff, 0xe0, 0xd8, 0x7f, 
0x36, 0xfe, 0x70, 0x1f, 0x80, 0x00, 0x00, 0x7e, 0x00, 0x6c, 0x01, 0xf8, 0x01, 0xb0, 0x07, 0xe0, 
0x06, 0xc0, 0x1f, 0x80, 0x1b, 0x00, 0x7e, 0x00, 0x6c, 0x01, 0xf8, 0x01, 0xb0, 0x07, 0xe3, 0x81, 
0x80, 0x07, 0xe3, 0x86, 0x00, 0x07, 0xe3, 0x86, 0xc0, 0x07, 0xe3, 0x80, 0x00, 0x07, 0xe3, 0x80, 
0x00, 0xfe, 0x3e, 0x00, 0x03, 0xf8, 0xf8, 0x36, 0x0f, 0xe3, 0xf8, 0x0c, 0x07, 0xf1, 0xfc, 0x30, 
0x03, 0xf8, 0xfe, 0x1b, 0x01, 0xfc, 0x7f, 0x00, 0x00, 0xfe, 0x3f, 0x80, 0x00, 0xfe, 0x3e, 0x18, 
0x3f, 0x8f, 0x80, 0x0f, 0xe3, 0xe3, 0x63, 0xf8, 0xf8, 0x00, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 
0xe0, 0x7f, 0xf8, 0x1f, 0xfe, 0x07, 0xff, 0x80, 

0x01, 0x80, 0x00, 0x01, 0xc3, 0x83, 0xb8, 0x0e, 0x1c, 0x1c, 0x00, 0x70, 0xe0, 0xe0, 0x01, 0xc3, 
0xc0, 0x60, 0x38, 0x70, 0x1c, 0xe0, 0xe1, 0xc0, 0x03, 0x83, 0x87, 0x00, 0x0e, 0x0e, 0x1c, 0x00, 
0x38, 0x38, 0x70, 0x00, 0xe0, 0xe1, 0xc0, 0x00, 0x38, 0x70, 0x00, 0x0e, 0x1c, 0x00, 0x03, 0x87, 
0x00, 0x01, 0xc1, 0xc2, 0x10, 0x70, 0x71, 0xdc, 0x38, 0x3c, 0x00, 0x01, 0xc0, 0xe0, 0xe0, 0x03, 
0x81, 0xc1, 0xc0, 0x07, 0x03, 0x83, 0x80, 0x0e, 0x07, 0x07, 0x00, 0x1c, 0x0e, 0x0e, 0x00, 0x38, 
0x38, 0x1c, 0x77, 0x1c, 0x06, 0x1c, 0x00, 0x70, 0x18, 0x70, 0x01, 0xc0, 0x61, 0xc0, 0x07, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x1e, 0x00, 0xf0, 0x00, 0xc0, 0x07, 0x80, 
0x3c, 0x00, 0x70, 0x01, 0xe0, 0x0f, 0x00, 0x00, 0x00, 0x78, 0x10, 0x0e, 0x01, 0xe0, 0x40, 0x00, 
0x07, 0x81, 0x00, 0x00, 0x1e, 0x04, 0x00, 0x00, 0x38, 0x70, 0x1c, 0x40, 0xe1, 0xc0, 0x71, 0x03, 
0x87, 0x00, 0x00, 0x0e, 0x1c, 0x00, 0x00, 0x70, 0xe0, 0x18, 0x07, 0x0e, 0x01, 0xc0, 0x70, 0x70, 
0x1c, 0x0e, 0x0e, 0x00, 0x01, 0xc1, 0xc0, 0x00, 0x38, 0x38, 0x00, 0x0e, 0x38, 0x38, 0x38, 0xe0, 
0x00, 0xe3, 0x83, 0x03, 0x8e, 0x36, 0x0e, 0x38, 0x70, 0x63, 0x8c, 0x10, 0xc7, 0x18, 0x21, 0x8e, 
0x30, 0x43, 0x1c, 0x60, 0x80, 0xe0, 0x20, 0x00, 0x03, 0x80, 0x80, 0x00, 0x0e, 0x02, 0x00, 0x00, 
0x38, 0x08, 0x1c, 0x40, 0xe0, 0x20, 0x00, 0x07, 0x80, 0xc3, 0x88, 0xf0, 0x18, 0x00, 0x1c, 0x1c, 
0x04, 0x01, 0x80, 0x1c, 0x1c, 0x04, 0x00, 0xc0, 0x1c, 0x1c, 0x04, 0x07, 0x70, 0x1c, 0x1c, 0x04, 
0x01, 0xc0, 0x1c, 0x1c, 0x04, 0x00, 0x00, 0x3c, 0x0c, 0x0e, 0x0f, 0x03, 0x0e, 0xe1, 0xc0, 0x60, 
0xe0, 0xe0, 0xe1, 0x11, 0xc1, 0xc0, 0x03, 0x83, 0x80, 0x03, 0x80, 0x04, 0x01, 0x10, 0x04, 0x40, 
0x10, 0x19, 0x88, 0x01, 0x02, 0x00, 0x40, 0x61, 0x80, 0x40, 0x08, 0x01, 0x80, 0x00, 0x00, 0x18, 
0x00, 0x20, 0x01, 0x80, 0x11, 0x00, 0x18, 0x01, 0x10, 0x01, 0x80, 0x11, 0x00, 0x18, 0x01, 0x10, 
0x01, 0x80, 0x11, 0x00, 0x18, 0x01, 0x08, 0x01, 0x80, 0x10, 0x80, 0x18, 0x01, 0x08, 0x01, 0x80, 
0x10, 0x80, 0x18, 0x01, 0x08, 0x1c, 0x38, 0x00, 0x07, 0x0e, 0x01, 0x01, 0xc3, 0x83, 0x88, 0x70, 
0xe0, 0x88, 0x1c, 0x38, 0x22, 0x07, 0x0e, 0x08, 0x81, 0xc3, 0x82, 0x20, 0x70, 0xe0, 0x88, 0x1c, 
0x04, 0x38, 0x70, 0x70, 0xe0, 0x00, 0x01, 0xc3, 0x80, 0x08, 0x07, 0x0e, 0x01, 0x10, 0x1c, 0x38, 
0x04, 0x40, 0x70, 0xe0, 0x11, 0x01, 0xc3, 0x80, 0x44, 0x07, 0x0e, 0x01, 0x10, 0x1c, 0x39, 0x83, 
0x00, 0x1c, 0x39, 0x83, 0x00, 0x1c, 0x39, 0x80, 0x80, 0x1c, 0x39, 0x8e, 0x20, 0x1c, 0x39, 0x80, 
0x00, 0x38, 0x08, 0x00, 0x00, 0xe0, 0x20, 0x04, 0x03, 0x80, 0x80, 0x18, 0x01, 0xc0, 0x40, 0x18, 
0x00, 0xe0, 0x20, 0x02, 0x00, 0x70, 0x10, 0x0e, 0x20, 0x38, 0x08, 0x00, 0x00, 0x38, 0x0c, 0x0c, 
0x0e, 0x03, 0x00, 0x03, 0x80, 0xc0, 0x40, 0xe0, 0x30, 0xe2, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x03, 0xc0, 0x00, 0x01, 0xc1, 0xc3, 0xb8, 0x0e, 0x0e, 0x1c, 0x00, 0x70, 0x70, 0xe0, 0x03, 0x80, 
0xc0, 0x40, 0x38, 0x38, 0x1c, 0xe0, 0xe0, 0xe0, 0x03, 0x83, 0x83, 0x80, 0x0e, 0x0e, 0x0e, 0x00, 
0x38, 0x38, 0x38, 0x00, 0xe0, 0xe0, 0xc3, 0xf8, 0x38, 0x30, 0xfe, 0x0e, 0x0c, 0x00, 0x03, 0x83, 
0x00, 0x01, 0xc0, 0xc1, 0xe0, 0x70, 0x31, 0xd8, 0x70, 0x1c, 0x3f, 0x81, 0xc0, 0xe0, 0xe0, 0x03, 
0x81, 0xc1, 0xc0, 0x07, 0x03, 0x83, 0x80, 0x0e, 0x07, 0x07, 0x00, 0x1c, 0x0e, 0x0e, 0x00, 0x38, 
0x38, 0x1c, 0x77, 0x1c, 0x0c, 0x1c, 0x00, 0x70, 0x30, 0x70, 0x01, 0xc0, 0xc1, 0xc0, 0x07, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x17, 0x01, 0x70, 0x00, 0x80, 0x05, 0xc0, 
0x5c, 0x00, 0x70, 0x01, 0x70, 0x17, 0x00, 0x00, 0x00, 0x7c, 0x10, 0x0e, 0x01, 0xf0, 0x40, 0x00, 
0x07, 0xc1, 0x00, 0x00, 0x1f, 0x04, 0x00, 0x00, 0x70, 0x38, 0x23, 0x81, 0xc0, 0xe0, 0x8e, 0x07, 
0x03, 0x81, 0xfc, 0x1c, 0x0e, 0x07, 0xf0, 0x70, 0x70, 0x10, 0x07, 0x07, 0x01, 0xc0, 0x70, 0x38, 
0x1c, 0x0e, 0x07, 0x00, 0x01, 0xc0, 0xe1, 0xfc, 0x38, 0x1c, 0x00, 0x1c, 0x18, 0x38, 0x70, 0x60, 
0x01, 0xc1, 0x82, 0x07, 0x06, 0x1c, 0x1c, 0x18, 0x70, 0x43, 0x84, 0x30, 0x87, 0x08, 0x61, 0x0e, 
0x10, 0xc2, 0x1c, 0x21, 0x80, 0xe0, 0x20, 0x00, 0x03, 0x80, 0x80, 0x00, 0x0e, 0x02, 0x00, 0x00, 
0x38, 0x08, 0x23, 0x80, 0xe0, 0x20, 0x7f, 0x03, 0x80, 0x84, 0x70, 0x70, 0x10, 0x00, 0x1c, 0x0e, 
0x08, 0x00, 0x80, 0x1c, 0x0e, 0x08, 0x00, 0x80, 0x1c, 0x0e, 0x08, 0x07, 0x70, 0x1c, 0x0e, 0x08, 
0x01, 0xc0, 0x1c, 0x0e, 0x08, 0x00, 0x00, 0x1e, 0x08, 0x0e, 0x07, 0x82, 0x0e, 0xe1, 0xe0, 0x40, 
0xe0, 0xc1, 0xc2, 0x09, 0x83, 0x80, 0x03, 0x07, 0x00, 0x03, 0x80, 0x0c, 0x00, 0xe0, 0x03, 0x80, 
0x60, 0x39, 0x88, 0x01, 0x02, 0x00, 0x40, 0xe0, 0xc0, 0x40, 0x08, 0x03, 0xc0, 0x00, 0x00, 0x3c, 
0x00, 0x40, 0x03, 0xc0, 0x20, 0x80, 0x3c, 0x02, 0x08, 0x03, 0xc0, 0x20, 0x80, 0x3c, 0x02, 0x08, 
0x03, 0xc0, 0x20, 0x80, 0x3c, 0x00, 0xf0, 0x03, 0xc0, 0x0f, 0x00, 0x3c, 0x00, 0xf0, 0x03, 0xc0, 
0x0f, 0x00, 0x3c, 0x00, 0xf0, 0x1c, 0x18, 0x00, 0x07, 0x06, 0x02, 0x01, 0xc1, 0x84, 0x70, 0x70, 
0x61, 0x04, 0x1c, 0x18, 0x41, 0x07, 0x06, 0x10, 0x41, 0xc1, 0x84, 0x10, 0x70, 0x61, 0x04, 0x1c, 
0x08, 0x38, 0x70, 0xe0, 0x70, 0x00, 0x03, 0x81, 0xc0, 0x10, 0x0e, 0x07, 0x02, 0x08, 0x38, 0x1c, 
0x08, 0x20, 0xe0, 0x70, 0x20, 0x83, 0x81, 0xc0, 0x82, 0x0e, 0x07, 0x02, 0x08, 0x38, 0x1f, 0x02, 
0x00, 0x38, 0x1f, 0x01, 0x00, 0x38, 0x1f, 0x01, 0x00, 0x38, 0x1f, 0x11, 0xc0, 0x38, 0x1f, 0x00, 
0x00, 0x38, 0x08, 0x00, 0x00, 0xe0, 0x20, 0x08, 0x03, 0x80, 0x80, 0x10, 0x01, 0xc0, 0x40, 0x08, 
0x00, 0xe0, 0x20, 0x04, 0x00, 0x70, 0x10, 0x11, 0xc0, 0x38, 0x08, 0x00, 0x00, 0x3c, 0x08, 0x04, 
0x0f, 0x02, 0x00, 0x03, 0xc0, 0x80, 0x80, 0xf0, 0x21, 0x1c, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x03, 0xc0, 0x00, 0x01, 0xc1, 0xc3, 0x80, 0x0e, 0x0e, 0x1c, 0x00, 0x70, 0x70, 0xe0, 0x07, 0x00, 
0xc0, 0x00, 0x38, 0x1c, 0x00, 0xe0, 0xe0, 0x70, 0x03, 0x83, 0x81, 0xc0, 0x0e, 0x0e, 0x07, 0x00, 
0x38, 0x38, 0x1c, 0x00, 0xe0, 0xe2, 0x40, 0x00, 0x38, 0x90, 0x00, 0x0e, 0x24, 0x00, 0x03, 0x89, 
0x00, 0x01, 0xc4, 0x40, 0x00, 0x71, 0x11, 0xc0, 0xe0, 0x0c, 0x00, 0x01, 0xc0, 0xe0, 0xe0, 0x03, 
0x81, 0xc1, 0xc0, 0x07, 0x03, 0x83, 0x80, 0x0e, 0x07, 0x07, 0x00, 0x1c, 0x0e, 0x0e, 0x00, 0x38, 
0x00, 0x1c, 0x00, 0x1c, 0x10, 0x1c, 0x00, 0x70, 0x40, 0x70, 0x01, 0xc1, 0x01, 0xc0, 0x07, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x17, 0x01, 0x70, 0x00, 0x00, 0x05, 0xc0, 
0x5c, 0x00, 0x00, 0x01, 0x70, 0x17, 0x00, 0x00, 0x00, 0x5e, 0x10, 0x00, 0x01, 0x78, 0x40, 0x00, 
0x05, 0xe1, 0x00, 0x00, 0x17, 0x84, 0x00, 0x00, 0xe0, 0x1c, 0x00, 0x03, 0x80, 0x70, 0x00, 0x0e, 
0x01, 0xc0, 0x00, 0x38, 0x07, 0x00, 0x00, 0x70, 0x70, 0x00, 0x07, 0x07, 0x00, 0x00, 0x70, 0x38, 
0x00, 0x0e, 0x07, 0x00, 0x01, 0xc0, 0xe0, 0x00, 0x38, 0x1c, 0x00, 0x1c, 0x08, 0x00, 0x70, 0x20, 
0x01, 0xc0, 0x80, 0x07, 0x02, 0x00, 0x1c, 0x08, 0x00, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0x00, 0x03, 0x80, 0x80, 0x00, 0x0e, 0x02, 0x00, 0x00, 
0x38, 0x08, 0x00, 0x00, 0xe0, 0x20, 0x00, 0x03, 0x80, 0x80, 0x00, 0x70, 0x10, 0x00, 0x0e, 0x0e, 
0x08, 0x00, 0x00, 0x0e, 0x0e, 0x08, 0x00, 0x00, 0x0e, 0x0e, 0x08, 0x00, 0x00, 0x0e, 0x0e, 0x08, 
0x00, 0x00, 0x0e, 0x0e, 0x08, 0x00, 0x00, 0x0f, 0x10, 0x00, 0x03, 0xc4, 0x00, 0x00, 0xe0, 0x80, 
0x00, 0x83, 0xc0, 0x01, 0x07, 0x80, 0x02, 0x0f, 0x00, 0x03, 0x80, 0x1c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc1, 0xc0, 0x40, 0x08, 0x03, 0xc0, 0x00, 0x00, 0x3c, 
0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x3c, 0x00, 0x00, 
0x03, 0xc0, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x03, 0xc0, 
0x00, 0x00, 0x3c, 0x00, 0x00, 0x1c, 0x48, 0x00, 0x07, 0x12, 0x00, 0x01, 0xc4, 0x80, 0x00, 0x71, 
0x20, 0x00, 0x1c, 0x48, 0x00, 0x07, 0x12, 0x00, 0x01, 0xc4, 0x80, 0x00, 0x71, 0x20, 0x00, 0x1c, 
0x00, 0x38, 0x01, 0xc0, 0x38, 0x00, 0x07, 0x00, 0xe0, 0x00, 0x1c, 0x03, 0x80, 0x00, 0x70, 0x0e, 
0x00, 0x01, 0xc0, 0x38, 0x00, 0x07, 0x00, 0xe0, 0x00, 0x1c, 0x03, 0x80, 0x00, 0x70, 0x0c, 0x00, 
0x0c, 0x70, 0x0c, 0x00, 0x0c, 0x70, 0x0c, 0x00, 0x0c, 0x70, 0x0c, 0x00, 0x0c, 0x70, 0x0c, 0x00, 
0x0c, 0x38, 0x08, 0x00, 0x00, 0xe0, 0x20, 0x00, 0x03, 0x80, 0x80, 0x00, 0x01, 0xc0, 0x40, 0x00, 
0x00, 0xe0, 0x20, 0x00, 0x00, 0x70, 0x10, 0x00, 0x00, 0x38, 0x08, 0x00, 0x00, 0x1c, 0x10, 0x00, 
0x07, 0x04, 0x00, 0x01, 0xc1, 0x00, 0x00, 0x70, 0x40, 0x00, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x03, 0xc0, 0x1f, 0x01, 0xc1, 0xc3, 0xbc, 0x0e, 0x0e, 0x1d, 0xe0, 0x70, 0x70, 0xef, 0x07, 0x00, 
0x40, 0xf8, 0x38, 0x1c, 0x1e, 0xe0, 0xe0, 0x70, 0x7b, 0x83, 0x81, 0xc1, 0xee, 0x0e, 0x07, 0x07, 
0xb8, 0x38, 0x1c, 0x1e, 0xe0, 0xe2, 0x40, 0xf0, 0x38, 0x90, 0x3c, 0x0e, 0x24, 0x0f, 0x03, 0x89, 
0x03, 0xc1, 0xc4, 0x41, 0xe0, 0x71, 0x17, 0xf0, 0xe0, 0x04, 0x1f, 0xe1, 0xc0, 0xe0, 0xef, 0x03, 
0x81, 0xc1, 0xde, 0x07, 0x03, 0x83, 0xbc, 0x0e, 0x07, 0x07, 0x78, 0x1c, 0x0e, 0x0e, 0xf0, 0x38, 
0x78, 0x1c, 0x3c, 0x1c, 0x20, 0x1c, 0x78, 0x70, 0x80, 0x71, 0xe1, 0xc2, 0x01, 0xc7, 0x87, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x17, 0x02, 0x70, 0xf7, 0x9e, 0x05, 0xc0, 
0x9c, 0x3d, 0xe7, 0x81, 0x70, 0x27, 0x0f, 0x79, 0xe0, 0x4e, 0x10, 0xf7, 0x81, 0x38, 0x43, 0xde, 
0x04, 0xe1, 0x0f, 0x78, 0x13, 0x84, 0x3d, 0xe0, 0xe0, 0x1c, 0x0f, 0x03, 0x80, 0x70, 0x3c, 0x0e, 
0x01, 0xc0, 0xf0, 0x38, 0x07, 0x03, 0xc0, 0x70, 0x73, 0xde, 0x07, 0x07, 0x3d, 0xe0, 0x70, 0x38, 
0xf7, 0x0e, 0x07, 0x1e, 0xe1, 0xc0, 0xe3, 0xdc, 0x38, 0x1c, 0x7b, 0x9e, 0x08, 0x74, 0x78, 0x21, 
0xd1, 0xe0, 0x87, 0x47, 0x82, 0x1d, 0x1e, 0x08, 0x74, 0x03, 0x80, 0xfc, 0x07, 0x01, 0xf8, 0x0e, 
0x03, 0xf0, 0x1c, 0x07, 0xe0, 0xe0, 0x21, 0xe7, 0x83, 0x80, 0x87, 0x9e, 0x0e, 0x02, 0x1e, 0x78, 
0x38, 0x08, 0x79, 0xe0, 0xe0, 0x21, 0xe7, 0x81, 0xc1, 0x1f, 0x3c, 0x38, 0x23, 0xe7, 0x8e, 0x0e, 
0x08, 0xfb, 0xf3, 0x8e, 0x0e, 0x08, 0xfb, 0xf3, 0x8e, 0x0e, 0x08, 0xfb, 0xf3, 0x8e, 0x0e, 0x08, 
0xfb, 0xf3, 0x8e, 0x0e, 0x08, 0xfb, 0xf3, 0x87, 0x20, 0xfb, 0xc1, 0xc8, 0x3e, 0xf0, 0x71, 0x8f, 
0x9e, 0x83, 0x83, 0xf9, 0x07, 0x07, 0xf2, 0x0e, 0x0f, 0xe3, 0xbc, 0x3f, 0x7d, 0xf9, 0xfe, 0x78, 
0xf8, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc3, 0x80, 0x40, 0x08, 0x03, 0xc0, 0x1f, 0x00, 0x3c, 
0x01, 0xf0, 0x03, 0xc0, 0x1f, 0x00, 0x3c, 0x01, 0xf0, 0x03, 0xc0, 0x1f, 0x00, 0x3c, 0x01, 0xf0, 
0x03, 0xc0, 0x1f, 0x00, 0x3c, 0x01, 0xf0, 0x03, 0xc0, 0x1f, 0x00, 0x3c, 0x01, 0xf0, 0x03, 0xc0, 
0x1f, 0x00, 0x3c, 0x01, 0xf0, 0x1c, 0x48, 0x1e, 0x07, 0x12, 0x07, 0x81, 0xc4, 0x81, 0xe0, 0x71, 
0x20, 0x78, 0x1c, 0x48, 0x1e, 0x07, 0x12, 0x07, 0x81, 0xc4, 0x81, 0xe0, 0x71, 0x20, 0x78, 0x1c, 
0x3c, 0x38, 0xf1, 0xc0, 0x38, 0x1e, 0x07, 0x00, 0xe0, 0x78, 0x1c, 0x03, 0x81, 0xe0, 0x70, 0x0e, 
0x07, 0x81, 0xc0, 0x38, 0x1e, 0x07, 0x00, 0xe0, 0x78, 0x1c, 0x03, 0x81, 0xe0, 0x70, 0x0e, 0x07, 
0x8e, 0x70, 0x0e, 0x07, 0x8e, 0x70, 0x0e, 0x07, 0x8e, 0x70, 0x0e, 0x07, 0x8e, 0x70, 0x0e, 0x07, 
0x8e, 0x38, 0x08, 0x79, 0xe0, 0xe0, 0x21, 0xe7, 0x83, 0x80, 0x83, 0xcf, 0x31, 0xc0, 0x41, 0xe7, 
0x98, 0xe0, 0x20, 0xf3, 0xcc, 0x70, 0x10, 0x79, 0xe6, 0x38, 0x08, 0x3c, 0xf3, 0x0e, 0x31, 0xf3, 
0xc3, 0x8c, 0x7c, 0xf0, 0xe3, 0x1f, 0x3c, 0x38, 0xc7, 0xcf, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x04, 0xe0, 0x21, 0xc1, 0xc1, 0xc3, 0xce, 0x0e, 0x0e, 0x1e, 0x70, 0x70, 0x70, 0xf3, 0x8f, 0x00, 
0x01, 0x9c, 0x38, 0x1e, 0x39, 0xe0, 0xe0, 0x78, 0xe7, 0x83, 0x81, 0xe3, 0x9e, 0x0e, 0x07, 0x8e, 
0x78, 0x38, 0x1e, 0x39, 0xe0, 0xe6, 0x03, 0x98, 0x39, 0x80, 0xe6, 0x0e, 0x60, 0x39, 0x83, 0x98, 
0x0e, 0x61, 0xc4, 0x03, 0x30, 0x73, 0x01, 0xc1, 0xe0, 0x00, 0x3b, 0x81, 0xc0, 0xe0, 0xf3, 0x83, 
0x81, 0xc1, 0xe7, 0x07, 0x03, 0x83, 0xce, 0x0e, 0x07, 0x07, 0x9c, 0x1c, 0x0e, 0x0f, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1c, 0xc0, 0x1c, 0x30, 0x73, 0x00, 0x70, 0xc1, 0xcc, 0x01, 0xc3, 0x07, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x13, 0x82, 0x70, 0x79, 0xe7, 0x04, 0xe0, 
0x9c, 0x1e, 0x79, 0xc1, 0x38, 0x27, 0x07, 0x9e, 0x70, 0x47, 0x10, 0x79, 0xc1, 0x1c, 0x41, 0xe7, 
0x04, 0x71, 0x07, 0x9c, 0x11, 0xc4, 0x1e, 0x71, 0xc0, 0x0e, 0x39, 0xc7, 0x00, 0x38, 0xe7, 0x1c, 
0x00, 0xe3, 0x9c, 0x70, 0x03, 0x8e, 0x70, 0x70, 0x71, 0xe7, 0x07, 0x07, 0x1e, 0x70, 0x70, 0x38, 
0x7b, 0x0e, 0x07, 0x0f, 0x61, 0xc0, 0xe1, 0xec, 0x38, 0x1c, 0x3d, 0x9f, 0x80, 0xcc, 0x7e, 0x03, 
0x31, 0xf8, 0x0c, 0xc7, 0xe0, 0x33, 0x1f, 0x80, 0xcc, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x83, 0x8e, 0x0e, 0x02, 0x0e, 0x38, 
0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x81, 0xc1, 0x0e, 0x08, 0x38, 0x21, 0xc1, 0x0f, 0x17, 
0x10, 0x70, 0xe1, 0x0f, 0x17, 0x10, 0x70, 0xe1, 0x0f, 0x17, 0x10, 0x70, 0xe1, 0x0f, 0x17, 0x10, 
0x70, 0xe1, 0x0f, 0x17, 0x10, 0x70, 0xe1, 0x07, 0xc0, 0x71, 0x01, 0xf0, 0x1c, 0x40, 0x71, 0x07, 
0x04, 0x07, 0x82, 0x38, 0x0f, 0x04, 0x70, 0x1e, 0x08, 0xe3, 0xce, 0x1c, 0x38, 0x70, 0x9c, 0x11, 
0x0e, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc7, 0x00, 0x40, 0x08, 0x04, 0xe0, 0x21, 0xc0, 0x4e, 
0x02, 0x1c, 0x04, 0xe0, 0x21, 0xc0, 0x4e, 0x02, 0x1c, 0x04, 0xe0, 0x21, 0xc0, 0x4e, 0x02, 0x1c, 
0x04, 0xe0, 0x21, 0xc0, 0x4e, 0x02, 0x1c, 0x04, 0xe0, 0x21, 0xc0, 0x4e, 0x02, 0x1c, 0x04, 0xe0, 
0x21, 0xc0, 0x4e, 0x02, 0x1c, 0x1c, 0xc0, 0x73, 0x07, 0x30, 0x1c, 0xc1, 0xcc, 0x07, 0x30, 0x73, 
0x01, 0xcc, 0x1c, 0xc0, 0x73, 0x07, 0x30, 0x1c, 0xc1, 0xcc, 0x07, 0x30, 0x73, 0x01, 0xcc, 0x1c, 
0x1c, 0x38, 0x73, 0x80, 0x1c, 0x73, 0x8e, 0x00, 0x71, 0xce, 0x38, 0x01, 0xc7, 0x38, 0xe0, 0x07, 
0x1c, 0xe3, 0x80, 0x1c, 0x73, 0x8e, 0x00, 0x71, 0xce, 0x38, 0x01, 0xc7, 0x38, 0xe0, 0x07, 0x1c, 
0xe6, 0xe0, 0x07, 0x1c, 0xe6, 0xe0, 0x07, 0x1c, 0xe6, 0xe0, 0x07, 0x1c, 0xe6, 0xe0, 0x07, 0x1c, 
0xe6, 0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x81, 0xc7, 0x31, 0xc0, 0x40, 0xe3, 
0x98, 0xe0, 0x20, 0x71, 0xcc, 0x70, 0x10, 0x38, 0xe6, 0x38, 0x08, 0x1c, 0x73, 0x0e, 0x20, 0xe0, 
0x83, 0x88, 0x38, 0x20, 0xe2, 0x0e, 0x08, 0x38, 0x83, 0x82, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x04, 0xe0, 0x71, 0xc1, 0xc3, 0x83, 0x87, 0x0e, 0x1c, 0x1c, 0x38, 0x70, 0xe0, 0xe1, 0xce, 0x00, 
0x03, 0x1c, 0x38, 0x0e, 0x30, 0xe0, 0xe0, 0x38, 0xc3, 0x83, 0x80, 0xe3, 0x0e, 0x0e, 0x03, 0x8c, 
0x38, 0x38, 0x0e, 0x30, 0xe0, 0xe6, 0x03, 0x1c, 0x39, 0x80, 0xc7, 0x0e, 0x60, 0x31, 0xc3, 0x98, 
0x0c, 0x71, 0xcc, 0x03, 0x38, 0x73, 0x01, 0xc1, 0xc0, 0x00, 0x71, 0xc1, 0xc0, 0xe0, 0xe3, 0x83, 
0x81, 0xc1, 0xc7, 0x07, 0x03, 0x83, 0x8e, 0x0e, 0x07, 0x07, 0x1c, 0x1c, 0x0e, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1d, 0x80, 0x1c, 0x20, 0x76, 0x00, 0x70, 0x81, 0xd8, 0x01, 0xc2, 0x07, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x13, 0x84, 0x70, 0x71, 0xc7, 0x04, 0xe1, 
0x1c, 0x1c, 0x71, 0xc1, 0x38, 0x47, 0x07, 0x1c, 0x70, 0x47, 0x90, 0x71, 0xc1, 0x1e, 0x41, 0xc7, 
0x04, 0x79, 0x07, 0x1c, 0x11, 0xe4, 0x1c, 0x71, 0xc0, 0x0e, 0x30, 0xc7, 0x00, 0x38, 0xc3, 0x1c, 
0x00, 0xe3, 0x0c, 0x70, 0x03, 0x8c, 0x30, 0x70, 0xe1, 0xc3, 0x07, 0x0e, 0x1c, 0x30, 0x70, 0x70, 
0x71, 0x0e, 0x0e, 0x0e, 0x21, 0xc1, 0xc1, 0xc4, 0x38, 0x38, 0x38, 0x8f, 0xe0, 0xc4, 0x3f, 0x83, 
0x10, 0xfe, 0x0c, 0x43, 0xf8, 0x31, 0x0f, 0xe0, 0xc4, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x83, 0x8e, 0x0e, 0x02, 0x0e, 0x38, 
0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x81, 0xe2, 0x07, 0x10, 0x3c, 0x40, 0xe2, 0x07, 0x17, 
0x10, 0x38, 0xe2, 0x07, 0x17, 0x10, 0x38, 0xe2, 0x07, 0x17, 0x10, 0x38, 0xe2, 0x07, 0x17, 0x10, 
0x38, 0xe2, 0x07, 0x17, 0x10, 0x38, 0xe2, 0x03, 0xc0, 0x3a, 0x00, 0xf0, 0x0e, 0x80, 0x3a, 0x07, 
0x88, 0x0f, 0x02, 0x70, 0x1e, 0x04, 0xe0, 0x3c, 0x09, 0xc3, 0x8e, 0x1c, 0x1c, 0x71, 0x1e, 0x23, 
0x8e, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc7, 0xc0, 0x40, 0x08, 0x04, 0xe0, 0x71, 0xc0, 0x4e, 
0x07, 0x1c, 0x04, 0xe0, 0x71, 0xc0, 0x4e, 0x07, 0x1c, 0x04, 0xe0, 0x71, 0xc0, 0x4e, 0x07, 0x1c, 
0x04, 0xe0, 0x71, 0xc0, 0x4e, 0x07, 0x1c, 0x04, 0xe0, 0x71, 0xc0, 0x4e, 0x07, 0x1c, 0x04, 0xe0, 
0x71, 0xc0, 0x4e, 0x07, 0x1c, 0x1c, 0xc0, 0x63, 0x87, 0x30, 0x18, 0xe1, 0xcc, 0x06, 0x38, 0x73, 
0x01, 0x8e, 0x1c, 0xc0, 0x63, 0x87, 0x30, 0x18, 0xe1, 0xcc, 0x06, 0x38, 0x73, 0x01, 0x8e, 0x1c, 
0x1c, 0x38, 0x73, 0x80, 0x1c, 0x61, 0x8e, 0x00, 0x71, 0x86, 0x38, 0x01, 0xc6, 0x18, 0xe0, 0x07, 
0x18, 0x63, 0x80, 0x1c, 0x61, 0x8e, 0x00, 0x71, 0x86, 0x38, 0x01, 0xc6, 0x18, 0xe0, 0x07, 0x18, 
0x7c, 0xe0, 0x07, 0x18, 0x7c, 0xe0, 0x07, 0x18, 0x7c, 0xe0, 0x07, 0x18, 0x7c, 0xe0, 0x07, 0x18, 
0x7c, 0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x81, 0xc7, 0x11, 0xc0, 0x40, 0xe3, 
0x88, 0xe0, 0x20, 0x71, 0xc4, 0x70, 0x10, 0x38, 0xe2, 0x38, 0x08, 0x1c, 0x71, 0x07, 0x40, 0xf1, 
0x01, 0xd0, 0x3c, 0x40, 0x74, 0x0f, 0x10, 0x1d, 0x03, 0xc4, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x08, 0xf0, 0x71, 0xc1, 0xff, 0x03, 0x87, 0x0f, 0xf8, 0x1c, 0x38, 0x7f, 0xc0, 0xe1, 0xce, 0x00, 
0x07, 0x00, 0x38, 0x0e, 0x70, 0xe0, 0xe0, 0x39, 0xc3, 0x83, 0x80, 0xe7, 0x0e, 0x0e, 0x03, 0x9c, 
0x38, 0x38, 0x0e, 0x70, 0xe0, 0xfe, 0x07, 0x1c, 0x3f, 0x81, 0xc7, 0x0f, 0xe0, 0x71, 0xc3, 0xf8, 
0x1c, 0x71, 0xfc, 0x07, 0x38, 0x7f, 0x01, 0xc1, 0xc0, 0x00, 0x71, 0xc1, 0xff, 0xe0, 0xe3, 0x83, 
0xff, 0xc1, 0xc7, 0x07, 0xff, 0x83, 0x8e, 0x0f, 0xff, 0x07, 0x1c, 0x1f, 0xfe, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1f, 0xc0, 0x1c, 0x40, 0x7f, 0x00, 0x71, 0x01, 0xfc, 0x01, 0xc4, 0x07, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x11, 0xc4, 0x70, 0x71, 0xc7, 0x04, 0x71, 
0x1c, 0x1c, 0x71, 0xc1, 0x1c, 0x47, 0x07, 0x1c, 0x70, 0x43, 0xd0, 0x71, 0xc1, 0x0f, 0x41, 0xc7, 
0x04, 0x3d, 0x07, 0x1c, 0x10, 0xf4, 0x1c, 0x71, 0xc0, 0x0e, 0x70, 0xe7, 0x00, 0x39, 0xc3, 0x9c, 
0x00, 0xe7, 0x0e, 0x70, 0x03, 0x9c, 0x38, 0x7f, 0x81, 0xc3, 0x87, 0xf8, 0x1c, 0x38, 0x7f, 0xe0, 
0x70, 0x0f, 0xfc, 0x0e, 0x01, 0xff, 0x81, 0xc0, 0x3f, 0xf0, 0x38, 0x07, 0xf0, 0xe0, 0x1f, 0xc3, 
0x80, 0x7f, 0x0e, 0x01, 0xfc, 0x38, 0x07, 0xf0, 0xe0, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x83, 0x8e, 0x0e, 0x02, 0x0e, 0x38, 
0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x80, 0xe2, 0x07, 0x10, 0x1c, 0x40, 0xe2, 0x07, 0x17, 
0x10, 0x39, 0xe2, 0x07, 0x17, 0x10, 0x39, 0xe2, 0x07, 0x17, 0x10, 0x39, 0xe2, 0x07, 0x17, 0x10, 
0x39, 0xe2, 0x07, 0x17, 0x10, 0x39, 0xe2, 0x01, 0xc0, 0x1e, 0x00, 0x70, 0x07, 0x80, 0x3a, 0x03, 
0x88, 0x0e, 0x00, 0x70, 0x1c, 0x00, 0xe0, 0x38, 0x01, 0xc3, 0x8e, 0x1c, 0x1c, 0xf1, 0x0e, 0x23, 
0x8e, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc1, 0xe0, 0x40, 0x08, 0x08, 0xf0, 0x71, 0xc0, 0x8f, 
0x07, 0x1c, 0x08, 0xf0, 0x71, 0xc0, 0x8f, 0x07, 0x1c, 0x08, 0xf0, 0x71, 0xc0, 0x8f, 0x07, 0x1c, 
0x08, 0xf0, 0x71, 0xc0, 0x8f, 0x07, 0x1c, 0x08, 0xf0, 0x71, 0xc0, 0x8f, 0x07, 0x1c, 0x08, 0xf0, 
0x71, 0xc0, 0x8f, 0x07, 0x1c, 0x1f, 0xc0, 0xe3, 0x87, 0xf0, 0x38, 0xe1, 0xfc, 0x0e, 0x38, 0x7f, 
0x03, 0x8e, 0x1f, 0xc0, 0xe3, 0x87, 0xf0, 0x38, 0xe1, 0xfc, 0x0e, 0x38, 0x7f, 0x03, 0x8e, 0x1c, 
0x1c, 0x38, 0x73, 0x80, 0x1c, 0xe1, 0xce, 0x00, 0x73, 0x87, 0x38, 0x01, 0xce, 0x1c, 0xe0, 0x07, 
0x38, 0x73, 0x80, 0x1c, 0xe1, 0xce, 0x00, 0x73, 0x87, 0x38, 0x01, 0xce, 0x1c, 0xe0, 0x07, 0x38, 
0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 
0x70, 0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x81, 0xc7, 0xe1, 0xc0, 0x40, 0xe3, 
0xf0, 0xe0, 0x20, 0x71, 0xf8, 0x70, 0x10, 0x38, 0xfc, 0x38, 0x08, 0x1c, 0x7e, 0x07, 0x40, 0x71, 
0x01, 0xd0, 0x1c, 0x40, 0x74, 0x07, 0x10, 0x1d, 0x01, 0xc4, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x08, 0x70, 0x03, 0xc1, 0xc1, 0xc3, 0x87, 0x0e, 0x0e, 0x1c, 0x38, 0x70, 0x70, 0xe1, 0xce, 0x00, 
0x07, 0x00, 0x38, 0x0e, 0x70, 0xe0, 0xe0, 0x39, 0xc3, 0x83, 0x80, 0xe7, 0x0e, 0x0e, 0x03, 0x9c, 
0x38, 0x38, 0x0e, 0x70, 0xe0, 0xe6, 0x07, 0xfc, 0x39, 0x81, 0xff, 0x0e, 0x60, 0x7f, 0xc3, 0x98, 
0x1f, 0xf1, 0xcc, 0x07, 0xf8, 0x73, 0x01, 0xc1, 0xc0, 0x00, 0x71, 0xc1, 0xc0, 0xe0, 0xe3, 0x83, 
0x81, 0xc1, 0xc7, 0x07, 0x03, 0x83, 0x8e, 0x0e, 0x07, 0x07, 0x1c, 0x1c, 0x0e, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1d, 0xe0, 0x1d, 0x80, 0x77, 0x80, 0x76, 0x01, 0xde, 0x01, 0xd8, 0x07, 0x00, 
0x38, 0x70, 0x01, 0xc1, 0xc0, 0x07, 0x07, 0x00, 0x1c, 0x11, 0xc8, 0x70, 0x71, 0xc7, 0x04, 0x72, 
0x1c, 0x1c, 0x71, 0xc1, 0x1c, 0x87, 0x07, 0x1c, 0x70, 0x41, 0xd0, 0x71, 0xc1, 0x07, 0x41, 0xc7, 
0x04, 0x1d, 0x07, 0x1c, 0x10, 0x74, 0x1c, 0x71, 0xc0, 0x0e, 0x70, 0xe7, 0x00, 0x39, 0xc3, 0x9c, 
0x00, 0xe7, 0x0e, 0x70, 0x03, 0x9c, 0x38, 0x70, 0x01, 0xc3, 0x87, 0x00, 0x1c, 0x38, 0x73, 0x80, 
0x70, 0x0e, 0x70, 0x0e, 0x01, 0xce, 0x01, 0xc0, 0x39, 0xc0, 0x38, 0x01, 0xf8, 0xf8, 0x07, 0xe3, 
0xe0, 0x1f, 0x8f, 0x80, 0x7e, 0x3e, 0x01, 0xf8, 0xf8, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x83, 0x8e, 0x0e, 0x02, 0x0e, 0x38, 
0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x80, 0xe4, 0x07, 0x90, 0x1c, 0x80, 0xf2, 0x07, 0xa3, 
0xa0, 0x1d, 0x74, 0x07, 0xa3, 0xa0, 0x1d, 0x74, 0x07, 0xa3, 0xa0, 0x1d, 0x74, 0x07, 0xa3, 0xa0, 
0x1d, 0x74, 0x07, 0xa3, 0xa0, 0x1d, 0x74, 0x01, 0xe0, 0x1c, 0x00, 0x78, 0x07, 0x00, 0x1c, 0x03, 
0xc8, 0x1e, 0x00, 0xe0, 0x3c, 0x01, 0xc0, 0x78, 0x03, 0x83, 0x8e, 0x1c, 0x0e, 0xba, 0x0f, 0x20, 
0x1e, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc0, 0xf0, 0x40, 0x08, 0x08, 0x70, 0x03, 0xc0, 0x87, 
0x00, 0x3c, 0x08, 0x70, 0x03, 0xc0, 0x87, 0x00, 0x3c, 0x08, 0x70, 0x03, 0xc0, 0x87, 0x00, 0x3c, 
0x08, 0x70, 0x03, 0xc0, 0x87, 0x00, 0x3c, 0x08, 0x70, 0x03, 0xc0, 0x87, 0x00, 0x3c, 0x08, 0x70, 
0x03, 0xc0, 0x87, 0x00, 0x3c, 0x1c, 0xc0, 0xff, 0x87, 0x30, 0x3f, 0xe1, 0xcc, 0x0f, 0xf8, 0x73, 
0x03, 0xfe, 0x1c, 0xc0, 0xff, 0x87, 0x30, 0x3f, 0xe1, 0xcc, 0x0f, 0xf8, 0x73, 0x03, 0xfe, 0x1c, 
0x1c, 0x38, 0x73, 0x80, 0x1c, 0xe1, 0xce, 0x00, 0x73, 0x87, 0x38, 0x01, 0xce, 0x1c, 0xe0, 0x07, 
0x38, 0x73, 0x80, 0x1c, 0xe1, 0xce, 0x00, 0x73, 0x87, 0x38, 0x01, 0xce, 0x1c, 0xe0, 0x07, 0x38, 
0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 
0x70, 0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x81, 0xc7, 0x01, 0xc0, 0x40, 0xe3, 
0x80, 0xe0, 0x20, 0x71, 0xc0, 0x70, 0x10, 0x38, 0xe0, 0x38, 0x08, 0x1c, 0x70, 0x03, 0x80, 0x79, 
0x00, 0xe0, 0x1e, 0x40, 0x38, 0x07, 0x90, 0x0e, 0x01, 0xe4, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x08, 0x70, 0x0d, 0xc1, 0xc1, 0xe3, 0x87, 0x0e, 0x0f, 0x1c, 0x38, 0x70, 0x78, 0xe1, 0xcf, 0x00, 
0x07, 0x00, 0x38, 0x1e, 0x70, 0xe0, 0xe0, 0x79, 0xc3, 0x83, 0x81, 0xe7, 0x0e, 0x0e, 0x07, 0x9c, 
0x38, 0x38, 0x1e, 0x70, 0xe0, 0xe6, 0x07, 0x00, 0x39, 0x81, 0xc0, 0x0e, 0x60, 0x70, 0x03, 0x98, 
0x1c, 0x01, 0xc4, 0x07, 0x00, 0x73, 0x01, 0xc1, 0xe0, 0x7f, 0x71, 0xc1, 0xc0, 0xe0, 0xe3, 0x83, 
0x81, 0xc1, 0xc7, 0x07, 0x03, 0x83, 0x8e, 0x0e, 0x07, 0x07, 0x1c, 0x1c, 0x0e, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1c, 0xf0, 0x1f, 0xc0, 0x73, 0xc0, 0x7f, 0x01, 0xcf, 0x01, 0xfc, 0x07, 0x00, 
0xb8, 0x70, 0x09, 0xc1, 0xc0, 0x27, 0x07, 0x00, 0x9c, 0x10, 0xe8, 0x70, 0x71, 0xc7, 0x04, 0x3a, 
0x1c, 0x1c, 0x71, 0xc1, 0x0e, 0x87, 0x07, 0x1c, 0x70, 0x40, 0xf0, 0x71, 0xc1, 0x03, 0xc1, 0xc7, 
0x04, 0x0f, 0x07, 0x1c, 0x10, 0x3c, 0x1c, 0x71, 0xc0, 0x0e, 0x70, 0xe7, 0x00, 0x39, 0xc3, 0x9c, 
0x00, 0xe7, 0x0e, 0x70, 0x03, 0x9c, 0x38, 0x70, 0x01, 0xc3, 0x87, 0x00, 0x1c, 0x38, 0x73, 0xc0, 
0x70, 0x0e, 0x78, 0x0e, 0x01, 0xcf, 0x01, 0xc0, 0x39, 0xe0, 0x38, 0x10, 0x7c, 0x7c, 0x41, 0xf1, 
0xf1, 0x07, 0xc7, 0xc4, 0x1f, 0x1f, 0x10, 0x7c, 0x7c, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x83, 0x8e, 0x0e, 0x02, 0x0e, 0x38, 
0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x80, 0x74, 0x03, 0xa0, 0x0e, 0x80, 0x74, 0x03, 0xa3, 
0xa0, 0x1d, 0x74, 0x03, 0xa3, 0xa0, 0x1d, 0x74, 0x03, 0xa3, 0xa0, 0x1d, 0x74, 0x03, 0xa3, 0xa0, 
0x1d, 0x74, 0x03, 0xa3, 0xa0, 0x1d, 0x74, 0x02, 0xe0, 0x0e, 0x00, 0xb8, 0x03, 0x80, 0x1c, 0x01, 
0xd0, 0x3c, 0x00, 0xe0, 0x78, 0x01, 0xc0, 0xf0, 0x03, 0x83, 0x8e, 0x1c, 0x0e, 0xba, 0x07, 0x40, 
0x6e, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc0, 0x70, 0x40, 0x08, 0x08, 0x70, 0x0d, 0xc0, 0x87, 
0x00, 0xdc, 0x08, 0x70, 0x0d, 0xc0, 0x87, 0x00, 0xdc, 0x08, 0x70, 0x0d, 0xc0, 0x87, 0x00, 0xdc, 
0x08, 0x70, 0x0d, 0xc0, 0x87, 0x00, 0xdc, 0x08, 0x70, 0x0d, 0xc0, 0x87, 0x00, 0xdc, 0x08, 0x70, 
0x0d, 0xc0, 0x87, 0x00, 0xdc, 0x1c, 0xc0, 0xe0, 0x07, 0x30, 0x38, 0x01, 0xcc, 0x0e, 0x00, 0x73, 
0x03, 0x80, 0x1c, 0xc0, 0xe0, 0x07, 0x30, 0x38, 0x01, 0xcc, 0x0e, 0x00, 0x73, 0x03, 0x80, 0x1c, 
0x1c, 0x38, 0x73, 0x80, 0x1c, 0xe1, 0xce, 0x00, 0x73, 0x87, 0x38, 0x01, 0xce, 0x1c, 0xe0, 0x07, 
0x38, 0x73, 0x80, 0x1c, 0xe1, 0xce, 0x00, 0x73, 0x87, 0x38, 0x01, 0xce, 0x1c, 0xe0, 0x07, 0x38, 
0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 0x70, 0xe0, 0x07, 0x38, 
0x70, 0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x81, 0xc7, 0x01, 0xc0, 0x40, 0xe3, 
0x80, 0xe0, 0x20, 0x71, 0xc0, 0x70, 0x10, 0x38, 0xe0, 0x38, 0x08, 0x1c, 0x70, 0x03, 0x80, 0x3a, 
0x00, 0xe0, 0x0e, 0x80, 0x38, 0x03, 0xa0, 0x0e, 0x00, 0xe8, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x1f, 0xf8, 0x39, 0xc1, 0xc0, 0xe3, 0x87, 0x0e, 0x07, 0x1c, 0x38, 0x70, 0x38, 0xe1, 0xc7, 0x00, 
0x07, 0x00, 0x38, 0x1c, 0x70, 0xe0, 0xe0, 0x71, 0xc3, 0x83, 0x81, 0xc7, 0x0e, 0x0e, 0x07, 0x1c, 
0x38, 0x38, 0x1c, 0x70, 0xe0, 0xe2, 0x07, 0x00, 0x38, 0x81, 0xc0, 0x0e, 0x20, 0x70, 0x03, 0x88, 
0x1c, 0x01, 0xc4, 0x27, 0x00, 0x71, 0x01, 0xc0, 0xe0, 0x1c, 0x3b, 0x81, 0xc0, 0xe0, 0xe3, 0x83, 
0x81, 0xc1, 0xc7, 0x07, 0x03, 0x83, 0x8e, 0x0e, 0x07, 0x07, 0x1c, 0x1c, 0x0e, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1c, 0x70, 0x1d, 0xe0, 0x71, 0xc0, 0x77, 0x81, 0xc7, 0x01, 0xde, 0x07, 0x00, 
0xb8, 0x70, 0x09, 0xc1, 0xc0, 0x27, 0x07, 0x00, 0x9c, 0x10, 0xe8, 0x70, 0x71, 0xc7, 0x04, 0x3a, 
0x1c, 0x1c, 0x71, 0xc1, 0x0e, 0x87, 0x07, 0x1c, 0x70, 0x40, 0xf0, 0x71, 0xc1, 0x03, 0xc1, 0xc7, 
0x04, 0x0f, 0x07, 0x1c, 0x10, 0x3c, 0x1c, 0x70, 0xe0, 0x1c, 0x70, 0xe3, 0x80, 0x71, 0xc3, 0x8e, 
0x01, 0xc7, 0x0e, 0x38, 0x07, 0x1c, 0x38, 0x70, 0x01, 0xc3, 0x87, 0x00, 0x1c, 0x38, 0x71, 0xe0, 
0x70, 0x0e, 0x3c, 0x0e, 0x01, 0xc7, 0x81, 0xc0, 0x38, 0xf0, 0x38, 0x10, 0x3c, 0x1e, 0x40, 0xf0, 
0x79, 0x03, 0xc1, 0xe4, 0x0f, 0x07, 0x90, 0x3c, 0x1e, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x83, 0x8e, 0x0e, 0x02, 0x0e, 0x38, 
0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x80, 0x74, 0x03, 0xa0, 0x0e, 0x80, 0x74, 0x03, 0xa3, 
0xc0, 0x1e, 0x74, 0x03, 0xa3, 0xc0, 0x1e, 0x74, 0x03, 0xa3, 0xc0, 0x1e, 0x74, 0x03, 0xa3, 0xc0, 
0x1e, 0x74, 0x03, 0xa3, 0xc0, 0x1e, 0x74, 0x06, 0x70, 0x0e, 0x01, 0x9c, 0x03, 0x80, 0x1c, 0x01, 
0xf0, 0x38, 0x09, 0xc0, 0x70, 0x13, 0x80, 0xe0, 0x27, 0x03, 0x8e, 0x1c, 0x0f, 0x3a, 0x07, 0xc1, 
0xce, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc0, 0x70, 0x40, 0x08, 0x1f, 0xf8, 0x39, 0xc1, 0xff, 
0x83, 0x9c, 0x1f, 0xf8, 0x39, 0xc1, 0xff, 0x83, 0x9c, 0x1f, 0xf8, 0x39, 0xc1, 0xff, 0x83, 0x9c, 
0x1f, 0xf8, 0x39, 0xc1, 0xff, 0x83, 0x9c, 0x1f, 0xf8, 0x39, 0xc1, 0xff, 0x83, 0x9c, 0x1f, 0xf8, 
0x39, 0xc1, 0xff, 0x83, 0x9c, 0x1c, 0x40, 0xe0, 0x07, 0x10, 0x38, 0x01, 0xc4, 0x0e, 0x00, 0x71, 
0x03, 0x80, 0x1c, 0x40, 0xe0, 0x07, 0x10, 0x38, 0x01, 0xc4, 0x0e, 0x00, 0x71, 0x03, 0x80, 0x1c, 
0x1c, 0x38, 0x71, 0xc0, 0x38, 0xe1, 0xc7, 0x00, 0xe3, 0x87, 0x1c, 0x03, 0x8e, 0x1c, 0x70, 0x0e, 
0x38, 0x71, 0xc0, 0x38, 0xe1, 0xc7, 0x00, 0xe3, 0x87, 0x1c, 0x03, 0x8e, 0x1c, 0x70, 0x0e, 0x38, 
0x70, 0x70, 0x0e, 0x38, 0x70, 0x70, 0x0e, 0x38, 0x70, 0x70, 0x0e, 0x38, 0x70, 0x70, 0x0e, 0x38, 
0x70, 0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x81, 0xc7, 0x01, 0xc0, 0x40, 0xe3, 
0x80, 0xe0, 0x20, 0x71, 0xc0, 0x70, 0x10, 0x38, 0xe0, 0x38, 0x08, 0x1c, 0x70, 0x03, 0x80, 0x3e, 
0x00, 0xe0, 0x0f, 0x80, 0x38, 0x03, 0xe0, 0x0e, 0x00, 0xf8, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x10, 0x38, 0x71, 0xc1, 0xc0, 0xe3, 0x87, 0x0e, 0x07, 0x1c, 0x38, 0x70, 0x38, 0xe1, 0xc7, 0x00, 
0x07, 0x00, 0x38, 0x1c, 0x70, 0xe0, 0xe0, 0x71, 0xc3, 0x83, 0x81, 0xc7, 0x0e, 0x0e, 0x07, 0x1c, 
0x38, 0x38, 0x1c, 0x70, 0xe0, 0xe2, 0x27, 0x00, 0x38, 0x89, 0xc0, 0x0e, 0x22, 0x70, 0x03, 0x88, 
0x9c, 0x01, 0xc4, 0x27, 0x00, 0x71, 0x01, 0xc0, 0xe0, 0x1c, 0x1e, 0x01, 0xc0, 0xe0, 0xe3, 0x83, 
0x81, 0xc1, 0xc7, 0x07, 0x03, 0x83, 0x8e, 0x0e, 0x07, 0x07, 0x1c, 0x1c, 0x0e, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1c, 0x38, 0x1c, 0xe0, 0x70, 0xe0, 0x73, 0x81, 0xc3, 0x81, 0xce, 0x07, 0x01, 
0xb8, 0x70, 0x19, 0xc1, 0xc0, 0x67, 0x07, 0x01, 0x9c, 0x10, 0xf0, 0x70, 0x71, 0xc7, 0x04, 0x3c, 
0x1c, 0x1c, 0x71, 0xc1, 0x0f, 0x07, 0x07, 0x1c, 0x70, 0x40, 0x70, 0x71, 0xc1, 0x01, 0xc1, 0xc7, 
0x04, 0x07, 0x07, 0x1c, 0x10, 0x1c, 0x1c, 0x70, 0xe0, 0x1c, 0x70, 0xe3, 0x80, 0x71, 0xc3, 0x8e, 
0x01, 0xc7, 0x0e, 0x38, 0x07, 0x1c, 0x38, 0x70, 0x01, 0xc3, 0x87, 0x00, 0x1c, 0x38, 0x70, 0xe0, 
0x70, 0x0e, 0x1c, 0x0e, 0x01, 0xc3, 0x81, 0xc0, 0x38, 0x70, 0x38, 0x18, 0x1c, 0x0e, 0x60, 0x70, 
0x39, 0x81, 0xc0, 0xe6, 0x07, 0x03, 0x98, 0x1c, 0x0e, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x83, 0x8e, 0x0e, 0x02, 0x0e, 0x38, 
0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x80, 0x78, 0x03, 0xc0, 0x0f, 0x00, 0x78, 0x01, 0xc1, 
0xc0, 0x0e, 0x38, 0x01, 0xc1, 0xc0, 0x0e, 0x38, 0x01, 0xc1, 0xc0, 0x0e, 0x38, 0x01, 0xc1, 0xc0, 
0x0e, 0x38, 0x01, 0xc1, 0xc0, 0x0e, 0x38, 0x04, 0x78, 0x17, 0x01, 0x1e, 0x05, 0xc0, 0x1c, 0x00, 
0xe0, 0x78, 0x19, 0xc8, 0xf0, 0x33, 0x91, 0xe0, 0x67, 0x23, 0x8e, 0x1c, 0x07, 0x1c, 0x03, 0x83, 
0x8e, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc0, 0x70, 0x40, 0x08, 0x10, 0x38, 0x71, 0xc1, 0x03, 
0x87, 0x1c, 0x10, 0x38, 0x71, 0xc1, 0x03, 0x87, 0x1c, 0x10, 0x38, 0x71, 0xc1, 0x03, 0x87, 0x1c, 
0x10, 0x38, 0x71, 0xc1, 0x03, 0x87, 0x1c, 0x10, 0x38, 0x71, 0xc1, 0x03, 0x87, 0x1c, 0x10, 0x38, 
0x71, 0xc1, 0x03, 0x87, 0x1c, 0x1c, 0x44, 0xe0, 0x07, 0x11, 0x38, 0x01, 0xc4, 0x4e, 0x00, 0x71, 
0x13, 0x80, 0x1c, 0x44, 0xe0, 0x07, 0x11, 0x38, 0x01, 0xc4, 0x4e, 0x00, 0x71, 0x13, 0x80, 0x1c, 
0x1c, 0x38, 0x71, 0xc0, 0x38, 0xe1, 0xc7, 0x00, 0xe3, 0x87, 0x1c, 0x03, 0x8e, 0x1c, 0x70, 0x0e, 
0x38, 0x71, 0xc0, 0x38, 0xe1, 0xc7, 0x00, 0xe3, 0x87, 0x1c, 0x03, 0x8e, 0x1c, 0x70, 0x0e, 0x38, 
0x70, 0x70, 0x0e, 0x38, 0x70, 0x70, 0x0e, 0x38, 0x70, 0x70, 0x0e, 0x38, 0x70, 0x70, 0x0e, 0x38, 
0x70, 0x38, 0x08, 0x38, 0xe0, 0xe0, 0x20, 0xe3, 0x83, 0x80, 0x81, 0xc7, 0x01, 0xc0, 0x40, 0xe3, 
0x80, 0xe0, 0x20, 0x71, 0xc0, 0x70, 0x10, 0x38, 0xe0, 0x38, 0x08, 0x1c, 0x70, 0x03, 0x80, 0x1c, 
0x00, 0xe0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x0e, 0x00, 0x70, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x20, 0x1c, 0x73, 0xc1, 0xc1, 0xe3, 0x86, 0x0e, 0x0f, 0x1c, 0x30, 0x70, 0x78, 0xe1, 0x83, 0x80, 
0x43, 0x84, 0x38, 0x38, 0x30, 0xe0, 0xe0, 0xe0, 0xc3, 0x83, 0x83, 0x83, 0x0e, 0x0e, 0x0e, 0x0c, 
0x38, 0x38, 0x38, 0x30, 0xe0, 0xe0, 0x63, 0x84, 0x38, 0x18, 0xe1, 0x0e, 0x06, 0x38, 0x43, 0x81, 
0x8e, 0x11, 0xc0, 0x63, 0x88, 0x70, 0x01, 0xc0, 0x70, 0x1c, 0x20, 0x01, 0xc0, 0xe0, 0xe3, 0x83, 
0x81, 0xc1, 0xc7, 0x07, 0x03, 0x83, 0x8e, 0x0e, 0x07, 0x07, 0x1c, 0x1c, 0x0e, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1c, 0x1c, 0x1c, 0x70, 0x70, 0x70, 0x71, 0xc1, 0xc1, 0xc1, 0xc7, 0x07, 0x01, 
0x38, 0x70, 0x11, 0xc1, 0xc0, 0x47, 0x07, 0x01, 0x1c, 0x10, 0x70, 0x70, 0x71, 0xc7, 0x04, 0x1c, 
0x1c, 0x1c, 0x71, 0xc1, 0x07, 0x07, 0x07, 0x1c, 0x70, 0x40, 0x30, 0x71, 0xc1, 0x00, 0xc1, 0xc7, 
0x04, 0x03, 0x07, 0x1c, 0x10, 0x0c, 0x1c, 0x70, 0x70, 0x38, 0x30, 0xc1, 0xc0, 0xe0, 0xc3, 0x07, 
0x03, 0x83, 0x0c, 0x1c, 0x0e, 0x0c, 0x30, 0x70, 0x01, 0xc3, 0x07, 0x00, 0x1c, 0x30, 0x70, 0x70, 
0x70, 0x0e, 0x0e, 0x0e, 0x01, 0xc1, 0xc1, 0xc0, 0x38, 0x38, 0x38, 0x1c, 0x1c, 0x86, 0x70, 0x72, 
0x19, 0xc1, 0xc8, 0x67, 0x07, 0x21, 0x9c, 0x1c, 0x86, 0x03, 0x80, 0x70, 0x07, 0x00, 0xe0, 0x0e, 
0x01, 0xc0, 0x1c, 0x03, 0x80, 0x60, 0x40, 0xe3, 0x81, 0x81, 0x03, 0x8e, 0x06, 0x04, 0x0e, 0x38, 
0x18, 0x10, 0x38, 0xe0, 0x60, 0x40, 0xe3, 0x80, 0x38, 0x01, 0xc0, 0x07, 0x00, 0x38, 0x01, 0xc1, 
0xc0, 0x0e, 0x38, 0x01, 0xc1, 0xc0, 0x0e, 0x38, 0x01, 0xc1, 0xc0, 0x0e, 0x38, 0x01, 0xc1, 0xc0, 
0x0e, 0x38, 0x01, 0xc1, 0xc0, 0x0e, 0x38, 0x08, 0x38, 0x17, 0x02, 0x0e, 0x05, 0xc0, 0x1c, 0x00, 
0xe0, 0x70, 0x1b, 0x88, 0xe0, 0x37, 0x11, 0xc0, 0x6e, 0x23, 0x8e, 0x1c, 0x07, 0x1c, 0x03, 0x83, 
0x9e, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc0, 0x70, 0x40, 0x08, 0x20, 0x1c, 0x73, 0xc2, 0x01, 
0xc7, 0x3c, 0x20, 0x1c, 0x73, 0xc2, 0x01, 0xc7, 0x3c, 0x20, 0x1c, 0x73, 0xc2, 0x01, 0xc7, 0x3c, 
0x20, 0x1c, 0x73, 0xc2, 0x01, 0xc7, 0x3c, 0x20, 0x1c, 0x73, 0xc2, 0x01, 0xc7, 0x3c, 0x20, 0x1c, 
0x73, 0xc2, 0x01, 0xc7, 0x3c, 0x1c, 0x0c, 0x70, 0x87, 0x03, 0x1c, 0x21, 0xc0, 0xc7, 0x08, 0x70, 
0x31, 0xc2, 0x1c, 0x0c, 0x70, 0x87, 0x03, 0x1c, 0x21, 0xc0, 0xc7, 0x08, 0x70, 0x31, 0xc2, 0x1c, 
0x1c, 0x38, 0x70, 0xe0, 0x70, 0x61, 0x83, 0x81, 0xc1, 0x86, 0x0e, 0x07, 0x06, 0x18, 0x38, 0x1c, 
0x18, 0x60, 0xe0, 0x70, 0x61, 0x83, 0x81, 0xc1, 0x86, 0x0e, 0x07, 0x06, 0x18, 0x38, 0x1c, 0x18, 
0x60, 0x38, 0x1c, 0x18, 0x60, 0x38, 0x1c, 0x18, 0x60, 0x38, 0x1c, 0x18, 0x60, 0x38, 0x1c, 0x18, 
0x60, 0x18, 0x10, 0x38, 0xe0, 0x60, 0x40, 0xe3, 0x81, 0x81, 0x01, 0xc7, 0x00, 0xc0, 0x80, 0xe3, 
0x80, 0x60, 0x40, 0x71, 0xc0, 0x30, 0x20, 0x38, 0xe0, 0x18, 0x10, 0x1c, 0x70, 0x03, 0x80, 0x1c, 
0x00, 0xe0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x0e, 0x00, 0x70, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0x60, 0x1e, 0x7d, 0xc1, 0xc3, 0xc3, 0xcc, 0x0e, 0x1e, 0x1e, 0x60, 0x70, 0xf0, 0xf3, 0x01, 0xc1, 
0x83, 0xcc, 0x38, 0x70, 0x39, 0xf0, 0xe1, 0xc0, 0xe7, 0xc3, 0x87, 0x03, 0x9f, 0x0e, 0x1c, 0x0e, 
0x7c, 0x38, 0x70, 0x39, 0xf0, 0xe0, 0xc3, 0xc8, 0x38, 0x30, 0xf2, 0x0e, 0x0c, 0x3c, 0x83, 0x83, 
0x0f, 0x21, 0xc0, 0xc3, 0x90, 0x70, 0x01, 0xc0, 0x38, 0x1c, 0x60, 0x01, 0xc0, 0xe0, 0xe3, 0x83, 
0x81, 0xc1, 0xc7, 0x07, 0x03, 0x83, 0x8e, 0x0e, 0x07, 0x07, 0x1c, 0x1c, 0x0e, 0x0e, 0x38, 0x38, 
0x38, 0x1c, 0x1c, 0x1c, 0x1f, 0x1c, 0x78, 0x70, 0x7c, 0x71, 0xe1, 0xc1, 0xf1, 0xc7, 0x87, 0x07, 
0x38, 0x70, 0x71, 0xc1, 0xc1, 0xc7, 0x07, 0x07, 0x1c, 0x10, 0x60, 0x70, 0x71, 0xc7, 0x04, 0x18, 
0x1c, 0x1c, 0x71, 0xc1, 0x06, 0x07, 0x07, 0x1c, 0x70, 0x40, 0x30, 0x71, 0xc1, 0x00, 0xc1, 0xc7, 
0x04, 0x03, 0x07, 0x1c, 0x10, 0x0c, 0x1c, 0x70, 0x38, 0x70, 0x39, 0xc0, 0xe1, 0xc0, 0xe7, 0x03, 
0x87, 0x03, 0x9c, 0x0e, 0x1c, 0x0e, 0x70, 0x70, 0x01, 0xe7, 0x07, 0x00, 0x1e, 0x70, 0x70, 0x78, 
0x70, 0x0e, 0x0f, 0x0e, 0x01, 0xc1, 0xe1, 0xc0, 0x38, 0x3c, 0x38, 0x1e, 0x38, 0xc6, 0x78, 0xe3, 
0x19, 0xe3, 0x8c, 0x67, 0x8e, 0x31, 0x9e, 0x38, 0xc6, 0x03, 0x80, 0x72, 0x07, 0x00, 0xe4, 0x0e, 
0x01, 0xc8, 0x1c, 0x03, 0x90, 0x70, 0xc0, 0xe7, 0x81, 0xc3, 0x03, 0x9e, 0x07, 0x0c, 0x0e, 0x78, 
0x1c, 0x30, 0x39, 0xe0, 0x70, 0xc0, 0xe7, 0x80, 0x30, 0x01, 0x80, 0x06, 0x00, 0x30, 0x01, 0x81, 
0x80, 0x0e, 0x38, 0x01, 0x81, 0x80, 0x0e, 0x38, 0x01, 0x81, 0x80, 0x0e, 0x38, 0x01, 0x81, 0x80, 
0x0e, 0x38, 0x01, 0x81, 0x80, 0x0e, 0x38, 0x38, 0x1c, 0x23, 0x8e, 0x07, 0x08, 0xe0, 0x1c, 0x00, 
0x60, 0xe0, 0x7b, 0x99, 0xc0, 0xf7, 0x33, 0x81, 0xee, 0x63, 0x8e, 0x1c, 0x87, 0x1c, 0x01, 0x83, 
0xee, 0x38, 0x08, 0x01, 0x02, 0x00, 0x41, 0xc0, 0xe0, 0x40, 0x08, 0x60, 0x1e, 0x7d, 0xc6, 0x01, 
0xe7, 0xdc, 0x60, 0x1e, 0x7d, 0xc6, 0x01, 0xe7, 0xdc, 0x60, 0x1e, 0x7d, 0xc6, 0x01, 0xe7, 0xdc, 
0x60, 0x1e, 0x7d, 0xc6, 0x01, 0xe7, 0xdc, 0x60, 0x1e, 0x7d, 0xc6, 0x01, 0xe7, 0xdc, 0x60, 0x1e, 
0x7d, 0xc6, 0x01, 0xe7, 0xdc, 0x1c, 0x18, 0x79, 0x07, 0x06, 0x1e, 0x41, 0xc1, 0x87, 0x90, 0x70, 
0x61, 0xe4, 0x1c, 0x18, 0x79, 0x07, 0x06, 0x1e, 0x41, 0xc1, 0x87, 0x90, 0x70, 0x61, 0xe4, 0x1c, 
0x1c, 0x38, 0x70, 0x70, 0xe0, 0x73, 0x81, 0xc3, 0x81, 0xce, 0x07, 0x0e, 0x07, 0x38, 0x1c, 0x38, 
0x1c, 0xe0, 0x70, 0xe0, 0x73, 0x81, 0xc3, 0x81, 0xce, 0x07, 0x0e, 0x07, 0x38, 0x1c, 0x38, 0x1c, 
0xe0, 0x1c, 0x38, 0x1c, 0xe0, 0x1c, 0x38, 0x1c, 0xe0, 0x1c, 0x38, 0x1c, 0xe0, 0x1c, 0x38, 0x1c, 
0xe0, 0x1c, 0x30, 0x39, 0xe0, 0x70, 0xc0, 0xe7, 0x81, 0xc3, 0x01, 0xcf, 0x00, 0xe1, 0x80, 0xe7, 
0x80, 0x70, 0xc0, 0x73, 0xc0, 0x38, 0x60, 0x39, 0xe0, 0x1c, 0x30, 0x1c, 0xf0, 0x03, 0x80, 0x0c, 
0x00, 0xe0, 0x03, 0x00, 0x38, 0x00, 0xc0, 0x0e, 0x00, 0x30, 0x10, 0x02, 0x04, 0x00, 0x81, 0x00, 
0x20, 0x40, 0x08, 0x10, 0x02, 0x04, 0x00, 0x80, 

0xf8, 0x7f, 0x38, 0xe7, 0xff, 0x02, 0x78, 0x3f, 0xf8, 0x13, 0xc1, 0xff, 0xc0, 0x9e, 0x00, 0x7e, 
0x00, 0xf8, 0xff, 0xc0, 0x1e, 0xc3, 0xff, 0x00, 0x7b, 0x0f, 0xfc, 0x01, 0xec, 0x3f, 0xf0, 0x07, 
0xb0, 0xff, 0xc0, 0x1e, 0xc3, 0xff, 0xc0, 0xf0, 0xff, 0xf0, 0x3c, 0x3f, 0xfc, 0x0f, 0x0f, 0xff, 
0x03, 0xc7, 0xff, 0xc1, 0xe1, 0xfc, 0x03, 0xe0, 0x0f, 0xf0, 0x7f, 0xc7, 0xf3, 0xf9, 0xf7, 0xcf, 
0xe7, 0xf3, 0xef, 0x9f, 0xcf, 0xe7, 0xdf, 0x3f, 0x9f, 0xcf, 0xbe, 0x7f, 0x3f, 0x9f, 0x7c, 0xfe, 
0x7c, 0x7f, 0x3e, 0x7f, 0x3f, 0xfe, 0x7d, 0xfc, 0xff, 0xf9, 0xf7, 0xf3, 0xff, 0xe7, 0xdf, 0xff, 
0x7d, 0xff, 0xf3, 0xe7, 0xff, 0xcf, 0x9f, 0xff, 0x3e, 0x7c, 0x21, 0xfc, 0xfb, 0xef, 0x9f, 0x08, 
0x7f, 0x3e, 0xfb, 0xe7, 0xc2, 0x1f, 0xcf, 0xbe, 0xf9, 0xf0, 0x10, 0xfb, 0xe7, 0xc0, 0x43, 0xef, 
0x9f, 0x01, 0x0f, 0xbe, 0x7c, 0x04, 0x3e, 0xf8, 0x0f, 0xc0, 0x0f, 0x00, 0x3f, 0x00, 0x3c, 0x00, 
0xfc, 0x00, 0xf0, 0x03, 0xf0, 0x03, 0xc1, 0xfc, 0x01, 0xde, 0x1f, 0xc0, 0x1d, 0xe1, 0xfc, 0x3e, 
0xf8, 0x3f, 0x87, 0xdf, 0x07, 0xf0, 0xfb, 0xe0, 0xfe, 0x1f, 0x7c, 0x11, 0xe0, 0xbc, 0x47, 0x82, 
0xf1, 0x1e, 0x0b, 0xc4, 0x78, 0x2f, 0x11, 0xe0, 0xbc, 0x0f, 0xe0, 0x3c, 0x1f, 0xc0, 0x78, 0x3f, 
0x80, 0xf0, 0x7f, 0x01, 0xe0, 0x1f, 0x00, 0x7b, 0xc0, 0x7c, 0x01, 0xef, 0x01, 0xf0, 0x07, 0xbc, 
0x07, 0xc0, 0x1e, 0xf0, 0x1f, 0x00, 0x7b, 0xc0, 0x10, 0x00, 0x80, 0x02, 0x00, 0x10, 0x00, 0x80, 
0x80, 0x04, 0x10, 0x00, 0x80, 0x80, 0x04, 0x10, 0x00, 0x80, 0x80, 0x04, 0x10, 0x00, 0x80, 0x80, 
0x04, 0x10, 0x00, 0x80, 0x80, 0x04, 0x10, 0x7c, 0x7f, 0xf7, 0xdf, 0x1f, 0xfd, 0xf0, 0x7f, 0x00, 
0x41, 0xff, 0xf7, 0xfb, 0xff, 0xef, 0xf7, 0xff, 0xdf, 0xe7, 0xdf, 0x0f, 0x02, 0x08, 0x01, 0x01, 
0xc7, 0x7c, 0x0f, 0xff, 0x03, 0xff, 0xc7, 0xdf, 0x80, 0x7f, 0xf8, 0xf8, 0x7f, 0x38, 0xef, 0x87, 
0xf3, 0x8e, 0xf8, 0x7f, 0x38, 0xef, 0x87, 0xf3, 0x8e, 0xf8, 0x7f, 0x38, 0xef, 0x87, 0xf3, 0x8e, 
0xf8, 0x7f, 0x38, 0xef, 0x87, 0xf3, 0x8e, 0xf8, 0x7f, 0x38, 0xef, 0x87, 0xf3, 0x8e, 0xf8, 0x7f, 
0x38, 0xef, 0x87, 0xf3, 0x8e, 0x7f, 0xf8, 0x1e, 0x1f, 0xfe, 0x07, 0x87, 0xff, 0x81, 0xe1, 0xff, 
0xe0, 0x78, 0x7f, 0xf8, 0x1e, 0x1f, 0xfe, 0x07, 0x87, 0xff, 0x81, 0xe1, 0xff, 0xe0, 0x78, 0x7f, 
0x3e, 0xfe, 0xf8, 0x1f, 0x80, 0x1e, 0x00, 0x7e, 0x00, 0x78, 0x01, 0xf8, 0x01, 0xe0, 0x07, 0xe0, 
0x07, 0x80, 0x1f, 0x80, 0x1e, 0x00, 0x7e, 0x00, 0x78, 0x01, 0xf8, 0x01, 0xe0, 0x07, 0xe0, 0x07, 
0x80, 0x07, 0xe0, 0x07, 0x80, 0x07, 0xe0, 0x07, 0x80, 0x07, 0xe0, 0x07, 0x80, 0x07, 0xe0, 0x07, 
0x80, 0x07, 0xc0, 0x1e, 0xf0, 0x1f, 0x00, 0x7b, 0xc0, 0x7c, 0x00, 0xf7, 0x80, 0x3e, 0x00, 0x7b, 
0xc0, 0x1f, 0x00, 0x3d, 0xe0, 0x0f, 0x80, 0x1e, 0xf0, 0x07, 0xc0, 0x0f, 0x78, 0x0f, 0xe0, 0x08, 
0x03, 0xf8, 0x02, 0x00, 0xfe, 0x00, 0x80, 0x3f, 0x80, 0x20, 0x1f, 0xfe, 0x07, 0xff, 0x81, 0xff, 
0xe0, 0x7f, 0xf8, 0x1f, 0xfe, 0x07, 0xff, 0x80, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x03, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x18, 
0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 
0xc0, 0x03, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x18, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x1c, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x01, 0x80, 0x02, 0x10, 0x02, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 
0x38, 0x0e, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x70, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x38, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xe0, 0x07, 0x00, 0x1c, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x07, 0x00, 0x70, 0x00, 
0x00, 0x00, 0x1c, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x38, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1c, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x80, 0x00, 0x00, 0x00, 0x0e, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x80, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x38, 0x00, 0xe0, 0x03, 0x80, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x70, 0x00, 
0x00, 0x38, 0x70, 0x07, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x07, 
0x00, 0x03, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 0x10, 
0x00, 0xe0, 0x04, 0xe0, 0x00, 0x01, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x04, 0x80, 0x09, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x03, 0x80, 0x3f, 0x80, 0x7f, 0x00, 0x08, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x38, 0x01, 0xfc, 0x01, 0xfc, 0x00, 0xe0, 0x01, 
0xc0, 0x06, 0xc0, 0x0d, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb0, 0x1b, 0x00, 0xe2, 
0x07, 0x10, 0x08, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x20, 0x00, 0x00, 0x00, 0x00, 
0x1c, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x80, 0x01, 0xe0, 0x01, 0xe0, 0x71, 
0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x0e, 0x00, 0x7f, 0x00, 0xfe, 0x00, 0xe0, 
0x38, 0x0e, 0x01, 0xc0, 0xfe, 0x1f, 0xc0, 0xd8, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x38, 
0x01, 0xfc, 0x03, 0xf8, 0x03, 0x60, 0x06, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xe0, 0x07, 0x00, 0x1c, 0x00, 0xe0, 0x0f, 0xe0, 0x7f, 0x00, 0x00, 0x00, 0x07, 0x00, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x07, 0x00, 0x70, 0x3f, 
0x83, 0xf8, 0x36, 0x01, 0xb0, 0x3b, 0x80, 0x77, 0x00, 0x71, 0x00, 0xe2, 0x01, 0xb0, 0x03, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x38, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1c, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 
0x80, 0x00, 0x00, 0x00, 0x0e, 0x03, 0x80, 0xfe, 0x0f, 0xe1, 0xfc, 0x00, 0x00, 0x00, 0x3a, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x80, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x38, 0x00, 0xe0, 0x03, 0x80, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x70, 0x00, 
0x00, 0x38, 0x70, 0x07, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x07, 
0x00, 0x03, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x01, 0xd0, 
0x00, 0xe0, 0x74, 0xe0, 0x00, 0x1d, 0x00, 0x00, 0x07, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x04, 0x80, 0x09, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 
0x40, 0x04, 0x40, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x10, 0x11, 0x01, 0x1c, 
0x08, 0xe0, 0x30, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x40, 0x00, 0x00, 0x00, 0x00, 
0x1c, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8e, 
0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 
0x38, 0x0e, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x88, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x38, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x20, 0x04, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xe0, 0x07, 0x00, 0x1c, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x07, 0x00, 0x70, 0x00, 
0x00, 0x00, 0x22, 0x01, 0x10, 0x3b, 0x80, 0x77, 0x00, 0x8e, 0x01, 0x1c, 0x01, 0x10, 0x02, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x38, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1c, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 
0x80, 0x00, 0x00, 0x00, 0x0e, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3a, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x80, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x38, 0x00, 0xe0, 0x03, 0x80, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x70, 0x00, 
0x00, 0x38, 0x70, 0x07, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x07, 
0x00, 0x03, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x01, 0xd0, 
0x00, 0xe0, 0x74, 0xe0, 0x00, 0x1d, 0x00, 0x00, 0x07, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x03, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x01, 
0x80, 0x08, 0x20, 0x10, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x08, 0x20, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x10, 0x08, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x41, 0x02, 0x08, 0x3b, 0x80, 0x77, 0x00, 0x00, 0x00, 0x00, 0x02, 0x08, 0x04, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 
0x00, 0x00, 0x38, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma16bAdd_Font = {0x01, 25, 0, 25, 0, 0, 25, 456, 0x1e00, 0x1eff,
(PEGUSHORT *) Tahoma16bAdd_Font_offset_table, &WinInwa18bMiy_Font,
(PEGUBYTE *) Tahoma16bAdd_Font_data_table};
//...........................................................................


ROMDATA PEGUSHORT Tahoma16bTai_offset_table[92] = {
0x0000,0x000e,0x001d,0x002c,0x003a,0x0048,0x0057,0x0062,0x006f,0x007d,0x008d,0x009d,0x00b1,0x00c5,0x00d4,0x00e3,
0x00f0,0x0102,0x0117,0x012d,0x013b,0x0149,0x0157,0x0166,0x0173,0x0181,0x018f,0x019d,0x01ac,0x01bb,0x01cc,0x01dd,
0x01ec,0x01fa,0x0208,0x0213,0x0221,0x022f,0x023e,0x0249,0x0257,0x0267,0x0275,0x0284,0x0295,0x02a2,0x02af,0x02bb,
0x02c5,0x02cf,0x02da,0x02ea,0x02f6,0x0302,0x030e,0x031a,0x031f,0x0327,0x032b,0x0340,0x0355,0x036a,0x037f,0x038d,
0x0394,0x03a1,0x03ab,0x03b5,0x03c0,0x03cb,0x03d6,0x03df,0x03e4,0x03ec,0x03f4,0x03f9,0x0400,0x0405,0x040b,0x0418,
0x0425,0x0434,0x0444,0x0453,0x0463,0x0473,0x0481,0x0495,0x04a5,0x04b7,0x04c7,0x04dd};



ROMDATA PEGUBYTE Tahoma16bTai_data_table[3900] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x60, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xfc, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xc8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x0c, 0x00, 0x18, 0x00, 0x07, 0x00, 0xf6, 
0x07, 0x90, 0xf6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x00, 0x00, 0x1f, 0xc7, 0xe6, 0x38, 0x00, 0x00, 0x03, 0x00, 0xc2, 0x0d, 0x36, 0x01, 
0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x68, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x98, 0x00, 0x24, 0x00, 0x18, 0xc1, 0x8e, 
0x18, 0xf1, 0x8e, 0x00, 0x00, 0x03, 0xff, 0xfc, 0x1f, 0xff, 0xe0, 0xff, 0xff, 0x07, 0xff, 0xf8, 
0x7f, 0x80, 0x00, 0x00, 0x3f, 0xcf, 0xf6, 0x7c, 0x00, 0x00, 0x03, 0x78, 0xc5, 0x12, 0xbf, 0x3e, 
0x93, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xb0, 0x00, 0x24, 0x00, 0x10, 0x43, 0x06, 
0x10, 0x43, 0x06, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7f, 0xc0, 0x00, 0x00, 0x70, 0x12, 0x7e, 0x7c, 0x00, 0x00, 0x03, 0x54, 0xc2, 0x68, 0xbf, 0x50, 
0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x18, 0x00, 0x3f, 0xe3, 0xfe, 
0x3f, 0xe3, 0xfe, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7f, 0xe0, 0x00, 0x00, 0x7f, 0x12, 0x3b, 0xdc, 0x00, 0x00, 0x01, 0xc8, 0xc3, 0x88, 0xe6, 0x20, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x01, 0xdc, 0x00, 
0x00, 0x00, 0x70, 0x00, 0x70, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x38, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x70, 0x00, 
0x00, 0x00, 0xe0, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7d, 0xe0, 0x00, 0x00, 0x7f, 0x8c, 0x3b, 0xdc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x61, 0xcc, 0x00, 
0x00, 0x00, 0x70, 0x00, 0x70, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xc0, 0xf8, 0x71, 0xb0, 0xe0, 0xfc, 0x03, 0x70, 0x36, 0x1c, 0x07, 0x0f, 0xe0, 0x3f, 0x01, 
0xf0, 0x39, 0xb0, 0x38, 0x7e, 0x07, 0x07, 0xe0, 0x70, 0x7f, 0x00, 0xfe, 0x03, 0xfe, 0x36, 0x07, 
0x03, 0xb8, 0x1c, 0x1f, 0x81, 0xc0, 0x7e, 0x00, 0xd8, 0x07, 0xe0, 0xe0, 0x70, 0x7f, 0xce, 0x0e, 
0x38, 0x1c, 0xe0, 0x71, 0xc0, 0xe3, 0x81, 0xce, 0x08, 0xe7, 0x08, 0xe0, 0x7f, 0x07, 0x03, 0x8e, 
0x0e, 0x3f, 0xe1, 0xf8, 0x0f, 0xe0, 0x1f, 0xc1, 0xf8, 0x07, 0xf8, 0xe0, 0x38, 0x3f, 0xe3, 0x80, 
0xc7, 0x08, 0xe1, 0xfc, 0x0f, 0xcc, 0xc1, 0xc6, 0x18, 0x00, 0xfc, 0x00, 0xfc, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7d, 0xe3, 0x87, 0x0e, 0x03, 0x80, 0x71, 0xdc, 0x7f, 0x03, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x93, 0x8c, 0x00, 
0x00, 0x00, 0x70, 0x00, 0xe0, 0x00, 0x1c, 0xc3, 0x9c, 0x00, 0x00, 0x00, 

0x3f, 0xf1, 0xfc, 0x73, 0xf8, 0xe3, 0xff, 0x0f, 0xf8, 0x7f, 0x1c, 0x09, 0x9f, 0xf8, 0x7f, 0xc3, 
0xf8, 0x73, 0xf8, 0x71, 0xff, 0x07, 0x1f, 0xf0, 0x70, 0xff, 0x81, 0xff, 0x07, 0xfe, 0x7f, 0x1f, 
0x87, 0xfc, 0x1c, 0x7f, 0xe1, 0xc1, 0xff, 0x83, 0xfe, 0x1f, 0xf9, 0x30, 0xf8, 0xff, 0xd3, 0x0e, 
0x4c, 0x1d, 0x30, 0x73, 0x20, 0xe6, 0x41, 0xd3, 0x1c, 0xe9, 0x9c, 0xe0, 0xff, 0x89, 0x83, 0x99, 
0x0e, 0x7f, 0xe3, 0xfe, 0x3f, 0xf8, 0x3f, 0xe3, 0xfe, 0x1f, 0xf9, 0x30, 0x38, 0xff, 0xe4, 0xc1, 
0x29, 0x9c, 0xe3, 0xff, 0x1f, 0xf9, 0x23, 0xc9, 0x30, 0x01, 0xff, 0x01, 0xff, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7d, 0xe3, 0x87, 0x0e, 0x03, 0x80, 0xf1, 0x9c, 0xff, 0x8f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x97, 0x8e, 0x00, 
0x00, 0x00, 0x70, 0x01, 0xe0, 0x00, 0x1d, 0x27, 0x9c, 0x1f, 0x00, 0x00, 

0x38, 0x72, 0x4e, 0x77, 0x5c, 0xe3, 0x87, 0x0e, 0x9c, 0xeb, 0x9c, 0x09, 0x98, 0x38, 0x61, 0xc4, 
0x9c, 0xe7, 0x5c, 0xe1, 0xc3, 0x87, 0x1c, 0x38, 0x71, 0xc1, 0xc3, 0x83, 0x8e, 0x00, 0xeb, 0x9b, 
0x8e, 0x4e, 0x1c, 0x70, 0xe1, 0xc1, 0xc3, 0x87, 0xae, 0x1c, 0x39, 0x31, 0xb9, 0xc0, 0x13, 0x0e, 
0x4c, 0x1d, 0x30, 0x73, 0x20, 0xe6, 0x41, 0xd3, 0x1c, 0xe9, 0x9c, 0xe1, 0xc1, 0xc9, 0x83, 0x99, 
0x0e, 0xe0, 0x07, 0x0e, 0x30, 0x38, 0x70, 0x72, 0x0f, 0x1c, 0x79, 0x30, 0x38, 0xc3, 0xe4, 0xc1, 
0x29, 0x9c, 0xe2, 0x07, 0x18, 0x39, 0x27, 0xcf, 0x60, 0x01, 0x07, 0x81, 0x07, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7f, 0xc3, 0x87, 0x0e, 0x03, 0x80, 0xe0, 0x1c, 0x83, 0xdc, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0xf8, 0x0f, 0x80, 0x7e, 0x07, 0x1d, 0x81, 0xe7, 0x01, 0xff, 0x01, 0xff, 0x07, 0xf8, 
0x0e, 0x78, 0x70, 0x7f, 0xc0, 0xf3, 0x99, 0x27, 0x9c, 0x3f, 0x80, 0x00, 

0x70, 0x3a, 0x4e, 0x77, 0x9c, 0xe7, 0x03, 0x9c, 0x0e, 0xe3, 0x9c, 0x07, 0x80, 0x1c, 0x00, 0xe4, 
0x9c, 0x77, 0x9c, 0x73, 0x81, 0xc7, 0x38, 0x1c, 0x73, 0x80, 0xe7, 0x01, 0xcf, 0xf8, 0xf3, 0xb3, 
0x9c, 0x0f, 0x1c, 0xe0, 0x71, 0xc3, 0x81, 0xce, 0x07, 0x38, 0x1c, 0xf1, 0xb9, 0xff, 0x0f, 0x0e, 
0x3c, 0x1c, 0xf0, 0x73, 0xc0, 0xe7, 0x81, 0xcf, 0x34, 0xe7, 0x94, 0xe3, 0x80, 0xe7, 0x83, 0x9e, 
0x0e, 0xff, 0x0e, 0x07, 0x00, 0x1c, 0xe0, 0x38, 0x07, 0x38, 0xfc, 0xf0, 0x38, 0x06, 0xf3, 0xc1, 
0xc7, 0x94, 0xe0, 0x03, 0x8f, 0xfd, 0xef, 0xc7, 0xc0, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7f, 0xc3, 0x87, 0x0e, 0x03, 0x80, 0xe0, 0x1c, 0x01, 0xde, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x1c, 0x1f, 0xc0, 0xff, 0x87, 0x3f, 0xc3, 0xff, 0x87, 0xfc, 0x07, 0xfc, 0x07, 0xfc, 
0x1f, 0xfc, 0x71, 0xff, 0x01, 0xff, 0xb9, 0xef, 0x9c, 0x60, 0x80, 0x00, 

0x7c, 0x39, 0x8e, 0x74, 0x9c, 0xe7, 0x03, 0x9c, 0x0e, 0x93, 0x9d, 0xc3, 0x87, 0x1c, 0x70, 0xe3, 
0x1c, 0x74, 0x9c, 0x73, 0xe1, 0xc7, 0x3e, 0x1c, 0x73, 0xe0, 0xe7, 0xe1, 0xcf, 0xfc, 0x93, 0xb3, 
0x9c, 0x07, 0x1c, 0xf8, 0x71, 0xc3, 0x81, 0xce, 0x07, 0x3e, 0x1c, 0x73, 0x39, 0xff, 0x87, 0x0e, 
0x1c, 0x1c, 0x70, 0x73, 0x80, 0xe7, 0x01, 0xc7, 0x36, 0xe3, 0xb6, 0xe3, 0xe0, 0xe3, 0x83, 0x9c, 
0x0e, 0xff, 0x8f, 0x87, 0x1e, 0x1c, 0xf8, 0x38, 0x07, 0x38, 0x5c, 0x77, 0x3a, 0x78, 0x71, 0xc1, 
0xc3, 0xb6, 0xe1, 0xc3, 0x87, 0xdc, 0x79, 0xc0, 0x00, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7f, 0xe3, 0x87, 0x0e, 0x03, 0x80, 0xe0, 0x1c, 0x01, 0xd2, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0e, 0x0e, 0x38, 0xe1, 0xc3, 0xc7, 0x72, 0xe3, 0x99, 0xc7, 0x00, 0x07, 0x00, 0x06, 0x0e, 
0x39, 0x9c, 0x71, 0xc0, 0x03, 0xde, 0xf0, 0xfb, 0x9c, 0xe0, 0x00, 0x00, 

0x7c, 0x38, 0x1c, 0x74, 0xbc, 0xe7, 0x73, 0x9d, 0xce, 0x97, 0x1c, 0xe3, 0x89, 0xdc, 0x98, 0xe0, 
0x38, 0x74, 0x9c, 0x73, 0xe1, 0xc7, 0x3e, 0x1c, 0x73, 0xe0, 0xe7, 0xe1, 0xcc, 0x1e, 0x97, 0x63, 
0x9c, 0xc7, 0x1c, 0xf8, 0x71, 0xc3, 0x99, 0xce, 0x67, 0x3e, 0x1c, 0x73, 0x38, 0xe3, 0xc7, 0x0e, 
0x1c, 0x1c, 0x70, 0x73, 0x88, 0xe7, 0x11, 0xc7, 0x66, 0xe3, 0xa6, 0xe3, 0xe0, 0xe3, 0x83, 0x9c, 
0x0e, 0x03, 0xcf, 0x87, 0x3f, 0x9c, 0xf8, 0x38, 0x07, 0x3b, 0x9c, 0x75, 0x3c, 0xfe, 0x71, 0xc3, 
0xe3, 0xb6, 0xe3, 0x23, 0x8e, 0x1c, 0x01, 0xc0, 0x00, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7c, 0xf3, 0x87, 0x0e, 0x03, 0x80, 0xe0, 0x1c, 0x01, 0xd2, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0xe6, 0x70, 0x73, 0x80, 0xe7, 0x48, 0xe7, 0x19, 0xce, 0x0c, 0x0e, 0x0c, 0x00, 0x07, 
0x39, 0x8e, 0x73, 0x80, 0xe3, 0x8e, 0x70, 0x03, 0x9c, 0xc0, 0x02, 0x30, 

0x38, 0x38, 0x3c, 0x73, 0x38, 0xe7, 0xcb, 0x9f, 0x2e, 0x67, 0x1c, 0xe3, 0x89, 0xdc, 0x98, 0xe0, 
0x78, 0x73, 0x38, 0x71, 0xc1, 0xc7, 0x1c, 0x1c, 0x70, 0xc0, 0xe1, 0xc1, 0xc3, 0x8e, 0x6f, 0x63, 
0x9d, 0x27, 0x1c, 0x70, 0x71, 0xc3, 0xa5, 0xce, 0x97, 0x1c, 0x1c, 0x76, 0x38, 0xe1, 0xc7, 0x0e, 
0x1c, 0x1c, 0x70, 0x73, 0x9c, 0xe7, 0x39, 0xc7, 0x62, 0xe3, 0xa2, 0xe0, 0xc0, 0xe3, 0x83, 0x8f, 
0x0e, 0x01, 0xc7, 0x07, 0x39, 0x9c, 0x30, 0x38, 0x07, 0x3e, 0x5c, 0x77, 0x38, 0xe6, 0x71, 0xc6, 
0xe3, 0xa2, 0xe3, 0x23, 0x99, 0x1c, 0x01, 0xc0, 0x00, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7c, 0xf3, 0x87, 0x0e, 0x03, 0x80, 0xe0, 0x1c, 0x01, 0xcc, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0xe6, 0x70, 0x73, 0x8c, 0xe7, 0x48, 0xe7, 0x19, 0xce, 0x12, 0x0e, 0x12, 0x03, 0x87, 
0x39, 0x8e, 0x73, 0x81, 0x33, 0x8e, 0x00, 0x03, 0x9c, 0xc0, 0xda, 0x30, 

0x70, 0x38, 0x38, 0x70, 0x70, 0xe7, 0xcb, 0x9f, 0x2e, 0x0e, 0x1c, 0x73, 0x87, 0xfc, 0x78, 0xe0, 
0x70, 0x70, 0x38, 0x73, 0x81, 0xc7, 0x38, 0x1c, 0x71, 0xc0, 0xe3, 0x81, 0xc4, 0xee, 0x0e, 0xc3, 
0x9d, 0x27, 0x1c, 0xe0, 0x71, 0xc3, 0xa5, 0xce, 0x97, 0x38, 0x1c, 0x76, 0x38, 0xe1, 0xc7, 0x0e, 
0x1c, 0x1c, 0x70, 0x73, 0xb6, 0xe7, 0x6d, 0xc7, 0xc3, 0xe3, 0xe3, 0xe1, 0xc0, 0xe3, 0x83, 0x9f, 
0x0e, 0x01, 0xce, 0x07, 0x38, 0xdc, 0x70, 0x38, 0x07, 0x3e, 0x5c, 0x73, 0xf8, 0xe3, 0x71, 0xcc, 
0xe3, 0xe3, 0xe3, 0xc3, 0x99, 0x1c, 0x01, 0xc6, 0x18, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7c, 0xf3, 0x87, 0x0e, 0x03, 0x80, 0xe0, 0x1c, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0xe6, 0x70, 0x73, 0x92, 0xe7, 0x30, 0xe7, 0x01, 0xce, 0x12, 0x0e, 0x12, 0x06, 0x47, 
0x38, 0x0e, 0x73, 0x99, 0x33, 0x8e, 0x00, 0x03, 0x9c, 0xc0, 0xff, 0x60, 

0x70, 0x38, 0x38, 0x70, 0x70, 0xe3, 0xf3, 0x8f, 0xce, 0x0f, 0x9c, 0x3b, 0x80, 0x7c, 0x39, 0xf0, 
0x70, 0x70, 0x38, 0x73, 0xc3, 0xe7, 0x3c, 0x1c, 0x73, 0xc0, 0xe7, 0x81, 0xc4, 0xfe, 0x0e, 0xc3, 
0x8e, 0xef, 0x9c, 0xf0, 0x73, 0xe1, 0xfd, 0xc7, 0xf7, 0x3c, 0x1c, 0x7c, 0x38, 0xe1, 0xc7, 0x3f, 
0x1c, 0x1c, 0x70, 0x73, 0xe3, 0xe7, 0xc7, 0xc7, 0xc1, 0xe3, 0xc1, 0xe3, 0xc0, 0xe7, 0xe3, 0x9c, 
0x0e, 0x03, 0xcf, 0x07, 0x3c, 0x7c, 0xf0, 0x38, 0x0f, 0x1f, 0x9c, 0x70, 0x38, 0xf1, 0xf1, 0xd8, 
0xe3, 0xc1, 0xe3, 0x83, 0x9e, 0x1c, 0x01, 0xc9, 0x30, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7d, 0xf3, 0xc7, 0x8f, 0x03, 0xc0, 0xf0, 0x1e, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0e, 0x0e, 0x70, 0x73, 0x92, 0xe7, 0x00, 0xe7, 0xc1, 0xce, 0x1c, 0x0e, 0x1c, 0x06, 0x47, 
0x3e, 0x0e, 0x73, 0xbc, 0xf3, 0xe7, 0x00, 0x03, 0x9c, 0xc1, 0xff, 0xe0, 

0x70, 0x38, 0x38, 0x70, 0x70, 0xe3, 0x83, 0x8e, 0x0e, 0x13, 0xfc, 0x3b, 0x80, 0x3c, 0x3f, 0x90, 
0x70, 0x70, 0x38, 0x73, 0x25, 0xdf, 0x32, 0x1c, 0x74, 0xc0, 0xe9, 0x81, 0xc3, 0xbe, 0x0f, 0x83, 
0x8e, 0xd7, 0x7c, 0xc8, 0x7d, 0xd1, 0xd9, 0xc7, 0x67, 0x32, 0x1c, 0x78, 0x38, 0xe1, 0xc7, 0xf9, 
0x1c, 0x1c, 0x70, 0x73, 0xc1, 0xe7, 0x83, 0xc7, 0x81, 0xe3, 0xc1, 0xe4, 0xc0, 0xe9, 0xff, 0x9c, 
0x0e, 0x04, 0xcc, 0x87, 0x32, 0x7d, 0x30, 0x38, 0x13, 0x1c, 0x1c, 0x70, 0x38, 0xc9, 0xf1, 0xf0, 
0xe3, 0xc1, 0xe3, 0x83, 0x9c, 0x1c, 0x01, 0xcf, 0x60, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7f, 0xe3, 0x26, 0x4c, 0x83, 0x20, 0xc8, 0x19, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x1c, 0x38, 0xe1, 0xce, 0xe7, 0x00, 0xe7, 0x21, 0xc7, 0x08, 0x07, 0x08, 0x07, 0x8e, 
0x39, 0x0e, 0x71, 0xf6, 0x33, 0x97, 0x00, 0x03, 0x9c, 0x63, 0xa5, 0xc0, 

0x70, 0x38, 0x7f, 0xf0, 0xff, 0xe3, 0x83, 0x8e, 0x0e, 0x12, 0x7c, 0x1f, 0x00, 0x3c, 0x3e, 0x90, 
0xff, 0xf0, 0x7f, 0xf3, 0x25, 0xcf, 0x32, 0x1f, 0xf4, 0xc0, 0xe9, 0x81, 0xc0, 0x1e, 0x0f, 0x83, 
0x8f, 0x97, 0x1c, 0xc8, 0x79, 0xd1, 0xf1, 0xc7, 0xc7, 0x32, 0x1c, 0x78, 0x39, 0xff, 0x87, 0xe9, 
0x3f, 0xfc, 0xff, 0xf3, 0xc1, 0xe7, 0x83, 0xc7, 0x00, 0xe3, 0x80, 0xe4, 0xc0, 0xe9, 0x8f, 0x9f, 
0xfe, 0x04, 0xcc, 0x87, 0x32, 0x3d, 0x30, 0x38, 0x13, 0x1c, 0x1c, 0xff, 0xf8, 0xc8, 0xf1, 0xe0, 
0xe3, 0x80, 0xe3, 0xff, 0x9f, 0xfc, 0x01, 0xc7, 0xc0, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x04, 0x10, 0x00, 0x20, 0x80, 0x01, 0x04, 0x00, 0x08, 
0x7f, 0xe3, 0x26, 0x4c, 0x83, 0x20, 0xc8, 0x19, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0xf8, 0x1f, 0xc1, 0xfd, 0xc7, 0xff, 0xc3, 0x23, 0xc7, 0xff, 0x87, 0xff, 0x83, 0xfe, 
0x19, 0x1f, 0xe1, 0xf3, 0xe1, 0x97, 0x00, 0x03, 0x9c, 0x3f, 0x80, 0x00, 

0x70, 0x38, 0x7f, 0xf0, 0xff, 0xe3, 0x83, 0x8e, 0x0e, 0x0c, 0x1c, 0x0e, 0x00, 0x1c, 0x3c, 0x60, 
0xff, 0xf0, 0x7f, 0xf1, 0xc3, 0x87, 0x1c, 0x1f, 0xf3, 0x80, 0xe7, 0x01, 0xc0, 0x0e, 0x0f, 0x03, 
0x8f, 0x0e, 0x0c, 0x70, 0x70, 0xe1, 0xe1, 0xc7, 0x87, 0x1c, 0x1c, 0x70, 0x39, 0xff, 0x07, 0x86, 
0x3f, 0xfc, 0xff, 0xf3, 0x80, 0xe7, 0x01, 0xc7, 0x00, 0xe3, 0x80, 0xe3, 0x80, 0xe7, 0x07, 0x9f, 
0xfe, 0x03, 0x87, 0x07, 0x1c, 0x1c, 0xe0, 0x38, 0x0e, 0x1c, 0x1c, 0xff, 0xf8, 0x70, 0x71, 0xc0, 
0xe3, 0x80, 0xe3, 0xff, 0x9f, 0xfc, 0x01, 0xc0, 0x00, 0x00, 0x03, 0x80, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0xfc, 0x1f, 0xff, 0xe0, 0xff, 0xff, 0x07, 0xff, 0xf8, 
0x7f, 0x81, 0xc3, 0x87, 0x01, 0xc0, 0x70, 0x0e, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xf0, 0x0f, 0x80, 0x79, 0xc7, 0xff, 0x81, 0xc3, 0x81, 0xff, 0x01, 0xff, 0x01, 0xf8, 
0x0e, 0x1f, 0xc0, 0x71, 0xc0, 0xe3, 0x80, 0x03, 0x9c, 0x1e, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x02, 0xe0, 0x11, 0xc0, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x10, 0x3c, 0xe3, 0xa1, 0xc7, 0x46, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x29, 0x59, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x60, 0x4f, 0xe4, 0x65, 0xc9, 0xda, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x18, 0xd9, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x39, 0xe3, 0x9b, 0xc7, 0x66, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x18, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma16bTai_Font = {0x01, 25, 0, 25, 0, 0, 25, 156, 0x0e01, 0x0e5b,
(PEGUSHORT *) Tahoma16bTai_offset_table, &Tahoma16bAdd_Font,
(PEGUBYTE *) Tahoma16bTai_data_table};


