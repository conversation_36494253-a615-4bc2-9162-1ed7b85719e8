/*##################################################################
  FILE    : FS_RAW.H

  USE     : File System RAW files routines & data structures header 
  			file.
  PROJECT : C-Map's File System.

  AUTHOR  : SiS[050727].
  UPDATED :
  ##################################################################


  Notes on the module.

*/

#ifndef __RAW_FILES__
	#define __RAW_FILES__


/************************************************************************
  #Include section.
 ************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include "CMapType.h"




/************************************************************************
  Types & Data Structure definition definition section.
 ************************************************************************/

/**
 * Raw file type. 
 * 
 * \ingroup RAWFILES
 */
typedef enum
{
	RAWT_INVALID = 0,		///< 0) Invalid type.
	RAWT_FORECAST_DATA,		///< 1) C-Meteo Forecast data.
	RAWT_MAX				///< 2) Used only for bounds checking.
	
} FS_RAW_TYPE;


/**
 * Raw file header data structure type. 
 * 
 * Fields of this data structure are written at the beginning of the RAW
 * file header and they should be read before start to read file data.
 *
 * \ingroup RAWFILES
 */
typedef struct  
{
	FS_RAW_TYPE		m_iType;			///< Raw file type.
	Long			m_dwFileSize;		///< File size (bytes).
	Word			m_wVersion;			///< Data version (protocol version).

} FS_RAW_HEADER;


/**
 * Pointer to one \c FS_RAW_HEADER data structure.
 * 
 * \ingroup RAWFILES
 */
typedef FS_RAW_HEADER * lpFS_RAW_HEADER;



/************************************************************************
  Exported Routines prototypes.
 ************************************************************************/


#ifdef __cplusplus
extern "C" 
{
#endif /* __cplusplus */

PRE_EXPORT_H extern void	IN_EXPORT_H FS_InitRawHeader ( lpFS_RAW_HEADER lpHeader );
PRE_EXPORT_H extern SWord	IN_EXPORT_H FS_RawReadHeader ( SWord nFp, lpFS_RAW_HEADER lpHeader );
PRE_EXPORT_H extern SWord	IN_EXPORT_H FS_RawWriteHeader ( SWord nFp, lpFS_RAW_HEADER lpHeader );
PRE_EXPORT_H extern SWord	IN_EXPORT_H FS_RawWrite ( SWord nFp, Byte *pBuff, Long dwSize );
PRE_EXPORT_H extern SWord	IN_EXPORT_H FS_RawRead	( SWord nFp, Byte *pBuff, Long dwSize );


#ifdef __cplusplus
}
#endif /* __cplusplus */




/************************************************************************
  END of Code.
 ************************************************************************/

#endif /* __RAW_FILES__ */