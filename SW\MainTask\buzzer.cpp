/*...........................................................................*/
/*.                  File Name : BUZZER.CPP                                 .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "buzzer.hpp"
#include "syslib.h"
#include "SysGPIO.h"
#include "SysALIVE.h"

/////////////////////////////////////////////////////////////////////////////
DWORD cBUZZER::m_dBuzzerMode = BUZZER_OFF;
DWORD cBUZZER::m_dBuzzerTick = 0;
DWORD cBUZZER::m_dBuzzerOffOn= BUZZER_PORT_OFF;
DWORD cBUZZER::m_dKeyToneTick= 0;
DWORD cBUZZER::m_dTxLedMode  = LED_OFF;
DWORD cBUZZER::m_dTxLedTick  = 0;
DWORD cBUZZER::m_dTxLedOffOn = LED_PORT_OFF;
DWORD cBUZZER::m_dRxLedMode  = LED_OFF;
DWORD cBUZZER::m_dRxLedTick  = 0;
DWORD cBUZZER::m_dRxLedOffOn = LED_PORT_OFF;
DWORD cBUZZER::m_dPwrLedMode = LED_OFF;
DWORD cBUZZER::m_dPwrLedTick = 0;
DWORD cBUZZER::m_dPwrLedOffOn= LED_PORT_OFF;
int   cBUZZER::m_nBeepMode   = BUZZER_BEEP_MODE_ON;
#ifdef EN_61993_ED3
int   cBUZZER::m_nBzrCnt     = 0;
#endif	// End of (EN_61993_ED3)
/////////////////////////////////////////////////////////////////////////////


cBUZZER::cBUZZER(void)
{
	m_dBuzzerMode = BUZZER_OFF;
	m_dBuzzerTick = 0;
	m_dBuzzerOffOn= BUZZER_PORT_OFF;
	m_dKeyToneTick= 0;
	m_dTxLedMode  = LED_OFF;
	m_dTxLedTick  = 0;
	m_dTxLedOffOn = LED_PORT_OFF;
	m_dRxLedMode  = LED_OFF;
	m_dRxLedTick  = 0;
	m_dRxLedOffOn = LED_PORT_OFF;
	m_dPwrLedMode = LED_OFF;
	m_dPwrLedTick = 0;
	m_dPwrLedOffOn= LED_PORT_OFF;
	m_nBeepMode   = BUZZER_BEEP_MODE_ON;

#ifdef EN_61993_ED3
	m_nBzrCnt = 0;
#endif	// End of (EN_61993_ED3)

}
cBUZZER::~cBUZZER(void)
{
}
DWORD cBUZZER::GetBuzzerMode(void)
{
      return(m_dBuzzerMode);
}
void  cBUZZER::SetBuzzerMode(DWORD dMode)
{
	m_dBuzzerMode = dMode;
	if (m_dBuzzerMode == BUZZER_OFF)
	{
		m_dBuzzerTick = 0;
		m_dBuzzerOffOn= BUZZER_PORT_OFF;
	}
	
	if (m_dBuzzerMode == BUZZER_CONTINUE)
	{
		m_dBuzzerTick = BUZZER_CONTINUE_ON_TICK;
		m_dBuzzerOffOn= BUZZER_PORT_ON;
	}
	
	if (m_dBuzzerMode == BUZZER_SHORT)
	{
#ifdef EN_61993_ED3
		m_dBuzzerMode = BUZZER_TWO_TIME_SHORT;
		m_dBuzzerTick = BUZZER_SHORT_ON_TICK;
		m_dBuzzerOffOn= BUZZER_PORT_ON;
		m_nBzrCnt = 2;
#else	// Else of (EN_61993_ED3)	
		m_dBuzzerTick = BUZZER_SHORT_ON_TICK;
		m_dBuzzerOffOn= BUZZER_PORT_ON;
#endif	// End of (EN_61993_ED3)		
	}
	
	if (m_dBuzzerMode == BUZZER_LONG)
	{
		m_dBuzzerTick = BUZZER_LONG_ON_TICK;
		m_dBuzzerOffOn= BUZZER_PORT_ON;
	}
	
	if (m_dBuzzerMode == BUZZER_SHORT_LONG)
	{
		m_dBuzzerTick = BUZZER_SHORT_LONG_ON_TICK;
		m_dBuzzerOffOn= BUZZER_PORT_ON;
	}
	
	if (m_dBuzzerMode == BUZZER_LONG_SHORT)
	{
		m_dBuzzerTick = BUZZER_LONG_SHORT_ON_TICK;
		m_dBuzzerOffOn= BUZZER_PORT_ON;
	}

#ifdef EN_61993_ED3
	if(m_dBuzzerMode == BUZZER_TWO_TIME_SHORT)
	{
		m_dBuzzerTick = BUZZER_SHORT_ON_TICK;
		m_dBuzzerOffOn= BUZZER_PORT_ON;
		m_nBzrCnt = 2;
	}
#endif	// End of (EN_61993_ED3)

	SetBuzzerPort();
}

DWORD cBUZZER::GetTxLedMode(void)
{
	return(m_dTxLedMode);
}

void cBUZZER::SetTxLedMode(DWORD dMode)
{
	m_dTxLedMode = dMode;
	if (m_dTxLedMode == LED_OFF)
	{
		m_dTxLedTick  = 0;
		m_dTxLedOffOn = LED_PORT_OFF;
	}
	
	if (m_dTxLedMode == LED_ON)
	{
		m_dTxLedTick  = LED_CONTINUE_ON_TICK;
		m_dTxLedOffOn = LED_PORT_ON;
	}
	
	if (m_dTxLedMode == LED_BLINK)
	{
		m_dTxLedTick  = LED_BLINK_ON_TICK;
		m_dTxLedOffOn = LED_PORT_ON;
	}
	
	if (m_dTxLedMode == LED_ON_TRIGGER)
	{
		m_dTxLedTick  = LED_BLINK_ON_TICK;
		m_dTxLedOffOn = LED_PORT_ON;
	}
	SetTxLedPort();
}

DWORD cBUZZER::GetRxLedMode(void)
{
	return(m_dRxLedMode);
}

void cBUZZER::SetRxLedMode(DWORD dMode)
{
	m_dRxLedMode = dMode;
	if (m_dRxLedMode == LED_OFF)
	{
		m_dRxLedTick  = 0;
		m_dRxLedOffOn = LED_PORT_OFF;
	}
	
	if (m_dRxLedMode == LED_ON)
	{
		m_dRxLedTick  = LED_CONTINUE_ON_TICK;
		m_dRxLedOffOn = LED_PORT_ON;
	}
	
	if (m_dRxLedMode == LED_BLINK)
	{
		m_dRxLedTick  = LED_BLINK_ON_TICK;
		m_dRxLedOffOn = LED_PORT_ON;
	}
	if (m_dRxLedMode == LED_ON_TRIGGER)
	{
		m_dRxLedTick  = LED_BLINK_ON_TICK;
		m_dRxLedOffOn = LED_PORT_ON;
	}
	SetRxLedPort();
}

DWORD cBUZZER::GetPwrLedMode(void)
{
	return(m_dPwrLedMode);
}

void cBUZZER::SetPwrLedMode(DWORD dMode)
{
	m_dPwrLedMode = dMode;
	if (m_dPwrLedMode == LED_OFF)
	{
		m_dPwrLedTick  = 0;
		m_dPwrLedOffOn = LED_PORT_OFF;
	}
	if (m_dPwrLedMode == LED_ON)
	{
		m_dPwrLedTick  = LED_CONTINUE_ON_TICK;
		m_dPwrLedOffOn = LED_PORT_ON;
	}
	if (m_dPwrLedMode == LED_BLINK)
	{
		m_dPwrLedTick  = LED_BLINK_ON_TICK;
		m_dPwrLedOffOn = LED_PORT_ON;
	}
	if (m_dPwrLedMode == LED_ON_TRIGGER)
	{
		m_dPwrLedTick  = LED_BLINK_ON_TICK;
		m_dPwrLedOffOn = LED_PORT_ON;
	}
	SetPwrLedPort();
}

void cBUZZER::SetKeyToneOn(void)
{
	if (m_dBuzzerMode == BUZZER_OFF)
	{
		m_dKeyToneTick = BUZZER_KEY_TONE_ON_TICK;
		m_dBuzzerOffOn = BUZZER_PORT_ON;
		SetBuzzerPort();
	}
}
void cBUZZER::SetBuzzerPort(void)
{
	if (m_dBuzzerOffOn == BUZZER_PORT_ON && GetPowerStatus())
	{
		SysSetGPIOxBitData((xSYS_GPIO *)GPIOB_PHSY_BASE_ADDR,BZR_GPIO_BIT_NO,1);
	}		
	else
	{
		SysSetGPIOxBitData((xSYS_GPIO *)GPIOB_PHSY_BASE_ADDR,BZR_GPIO_BIT_NO,0);
	}		
}

void cBUZZER::SetTxLedPort(void)
{
	if (m_dTxLedOffOn == LED_PORT_ON && GetPowerStatus())
	{
		SysSetGPIOxBitData((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR,TX_LED_BIT_NO,1);
	}		
	else
	{
		SysSetGPIOxBitData((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR,TX_LED_BIT_NO,0);
	}		
}

void cBUZZER::SetRxLedPort(void)
{
	if (m_dRxLedOffOn == LED_PORT_ON && GetPowerStatus())
	{
		SysSetGPIOxBitData((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR,RX_LED_BIT_NO,1);
	}		
	else
	{
		SysSetGPIOxBitData((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR,RX_LED_BIT_NO,0);
	}		
}

void cBUZZER::SetPwrLedPort(void)
{
	if (m_dPwrLedOffOn == LED_PORT_ON && GetPowerStatus())
	{
		//SysSetALIVExBitData(PWR_LED_ON_BIT_NO,1);
	}		
	else
	{
		//SysSetALIVExBitData(PWR_LED_ON_BIT_NO,0);
	}		
}

void cBUZZER::SetBuzzerBeepOff(void)
{
	m_nBeepMode = BUZZER_BEEP_MODE_OFF;
}
void cBUZZER::SetBuzzerBeepOn(void)
{
	m_nBeepMode = BUZZER_BEEP_MODE_ON;
}

void cBUZZER::RunIntHandler(void)
{

	if (m_dKeyToneTick)
	{
		--m_dKeyToneTick;
		if (m_dKeyToneTick == 0 && m_dBuzzerMode == BUZZER_OFF)
		{
			m_dBuzzerOffOn = BUZZER_PORT_OFF;
			SetBuzzerPort();
		}
	}
	
	if (m_dBuzzerMode != BUZZER_OFF)
	{
		if (m_dBuzzerTick)
		--m_dBuzzerTick;
		if (m_dBuzzerTick == 0)
		{
			if (m_dBuzzerOffOn == BUZZER_PORT_ON)
			{
				m_dBuzzerOffOn = BUZZER_PORT_OFF;
				if (m_dBuzzerMode == BUZZER_SHORT)       m_dBuzzerTick = BUZZER_SHORT_OFF_TICK;
				if (m_dBuzzerMode == BUZZER_LONG)        m_dBuzzerTick = BUZZER_LONG_OFF_TICK;
				if (m_dBuzzerMode == BUZZER_SHORT_LONG)  m_dBuzzerTick = BUZZER_SHORT_LONG_OFF_TICK;
				if (m_dBuzzerMode == BUZZER_LONG_SHORT)  m_dBuzzerTick = BUZZER_LONG_SHORT_OFF_TICK;
#ifdef EN_61993_ED3
				if (m_dBuzzerMode == BUZZER_TWO_TIME_SHORT)
				{
					m_nBzrCnt--;
					m_dBuzzerTick = BUZZER_SHORT_OFF_TICK;
				}					
#endif	// End of (EN_61993_ED3)
				
			}
			else
			{
				m_dBuzzerOffOn = BUZZER_PORT_ON;
				if (m_dBuzzerMode == BUZZER_SHORT)       m_dBuzzerTick = BUZZER_SHORT_ON_TICK;
				if (m_dBuzzerMode == BUZZER_LONG)        m_dBuzzerTick = BUZZER_LONG_ON_TICK;
				if (m_dBuzzerMode == BUZZER_SHORT_LONG)  m_dBuzzerTick = BUZZER_SHORT_LONG_ON_TICK;
				if (m_dBuzzerMode == BUZZER_LONG_SHORT)  m_dBuzzerTick = BUZZER_LONG_SHORT_ON_TICK;
#ifdef EN_61993_ED3
				if (m_dBuzzerMode == BUZZER_TWO_TIME_SHORT)  
				{
					if(m_nBzrCnt <= 0)
					{
						m_dBuzzerMode = BUZZER_OFF;
						m_dBuzzerTick = 0;
						m_dBuzzerOffOn= BUZZER_PORT_OFF;
					}
					else
					{
						m_dBuzzerTick = BUZZER_SHORT_ON_TICK;
					}						
				}					
#endif	// End of (EN_61993_ED3)			
			}
			if (m_dBuzzerMode == BUZZER_CONTINUE)
			{
				m_dBuzzerOffOn = BUZZER_PORT_ON;
				m_dBuzzerTick = BUZZER_CONTINUE_ON_TICK;
			}
			SetBuzzerPort();
		}
	}
	
	if (m_dTxLedMode != LED_OFF)
	{
		if (m_dTxLedTick)
			--m_dTxLedTick;
		
		if (m_dTxLedTick == 0)
		{
			if (m_dTxLedMode == LED_ON_TRIGGER)
			{
				m_dTxLedOffOn = LED_PORT_OFF;
				m_dTxLedTick  = 0;
				m_dTxLedMode  = LED_OFF;
			}
			else
			{
				if (m_dTxLedOffOn == LED_PORT_ON)
				{
					m_dTxLedOffOn = LED_PORT_OFF;
					m_dTxLedTick  = LED_BLINK_OFF_TICK;
				}
				else
				{
					m_dTxLedOffOn = LED_PORT_ON;
					m_dTxLedTick  = LED_BLINK_ON_TICK;
				}
				
				if (m_dTxLedMode == LED_ON)
				{
					m_dTxLedOffOn = LED_PORT_ON;
					m_dTxLedTick  = LED_CONTINUE_ON_TICK;
				}
			}
			SetTxLedPort();
		}
	}
	
	if (m_dRxLedMode != LED_OFF)
	{
		if (m_dRxLedTick)
			--m_dRxLedTick;
		
		if (m_dRxLedTick == 0)
		{
			if (m_dRxLedMode == LED_ON_TRIGGER)
			{
				m_dRxLedOffOn = LED_PORT_OFF;
				m_dRxLedTick  = 0;
				m_dRxLedMode  = LED_OFF;
			}
			else
			{
				if (m_dRxLedOffOn == LED_PORT_ON)
				{
					m_dRxLedOffOn = LED_PORT_OFF;
					m_dRxLedTick  = LED_BLINK_OFF_TICK;
				}
				else
				{
					m_dRxLedOffOn = LED_PORT_ON;
					m_dRxLedTick  = LED_BLINK_ON_TICK;
				}
				
				if (m_dRxLedMode == LED_ON)
				{
					m_dRxLedOffOn = LED_PORT_ON;
					m_dRxLedTick  = LED_CONTINUE_ON_TICK;
				}
			}
			SetRxLedPort();
		}
	}
	
	if (m_dPwrLedMode != LED_OFF)
	{
		if (m_dPwrLedTick)
			--m_dPwrLedTick;
		
		if (m_dPwrLedTick == 0)
		{
			if (m_dPwrLedMode == LED_ON_TRIGGER)
			{
				m_dPwrLedOffOn = LED_PORT_OFF;
				m_dPwrLedTick  = 0;
				m_dPwrLedMode  = LED_OFF;
			}
			else
			{
				if (m_dPwrLedOffOn == LED_PORT_ON)
				{
					m_dPwrLedOffOn = LED_PORT_OFF;
					m_dPwrLedTick  = LED_BLINK_OFF_TICK;
				}
				else
				{
					m_dPwrLedOffOn = LED_PORT_ON;
					m_dPwrLedTick  = LED_BLINK_ON_TICK;
				}
				
				if (m_dPwrLedMode == LED_ON)
				{
					m_dPwrLedOffOn = LED_PORT_ON;
					m_dPwrLedTick  = LED_CONTINUE_ON_TICK;
				}
			}
			SetPwrLedPort();
		}
	}
}

