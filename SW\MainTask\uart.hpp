/*...........................................................................*/
/*.                  File Name : UART.HPP                                   .*/
/*.                                                                         .*/
/*.                       Date : 2004.02.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"

#ifndef  __UART_HPP
#define  __UART_HPP

//-----------------------------------------------------------------------------
#define  UART_0                           0
#define  UART_1                           1
#define  UART_2                           2
#define  UART_3                           3
//-----------------------------------------------------------------------------
#define  UART_RX_BUFF_SIZE             4096
#define  UART_TX_BUFF_SIZE             4096
//-----------------------------------------------------------------------------
#if defined(_EN_DBG_MSG_)
#define  UART_DBG_MSG_BUF_SIZE		   1024
#endif

#define  UART_NULL_CHAR                  -1

class cUART
{
   private:
      DWORD m_dBaseAddr;
      volatile int   m_nRxHead;
      volatile int   m_nRxTail;
      volatile int   m_nTxHead;
      volatile int   m_nTxTail;
      volatile int   m_nRxTemp;
      volatile int   m_nSending;
      BYTE *m_pRxBuff;
      BYTE *m_pTxBuff;
      BYTE *m_pRxData;
	  
#if defined(_EN_DBG_MSG_)
	  BYTE *m_pDbgMsgBuf;
#endif

      xSYS_UART *m_pSysUART;

   public:
      cUART(DWORD dBaseAddr,int Speed,BYTE Parity,int DataBit,int StopBit,int TxInt,int RxInt);
      virtual ~cUART(void);
   public:
      void  SetUartPara(int Speed,BYTE Parity,int DataBit,int StopBit,int TxInt,int RxInt);
      int   GetComData(void);
      void  PutComData(BYTE Data);
      int   ReadComData(BYTE *pData,int nSize);
      void  WriteComData(const BYTE *pData,int nSize);
#if defined(_EN_DBG_MSG_)	  
	  void  OutputDbgMsg(const char *format,...);
#endif
      int   IsSendingData(void);
      void  RunRxIntHandler(void);
      void  RunTxIntHandler(void);
      void  RunUartIsrHandler(void);

      BYTE  GetUartParaParity(BYTE Parity);
      int   GetUartParaDataBit(int DataBit);
      int   GetUartParaStopBit(int StopBit);
};

#endif

