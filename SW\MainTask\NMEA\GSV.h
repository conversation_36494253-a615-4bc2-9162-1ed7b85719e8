#ifndef __NMEA_0183_GSV_H__
#define __NMEA_0183_GSV_H__

#include "Sentence.hpp"

/******************************************************************************
 * 
 * CGsv - GNSS Satellites in View
 * 
 * $--CGsv,x,x,xx,xx,xx,xxx,xx,................,xx,xx,xxx,xx*hh&lt;CR&gt;&lt;LF&gt;
 *        | | |  |   |  |  |      |            |  |   |  |
 *        1 2 3  4   5  6  7     n~m         m+1 m+2 m+3 m+4
 *
 *	1. Total number of sentence, 1 to 9
 *  2. Sentence number, 1 to 9
 *  3. Total number of satellites in view
 *  4. Satellites ID number
 *  5. Elevation, degrees, 90 maximum
 *  6. Azimuth, degree True, 000 to 359
 *  7. SNR(C/No) 00-99 dB-Hz, null when not tracking
 *  n ~ m. 2nd, 3rd SV
 *  m+1 ~ m+4. 4th SV
 *
 ******************************************************************************/

class CGsv : public CSentence {
public:
	CGsv();
	CGsv(char *pszSentence);
	~CGsv() {}
	
	void Parse();
	void SetSentence(char *pszSentence);
	void GetPlainText(char *pszPlainText);

	int  GetTotalSentNumber()   { return m_nTotalSentNumber; }
	int  GetSentNumber()        { return m_nSentNumber; }
	int  GetTotalSVNumber()     { return m_nTotalSVNumber; }
	int  GetSatID(int nIdx)     { return m_nSatID[nIdx]; }
	int  GetElevation(int nIdx) { return m_nElevation[nIdx]; }
	int  GetAzimuth(int nIdx)   { return m_nAzimuth[nIdx]; }
	int  GetSNR(int nIdx)       { return m_nSNR[nIdx]; }
	int  GetSVCountInSent()     { return m_nSVCountInSent; }

protected:
	int m_nTotalSentNumber;
	int m_nSentNumber;
	int m_nTotalSVNumber;
	int m_nSVCountInSent;
	int m_nSatID[4];
	int m_nElevation[4];
	int m_nAzimuth[4];
	int m_nSNR[4];
};

#endif ///:~ __NMEA_0183_GSV_H__
