/*...........................................................................*/
/*.                  File Name : SYSLIB.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#include <sys\stat.h>

#ifndef  __SYSLIB_H__
#define  __SYSLIB_H__

#ifdef  __cplusplus
extern "C" {
#endif

//========================================================================
long  SysGetStartOfHeapMemory(void);
long  SysGetLastOfHeapMemory(void);
long  SysGetStartOfFreeMemory(void);
void  SysDisableICache(void);
void  SysEnableICache(void);
void  SysDisableDCache(void);
void  SysEnableDCache(void);
void  SysDisableAlignFault(void);
void  SysEnableAlignFault(void);
void  SysDisableMMU(void);
void  SysEnableMMU(void);
void  SysSetFastBusMode(void);
void  SysSetAsyncBusMode(void);
#ifdef  __POLLUX__
void  SysSetTTBase(DWORD dBase);
#else                           // SPICA
void  SysSetTTBase0(DWORD dBase);
void  SysSetTTBase1(DWORD dBase);
void  SysSetTTBaseControl(DWORD dCtrl);
#endif
void  SysSetDomain(DWORD dDomain);
void  SysInvalidateICache(void);
void  SysInvalidateICacheMVA(DWORD dMVA);
void  SysPrefetchICacheMVA(DWORD dMVA);
void  SysInvalidateDCache(void);
void  SysInvalidateDCacheMVA(DWORD dMVA);
void  SysInvalidateIDCache(void);
void  SysCleanDCacheMVA(DWORD dMVA);
void  SysCleanInvalidateDCacheMVA(DWORD dMVA);
void  SysCleanDCacheIndex(DWORD nIndex);
void  SysCleanInvalidateDCacheIndex(DWORD nIndex);
void  SysCleanAllDCache926(void);
void  SysCleanFlushAllDCache926(void);
void  SysWaitForInterrupt(void);
void  SysInvalidateTLB(void);
void  SysInvalidateITLB(void);
void  SysInvalidateITLBMVA(DWORD dMVA);
void  SysInvalidateDTLB(void);
void  SysInvalidateDTLBMVA(DWORD dMVA);
void  SysSetDCacheLockdownBase(DWORD dBase);
void  SysSetICacheLockdownBase(DWORD dBase);
void  SysSetDTLBLockdown(DWORD dBase);
void  SysSetITLBLockdown(DWORD dBase);
void  SysSetProcessID(DWORD dID);
void  SysSetResetVectorLow(void);
void  SysSetResetVectorHigh(void);
#ifdef  __SPICA__
void  SysSetNormalVectorBaseAddr(DWORD dBaseAddr);
void  SysSetMonitorVectorBaseAddr(DWORD dBaseAddr);
DWORD SysGetInterruptStatusRegister(void);
void  SysSetPeriPortReMapRegister(DWORD dBaseAddr);
#endif
void  SysEnableIRQ(void);
void  SysDisableIRQ(void);
void  SysEnableFIQ(void);
void  SysDisableFIQ(void);
//========================================================================
DWORD SysSaveStatusRegInCPU(void);
void  SysRestStatusRegInCPU(DWORD dStatusReg);
//========================================================================

//========================================================================
typedef struct _MEMLINK {
     struct _MEMLINK *pNext;
     struct _MEMLINK *pPrev;
     DWORD  dSize;
     HWORD  wFixed;
     HWORD  wUsed;
    }MEMLINK;
typedef struct {
     MEMLINK *pMemLink;
     void    *pStart;
     DWORD    dSize;
    }MEMBLOCK;
//========================================================================
#define MEMORY_ALIGN   16
#define MEMLINK_SIZE   ((sizeof(MEMLINK) % MEMORY_ALIGN == 0) ? \
                        sizeof(MEMLINK) : \
                        ((sizeof(MEMLINK) / MEMORY_ALIGN + 1) * 16))
//========================================================================

//========================================================================
void  SysInitFreeMemory(void);
DWORD SysGetAllFreeMemorySize(void);
DWORD SysGetMaxFreeMemorySize(void);
int   SysMakeMemoryTree(DWORD *pMemTree);
int   SysCheckMemoryTree(DWORD *pErrorMem,MEMLINK *pMemLink);
int   SysGetNumberOfFragment(void);
DWORD SysGetLastFreeAddress(DWORD *pBlockSize);
int   SysRemoveMemFragment(void);
//========================================================================
void  *__wrap_malloc(DWORD nSize);
void  __wrap_free(void *pMemAddr);
void  *__wrap_calloc(DWORD nItems,DWORD dSize);
void  __wrap_abort(void);
int   __wrap_open(char *Path,int Flags);
int   __wrap_close(int file);
int   __wrap_fstat(int file,struct stat *st);
int   __wrap_isatty(int file);
int   __wrap_lseek(int file,int ptr,int dir);
int   __wrap_read(int file,char *ptr,int len);
caddr_t __wrap_sbrk(int incr);
int   __wrap_write(int file,char *ptr,int len);
int   __wrap_fputc(int character,void *stream);
int   __wrap_fputs(const char *str,void *stream);
int   __wrap_puts(const char *);
int   __wrap_printf(const char *format,...);
//========================================================================
int   __wrap__open_r(void *REENT,const char *file,int flags,int mode);
int   __wrap__close_r(void *REENT,int FD);
int   __wrap__fstat_r(void *REENT,int FD,struct stat *PSTAT);
off_t __wrap__lseek_r(void *REENT,int FD,off_t POS,int WHENCE);
long  __wrap__read_r(void *REENT,int FD,void *BUF,size_t CNT);
char *__wrap__sbrk_r(void *REENT, size_t INCR);
long  __wrap__write_r(void *REENT,int FD, const void *BUF, size_t CNT);
//========================================================================
void  SysCheckCpuType(void);
void  SysSetCpuType(int nCpuType);
int   SysGetCpuType(void);
void  SysCheckDeviceType(void);
void  SysSetDeviceType(int nDeviceType);
int   SysGetDeviceType(void);
void  SysCheckNavisModelType(void);
void  SysSetNavisModelType(int nNavisType);
int   SysGetNavisModelType(void);
#ifdef  __SPICA__
void  SysSetSGP330ScrnMode(int nScrnMode);
int   SysGetSGP330ScrnMode(void);
#endif
DWORD SysGetRadarScreenAddress(void);
DWORD SysGetChartScreenAddress(void);
DWORD SysGetMenuScreenAddress(void);
DWORD SysGetScreenAddress(void);
DWORD SysGetScreenWidth(void);
DWORD SysGetScreenHeight(void);
DWORD SysGetScreenWidthByDevice(int nDeviceType);
DWORD SysGetScreenHeightByDevice(int nDeviceType);
//========================================================================
void  SysDelayLoop(volatile DWORD dDelayCnt);
void  SysDelayMicroSec(DWORD dDelayMicro);
void  SysDelayMiliSec(DWORD dDelayMili);
DWORD SysGetSystemTimer(void);
DWORD SysIncSystemTimer(void);
DWORD SysCalcTickToMili(DWORD dTick);
DWORD SysCalcMiliToTick(DWORD dMili);
DWORD SysGetDiffTimeTick(DWORD dTime);
DWORD SysGetDiffTimeMili(DWORD dTime);
//========================================================================
void  SysSetUTCDate(int nYear,int nMonth,int nDay);
void  SysGetUTCDate(int *pYear,int *pMonth,int *pDay);
void  SysSetUTCTime(int nHour,int nMinute,int nSecond);
void  SysGetUTCTime(int *pHour,int *pMinute,int *pSecond);
void  SysSetLOCDate(int nYear,int nMonth,int nDay);
void  SysGetLOCDate(int *pYear,int *pMonth,int *pDay);
void  SysSetLOCTime(int nHour,int nMinute,int nSecond);
void  SysGetLOCTime(int *pHour,int *pMinute,int *pSecond);
//========================================================================
void  SysSetAlphaBlendValue(UCHAR bAlpha);
UCHAR SysGetAlphaBlendValue(void);
void  SysSetOriginAlphaBlendValue(void);
void  SysSetBrightValue(int nPercent);
int   SysGetBrightValue(void);
void  SysSetPaletteData(int nSize,DWORD *pColorTable);
//========================================================================
void  SysRunSystemPowerOff(void);
void  SysRunSystemPowerOn(void);
//========================================================================
void  SysSetNavis5100AModelMode(int nMode);
int   SysGetNavis5100AModelMode(void);
//========================================================================
int   SysCheckLCD3800Mitsubishi(void);
//========================================================================

#ifdef  __cplusplus
}
#endif

#endif

