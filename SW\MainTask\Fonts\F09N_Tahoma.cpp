//####################################################################
//@ FontFace:Tahoma
//@ FontSize:9
//@ RangeCount:1
//@ Range:0x0000~0x007F
//####################################################################
#include "Type.hpp"
#include "Font.h"

HWORD F09N_Tahoma_offset_table[129] = {
0x0000,0x0006,0x000a,0x000e,0x0012,0x0016,0x001a,0x001e,0x0027,0x002c,0x0032,0x0038,0x0041,0x004a,0x0053,0x005c,
0x0065,0x006e,0x0077,0x0080,0x0089,0x0092,0x009b,0x00a4,0x00ad,0x00b6,0x00bf,0x00c8,0x00d1,0x00d7,0x00dd,0x00e3,
0x00e9,0x00ef,0x00f2,0x00f7,0x0100,0x0106,0x010c,0x0112,0x0115,0x0119,0x011d,0x0123,0x0129,0x012c,0x0130,0x0134,
0x0139,0x013f,0x0144,0x014a,0x0150,0x0156,0x015b,0x0161,0x0167,0x016d,0x0173,0x0176,0x0179,0x017f,0x0183,0x0189,
0x018f,0x0197,0x019d,0x01a3,0x01a9,0x01af,0x01b5,0x01bb,0x01c1,0x01c7,0x01cb,0x01cf,0x01d5,0x01db,0x01e1,0x01e7,
0x01ed,0x01f3,0x01fa,0x0200,0x0206,0x020c,0x0212,0x0218,0x0220,0x0226,0x022c,0x0232,0x0236,0x023b,0x023f,0x0245,
0x024a,0x024e,0x0253,0x0258,0x025d,0x0262,0x0267,0x026c,0x0271,0x0276,0x0279,0x027c,0x0281,0x0285,0x028b,0x0290,
0x0295,0x029a,0x029f,0x02a4,0x02a9,0x02ad,0x02b2,0x02b8,0x02be,0x02c4,0x02ca,0x02d0,0x02d5,0x02d8,0x02dd,0x02e3,
0x02e8 };

BYTE F09N_Tahoma_data_table[837] = {
0x00, 0x01, 0x08, 0x80, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0x24, 
0x21, 0x06, 0x11, 0x40, 0x00, 0x00, 0x01, 0x38, 0x47, 0x1c, 0x13, 0xce, 0x7c, 0xe3, 0x80, 0x00, 
0x00, 0x38, 0x70, 0x47, 0x8e, 0x79, 0xf7, 0xce, 0x45, 0xcd, 0x14, 0x11, 0x44, 0xe7, 0x8e, 0x3c, 
0x73, 0xe8, 0xa2, 0x82, 0x8a, 0x2f, 0xba, 0x1c, 0x40, 0x10, 0x10, 0x00, 0x80, 0x60, 0x41, 0x28, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc0, 0x1e, 

0x00, 0x01, 0x08, 0x80, 0x01, 0x20, 0x00, 0x00, 0x70, 0x00, 0x00, 0x06, 0x03, 0x00, 0x00, 0xc0, 
0x00, 0x78, 0x18, 0x00, 0x06, 0x03, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0x24, 
0x7a, 0xa9, 0x12, 0x24, 0x44, 0x00, 0x01, 0x44, 0xc8, 0xa2, 0x32, 0x11, 0x05, 0x14, 0x40, 0x0c, 
0x18, 0x44, 0x88, 0x44, 0x51, 0x45, 0x04, 0x11, 0x44, 0x85, 0x24, 0x11, 0x65, 0x14, 0x51, 0x22, 
0x88, 0x88, 0xa2, 0x82, 0x8a, 0x20, 0xa2, 0x04, 0xa0, 0x08, 0x10, 0x00, 0x80, 0x80, 0x40, 0x08, 
0x20, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x42, 0x20, 0x12, 

0x00, 0x01, 0x08, 0x80, 0x01, 0x20, 0x00, 0x70, 0x78, 0x00, 0x0e, 0x0f, 0x03, 0x00, 0xc1, 0xe0, 
0xe0, 0x78, 0x18, 0x00, 0x06, 0x07, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xfe, 
0xa1, 0x49, 0x04, 0x12, 0x84, 0x00, 0x02, 0x44, 0x40, 0x82, 0x53, 0x90, 0x09, 0x14, 0x49, 0x31, 
0xc6, 0x05, 0x34, 0xa4, 0x50, 0x45, 0x04, 0x10, 0x44, 0x85, 0x44, 0x1b, 0x65, 0x14, 0x51, 0x22, 
0x80, 0x88, 0xa2, 0x82, 0x51, 0x41, 0x21, 0x05, 0x10, 0x03, 0x9c, 0x73, 0x98, 0x87, 0x73, 0x69, 
0x27, 0x9c, 0x67, 0x1d, 0x67, 0x74, 0xa2, 0xaa, 0x28, 0xbe, 0x42, 0x22, 0x12, 

0x00, 0x01, 0x08, 0x83, 0xc0, 0xc0, 0x00, 0x60, 0x78, 0x00, 0x1e, 0x0f, 0x03, 0x03, 0xc0, 0xc0, 
0xe0, 0x78, 0x18, 0x00, 0x06, 0x07, 0x81, 0x80, 0xc0, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x80, 0x48, 
0x60, 0x86, 0x04, 0x17, 0xdf, 0x0e, 0x02, 0x44/*0x54*/, 0x41, 0x1c, 0x90, 0x5e, 0x10, 0xe3, 0xc0, 0x40, 
0x01, 0x09, 0x54, 0xa7, 0x90, 0x45, 0xe7, 0x93, 0x7c, 0x85, 0x84, 0x1b, 0x55, 0x17, 0x91, 0x3c, 
0x70, 0x88, 0x94, 0x54, 0x21, 0x42, 0x21, 0x04, 0x00, 0x00, 0x52, 0x84, 0xa5, 0xc9, 0x49, 0x2a, 
0x25, 0x52, 0x94, 0xa5, 0x88, 0x24, 0xa2, 0xa9, 0x48, 0x84, 0xc2, 0x35, 0x52, 

0x00, 0xf9, 0xf8, 0xbf, 0xc0, 0x00, 0x00, 0xe0, 0x70, 0x00, 0x1e, 0x0f, 0x07, 0x83, 0xc0, 0xc0, 
0xe0, 0x78, 0x3c, 0x1e, 0x0e, 0x03, 0x01, 0xc1, 0xe0, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x81, 0xfc, 
0x31, 0x49, 0x84, 0x12, 0x84, 0x00, 0x04, 0x44, 0x42, 0x02, 0xf8, 0x51, 0x11, 0x10, 0x49, 0x31, 
0xc6, 0x11, 0x55, 0xf4, 0x50, 0x45, 0x04, 0x11, 0x44, 0x85, 0x44, 0x15, 0x4d, 0x14, 0x11, 0x28, 
0x08, 0x88, 0x94, 0x54, 0x50, 0x84, 0x20, 0x84, 0x00, 0x01, 0xd2, 0x84, 0xbc, 0x89, 0x49, 0x2c, 
0x25, 0x52, 0x94, 0xa5, 0x06, 0x24, 0x94, 0xa8, 0x85, 0x08, 0x42, 0x20, 0x92, 

0x00, 0x08, 0x00, 0x83, 0xc0, 0x00, 0x00, 0xe0, 0x78, 0x00, 0x1e, 0x0f, 0x03, 0x01, 0xc0, 0xc0, 
0x00, 0x38, 0x00, 0x0c, 0x06, 0x03, 0x01, 0x80, 0xc0, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 
0x2a, 0xa9, 0x04, 0x14, 0x44, 0x20, 0x04, 0x44, 0x44, 0x22, 0x10, 0x51, 0x21, 0x14, 0x42, 0x0c, 
0x18, 0x00, 0xb9, 0x14, 0x51, 0x45, 0x04, 0x11, 0x44, 0x85, 0x24, 0x15, 0x4d, 0x14, 0x13, 0x24, 
0x88, 0x88, 0x94, 0x28, 0x88, 0x88, 0x20, 0x84, 0x00, 0x02, 0x52, 0x84, 0xa0, 0x87, 0x49, 0x2a, 
0x25, 0x52, 0x94, 0xa5, 0x01, 0x24, 0x94, 0x51, 0x45, 0x10, 0x42, 0x20, 0x12, 

0x00, 0x08, 0x00, 0x80, 0x00, 0x00, 0x00, 0xe0, 0x30, 0x00, 0x1e, 0x0f, 0x03, 0x00, 0xc0, 0xc0, 
0xe0, 0x38, 0x00, 0x0c, 0x06, 0x03, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x90, 
0xf0, 0x47, 0x82, 0x20, 0x00, 0x40, 0x48, 0x38, 0xef, 0x9c, 0x13, 0x8e, 0x20, 0xe3, 0x80, 0x00, 
0x00, 0x10, 0x71, 0x17, 0x8e, 0x79, 0xf4, 0x0f, 0x45, 0xd9, 0x17, 0xd1, 0x44, 0xe4, 0x0f, 0x22, 
0x70, 0x87, 0x88, 0x28, 0x88, 0x8f, 0xa0, 0x44, 0x07, 0xc3, 0xdc, 0x73, 0x9c, 0x81, 0x49, 0x29, 
0x15, 0x52, 0x67, 0x1d, 0x0e, 0x13, 0x88, 0x52, 0x22, 0x3e, 0x32, 0xc0, 0x1e, 

0x00, 0x08, 0x00, 0x80, 0x00, 0x00, 0x00, 0xe0, 0x30, 0x00, 0x00, 0x0f, 0x03, 0x00, 0x01, 0xe0, 
0x00, 0x38, 0x00, 0x0c, 0x06, 0x03, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x00, 0x01, 0x40, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x5c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x20, 
0x00, 0x00, 0x04, 0x04, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x08, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x04, 0x04, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 

};

FONT F09N_Tahoma = {
	0x01, 9, 0, 9, 93, 0x0000, 0x007f,
	(HWORD *)F09N_Tahoma_offset_table, 0,
	(BYTE *)F09N_Tahoma_data_table
};

