/*...........................................................................*/
/*.                  File Name : Tahoma09nArabic.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2014.04.03                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY NewGulLim12bKor_Font;


ROMDATA PEGUSHORT Tahoma09nArabic4_offset_table[145] = {
0x0000,0x000f,0x001e,0x002d,0x003c,0x004b,0x005a,0x0069,0x0078,0x0087,0x0096,0x00a5,0x00b4,0x00c3,0x00d2,0x00e1,
0x00f0,0x00f6,0x00fa,0x00fe,0x0102,0x0106,0x010c,0x0113,0x0117,0x011b,0x0128,0x0137,0x013c,0x0142,0x0146,0x014a,
0x0156,0x0164,0x0169,0x016f,0x0176,0x017f,0x018b,0x0199,0x019e,0x01a4,0x01b0,0x01be,0x01c3,0x01c9,0x01d2,0x01dd,
0x01e8,0x01f3,0x01fc,0x0207,0x0212,0x021d,0x0226,0x0231,0x023c,0x0247,0x024e,0x0256,0x025d,0x0265,0x026a,0x0270,
0x0275,0x027b,0x028d,0x02a0,0x02b0,0x02c1,0x02d3,0x02e6,0x02f6,0x0307,0x0318,0x0329,0x0336,0x0344,0x0355,0x0366,
0x0373,0x0381,0x038c,0x0398,0x03a3,0x03af,0x03ba,0x03c6,0x03d1,0x03dd,0x03e6,0x03ef,0x03f9,0x0401,0x040a,0x0413,
0x041d,0x0425,0x0432,0x0440,0x0447,0x0450,0x045d,0x046a,0x0471,0x047a,0x0485,0x0491,0x0499,0x04a2,0x04ac,0x04b7,
0x04bc,0x04c2,0x04cb,0x04d5,0x04de,0x04e8,0x04f2,0x04fd,0x0502,0x0508,0x050f,0x0518,0x0523,0x052c,0x0532,0x0539,
0x0546,0x0555,0x0562,0x0571,0x0576,0x057c,0x0587,0x0594,0x059e,0x05aa,0x05b3,0x05be,0x05c7,0x05d2,0x05e1,0x05f0,
0x05ff};



ROMDATA PEGUBYTE Tahoma09nArabic4_data_table[3456 + 192] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x55, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xab, 
0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 
0x00, 0xa0, 0x04, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 
0x01, 0x40, 0x0e, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x01, 0x11, 
0x10, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x01, 0x00, 0x10, 0x02, 0x00, 0x20, 0x04, 0x00, 0x40, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x01, 0x40, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x02, 0x01, 0x01, 0x00, 0x20, 0x08, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x80, 0x08, 0x02, 0x00, 0x40, 0x20, 0x04, 0x02, 0x0f, 0xfe, 0x1f, 0xfc, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x01, 0x11, 
0x10, 0x41, 0x08, 0x80, 0x00, 0x00, 0x00, 0x10, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xa0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x01, 0x00, 0x10, 0x02, 0x00, 0x20, 0x04, 0x00, 0x40, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 
0x10, 0x02, 0x06, 0x06, 0x00, 0x20, 0x08, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x04, 0x40, 0x84, 0x09, 0x02, 0x20, 0x50, 0x22, 0x05, 0x02, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x01, 0x11, 
0x10, 0x82, 0x08, 0x80, 0x80, 0x00, 0x00, 0x20, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x02, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x04, 0x00, 0x00, 0x80, 0x00, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x40, 0x00, 0x00, 0x00, 
0x08, 0x01, 0x00, 0x10, 0x02, 0x00, 0x24, 0x04, 0x80, 0x48, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x40, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 
0x10, 0x02, 0x18, 0x18, 0x00, 0x20, 0x08, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x04, 0x20, 0x82, 0x08, 0x82, 0x10, 0x48, 0x21, 0x04, 0x82, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x01, 0x11, 
0x11, 0xc7, 0x08, 0x81, 0x1c, 0x04, 0x00, 0x70, 0x91, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x01, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x40, 
0x08, 0x01, 0x00, 0x10, 0x02, 0x00, 0x20, 0x04, 0x00, 0x40, 0x08, 0x00, 0xe0, 0x00, 0x00, 0x00, 
0x0e, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x60, 0x00, 0x00, 0xc0, 0x05, 0x18, 0x00, 0x01, 
0x10, 0x22, 0x20, 0x20, 0x00, 0x20, 0x08, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x80, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x01, 
0x04, 0x20, 0x82, 0x08, 0x82, 0x10, 0x48, 0x21, 0x04, 0x82, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x01, 0x11, 
0x10, 0x00, 0x08, 0x83, 0xa2, 0x08, 0x00, 0x01, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x70, 
0x0a, 0x00, 0xa0, 0x00, 0xa0, 0x50, 0x05, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x80, 0x80, 0x00, 0x21, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 0x0a, 0x00, 0x01, 0x40, 0x01, 
0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x01, 0x00, 0x10, 0x02, 0x00, 0x20, 0x04, 0x00, 0x40, 0x08, 0x01, 0x00, 0x70, 0x1c, 0x00, 
0x10, 0x07, 0x01, 0xc0, 0x00, 0x09, 0x00, 0x00, 0x90, 0x00, 0x01, 0x20, 0x00, 0x24, 0x00, 0x02, 
0x10, 0x42, 0x20, 0x20, 0x00, 0x20, 0x08, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x20, 0x38, 0x01, 0x00, 0x00, 0x00, 0x00, 0x88, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 
0x84, 0x10, 0x81, 0x08, 0x42, 0x08, 0x44, 0x20, 0x84, 0x42, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x31, 0x11, 
0x11, 0x86, 0x08, 0x80, 0x20, 0x1c, 0x60, 0x03, 0x91, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x90, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x0f, 0x01, 0xe0, 0x3c, 0x07, 0x83, 
0xc0, 0x78, 0x0f, 0x01, 0xe0, 0xf0, 0x1e, 0x03, 0xc0, 0x20, 0x20, 0x40, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x70, 0x03, 0x80, 0x00, 0xe0, 0x00, 0x70, 0x03, 0x80, 0x1c, 
0x09, 0xc1, 0x38, 0x13, 0x82, 0x70, 0x27, 0x04, 0xe0, 0x4e, 0x09, 0xc1, 0x00, 0x88, 0x22, 0x1e, 
0x10, 0x08, 0x82, 0x21, 0xe0, 0x08, 0x80, 0x1c, 0x88, 0x3c, 0x01, 0x10, 0x00, 0x22, 0x0f, 0x01, 
0x10, 0x22, 0x18, 0x18, 0x00, 0x20, 0x08, 0x21, 0x03, 0x81, 0xc0, 0x70, 0x38, 0x40, 0x80, 0x00, 
0x00, 0x10, 0x48, 0x1c, 0x81, 0x86, 0x18, 0x00, 0x80, 0x01, 0x80, 0x08, 0x00, 0x18, 0x00, 0x00, 
0x84, 0x10, 0x81, 0x08, 0x42, 0x08, 0x44, 0x20, 0x84, 0x42, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x41, 0x11, 
0x12, 0x49, 0x08, 0x88, 0x18, 0x00, 0x90, 0x20, 0x11, 0x10, 0x09, 0x00, 0x01, 0x00, 0x51, 0x10, 
0x80, 0x48, 0x00, 0x08, 0x04, 0x02, 0x40, 0x00, 0x40, 0x03, 0x81, 0xe0, 0x1a, 0x03, 0x40, 0xe0, 
0x78, 0x06, 0x80, 0xd0, 0x38, 0x1e, 0x01, 0xa0, 0x34, 0x10, 0x20, 0x20, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x10, 0x00, 0x04, 0x00, 0x02, 0x00, 0x02, 0x00, 0x00, 0x40, 0x00, 0x10, 0x00, 0x08, 0x00, 
0x08, 0x00, 0x22, 0x00, 0x11, 0x00, 0x88, 0x04, 0x40, 0x01, 0x10, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x0a, 0x21, 0x44, 0x14, 0x42, 0x88, 0x28, 0x85, 0x10, 0x51, 0x0a, 0x21, 0x30, 0x88, 0x40, 0x21, 
0x13, 0x08, 0x84, 0x02, 0x12, 0x08, 0x90, 0x22, 0x88, 0x66, 0x01, 0x10, 0x06, 0x22, 0x19, 0x82, 
0x10, 0x42, 0x04, 0x04, 0x10, 0x20, 0x08, 0x21, 0x04, 0x42, 0x20, 0x88, 0x44, 0x40, 0x82, 0x00, 
0x80, 0x28, 0x88, 0x22, 0x82, 0x49, 0x24, 0x20, 0x60, 0x02, 0x42, 0x06, 0x00, 0x24, 0x08, 0x00, 
0x44, 0x08, 0x80, 0x88, 0x22, 0x04, 0x42, 0x20, 0x44, 0x22, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x41, 0x11, 
0x12, 0x28, 0x88, 0x88, 0x06, 0x00, 0x88, 0x21, 0x11, 0x10, 0x09, 0x00, 0x41, 0x08, 0x89, 0x10, 
0x80, 0x48, 0x02, 0x08, 0x44, 0x02, 0x40, 0x10, 0x42, 0x0c, 0x06, 0x40, 0x04, 0x00, 0x83, 0x01, 
0x90, 0x01, 0x00, 0x20, 0xc0, 0x64, 0x00, 0x40, 0x08, 0x08, 0x10, 0x10, 0x21, 0x08, 0x21, 0x00, 
0x11, 0x10, 0x04, 0x44, 0x04, 0x22, 0x04, 0x22, 0x00, 0x44, 0x40, 0x11, 0x10, 0x10, 0x88, 0x10, 
0x88, 0x00, 0x42, 0x00, 0x21, 0x01, 0x08, 0x08, 0x40, 0x02, 0x10, 0x01, 0x08, 0x08, 0x40, 0x42, 
0x0c, 0x21, 0x84, 0x18, 0x43, 0x08, 0x30, 0x86, 0x10, 0x61, 0x0c, 0x20, 0xc0, 0x50, 0x40, 0x21, 
0x0c, 0x05, 0x04, 0x02, 0x12, 0x07, 0x90, 0x22, 0x78, 0x42, 0x40, 0xf0, 0x09, 0x1e, 0x10, 0x80, 
0x10, 0x02, 0x02, 0x02, 0x10, 0x20, 0x08, 0x21, 0x04, 0x42, 0x20, 0x88, 0x44, 0x40, 0x80, 0x00, 
0x84, 0x44, 0x88, 0x22, 0x44, 0x48, 0xa2, 0x20, 0x18, 0x02, 0x22, 0x01, 0x90, 0x22, 0x08, 0x40, 
0x44, 0x08, 0x80, 0x88, 0x22, 0x04, 0x42, 0x20, 0x44, 0x22, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x49, 0x11, 
0x12, 0x28, 0x88, 0x88, 0x02, 0x40, 0x48, 0x21, 0x11, 0x10, 0x09, 0x00, 0x41, 0x08, 0x88, 0xf0, 
0x80, 0x48, 0x02, 0x08, 0x44, 0x02, 0x40, 0x10, 0x42, 0x10, 0x08, 0x40, 0x18, 0x03, 0x04, 0x02, 
0x10, 0x06, 0x00, 0xc1, 0x00, 0x84, 0x01, 0x80, 0x30, 0x08, 0x10, 0x10, 0x20, 0x84, 0x10, 0x88, 
0x11, 0x12, 0x04, 0x44, 0x04, 0x22, 0x04, 0x22, 0x20, 0x44, 0x48, 0x11, 0x10, 0x10, 0x88, 0x10, 
0x88, 0x82, 0x42, 0x41, 0x21, 0x09, 0x08, 0x48, 0x44, 0x12, 0x12, 0x09, 0x08, 0x48, 0x42, 0x42, 
0x0c, 0x21, 0x84, 0x18, 0x43, 0x08, 0x30, 0x86, 0x10, 0x61, 0x0c, 0x21, 0x00, 0xe0, 0x23, 0x12, 
0x10, 0x0e, 0x02, 0x31, 0x22, 0x00, 0x90, 0x22, 0x08, 0x46, 0x40, 0x10, 0x08, 0x82, 0x11, 0x90, 
0x12, 0x02, 0x02, 0x02, 0x10, 0x20, 0x08, 0x21, 0x04, 0x42, 0x20, 0x88, 0x44, 0x40, 0x90, 0x20, 
0x84, 0x44, 0x78, 0x22, 0x44, 0x48, 0xa2, 0x20, 0x09, 0x01, 0x22, 0x00, 0x90, 0x12, 0x08, 0x40, 
0x24, 0x04, 0x80, 0x48, 0x12, 0x02, 0x41, 0x20, 0x24, 0x12, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x31, 0x11, 
0x11, 0xe4, 0x88, 0x84, 0x04, 0x40, 0x24, 0x61, 0x11, 0x08, 0x10, 0x80, 0x43, 0x08, 0x88, 0x10, 
0x40, 0x84, 0x02, 0x18, 0x42, 0x04, 0x20, 0x10, 0xc2, 0x20, 0x10, 0x40, 0x60, 0x0d, 0x08, 0x04, 
0x10, 0x18, 0x03, 0x42, 0x01, 0x04, 0x06, 0x00, 0xd0, 0x08, 0x10, 0x10, 0x20, 0x84, 0x10, 0x88, 
0x11, 0x12, 0x04, 0x44, 0x04, 0x22, 0x04, 0x22, 0x20, 0x44, 0x48, 0x11, 0x10, 0x10, 0x88, 0x10, 
0x88, 0x82, 0x84, 0x41, 0x42, 0x0a, 0x10, 0x50, 0x84, 0x14, 0x22, 0x0a, 0x10, 0x50, 0x82, 0x84, 
0x08, 0x41, 0x08, 0x10, 0x82, 0x10, 0x21, 0x04, 0x20, 0x42, 0x08, 0x42, 0x01, 0x10, 0x1c, 0x0c, 
0x20, 0x11, 0x01, 0xc0, 0xc1, 0x01, 0x08, 0x1c, 0x10, 0x3c, 0x40, 0x22, 0x08, 0x84, 0x0f, 0x10, 
0x12, 0x03, 0x02, 0x02, 0x08, 0x44, 0x08, 0x21, 0x0c, 0x42, 0x31, 0x88, 0xc6, 0x21, 0x10, 0x21, 
0x84, 0x44, 0x08, 0x22, 0x45, 0x87, 0x92, 0x10, 0x11, 0x00, 0x91, 0x01, 0x10, 0x09, 0x18, 0x40, 
0x28, 0x05, 0x40, 0x50, 0x15, 0x02, 0x81, 0x50, 0x28, 0x15, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 


0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x41, 0x0d, 
0x0c, 0x23, 0xe8, 0x63, 0xf8, 0x40, 0x23, 0xce, 0xd0, 0xc7, 0xe0, 0x7f, 0xbe, 0x76, 0x70, 0x0e, 
0x3f, 0x03, 0xfd, 0xf3, 0xb1, 0xf8, 0x1f, 0xef, 0x9d, 0xa2, 0x12, 0x3f, 0x80, 0xf0, 0xe8, 0x04, 
0x0f, 0xe0, 0x3c, 0x3a, 0x01, 0x03, 0xf8, 0x0f, 0x0e, 0xf1, 0xed, 0xe3, 0xd8, 0x87, 0x10, 0xe8, 
0x1e, 0xe2, 0x07, 0xbb, 0xfb, 0xdc, 0xfb, 0xdd, 0xa0, 0x7b, 0x88, 0x1e, 0xef, 0xef, 0x73, 0xef, 
0x76, 0x83, 0xf8, 0x41, 0xfd, 0xf7, 0xe3, 0xbf, 0x74, 0x1f, 0xc2, 0x0f, 0xef, 0xbf, 0x1d, 0xfb, 
0xff, 0x8f, 0xf7, 0xff, 0x1f, 0xef, 0xfe, 0x3f, 0xdf, 0xfc, 0x7f, 0xba, 0x01, 0x0f, 0xf0, 0x73, 
0xa0, 0x10, 0xff, 0x07, 0x38, 0xfe, 0x07, 0xe7, 0xe1, 0xe7, 0x20, 0x42, 0x07, 0xf8, 0x79, 0xcf, 
0xe1, 0xfd, 0xfc, 0x7d, 0xc7, 0x84, 0x0f, 0xce, 0xcb, 0x85, 0xcf, 0x73, 0xb9, 0x1e, 0x10, 0x3f, 
0x3b, 0x38, 0x07, 0xff, 0x9e, 0x70, 0x8f, 0x8f, 0xe1, 0x00, 0x88, 0xfe, 0x08, 0x10, 0xf3, 0xb0, 
0xf0, 0x1e, 0x71, 0xe0, 0x79, 0xcf, 0x07, 0x9c, 0xf0, 0x79, 0xcf, 0xfe, 0x1f, 0xfc, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x20, 0x84, 0x40, 0x00, 0x20, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x10, 0x00, 0x00, 0x00, 0x08, 0x04, 
0x00, 0x00, 0x00, 0x02, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0x10, 0x88, 
0x10, 0x02, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x40, 0x08, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x82, 0x00, 0x41, 0x00, 0x00, 0x00, 0x00, 0x04, 0x10, 0x02, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x01, 0x00, 0x00, 0x00, 
0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x82, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x08, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x10, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x05, 0x80, 0x82, 0x00, 0x00, 0x81, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x41, 0x08, 0x80, 0x00, 0x1f, 0x80, 0x00, 0x00, 0x00, 0x80, 0x08, 0x02, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x88, 0x40, 0x08, 0x02, 0x04, 0x22, 
0x10, 0x00, 0x00, 0x01, 0x08, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x21, 0x04, 
0x20, 0x01, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x80, 0x04, 0x20, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x44, 0x00, 0x22, 0x00, 0x00, 0x00, 0x00, 0x02, 0x20, 0x01, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x84, 0x00, 0x00, 
0x10, 0x88, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x10, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x08, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x01, 0x04, 0x00, 0x00, 0x7e, 0x00, 0x28, 0x00, 0x00, 0x29, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x8e, 0x1d, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x07, 0x80, 0x00, 0x00, 0x03, 0xc1, 
0xe0, 0x00, 0x00, 0x00, 0xf0, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x30, 0xc6, 0x03, 
0xc0, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x38, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x78, 0x00, 0x00, 
0x0f, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x0e, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 



};

xFONTYY Tahoma09nArabic4 = {0x01, 19, 0, 19, 0, 0, 19, 192, 0xfe70, 0xfeff,
(PEGUSHORT *) Tahoma09nArabic4_offset_table, &NewGulLim12bKor_Font,
(PEGUBYTE *) Tahoma09nArabic4_data_table};


ROMDATA PEGUSHORT Tahoma09nArabic3_offset_table[689] = {
0x0000,0x0004,0x0008,0x0014,0x0022,0x0027,0x002d,0x0039,0x0047,0x004c,0x0052,0x005e,0x006c,0x0071,0x0077,0x0083,
0x0091,0x0096,0x009c,0x00a8,0x00b6,0x00bb,0x00c1,0x00cd,0x00db,0x00e1,0x00e7,0x00f4,0x0102,0x0109,0x0112,0x011f,
0x012d,0x0134,0x013d,0x0146,0x0151,0x015c,0x0167,0x0170,0x017b,0x0186,0x0191,0x019a,0x01a5,0x01b0,0x01bb,0x01c4,
0x01cf,0x01da,0x01e5,0x01ec,0x01f4,0x01fb,0x0203,0x020a,0x0212,0x0219,0x0221,0x0226,0x022c,0x0231,0x0237,0x0245,
0x0254,0x025c,0x0265,0x0273,0x0282,0x028a,0x0293,0x02a1,0x02b0,0x02b8,0x02c1,0x02cf,0x02de,0x02e6,0x02ef,0x02f9,
0x0304,0x030e,0x0319,0x031f,0x0325,0x032c,0x0335,0x033c,0x0344,0x0349,0x0351,0x035c,0x0365,0x0370,0x0379,0x0387,
0x0395,0x03a3,0x03b1,0x03c0,0x03cf,0x03de,0x03ed,0x03fc,0x040b,0x041a,0x0429,0x0438,0x0447,0x0456,0x0465,0x0474,
0x0483,0x0492,0x04a1,0x04b0,0x04bf,0x04ce,0x04dd,0x04ec,0x04fb,0x050a,0x0519,0x0528,0x0537,0x0546,0x0555,0x0564,
0x0573,0x0582,0x0591,0x05a0,0x05ab,0x05b7,0x05bf,0x05c8,0x05ce,0x05d5,0x05db,0x05e2,0x05e8,0x05ef,0x05f6,0x05fc,
0x0603,0x0609,0x0610,0x0616,0x061d,0x062a,0x0639,0x063e,0x0643,0x0648,0x064e,0x065d,0x066c,0x067b,0x068a,0x0699,
0x06a8,0x06b7,0x06c6,0x06d5,0x06e4,0x06f3,0x0702,0x0711,0x0720,0x072f,0x073e,0x074d,0x075c,0x0769,0x0778,0x077d,
0x0783,0x0792,0x07a1,0x07b0,0x07bf,0x07ce,0x07dd,0x07ec,0x07fb,0x080a,0x0819,0x0828,0x0837,0x0846,0x0855,0x0864,
0x0873,0x0882,0x0891,0x08a0,0x08af,0x08be,0x08cd,0x08dc,0x08eb,0x08fa,0x0909,0x0918,0x0927,0x0936,0x0945,0x0954,
0x0963,0x0972,0x0981,0x0990,0x099f,0x09ae,0x09bd,0x09cc,0x09db,0x09ea,0x09f9,0x0a08,0x0a17,0x0a26,0x0a35,0x0a44,
0x0a53,0x0a62,0x0a71,0x0a80,0x0a8f,0x0a9e,0x0aad,0x0abc,0x0acb,0x0ada,0x0ae9,0x0af8,0x0b07,0x0b16,0x0b25,0x0b34,
0x0b43,0x0b52,0x0b61,0x0b70,0x0b7f,0x0b8e,0x0b9d,0x0bac,0x0bbb,0x0bca,0x0bd9,0x0be8,0x0bf7,0x0c06,0x0c15,0x0c24,
0x0c33,0x0c42,0x0c51,0x0c60,0x0c6f,0x0c7e,0x0c8d,0x0c9c,0x0cab,0x0cba,0x0cc9,0x0cd8,0x0ce7,0x0cf6,0x0d05,0x0d0b,
0x0d10,0x0d15,0x0d1a,0x0d1f,0x0d2e,0x0d3d,0x0d4c,0x0d5b,0x0d6a,0x0d79,0x0d88,0x0d97,0x0da6,0x0db5,0x0dc4,0x0dd3,
0x0de2,0x0df1,0x0e00,0x0e0f,0x0e1e,0x0e2d,0x0e3c,0x0e4b,0x0e5a,0x0e69,0x0e78,0x0e87,0x0e96,0x0ea5,0x0eb4,0x0ec3,
0x0ed2,0x0ee1,0x0ef0,0x0eff,0x0f0e,0x0f1d,0x0f2c,0x0f3b,0x0f4a,0x0f59,0x0f68,0x0f77,0x0f86,0x0f95,0x0fa4,0x0fb3,
0x0fc2,0x0fd1,0x0fe0,0x0fef,0x0ffe,0x100d,0x101c,0x102b,0x103a,0x1049,0x1058,0x1067,0x1076,0x1085,0x1094,0x10a3,
0x10b2,0x10c1,0x10d0,0x10df,0x10ee,0x10fd,0x110c,0x111b,0x112a,0x1139,0x1148,0x1157,0x1166,0x1175,0x1184,0x1193,
0x11a2,0x11b1,0x11c0,0x11cf,0x11de,0x11ed,0x11fc,0x120b,0x121a,0x1229,0x1238,0x1247,0x1256,0x1265,0x1274,0x1283,
0x1292,0x12a1,0x12b0,0x12bf,0x12ce,0x12dd,0x12ec,0x12fb,0x130a,0x1319,0x1328,0x1337,0x1346,0x1355,0x1364,0x1373,
0x1382,0x1391,0x13a0,0x13af,0x13be,0x13cd,0x13dc,0x13eb,0x13fa,0x1409,0x1418,0x1427,0x1436,0x1445,0x1454,0x1463,
0x1472,0x1481,0x1490,0x149f,0x14ae,0x14bd,0x14cc,0x14db,0x14ea,0x14f9,0x1508,0x1517,0x1526,0x1535,0x1544,0x1553,
0x1562,0x1571,0x1580,0x158f,0x159e,0x15ad,0x15bc,0x15cb,0x15da,0x15e9,0x15f8,0x1607,0x1616,0x1625,0x1634,0x1643,
0x1652,0x1661,0x1670,0x167f,0x168e,0x169d,0x16ac,0x16bb,0x16ca,0x16d9,0x16e8,0x16f7,0x1706,0x1715,0x1724,0x1733,
0x1742,0x1751,0x1760,0x176f,0x177e,0x178d,0x179c,0x17ab,0x17ba,0x17c9,0x17d8,0x17e7,0x17f6,0x1805,0x1814,0x1823,
0x1832,0x1841,0x1850,0x185f,0x186e,0x187d,0x188c,0x189b,0x18aa,0x18b9,0x18c8,0x18d7,0x18e6,0x18f5,0x1904,0x1913,
0x1922,0x1931,0x1940,0x194f,0x195e,0x196d,0x197c,0x198b,0x199a,0x19a9,0x19b8,0x19c7,0x19d6,0x19e5,0x19f4,0x19fa,
0x1a00,0x1a0f,0x1a1e,0x1a2d,0x1a3c,0x1a4b,0x1a5a,0x1a69,0x1a78,0x1a87,0x1a96,0x1aa5,0x1ab4,0x1ac3,0x1ad2,0x1ae1,
0x1af0,0x1aff,0x1b0e,0x1b1d,0x1b2c,0x1b3b,0x1b4a,0x1b59,0x1b68,0x1b77,0x1b86,0x1b95,0x1ba4,0x1bb3,0x1bc2,0x1bd1,
0x1be0,0x1bef,0x1bfe,0x1c0d,0x1c1c,0x1c2b,0x1c3a,0x1c49,0x1c58,0x1c67,0x1c76,0x1c85,0x1c94,0x1ca3,0x1cb2,0x1cc1,
0x1cd0,0x1cdf,0x1cee,0x1cfd,0x1d0c,0x1d1b,0x1d2a,0x1d39,0x1d48,0x1d57,0x1d66,0x1d75,0x1d84,0x1d93,0x1da2,0x1db1,
0x1dc0,0x1dcf,0x1dde,0x1ded,0x1dfc,0x1e0b,0x1e1a,0x1e29,0x1e38,0x1e47,0x1e56,0x1e65,0x1e74,0x1e83,0x1e92,0x1ea1,
0x1eb0,0x1ebf,0x1ece,0x1edd,0x1eec,0x1efb,0x1f0a,0x1f19,0x1f28,0x1f37,0x1f46,0x1f55,0x1f64,0x1f73,0x1f82,0x1f91,
0x1fa0,0x1faf,0x1fbe,0x1fcd,0x1fdc,0x1feb,0x1ffa,0x2009,0x2018,0x2027,0x2036,0x2045,0x2054,0x2063,0x2072,0x2081,
0x2090,0x209f,0x20ae,0x20bd,0x20cc,0x20db,0x20ea,0x20f9,0x2108,0x2117,0x2126,0x2135,0x2144,0x2153,0x2162,0x2171,
0x2180,0x218f,0x219e,0x21ad,0x21bc,0x21cb,0x21da,0x21e9,0x21f8,0x2207,0x2216,0x2225,0x2234,0x2243,0x2252,0x2261,
0x2270,0x227f,0x228e,0x229d,0x22ac,0x22bb,0x22ca,0x22d9,0x22e8,0x22f7,0x2306,0x2315,0x2324,0x2333,0x2342,0x2351,
0x2360,0x236f,0x237e,0x238d,0x239c,0x23ab,0x23ba,0x23c9,0x23d8,0x23e7,0x23f6,0x2405,0x2414,0x2423,0x2432,0x2441,
0x2450,0x245f,0x246e,0x2480,0x248f,0x249e,0x24ad,0x24bc,0x24cb,0x24da,0x24e9,0x24f8,0x2507,0x251c,0x252b,0x253a,
0x2549};



ROMDATA PEGUBYTE Tahoma09nArabic3_data_table[21492 + 1194] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0xaa, 0xf9, 0x54, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, 0x02, 0x81, 0x41, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x60, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 
0x00, 0x80, 0x80, 0x03, 0x00, 0x0c, 0x02, 0x02, 0x00, 0x0c, 0x00, 0x30, 0x08, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x80, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x9f, 0xaf, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 
0x08, 0x00, 0x00, 0x00, 0x00, 0x02, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x04, 0x08, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x0c, 
0x03, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x0c, 0x0c, 0x00, 0x30, 0x00, 0xc0, 0x30, 0x30, 0x00, 0x00, 
0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x20, 0x04, 0x10, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x50, 0x01, 0x40, 0x00, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0x1e, 0x00, 0x04, 0x20, 0x00, 0x10, 0x00, 0x40, 0x20, 0x20, 0x0c, 0x40, 0x31, 
0x0c, 0x8c, 0x80, 0x31, 0x00, 0xc4, 0x32, 0x32, 0x00, 0xc4, 0x03, 0x10, 0xc8, 0xc8, 0x00, 0x00, 
0x00, 0x80, 0x00, 0x10, 0x40, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x0a, 0x41, 0x48, 0xa4, 0xa4, 0x10, 0x40, 0x00, 0x08, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x07, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe2, 0xbf, 0x05, 0x40, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc1, 0x08, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0x02, 0xc2, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x01, 0x20, 0x03, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x00, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0xa0, 0x02, 0x00, 0x20, 0x04, 0x10, 0x01, 0x40, 0x00, 
0x14, 0x02, 0x00, 0x00, 0x00, 0x02, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x85, 
0x0a, 0x0a, 0x09, 0x12, 0x00, 0x04, 0x20, 0x00, 0x60, 0x01, 0x80, 0xc0, 0xc0, 0x01, 0x80, 0x06, 
0x13, 0x13, 0x00, 0x06, 0x00, 0x18, 0x4c, 0x4c, 0x00, 0x18, 0x00, 0x61, 0x31, 0x30, 0x00, 0x00, 
0x00, 0xf0, 0x00, 0x10, 0x41, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x00, 0x40, 0x08, 0x18, 0x18, 0x28, 0xa0, 0x00, 0x08, 0x20, 0x44, 0x82, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x40, 0x02, 0x80, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x00, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x01, 0x20, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x00, 0x40, 0x00, 0x40, 0x50, 0x05, 0x00, 0x05, 0x03, 0xc0, 0x3c, 0x07, 0x9e, 0x00, 0x00, 0x05, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x40, 0x00, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1f, 0x3e, 0x10, 0x87, 0xbc, 0x01, 0x80, 0x06, 0x03, 0x03, 0x00, 0x06, 0x00, 0x18, 
0x0c, 0x0c, 0x00, 0x18, 0x00, 0x60, 0x30, 0x30, 0x00, 0x60, 0x01, 0x80, 0xc0, 0xc0, 0x00, 0x00, 
0x00, 0x90, 0x20, 0x1e, 0x7b, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x00, 0x40, 0x08, 0x60, 0x60, 0x18, 0x62, 0x8a, 0x08, 0x20, 0xa8, 0x00, 
0x00, 0x00, 0x20, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x00, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x01, 0x20, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0xa0, 0x02, 0x40, 0x24, 0x04, 0x92, 0x01, 0x80, 0x00, 
0x18, 0x05, 0x00, 0x30, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0xa4, 0x02, 0x00, 0x08, 0x04, 0x04, 0x00, 0x08, 0x00, 0x20, 
0x10, 0x10, 0x00, 0x20, 0x00, 0x80, 0x40, 0x40, 0x00, 0x80, 0x02, 0x01, 0x01, 0x00, 0x00, 0x00, 
0x01, 0xf0, 0x20, 0x12, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x70, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x04, 0x40, 0x88, 0x80, 0x80, 0x61, 0x81, 0x04, 0x08, 0x20, 0x7d, 0x45, 
0x00, 0x00, 0x51, 0x40, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x0e, 0x00, 0x00, 0x00, 
0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x01, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x01, 0x20, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x00, 0x40, 0x00, 0x40, 0x50, 0x05, 0x00, 0x05, 0x07, 0xc0, 0x7c, 0x0f, 0xbe, 0x02, 0x40, 0x00, 
0x24, 0x00, 0x00, 0x48, 0x00, 0x04, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x02, 0x02, 
0x04, 0x04, 0x08, 0x08, 0x29, 0x4f, 0xfc, 0x02, 0x00, 0x08, 0x04, 0x04, 0x00, 0x08, 0x00, 0x20, 
0x10, 0x10, 0x00, 0x20, 0x00, 0x80, 0x40, 0x40, 0x00, 0x80, 0x02, 0x01, 0x01, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3c, 0x3e, 0xf9, 0x01, 0xc1, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1c, 0x00, 0x10, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x08, 0x41, 0x08, 0x80, 0x80, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x11, 0x00, 0x00, 0x00, 
0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x71, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x01, 0x20, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x20, 0x07, 
0x22, 0x0f, 0x00, 0x44, 0x00, 0xe4, 0x41, 0xe1, 0xe0, 0xf0, 0x1e, 0x03, 0xc0, 0x78, 0x3c, 0x07, 
0x80, 0xf0, 0x1e, 0x0f, 0x01, 0xe0, 0x3c, 0x07, 0x83, 0xc0, 0x78, 0x0f, 0x00, 0x80, 0x81, 0x01, 
0x02, 0x02, 0x04, 0x04, 0x00, 0x00, 0x00, 0x01, 0x80, 0x06, 0x03, 0x03, 0x00, 0x06, 0x00, 0x18, 
0x0c, 0x0c, 0x00, 0x18, 0x00, 0x60, 0x30, 0x30, 0x00, 0x60, 0x01, 0x80, 0xc0, 0xc0, 0x81, 0x00, 
0x04, 0x08, 0x24, 0x00, 0x00, 0x82, 0x40, 0x80, 0x00, 0x00, 0x0e, 0x40, 0xc0, 0xe4, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x04, 0x40, 0x88, 0x60, 0x60, 0x61, 0x83, 0x0c, 0x18, 0x60, 0x61, 0x86, 
0x00, 0x00, 0x61, 0x80, 0x08, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x10, 0x00, 0x30, 0x00, 
0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x91, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x01, 0x20, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x40, 0x24, 0x00, 0x04, 0x02, 0x01, 0x20, 0x00, 0x20, 0x10, 0x09, 0x00, 0x01, 0x00, 0x80, 
0x48, 0x00, 0x08, 0x04, 0x02, 0x40, 0x00, 0x40, 0x20, 0x12, 0x00, 0x02, 0x00, 0x82, 0x24, 0x08, 
0xa2, 0x19, 0x90, 0x44, 0x81, 0x14, 0x43, 0x30, 0x38, 0x1e, 0x01, 0xa0, 0x34, 0x0e, 0x07, 0x80, 
0x68, 0x0d, 0x03, 0x81, 0xe0, 0x1a, 0x03, 0x40, 0xe0, 0x78, 0x06, 0x80, 0xd0, 0x40, 0x80, 0x81, 
0x01, 0x02, 0x02, 0x04, 0x00, 0x00, 0x00, 0x80, 0x42, 0x01, 0x00, 0x80, 0x82, 0x01, 0x08, 0x04, 
0x02, 0x02, 0x08, 0x04, 0x20, 0x10, 0x08, 0x08, 0x20, 0x10, 0x80, 0x40, 0x20, 0x20, 0x81, 0x00, 
0x04, 0x08, 0x7c, 0x08, 0x01, 0x44, 0x41, 0x40, 0x01, 0x04, 0x11, 0x41, 0x21, 0x14, 0x12, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x08, 0x41, 0x08, 0x10, 0x10, 0x92, 0x44, 0x92, 0x24, 0x90, 0x92, 0x49, 
0x06, 0x18, 0x92, 0x42, 0x06, 0x00, 0x24, 0x08, 0x42, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x0c, 0x00, 0x48, 0x10, 
0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x11, 0x11, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x81, 0x20, 0x82, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x40, 0x24, 0x01, 0x04, 0x22, 0x01, 0x20, 0x08, 0x21, 0x10, 0x09, 0x00, 0x41, 0x08, 0x80, 
0x48, 0x02, 0x08, 0x44, 0x02, 0x40, 0x10, 0x42, 0x20, 0x12, 0x00, 0x82, 0x08, 0x81, 0xe4, 0x08, 
0x9e, 0x10, 0x90, 0x3c, 0x81, 0x13, 0xc2, 0x10, 0xc0, 0x64, 0x00, 0x40, 0x08, 0x30, 0x19, 0x00, 
0x10, 0x02, 0x0c, 0x06, 0x40, 0x04, 0x00, 0x83, 0x01, 0x90, 0x01, 0x00, 0x20, 0x20, 0x40, 0x40, 
0x80, 0x81, 0x01, 0x02, 0x10, 0x82, 0x10, 0x80, 0x22, 0x00, 0x80, 0x40, 0x42, 0x00, 0x88, 0x02, 
0x01, 0x01, 0x08, 0x02, 0x20, 0x08, 0x04, 0x04, 0x20, 0x08, 0x80, 0x20, 0x10, 0x10, 0x81, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x22, 0x24, 0x42, 0x20, 0xc1, 0x08, 0x11, 0x22, 0x21, 0x12, 0x22, 0x01, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x00, 0x40, 0x08, 0x08, 0x08, 0x8a, 0x24, 0x51, 0x22, 0x88, 0x8a, 0x28, 
0x89, 0x24, 0x8a, 0x22, 0x01, 0x90, 0x12, 0x08, 0x42, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x03, 0x00, 0x44, 0x10, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x11, 0x11, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x81, 0x20, 0xc2, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x40, 0x24, 0x01, 0x04, 0x22, 0x01, 0x20, 0x08, 0x21, 0x10, 0x09, 0x00, 0x41, 0x08, 0x80, 
0x48, 0x02, 0x08, 0x44, 0x02, 0x40, 0x10, 0x42, 0x20, 0x12, 0x00, 0x82, 0x08, 0x80, 0x24, 0x08, 
0x82, 0x11, 0x90, 0x04, 0x81, 0x10, 0x42, 0x31, 0x20, 0x84, 0x01, 0x80, 0x30, 0x40, 0x21, 0x00, 
0x60, 0x0c, 0x10, 0x08, 0x40, 0x18, 0x03, 0x05, 0x43, 0x50, 0x06, 0x00, 0xc0, 0x20, 0x40, 0x40, 
0x80, 0x81, 0x01, 0x02, 0x08, 0x41, 0x08, 0x80, 0x22, 0x00, 0x80, 0x40, 0x42, 0x00, 0x88, 0x02, 
0x01, 0x01, 0x08, 0x02, 0x20, 0x08, 0x04, 0x04, 0x20, 0x08, 0x80, 0x20, 0x10, 0x10, 0x81, 0x20, 
0x44, 0x09, 0x02, 0x08, 0x22, 0x23, 0xc2, 0x21, 0x21, 0x08, 0x11, 0x22, 0x21, 0x12, 0x22, 0x02, 
0x80, 0x00, 0x00, 0x28, 0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x40, 0x48, 0x08, 0x08, 0x08, 0x8a, 0x24, 0x51, 0x22, 0x88, 0x8a, 0x28, 
0x89, 0x22, 0x8a, 0x22, 0x00, 0x90, 0x0a, 0x08, 0x42, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x01, 0x20, 0x24, 0x10, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0xf1, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x81, 0x20, 0xa2, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x44, 0x20, 0x42, 0x01, 0x0c, 0x21, 0x02, 0x10, 0x08, 0x61, 0x08, 0x10, 0x80, 0x43, 0x08, 0x40, 
0x84, 0x02, 0x18, 0x42, 0x04, 0x20, 0x10, 0xc2, 0x10, 0x21, 0x00, 0x86, 0x08, 0x40, 0x42, 0x07, 
0x04, 0x0f, 0x08, 0x08, 0x40, 0xe0, 0x81, 0xe2, 0x01, 0x24, 0x06, 0x00, 0xd0, 0x80, 0x41, 0x01, 
0x80, 0x34, 0x25, 0x15, 0x40, 0x60, 0x0d, 0x08, 0x04, 0x10, 0x18, 0x03, 0x40, 0x20, 0x40, 0x40, 
0x80, 0x81, 0x01, 0x02, 0x08, 0x41, 0x08, 0x40, 0x21, 0x00, 0x80, 0x40, 0x41, 0x00, 0x84, 0x02, 
0x01, 0x01, 0x04, 0x02, 0x10, 0x08, 0x04, 0x04, 0x10, 0x08, 0x40, 0x20, 0x10, 0x10, 0x42, 0x20, 
0x42, 0x11, 0x02, 0x18, 0x22, 0x20, 0x42, 0x22, 0x21, 0x0b, 0x11, 0x22, 0xc1, 0x12, 0x2c, 0x04, 
0x40, 0x00, 0x00, 0x44, 0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x40, 0x48, 0x0c, 0x08, 0x08, 0x79, 0x23, 0xc9, 0x1e, 0x48, 0x79, 0xe4, 
0x87, 0x12, 0x79, 0x21, 0x01, 0x08, 0x09, 0x18, 0xc6, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x02, 0x20, 0x12, 0x30, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x20, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x44, 0x02, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x11, 0x12, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x42, 0x21, 0xa2, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x43, 0x1f, 0x81, 0xfe, 0xf9, 0xd8, 0xfc, 0x0f, 0xf7, 0xce, 0xc7, 0xe0, 0x7f, 0xbe, 0x76, 0x3f, 
0x03, 0xfd, 0xf3, 0xb1, 0xf8, 0x1f, 0xef, 0x9d, 0x8f, 0xc0, 0xff, 0x7c, 0x76, 0x3f, 0x81, 0xf9, 
0xf8, 0x79, 0xc7, 0xf0, 0x3f, 0x3f, 0x0f, 0x3a, 0x21, 0x03, 0xf8, 0x0f, 0x0e, 0x94, 0x54, 0xfe, 
0x03, 0xc3, 0xa0, 0x10, 0x3f, 0x80, 0xf0, 0xe9, 0x45, 0x4f, 0xe0, 0x3c, 0x3b, 0xc7, 0xb7, 0x8f, 
0x6f, 0x1e, 0xde, 0x3d, 0x88, 0x71, 0x0e, 0x3f, 0xc0, 0xff, 0x7f, 0x8f, 0xb8, 0xff, 0x03, 0xfd, 
0xfe, 0x3e, 0xe3, 0xfc, 0x0f, 0xf7, 0xf8, 0xfb, 0x8f, 0xf0, 0x3f, 0xdf, 0xe3, 0xee, 0x3c, 0x20, 
0x71, 0xe1, 0x03, 0xf1, 0xd9, 0xc0, 0x39, 0xc2, 0x3e, 0x7c, 0xff, 0xcf, 0x3f, 0xfc, 0xf3, 0xb8, 
0x00, 0x3f, 0xfb, 0x80, 0x03, 0xff, 0x9f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0x87, 0xf7, 0xf1, 0xf7, 0x08, 0xf8, 0x47, 0xc2, 0x3e, 0x08, 0x23, 
0xe1, 0x0f, 0x08, 0xf8, 0xfe, 0x07, 0xf0, 0xf3, 0x9c, 0xec, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc1, 0xfc, 0x20, 0x11, 0xe7, 
0x67, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe0, 0x00, 0x00, 0x00, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc2, 0x04, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0x0e, 0xe2, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x3c, 0x1f, 0x23, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x00, 

0x00, 0x02, 0x00, 0x20, 0x04, 0x20, 0x28, 0x02, 0x80, 0xa5, 0x01, 0x40, 0x14, 0x05, 0x14, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x01, 0x20, 0x00, 0x80, 0x40, 0x80, 0x40, 0x00, 
0x00, 0x00, 0x22, 0x12, 0x00, 0x14, 0x0a, 0x08, 0x04, 0x00, 0x05, 0x02, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x41, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 
0x40, 0x01, 0x02, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x00, 0x00, 0x02, 0xc0, 0x00, 0x2c, 0x40, 
0x00, 0xc0, 0x04, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x20, 0x41, 0x02, 0x08, 0x08, 0x20, 
0x9f, 0x7e, 0x08, 0x20, 0x10, 0x01, 0x00, 0x08, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x84, 0x00, 0x00, 0x00, 0x42, 0x21, 0x00, 
0x50, 0x14, 0x10, 0x88, 0x40, 0x00, 0x00, 0x04, 0x22, 0x10, 0x00, 0x00, 0x02, 0x85, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x10, 0x82, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 
0x80, 0x00, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x04, 0x00, 0x03, 0x00, 0x00, 0x30, 0x3f, 
0xff, 0x00, 0x03, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x82, 0x04, 0x10, 0x10, 0x41, 
0x06, 0x0c, 0x10, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xc0, 0x52, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x14, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x02, 0x00, 0x20, 0x04, 0x20, 0x10, 0x01, 0x00, 0x42, 0x01, 0x40, 0x14, 0x05, 0x14, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x78, 0x00, 0x80, 0x40, 0x3c, 0x1e, 0x00, 
0x00, 0x00, 0x0f, 0x07, 0x80, 0x08, 0x04, 0x03, 0xc1, 0xe0, 0x05, 0x02, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x63, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 
0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x02, 0x00, 0x00, 0x20, 0x00, 
0x00, 0xff, 0xf8, 0x00, 0x0f, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe3, 0x87, 0x1c, 0x38, 0xe0, 0xe3, 0x8e, 
0x18, 0x70, 0xe3, 0x80, 0x10, 0x01, 0x00, 0x08, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma09nArabic3 = {0x01, 19, 0, 19, 0, 0, 19, 1194, 0xfb50, 0xfdff,
(PEGUSHORT *) Tahoma09nArabic3_offset_table, &Tahoma09nArabic4, 
(PEGUBYTE *) Tahoma09nArabic3_data_table};


ROMDATA PEGUSHORT Tahoma09nArabic2_offset_table[97] = {
0x0000,0x000f,0x001e,0x002d,0x003c,0x004b,0x005a,0x0069,0x0078,0x0087,0x0096,0x00a5,0x00b4,0x00c3,0x00d2,0x00e1,
0x00f0,0x00ff,0x010e,0x011d,0x012c,0x013b,0x014a,0x0159,0x0168,0x0177,0x0186,0x0195,0x01a4,0x01b3,0x01c2,0x01d1,
0x01e0,0x01ef,0x01fe,0x020d,0x021c,0x022b,0x023a,0x0249,0x0258,0x0267,0x0276,0x0285,0x0294,0x02a3,0x02b2,0x02c1,
0x02d0,0x02df,0x02ee,0x02fd,0x030c,0x031b,0x032a,0x0339,0x0348,0x0357,0x0366,0x0375,0x0384,0x0393,0x03a2,0x03b1,
0x03c0,0x03cf,0x03de,0x03ed,0x03fc,0x040b,0x041a,0x0429,0x0438,0x0447,0x0456,0x0465,0x0474,0x0483,0x0492,0x04a1,
0x04b0,0x04bf,0x04ce,0x04dd,0x04ec,0x04fb,0x050a,0x0519,0x0528,0x0537,0x0546,0x0555,0x0564,0x0573,0x0582,0x0591,
0x05a0};



ROMDATA PEGUBYTE Tahoma09nArabic2_data_table[3240 + 180] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 
0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 
0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 
0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 
0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 
0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 
0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 
0x08, 0x02, 0x10, 0x04, 

0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 
0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 
0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 
0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 
0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 
0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 
0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 
0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 
0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 
0x0f, 0xfe, 0x1f, 0xfc, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma09nArabic2 = {0x01, 19, 0, 19, 0, 0, 19, 180, 0x08a0, 0x08ff,
(PEGUSHORT *) Tahoma09nArabic2_offset_table, &Tahoma09nArabic3, 
(PEGUBYTE *) Tahoma09nArabic2_data_table};


ROMDATA PEGUSHORT Tahoma09nArabic1_offset_table[49] = {
0x0000,0x000c,0x0018,0x0024,0x0030,0x003c,0x0048,0x0054,0x005d,0x0066,0x006d,0x0074,0x0079,0x008b,0x0094,0x009d,
0x00a6,0x00b3,0x00c0,0x00ce,0x00dc,0x00ea,0x00f3,0x00fc,0x0106,0x0110,0x011a,0x0126,0x012b,0x0130,0x0142,0x0151,
0x0160,0x016f,0x017e,0x018d,0x019c,0x01ab,0x01ba,0x01c9,0x01d8,0x01e7,0x01f6,0x0205,0x0214,0x0223,0x0232,0x0241,
0x0250};



ROMDATA PEGUBYTE Tahoma09nArabic1_data_table[1332 + 74] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x05, 0x20, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x20, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x78, 0x00, 0xf0, 0x01, 0xe0, 0x03, 
0xc0, 0x07, 0x80, 0x0f, 0x00, 0x1e, 0x00, 0x3c, 0x00, 0x78, 0x00, 0xf0, 0x01, 0xe0, 0x03, 0xc0, 
0x07, 0x80, 0x0f, 0x00, 0x1e, 0x00, 0x3c, 0x00, 0x78, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0x00, 0x00, 
0x00, 0x02, 0x80, 0x80, 0x40, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0xc0, 0x03, 0x00, 0x00, 0x00, 
0x00, 0x3c, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x01, 0xf0, 0x00, 0x00, 
0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0x00, 0x00, 
0x00, 0x24, 0x0a, 0x00, 0x20, 0x80, 0x00, 0x20, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x81, 0xc0, 0xe0, 0x03, 0x00, 0x18, 0x01, 0x00, 0x04, 0x00, 0x10, 0x01, 0x00, 0x00, 
0x80, 0x7c, 0x04, 0x00, 0xfc, 0x02, 0x00, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x00, 0x00, 0x50, 0x00, 0x00, 0x50, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x81, 0x00, 0x00, 
0x28, 0x04, 0x02, 0x01, 0x00, 0x04, 0x80, 0x24, 0x01, 0x00, 0x04, 0x00, 0x10, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x20, 0x84, 0x00, 0x20, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc1, 0xe0, 0x40, 0x80, 0x00, 
0x00, 0x04, 0x02, 0x01, 0x00, 0x04, 0x40, 0x22, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x03, 0x81, 0xc4, 
0x09, 0x12, 0x44, 0x80, 0x20, 0x0e, 0x00, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x40, 0x24, 0x02, 0x40, 0x24, 0x02, 0x40, 0x24, 0x02, 0x40, 0x20, 0x70, 0x38, 0x20, 0x40, 0x00, 
0x00, 0x44, 0xc2, 0x61, 0x31, 0x04, 0x48, 0x22, 0x40, 0x21, 0x00, 0x84, 0x02, 0x04, 0x42, 0x24, 
0x09, 0x02, 0x40, 0x90, 0x20, 0x00, 0x00, 0x00, 0xa4, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x40, 0x24, 0x02, 0x40, 0x24, 0x02, 0x40, 0x24, 0x02, 0x40, 0x21, 0x80, 0xc0, 0x10, 0x22, 0x00, 
0x44, 0x43, 0x01, 0x80, 0xc1, 0x03, 0xc8, 0x1e, 0x40, 0x11, 0x00, 0x44, 0x01, 0x04, 0x42, 0x24, 
0x09, 0x02, 0x40, 0x90, 0x20, 0x84, 0x00, 0x88, 0xa4, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x40, 0x24, 0x02, 0x40, 0x24, 0x02, 0x40, 0x24, 0x02, 0x40, 0x22, 0x01, 0x20, 0x10, 0x21, 0x20, 
0x44, 0x44, 0x02, 0x01, 0x01, 0x00, 0x48, 0x02, 0x40, 0x11, 0x00, 0x44, 0x01, 0x04, 0x42, 0x24, 
0x09, 0x02, 0x40, 0x90, 0x20, 0x42, 0x40, 0x88, 0xa4, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x20, 0x42, 0x04, 0x20, 0x42, 0x04, 0x20, 0x42, 0x04, 0x20, 0x44, 0x02, 0x00, 0x10, 0x21, 0x20, 
0x44, 0x48, 0x04, 0x02, 0x00, 0x80, 0x84, 0x04, 0x20, 0x10, 0x80, 0x42, 0x01, 0x0c, 0x46, 0x22, 
0x10, 0x84, 0x21, 0x08, 0x40, 0x42, 0x40, 0x88, 0xa4, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 
0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 
0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x24, 0x00, 0x48, 0x00, 

0x1f, 0x81, 0xf8, 0x1f, 0x81, 0xf8, 0x1f, 0x81, 0xf8, 0x1f, 0x84, 0x02, 0x51, 0xe3, 0xc1, 0x20, 
0x7b, 0x88, 0x04, 0x02, 0x00, 0x7f, 0x03, 0xf8, 0x1f, 0xe0, 0x7f, 0x81, 0xfe, 0x0b, 0x85, 0xc1, 
0xe0, 0x78, 0x1e, 0x07, 0x80, 0x42, 0x40, 0xf7, 0x3c, 0x00, 0x78, 0x00, 0xf0, 0x01, 0xe0, 0x03, 
0xc0, 0x07, 0x80, 0x0f, 0x00, 0x1e, 0x00, 0x3c, 0x00, 0x78, 0x00, 0xf0, 0x01, 0xe0, 0x03, 0xc0, 
0x07, 0x80, 0x0f, 0x00, 0x1e, 0x00, 0x3c, 0x00, 0x78, 0x00, 

0x00, 0x00, 0x00, 0x02, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x00, 0x80, 0x01, 0x20, 
0x40, 0x08, 0x04, 0x02, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x08, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x40, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0a, 0x80, 0x20, 0x00, 0x00, 0x00, 0x05, 0x00, 0x20, 0x00, 0x02, 0x11, 0x08, 0x01, 0x07, 0x90, 
0x80, 0x04, 0x22, 0x11, 0x08, 0x02, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x08, 0x40, 
0xa0, 0x00, 0x00, 0x00, 0x00, 0x84, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x05, 0x00, 0x50, 0x00, 0x00, 0x50, 0x00, 0x01, 0xe0, 0xf0, 0x82, 0x8e, 0x0f, 
0x00, 0x03, 0xc1, 0xe0, 0xf0, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x28, 0x20, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x18, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma09nArabic1 = {0x01, 19, 0, 19, 0, 0, 19, 74, 0x0750, 0x077f,
(PEGUSHORT *) Tahoma09nArabic1_offset_table, &Tahoma09nArabic2, 
(PEGUBYTE *) Tahoma09nArabic1_data_table};


ROMDATA PEGUSHORT Tahoma09nArabic_offset_table[257] = {
0x0000,0x000c,0x0023,0x0035,0x0044,0x0053,0x0062,0x0071,0x0080,0x008f,0x009e,0x00ad,0x00b8,0x00bd,0x00c2,0x00d4,
0x00dd,0x00e3,0x00e9,0x00ee,0x00f6,0x00fc,0x0101,0x0110,0x011f,0x012e,0x013d,0x014c,0x0151,0x0160,0x016f,0x0174,
0x017b,0x018a,0x0190,0x0194,0x0198,0x019e,0x01a2,0x01af,0x01b3,0x01bf,0x01c6,0x01d2,0x01de,0x01e7,0x01f0,0x01f9,
0x0200,0x0207,0x020c,0x0211,0x0223,0x0235,0x0246,0x0257,0x0262,0x026d,0x0276,0x027f,0x028e,0x029d,0x02ac,0x02bb,
0x02ca,0x02cf,0x02dc,0x02e9,0x02f4,0x02fe,0x0307,0x0311,0x0318,0x031e,0x032b,0x0338,0x033c,0x0342,0x0346,0x034a,
0x034e,0x0352,0x0357,0x035a,0x035e,0x0361,0x0364,0x0365,0x0369,0x036c,0x036f,0x0372,0x0376,0x0377,0x037b,0x037e,
0x038d,0x0396,0x039f,0x03a8,0x03b1,0x03ba,0x03c3,0x03cc,0x03d5,0x03de,0x03e7,0x03f0,0x03f5,0x03fa,0x0402,0x040e,
0x041b,0x041c,0x0420,0x0424,0x0428,0x042b,0x0431,0x0437,0x043e,0x044b,0x0457,0x0463,0x046f,0x047b,0x0487,0x0493,
0x049f,0x04ab,0x04b4,0x04bd,0x04c6,0x04cf,0x04d8,0x04e1,0x04ea,0x04f1,0x04f8,0x04ff,0x0506,0x050d,0x0514,0x051b,
0x0522,0x0529,0x052e,0x0533,0x0539,0x053e,0x0545,0x054a,0x054f,0x0554,0x0559,0x056b,0x057d,0x058f,0x05a0,0x05b1,
0x05bc,0x05c5,0x05d2,0x05df,0x05ec,0x05f9,0x0606,0x0613,0x0620,0x062d,0x063b,0x064d,0x065b,0x0666,0x0671,0x067c,
0x068a,0x0698,0x06a6,0x06b4,0x06c2,0x06d0,0x06da,0x06e4,0x06ee,0x06f8,0x0702,0x070c,0x0716,0x0720,0x072a,0x0735,
0x073e,0x0745,0x074d,0x0755,0x075d,0x0764,0x076a,0x0770,0x0776,0x077c,0x0782,0x0788,0x078e,0x079b,0x07a9,0x07b6,
0x07bc,0x07c9,0x07d6,0x07e4,0x07f2,0x07f6,0x07fd,0x0805,0x080e,0x0812,0x0817,0x081b,0x081e,0x0826,0x0837,0x0847,
0x0848,0x0849,0x084d,0x0852,0x085a,0x085e,0x0861,0x0867,0x086d,0x0871,0x087e,0x0883,0x0888,0x0889,0x088e,0x0895,
0x089a,0x08a3,0x08ac,0x08b5,0x08be,0x08c7,0x08d0,0x08d9,0x08e2,0x08eb,0x08f4,0x0906,0x0917,0x0920,0x0926,0x092f,
0x093a};



ROMDATA PEGUBYTE Tahoma09nArabic_data_table[5328 + 296] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x01, 0x40, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x54, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x05, 0x40, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xae, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x80, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0f, 0xf0, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x05, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x01, 0x80, 0x06, 0x00, 0x18, 0x00, 0x60, 0x01, 0x80, 0x56, 0x00, 0x00, 0x20, 0x14, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x08, 0x00, 0x00, 0x10, 0x0f, 0x80, 0x03, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x40, 0x04, 0x00, 0x00, 0x00, 0x00, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x80, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x40, 0x00, 0x02, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x18, 0x00, 0x60, 0x01, 0x80, 0x06, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x18, 0x30, 0x00, 0x00, 0x10, 0x70, 0x70, 0x04, 0x80, 0x00, 0x00, 0x02, 0x88, 0x00, 0x06, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0xf0, 0x00, 0x7f, 0xf0, 0xff, 0xe0, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x80, 0x00, 0x31, 
0xf8, 0x00, 0xf0, 0x01, 0xe0, 0x03, 0xc0, 0x07, 0x80, 0x00, 0x1f, 0xfc, 0x3f, 0xf8, 0x03, 0xc7, 
0xff, 0x00, 0x44, 0x01, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 0x00, 0x01, 
0xe0, 0x03, 0xc0, 0x07, 0x80, 0x0f, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x80, 0x14, 0x00, 0x20, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0xc0, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 
0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x04, 0x44, 0x09, 0x00, 0x00, 0x00, 0x40, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x00, 0x08, 0x00, 0x00, 0x0f, 0x00, 0x00, 
0x78, 0x00, 0x00, 0x0a, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 
0x00, 0x05, 0x00, 0x08, 0x00, 0xa0, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x88, 0x29, 0x00, 0x20, 
0x18, 0x80, 0x62, 0x01, 0x88, 0x06, 0x20, 0x18, 0x80, 0x62, 0x00, 0x80, 0x20, 0x08, 0x02, 0x00, 
0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x80, 0x00, 0x00, 0x00, 0x10, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x3c, 0x00, 0x00, 0x0c, 0x7f, 0xf0, 0x7f, 0xf8, 0x00, 0x00, 0x03, 0x90, 0x08, 0x19, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x00, 0x10, 0x04, 0x20, 0x08, 0x04, 0x24, 
0x01, 0x00, 0x44, 0x11, 0x00, 0x00, 0x80, 0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 0x00, 0x21, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x80, 0x20, 0x00, 0x81, 0x84, 0xc9, 0x07, 0x00, 0x08, 0x48, 0x20, 0x80, 0xf0, 0x82, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x04, 0x44, 0x0b, 0x82, 0x44, 0x00, 0xe0, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 
0x48, 0xa0, 0x05, 0x00, 0x14, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x01, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x01, 0xc0, 0x08, 0x01, 0x00, 0x20, 
0x03, 0x00, 0x0e, 0x00, 0x30, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0x80, 0x20, 0x08, 0x02, 0x00, 
0x00, 0x00, 0xf0, 0x00, 0x04, 0x00, 0x00, 0x00, 0x80, 0x01, 0x01, 0x40, 0x00, 0x00, 0x28, 0x80, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x48, 0x48, 0x00, 0x00, 0x00, 0xe0, 0x08, 0x66, 0xcc, 
0x00, 0x00, 0x80, 0x00, 0x04, 0x0c, 0x26, 0x48, 0x30, 0x10, 0x00, 0x41, 0x04, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x02, 0x10, 0x04, 0x20, 0x08, 0x04, 0x04, 
0x01, 0x00, 0x44, 0x21, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x02, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x20, 0x20, 0x04, 0x80, 0x00, 0x01, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x80, 0x20, 0x00, 0x40, 0xf8, 0x76, 0x18, 0x00, 0x07, 0xc8, 0x20, 0x81, 0x09, 0xc4, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x84, 0x44, 0x08, 0x04, 0xa8, 0x00, 0x00, 0xf0, 0x04, 0x00, 0x00, 0x00, 0x00, 
0xa0, 0x00, 0x00, 0xa0, 0x00, 0x01, 0xc0, 0x40, 0x00, 0x00, 0x14, 0x00, 0x00, 0x1f, 0x00, 0x00, 
0xf8, 0x00, 0x00, 0x04, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x40, 0x00, 0x00, 0x00, 0x14, 0x0a, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0c, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00, 0x06, 0xa0, 0x08, 0x01, 0x00, 0x20, 
0x0c, 0x00, 0x35, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x80, 0x20, 0x08, 0x02, 0x00, 
0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x21, 0xc0, 0x03, 0x80, 0x00, 0x00, 0x14, 0x18, 0x82, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x0c, 0x50, 0x28, 0x00, 0x00, 0x03, 0x7e, 0x00, 0x1b, 0xb0, 
0x00, 0x01, 0x40, 0x00, 0x02, 0x07, 0xc3, 0xb2, 0x40, 0x28, 0x0e, 0x41, 0x04, 0x08, 0x40, 0x00, 
0x80, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x18, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x80, 0x04, 0x12, 
0xc8, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x04, 0x10, 0x04, 0x20, 0x08, 0x04, 0x04, 
0x01, 0x00, 0x44, 0x71, 0x02, 0x38, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 0xe0, 0x71, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x01, 0x80, 0x0c, 0x02, 0x20, 0x08, 
0x00, 0x10, 0x00, 0x00, 0x03, 0x80, 0x1c, 0x22, 0xc8, 0x88, 0x8a, 0x48, 0x90, 0x00, 0x48, 0x44, 
0x80, 0x20, 0x00, 0x40, 0x80, 0x40, 0x10, 0x07, 0x00, 0x44, 0x41, 0x41, 0x08, 0x88, 0x00, 0x04, 
0x00, 0x00, 0x02, 0x44, 0x44, 0x08, 0x0e, 0x7c, 0x03, 0x80, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x01, 0xc0, 0x06, 0x00, 0x30, 0x01, 0x80, 0x0c, 0x00, 
0x60, 0x03, 0x00, 0x12, 0x00, 0x90, 0x08, 0x00, 0x00, 0x00, 0x08, 0x40, 0x88, 0x11, 0x02, 0x20, 
0x10, 0x00, 0x42, 0x01, 0x00, 0x04, 0x00, 0x10, 0x00, 0x40, 0x00, 0x80, 0x20, 0x08, 0x02, 0x04, 
0x00, 0x01, 0xf0, 0x10, 0x0a, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x60, 0x85, 
0x14, 0x50, 0x03, 0x80, 0x0e, 0x04, 0x70, 0x80, 0x0e, 0x00, 0x70, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x10, 0x14, 0x00, 0x00, 0x09, 0x00, 0x04, 0x60, 0x18, 0x00, 0x00, 0x84, 0x00, 0x08, 0x0c, 0x60, 
0x21, 0x00, 0x01, 0x00, 0x02, 0x04, 0x02, 0x01, 0x40, 0x44, 0x10, 0x22, 0x0a, 0x08, 0x40, 0x00, 
0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x1c, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x24, 0x00, 0x00, 0x00, 0x02, 0x00, 0x21, 0x04, 0x08, 0x24, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x06, 0x10, 0x04, 0x20, 0x08, 0x02, 0x04, 
0x01, 0x00, 0x44, 0x01, 0x07, 0x44, 0x80, 0x00, 0x40, 0x14, 0x01, 0x40, 0x00, 0x00, 0x00, 0x10, 
0x20, 0x02, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x01, 0x00, 0x81, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x02, 0x40, 0x12, 0x04, 0x20, 0x08, 
0x00, 0x00, 0x10, 0x00, 0x04, 0x40, 0x22, 0x44, 0x11, 0x11, 0x10, 0x91, 0x20, 0x90, 0x90, 0x88, 
0x80, 0x20, 0x80, 0x20, 0x40, 0x20, 0x08, 0x08, 0x80, 0x44, 0x41, 0x41, 0x08, 0x08, 0x00, 0x15, 
0x00, 0x00, 0x02, 0x44, 0x44, 0x08, 0x01, 0x80, 0x04, 0x41, 0xf0, 0x04, 0x00, 0x00, 0x0a, 0x00, 
0x40, 0x00, 0x00, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x10, 0x20, 
0x40, 0x81, 0x02, 0x04, 0x08, 0x7c, 0x80, 0x00, 0x00, 0x14, 0xa5, 0x00, 0x10, 0x00, 0x00, 0x00, 
0x00, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x08, 0x02, 0x00, 0x09, 0x00, 0x48, 0x02, 0x40, 0x12, 0x00, 
0x90, 0x04, 0x80, 0x12, 0x00, 0x90, 0x08, 0x00, 0x40, 0x00, 0x08, 0x01, 0x08, 0x21, 0x04, 0x20, 
0x10, 0x00, 0x40, 0x01, 0x00, 0x04, 0x00, 0x10, 0x00, 0x40, 0x00, 0x80, 0x20, 0x08, 0x02, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x81, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x40, 0x11, 0x00, 0x88, 0x00, 0x11, 0x00, 0x88, 0x00, 0x00, 0x38, 0x00, 0x80, 
0x20, 0x20, 0x42, 0x24, 0x12, 0x00, 0x02, 0xc3, 0x0c, 0x08, 0x41, 0x08, 0x00, 0x10, 0x8e, 0xe0, 
0x42, 0x04, 0x82, 0x84, 0x01, 0x02, 0x01, 0x00, 0xf8, 0x44, 0x10, 0x22, 0x0a, 0x08, 0x40, 0x01, 
0x40, 0x00, 0x00, 0x40, 0x00, 0x00, 0x02, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x22, 0x00, 0x00, 0x00, 0x02, 0x00, 0x42, 0x08, 0x10, 0x48, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x06, 0x10, 0x04, 0x20, 0x08, 0x01, 0x04, 
0x01, 0x0c, 0x44, 0x61, 0x00, 0x40, 0x80, 0x00, 0x20, 0x00, 0x00, 0x00, 0xf0, 0x78, 0x3c, 0x08, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x38, 0x27, 0x04, 0xe1, 0x00, 0x81, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x02, 0x20, 0x11, 0x02, 0x20, 0x08, 
0x38, 0x81, 0x08, 0x60, 0x04, 0x00, 0x20, 0x88, 0x22, 0x22, 0x21, 0x22, 0x49, 0x23, 0x21, 0x10, 
0x80, 0x21, 0xc0, 0x20, 0x20, 0x10, 0x04, 0x08, 0x80, 0x44, 0x41, 0x41, 0x08, 0x10, 0x00, 0x0e, 
0x00, 0x00, 0x02, 0x54, 0x44, 0x08, 0x30, 0x60, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x04, 0x08, 0x10, 
0x20, 0x40, 0x81, 0x02, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1c, 0x00, 0x0e, 0x09, 0xc2, 0x00, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x12, 0x00, 0x90, 0x06, 0x00, 0x80, 0x00, 0x06, 0x00, 0x88, 0x11, 0x02, 0x20, 
0x0c, 0x00, 0x30, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x80, 0x20, 0x08, 0x02, 0x40, 
0x90, 0x24, 0x09, 0x02, 0x40, 0x87, 0x21, 0xe0, 0x40, 0x80, 0x80, 0x81, 0x80, 0x18, 0x61, 0x86, 
0x18, 0x60, 0x04, 0x00, 0x10, 0x00, 0x81, 0x80, 0x10, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x40, 0x40, 0x84, 0x48, 0x22, 0x00, 0x03, 0x44, 0x8a, 0x10, 0x82, 0x10, 0x00, 0x21, 0x0e, 0xe0, 
0x84, 0x08, 0x40, 0x0e, 0x01, 0x01, 0x00, 0x80, 0x80, 0x82, 0x12, 0x22, 0x0a, 0x08, 0x40, 0x00, 
0x00, 0x00, 0x38, 0x40, 0x30, 0x38, 0x39, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x22, 0x00, 0x00, 0x00, 0x02, 0x20, 0x42, 0x08, 0x10, 0x48, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x00, 0x10, 0x04, 0x20, 0x08, 0x00, 0x84, 
0x01, 0x10, 0x44, 0x91, 0x10, 0x30, 0x88, 0x04, 0x51, 0x00, 0x90, 0x08, 0x1c, 0x0e, 0x07, 0x04, 
0x08, 0x00, 0x00, 0x00, 0x40, 0x00, 0x10, 0x00, 0x88, 0x00, 0x44, 0x28, 0x85, 0x11, 0x30, 0x99, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x82, 0x20, 0x11, 0x04, 0x24, 0x08, 
0x44, 0x81, 0x14, 0x91, 0x03, 0x08, 0x18, 0x88, 0x22, 0x22, 0x21, 0x22, 0x49, 0x23, 0x21, 0x10, 
0x80, 0x21, 0xc0, 0x20, 0x20, 0x10, 0x04, 0x10, 0x40, 0x42, 0x82, 0x20, 0xf8, 0x20, 0x00, 0x3f, 
0x90, 0x08, 0x01, 0xd4, 0x44, 0x08, 0x48, 0x91, 0x03, 0x08, 0x04, 0x80, 0x48, 0x04, 0x80, 0x48, 
0x04, 0x80, 0x48, 0x04, 0x80, 0x40, 0xe0, 0x70, 0x38, 0x1c, 0x0e, 0x07, 0x03, 0x82, 0x04, 0x08, 
0x10, 0x20, 0x40, 0x81, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x10, 
0x00, 0x04, 0x00, 0x22, 0x00, 0x11, 0x0a, 0x22, 0x62, 0x08, 0x90, 0x44, 0x82, 0x24, 0x11, 0x20, 
0x89, 0x04, 0x40, 0x0e, 0x00, 0x72, 0x01, 0x01, 0x00, 0x02, 0x01, 0x01, 0x08, 0x21, 0x04, 0x24, 
0x02, 0x10, 0x08, 0x40, 0x21, 0x00, 0x84, 0x02, 0x10, 0x08, 0x40, 0x90, 0x24, 0x09, 0x02, 0x40, 
0x90, 0x24, 0x09, 0x02, 0x40, 0x88, 0xa0, 0x38, 0xa1, 0x41, 0x41, 0x42, 0x43, 0x24, 0x92, 0x49, 
0x24, 0x91, 0x03, 0x04, 0x0c, 0x20, 0x62, 0x44, 0x0c, 0x20, 0x60, 0x00, 0x00, 0x00, 0x00, 0xa0, 
0x40, 0x40, 0x84, 0x48, 0x22, 0x00, 0x03, 0x44, 0x8a, 0x10, 0x82, 0x10, 0x00, 0x21, 0x0f, 0xe0, 
0x84, 0x08, 0x20, 0x0e, 0x01, 0x01, 0x00, 0x80, 0x40, 0x82, 0x0c, 0x14, 0x11, 0x07, 0xc0, 0x00, 
0x08, 0x00, 0x44, 0x4c, 0x40, 0x44, 0x45, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x1e, 0x20, 0x0e, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x00, 0x10, 0x04, 0x20, 0x08, 0x00, 0x84, 
0x01, 0x10, 0x44, 0x89, 0x10, 0x0c, 0x88, 0x04, 0x89, 0x00, 0x90, 0x08, 0x60, 0x30, 0x18, 0x02, 
0x04, 0x42, 0x00, 0x44, 0x40, 0x11, 0x10, 0x01, 0x08, 0x00, 0x84, 0x30, 0x86, 0x10, 0xc0, 0x61, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x81, 0xe4, 0x0f, 0x00, 0x24, 0x08, 
0x44, 0x81, 0x22, 0x89, 0x00, 0xc8, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x80, 0x20, 0x80, 0x20, 0x20, 0x10, 0x08, 0x10, 0x40, 0x42, 0x82, 0x20, 0x08, 0x40, 0x01, 0x0e, 
0x10, 0x09, 0x00, 0x44, 0x44, 0x08, 0x44, 0x89, 0x00, 0xc8, 0x04, 0x80, 0x48, 0x04, 0x80, 0x48, 
0x04, 0x80, 0x48, 0x04, 0x80, 0x43, 0x01, 0x80, 0xc0, 0x60, 0x30, 0x18, 0x0c, 0x01, 0x02, 0x04, 
0x08, 0x10, 0x20, 0x40, 0x81, 0x10, 0x84, 0x10, 0x81, 0x08, 0x42, 0x00, 0x44, 0x40, 0x11, 0x10, 
0x04, 0x44, 0x00, 0x42, 0x00, 0x21, 0x0c, 0x21, 0x82, 0x07, 0x90, 0x3c, 0x81, 0xe4, 0x0f, 0x20, 
0x79, 0x03, 0xc8, 0x02, 0x40, 0x12, 0x00, 0x82, 0x00, 0x02, 0x00, 0x80, 0x08, 0x01, 0x00, 0x24, 
0x01, 0x10, 0x04, 0x40, 0x11, 0x00, 0x44, 0x01, 0x10, 0x04, 0x40, 0x90, 0x24, 0x09, 0x02, 0x40, 
0x90, 0x24, 0x09, 0x02, 0x40, 0x88, 0x90, 0xc1, 0x12, 0x22, 0x22, 0x22, 0x24, 0xa2, 0x8a, 0x28, 
0xa2, 0x89, 0x00, 0xdc, 0x02, 0x20, 0x1a, 0x24, 0x03, 0x20, 0x18, 0x08, 0x00, 0x20, 0x01, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0xc3, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x70, 
0x00, 0x00, 0x11, 0x04, 0x01, 0x01, 0x00, 0x80, 0x40, 0x82, 0x08, 0x14, 0x11, 0x00, 0x40, 0x08, 
0x88, 0x00, 0x84, 0x30, 0x40, 0x44, 0x44, 0x80, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x02, 0x40, 0x12, 0x00, 0x02, 0x00, 0x42, 0x08, 0x10, 0x48, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x04, 
0x01, 0x12, 0x44, 0x89, 0x10, 0x04, 0x88, 0x04, 0x89, 0x00, 0x90, 0x08, 0x80, 0x40, 0x20, 0x02, 
0x04, 0x21, 0x20, 0x44, 0x48, 0x11, 0x12, 0x09, 0x09, 0x04, 0x84, 0x30, 0x86, 0x11, 0x00, 0x81, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x80, 0x24, 0x01, 0x20, 0x24, 0x08, 
0x44, 0x81, 0x22, 0x89, 0x00, 0x48, 0x02, 0x88, 0x22, 0x22, 0x21, 0x22, 0x49, 0x23, 0x21, 0x10, 
0x80, 0x20, 0x00, 0x20, 0x20, 0x10, 0x10, 0x10, 0x40, 0x42, 0x82, 0x20, 0x08, 0x44, 0x02, 0x15, 
0x10, 0x09, 0x00, 0x54, 0x44, 0x08, 0x44, 0x89, 0x00, 0x48, 0x04, 0x80, 0x48, 0x04, 0x80, 0x48, 
0x04, 0x80, 0x48, 0x04, 0x80, 0x44, 0x02, 0x01, 0x00, 0x90, 0x40, 0x20, 0x15, 0x01, 0x02, 0x04, 
0x08, 0x10, 0x20, 0x40, 0x81, 0x08, 0x42, 0x08, 0x40, 0x84, 0x21, 0x20, 0x44, 0x48, 0x11, 0x12, 
0x04, 0x44, 0x82, 0x42, 0x41, 0x21, 0x0c, 0x22, 0x02, 0x00, 0x90, 0x04, 0x80, 0x24, 0x01, 0x20, 
0x09, 0x00, 0x48, 0x02, 0x40, 0x12, 0x00, 0x83, 0xff, 0xfa, 0x00, 0x88, 0x09, 0x01, 0x20, 0x24, 
0x01, 0x10, 0x04, 0x40, 0x11, 0x00, 0x44, 0x01, 0x10, 0x04, 0x40, 0x90, 0x24, 0x09, 0x02, 0x40, 
0x90, 0x24, 0x09, 0x02, 0x40, 0x88, 0x91, 0x01, 0x12, 0x22, 0x22, 0x22, 0x24, 0xa2, 0x8a, 0x28, 
0xa2, 0x89, 0x00, 0x44, 0x01, 0x20, 0x0a, 0x24, 0x01, 0x20, 0x08, 0x14, 0x00, 0x50, 0x01, 0x10, 
0x40, 0x40, 0x84, 0x48, 0x21, 0x00, 0x04, 0x60, 0x18, 0x10, 0x82, 0x10, 0x00, 0x21, 0x17, 0xd0, 
0x84, 0x08, 0x10, 0x80, 0x01, 0x01, 0x00, 0x80, 0x40, 0x82, 0x10, 0x14, 0x11, 0x00, 0x44, 0x08, 
0x89, 0x04, 0x84, 0x40, 0x48, 0x44, 0x44, 0x80, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 
0x90, 0x00, 0x40, 0x10, 0x80, 0x20, 0x04, 0x60, 0x11, 0x00, 0x04, 0xe0, 0x21, 0x04, 0x08, 0x24, 
0x48, 0x00, 0x90, 0x01, 0x20, 0x02, 0x40, 0x04, 0x80, 0x06, 0x10, 0x04, 0x20, 0x08, 0x00, 0x84, 
0x01, 0x0c, 0x44, 0x79, 0x08, 0x08, 0x84, 0x08, 0x88, 0x81, 0x08, 0x11, 0x00, 0x80, 0x40, 0x02, 
0x04, 0x21, 0x20, 0x44, 0x48, 0x11, 0x12, 0x0a, 0x11, 0x05, 0x08, 0x21, 0x04, 0x22, 0x01, 0x01, 
0x20, 0x02, 0x40, 0x04, 0x80, 0x09, 0x00, 0x12, 0x00, 0x00, 0x40, 0x44, 0x02, 0x20, 0x22, 0x10, 
0xc4, 0x42, 0x22, 0x78, 0x80, 0x84, 0x04, 0x44, 0x11, 0x11, 0x10, 0x91, 0x20, 0x90, 0x90, 0x88, 
0x80, 0x20, 0x00, 0x20, 0x20, 0x10, 0x20, 0x10, 0x40, 0x21, 0x06, 0x10, 0x04, 0x8e, 0x33, 0x04, 
0x08, 0x11, 0x00, 0x84, 0x44, 0x08, 0x3c, 0x78, 0x80, 0x84, 0x08, 0x40, 0x84, 0x08, 0x40, 0x84, 
0x08, 0x40, 0x84, 0x08, 0x40, 0x88, 0x04, 0x02, 0x01, 0x00, 0x80, 0x4a, 0x20, 0x01, 0x02, 0x04, 
0x08, 0x10, 0x20, 0x40, 0x81, 0x08, 0x42, 0x08, 0x40, 0x84, 0x21, 0x20, 0x44, 0x48, 0x11, 0x12, 
0x04, 0x44, 0x82, 0x84, 0x41, 0x42, 0x08, 0x44, 0x01, 0x01, 0x08, 0x08, 0x40, 0x42, 0x02, 0x10, 
0x10, 0x80, 0x88, 0x04, 0x40, 0x21, 0x00, 0x80, 0x00, 0x09, 0x00, 0x88, 0x09, 0x01, 0x20, 0x22, 
0x01, 0x08, 0x04, 0x20, 0x10, 0x80, 0x42, 0x01, 0x08, 0x04, 0x21, 0x08, 0x42, 0x10, 0x84, 0x21, 
0x08, 0x42, 0x10, 0x84, 0x21, 0x08, 0x92, 0x51, 0x12, 0x22, 0x22, 0x21, 0xe3, 0x9e, 0x79, 0xe7, 
0x9e, 0x78, 0x80, 0x82, 0x02, 0x10, 0x11, 0xe2, 0x02, 0x10, 0x10, 0x22, 0x00, 0x88, 0x01, 0x10, 
0x20, 0x20, 0x42, 0x24, 0x11, 0x80, 0x0c, 0x50, 0x28, 0x08, 0x41, 0x08, 0x00, 0x10, 0xa0, 0x08, 
0x42, 0x04, 0x10, 0x80, 0x01, 0x01, 0x00, 0x80, 0x40, 0x92, 0x20, 0x08, 0x30, 0x80, 0x24, 0x08, 
0x89, 0x05, 0x08, 0x80, 0x30, 0xa4, 0x44, 0x80, 

0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x03, 0x83, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 
0xf0, 0x00, 0x7f, 0xf0, 0xff, 0xe7, 0xf8, 0x60, 0xcf, 0xff, 0xe5, 0x10, 0x00, 0x80, 0x04, 0x12, 
0xf8, 0x00, 0xf0, 0x01, 0xe0, 0x03, 0xc0, 0x07, 0x80, 0x06, 0x1f, 0xfc, 0x3f, 0xf8, 0xa0, 0x87, 
0xff, 0x10, 0x44, 0x09, 0x07, 0xf0, 0x83, 0xf0, 0x70, 0x7e, 0x07, 0xe1, 0x10, 0x80, 0x40, 0x3c, 
0x78, 0x21, 0x20, 0x7b, 0x88, 0x1e, 0xe2, 0x0f, 0xe1, 0x07, 0xf1, 0xfe, 0x3f, 0xc2, 0x01, 0x01, 
0xe0, 0x03, 0xc0, 0x07, 0x80, 0x0f, 0x00, 0x1e, 0x00, 0x3e, 0x3f, 0x82, 0x04, 0x1f, 0xc1, 0xe0, 
0xb8, 0x3c, 0x1c, 0x08, 0x7f, 0x03, 0xf8, 0x22, 0xc8, 0x88, 0x8a, 0x48, 0x90, 0x00, 0x48, 0x44, 
0xff, 0xe0, 0x00, 0x20, 0x20, 0x10, 0x3f, 0x8f, 0x80, 0x21, 0x04, 0x10, 0x05, 0x04, 0x33, 0x00, 
0x07, 0xe0, 0x81, 0x04, 0x44, 0x08, 0x04, 0x08, 0x7f, 0x03, 0xf0, 0x3f, 0x03, 0xf0, 0x3f, 0x03, 
0xf0, 0x3f, 0x03, 0xf0, 0x3f, 0x08, 0x04, 0x02, 0x51, 0x10, 0x80, 0x40, 0x25, 0x1e, 0x3c, 0x78, 
0xf1, 0xe3, 0xc7, 0x8f, 0x1e, 0x08, 0x42, 0x08, 0x42, 0x84, 0x21, 0x20, 0x7b, 0x88, 0x1e, 0xe2, 
0x07, 0xb8, 0x83, 0xf8, 0x41, 0xfc, 0x7f, 0x84, 0x00, 0xfe, 0x07, 0xf0, 0x3f, 0x81, 0xfc, 0x0f, 
0xe0, 0x7f, 0x04, 0x08, 0x20, 0x40, 0xff, 0x1f, 0xff, 0xf0, 0xff, 0x07, 0xf0, 0xfe, 0x1f, 0xc1, 
0xfe, 0x07, 0xf8, 0x1f, 0xe0, 0x7f, 0x81, 0xfe, 0x07, 0xf8, 0x1e, 0x07, 0x81, 0xe0, 0x78, 0x1e, 
0x07, 0x81, 0xe0, 0x78, 0x1e, 0x3f, 0xe2, 0x00, 0xe1, 0xc1, 0xc1, 0xc0, 0x20, 0x82, 0x08, 0x20, 
0x82, 0x08, 0x7f, 0x01, 0xfc, 0x0f, 0xe0, 0x21, 0xfc, 0x0f, 0xe1, 0xc0, 0x07, 0x00, 0x3c, 0xe0, 
0x10, 0x14, 0x00, 0x00, 0x08, 0x60, 0x30, 0x48, 0x48, 0x00, 0x00, 0x84, 0x00, 0x08, 0x7f, 0xfc, 
0x21, 0x01, 0xe0, 0x80, 0x01, 0x01, 0x00, 0x80, 0x40, 0x6c, 0x20, 0x08, 0x20, 0x80, 0x24, 0x0f, 
0x71, 0x07, 0xf0, 0x88, 0x40, 0xb9, 0xff, 0x00, 

0x00, 0x84, 0xc4, 0x01, 0xe9, 0x00, 0x00, 0x02, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x06, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x80, 0x40, 0x00, 
0x00, 0x21, 0x20, 0x40, 0x08, 0x10, 0x02, 0x08, 0x01, 0x04, 0x00, 0x00, 0x00, 0x02, 0x01, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf8, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 
0x00, 0x00, 0x7e, 0x00, 0x07, 0x00, 0x04, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x0a, 0x00, 
0x00, 0x0a, 0x00, 0x00, 0x0a, 0x08, 0x04, 0x02, 0x01, 0x00, 0x80, 0x44, 0x20, 0x00, 0x14, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x43, 0x08, 0x40, 0x84, 0x21, 0x20, 0x40, 0x08, 0x12, 0x82, 
0x04, 0xa0, 0x82, 0x00, 0x41, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x50, 0x00, 0x03, 0xf0, 0x1f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 
0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x02, 0x20, 0x00, 0x00, 0x00, 0x02, 0x2f, 0x82, 0x08, 0x20, 
0x82, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x20, 0x02, 0x82, 0x00, 0x08, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xf0, 0x7f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x08, 
0x01, 0x04, 0x00, 0x80, 0x00, 0xa8, 0x00, 0x00, 

0x00, 0x85, 0x24, 0x3e, 0x28, 0x80, 0x00, 0x14, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x01, 0x00, 0x00, 0x02, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x84, 0x42, 0x21, 0x00, 
0x00, 0x42, 0x10, 0x80, 0x04, 0x20, 0x01, 0x10, 0x00, 0x88, 0x00, 0x00, 0x00, 0x01, 0x08, 0x84, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0xa0, 0x00, 0x04, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x08, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x22, 0x11, 0x08, 0x84, 0x42, 0x21, 0x10, 0x80, 0x08, 0x10, 
0x40, 0x02, 0x80, 0x00, 0x00, 0x10, 0x86, 0x90, 0xa9, 0x08, 0x42, 0x10, 0x90, 0x04, 0x20, 0x01, 
0x08, 0x00, 0x44, 0x28, 0x22, 0x00, 0x00, 0x02, 0x10, 0x00, 0x00, 0x10, 0x04, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x01, 0x08, 0x00, 0x00, 0x00, 0x05, 0x43, 0x04, 0x10, 0x41, 
0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x01, 0xff, 0xf7, 0xff, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x70, 0x04, 0x80, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x10, 
0x80, 0x88, 0x20, 0x42, 0x29, 0x28, 0x00, 0x00, 

0x7f, 0xe7, 0x3b, 0xc0, 0x47, 0xff, 0xf7, 0xef, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x02, 0x00, 0x00, 0x05, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xe3, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x3c, 0x1e, 0x00, 
0x01, 0x8c, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0xe0, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x78, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x70, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 
0x00, 0x04, 0x00, 0x00, 0x0a, 0x03, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x0f, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x19, 0x67, 0x16, 0x71, 0x8c, 0x0f, 0x00, 0x03, 0xc1, 0x00, 
0xf0, 0x40, 0x38, 0x00, 0x1c, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x03, 0x8c, 0x38, 0xe3, 0x8e, 
0x38, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x03, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 
0x00, 0x70, 0x00, 0x3c, 0x2a, 0x28, 0x00, 0x00, 


};

xFONTYY Tahoma09nArabic_Font = {0x01, 19, 0, 19, 0, 0, 19, 296, 0x0600, 0x06ff,
(PEGUSHORT *) Tahoma09nArabic_offset_table, &Tahoma09nArabic1, 
(PEGUBYTE *) Tahoma09nArabic_data_table};


