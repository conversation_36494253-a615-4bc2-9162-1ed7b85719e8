#include <string.h>
#include "Wnd.hpp"

#ifndef __EDIT_CTRL_HPP__
#define __EDIT_CTRL_HPP__

class CEditCtrl : public CWnd {
protected:
	RECT   m_rectEdit;
	POINT  m_ptCursor;
	int    m_nFontSize;
	BYTE  *m_pszText;
	BYTE  *m_pszBuffer;
	BYTE  *m_pszFormat;
	int    m_nTextLen;
	int    m_nMaxLen;
	int    m_nCurCharPos;
	int    m_nHLimit, m_nVLimit;
	int    m_nFontWidth, m_nFontHeight;
	BOOL   m_bFocus;
	BOOL   m_bOnlyNumeric;
	BOOL   m_bPassword;
	BOOL   m_bEditMode;
	BOOL   m_bModify;
	int    m_nPrevKey;
	BOOL   m_bEnable;
	BOOL   m_bCnLang;
	BOOL   m_bEditComplete;

	FONT  *m_pFont;

	BYTE PutChar(BYTE nChar);
	BYTE PutCompleteChar(BYTE nChar);
	BYTE MoveNext();
	BYTE DelChar();
	
	void SetFontSize(int nFontSize);
	void SetMaxLen(int nMaxLen);
	char GetCurChar(char szChar[2]);

public:
	CEditCtrl(cSCREEN *pScreen, const BYTE **pCaption=(const BYTE **)0, DWORD dWndID=0);
	~CEditCtrl();

	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	void OnKeyEvent(int nKey, DWORD nFlags=0);
	void OnCursorEvent(int nState);

	void Create(int x, int y, int width, int height, int nMaxLen=512,
		        BOOL bOnlyNumeric=0, BOOL bPassword=0, int nFontSize=FONT_ENG_12X06_WFix);
	void Create(RECT rectSize, int nMaxLen, BOOL bOnlyNumeric=0,
		        BOOL bPassword=0, int nFontSize=FONT_ENG_12X06_WFix);//FONT_ENG_16X08

	void SetCnMode(BOOL bTLn) { m_bCnLang = bTLn; } // Han

	void SetEditMode(BOOL bEditMode=1) { m_bEditMode = bEditMode; }
	void SetFormat(const BYTE *pszFormat);
	void SetText(const BYTE *pszText);
	void SetAvailablInputCount(int nCount) { m_nMaxLen = nCount; }
	
	BOOL IsEditComplete() { return m_bEditComplete; }
	BOOL GetEditMode() { return m_bEditMode; }
	int  GetText(BYTE *pszText);
	int  GetFormat(BYTE *pszFormat);
	int  GetFontSize() { return m_nFontSize; }
	int  GetLength()   { return strlen((char *)m_pszText); }
	BOOL IsEmpty()     { return m_pszText[0] == '\0'; }

	void Reset(bool bRedraw = TRUE);
	void SetFocus(BOOL bFocus);
	void Enable(BOOL bEnable=1) { m_bEnable = bEnable; }
	BOOL IsEnable() { return m_bEnable; }
};

#endif
