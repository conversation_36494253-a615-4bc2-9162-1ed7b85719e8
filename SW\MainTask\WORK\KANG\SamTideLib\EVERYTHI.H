#ifndef _TIDELIB_EVERYTHI_H_
#define _TIDELIB_EVERYTHI_H_

#ifdef  __cplusplus
extern "C" {
#endif

/* everything.h - external declarations for everything */
// Changes for WXTide32 by mgh:
// Added all #ifdef _WINDOWS stuff (from original WinTide port)
// Added defs for window_list, put_location, moon_age, sun_rise_set

/* Include everything, all the time.  This saves _me_ time. */

#include <stdio.h>
#include <math.h>
#include <stdlib.h>
#include <signal.h>
//#include <unistd.h>
#include <assert.h>
#include <string.h>
#include <time.h>
//#include <sys/time.h> mgh-
#include <ctype.h>
#include "config.h"
#include "arglib.h"

/* Compatibility by <PERSON> */

#ifdef OS2
#include <float.h>
#endif /* OS2 */

#ifdef _WINDOWS /* Down to #endif added mgh */
#ifndef M_PI
#define M_PI 3.141592654
#endif
#define EOL "\r\n"
#undef putchar
#define putchar(c)\
  printf("%c", (c))
#define puts(s)\
  printf(s);   \
  printf("\r\n")
//#define assert win_assert
#else
#define EOL "\n"
#endif

int win_fprintf(FILE *stream,char const *fstring, ...);
#define fprintf win_fprintf

//#define assert(p)   ((p) ? (void)0 : (void) _assertfail("Assertion failed: ",#p, __FILE__, __LINE__ ))
void win_assert_sub(void);
#define win_assert(p)   ((p) ? (void)0 : (void)win_assert_sub())

#define linelen 3000

#ifndef MAX
#define MAX(a,b) (((a)<(b))?(b):(a))
#endif

/* Typo prevention */
#define DAYSECONDS 86400
#define HOURSECONDS 3600

/* Width of graph in ASCII graph mode */
#define TEXTWIDTH 73
/* Tstep adjustment for ASCII graph mode */
#define AGTSTEP 20
/* Tstep adjustment for banner mode */
#define BANTSTEP 10

/* Handle compilers that are anal about the type of signal handlers. */
#ifdef __SUNPRO_CC
#define SIGHANDTYPE void(*)(int)
#endif

#define STREND(s) s+strlen(s)
#define DEG2BAMS(d) ((WORD)((long)(d/360.0*65536.0) & 0x0000ffff))
#define BAMS2DEG(b) ((signed short)b*360.0/65536.0)
//#define DEG2BAMS(d) ((WORD)((long)(d/360.0*65536.0+(d<0.0? -.5 : 0.0)) & 0x0000ffff))
//#define BAMS2DEG(b) (((WORD)b*360.0/65536.0)+(b&0x8000? -360.0 : 0.0))

enum tideerr {BADCOLORSPEC, WANTMOREARGS, CONFIGFAIL,
  BADINTEGER, BADFLOAT, BADHHMM, STRETCHTOOSMALL, STRETCHTOOBIG,
  BADTIMESTAMP, MISSINGYEAR, BADGEOMETRY, CANTOPENDISPLAY, CANTOPENFILE,
  OFFSETSTEXTONLY, BADMULT, NODEANGIF, MAXBELOWMIN, ARGERR, OUTOFMEMORY};

/* Units stuff */

typedef enum {LENGTH, VELOCITY, BOGUS} unit_type;
typedef struct {
  char *name;
  char *abbrv;
  unit_type type;
  double conv_factor;
} unit;
#define NUMUNITS 4

#define MAXNAMELEN 90
#define USF_REMOVE 1
#define USF_UPDATE 2
#define USF_WRITE  3

// ISKANG
#define	NUM_HARMIDX			12984
#define	NUM_TCD_RECORD		7163


extern int hhmm2seconds (char *hhmm);
extern void load_data (void);
extern void UserStation(char *custom_name);
//extern void UserStationFuncs(int function, char *custom_name);
extern void check_epoch (void);
extern time_t parse_time_string (char *time_string);
extern int yearoftimet (time_t t);
extern void  figure_multipliers();
extern void happy_new_year (int new_year);
extern double time2tide (time_t t);
extern double time2atide (time_t);
extern double time2secondary (time_t t);
extern double time2asecondary (time_t);
extern void do_timestamp (char buf[20], struct tm *t);
extern struct tm *tmtime (time_t t);
extern int next_big_event (time_t *t);
extern int update_high_tide (void);
extern void list_tides (int);
extern void tide2ascii (void);
extern time_t prev_hour (time_t);
extern time_t increment_hour (time_t);
extern time_t prev_day (time_t);
extern time_t increment_day (time_t);
extern void change_time_zone (char *);
extern void barf (enum tideerr);
extern void do_calendar ();
extern void do_banner ();
extern void do_stats (time_t, time_t);
extern void do_raw (time_t, time_t, int);
extern char *strADupe (char **dst, char *src);
extern char *stradoop (char *);
extern void set_epoch (int, int, int);
extern void do_datestamp (char *, struct tm *);
extern char *nojunk (char *);
extern char *do_long_timestamp (struct tm *);
extern time_t sunday_month (time_t);
extern void tide2ps (int);
extern void fudge_constituents (char *);
extern int linterp (int, int, double);
extern int findunit (char *);
extern void make_depth_caption (int *, int, char *);
extern char *seconds2hhmm (int);
extern void do_incremental (int);
extern time_t tm2gmt (struct tm *ht);
extern time_t tz_gm2localtime(time_t t);
extern struct tm *tzlocaltime( time_t *t );
extern char *tz_get_name();
extern void add_mru( char *station );
extern char *tz_time2sec( char *psrc, long *timesec );
extern void noMemErr(char *err);

extern char hfile_name[MAXARGLEN+1], location[MAXARGLEN*2], *ppm, *gif,
  tzfile[MAXARGLEN+1], *geometry, units[MAXARGLEN+1],
  units_abbrv[MAXARGLEN+1], tadjust_tzname[MAXARGLEN+1];
extern char next_ht_text[20], next_ht_date[20], next_lt_text[20],
  next_lt_date[20], Izone[40], tadjust_last[256];

#ifndef WIN32_ORG
extern char fgrise_color_arg[], fgfall_color_arg[],
  fgtext_color_arg[], fgmark_color_arg[],
  fgmllw_color_arg[], fgmiddle_color_arg[];
#else
extern char *fgrise_color_arg, *fgfall_color_arg,
  *fgtext_color_arg, *fgmark_color_arg,
  *fgmllw_color_arg, *fgmiddle_color_arg;
#endif

extern int Usetadjust, Itadjust, tadjust, utc, list, checkyear, text, skinny, now, graphmode,
  httimeoff, lttimeoff, tstep, mark, middle, mllw, lines, PPMWIDTH,
  PPMHEIGHT, hinc, tinc, loctz, iscurrent, curonly, toplines, hincmagic,
  calendar, banner, weekday, hairy, linegraph, ps, noampm, uutc,
  have_offsets, sun_moon, had_map, increment_step, incremental_tides,
  Ihttimeoff,Ilttimeoff, num_days, num_months, subproc, OnlyTCD, map_cues, map_grid, datemdy, mapZoom;
extern time_t next_ht, prev_ht, next_ht_adj, prev_ht_adj, faketime, epoch;
extern double amplitude, htleveloff, ltleveloff, DATUM, marklev,
  absmax, absmin, fakedatum, fakeamplitude,
  Ihtleveloff,Iltleveloff,Ihlevelmult,Illevelmult,hlevelmult, llevelmult;
extern double Ilat, Ilon;
extern unit known_units[NUMUNITS];
extern float mapFindRadius;

#ifndef WIN32_ORG
extern char IDX_station_name[], custom_name[];
#else
extern char IDX_station_name[], *custom_name;
#endif

extern int window_list, keep_index, new_params, save_windows;
extern int put_location( char *loc, int rec_num );
extern time_t adjust_time_for_offsets(time_t t);
extern short int have_user_offsets;
extern int have_BOGUS, convert_BOGUS;
extern int check_file_path(char *dst, char*filename);
extern int moon_phase (time_t *moontime, int *phase, time_t nowtime);
extern int Next_Moon_Phase(time_t *t, int dir);
extern int next_sun_event(time_t *t, double lat, double lon, int dir);
extern int next_moon_event(time_t *t, double lat, double lon, int dir);
void s_sunrise_set(char *dst, time_t tm);
void s_moonrise_set(char *dst, time_t tm);

#ifdef  __cplusplus
}
#endif

#endif
