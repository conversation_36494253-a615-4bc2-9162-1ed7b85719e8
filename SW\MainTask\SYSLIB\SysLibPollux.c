/*...........................................................................*/
/*.                  File Name : SYSLIBPOLLUX.C                             .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysTimer.h"
#include "SysMLC.h"
#include "SysALIVE.h"
#include "SysLibPollux.h"

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

//========================================================================
static int      G_nCpuType    = CPU_TYPE_POLLUX;
static int      G_nDeviceType = DEVICE_TYPE_05_0;
//========================================================================
volatile DWORD  G_dSystemTickCounter = 0;
//========================================================================
static xSYSDATE G_xUTCDate = {2008, 1, 1};
static xSYSTIME G_xUTCTime = { 0, 0, 0};
static xSYSDATE G_xLOCDate = {2008, 1, 1};
static xSYSTIME G_xLOCTime = { 0, 0, 0};
//========================================================================

//========================================================================
static MEMBLOCK G_AllMemBlock;
//========================================================================

//========================================================================
//#define  __USE_FIXED_MEM__

#define  __FIXED_MEM_SIZE_0256__          256 
#define  __FIXED_MEM_SIZE_0512__          512 
#define  __FIXED_MEM_SIZE_1024__         1024 

#define  __FIXED_MEM_COUNT_0256__        4096 
#define  __FIXED_MEM_COUNT_0512__        4096 
#define  __FIXED_MEM_COUNT_1024__        4096 

#if defined(__USE_FIXED_MEM__)
static FIXEDMEM  G_xFixed0256Memory = {0,};
static FIXEDMEM  G_xFixed0512Memory = {0,};
static FIXEDMEM  G_xFixed1024Memory = {0,};
#endif
//========================================================================

//========================================================================
void  SysInitFreeMemory(void)
{
#if defined(__USE_FIXED_MEM__)
	static UCHAR  vUsedState0256[__FIXED_MEM_COUNT_0256__];
	static UCHAR  vUsedState0512[__FIXED_MEM_COUNT_0512__];
	static UCHAR  vUsedState1024[__FIXED_MEM_COUNT_1024__];

	static UCHAR  vFreeMemory0256[__FIXED_MEM_COUNT_0256__ * __FIXED_MEM_SIZE_0256__ + 64];
	static UCHAR  vFreeMemory0512[__FIXED_MEM_COUNT_0512__ * __FIXED_MEM_SIZE_0512__ + 64];
	static UCHAR  vFreeMemory1024[__FIXED_MEM_COUNT_1024__ * __FIXED_MEM_SIZE_1024__ + 64];
#endif

      DWORD dBuffer;
      DWORD dSize;
      MEMLINK *pBuffer;

#if defined(__USE_FIXED_MEM__)
	  G_xFixed0256Memory.nBlockSize  = __FIXED_MEM_SIZE_0256__;
	  G_xFixed0256Memory.nFullCount  = __FIXED_MEM_COUNT_0256__;
	  G_xFixed0256Memory.nFreeCount  = __FIXED_MEM_COUNT_0256__;
	  G_xFixed0256Memory.nLastUsedNo = 0;
	  G_xFixed0256Memory.nLastFreeNo =-1;
	  G_xFixed0256Memory.pUsedState  = &vUsedState0256[0];
	  G_xFixed0256Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemory0256[0] + 0x0f) & 0xfffffff0);

	  G_xFixed0512Memory.nBlockSize  = __FIXED_MEM_SIZE_0512__;
	  G_xFixed0512Memory.nFullCount  = __FIXED_MEM_COUNT_0512__;
	  G_xFixed0512Memory.nFreeCount  = __FIXED_MEM_COUNT_0512__;
	  G_xFixed0512Memory.nLastUsedNo = 0;
	  G_xFixed0512Memory.nLastFreeNo =-1;
	  G_xFixed0512Memory.pUsedState  = &vUsedState0512[0];
	  G_xFixed0512Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemory0512[0] + 0x0f) & 0xfffffff0);

	  G_xFixed1024Memory.nBlockSize  = __FIXED_MEM_SIZE_1024__;
	  G_xFixed1024Memory.nFullCount  = __FIXED_MEM_COUNT_1024__;
	  G_xFixed1024Memory.nFreeCount  = __FIXED_MEM_COUNT_1024__;
	  G_xFixed1024Memory.nLastUsedNo = 0;
	  G_xFixed1024Memory.nLastFreeNo =-1;
	  G_xFixed1024Memory.pUsedState  = &vUsedState1024[0];
	  G_xFixed1024Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemory1024[0] + 0x0f) & 0xfffffff0);

	  memset(G_xFixed0256Memory.pUsedState,0x00,__FIXED_MEM_COUNT_0256__ * sizeof(G_xFixed0256Memory.pUsedState[0]));
	  memset(G_xFixed0512Memory.pUsedState,0x00,__FIXED_MEM_COUNT_0512__ * sizeof(G_xFixed0512Memory.pUsedState[0]));
	  memset(G_xFixed1024Memory.pUsedState,0x00,__FIXED_MEM_COUNT_1024__ * sizeof(G_xFixed1024Memory.pUsedState[0]));
#endif

	  dBuffer = SysGetStartOfFreeMemory();
      dSize = RAM_TOTAL_SIZE - 4 * 1024;
      dSize = dSize - (dBuffer - RAM_START_ADDRESS);

      G_AllMemBlock.pStart = (void *)dBuffer;
      G_AllMemBlock.dSize  = dSize;

      if ((dBuffer % MEMORY_ALIGN) != 0)
         {
          dSize -= (MEMORY_ALIGN - dBuffer % MEMORY_ALIGN);
          dBuffer = dBuffer + (MEMORY_ALIGN - dBuffer % MEMORY_ALIGN);
         }

      if ((dSize % MEMORY_ALIGN) != 0)
          dSize -= (dSize % MEMORY_ALIGN);

      pBuffer = (MEMLINK *)dBuffer;

      pBuffer->pPrev = NULL;
      pBuffer->pNext = NULL;
      pBuffer->dSize = dSize - MEMLINK_SIZE;
      pBuffer->wFixed= 1;
      pBuffer->wUsed = 0;

      G_AllMemBlock.pMemLink = pBuffer;
}
void  SysRunCTORS(void)
{
      extern DWORD _CTORS_START;
      extern DWORD _CTORS_END;
      DWORD  *pStartAddr;
      int    nSize;
      void (*pRunPtr)(void);

      nSize = (_CTORS_END - _CTORS_START) / 4;

      pStartAddr = (DWORD *)_CTORS_START;

      while (nSize > 0)
            {
             pRunPtr = (void (*)(void))*pStartAddr;
             pRunPtr();

             ++pStartAddr;
             --nSize;
            }
}
DWORD SysGetAllFreeMemorySize(void)
{
      MEMLINK *pLink;
      DWORD  dTotalMem;

      dTotalMem = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if (!pLink->wUsed)
                 dTotalMem += pLink->dSize;

             pLink = pLink->pNext;
            }

      return(dTotalMem);
}
DWORD SysGetMaxFreeMemorySize(void)
{
      MEMLINK *pLink;
      DWORD  dMaxMem;

      dMaxMem = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if (!pLink->wUsed)
                 if (dMaxMem < pLink->dSize)
                     dMaxMem = pLink->dSize;

             pLink = pLink->pNext;
            }

      return(dMaxMem);
}
int   SysMakeMemoryTree(DWORD *pMemTree)
{
      MEMLINK *pLink;
      int   nNodeSize;

      nNodeSize = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             *pMemTree++ = (DWORD)pLink->pNext;
             *pMemTree++ = (DWORD)pLink->pPrev;
             *pMemTree++ = (DWORD)pLink->dSize;
             *pMemTree++ = (DWORD)pLink->wUsed;

             pLink = pLink->pNext;

             ++nNodeSize;
            }

      return(nNodeSize);
}
int   SysCheckMemoryTree(DWORD *pErrorMem,MEMLINK *pMemLink)
{
      MEMLINK *pLink;
      DWORD dStartMem;
      DWORD dLastMem;
      DWORD dPrev,dNext,dLink;
      int   nError;

      dStartMem = (DWORD)G_AllMemBlock.pStart;
      dLastMem  = (DWORD)G_AllMemBlock.pStart + G_AllMemBlock.dSize;

      nError = 0;
      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             dPrev = (DWORD)(pLink->pPrev);
             dNext = (DWORD)(pLink->pNext);
             dLink = (DWORD)(pLink);

             if ((dNext != 0 && (dNext < dStartMem || dNext >= dLastMem)) ||
                 (dPrev != 0 && (dPrev < dStartMem || dPrev >= dLastMem)) ||
                 (dLink != 0 && (dLink < dStartMem || dLink >= dLastMem)))
                {
                 *pErrorMem = (DWORD)pLink;
                 *pMemLink  = *pLink;
                 nError = 1;
                 break;
                }

             if ((dPrev & 0x0000000f) ||
                 (dNext & 0x0000000f) ||
                 (dLink & 0x0000000f))
                {
                 *pErrorMem = (DWORD)pLink;
                 *pMemLink  = *pLink;
                 nError = 2;
                 break;
                }

             pLink = pLink->pNext;
            }

      return(nError);
}
int   SysGetNumberOfFragment(void)
{
      MEMLINK *pLink;
      int   nFragCnt;

      nFragCnt = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if (pLink->wUsed == 0)
                 ++nFragCnt;

             pLink = pLink->pNext;
            }

      return(nFragCnt);
}
DWORD SysGetLastFreeAddress(DWORD *pBlockSize)
{
      MEMLINK *pLink;
      DWORD  dLastAddr = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             dLastAddr   = (DWORD)pLink;
             *pBlockSize = pLink->dSize;

             pLink = pLink->pNext;
            }

      return(dLastAddr);
}
int   SysRemoveMemFragment(void)
{
      return(0);
}
//========================================================================

//========================================================================
#if defined(__USE_FIXED_MEM__)
void  *MallocFixedMemory(FIXEDMEM *pFixedMemory,int nSize)
{
	int  nTempX;

	if (pFixedMemory->nBlockSize <= nSize)
		return(NULL);

	if (pFixedMemory->nFreeCount > 0)
	{
		if (pFixedMemory->nLastFreeNo != -1)
		{
			nTempX = pFixedMemory->nLastFreeNo;

			if (pFixedMemory->pUsedState[nTempX] == 0)
			{
				pFixedMemory->pUsedState[nTempX] = 1;

				--pFixedMemory->nFreeCount;

				return(pFixedMemory->pFreeMemory + nTempX * pFixedMemory->nBlockSize);
			}
		}

		if (pFixedMemory->nLastUsedNo > 0)
		{
			nTempX = pFixedMemory->nLastUsedNo - 1;

			if (pFixedMemory->pUsedState[nTempX] == 0)
			{
				pFixedMemory->pUsedState[nTempX] = 1;

				--pFixedMemory->nLastUsedNo;
				--pFixedMemory->nFreeCount;

				return(pFixedMemory->pFreeMemory + nTempX * pFixedMemory->nBlockSize);
			}
		}


		nTempX = pFixedMemory->nLastUsedNo;

		while (1)
		{
			if (pFixedMemory->pUsedState[nTempX] == 0)
			{
				pFixedMemory->pUsedState[nTempX] = 1;

				pFixedMemory->nLastUsedNo = nTempX;
				--pFixedMemory->nFreeCount;

				return(pFixedMemory->pFreeMemory + nTempX * pFixedMemory->nBlockSize);
			}

			++nTempX;
			if (nTempX >= pFixedMemory->nFullCount)
				nTempX = 0;
		}
	}

	return(NULL);
}
int   FreeFixedMemory(FIXEDMEM *pFixedMemory,void *pMemAddr)
{
	DWORD dAddrX;
	int   nTempX;

	dAddrX = (DWORD)pFixedMemory->pFreeMemory + pFixedMemory->nBlockSize * pFixedMemory->nFullCount;

	if ((DWORD)pMemAddr < (DWORD)pFixedMemory->pFreeMemory || (DWORD)pMemAddr >= dAddrX)
		return(-1);

	dAddrX = (DWORD)pMemAddr - (DWORD)pFixedMemory->pFreeMemory;
	nTempX = dAddrX / pFixedMemory->nBlockSize;

	if (nTempX >= 0 && nTempX < pFixedMemory->nFullCount)
	{
		if (pFixedMemory->pUsedState[nTempX] != 0)
		{
			pFixedMemory->pUsedState[nTempX] = 0;

			++pFixedMemory->nFreeCount;

			pFixedMemory->nLastFreeNo = nTempX;
		}

		return(nTempX);
	}

	return(-1);
}
#endif ///:~ __USE_FIXED_MEM__

//========================================================================
void  *__wrap_malloc(DWORD dSize)
{
      MEMLINK *pLink;
      MEMLINK *pTemp;
#if  __USE_RTOS_MODE__
      DWORD dStatusReg;
#endif

#if  __USE_RTOS_MODE__
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif

#if defined(__USE_FIXED_MEM__)
	  void  *pMemX;

	  pMemX = MallocFixedMemory(&G_xFixed0256Memory,dSize);
	  if (pMemX != NULL)
		  return(pMemX);

	  pMemX = MallocFixedMemory(&G_xFixed0512Memory,dSize);
	  if (pMemX != NULL)
		  return(pMemX);

	  pMemX = MallocFixedMemory(&G_xFixed1024Memory,dSize);
	  if (pMemX != NULL)
		  return(pMemX);
#endif

	  dSize = (dSize + MEMORY_ALIGN - 1) / MEMORY_ALIGN * MEMORY_ALIGN;
      if (dSize < MEMORY_ALIGN)
          dSize = MEMORY_ALIGN;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if ((pLink->dSize >= dSize) && !pLink->wUsed)
                {
                 if ((pLink->dSize - dSize) > MEMLINK_SIZE)
                    {
                     pTemp = (MEMLINK *)((char *)pLink + MEMLINK_SIZE + dSize);
                     pTemp->pNext = pLink->pNext;
                     pTemp->pPrev = pLink;
                     pLink->pNext = pTemp;
                     pTemp->wFixed= 0;
                     pTemp->dSize = pLink->dSize - dSize - MEMLINK_SIZE;
                     pTemp->wUsed = 0;
                     pLink->dSize = dSize;
                    }

                 pLink->wUsed = 1;
                 break;
                }

             pLink = pLink->pNext;
            }

#if  __USE_RTOS_MODE__
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif

      if (pLink == NULL)
          return(NULL);

      return((char *)pLink + MEMLINK_SIZE);
}
void  __wrap_free(void *pMemAddr)
{
      MEMLINK *pLink;
      MEMLINK *pTemp;
      MEMLINK *pPrev;
#if  __USE_RTOS_MODE__
      DWORD dStatusReg;
#endif

#if  __USE_RTOS_MODE__
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif

#if defined(__USE_FIXED_MEM__)
	  if (FreeFixedMemory(&G_xFixed0256Memory,pMemAddr) >= 0)  return;
	  if (FreeFixedMemory(&G_xFixed0512Memory,pMemAddr) >= 0)  return;
	  if (FreeFixedMemory(&G_xFixed1024Memory,pMemAddr) >= 0)  return;
#endif

	  pMemAddr = (char *)pMemAddr - MEMLINK_SIZE;
      pLink = G_AllMemBlock.pMemLink;
      pPrev = NULL;
      while (pLink != NULL)
            {
             if (pLink == pMemAddr)
                {
                 pLink->wUsed = 0;
                 pTemp = pLink->pNext;
                 if ((pTemp != NULL) && !pTemp->wUsed && !pTemp->wFixed)
                    {
                     pLink->dSize += pTemp->dSize + MEMLINK_SIZE;
                     pLink->pNext = pTemp->pNext;
                    }
                 if (!pLink->wFixed && (pPrev != NULL) && !pPrev->wUsed)
                    {
                     pPrev->dSize += pLink->dSize + MEMLINK_SIZE;
                     pPrev->pNext = pLink->pNext;
                    }
                 break;
                }
             pPrev = pLink;
             pLink = pLink->pNext;
            }
#if  __USE_RTOS_MODE__
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif
}
void  *__wrap_calloc(DWORD nItems,DWORD nSize)
{
      void *pMem;

      pMem = malloc(nItems * nSize);
      memset(pMem,0x00,nItems * nSize);
      return(pMem);
}
void  __wrap_abort(void)
{
      while (1);
}
int   __wrap_open(char *Path,int Flags)
{
      return(-1);
}
int   __wrap_close(int file)
{
      return(-1);
}
int   __wrap_fstat(int file,struct stat *st)
{
      st->st_mode = S_IFCHR;
      return(0);
}
int   __wrap_isatty(int file)
{
      return(1);
}
int   __wrap_lseek(int file,int ptr,int dir)
{
      return(0);
}
int   __wrap_read(int file,char *ptr,int len)
{
      return(0);
}
caddr_t __wrap_sbrk(int incr)
{
      static char *heap_end = NULL;
      char *prev_heap_end;
#if  __USE_RTOS_MODE__
      DWORD dStatusReg;
#endif

#if  __USE_RTOS_MODE__
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif
      incr = incr + 15;
      incr = incr & 0xfffffff0;

      if (heap_end == NULL)
          heap_end = (char *)SysGetStartOfHeapMemory();

      prev_heap_end = heap_end;

      if ((heap_end + incr) >= (char *)SysGetLastOfHeapMemory())
          abort();

      heap_end += incr;

#if  __USE_RTOS_MODE__
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif

      return((caddr_t)prev_heap_end);
}
int   __wrap_write(int file,char *ptr,int len)
{
      return(len);
}
int   __wrap_fputc(int character,void *stream)
{
      return(0);
}
int   __wrap_fputs(const char *str,void *stream)
{
      return(0);
}
int   __wrap_puts(const char *str)
{
      __wrap_printf(str);
      return(0);
}
int   __wrap_printf(const char *format,...)
{
      return(0);
}
//========================================================================
int   __wrap__open_r(void *REENT,const char *file,int flags,int mode)
{
      return(0);
}
int   __wrap__close_r(void *REENT,int FD)
{
      return(0);
}
int   __wrap__fstat_r(void *REENT,int FD,struct stat *PSTAT)
{
      PSTAT->st_mode = S_IFCHR;
      return(0);
}
off_t __wrap__lseek_r(void *REENT,int FD,off_t POS,int WHENCE)
{
      return(0);
}
long  __wrap__read_r(void *REENT,int FD,void *BUF,size_t CNT)
{
      return(0);
}
char *__wrap__sbrk_r(void *REENT, size_t INCR)
{
      return(__wrap_sbrk(INCR));
}
long  __wrap__write_r(void *REENT,int FD, const void *BUF, size_t CNT)
{
      return(CNT);
}
//========================================================================
void  SysCheckCpuType(void)
{
#ifdef  __POLLUX__
      SysSetCpuType(CPU_TYPE_POLLUX);
#else                           // SPICA
#endif
}
void  SysSetCpuType(int nCpuType)
{
//    G_nCpuType = CPU_TYPE_POLLUX;
      G_nCpuType = nCpuType;
}
int   SysGetCpuType(void)
{
      return(G_nCpuType);
}
void  SysCheckDeviceType(void)
{
//    SysSetDeviceType(DEVICE_TYPE_05_6);

      xSYS_GPIO *pSysGPIO = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

      if (!(pSysGPIO->dPAD & 0x00000003))
          SysSetDeviceType(DEVICE_TYPE_05_6);
      else
          SysSetDeviceType(DEVICE_TYPE_05_0);
}
void  SysSetDeviceType(int nDeviceType)
{
      G_nDeviceType = nDeviceType;
}
int   SysGetDeviceType(void)
{
      return(G_nDeviceType);
}
DWORD SysGetScreenAddress(void)
{
      return(MENU_LAYER_BASE_ADDR);
}
DWORD SysGetScreenWidth(void)
{
      return(SysGetScreenWidthByDevice(G_nDeviceType));
}
DWORD SysGetScreenHeight(void)
{
      return(SysGetScreenHeightByDevice(G_nDeviceType));
}
DWORD SysGetScreenWidthByDevice(int nDeviceType)
{
      DWORD dTempX = DVC_05_6_SCRN_WIDTH;

      if (nDeviceType == DEVICE_TYPE_03_5) dTempX = DVC_03_5_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_04_3) dTempX = DVC_04_3_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_05_0) dTempX = DVC_05_0_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_05_6) dTempX = DVC_05_6_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_06_5) dTempX = DVC_06_5_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_07_0) dTempX = DVC_07_0_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_08_4) dTempX = DVC_08_4_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_10_4) dTempX = DVC_10_4_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_12_1) dTempX = DVC_12_1_SCRN_WIDTH;
      if (nDeviceType == DEVICE_TYPE_15_1) dTempX = DVC_15_1_SCRN_WIDTH;
      return(dTempX);
}
DWORD SysGetScreenHeightByDevice(int nDeviceType)
{
      DWORD dTempX = DVC_05_6_SCRN_HEIGHT;

      if (nDeviceType == DEVICE_TYPE_03_5) dTempX = DVC_03_5_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_04_3) dTempX = DVC_04_3_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_05_0) dTempX = DVC_05_0_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_05_6) dTempX = DVC_05_6_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_06_5) dTempX = DVC_06_5_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_07_0) dTempX = DVC_07_0_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_08_4) dTempX = DVC_08_4_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_10_4) dTempX = DVC_10_4_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_12_1) dTempX = DVC_12_1_SCRN_HEIGHT;
      if (nDeviceType == DEVICE_TYPE_15_1) dTempX = DVC_15_1_SCRN_HEIGHT;
      return(dTempX);
}
//========================================================================
void  SysDelayLoop(volatile DWORD dDelayCnt)
{
      while (dDelayCnt) dDelayCnt--;
}
void  SysDelayMicroSec(DWORD dDelayMicro)
{
      SysTimerDelayMicroSec(dDelayMicro);
}
void  SysDelayMiliSec(DWORD dDelayMili)
{
      SysTimerDelayMiliSec(dDelayMili);
}
DWORD SysGetSystemTimer(void)
{
      return(G_dSystemTickCounter);
}
DWORD SysIncSystemTimer(void)
{
      ++G_dSystemTickCounter;
      return(G_dSystemTickCounter);
}
DWORD SysCalcTickToMili(DWORD dTick)
{
      return(CALC_TICK_TO_MILI(dTick));
}
DWORD SysCalcMiliToTick(DWORD dMili)
{
      return(CALC_MILI_TO_TICK(dMili));
}
DWORD SysGetDiffTimeTick(DWORD dTime)
{
      DWORD dTemp;

      dTemp = SysGetSystemTimer();
      if (dTemp >= dTime)
          dTemp -= dTime;
      else
          dTemp += (0xffffffff - dTime);
      return(dTemp);
}
DWORD SysGetDiffTimeMili(DWORD dTime)
{
      return(CALC_TICK_TO_MILI(SysGetDiffTimeTick(dTime)));
}
//========================================================================
void  SysSetUTCDate(int nYear,int nMonth,int nDay)
{
      G_xUTCDate.nYear  = nYear;
      G_xUTCDate.nMonth = nMonth;
      G_xUTCDate.nDay   = nDay;
}
void  SysGetUTCDate(int *pYear,int *pMonth,int *pDay)
{
      *pYear  = G_xUTCDate.nYear;
      *pMonth = G_xUTCDate.nMonth;
      *pDay   = G_xUTCDate.nDay;
}
void  SysSetUTCTime(int nHour,int nMinute,int nSecond)
{
      G_xUTCTime.nHour  = nHour;
      G_xUTCTime.nMinute= nMinute;
      G_xUTCTime.nSecond= nSecond;
}
void  SysGetUTCTime(int *pHour,int *pMinute,int *pSecond)
{
      *pHour  = G_xUTCTime.nHour;
      *pMinute= G_xUTCTime.nMinute;
      *pSecond= G_xUTCTime.nSecond;
}
void  SysSetLOCDate(int nYear,int nMonth,int nDay)
{
      G_xLOCDate.nYear  = nYear;
      G_xLOCDate.nMonth = nMonth;
      G_xLOCDate.nDay   = nDay;
}
void  SysGetLOCDate(int *pYear,int *pMonth,int *pDay)
{
      *pYear  = G_xLOCDate.nYear;
      *pMonth = G_xLOCDate.nMonth;
      *pDay   = G_xLOCDate.nDay;
}
void  SysSetLOCTime(int nHour,int nMinute,int nSecond)
{
      G_xLOCTime.nHour  = nHour;
      G_xLOCTime.nMinute= nMinute;
      G_xLOCTime.nSecond= nSecond;
}
void  SysGetLOCTime(int *pHour,int *pMinute,int *pSecond)
{
      *pHour  = G_xLOCTime.nHour;
      *pMinute= G_xLOCTime.nMinute;
      *pSecond= G_xLOCTime.nSecond;
}
//========================================================================
//========================================================================
void  SetTransponderReset(void)
{
#if defined(__POLLUX__)
      SysSetALIVExBitData(TP_OFF_BIT_NO,1);
      SysDelayMiliSec(1000);
      SysSetALIVExBitData(TP_OFF_BIT_NO,0);
#endif ///:~ __CPU_AU1100__
}
void  Au1100DelayMiliSec(DWORD DelayMili)
{
      SysDelayMiliSec(DelayMili);
}
int   GetMKDLcdType(void)
{
      int  nLcdType;

      nLcdType = 0;
      nLcdType = 1;

      return(nLcdType);
}
void  SetLcdControlBitOnOff(int nOnOff)
{
      if (nOnOff)
         {
          SysSetALIVExBitData(LCD_ON_BIT_NO,1);
         }
      else
         {
          SysSetALIVExBitData(LCD_ON_BIT_NO,0);
         }
}
//==============================================================================
static int  G_nPowerStaus = 1;                          // o=off,1=on
static int  G_nCanCheckPowerStatus = 0;
//-----------------------------------------------------------------------------
#define  PWR_KEY_IN_PORT_BIT                (1 <<  9)
#define  PWR_LCD_OUT_PORT_BIT               (1 << 10)
#define  TX_LED_PORT_BIT                    (1 <<  0)
#define  RX_LED_PORT_BIT                    (1 <<  1)
#define  PWR_LED_PORT_BIT                   (1 <<  4)
#define  BUZZER_PORT_BIT                    (1 << 29)
//-----------------------------------------------------------------------------
#define  AU1100_LCD_ADDR                  0xB5000000
#define  LCD_PWMDIV                           0x0024
#define  LCD_PWMHI                            0x0028
//==============================================================================
void  SetCheckPowerStatus(int nMode)
{
      G_nCanCheckPowerStatus = nMode;
}
void  CheckPowerStatus(void)
{
	    static DWORD dSysTimer = 0;
	    static DWORD dPwrTimer = 0;
      static int   nPwrChange= 0;
      xSYS_GPIO *pSysGPIO = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

      dSysTimer++;
// *(DWORD *)0x03b00000 = 0x11111111;
// *(DWORD *)0x03b00004 = dSysTimer;
// *(DWORD *)0x03b00008 = pSysGPIO->dPAD & (1 << PWR_GPIO_BIT_NO);
// *(DWORD *)0x03b0000c = 0x11111111;

      if (G_nCanCheckPowerStatus && !(pSysGPIO->dPAD & (1 << PWR_GPIO_BIT_NO)))
         {
          if (dPwrTimer == 0)
             {
              dPwrTimer = dSysTimer;
             }
          else
             {
              if (nPwrChange == 0)
                 {
                  if (dPwrTimer > dSysTimer)
                      dPwrTimer = dSysTimer;
                  if ((dSysTimer - dPwrTimer) > 100)     // 1 sec
                     {
                      G_nPowerStaus = 1 - G_nPowerStaus;
                      SetPowerStatus(G_nPowerStaus);
                      nPwrChange = 1;
                     }
                 }
             }
         }
      else
         {
          dPwrTimer = 0;
          nPwrChange= 0;
         }
}
void  SetPowerStatus(int nStatus)
{
      static HWORD wLcdPeriod = 0x0000;
      static HWORD wLcdDuty   = 0x0000;

      static HWORD wKeyPeriod = 0x0000;
      static HWORD wKeyDuty   = 0x0000;

      static HWORD wLedPeriod = 0x0000;
      static HWORD wLedDuty   = 0x0000;

      G_nPowerStaus = nStatus;
      if (G_nPowerStaus)
         {
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wLcdDuty);

          SysSetPWMDutyCycle(PWM_KEY_CHANNEL,wKeyDuty);

          SysSetPWMDutyCycle(PWM_BUZ_CHANNEL,wLedDuty);
         }
      else
         {
          wLcdPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
          wLcdDuty   = SysGetPWMDutyCycle(PWM_LCD_CHANNEL);
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);

          wKeyPeriod = SysGetPWMPeriod(PWM_KEY_CHANNEL);
          wKeyDuty   = SysGetPWMDutyCycle(PWM_KEY_CHANNEL);
          SysSetPWMDutyCycle(PWM_KEY_CHANNEL,wKeyPeriod + 1);

          wLedPeriod = SysGetPWMPeriod(PWM_BUZ_CHANNEL);
          wLedDuty   = SysGetPWMDutyCycle(PWM_BUZ_CHANNEL);
          SysSetPWMDutyCycle(PWM_BUZ_CHANNEL,wLedPeriod + 1);
         }
}
int   GetPowerStatus(void)
{
      return(G_nPowerStaus);
}
int   IsLcdLedType(void)
{
//    BYTE *pMemX;

//    pMemX = (BYTE *)0xbfc00000;
//    if (pMemX[0x50] == 'L' && pMemX[0x51] == 'E' && pMemX[0x52] == 'D')
          return(1);
//    return(0);
}
int   SysIs640x480Mode(void)
{
      if (SysGetDeviceType() == DEVICE_TYPE_05_6)
          return(1);

      return(0);
}
//========================================================================

