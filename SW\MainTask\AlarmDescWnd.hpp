#ifndef __ALARM_DESCRIPTION_WND_HPP__
#define __ALARM_DESCRIPTION_WND_HPP__

#include "Wnd.hpp"

class CAlarmDescWnd : public CWnd {
	private:
		int m_nCurAlarmID;
		int m_nCurAlarmInstance; // bam alert instance

	public:
		CAlarmDescWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);

		void SetCurAlarmID(int nID);
		int GetCurAlarmID();
		void SetCurAlarmInstance(int nInstance);
		int GetCurAlarmInstance();		
		const HWORD *GetDescText(int nAlarmID);
		const HWORD *GetDescTextBam(int nAlarmID);		

		void DrawAutoSizingTextNormal(int nStartX, int nStartY, int nLineW, int nLineH, const HWORD *pUnicodeStr, FONT *pFont, COLORREF clrTxt);
		void DrawAutoSizingTextExtra(int nStartX, int nStartY, int nLineW, int nLineH, const HWORD *pUnicodeStr, FONT *pFont, COLORREF clrTxt);
		void DrawFuncBtn();
		void DrawOutLine();
		void DrawAlarmID();
		void DrawAlarmDesc();
		void DrawWnd(BOOL bRedraw = TRUE);

	private:
		void InitVariables();
		void InitAlarmDescWnd();
};

#endif	// End of __ALARM_DESCRIPTION_WND_HPP__

