/*...........................................................................*/
/*.                  File Name : Tahoma14bBengali.cpp                       .*/
/*.                                                                         .*/
/*.                       Date : 2014.04.10                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma14bArabic_Font;

ROMDATA PEGUSHORT Tahoma14bBengali1_offset_table[129] = {
0x0000,0x0017,0x002e,0x0044,0x0059,0x0070,0x007d,0x0092,0x00a7,0x00bc,0x00cb,0x00da,0x00eb,0x0102,0x0119,0x0130,
0x0142,0x0159,0x0168,0x0178,0x018f,0x019f,0x01af,0x01c6,0x01dd,0x01f4,0x020b,0x021f,0x022f,0x023f,0x024f,0x025f,
0x0271,0x0280,0x0290,0x02a0,0x02b7,0x02ce,0x02e2,0x02f3,0x0302,0x0312,0x0322,0x0332,0x0349,0x0359,0x036a,0x037a,
0x0386,0x0396,0x03aa,0x03c1,0x03ce,0x03dd,0x03ec,0x03fc,0x040c,0x041c,0x042c,0x043c,0x044b,0x0462,0x0472,0x0489,
0x04a0,0x04af,0x04bf,0x04ce,0x04de,0x04ee,0x04ff,0x050f,0x0526,0x053d,0x0554,0x0563,0x057a,0x0591,0x05a8,0x05bf,
0x05d5,0x05eb,0x0601,0x060e,0x061c,0x0629,0x0640,0x064d,0x0664,0x067a,0x0691,0x06a8,0x06bf,0x06d6,0x06ed,0x0704,
0x071b,0x0732,0x0749,0x0760,0x0777,0x078e,0x07a5,0x07bc,0x07d3,0x07ea,0x0801,0x0818,0x082f,0x0846,0x085d,0x0874,
0x088b,0x08a2,0x08b9,0x08d0,0x08e7,0x08fe,0x0915,0x092c,0x0943,0x095a,0x0971,0x0988,0x099f,0x09b6,0x09cd,0x09e4,
0x09fb};



ROMDATA PEGUBYTE Tahoma14bBengali1_data_table[8960 + 320] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xf8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 
0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0xfe, 0x00, 0x00, 0x07, 0xff, 
0xfc, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x80, 0x0b, 0x03, 0xff, 0xfe, 0x07, 
0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x3f, 
0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 
0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 
0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 
0xf8, 0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 0xff, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x82, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x09, 0x82, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0xa4, 0x00, 
0x00, 0x02, 0x0f, 0x80, 0x1f, 0x00, 0x0f, 0x80, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x07, 0xc0, 
0x00, 0x1c, 0x00, 0x0c, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x81, 0xf0, 
0x00, 0x10, 0x01, 0xf0, 0x00, 0x10, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x10, 0x00, 
0x00, 0x1f, 0x00, 0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x7c, 0x00, 
0x00, 0x00, 0x7c, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x01, 0x82, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x09, 0x82, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb9, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 0xe4, 0x00, 
0x00, 0x06, 0x30, 0x40, 0x20, 0xc0, 0x30, 0x40, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x00, 0x00, 0x00, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x30, 0xc0, 0x00, 0x00, 0x18, 0x70, 
0x00, 0x38, 0x00, 0x18, 0x00, 0x03, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x06, 0x18, 
0x00, 0x38, 0x06, 0x18, 0x00, 0x38, 0x00, 0x00, 0x00, 0x0c, 0x30, 0x00, 0x00, 0x00, 0x38, 0x00, 
0x00, 0x61, 0x80, 0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x86, 0x00, 
0x00, 0x01, 0x86, 0x01, 0x86, 0x00, 0x00, 0x00, 0x03, 0x02, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x0f, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x1f, 0xc0, 0xfe, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb0, 0x03, 0x78, 0x00, 
0x00, 0x0c, 0x20, 0x20, 0x3f, 0xe0, 0x20, 0x20, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x00, 0x00, 0x00, 0x40, 0x60, 0x00, 0x00, 0x00, 0x00, 0x40, 0x60, 0x00, 0x00, 0x20, 0x38, 
0x00, 0x60, 0x00, 0x30, 0x00, 0x04, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x08, 0x0c, 
0x00, 0x2c, 0x08, 0x0c, 0x00, 0x2c, 0x00, 0x00, 0x1c, 0x10, 0x18, 0x00, 0x00, 0x00, 0x2c, 0x00, 
0x00, 0x40, 0xc0, 0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x01, 0x01, 0x00, 
0x00, 0x02, 0x03, 0x01, 0x03, 0x00, 0x00, 0x00, 0x0e, 0x02, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x40, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x08, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x3f, 0xe1, 0xff, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x10, 0x02, 0x60, 0x00, 
0x00, 0x08, 0x40, 0x10, 0x60, 0xf0, 0x40, 0x10, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x00, 0x00, 0x00, 0x80, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x18, 
0x00, 0xc0, 0x00, 0x20, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x06, 
0x00, 0x24, 0x00, 0x06, 0x00, 0x24, 0x00, 0x00, 0xf8, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x24, 0x00, 
0x00, 0x80, 0x60, 0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x09, 0x02, 0x01, 0x80, 
0x00, 0x00, 0x01, 0x82, 0x01, 0x80, 0x00, 0x00, 0x18, 0x02, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x08, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x60, 0x73, 0x03, 0x80, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x1e, 0x40, 
0xf2, 0x00, 0x07, 0x90, 0x00, 0x3c, 0x80, 0x00, 0xfc, 0x01, 0xf0, 0x00, 0x78, 0x00, 0x3c, 0x00, 
0x0f, 0x1c, 0x00, 0x1e, 0x38, 0x00, 0x1e, 0x3c, 0x07, 0x8f, 0x00, 0x1d, 0x18, 0x3a, 0x20, 0x0f, 
0xc0, 0xe8, 0x03, 0x10, 0x07, 0x70, 0x03, 0x10, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x07, 0x0f, 0x80, 0x00, 0x10, 0x39, 0xe0, 0x38, 0xe0, 0x78, 0x30, 0x38, 0x78, 0x1e, 0x0c, 
0x38, 0xf0, 0x38, 0x70, 0x38, 0x7e, 0x02, 0x70, 0x3f, 0xc0, 0x70, 0x7c, 0x07, 0x8e, 0x00, 0x06, 
0x00, 0x26, 0x0f, 0x06, 0x0f, 0x1e, 0x00, 0xf7, 0x80, 0x0e, 0x0c, 0x1c, 0x78, 0x07, 0x9e, 0x03, 
0x80, 0x00, 0x60, 0xe1, 0xf0, 0x04, 0x00, 0x04, 0x07, 0xc0, 0xe3, 0x80, 0xe9, 0x80, 0x00, 0x87, 
0x0f, 0x01, 0xb9, 0x80, 0x39, 0x80, 0x07, 0x00, 0x70, 0x02, 0x00, 0x02, 0x00, 0xfc, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x18, 0x30, 0x1d, 0xe0, 0x71, 0xc0, 0xf1, 0xc0, 0xc3, 0xc0, 0x1d, 0xe0, 
0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x08, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x12, 0x04, 0x80, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x03, 
0xf0, 0x00, 0x07, 0xe0, 0x00, 0x07, 0xc0, 0x1e, 0x1f, 0x80, 0x00, 0x3f, 0x00, 0x00, 0x7e, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x08, 0x00, 0x08, 0x19, 0x60, 
0xcb, 0x1e, 0x06, 0x58, 0x00, 0x32, 0xc0, 0x01, 0xfe, 0x03, 0x0c, 0x00, 0x64, 0x00, 0x32, 0x17, 
0x0c, 0x92, 0x3c, 0x19, 0x24, 0x78, 0x2d, 0x4e, 0x0b, 0x53, 0x97, 0x1a, 0xf8, 0x35, 0xf0, 0x18, 
0x30, 0xd6, 0x35, 0x88, 0x35, 0xb8, 0x35, 0x89, 0x70, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x0e, 0xb0, 0xe0, 0x3e, 0x18, 0x63, 0xf0, 0x69, 0xf0, 0xac, 0x18, 0x74, 0xfc, 0x1a, 0x0c, 
0x75, 0x10, 0x64, 0xc8, 0x75, 0x83, 0x00, 0x6b, 0xc3, 0x60, 0xe9, 0xb6, 0x0f, 0x5a, 0x03, 0xc3, 
0x03, 0x1e, 0x0d, 0x03, 0x0d, 0x02, 0x01, 0x1c, 0x00, 0x1d, 0x06, 0x3b, 0x8c, 0x08, 0x02, 0x03, 
0x40, 0x60, 0x31, 0x96, 0x1c, 0x04, 0x00, 0x04, 0x0b, 0x61, 0x92, 0x41, 0x97, 0x81, 0xf0, 0xc6, 
0x9f, 0x82, 0xac, 0xc1, 0xac, 0xc0, 0xc4, 0x81, 0x8c, 0x02, 0x00, 0x02, 0x03, 0x06, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x34, 0x18, 0x73, 0xf0, 0x6a, 0x60, 0xc9, 0x21, 0xa7, 0xe0, 0x27, 0x30, 
0x3d, 0xe0, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x04, 0xc0, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x1e, 0x06, 
0x08, 0x00, 0x0c, 0x10, 0x00, 0x1c, 0x30, 0x60, 0x30, 0x40, 0x00, 0x60, 0x80, 0x00, 0xc1, 0x00, 
0x00, 0x00, 0x17, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0xf1, 0x00, 0x01, 0x38, 0x00, 0xcc, 0x60, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x12, 0x08, 0x00, 0x08, 0x00, 0xb0, 
0x05, 0xa6, 0x00, 0x2c, 0x00, 0x01, 0x60, 0x03, 0x87, 0x06, 0x04, 0x00, 0x02, 0x00, 0x01, 0x2f, 
0x80, 0xa2, 0x44, 0x01, 0x44, 0x89, 0x41, 0x41, 0x10, 0x50, 0x6f, 0x82, 0x0c, 0x04, 0x10, 0x30, 
0x08, 0x10, 0x53, 0x88, 0x57, 0x98, 0x53, 0x8a, 0xf8, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x0c, 0xc0, 0x70, 0x7f, 0x18, 0x46, 0x38, 0x05, 0x10, 0xa4, 0x18, 0x84, 0x8e, 0x01, 0x0c, 
0x7d, 0xf0, 0x65, 0x48, 0x66, 0x00, 0x80, 0x8e, 0x00, 0x31, 0x0b, 0x03, 0x08, 0x5a, 0x04, 0x03, 
0x0c, 0x03, 0x00, 0x83, 0x00, 0x83, 0x02, 0x76, 0x00, 0x30, 0x86, 0x33, 0x06, 0x18, 0x03, 0x00, 
0x20, 0x80, 0x31, 0x9c, 0x0e, 0x04, 0x00, 0x04, 0x11, 0x31, 0x94, 0x41, 0x90, 0xc3, 0xf8, 0xc0, 
0x91, 0xc4, 0xbc, 0xc2, 0xbc, 0xc3, 0x08, 0x83, 0x02, 0x02, 0x00, 0x02, 0x06, 0x03, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x34, 0x18, 0x66, 0x38, 0x0a, 0x60, 0x0a, 0x21, 0xac, 0x70, 0x42, 0x10, 
0x7f, 0xf0, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x04, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x07, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x22, 0x0c, 
0x04, 0x00, 0x18, 0x08, 0x00, 0x30, 0x08, 0xc0, 0x60, 0x20, 0x00, 0xc0, 0x40, 0x01, 0x80, 0x80, 
0x00, 0x00, 0x2f, 0x82, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x01, 0x12, 0x00, 0x03, 0x78, 0x00, 0xdc, 0xe0, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x03, 0x80, 0x00, 0x12, 0x08, 0x00, 0x08, 0x1f, 0x90, 
0xfc, 0xa3, 0x07, 0xe5, 0xf0, 0x3f, 0x2f, 0x07, 0x33, 0x8c, 0x06, 0x03, 0xfe, 0x01, 0xff, 0x22, 
0xc7, 0xbe, 0x06, 0x0f, 0x7c, 0x0c, 0x4f, 0x79, 0x13, 0xde, 0x62, 0xbe, 0x0c, 0x7c, 0x18, 0x63, 
0xc5, 0xf0, 0x89, 0x88, 0x99, 0x98, 0x89, 0x8a, 0x2c, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x0d, 0xfc, 0x38, 0xc1, 0x98, 0xc4, 0x38, 0xfd, 0xf0, 0xfc, 0x18, 0x9d, 0x86, 0x3f, 0x0c, 
0x31, 0x38, 0x1d, 0xf0, 0x0f, 0xc7, 0x80, 0x8f, 0xc7, 0xf1, 0x3a, 0xff, 0x11, 0xdc, 0x08, 0x03, 
0x08, 0x03, 0x1f, 0x83, 0x1f, 0x83, 0x03, 0xf3, 0xdc, 0x3f, 0x86, 0x07, 0xe7, 0x10, 0x03, 0x07, 
0xe1, 0x80, 0x30, 0x1f, 0x07, 0x04, 0x00, 0x04, 0x2f, 0xf0, 0x77, 0xc0, 0xf0, 0xc6, 0x0c, 0xc7, 
0xb1, 0xc4, 0xcc, 0xc4, 0xcc, 0xc2, 0x0f, 0x86, 0x03, 0x02, 0x00, 0x02, 0x0c, 0xfb, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x1c, 0x18, 0xc4, 0x18, 0x3b, 0xe0, 0x7b, 0xe0, 0x68, 0x30, 0x7f, 0xf0, 
0xe7, 0x38, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x04, 0x60, 0x00, 0x1f, 0x00, 0x00, 0x78, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x03, 0x18, 
0xf2, 0x00, 0x31, 0xe4, 0x00, 0x63, 0xc5, 0x8e, 0xc7, 0x90, 0x01, 0x8f, 0x20, 0x03, 0x1e, 0x40, 
0x00, 0x00, 0x22, 0xc2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x18, 0x00, 0x03, 0x3c, 0x01, 0xd5, 0xe8, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x04, 0x40, 0x00, 0x0c, 0x08, 0x00, 0x08, 0x30, 0xe1, 
0x87, 0x03, 0x0c, 0x38, 0x30, 0x61, 0xc2, 0x06, 0x49, 0x8c, 0x06, 0x0e, 0x00, 0x07, 0x00, 0x1c, 
0xc8, 0x03, 0x0e, 0x10, 0x06, 0x1c, 0x70, 0x07, 0x1c, 0x01, 0xdc, 0xb0, 0x0c, 0x60, 0x18, 0x65, 
0x75, 0x80, 0x87, 0x08, 0x8f, 0x18, 0x87, 0x09, 0xcc, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x03, 0x82, 0x38, 0x80, 0x98, 0xce, 0x18, 0x60, 0x18, 0xc0, 0x18, 0xa1, 0x06, 0x10, 0x0c, 
0x60, 0xf8, 0x30, 0x38, 0x18, 0x3c, 0x00, 0x98, 0x2c, 0x01, 0x27, 0xc0, 0x12, 0x07, 0x08, 0x03, 
0x10, 0x03, 0x18, 0x03, 0x18, 0x03, 0x06, 0x93, 0x26, 0x2c, 0x06, 0x0c, 0x33, 0x10, 0x03, 0x0c, 
0x01, 0x00, 0x30, 0x21, 0x83, 0x04, 0x00, 0x04, 0x3c, 0x01, 0xc0, 0xe1, 0x80, 0xc4, 0x04, 0xc3, 
0x20, 0xc4, 0x78, 0xc4, 0x78, 0xc4, 0x00, 0xc6, 0x03, 0x02, 0x00, 0x02, 0x08, 0x27, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x70, 0x18, 0xce, 0x18, 0x6f, 0xe0, 0x80, 0x31, 0xdc, 0x30, 0xc0, 0x00, 
0xc2, 0x18, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x07, 0x19, 
0x5a, 0x00, 0x32, 0xb4, 0x00, 0x64, 0xb5, 0x94, 0xca, 0xd0, 0x01, 0x95, 0xa0, 0x03, 0x2b, 0x40, 
0x00, 0x00, 0x1c, 0xc2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0x38, 0x00, 0x01, 0xec, 0x05, 0x77, 0xf8, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x04, 0x40, 0x00, 0x00, 0x08, 0x00, 0x08, 0x60, 0x83, 
0x04, 0x03, 0x18, 0x20, 0x20, 0xc1, 0x03, 0x86, 0x79, 0x8c, 0x06, 0x0c, 0x00, 0x06, 0x00, 0x00, 
0xd8, 0x43, 0x36, 0x30, 0x86, 0x6c, 0x60, 0x03, 0x18, 0x00, 0xc0, 0xb0, 0x0c, 0x60, 0x18, 0x67, 
0x31, 0x80, 0x80, 0x08, 0x80, 0x18, 0x80, 0x08, 0x0c, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x21, 0x18, 0x80, 0x98, 0xcb, 0x18, 0xc0, 0x18, 0x84, 0x18, 0xc3, 0x86, 0x30, 0x0c, 
0x60, 0x18, 0x60, 0x18, 0x10, 0x18, 0x00, 0xa2, 0x18, 0x01, 0x45, 0x80, 0x14, 0x03, 0x08, 0x03, 
0x10, 0x43, 0x18, 0x43, 0x18, 0x43, 0x0d, 0xf3, 0x23, 0x28, 0x06, 0x08, 0x1b, 0x10, 0x03, 0x18, 
0x01, 0x08, 0x30, 0x20, 0xc3, 0x04, 0x00, 0x04, 0x38, 0x01, 0x80, 0x63, 0x00, 0xc4, 0x04, 0xc6, 
0x70, 0xc4, 0x01, 0xc4, 0x00, 0xc4, 0x10, 0xc6, 0x03, 0x02, 0x00, 0x02, 0x18, 0x1e, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x60, 0x18, 0xca, 0x78, 0xd0, 0xb1, 0x84, 0x31, 0x96, 0x31, 0xc0, 0x20, 
0xc2, 0x18, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x60, 0x00, 0x02, 0x00, 0x00, 0x1c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x1b, 0x19, 
0xd8, 0x00, 0x33, 0xb0, 0x00, 0x67, 0xb1, 0x9c, 0xce, 0xc0, 0x01, 0x9d, 0x80, 0x03, 0x3b, 0x00, 
0x00, 0x00, 0x00, 0xc2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0xd9, 0x00, 0x00, 0x0c, 0x0f, 0x66, 0x7c, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x04, 0x40, 0x00, 0x0c, 0x08, 0x00, 0x08, 0x60, 0x83, 
0x04, 0x03, 0x18, 0x20, 0x60, 0xc1, 0x05, 0x03, 0x19, 0x8c, 0x0e, 0x18, 0x00, 0x0c, 0x00, 0x08, 
0xd8, 0x43, 0x66, 0x30, 0x86, 0xcd, 0x60, 0x03, 0x18, 0x00, 0xc8, 0xb0, 0x1c, 0x60, 0x38, 0x60, 
0x31, 0x80, 0x82, 0x08, 0x82, 0x18, 0x82, 0x08, 0x8c, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x04, 0x21, 0x18, 0xb0, 0xb8, 0xc3, 0x18, 0xc7, 0x18, 0x84, 0x38, 0xc2, 0xc6, 0x30, 0x18, 
0x60, 0x18, 0x60, 0x18, 0x31, 0x18, 0x42, 0xa2, 0x18, 0x01, 0x41, 0x80, 0x1c, 0x03, 0x0c, 0x06, 
0x10, 0x43, 0x18, 0x43, 0x18, 0x43, 0x12, 0xe3, 0x23, 0x28, 0x46, 0x18, 0x1b, 0x18, 0x06, 0x18, 
0x01, 0x08, 0x70, 0x40, 0xc3, 0x04, 0x00, 0x04, 0x38, 0x01, 0x80, 0x63, 0x01, 0xc5, 0x89, 0xc6, 
0x50, 0xc6, 0x01, 0x84, 0x38, 0xc4, 0x10, 0xc6, 0x07, 0x02, 0x00, 0x02, 0x18, 0x00, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x60, 0x30, 0xc2, 0x98, 0xcf, 0x31, 0x84, 0x31, 0x86, 0x31, 0xb0, 0xc0, 
0xc0, 0x18, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x60, 0x00, 0x06, 0x00, 0x00, 0x28, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0x33, 0x18, 
0x18, 0x00, 0x30, 0x30, 0x00, 0x60, 0x31, 0x80, 0xc0, 0xc0, 0x01, 0x81, 0x80, 0x03, 0x03, 0x00, 
0x00, 0x00, 0x08, 0xc2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x01, 0x9b, 0x00, 0x00, 0xec, 0x0f, 0x64, 0x5c, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x03, 0x80, 0x00, 0x12, 0x08, 0x00, 0x08, 0x60, 0x93, 
0x04, 0xa3, 0x18, 0x24, 0x40, 0xc1, 0x23, 0x01, 0xf3, 0x06, 0x1c, 0x18, 0x00, 0x0c, 0x00, 0x08, 
0xdc, 0xe7, 0x66, 0x39, 0xce, 0xcd, 0x30, 0x06, 0x0c, 0x01, 0x88, 0xb8, 0x38, 0x70, 0x70, 0x30, 
0x71, 0xc0, 0xc7, 0x10, 0xc6, 0x30, 0xc7, 0x10, 0x8c, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x04, 0x71, 0x18, 0xc8, 0xb0, 0xe3, 0x10, 0xc8, 0xb8, 0xce, 0x30, 0x60, 0xc4, 0x38, 0x38, 
0x30, 0x70, 0x70, 0x38, 0x21, 0x18, 0xe2, 0xc7, 0x18, 0x10, 0xe3, 0x81, 0x0e, 0x07, 0x06, 0x0e, 
0x18, 0xe6, 0x1c, 0xc6, 0x1c, 0xe6, 0x03, 0x8f, 0x23, 0x1c, 0x4c, 0x10, 0x3b, 0x0c, 0x1e, 0x18, 
0x21, 0x9c, 0x60, 0x41, 0xc3, 0x04, 0x00, 0x04, 0x18, 0x11, 0xc0, 0xe3, 0x83, 0x87, 0x51, 0x87, 
0x10, 0x83, 0x07, 0x86, 0x45, 0x86, 0x39, 0xc7, 0x0e, 0x02, 0x00, 0x02, 0x18, 0x00, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x70, 0x70, 0xe2, 0x98, 0xe0, 0x71, 0xce, 0x71, 0xc6, 0x21, 0xbf, 0xc0, 
0x60, 0x30, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x04, 0x60, 0x00, 0x04, 0x00, 0x00, 0x18, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x08, 0x00, 0x08, 0x00, 0x60, 0x80, 0x00, 0x80, 0x00, 0x33, 0x0c, 
0x38, 0x00, 0x18, 0x70, 0x00, 0x38, 0x70, 0xc2, 0x61, 0xc0, 0x00, 0xc3, 0x80, 0x01, 0x87, 0x00, 
0x00, 0x00, 0x08, 0xc2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x01, 0x9b, 0x00, 0x00, 0xac, 0xde, 0x40, 0x10, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x12, 0x08, 0x00, 0x08, 0x38, 0xe1, 
0xc7, 0x26, 0x0e, 0x38, 0xc0, 0x71, 0xc2, 0x00, 0x06, 0x07, 0xf8, 0x18, 0x00, 0x8c, 0x00, 0x4d, 
0x8f, 0xfe, 0x66, 0x1f, 0xfc, 0xcd, 0x1f, 0xfc, 0x07, 0xff, 0x0d, 0x9f, 0xf0, 0x3f, 0xe0, 0x3f, 
0xe0, 0xfe, 0x7f, 0xf0, 0x7f, 0xf0, 0x7f, 0xf0, 0xd8, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x0f, 0xff, 0x10, 0x49, 0x70, 0x7e, 0x30, 0x78, 0xf0, 0x7f, 0xf0, 0x3f, 0x8c, 0x1f, 0xf0, 
0x3f, 0xe0, 0x3f, 0xf0, 0x7f, 0xff, 0xfe, 0x7f, 0xfc, 0x20, 0xff, 0xc2, 0x07, 0xfe, 0x03, 0xfc, 
0x0f, 0xfe, 0x0f, 0xfe, 0x0f, 0xfe, 0x01, 0xfc, 0xe7, 0x1f, 0xfc, 0x3f, 0xf7, 0x07, 0xfc, 0x0c, 
0x60, 0xff, 0xe1, 0xff, 0x8a, 0x04, 0x00, 0x04, 0x1c, 0x20, 0xff, 0xc1, 0xff, 0x03, 0x53, 0x83, 
0xf1, 0x81, 0xff, 0x03, 0xc7, 0x83, 0xff, 0x83, 0xfc, 0x02, 0x00, 0x02, 0x18, 0x01, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x3f, 0xe0, 0x7c, 0x90, 0x7f, 0xe0, 0xff, 0xe0, 0xfc, 0x61, 0x8f, 0x08, 
0x70, 0x70, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x04, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x08, 0x00, 0x08, 0x00, 0x60, 0x80, 0x00, 0x80, 0x00, 0x33, 0x0f, 
0xf0, 0x00, 0x1f, 0xe0, 0x00, 0x3f, 0xe0, 0xfe, 0x7f, 0x80, 0x00, 0xff, 0x00, 0x01, 0xfe, 0x00, 
0x00, 0x00, 0x0d, 0x82, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x01, 0x9b, 0x00, 0x00, 0xf8, 0xd6, 0x00, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x08, 0x00, 0x08, 0x1f, 0x80, 
0xfc, 0x1c, 0x07, 0xe0, 0xc0, 0x3f, 0x06, 0x00, 0x7c, 0x01, 0xe0, 0x18, 0x00, 0x8c, 0x00, 0x47, 
0x07, 0xbc, 0x3e, 0x0f, 0x78, 0x7c, 0x07, 0xf0, 0x01, 0xfc, 0x07, 0x07, 0xe0, 0x0f, 0xc0, 0x0f, 
0x80, 0x3e, 0x3d, 0xe0, 0x3d, 0xe0, 0x3d, 0xe0, 0x70, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x01, 0xde, 0xe0, 0x33, 0xc0, 0x3c, 0xe0, 0x38, 0xe0, 0x3b, 0xc0, 0x1f, 0x70, 0x07, 0xc0, 
0x0f, 0xc0, 0x0f, 0xc0, 0x5e, 0xf7, 0xbc, 0x5d, 0xe7, 0xe0, 0x3c, 0x7e, 0x01, 0xf8, 0x01, 0xf0, 
0x07, 0xbc, 0x07, 0xbc, 0x07, 0xbc, 0x00, 0x78, 0x1e, 0x0f, 0x38, 0x27, 0xc6, 0x01, 0xf0, 0x07, 
0xc0, 0x77, 0xc0, 0x7e, 0x0e, 0x04, 0x00, 0x04, 0x07, 0xe0, 0x3f, 0x00, 0xfe, 0x01, 0x8f, 0x01, 
0xce, 0x00, 0xfc, 0x01, 0xc7, 0x01, 0xef, 0x00, 0xf0, 0x02, 0x00, 0x02, 0x1c, 0x01, 0x04, 0x00, 
0x04, 0x08, 0x00, 0x08, 0x1f, 0x80, 0x38, 0x70, 0x1f, 0x80, 0x7b, 0xc0, 0x79, 0xc1, 0xc0, 0x08, 
0x1d, 0xc0, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 0x00, 0x02, 0x00, 0x02, 0x04, 
0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 0x80, 0x00, 0x0c, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x08, 0x00, 0x08, 0x00, 0x60, 0x80, 0x00, 0x80, 0x00, 0x1f, 0x03, 
0xe0, 0x00, 0x07, 0xc0, 0x00, 0x0f, 0x80, 0x3e, 0x1f, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x7c, 0x00, 
0x00, 0x00, 0x07, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 
0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 
0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 0x00, 0xf9, 0x00, 0x00, 0x70, 0x60, 0x00, 0x00, 
0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 
0x08, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 0x00, 0x01, 0x00, 


0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xf8, 0x00, 0x80, 
0x04, 0x00, 0x00, 0x20, 0xc0, 0x01, 0x06, 0x00, 0xcc, 0x00, 0x00, 0x0c, 0x01, 0x86, 0x00, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 
0xff, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0xfc, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0xfe, 0x0c, 0x01, 0x07, 0xff, 
0xfc, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x18, 
0x00, 0x00, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x80, 0x00, 0x03, 0xff, 0xfe, 0x07, 
0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x0f, 0xff, 0xf8, 0x00, 0x60, 0xff, 0xff, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x3f, 
0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 
0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 
0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 
0xf8, 0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 0xff, 0xff, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x04, 0x00, 0x00, 0x20, 0xc0, 0x01, 0x06, 0x00, 0xcc, 0x00, 0x00, 0x0e, 0x01, 0x07, 0x00, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x0c, 0x06, 0x00, 0x00, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x04, 0x00, 0x00, 0x20, 0x60, 0x01, 0x07, 0x00, 0xcc, 0x00, 0x00, 0x07, 0x06, 0x03, 0x83, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x38, 
0x00, 0x00, 0x00, 0x02, 0x06, 0x00, 0x00, 0x00, 0x60, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x04, 0x00, 0x00, 0x20, 0x78, 0x01, 0x03, 0xc0, 0x78, 0x00, 0x00, 0x01, 0xf8, 0x00, 0xfc, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x1e, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x04, 0x00, 0x00, 0x20, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0f, 0xfe, 0x00, 0x00, 0x00, 0x1f, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x04, 0x00, 0x00, 0x20, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 



};

xFONTYY Tahoma14bBengali1 = {0x01, 29, 0, 29, 0, 0, 29, 320, 0x0d80, 0x0dff,
(PEGUSHORT *) Tahoma14bBengali1_offset_table, &Tahoma14bArabic_Font,
(PEGUBYTE *) Tahoma14bBengali1_data_table};


ROMDATA PEGUSHORT Tahoma14bBengali_offset_table[129] = {
0x0000,0x0017,0x0021,0x0035,0x0049,0x0060,0x006f,0x0082,0x008c,0x0099,0x00a8,0x00b7,0x00c3,0x00ce,0x00e5,0x00fc,
0x0105,0x0112,0x0129,0x0140,0x014b,0x0158,0x0167,0x0174,0x0181,0x018d,0x0199,0x01a4,0x01b1,0x01c0,0x01cf,0x01dc,
0x01e7,0x01f3,0x0202,0x020d,0x0218,0x0226,0x0233,0x023e,0x024a,0x0256,0x026d,0x027a,0x0289,0x0295,0x02a4,0x02b1,
0x02bd,0x02c9,0x02e0,0x02ed,0x0304,0x031b,0x0332,0x033e,0x034a,0x0356,0x0360,0x0377,0x038e,0x0397,0x039e,0x03b2,
0x03be,0x03d1,0x03dd,0x03eb,0x03f6,0x0402,0x0419,0x0430,0x043c,0x0448,0x045f,0x0476,0x048d,0x04a4,0x04ad,0x04b7,
0x04ce,0x04e5,0x04fc,0x0513,0x052a,0x0541,0x0558,0x056f,0x0583,0x059a,0x05b1,0x05c8,0x05df,0x05ee,0x05f9,0x0610,
0x061c,0x0628,0x0634,0x0640,0x064b,0x0662,0x0679,0x0683,0x068e,0x0698,0x06a5,0x06ae,0x06b8,0x06c5,0x06ce,0x06dd,
0x06e8,0x06f4,0x0700,0x070b,0x0718,0x0723,0x0730,0x073e,0x0746,0x0750,0x0758,0x0765,0x077c,0x0793,0x07aa,0x07c1,
0x07d8};



ROMDATA PEGUBYTE Tahoma14bBengali_data_table[7028 + 251] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x1f, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x80, 
0x00, 0x00, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0xff, 0xff, 
0x81, 0xff, 0xff, 0x03, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xff, 0xf0, 0x3f, 
0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 
0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x00, 0x00, 
0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x0f, 
0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x81, 
0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x07, 0x00, 0x08, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0x00, 0x7c, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x1e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x07, 0xf8, 0x30, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x0c, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x01, 0xc0, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x3e, 0x03, 0xf0, 0x1f, 0xf0, 0x3f, 0xe0, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0x00, 0x7f, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x03, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xe0, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x1c, 0x3f, 0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x0e, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x1f, 0x80, 0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0xfc, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x1f, 0x01, 0xf8, 0x07, 0xf8, 0x0f, 0xf0, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0x00, 0x07, 0x84, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xf0, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x08, 0x1f, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x07, 0x80, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x1e, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x80, 0x0c, 0x00, 0x0c, 0x00, 0x18, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0x00, 0x01, 0x84, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x1c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x01, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0x01, 0x80, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x08, 
0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0d, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0xff, 0xff, 0xff, 0xfb, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xec, 0x64, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0xf0, 0x78, 0xc4, 0x00, 0x04, 0x08, 0x00, 0x08, 0x0f, 0x01, 0xe3, 0xff, 0xfe, 0x31, 0xf1, 0xef, 
0xff, 0xf8, 0xb8, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0x1e, 0x0f, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xf8, 0xef, 0xff, 0xfc, 0xf3, 0xff, 0xfc, 0xe7, 0xff, 0xfc, 0x40, 0x00, 0x40, 0x7b, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x88, 0x00, 0x08, 0xff, 0xf8, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x01, 0xe0, 0x00, 0x1f, 0xff, 0x00, 0x00, 0x1f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x1c, 0x01, 0x80, 0x10, 0x00, 0x10, 0x20, 0x00, 0x20, 0x70, 
0x00, 0xf8, 0xe0, 0x01, 0xf0, 0x00, 0xf0, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x0f, 
0xe2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x11, 0xff, 0xff, 0xff, 0x88, 
0x00, 0x08, 0xff, 0xf7, 0x63, 0x20, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 
0x06, 0x00, 0x80, 0x00, 0x00, 0xe0, 0x18, 0x06, 0x00, 0xe0, 0xe0, 0x01, 0x00, 0xff, 0xff, 0xff, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x00, 0x0e, 0x10, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xee, 0x6f, 0x00, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0xf8, 0xfc, 0xc4, 0x00, 0x04, 0x08, 0x00, 0x08, 0x1f, 0x83, 0xf3, 0xff, 0xfe, 0x19, 0xf3, 0xff, 
0xff, 0xf9, 0xfc, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3e, 0x3f, 0xef, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xf9, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xfd, 0xe7, 0xff, 0xfc, 0x40, 0x00, 0x40, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x88, 0x00, 0x08, 0xff, 0xf8, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x01, 0xe0, 0x00, 0x1f, 0xff, 0x00, 0x00, 0x1f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x7c, 0x03, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x21, 0xf0, 
0x00, 0xfb, 0xe0, 0x01, 0xf0, 0x01, 0xf8, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x0f, 
0xe2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x11, 0xff, 0xff, 0xff, 0x88, 
0x00, 0x08, 0xff, 0xf7, 0xf3, 0x78, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 
0x06, 0x01, 0xc0, 0x03, 0xc3, 0xf8, 0x78, 0x06, 0x01, 0xf1, 0xf0, 0x03, 0xc0, 0xff, 0xff, 0xff, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xc0, 0x00, 0x6e, 0x30, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x03, 0x80, 0x00, 0x0e, 0x08, 0x00, 0x08, 0x00, 0x18, 0x00, 0x6e, 
0x0c, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0xfe, 0x63, 0xe0, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x98, 0xcc, 0xc4, 0x00, 0x04, 0x08, 0x00, 0x08, 0x1c, 0xc3, 0x9b, 0x03, 0x00, 0x7d, 0x86, 0x1c, 
0x30, 0xc0, 0xec, 0x18, 0x03, 0x00, 0x03, 0x60, 0x03, 0x30, 0x33, 0xf3, 0x00, 0x06, 0x00, 0xc0, 
0x0c, 0x03, 0x1c, 0x00, 0x01, 0x9b, 0x06, 0x01, 0x86, 0x00, 0x60, 0x40, 0x00, 0x41, 0xc7, 0x06, 
0x00, 0x00, 0xc0, 0x00, 0x01, 0x8c, 0x1c, 0xc0, 0x0c, 0x08, 0x00, 0x08, 0x00, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x06, 0x30, 0xe6, 0x18, 0x60, 0xc0, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x60, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x60, 0x07, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x21, 0x80, 
0x00, 0x33, 0x00, 0x00, 0x60, 0x01, 0x98, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x0c, 0x00, 0xc0, 0x08, 
0x00, 0x08, 0x39, 0x83, 0xb3, 0x1f, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x0f, 
0x06, 0x00, 0xf0, 0x67, 0xe3, 0x18, 0xf0, 0x06, 0x03, 0x19, 0xb0, 0xe0, 0xf0, 0x01, 0x80, 0x18, 
0x70, 0x07, 0xfc, 0x00, 0xc0, 0x06, 0x00, 0x18, 0x60, 0xc0, 0x00, 0x6e, 0x70, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x02, 0x80, 0x00, 0x0a, 0x08, 0x00, 0x08, 0x07, 0x18, 0x1e, 0x66, 
0x0f, 0x03, 0xc0, 0x03, 0x00, 0x1b, 0x00, 0x76, 0x60, 0x70, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0xd8, 0xed, 0xc4, 0x00, 0x04, 0x08, 0x00, 0x08, 0x1c, 0xc3, 0x9b, 0x0f, 0xc0, 0x6d, 0x87, 0x8c, 
0x3e, 0xc0, 0x7c, 0x18, 0x03, 0xfc, 0x06, 0x30, 0x0f, 0x30, 0x3b, 0x33, 0x00, 0x07, 0x00, 0xc0, 
0x0c, 0x03, 0xec, 0x03, 0xc1, 0xdb, 0x06, 0x01, 0xfe, 0x0f, 0x60, 0x40, 0x00, 0x41, 0x63, 0x06, 
0x00, 0x07, 0xc0, 0x18, 0x01, 0xcc, 0x0c, 0xc0, 0x7c, 0x08, 0x00, 0x08, 0x3f, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x1f, 0xb0, 0x76, 0x0c, 0x60, 0xf0, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x78, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xc0, 0x06, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x23, 0x00, 
0x00, 0x36, 0x00, 0x00, 0x60, 0x01, 0xb8, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x0c, 0x00, 0xc0, 0x08, 
0x00, 0x08, 0x19, 0x80, 0x73, 0x03, 0x80, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x1f, 
0x83, 0x00, 0x38, 0x66, 0x73, 0x19, 0xde, 0x06, 0x63, 0x18, 0x30, 0xf0, 0x38, 0x0f, 0x80, 0xf8, 
0x7c, 0x07, 0xfc, 0x01, 0xc0, 0x06, 0x3c, 0x18, 0x60, 0xc0, 0x3c, 0x60, 0xe0, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x03, 0x80, 0x00, 0x0e, 0x08, 0x00, 0x08, 0x0f, 0x98, 0x3f, 0x66, 
0x1f, 0x87, 0xe6, 0x33, 0x18, 0x1b, 0x30, 0x0e, 0x60, 0x18, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0xd8, 0xef, 0x84, 0x00, 0x04, 0x08, 0x00, 0x08, 0x00, 0xc0, 0x1f, 0x3f, 0xe0, 0x0d, 0x87, 0xec, 
0x1e, 0xc6, 0x78, 0x1c, 0x03, 0xfe, 0x07, 0xbc, 0x3f, 0x30, 0x3b, 0x73, 0x70, 0x0f, 0x8c, 0xc6, 
0x0d, 0xc3, 0xec, 0xc7, 0xe0, 0xdb, 0x06, 0x70, 0xfe, 0x1f, 0xe0, 0x40, 0x00, 0x40, 0x7f, 0x06, 
0xf8, 0x0f, 0xc3, 0x39, 0x03, 0xcc, 0x0c, 0xc0, 0xfc, 0x08, 0x00, 0x08, 0x7f, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x1f, 0xb0, 0x7e, 0x0f, 0x61, 0xf8, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0xfc, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xc0, 0x06, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x23, 0x00, 
0x00, 0x36, 0x00, 0x00, 0x60, 0x01, 0xb0, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0xcc, 0x60, 0xdc, 0x08, 
0x00, 0x08, 0x19, 0x81, 0xf3, 0x00, 0xc0, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x39, 
0xc3, 0x80, 0x0c, 0x77, 0xb1, 0xf1, 0x8e, 0x66, 0x63, 0x18, 0x30, 0x30, 0x0c, 0x1f, 0x83, 0xf8, 
0x1e, 0x01, 0x80, 0x01, 0x87, 0x8c, 0x7e, 0x18, 0x60, 0xc6, 0x7e, 0x61, 0xc0, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x0d, 0x98, 0xbb, 0x66, 
0x1d, 0x87, 0x6e, 0x33, 0xfc, 0xdb, 0xf8, 0x3e, 0x67, 0x8c, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0x18, 0x0f, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xc3, 0x98, 0xfe, 0x33, 0x30, 0x0d, 0x80, 0x6c, 
0x30, 0xc6, 0x61, 0x1f, 0x03, 0x66, 0x37, 0xcc, 0x33, 0x30, 0x03, 0x73, 0x78, 0x7d, 0x8c, 0xff, 
0x0d, 0xe1, 0xcc, 0xe6, 0x70, 0x33, 0x07, 0xf1, 0xc6, 0x18, 0xe0, 0x40, 0x00, 0x40, 0x7f, 0x1c, 
0xfc, 0x18, 0xc3, 0xb3, 0x87, 0xec, 0x3c, 0xc1, 0x8c, 0x08, 0x00, 0x08, 0x66, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x1f, 0xb0, 0xee, 0x07, 0xe1, 0xd8, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0xec, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xc0, 0x06, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x23, 0x00, 
0x00, 0x36, 0x00, 0x00, 0x60, 0x00, 0xc0, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0xcf, 0xf0, 0xde, 0x08, 
0x00, 0x08, 0x79, 0x83, 0x33, 0x3c, 0x60, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x30, 
0xc1, 0xe0, 0x0c, 0x37, 0xb1, 0xf1, 0x9c, 0x67, 0xf1, 0xf8, 0x3f, 0xf1, 0xe6, 0x3d, 0x83, 0x18, 
0x07, 0x01, 0x9c, 0x03, 0x8f, 0x8c, 0x66, 0x38, 0x60, 0xde, 0x66, 0x63, 0x80, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x4d, 0x99, 0xc3, 0x66, 
0x01, 0x87, 0x7e, 0x19, 0xec, 0xcd, 0xd8, 0x66, 0x6f, 0xcc, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0x18, 0x0c, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xe3, 0xdc, 0xfc, 0x73, 0x70, 0x39, 0x80, 0x6c, 
0x3c, 0xc3, 0x7f, 0x9f, 0xc3, 0xe6, 0x30, 0xcc, 0x73, 0xb0, 0x03, 0x33, 0x78, 0x79, 0x86, 0x7b, 
0x0d, 0xe0, 0x0c, 0x67, 0xb1, 0xf3, 0x07, 0xb1, 0xe6, 0x1e, 0xe0, 0x40, 0x00, 0x40, 0xf3, 0x1f, 
0xce, 0x3c, 0xc1, 0xbf, 0xc6, 0xfc, 0x3e, 0xc3, 0xcc, 0x08, 0x00, 0x08, 0x70, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x31, 0xe6, 0x06, 0xe0, 0x18, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x0c, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xc0, 0x06, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x23, 0x00, 
0x00, 0x36, 0x00, 0x00, 0x60, 0x00, 0x70, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x67, 0xb0, 0xde, 0x08, 
0x00, 0x08, 0x7d, 0x87, 0x3b, 0x7e, 0x60, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x30, 
0xc0, 0x60, 0xfc, 0x33, 0xb3, 0x19, 0x98, 0x33, 0xb0, 0xf8, 0x3f, 0xe3, 0xf6, 0x67, 0x87, 0x98, 
0x03, 0x81, 0xbe, 0x03, 0x0c, 0xd8, 0x0e, 0x30, 0x60, 0xf6, 0x66, 0x3f, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x03, 0x80, 0x00, 0x0e, 0x08, 0x00, 0x08, 0x61, 0xd8, 0xe3, 0xe6, 
0x1f, 0x80, 0xf6, 0x18, 0x0c, 0x6c, 0x18, 0xf7, 0x6e, 0xcc, 0x40, 0x00, 0x40, 0x80, 0x00, 0x80, 
0x18, 0x0c, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x70, 0x6c, 0x18, 0x1f, 0x70, 0x3f, 0x83, 0xec, 
0x3f, 0xc3, 0x3d, 0x99, 0xc1, 0xcc, 0x18, 0xcc, 0x1f, 0xf0, 0x03, 0xf3, 0x18, 0x61, 0x86, 0x03, 
0x0c, 0x60, 0x0c, 0x73, 0xb1, 0xfb, 0x02, 0x31, 0xfe, 0x1e, 0x60, 0x40, 0x00, 0x40, 0x43, 0x03, 
0xc6, 0x3f, 0xc1, 0xde, 0xc7, 0xdc, 0x07, 0xc3, 0xfc, 0x08, 0x00, 0x08, 0x70, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x31, 0xf6, 0x1e, 0x61, 0xf0, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0xf8, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xc0, 0x06, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x23, 0x00, 
0x00, 0x36, 0x00, 0x00, 0x60, 0x00, 0x38, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x60, 0x30, 0xc6, 0x08, 
0x00, 0x08, 0x0f, 0x83, 0xff, 0x76, 0x60, 0x00, 0x00, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x39, 
0xc0, 0x30, 0xf8, 0x18, 0x33, 0x19, 0xd8, 0x30, 0x30, 0x18, 0x31, 0x83, 0xb6, 0x79, 0x81, 0xf8, 
0x01, 0x81, 0xb6, 0x0e, 0x0c, 0xf8, 0x3c, 0x70, 0x60, 0xe6, 0x7e, 0x1e, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x0a, 0x08, 0x00, 0x08, 0x3f, 0xf8, 0x7f, 0xe6, 
0x1f, 0x07, 0xe6, 0x0e, 0x1c, 0x76, 0x38, 0x3f, 0xe0, 0xdc, 0x40, 0x00, 0x40, 0x80, 0x00, 0x86, 
0x7b, 0x3c, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x38, 0x66, 0x18, 0x0f, 0x60, 0x07, 0x83, 0x8c, 
0x07, 0xc1, 0xc3, 0x98, 0xc0, 0x0c, 0x18, 0xcc, 0x0f, 0x70, 0xcf, 0xe3, 0x38, 0x73, 0x83, 0x87, 
0x0c, 0xe0, 0x0c, 0x38, 0x70, 0x1f, 0x00, 0x30, 0x3e, 0x0e, 0x60, 0x40, 0x00, 0x40, 0x03, 0x01, 
0xde, 0x07, 0xc0, 0xe1, 0xc3, 0x9c, 0x01, 0xc0, 0x7c, 0x08, 0x00, 0x08, 0x30, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x30, 0x3e, 0x1c, 0x61, 0xf0, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0xf8, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0xe0, 0x07, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x23, 0x80, 
0x00, 0x37, 0x00, 0x00, 0x60, 0x00, 0x18, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x38, 0x70, 0xce, 0x08, 
0x00, 0x08, 0x03, 0x80, 0xf7, 0x06, 0xe0, 0x00, 0x3e, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x1f, 
0x87, 0x30, 0x3c, 0x1c, 0x63, 0x18, 0xec, 0x38, 0x70, 0x18, 0x31, 0x80, 0x3e, 0x7f, 0x86, 0x78, 
0x00, 0xc1, 0x86, 0x1c, 0x00, 0xf0, 0x31, 0xe0, 0x60, 0x46, 0x3c, 0x00, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x0e, 0x08, 0x00, 0x08, 0x1f, 0x38, 0x3e, 0xe6, 
0x07, 0x87, 0x83, 0x07, 0xf8, 0x3f, 0xf0, 0x06, 0xe1, 0xf8, 0x40, 0x00, 0x40, 0x80, 0x00, 0x87, 
0xfb, 0xfc, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x1f, 0xe7, 0xf8, 0x03, 0x00, 0x01, 0x80, 0x0c, 
0x01, 0xc0, 0xff, 0x1f, 0x80, 0x07, 0x0f, 0x8c, 0x03, 0x30, 0xff, 0x03, 0xf0, 0x3f, 0x01, 0xfe, 
0x0f, 0xc0, 0x0c, 0x1f, 0xe0, 0x07, 0x00, 0x30, 0x0e, 0x00, 0x60, 0x40, 0x00, 0x40, 0x03, 0x00, 
0xdc, 0x01, 0xc0, 0x7f, 0x80, 0x0c, 0x00, 0xc0, 0x1c, 0x08, 0x00, 0x08, 0x00, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x30, 0x0e, 0x00, 0x60, 0x78, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x3c, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x78, 0x03, 0x80, 0x10, 0x00, 0x10, 0x20, 0x00, 0x21, 0xe0, 
0x00, 0x33, 0xc0, 0x00, 0x60, 0x00, 0x38, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x1f, 0xe0, 0xfc, 0x08, 
0x00, 0x08, 0x01, 0x80, 0x33, 0x0f, 0xc1, 0x80, 0x1f, 0xc2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x0f, 
0x07, 0xf0, 0x0e, 0x0f, 0xe1, 0xf0, 0x7e, 0x1f, 0xe0, 0x18, 0x3f, 0x80, 0x7c, 0x0f, 0x87, 0x98, 
0x00, 0xc0, 0xfc, 0x78, 0x01, 0xe0, 0x3f, 0xc0, 0x60, 0x06, 0x00, 0x00, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x08, 0x00, 0x08, 0x00, 0x18, 0x00, 0x66, 
0x00, 0x80, 0x03, 0x03, 0xf0, 0x0f, 0xe0, 0x06, 0x61, 0xe0, 0x40, 0x00, 0x40, 0x80, 0x00, 0x83, 
0x99, 0xcc, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x0f, 0x81, 0xe0, 0x03, 0x00, 0x01, 0x80, 0x0c, 
0x00, 0xc0, 0x7e, 0x0f, 0x00, 0x02, 0x07, 0x0c, 0x03, 0x30, 0x73, 0x01, 0xe0, 0x1e, 0x00, 0xfc, 
0x07, 0x80, 0x0c, 0x0f, 0x80, 0x03, 0x00, 0x30, 0x06, 0x00, 0x60, 0x40, 0x00, 0x40, 0x03, 0x00, 
0xc0, 0x00, 0xc0, 0x3f, 0x00, 0x0c, 0x00, 0xc0, 0x0c, 0x08, 0x00, 0x08, 0x00, 0xc0, 0x80, 0x00, 
0x81, 0x00, 0x01, 0x02, 0x00, 0x02, 0x00, 0x30, 0x06, 0x00, 0x60, 0x08, 0x10, 0x00, 0x10, 0x20, 
0x00, 0x20, 0x00, 0x04, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x78, 0x03, 0x80, 0x10, 0x00, 0x10, 0x20, 0x00, 0x21, 0xe0, 
0x00, 0x33, 0xc0, 0x00, 0x60, 0x00, 0x10, 0x20, 0x00, 0x20, 0x40, 0x00, 0x40, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x03, 
0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 0x10, 0x00, 0x10, 0x0f, 0xc0, 0x78, 0x08, 
0x00, 0x08, 0x01, 0x80, 0x33, 0x0f, 0xe1, 0xf0, 0x07, 0xc2, 0x00, 0x02, 0x04, 0x00, 0x04, 0x00, 
0x07, 0xe0, 0x04, 0x07, 0xc0, 0xe0, 0x3e, 0x07, 0xc0, 0x18, 0x0f, 0x00, 0x78, 0x01, 0x81, 0xd8, 
0x00, 0xc0, 0x78, 0x60, 0x01, 0x80, 0x1f, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00, 0x80, 0x00, 0x81, 
0x00, 0x01, 0x02, 0x00, 0x02, 0x04, 0x00, 0x04, 0x08, 0x00, 0x08, 

0x1f, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x80, 
0x00, 0x00, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x0f, 0xff, 0xf8, 0x00, 0x00, 0xff, 0xff, 
0x81, 0xff, 0xff, 0x03, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xff, 0xf0, 0x3f, 
0xff, 0xe0, 0x70, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x06, 0x06, 0x00, 0x60, 0x00, 0xc0, 0x18, 
0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x38, 0x01, 0x80, 0x1f, 0xff, 0xf0, 0x3f, 0xff, 0xe0, 0xe0, 
0x00, 0x31, 0xc0, 0x00, 0x60, 0x00, 0x00, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0xc0, 0xff, 0xff, 0x81, 
0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x00, 0x03, 
0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 0x1f, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x0f, 
0xff, 0xf8, 0x70, 0x00, 0xe0, 0x00, 0x70, 0xf8, 0x1c, 0xc3, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x81, 
0xff, 0xff, 0x03, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x0f, 0xff, 0xf8, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0xe0, 0x03, 0x80, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x70, 0x00, 0xf0, 0x07, 0x30, 0x1c, 0x3e, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x81, 0x80, 0x07, 0x80, 0x78, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x70, 0x00, 0xf8, 0x0f, 0xb1, 0xcc, 0x37, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x81, 0xfc, 0x07, 0xc0, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x03, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x38, 0x0d, 0xf3, 0xec, 0x7f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0xc0, 0xff, 0x80, 0xe0, 0x7c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x03, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1c, 0x0d, 0xe3, 0x7c, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0xf0, 0x03, 0x80, 0x70, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x03, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x03, 0x78, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma14bBengali_Font = {0x01, 29, 0, 29, 0, 0, 29, 251, 0x0980, 0x09ff,
(PEGUSHORT *) Tahoma14bBengali_offset_table, &Tahoma14bBengali1, 
(PEGUBYTE *) Tahoma14bBengali_data_table};


