/*...........................................................................*/
/*.                  File Name : Tahoma08nArabic.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2014.04.03                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY NewGulLim10bKor_Font;

ROMDATA PEGUSHORT Tahoma08nArabic4_offset_table[145] = {
0x0000,0x000d,0x001a,0x0027,0x0034,0x0041,0x004e,0x005b,0x0068,0x0075,0x0082,0x008f,0x009c,0x00a9,0x00b6,0x00c3,
0x00d0,0x00d5,0x00d9,0x00dd,0x00e0,0x00e4,0x00e9,0x00ef,0x00f2,0x00f6,0x0101,0x010e,0x0112,0x0117,0x011a,0x011e,
0x0129,0x0135,0x0139,0x013e,0x0144,0x014b,0x0156,0x0162,0x0166,0x016b,0x0176,0x0182,0x0186,0x018b,0x0193,0x019c,
0x01a5,0x01ae,0x01b6,0x01bf,0x01c8,0x01d1,0x01d9,0x01e2,0x01eb,0x01f4,0x01fa,0x0201,0x0207,0x020e,0x0213,0x0218,
0x021d,0x0222,0x0232,0x0243,0x0250,0x025e,0x026e,0x027f,0x028c,0x029a,0x02a9,0x02b8,0x02c2,0x02cc,0x02db,0x02ea,
0x02f4,0x02fe,0x0307,0x0310,0x0319,0x0322,0x032b,0x0334,0x033d,0x0346,0x034e,0x0356,0x035e,0x0364,0x036c,0x0374,
0x037c,0x0382,0x038d,0x0399,0x039e,0x03a5,0x03b0,0x03bc,0x03c1,0x03c8,0x03d1,0x03db,0x03e1,0x03e8,0x03f0,0x03f9,
0x03fd,0x0402,0x040a,0x0413,0x041a,0x0422,0x042b,0x0435,0x0439,0x043e,0x0444,0x044b,0x0454,0x045b,0x0460,0x0466,
0x0471,0x047e,0x0489,0x0496,0x049a,0x049f,0x04a8,0x04b2,0x04bb,0x04c5,0x04cd,0x04d6,0x04de,0x04e7,0x04f4,0x0501,
0x050e};



ROMDATA PEGUBYTE Tahoma08nArabic4_data_table[2592] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xa9, 0x20, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x52, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xa0, 0x50, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x40, 0xa0, 0x38, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x86, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xe3, 0xff, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x02, 0x22, 0x42, 0x10, 0x90, 0x00, 
0x00, 0x00, 0x40, 0x90, 0x00, 0x00, 0x00, 0x01, 0x42, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x40, 0x10, 0x10, 0x04, 0x04, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 
0x00, 0x40, 0x00, 0x20, 0x00, 0x14, 0x00, 0x0a, 0x00, 0x01, 0x00, 0x86, 0x18, 0x02, 0x02, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x82, 0x41, 0x10, 0x48, 0x24, 0x14, 0x12, 0x0a, 0x08, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x02, 0x22, 0x44, 0x20, 0x90, 0x20, 
0x00, 0x00, 0x80, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x10, 
0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 
0x01, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 
0x40, 0x40, 0x10, 0x10, 0x05, 0x04, 0x81, 0x41, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x82, 
0x00, 0x00, 0x02, 0x00, 0x20, 0x00, 0x00, 0x00, 0x0a, 0x01, 0x00, 0x98, 0x60, 0x02, 0x02, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x42, 0x21, 0x08, 0x44, 0x22, 0x12, 0x11, 0x09, 0x08, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x02, 0x22, 0x4e, 0x70, 0x90, 0x46, 
0x02, 0x01, 0xc4, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 
0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x02, 0x01, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x80, 
0x40, 0x40, 0x10, 0x10, 0x04, 0x04, 0x01, 0x01, 0x00, 0xe0, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x60, 0x00, 0x30, 0x00, 0x0c, 0x01, 0x46, 0x00, 0x01, 0x00, 0x90, 0x40, 0x02, 0x02, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x42, 0x21, 0x08, 0x44, 0x22, 0x12, 0x11, 0x09, 0x08, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x02, 0x22, 0x40, 0x00, 0x90, 0xe9, 
0x04, 0x00, 0x08, 0x90, 0x00, 0x00, 0x00, 0x01, 0x03, 0x81, 0x40, 0x28, 0x01, 0x41, 0x40, 0x28, 
0x01, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 
0x20, 0x20, 0x00, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x80, 0x02, 0x80, 
0x02, 0x80, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x40, 0x10, 0x10, 0x04, 0x04, 0x01, 0x01, 0x01, 0x01, 0xf0, 0xe0, 0x04, 0x07, 0xc3, 0x80, 
0x00, 0x90, 0x00, 0x48, 0x00, 0x12, 0x00, 0x09, 0x00, 0x0d, 0x06, 0x88, 0x20, 0x02, 0x02, 0x11, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x03, 0x80, 0x80, 0x00, 0x00, 0x09, 0x00, 0x00, 
0x09, 0x00, 0x00, 0x00, 0x22, 0x11, 0x04, 0x42, 0x21, 0x11, 0x10, 0x88, 0x88, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x3a, 0x22, 0x46, 0x30, 0x90, 0x08, 
0x0e, 0x60, 0x1c, 0x90, 0x00, 0x00, 0x00, 0x00, 0x84, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x07, 0x07, 0x83, 0xc0, 0xe0, 0xe0, 0xf0, 0x78, 0x1c, 0x1c, 0x1e, 0x0f, 0x02, 0x04, 
0x10, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x0c, 0x03, 0x00, 0xc0, 0x01, 0x80, 0x03, 0x00, 0xc0, 0x30, 
0x58, 0x4c, 0x16, 0x13, 0x05, 0x84, 0xc1, 0x61, 0x31, 0x01, 0x11, 0x13, 0xe4, 0x04, 0x44, 0x4f, 
0x80, 0x90, 0x06, 0x48, 0x60, 0x12, 0x00, 0x09, 0x0c, 0x05, 0x02, 0x86, 0x18, 0x02, 0x02, 0x11, 
0x07, 0x07, 0x07, 0x0e, 0x10, 0x40, 0x00, 0x00, 0x84, 0x86, 0x43, 0x0c, 0x60, 0x08, 0x00, 0x60, 
0x08, 0x00, 0x60, 0x00, 0x22, 0x11, 0x04, 0x42, 0x21, 0x11, 0x10, 0x88, 0x88, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x42, 0x22, 0x49, 0x48, 0x91, 0x06, 
0x00, 0x90, 0x80, 0x91, 0x01, 0x20, 0x01, 0x01, 0x44, 0x88, 0x09, 0x00, 0x08, 0x08, 0x09, 0x00, 
0x08, 0x01, 0xc1, 0xe0, 0x70, 0x38, 0x38, 0x3c, 0x0e, 0x07, 0x07, 0x07, 0x81, 0xc0, 0xe1, 0x02, 
0x08, 0x10, 0x84, 0x21, 0x00, 0x00, 0x80, 0x00, 0x80, 0x02, 0x00, 0x10, 0x00, 0x08, 0x00, 0x08, 
0x00, 0x20, 0x01, 0x00, 0x09, 0x00, 0x12, 0x04, 0x81, 0x20, 0x02, 0x40, 0x04, 0x81, 0x20, 0x48, 
0x64, 0x52, 0x19, 0x14, 0x86, 0x45, 0x21, 0x91, 0x49, 0x30, 0xa1, 0x02, 0x24, 0xc2, 0x84, 0x08, 
0x90, 0x72, 0x09, 0x38, 0x90, 0x0e, 0x00, 0xc7, 0x12, 0x0d, 0x06, 0x81, 0x04, 0x42, 0x02, 0x11, 
0x08, 0x88, 0x88, 0x91, 0x10, 0x41, 0x01, 0x01, 0x44, 0x89, 0x24, 0x92, 0x91, 0x06, 0x00, 0x91, 
0x06, 0x00, 0x90, 0x80, 0x12, 0x09, 0x02, 0x41, 0x20, 0x90, 0x90, 0x48, 0x48, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x42, 0x22, 0x49, 0x48, 0x91, 0x01, 
0x00, 0x90, 0x88, 0x91, 0x01, 0x20, 0x21, 0x12, 0x24, 0x88, 0x09, 0x01, 0x08, 0x88, 0x09, 0x01, 
0x08, 0x86, 0x06, 0x40, 0x20, 0x10, 0xc0, 0xc8, 0x04, 0x02, 0x18, 0x19, 0x00, 0x80, 0x40, 0x82, 
0x04, 0x10, 0x42, 0x10, 0x80, 0x44, 0x80, 0x44, 0x81, 0x12, 0x08, 0x90, 0x04, 0x48, 0x04, 0x48, 
0x11, 0x20, 0x89, 0x00, 0x11, 0x00, 0x22, 0x08, 0x82, 0x20, 0x04, 0x40, 0x08, 0x82, 0x20, 0x88, 
0x44, 0x62, 0x11, 0x18, 0x84, 0x46, 0x21, 0x11, 0x88, 0xc0, 0x41, 0x01, 0x43, 0x01, 0x04, 0x05, 
0x10, 0x12, 0x09, 0x08, 0x92, 0x02, 0x01, 0x21, 0x12, 0x41, 0x20, 0x81, 0x04, 0x42, 0x02, 0x11, 
0x08, 0x88, 0x88, 0x91, 0x10, 0x40, 0x01, 0x12, 0x24, 0x89, 0x24, 0x92, 0x91, 0x01, 0x00, 0x91, 
0x01, 0x20, 0x90, 0x88, 0x12, 0x09, 0x02, 0x41, 0x20, 0x90, 0x90, 0x48, 0x48, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x3a, 0x22, 0x47, 0x48, 0x91, 0x01, 
0x20, 0x48, 0x88, 0x91, 0x01, 0x20, 0x21, 0x12, 0x23, 0x88, 0x09, 0x01, 0x08, 0x88, 0x09, 0x01, 
0x08, 0x88, 0x08, 0x41, 0xc0, 0xe1, 0x01, 0x08, 0x38, 0x1c, 0x20, 0x21, 0x07, 0x03, 0x80, 0x82, 
0x04, 0x10, 0x42, 0x10, 0x90, 0x44, 0x90, 0x44, 0x81, 0x12, 0x08, 0x91, 0x04, 0x49, 0x04, 0x48, 
0x11, 0x20, 0x89, 0x10, 0xa2, 0x21, 0x44, 0x51, 0x14, 0x44, 0x28, 0x88, 0x51, 0x14, 0x45, 0x10, 
0x88, 0x44, 0x22, 0x11, 0x08, 0x84, 0x42, 0x21, 0x11, 0x00, 0xa0, 0x98, 0x84, 0x02, 0x82, 0x62, 
0x10, 0x12, 0x06, 0x08, 0x62, 0x02, 0x01, 0x21, 0x0c, 0x41, 0x20, 0x81, 0x04, 0x42, 0x02, 0x11, 
0x08, 0x88, 0xc8, 0x91, 0x90, 0x48, 0x21, 0x12, 0x23, 0x89, 0x25, 0x0e, 0x91, 0x01, 0x20, 0x49, 
0x01, 0x20, 0x48, 0x88, 0x0c, 0x07, 0x01, 0x80, 0xe0, 0x60, 0x70, 0x30, 0x38, 0x40, 0x22, 0x01, 
0x00, 0x00, 

0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x42, 0x1a, 0x31, 0x3e, 0x8c, 0xfe, 
0x20, 0x27, 0x36, 0x8c, 0xfe, 0x1f, 0xde, 0x6d, 0xc0, 0x67, 0xf0, 0xfe, 0xf3, 0x67, 0xf0, 0xfe, 
0xf3, 0x69, 0x09, 0x3e, 0x07, 0x1d, 0x01, 0x07, 0xc0, 0xe3, 0xa0, 0x20, 0xf8, 0x1c, 0x7f, 0x3d, 
0xf9, 0xec, 0x43, 0x10, 0xd0, 0x7b, 0x10, 0x7b, 0x7e, 0xec, 0xf7, 0x6d, 0x07, 0xb1, 0x07, 0xb7, 
0xee, 0xcf, 0x76, 0xd0, 0xfc, 0x21, 0xfb, 0xbe, 0x2f, 0xb4, 0x3f, 0x08, 0x7e, 0xef, 0x8b, 0xef, 
0xf1, 0xfb, 0xfc, 0x7e, 0xff, 0x1f, 0xbf, 0xc7, 0xed, 0x01, 0x1f, 0xe3, 0x74, 0x04, 0x7f, 0x8d, 
0xcf, 0xe1, 0xf9, 0xf3, 0x9a, 0x04, 0x40, 0xfe, 0x73, 0x3e, 0x1f, 0x7e, 0x7b, 0x3c, 0x43, 0xe6, 
0xd7, 0x17, 0x37, 0x2e, 0x4f, 0x88, 0x3e, 0x6d, 0xc0, 0x7f, 0xce, 0xe2, 0x7c, 0xfe, 0x20, 0x24, 
0xfe, 0x20, 0x27, 0x36, 0x38, 0x1c, 0xc7, 0x03, 0x99, 0xc1, 0xcc, 0xe0, 0xe6, 0x7f, 0xe3, 0xff, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x48, 0x00, 
0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x08, 0x08, 0x00, 0x40, 0x01, 0x01, 0x00, 0x00, 0x00, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x42, 0x10, 0x90, 0x40, 0x10, 0x40, 0x00, 0x00, 0x00, 0x01, 0x04, 0x01, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x80, 0x21, 0x00, 0x00, 0x00, 0x04, 0x20, 0x08, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x04, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xf8, 0x40, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x00, 
0x10, 0x10, 0x00, 0x00, 0x00, 0x08, 0x20, 0x00, 0x00, 0x00, 0x05, 0x02, 0x10, 0x00, 0x20, 0x20, 
0x00, 0x1f, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x10, 0x90, 0x00, 
0x1f, 0xc0, 0x00, 0x00, 0x10, 0x02, 0x02, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x04, 0x00, 0x00, 0x40, 0x80, 0x80, 0x00, 0x00, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x84, 0x21, 0x10, 0x40, 0x10, 0x40, 0x00, 0x00, 0x00, 0x01, 0x04, 0x01, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x80, 0x21, 0x00, 0x00, 0x00, 0x04, 0x20, 0x08, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x09, 0x08, 0x00, 0x04, 0x24, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x00, 
0x10, 0x10, 0x00, 0x00, 0x00, 0x08, 0x20, 0x00, 0x00, 0x00, 0x06, 0x04, 0x20, 0x00, 0x1f, 0xc0, 
0x28, 0x00, 0x02, 0xa8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x61, 0xf8, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0xc3, 0xc0, 0x00, 0x00, 0x78, 0x78, 0x00, 0x00, 0x0f, 0x0f, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x18, 0xc6, 0x0f, 0x80, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0xf8, 0x00, 
0x00, 0x00, 0x00, 0x0f, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x07, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xf0, 0x00, 0x03, 0xc3, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 
0x20, 0x20, 0x00, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x04, 0x18, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc1, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 


};

xFONTYY Tahoma08nArabic4 = {0x01, 16, 0, 16, 0, 0, 16, 162, 0xfe70, 0xfeff,
(PEGUSHORT *) Tahoma08nArabic4_offset_table, &NewGulLim10bKor_Font,
(PEGUBYTE *) Tahoma08nArabic4_data_table};


ROMDATA PEGUSHORT Tahoma08nArabic3_offset_table[689] = {
0x0000,0x0004,0x0008,0x0013,0x001f,0x0023,0x0028,0x0033,0x003f,0x0043,0x0048,0x0053,0x005f,0x0063,0x0068,0x0073,
0x007f,0x0083,0x0088,0x0093,0x009f,0x00a3,0x00a8,0x00b3,0x00bf,0x00c3,0x00c8,0x00d3,0x00df,0x00e4,0x00eb,0x00f6,
0x0102,0x0107,0x010e,0x0116,0x011f,0x0128,0x0131,0x0139,0x0142,0x014b,0x0154,0x015c,0x0165,0x016e,0x0177,0x017f,
0x0188,0x0191,0x019a,0x01a0,0x01a7,0x01ad,0x01b4,0x01ba,0x01c1,0x01c7,0x01ce,0x01d3,0x01d8,0x01dd,0x01e2,0x01ee,
0x01fb,0x0201,0x0208,0x0214,0x0221,0x0227,0x022e,0x023a,0x0247,0x024d,0x0254,0x0260,0x026d,0x0274,0x027c,0x0285,
0x028f,0x0298,0x02a2,0x02a6,0x02ab,0x02b1,0x02b8,0x02be,0x02c5,0x02c9,0x02d0,0x02d9,0x02e0,0x02e9,0x02f0,0x02fc,
0x0308,0x0314,0x0320,0x032d,0x033a,0x0347,0x0354,0x0361,0x036e,0x037b,0x0388,0x0395,0x03a2,0x03af,0x03bc,0x03c9,
0x03d6,0x03e3,0x03f0,0x03fd,0x040a,0x0417,0x0424,0x0431,0x043e,0x044b,0x0458,0x0465,0x0472,0x047f,0x048c,0x0499,
0x04a6,0x04b3,0x04c0,0x04cd,0x04d6,0x04e0,0x04e6,0x04ed,0x04f2,0x04f8,0x04fd,0x0503,0x0508,0x050e,0x0514,0x0519,
0x051f,0x0524,0x052a,0x052f,0x0535,0x0540,0x054d,0x0551,0x0555,0x0559,0x055e,0x056b,0x0578,0x0585,0x0592,0x059f,
0x05ac,0x05b9,0x05c6,0x05d3,0x05e0,0x05ed,0x05fa,0x0607,0x0614,0x0621,0x062e,0x063b,0x0648,0x0653,0x0660,0x0664,
0x0669,0x0676,0x0683,0x0690,0x069d,0x06aa,0x06b7,0x06c4,0x06d1,0x06de,0x06eb,0x06f8,0x0705,0x0712,0x071f,0x072c,
0x0739,0x0746,0x0753,0x0760,0x076d,0x077a,0x0787,0x0794,0x07a1,0x07ae,0x07bb,0x07c8,0x07d5,0x07e2,0x07ef,0x07fc,
0x0809,0x0816,0x0823,0x0830,0x083d,0x084a,0x0857,0x0864,0x0871,0x087e,0x088b,0x0898,0x08a5,0x08b2,0x08bf,0x08cc,
0x08d9,0x08e6,0x08f3,0x0900,0x090d,0x091a,0x0927,0x0934,0x0941,0x094e,0x095b,0x0968,0x0975,0x0982,0x098f,0x099c,
0x09a9,0x09b6,0x09c3,0x09d0,0x09dd,0x09ea,0x09f7,0x0a04,0x0a11,0x0a1e,0x0a2b,0x0a38,0x0a45,0x0a52,0x0a5f,0x0a6c,
0x0a79,0x0a86,0x0a93,0x0aa0,0x0aad,0x0aba,0x0ac7,0x0ad4,0x0ae1,0x0aee,0x0afb,0x0b08,0x0b15,0x0b22,0x0b2f,0x0b35,
0x0b3a,0x0b3f,0x0b44,0x0b49,0x0b56,0x0b63,0x0b70,0x0b7d,0x0b8a,0x0b97,0x0ba4,0x0bb1,0x0bbe,0x0bcb,0x0bd8,0x0be5,
0x0bf2,0x0bff,0x0c0c,0x0c19,0x0c26,0x0c33,0x0c40,0x0c4d,0x0c5a,0x0c67,0x0c74,0x0c81,0x0c8e,0x0c9b,0x0ca8,0x0cb5,
0x0cc2,0x0ccf,0x0cdc,0x0ce9,0x0cf6,0x0d03,0x0d10,0x0d1d,0x0d2a,0x0d37,0x0d44,0x0d51,0x0d5e,0x0d6b,0x0d78,0x0d85,
0x0d92,0x0d9f,0x0dac,0x0db9,0x0dc6,0x0dd3,0x0de0,0x0ded,0x0dfa,0x0e07,0x0e14,0x0e21,0x0e2e,0x0e3b,0x0e48,0x0e55,
0x0e62,0x0e6f,0x0e7c,0x0e89,0x0e96,0x0ea3,0x0eb0,0x0ebd,0x0eca,0x0ed7,0x0ee4,0x0ef1,0x0efe,0x0f0b,0x0f18,0x0f25,
0x0f32,0x0f3f,0x0f4c,0x0f59,0x0f66,0x0f73,0x0f80,0x0f8d,0x0f9a,0x0fa7,0x0fb4,0x0fc1,0x0fce,0x0fdb,0x0fe8,0x0ff5,
0x1002,0x100f,0x101c,0x1029,0x1036,0x1043,0x1050,0x105d,0x106a,0x1077,0x1084,0x1091,0x109e,0x10ab,0x10b8,0x10c5,
0x10d2,0x10df,0x10ec,0x10f9,0x1106,0x1113,0x1120,0x112d,0x113a,0x1147,0x1154,0x1161,0x116e,0x117b,0x1188,0x1195,
0x11a2,0x11af,0x11bc,0x11c9,0x11d6,0x11e3,0x11f0,0x11fd,0x120a,0x1217,0x1224,0x1231,0x123e,0x124b,0x1258,0x1265,
0x1272,0x127f,0x128c,0x1299,0x12a6,0x12b3,0x12c0,0x12cd,0x12da,0x12e7,0x12f4,0x1301,0x130e,0x131b,0x1328,0x1335,
0x1342,0x134f,0x135c,0x1369,0x1376,0x1383,0x1390,0x139d,0x13aa,0x13b7,0x13c4,0x13d1,0x13de,0x13eb,0x13f8,0x1405,
0x1412,0x141f,0x142c,0x1439,0x1446,0x1453,0x1460,0x146d,0x147a,0x1487,0x1494,0x14a1,0x14ae,0x14bb,0x14c8,0x14d5,
0x14e2,0x14ef,0x14fc,0x1509,0x1516,0x1523,0x1530,0x153d,0x154a,0x1557,0x1564,0x1571,0x157e,0x158b,0x1598,0x15a5,
0x15b2,0x15bf,0x15cc,0x15d9,0x15e6,0x15f3,0x1600,0x160d,0x161a,0x1627,0x1634,0x1641,0x164e,0x165b,0x1668,0x166d,
0x1672,0x167f,0x168c,0x1699,0x16a6,0x16b3,0x16c0,0x16cd,0x16da,0x16e7,0x16f4,0x1701,0x170e,0x171b,0x1728,0x1735,
0x1742,0x174f,0x175c,0x1769,0x1776,0x1783,0x1790,0x179d,0x17aa,0x17b7,0x17c4,0x17d1,0x17de,0x17eb,0x17f8,0x1805,
0x1812,0x181f,0x182c,0x1839,0x1846,0x1853,0x1860,0x186d,0x187a,0x1887,0x1894,0x18a1,0x18ae,0x18bb,0x18c8,0x18d5,
0x18e2,0x18ef,0x18fc,0x1909,0x1916,0x1923,0x1930,0x193d,0x194a,0x1957,0x1964,0x1971,0x197e,0x198b,0x1998,0x19a5,
0x19b2,0x19bf,0x19cc,0x19d9,0x19e6,0x19f3,0x1a00,0x1a0d,0x1a1a,0x1a27,0x1a34,0x1a41,0x1a4e,0x1a5b,0x1a68,0x1a75,
0x1a82,0x1a8f,0x1a9c,0x1aa9,0x1ab6,0x1ac3,0x1ad0,0x1add,0x1aea,0x1af7,0x1b04,0x1b11,0x1b1e,0x1b2b,0x1b38,0x1b45,
0x1b52,0x1b5f,0x1b6c,0x1b79,0x1b86,0x1b93,0x1ba0,0x1bad,0x1bba,0x1bc7,0x1bd4,0x1be1,0x1bee,0x1bfb,0x1c08,0x1c15,
0x1c22,0x1c2f,0x1c3c,0x1c49,0x1c56,0x1c63,0x1c70,0x1c7d,0x1c8a,0x1c97,0x1ca4,0x1cb1,0x1cbe,0x1ccb,0x1cd8,0x1ce5,
0x1cf2,0x1cff,0x1d0c,0x1d19,0x1d26,0x1d33,0x1d40,0x1d4d,0x1d5a,0x1d67,0x1d74,0x1d81,0x1d8e,0x1d9b,0x1da8,0x1db5,
0x1dc2,0x1dcf,0x1ddc,0x1de9,0x1df6,0x1e03,0x1e10,0x1e1d,0x1e2a,0x1e37,0x1e44,0x1e51,0x1e5e,0x1e6b,0x1e78,0x1e85,
0x1e92,0x1e9f,0x1eac,0x1eb9,0x1ec6,0x1ed3,0x1ee0,0x1eed,0x1efa,0x1f07,0x1f14,0x1f21,0x1f2e,0x1f3b,0x1f48,0x1f55,
0x1f62,0x1f6f,0x1f7c,0x1f8c,0x1f99,0x1fa6,0x1fb3,0x1fc0,0x1fcd,0x1fda,0x1fe7,0x1ff4,0x2001,0x2013,0x2020,0x202d,
0x203a};



ROMDATA PEGUBYTE Tahoma08nArabic3_data_table[16512] = {
0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x9d, 0x7e, 0x3a, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x03, 0x06, 0x18, 0x00, 0xc0, 0x0c, 0x18, 0x60, 0x53, 0x05, 0x35, 0x3a, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x10, 0x41, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9a, 0x80, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x80, 0x00, 0x50, 0x00, 
0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x10, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc0, 0x0c, 0x18, 0x60, 0x03, 0x00, 0x30, 0x61, 0x80, 0x0c, 0x00, 0xc0, 0xc1, 0x80, 
0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x2b, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x80, 0x84, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x05, 
0x00, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x1c, 0x70, 0x00, 0x21, 0x00, 0x0c, 0x00, 0xc1, 
0x86, 0x03, 0x30, 0x33, 0x67, 0x98, 0x0c, 0xc0, 0xcd, 0x9e, 0x60, 0x33, 0x03, 0x33, 0x36, 0x60, 
0x00, 0x00, 0x10, 0x00, 0x10, 0x84, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf0, 0x50, 0x28, 0xae, 0xb0, 0x00, 0x00, 
0x04, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x07, 0xd4, 0x0f, 0x9f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x0c, 0x0f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe0, 
0x16, 0x03, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x00, 0x00, 0x07, 0xfe, 0x3f, 0xf1, 0xff, 0x80, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x00, 0xa0, 0x04, 0x00, 0x80, 0x84, 0x02, 0x80, 0x01, 0x40, 0x80, 0x50, 0x00, 
0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x40, 0x0a, 0x14, 0x50, 0x00, 0x21, 0x00, 0x30, 0x03, 0x06, 
0x18, 0x00, 0xc0, 0x0c, 0x18, 0x60, 0x03, 0x00, 0x30, 0x61, 0x80, 0x0c, 0x00, 0xc0, 0xc1, 0x80, 
0x00, 0x00, 0x1c, 0x08, 0x10, 0x88, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x10, 0x08, 0x04, 0x30, 0xc1, 0x8c, 0x00, 
0x04, 0x20, 0x12, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0xa8, 0x01, 0x50, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x12, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x20, 
0x00, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x20, 0x04, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x80, 
0x04, 0x0a, 0x01, 0x40, 0x0a, 0x07, 0x00, 0xe0, 0xe7, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x05, 
0x00, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, 0x0a, 0x00, 0x3c, 0xf0, 0x84, 0x39, 0xc0, 0xc0, 0x0c, 0x18, 
0x60, 0x03, 0x00, 0x30, 0x61, 0x80, 0x0c, 0x00, 0xc1, 0x86, 0x00, 0x30, 0x03, 0x03, 0x06, 0x00, 
0x00, 0x00, 0x14, 0x08, 0x1c, 0xfc, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x00, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x10, 0x08, 0x04, 0xc3, 0x01, 0x8c, 0x52, 
0x84, 0x21, 0xa0, 0x00, 0x00, 0x08, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x57, 0xc0, 0xa0, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x21, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x20, 
0x09, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x20, 0x04, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x00, 0xa0, 0x05, 0x00, 0xa0, 0xa5, 0x01, 0x80, 0x00, 0xc1, 0x40, 0x30, 0x00, 
0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x40, 0x80, 0x08, 0x10, 
0x40, 0x02, 0x00, 0x20, 0x41, 0x00, 0x08, 0x00, 0x81, 0x04, 0x00, 0x20, 0x02, 0x02, 0x04, 0x00, 
0x00, 0x00, 0x3c, 0x0e, 0x14, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x38, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x00, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x10, 0x08, 0x04, 0x82, 0x02, 0x10, 0x21, 
0x00, 0x01, 0xf5, 0x28, 0x00, 0x14, 0xa0, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x01, 0x80, 0x00, 0x00, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x21, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x20, 
0x09, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x20, 0x04, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x80, 
0x04, 0x0a, 0x01, 0x40, 0x0a, 0x0f, 0x01, 0xe1, 0xef, 0x02, 0x40, 0x01, 0x20, 0x00, 0x48, 0x00, 
0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x10, 0x80, 0x84, 0x04, 0x20, 0x21, 0x4a, 0x7b, 0xc0, 0x40, 0x04, 0x08, 
0x20, 0x01, 0x00, 0x10, 0x20, 0x80, 0x04, 0x00, 0x40, 0x82, 0x00, 0x10, 0x01, 0x01, 0x02, 0x00, 
0x00, 0x00, 0x00, 0x0a, 0x3d, 0xe8, 0x1c, 0x40, 0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00, 
0x00, 0x0e, 0x00, 0x10, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x10, 0x68, 0x34, 0x41, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x02, 0x40, 0x00, 0x00, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x40, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x21, 
0xc9, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x20, 0x04, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x40, 0x19, 0x21, 0x80, 0x48, 0x03, 
0x24, 0x30, 0xe0, 0xe0, 0xf0, 0x78, 0x1c, 0x1c, 0x1e, 0x0f, 0x03, 0x83, 0x83, 0xc1, 0xe0, 0x70, 
0x70, 0x78, 0x3c, 0x08, 0x10, 0x40, 0x82, 0x04, 0x10, 0x20, 0x00, 0x00, 0x00, 0x30, 0x03, 0x06, 
0x18, 0x00, 0xc0, 0x0c, 0x18, 0x60, 0x03, 0x00, 0x30, 0x61, 0x80, 0x0c, 0x00, 0xc0, 0xc1, 0x84, 
0x10, 0x00, 0x82, 0x1e, 0x00, 0x04, 0x24, 0x20, 0x00, 0x00, 0x32, 0x18, 0x32, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x20, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x10, 0x28, 0x14, 0x30, 0xc3, 0x18, 0x63, 
0x0c, 0x60, 0xc6, 0x30, 0xc6, 0x18, 0xc0, 0x10, 0x00, 0xc0, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x02, 0x00, 0x18, 0x00, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x40, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x49, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x20, 0x04, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x44, 0x40, 0x48, 0x00, 0x40, 0x40, 0x48, 0x00, 0x40, 0x40, 0x48, 0x00, 0x40, 0x40, 0x48, 0x00, 
0x40, 0x40, 0x48, 0x00, 0x40, 0x40, 0x48, 0x00, 0x40, 0x41, 0xc8, 0x24, 0xe2, 0x48, 0x39, 0x04, 
0x9c, 0x48, 0x38, 0x3c, 0x0e, 0x07, 0x07, 0x07, 0x81, 0xc0, 0xe0, 0xe0, 0xf0, 0x38, 0x1c, 0x1c, 
0x1e, 0x07, 0x03, 0x84, 0x08, 0x20, 0x41, 0x02, 0x08, 0x10, 0x84, 0x21, 0x10, 0x09, 0x00, 0x81, 
0x04, 0x40, 0x24, 0x02, 0x04, 0x11, 0x00, 0x90, 0x08, 0x10, 0x44, 0x02, 0x40, 0x20, 0x20, 0x44, 
0x10, 0x00, 0x82, 0x00, 0x08, 0x0a, 0x24, 0x50, 0x01, 0x08, 0x49, 0x24, 0x49, 0x24, 0x04, 0x00, 
0x00, 0x04, 0x00, 0x70, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x10, 0x68, 0x34, 0x08, 0x24, 0xa4, 0x94, 
0x92, 0x91, 0x29, 0x49, 0x29, 0x25, 0x22, 0x0c, 0x01, 0x21, 0x11, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x41, 0x80, 0x24, 0x20, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x40, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x24, 
0x49, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x21, 0x21, 0x44, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 
0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x24, 0x22, 0x48, 0x09, 0x04, 
0x84, 0x48, 0xc0, 0xc8, 0x04, 0x02, 0x18, 0x19, 0x00, 0x80, 0x43, 0x03, 0x20, 0x10, 0x08, 0x60, 
0x64, 0x02, 0x01, 0x02, 0x08, 0x10, 0x40, 0x82, 0x04, 0x10, 0x42, 0x10, 0x90, 0x09, 0x00, 0x81, 
0x04, 0x40, 0x24, 0x02, 0x04, 0x11, 0x00, 0x90, 0x08, 0x10, 0x44, 0x02, 0x40, 0x20, 0x20, 0x44, 
0x10, 0x00, 0x82, 0x00, 0x08, 0x91, 0x24, 0x88, 0x71, 0x10, 0x49, 0x24, 0x49, 0x24, 0x0a, 0x00, 
0x00, 0x0a, 0x00, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x12, 0x09, 0x04, 0x08, 0x24, 0xa4, 0x94, 
0x92, 0x91, 0x29, 0x49, 0x28, 0xa5, 0x22, 0x02, 0x40, 0xa1, 0x11, 0x10, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x40, 0x40, 0x24, 0x22, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x40, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x24, 
0x49, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x21, 0x21, 0x24, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 
0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x08, 0x44, 0x40, 0x48, 0x18, 0x21, 0x88, 0x09, 0x03, 
0x04, 0x31, 0x21, 0x28, 0x38, 0x1c, 0x20, 0x21, 0x07, 0x03, 0x84, 0x04, 0x20, 0xe0, 0x70, 0xa8, 
0xac, 0x1c, 0x0e, 0x02, 0x08, 0x10, 0x40, 0x82, 0x04, 0x10, 0x42, 0x10, 0x90, 0x09, 0x00, 0x81, 
0x04, 0x40, 0x24, 0x02, 0x04, 0x11, 0x00, 0x90, 0x08, 0x10, 0x44, 0x02, 0x40, 0x20, 0x20, 0x44, 
0x12, 0x08, 0x82, 0x41, 0x08, 0x91, 0x1c, 0x88, 0x91, 0x1e, 0x49, 0x28, 0x49, 0x28, 0x10, 0x00, 
0x00, 0x10, 0x00, 0x00, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x12, 0x09, 0x04, 0x08, 0x23, 0xa4, 0x74, 
0x8e, 0x90, 0xe7, 0x48, 0xe4, 0x9d, 0x22, 0x02, 0x40, 0x51, 0x11, 0x10, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x40, 0x48, 0x12, 0x22, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x00, 0x00, 0x00, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x40, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x23, 
0xc9, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x21, 0x21, 0x24, 0x02, 0x20, 0x11, 0x00, 0x80, 

0x43, 0x3f, 0x87, 0xf7, 0x9b, 0x3f, 0x87, 0xf7, 0x9b, 0x3f, 0x87, 0xf7, 0x9b, 0x3f, 0x87, 0xf7, 
0x9b, 0x3f, 0x87, 0xf7, 0x9b, 0x3f, 0x87, 0xf7, 0x9b, 0x3f, 0x87, 0xe7, 0xce, 0x67, 0xf0, 0xfc, 
0xf9, 0xcd, 0x01, 0x07, 0xc0, 0xe3, 0xaa, 0x2a, 0xf8, 0x1c, 0x75, 0x45, 0x5f, 0x03, 0x8e, 0x80, 
0x83, 0xe0, 0x71, 0xfc, 0xf7, 0xe7, 0xbf, 0x3d, 0xf9, 0xec, 0x43, 0x10, 0xcf, 0xf0, 0xff, 0x7e, 
0x7b, 0x3f, 0xc3, 0xfd, 0xf9, 0xec, 0xff, 0x0f, 0xf7, 0xe7, 0xb3, 0xfc, 0x3f, 0xdb, 0xc7, 0xb3, 
0xe2, 0x0e, 0x7c, 0x41, 0xf3, 0x6e, 0x03, 0x70, 0x8e, 0x71, 0xfe, 0x77, 0xfe, 0x77, 0x60, 0x01, 
0xff, 0x60, 0x01, 0xff, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xf0, 0xfb, 0xf3, 0xd8, 0x9f, 0x13, 
0xe2, 0x7c, 0x21, 0x3e, 0x23, 0xc4, 0xf9, 0xfc, 0x3f, 0x8e, 0x66, 0x6c, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0x88, 0x09, 0xcd, 0x9f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x21, 0x0f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe0, 
0x36, 0x23, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1e, 0x1e, 0x27, 0xfe, 0x3f, 0xf1, 0xff, 0x80, 

0x00, 0x04, 0x00, 0x80, 0x44, 0x0a, 0x01, 0x41, 0x4a, 0x0a, 0x01, 0x41, 0x4a, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x21, 0x20, 0x08, 0x08, 0x20, 0x20, 0x02, 0x80, 0x04, 0x84, 0x80, 0x50, 0x50, 0xa8, 
0xa8, 0x0a, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x10, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x40, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x08, 0x00, 0x41, 0x00, 0x00, 0x00, 0x01, 0x02, 0x10, 0x00, 0x28, 0x00, 0x28, 0x80, 0x06, 
0x00, 0x80, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0x10, 
0x82, 0x10, 0x21, 0x09, 0xef, 0x84, 0x20, 0x20, 0x04, 0x01, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x80, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x10, 0x10, 0x00, 0x02, 0x82, 0x02, 0x00, 0x20, 0x20, 0x40, 
0x40, 0x00, 0x00, 0x28, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x84, 0x21, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x08, 0x00, 0x41, 0x00, 0x00, 0x00, 0x00, 0x02, 0x10, 0x00, 0x30, 0x00, 0x30, 0x7f, 0xf8, 
0x00, 0x7f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x21, 
0x04, 0x20, 0x42, 0x10, 0x41, 0x08, 0x40, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xf0, 0xaa, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x14, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x04, 0x00, 0x80, 0x44, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x01, 0x41, 0x4a, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x78, 0x78, 0x08, 0x08, 0x0f, 0x0f, 0x00, 0x00, 0x01, 0xe1, 0xe0, 0x00, 0x00, 0x3c, 
0x3c, 0x0a, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x18, 0xc6, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x40, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xf0, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x20, 0x00, 0x07, 
0xff, 0x00, 0x07, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x30, 0xc6, 
0x18, 0xc1, 0x8c, 0x61, 0x8e, 0x31, 0x80, 0x20, 0x00, 0x01, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma08nArabic3 = {0x01, 16, 0, 16, 0, 0, 16, 1032, 0xfb50, 0xfdff,
(PEGUSHORT *) Tahoma08nArabic3_offset_table, &Tahoma08nArabic4, 
(PEGUBYTE *) Tahoma08nArabic3_data_table};


ROMDATA PEGUSHORT Tahoma08nArabic2_offset_table[97] = {
0x0000,0x000d,0x001a,0x0027,0x0034,0x0041,0x004e,0x005b,0x0068,0x0075,0x0082,0x008f,0x009c,0x00a9,0x00b6,0x00c3,
0x00d0,0x00dd,0x00ea,0x00f7,0x0104,0x0111,0x011e,0x012b,0x0138,0x0145,0x0152,0x015f,0x016c,0x0179,0x0186,0x0193,
0x01a0,0x01ad,0x01ba,0x01c7,0x01d4,0x01e1,0x01ee,0x01fb,0x0208,0x0215,0x0222,0x022f,0x023c,0x0249,0x0256,0x0263,
0x0270,0x027d,0x028a,0x0297,0x02a4,0x02b1,0x02be,0x02cb,0x02d8,0x02e5,0x02f2,0x02ff,0x030c,0x0319,0x0326,0x0333,
0x0340,0x034d,0x035a,0x0367,0x0374,0x0381,0x038e,0x039b,0x03a8,0x03b5,0x03c2,0x03cf,0x03dc,0x03e9,0x03f6,0x0403,
0x0410,0x041d,0x042a,0x0437,0x0444,0x0451,0x045e,0x046b,0x0478,0x0485,0x0492,0x049f,0x04ac,0x04b9,0x04c6,0x04d3,
0x04e0};



ROMDATA PEGUBYTE Tahoma08nArabic2_data_table[2496] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 
0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 
0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 

0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 
0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 
0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma08nArabic2 = {0x01, 16, 0, 16, 0, 0, 16, 156, 0x08a0, 0x08ff,
(PEGUSHORT *) Tahoma08nArabic2_offset_table, &Tahoma08nArabic3, 
(PEGUBYTE *) Tahoma08nArabic2_data_table};


ROMDATA PEGUSHORT Tahoma08nArabic1_offset_table[49] = {
0x0000,0x000b,0x0016,0x0021,0x002c,0x0037,0x0042,0x004d,0x0055,0x005d,0x0063,0x0069,0x006e,0x007e,0x0086,0x008e,
0x0096,0x00a1,0x00ac,0x00b8,0x00c4,0x00d0,0x00d8,0x00e0,0x00e9,0x00f2,0x00fb,0x0104,0x0109,0x010e,0x011e,0x012b,
0x0138,0x0145,0x0152,0x015f,0x016c,0x0179,0x0186,0x0193,0x01a0,0x01ad,0x01ba,0x01c7,0x01d4,0x01e1,0x01ee,0x01fb,
0x0208};



ROMDATA PEGUBYTE Tahoma08nArabic1_data_table[1040] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x13, 0x02, 0xb0, 0x03, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0x80, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x0e, 0x00, 0x70, 
0x03, 0x80, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x0e, 0x00, 0x70, 0x03, 0x80, 0x1c, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x40, 0x00, 0x00, 0x00, 
0xa0, 0x20, 0x40, 0x00, 0x00, 0x00, 0x0c, 0x00, 0xc0, 0x0c, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x03, 0xc0, 0x00, 0x02, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x03, 0x00, 0x30, 0x00, 0x00, 0x00, 0x05, 0x05, 0x01, 
0xf2, 0x08, 0x01, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xe0, 0xe0, 0xe0, 0x06, 0x00, 0xc0, 0x20, 0x02, 0x00, 0x20, 0x04, 0x00, 0x08, 0x0f, 0x02, 0x00, 
0x40, 0x10, 0x00, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x00, 0x01, 0x40, 0x00, 0x05, 0x00, 0x40, 0x00, 0x01, 0x00, 0x00, 0x02, 0x08, 0x00, 0x02, 0x81, 
0x01, 0x01, 0x00, 0x09, 0x01, 0x20, 0x10, 0x01, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x42, 0x38, 0x01, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc1, 0xc1, 0x04, 0x00, 0x00, 0x01, 
0x01, 0x01, 0x00, 0x09, 0x01, 0x20, 0x0c, 0x00, 0xc0, 0x0c, 0x1c, 0x1c, 0x41, 0x24, 0x92, 0x40, 
0x40, 0x00, 0x00, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 0x80, 0x90, 0x10, 0x70, 0x70, 0x82, 0x10, 0x00, 0x09, 
0x31, 0x31, 0x31, 0x07, 0x20, 0xe4, 0x02, 0x40, 0x24, 0x02, 0x22, 0x22, 0x41, 0x20, 0x90, 0x48, 
0x42, 0x10, 0x00, 0x0a, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 0x80, 0x90, 0x11, 0x81, 0x80, 0x41, 0x08, 0x04, 0x48, 
0xc0, 0xc0, 0xc1, 0x01, 0x20, 0x24, 0x02, 0x40, 0x24, 0x02, 0x22, 0x22, 0x41, 0x20, 0x90, 0x48, 
0x41, 0x08, 0x04, 0x4a, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 0x80, 0x90, 0x12, 0x02, 0x40, 0x41, 0x09, 0x04, 0x49, 
0x01, 0x01, 0x01, 0x01, 0x20, 0x24, 0x02, 0x40, 0x24, 0x02, 0x22, 0x22, 0x41, 0x20, 0x90, 0x48, 
0x41, 0x09, 0x04, 0x4a, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 
0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x28, 0x01, 0x40, 0x0a, 0x00, 0x50, 0x02, 0x80, 0x14, 
0x00, 

0x3f, 0x87, 0xf0, 0xfe, 0x1f, 0xc3, 0xf8, 0x7f, 0x0f, 0xe2, 0x02, 0xa7, 0x9e, 0x09, 0x07, 0xb1, 
0x01, 0x01, 0x00, 0xfe, 0x1f, 0xc3, 0xfc, 0x3f, 0xc3, 0xfc, 0x5c, 0x5c, 0x3e, 0x1f, 0x0f, 0x87, 
0x81, 0x09, 0x07, 0xb3, 0x80, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x0e, 0x00, 0x70, 
0x03, 0x80, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x0e, 0x00, 0x70, 0x03, 0x80, 0x1c, 
0x00, 

0x00, 0x00, 0x00, 0x10, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x02, 0x02, 0x00, 0x3d, 0x04, 0x01, 
0x01, 0x01, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x10, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x09, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x15, 0x00, 0x80, 0x00, 0x00, 0x00, 0xa0, 0x08, 0x00, 0x01, 0x01, 0x00, 0x08, 0x11, 0x04, 0x01, 
0x09, 0x09, 0x08, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x44, 0x14, 0x00, 0x00, 0x00, 
0x02, 0x11, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x28, 0x05, 0x00, 0x00, 0x14, 0x00, 0x00, 0xf0, 0xf2, 0x14, 0x60, 0xf8, 0x00, 
0xf0, 0xf0, 0xf0, 0x00, 0x00, 0xa0, 0x00, 0x00, 0x00, 0x28, 0x80, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x60, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 


};

xFONTYY Tahoma08nArabic1 = {0x01, 16, 0, 16, 0, 0, 16, 65, 0x0750, 0x077f,
(PEGUSHORT *) Tahoma08nArabic1_offset_table, &Tahoma08nArabic2, 
(PEGUBYTE *) Tahoma08nArabic1_data_table};


ROMDATA PEGUSHORT Tahoma08nArabic_offset_table[257] = {
0x0000,0x000a,0x001e,0x002e,0x003b,0x0048,0x0055,0x0062,0x006f,0x007c,0x0089,0x0096,0x00a0,0x00a5,0x00a9,0x00b9,
0x00c1,0x00c7,0x00cc,0x00d0,0x00d8,0x00de,0x00e2,0x00ef,0x00fc,0x0109,0x0116,0x0123,0x0128,0x0135,0x0142,0x0147,
0x014d,0x015a,0x015f,0x0163,0x0166,0x016b,0x016e,0x0179,0x017c,0x0187,0x018d,0x0198,0x01a3,0x01ab,0x01b3,0x01bb,
0x01c1,0x01c7,0x01cc,0x01d1,0x01e1,0x01f1,0x0200,0x020f,0x0218,0x0221,0x0229,0x0231,0x023e,0x024b,0x0258,0x0265,
0x0272,0x0275,0x0280,0x028b,0x0294,0x029c,0x02a4,0x02ad,0x02b3,0x02b8,0x02c3,0x02ce,0x02d2,0x02d8,0x02dc,0x02e0,
0x02e3,0x02e7,0x02ec,0x02ef,0x02f3,0x02f6,0x02f9,0x02fa,0x02fe,0x0301,0x0304,0x0307,0x030a,0x030b,0x030f,0x0312,
0x031f,0x0326,0x032d,0x0334,0x033b,0x0342,0x0349,0x0350,0x0357,0x035e,0x0365,0x036c,0x0371,0x0376,0x037d,0x0388,
0x0393,0x0394,0x0398,0x039b,0x039e,0x03a1,0x03a7,0x03ac,0x03b2,0x03bd,0x03c8,0x03d3,0x03de,0x03e9,0x03f4,0x03ff,
0x040a,0x0415,0x041d,0x0425,0x042d,0x0435,0x043d,0x0445,0x044d,0x0453,0x0459,0x045f,0x0465,0x046b,0x0471,0x0477,
0x047d,0x0483,0x0488,0x048d,0x0493,0x0498,0x049f,0x04a4,0x04a9,0x04ae,0x04b3,0x04c3,0x04d3,0x04e3,0x04f2,0x0501,
0x050a,0x0512,0x051d,0x0528,0x0533,0x053e,0x0549,0x0554,0x055f,0x056a,0x0576,0x0586,0x0593,0x059c,0x05a5,0x05ae,
0x05ba,0x05c7,0x05d3,0x05df,0x05eb,0x05f7,0x05ff,0x0607,0x060f,0x0617,0x0620,0x0629,0x0632,0x063b,0x0644,0x064d,
0x0655,0x065b,0x0662,0x0669,0x0670,0x0676,0x067b,0x0680,0x0685,0x068a,0x068f,0x0694,0x0699,0x06a4,0x06b0,0x06bb,
0x06c0,0x06cb,0x06d6,0x06e2,0x06ee,0x06f1,0x06f7,0x06fe,0x0705,0x0709,0x070d,0x0710,0x0713,0x071b,0x072a,0x073a,
0x073b,0x073c,0x0740,0x0744,0x074c,0x0750,0x0753,0x0758,0x075d,0x0761,0x076c,0x076f,0x0772,0x0773,0x0777,0x077d,
0x0782,0x0789,0x0790,0x0797,0x079e,0x07a5,0x07ac,0x07b3,0x07ba,0x07c1,0x07c8,0x07d8,0x07e7,0x07ef,0x07f4,0x07fc,
0x0805};



ROMDATA PEGUBYTE Tahoma08nArabic_data_table[4112] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x03, 0x60, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x0a, 
0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 
0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x48, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 
0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x05, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0xc0, 0x0c, 0x0a, 0x60, 0x06, 0x00, 0x60, 0x06, 0x04, 
0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
0x08, 0x00, 0x01, 0x20, 0xf8, 0x00, 0x90, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x0f, 0x00, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x50, 0x40, 0x00, 
0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0xe3, 0x80, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x01, 0x40, 0x00, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x30, 0x01, 0x80, 0x18, 0x01, 0x81, 0x58, 0x00, 
0x00, 0x0a, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 
0x30, 0x00, 0x01, 0x27, 0x07, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xa4, 0x08, 0x0d, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfd, 0xc0, 0x03, 
0xff, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x10, 0x00, 0x0c, 0xf8, 0x01, 0xc0, 0x0e, 
0x00, 0x70, 0x03, 0x80, 0x00, 0x3f, 0xf1, 0xff, 0x80, 0x01, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x03, 0x80, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x03, 0x00, 0x20, 0x04, 0x00, 
0x00, 0x46, 0x0f, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x10, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x20, 0x20, 0x00, 0x00, 0x20, 0x00, 0x01, 0xc0, 0x00, 0x70, 0x00, 0x21, 0x45, 
0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 
0x00, 0x00, 0xc0, 0x80, 0xa0, 0x00, 0x0c, 0xc0, 0xcc, 0x06, 0x60, 0x66, 0x06, 0x60, 0x66, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x08, 0x00, 0x00, 0x00, 
0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 
0x38, 0x00, 0x00, 0xe7, 0xff, 0x0a, 0x05, 0x00, 0x00, 0x00, 0xe8, 0x08, 0x12, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x00, 0x20, 0x11, 0x00, 0x80, 0xe1, 0x00, 0x80, 0x88, 0x88, 0x00, 0x20, 
0x00, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x10, 0x00, 0x08, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x00, 0x08, 
0x02, 0x80, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc7, 0x00, 0x20, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x04, 0x00, 0x86, 0x29, 0x47, 0x00, 0x44, 0x82, 0x20, 0xe2, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x48, 0x24, 0x10, 0x40, 0x38, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x40, 0x00, 0x50, 0x00, 0x00, 0x80, 
0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x0a, 0x00, 0x00, 0x00, 0x01, 0x00, 0x50, 0x00, 0x01, 0x40, 0x10, 0x05, 0x00, 0x30, 0x00, 
0x00, 0x03, 0x40, 0x20, 0x10, 0x08, 0x03, 0x00, 0x34, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x04, 
0x04, 0x04, 0x04, 0x00, 0x00, 0x07, 0x00, 0x01, 0x00, 0x00, 0x02, 0x00, 0x10, 0x28, 0x00, 0x00, 
0x31, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x03, 0x0c, 0x03, 0x00, 0x00, 0x00, 0x30, 0x00, 0x6d, 0xb0, 0x00, 0x80, 
0x00, 0x10, 0xc5, 0x28, 0x18, 0x80, 0x10, 0x44, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x02, 
0x80, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x04, 0x20, 0x11, 0x00, 0x81, 0x11, 0x00, 0x80, 0x89, 0x08, 0x20, 0x20, 
0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 
0x00, 0x10, 0x20, 0x14, 0x00, 0x00, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x04, 0x00, 0x43, 0xc6, 0x88, 0x00, 0x3c, 0x44, 0x21, 0x17, 0x20, 0x00, 0x40, 
0x00, 0x00, 0x04, 0x48, 0x2e, 0x26, 0x80, 0x00, 0x38, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x14, 0x00, 0x00, 0xe0, 0x20, 0x00, 0x00, 0x50, 0x00, 0x03, 0xc0, 0x00, 0xf2, 0x80, 0x50, 0x05, 
0x07, 0x00, 0x00, 0x00, 0x00, 0x11, 0x40, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x0a, 
0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0x00, 0x0c, 0xa0, 0x20, 0x10, 0x08, 0x0c, 0x00, 0xca, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x04, 
0x04, 0x04, 0x04, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x27, 0x00, 0x38, 0x00, 0x00, 0x0a, 
0x31, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x88, 0x01, 0x00, 0x00, 0x00, 0xdf, 0x00, 0x17, 0x40, 0x01, 0x40, 
0x00, 0x08, 0x78, 0xd2, 0x21, 0x41, 0xc8, 0x84, 0x22, 0x00, 0x10, 0x00, 0x20, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x18, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x01, 0x04, 0xe8, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x08, 0x20, 0x11, 0x00, 0x81, 0x01, 0x00, 0x80, 0x8b, 0x88, 0x46, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x10, 0x1c, 0x1c, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x00, 0x0c, 
0x01, 0x80, 0x20, 0x20, 0x00, 0x80, 0x00, 0x01, 0x80, 0x30, 0xce, 0x33, 0x26, 0x72, 0x64, 0x80, 
0x02, 0x46, 0x48, 0x04, 0x00, 0x42, 0x04, 0x0c, 0x0c, 0x04, 0x44, 0x51, 0x12, 0x40, 0x01, 0x50, 
0x00, 0x01, 0x84, 0x48, 0x20, 0x77, 0xc0, 0x60, 0x28, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x05, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x0e, 0x00, 0x60, 0x0c, 0x01, 0x80, 0x30, 0x06, 0x00, 0xc0, 0x18, 0x03, 0x00, 0x80, 0x00, 
0x00, 0x08, 0x40, 0x20, 0x10, 0x08, 0x08, 0x00, 0x84, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0x04, 
0x04, 0x04, 0x04, 0x10, 0x00, 0x0f, 0x02, 0x02, 0x83, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x40, 0x14, 0xa5, 0x00, 0xc0, 0x0e, 0x29, 0x84, 0x01, 0x80, 0x30, 0x00, 0x01, 0x00, 0x00, 0x04, 
0x18, 0x00, 0x00, 0x70, 0x00, 0x58, 0x61, 0x80, 0x00, 0x31, 0x00, 0x00, 0x08, 0x80, 0x00, 0x01, 
0x00, 0x08, 0x40, 0x81, 0xa2, 0x22, 0x08, 0x8a, 0x22, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x03, 
0x80, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x24, 0x00, 0x00, 0x00, 0x20, 0x02, 0x11, 0x02, 0x09, 0x28, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x0c, 0x20, 0x11, 0x00, 0x80, 0x81, 0x00, 0x80, 0x88, 0x08, 0xe9, 0x20, 
0x00, 0x80, 0x50, 0x0a, 0x00, 0x00, 0x00, 0x08, 0x20, 0x02, 0x00, 0x00, 0x00, 0x50, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x10, 0x20, 0x20, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x00, 0x12, 
0x02, 0x41, 0xa0, 0x20, 0x00, 0x02, 0x00, 0x02, 0x40, 0x49, 0x11, 0x44, 0x48, 0x84, 0x89, 0x04, 
0x94, 0x88, 0x88, 0x04, 0x20, 0x21, 0x02, 0x02, 0x12, 0x04, 0x44, 0x51, 0x10, 0x40, 0x00, 0xe0, 
0x00, 0x02, 0x44, 0x48, 0x20, 0x08, 0x00, 0x90, 0x78, 0x04, 0x00, 0x00, 0x28, 0x02, 0x00, 0x00, 
0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x08, 0x20, 0x82, 0x08, 0x20, 0x82, 
0x0f, 0x20, 0x00, 0x00, 0x05, 0x29, 0x40, 0x08, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x10, 0x00, 0x90, 0x12, 0x02, 0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 0x80, 0x40, 0x08, 
0x00, 0x04, 0x01, 0xa0, 0xd0, 0x68, 0x04, 0x00, 0x40, 0x02, 0x00, 0x20, 0x02, 0x00, 0x20, 0x04, 
0x04, 0x04, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x02, 0x08, 0x10, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x20, 0x10, 0x12, 0x40, 0x02, 0x40, 0x48, 0x00, 0x03, 0x80, 0x20, 0x08, 
0x20, 0x88, 0x20, 0x90, 0x00, 0x68, 0x91, 0x41, 0x10, 0x42, 0x00, 0x08, 0x8d, 0x80, 0x02, 0x82, 
0x84, 0x04, 0x20, 0x40, 0xfa, 0x22, 0x08, 0x8a, 0x22, 0x00, 0x28, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x40, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x22, 0x00, 0x00, 0x00, 0x20, 0x04, 0x22, 0x04, 0x12, 0x28, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x0c, 0x20, 0x11, 0x00, 0x80, 0x41, 0x00, 0x8e, 0x89, 0x88, 0x08, 0x20, 
0x00, 0x40, 0x00, 0x00, 0x07, 0x07, 0x07, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
0x00, 0x18, 0x2c, 0x16, 0x20, 0x20, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x00, 0x12, 
0x02, 0x40, 0xa0, 0x21, 0xc4, 0x11, 0x0c, 0x02, 0x00, 0x42, 0x20, 0x88, 0x91, 0x09, 0x12, 0x49, 
0x29, 0x31, 0x08, 0x04, 0x70, 0x20, 0x81, 0x02, 0x21, 0x04, 0x28, 0x50, 0xf0, 0x80, 0x03, 0xf8, 
0x00, 0x02, 0x54, 0x48, 0x20, 0xc3, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0xc1, 0xc1, 0xc1, 0xc1, 0xc1, 0xc1, 0xc1, 0x04, 0x10, 0x41, 0x04, 0x10, 0x41, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x06, 
0x0b, 0x10, 0x00, 0x90, 0x12, 0x02, 0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 0x80, 0x30, 0x30, 
0x00, 0x03, 0x00, 0xa0, 0x50, 0x28, 0x03, 0x00, 0x30, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x04, 
0x04, 0x04, 0x04, 0x82, 0x41, 0x20, 0x90, 0x48, 0x23, 0x21, 0xc1, 0x04, 0x08, 0x10, 0x31, 0x8c, 
0x63, 0x18, 0xc6, 0x01, 0x00, 0x10, 0x02, 0x0c, 0x02, 0x00, 0x40, 0x00, 0x00, 0x00, 0x10, 0x10, 
0x41, 0x11, 0x41, 0x10, 0x00, 0x68, 0x91, 0x72, 0x20, 0x84, 0x00, 0x11, 0x0d, 0x82, 0x64, 0x40, 
0x0e, 0x04, 0x10, 0x20, 0x84, 0x11, 0x45, 0x0a, 0x1e, 0x00, 0x00, 0x00, 0x18, 0x80, 0x61, 0xc3, 
0x20, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x1e, 0x20, 0x1c, 0x00, 0x22, 0x04, 0x22, 0x04, 0x12, 0x28, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x00, 0x20, 0x11, 0x00, 0x80, 0x21, 0x00, 0x90, 0x8a, 0x49, 0x06, 0x24, 
0x04, 0xa2, 0x02, 0x40, 0x41, 0xc1, 0xc1, 0xc2, 0x08, 0x42, 0x00, 0x01, 0x00, 0x01, 0x00, 0x12, 
0x00, 0x24, 0x32, 0x19, 0x26, 0x26, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x02, 0x0e, 
0x01, 0xc1, 0xa4, 0x22, 0x24, 0x12, 0x92, 0x41, 0x88, 0x32, 0x20, 0x88, 0x91, 0x09, 0x12, 0x49, 
0x29, 0x31, 0x08, 0x04, 0x20, 0x20, 0x81, 0x04, 0x21, 0x04, 0x28, 0x88, 0x11, 0x00, 0x10, 0xe2, 
0x02, 0x01, 0xd4, 0x48, 0x21, 0x24, 0x90, 0x62, 0x02, 0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 
0x80, 0x90, 0x10, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x82, 0x08, 0x20, 0x82, 0x08, 0x20, 
0x84, 0x21, 0x04, 0x20, 0x42, 0x10, 0x80, 0x00, 0x40, 0x00, 0x40, 0x00, 0x40, 0x04, 0x80, 0x09, 
0x0c, 0x93, 0x10, 0x72, 0x0e, 0x41, 0xc8, 0x39, 0x07, 0x20, 0xe0, 0x1c, 0x03, 0x90, 0x08, 0x40, 
0x01, 0x00, 0x81, 0xa0, 0xd0, 0x69, 0x00, 0x90, 0x08, 0x80, 0x48, 0x04, 0x80, 0x48, 0x04, 0x84, 
0x84, 0x84, 0x84, 0x82, 0x41, 0x20, 0x90, 0x48, 0x24, 0x90, 0x72, 0x8a, 0x14, 0x28, 0x4a, 0x52, 
0x94, 0xa5, 0x29, 0x20, 0xc2, 0x0c, 0x41, 0x92, 0x41, 0x88, 0x30, 0x10, 0x01, 0x00, 0x28, 0x10, 
0x41, 0x11, 0x41, 0x10, 0x00, 0x58, 0x61, 0xb2, 0x20, 0x84, 0x00, 0x11, 0x0f, 0x82, 0x64, 0x21, 
0x04, 0x04, 0x10, 0x20, 0x44, 0x10, 0x85, 0x11, 0x02, 0x00, 0x02, 0x00, 0x24, 0x98, 0x82, 0x24, 
0x90, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x02, 0x40, 0x24, 0x00, 0x1c, 0x04, 0x22, 0x04, 0x12, 0x28, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x00, 0x20, 0x11, 0x00, 0x88, 0x01, 0x00, 0x90, 0x8a, 0x49, 0x01, 0x24, 
0x05, 0x12, 0x02, 0x40, 0x46, 0x06, 0x06, 0x01, 0x04, 0x21, 0x00, 0x89, 0x00, 0x89, 0x00, 0x22, 
0x00, 0x44, 0x22, 0x11, 0x18, 0x18, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x02, 0x02, 
0x40, 0x48, 0x24, 0x22, 0x24, 0x14, 0x52, 0x40, 0x48, 0x0a, 0x20, 0x88, 0x91, 0x09, 0x12, 0x49, 
0x29, 0x31, 0x08, 0x04, 0x00, 0x20, 0x81, 0x04, 0x21, 0x04, 0x28, 0x88, 0x11, 0x20, 0x21, 0x52, 
0x02, 0x40, 0x54, 0x48, 0x21, 0x24, 0x90, 0x12, 0x02, 0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 
0x80, 0x90, 0x11, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x41, 0x04, 0x10, 0x41, 0x04, 0x10, 
0x42, 0x10, 0x82, 0x10, 0x21, 0x08, 0x40, 0x22, 0x40, 0x22, 0x40, 0x22, 0x40, 0x08, 0x80, 0x11, 
0x08, 0x8c, 0x10, 0x12, 0x02, 0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 0x80, 0x90, 0x08, 0x7f, 
0xfd, 0x00, 0x88, 0x24, 0x12, 0x09, 0x00, 0x90, 0x08, 0x80, 0x48, 0x04, 0x80, 0x48, 0x04, 0x84, 
0x84, 0x84, 0x84, 0x82, 0x41, 0x20, 0x90, 0x48, 0x24, 0x91, 0x84, 0x51, 0x22, 0x44, 0x4a, 0x52, 
0x94, 0xa5, 0x29, 0x20, 0x2e, 0x02, 0x40, 0x52, 0x40, 0x48, 0x08, 0x28, 0x02, 0x80, 0x44, 0x10, 
0x41, 0x11, 0x41, 0x08, 0x00, 0x88, 0x01, 0x32, 0x20, 0x84, 0x00, 0x11, 0x18, 0xc2, 0x64, 0x10, 
0x80, 0x04, 0x10, 0x20, 0x44, 0x13, 0x05, 0x11, 0x02, 0x01, 0x12, 0x00, 0x44, 0x60, 0x82, 0x24, 
0x90, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x05, 0x40, 0x02, 
0x01, 0x10, 0x08, 0x04, 0x60, 0x22, 0x00, 0x20, 0x02, 0x11, 0x02, 0x09, 0x28, 0x01, 0x40, 0x0a, 
0x00, 0x50, 0x02, 0x80, 0x0c, 0x20, 0x11, 0x00, 0x80, 0x21, 0x00, 0x8e, 0x89, 0xc9, 0x01, 0x24, 
0x05, 0x12, 0x02, 0x40, 0x48, 0x08, 0x08, 0x01, 0x04, 0x21, 0x20, 0x89, 0x20, 0x89, 0x21, 0x44, 
0x42, 0x88, 0x44, 0x22, 0x20, 0x20, 0x50, 0x02, 0x80, 0x14, 0x00, 0xa0, 0x05, 0x00, 0x02, 0x02, 
0x40, 0x48, 0x24, 0x22, 0x24, 0x14, 0x4e, 0x40, 0x48, 0x09, 0x11, 0x44, 0x48, 0x84, 0x89, 0x04, 
0x94, 0x88, 0x88, 0x04, 0x00, 0x20, 0x81, 0x08, 0x21, 0x02, 0x10, 0x88, 0x0a, 0x73, 0x30, 0x42, 
0x02, 0x40, 0x84, 0x48, 0x20, 0xe3, 0x90, 0x12, 0x02, 0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x04, 
0x80, 0x90, 0x12, 0x02, 0x02, 0x02, 0x42, 0x02, 0x02, 0xa0, 0x41, 0x04, 0x10, 0x41, 0x04, 0x10, 
0x42, 0x10, 0x82, 0x10, 0x21, 0x08, 0x48, 0x22, 0x48, 0x22, 0x48, 0x22, 0x48, 0x51, 0x10, 0xa2, 
0x11, 0x10, 0x10, 0x12, 0x02, 0x40, 0x48, 0x09, 0x01, 0x20, 0x24, 0x08, 0x81, 0x10, 0x08, 0x00, 
0x05, 0x00, 0x88, 0x24, 0x12, 0x09, 0x00, 0x90, 0x08, 0x80, 0x48, 0x04, 0x80, 0x48, 0x04, 0x84, 
0x84, 0x84, 0x84, 0x82, 0x41, 0x20, 0x90, 0x48, 0x24, 0x92, 0xa4, 0x51, 0x22, 0x44, 0x39, 0xce, 
0x73, 0x9c, 0xe7, 0x20, 0x22, 0x04, 0x40, 0x4e, 0x40, 0x48, 0x08, 0x40, 0x04, 0x00, 0x44, 0x08, 
0x20, 0x88, 0x20, 0x86, 0x03, 0x0c, 0x03, 0x01, 0x10, 0x42, 0x00, 0x08, 0xa7, 0x20, 0x02, 0x10, 
0x80, 0x04, 0x10, 0x20, 0x44, 0x94, 0x02, 0x11, 0x01, 0x41, 0x12, 0x42, 0x88, 0x80, 0x62, 0x24, 
0x90, 

0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x03, 0x87, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfd, 0xc0, 0x03, 
0xff, 0x1f, 0xfb, 0xf8, 0x61, 0x9f, 0xff, 0xaf, 0x00, 0x00, 0x01, 0x04, 0xf8, 0x01, 0xc0, 0x0e, 
0x00, 0x70, 0x03, 0x80, 0x0c, 0x3f, 0xf1, 0xff, 0x94, 0x21, 0xff, 0x90, 0x88, 0x48, 0xfe, 0x23, 
0xf8, 0xe1, 0xfc, 0x3f, 0x89, 0x08, 0x08, 0x1e, 0x78, 0x21, 0x20, 0xf6, 0x20, 0xf6, 0x21, 0xf8, 
0x43, 0xf1, 0xf8, 0xfc, 0x20, 0x20, 0x70, 0x03, 0x80, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x39, 0xfc, 
0x40, 0x87, 0xc3, 0xc5, 0xc3, 0xe3, 0x82, 0x3f, 0x87, 0xf0, 0xce, 0x33, 0x26, 0x72, 0x64, 0x80, 
0x02, 0x46, 0x4f, 0xfc, 0x00, 0x20, 0x81, 0x0f, 0xde, 0x02, 0x11, 0x04, 0x0c, 0x23, 0x30, 0x01, 
0xfc, 0x21, 0x84, 0x48, 0x20, 0x20, 0x8f, 0xe1, 0xfc, 0x3f, 0x87, 0xf0, 0xfe, 0x1f, 0xc3, 0xf8, 
0x7f, 0x0f, 0xe2, 0x02, 0x02, 0xa2, 0x02, 0x02, 0xa2, 0x07, 0x9e, 0x79, 0xe7, 0x9e, 0x79, 0xe7, 
0x82, 0x10, 0x82, 0x10, 0xa1, 0x08, 0x48, 0x3d, 0x88, 0x3d, 0x88, 0x3d, 0x88, 0x7e, 0x10, 0xfc, 
0x7e, 0x10, 0x0f, 0xe1, 0xfc, 0x3f, 0x87, 0xf0, 0xfe, 0x1f, 0xc2, 0x18, 0x43, 0x0f, 0xf3, 0xff, 
0xf8, 0xff, 0x07, 0xc3, 0xe1, 0xf0, 0xff, 0x0f, 0xf0, 0x7f, 0x87, 0xf8, 0x7f, 0x87, 0xf8, 0x78, 
0x78, 0x78, 0x78, 0x7c, 0x3e, 0x1f, 0x0f, 0x87, 0xcf, 0xe2, 0x43, 0x8e, 0x1c, 0x38, 0x48, 0x42, 
0x10, 0x84, 0x21, 0x1f, 0xc1, 0xf8, 0x3f, 0x82, 0x3f, 0x87, 0xf1, 0x80, 0x18, 0x03, 0xb8, 0x04, 
0x18, 0x00, 0x00, 0x67, 0xff, 0x0a, 0x05, 0x00, 0x00, 0x31, 0x00, 0x00, 0x7f, 0xf0, 0x01, 0xe0, 
0x80, 0x04, 0x10, 0x20, 0x43, 0x64, 0x02, 0x20, 0x81, 0x41, 0xec, 0x43, 0xf0, 0x90, 0x85, 0xcf, 
0xe0, 

0x01, 0x26, 0x40, 0x7a, 0x40, 0x00, 0x02, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x31, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x08, 0x08, 0x00, 0x00, 0x21, 0x20, 0x80, 0x20, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x00, 0x00, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x3f, 0x00, 0x00, 0x04, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 
0x00, 0x1e, 0x00, 0x0c, 0x00, 0x20, 0x80, 0x00, 0x00, 0x00, 0x00, 0x80, 0x28, 0x00, 0x00, 0xa0, 
0x00, 0x02, 0x82, 0x02, 0x02, 0x02, 0x42, 0x02, 0x42, 0xa0, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x10, 0xc2, 0x10, 0x21, 0x08, 0x48, 0x20, 0x08, 0x25, 0x08, 0x25, 0x08, 0x40, 0x10, 0x80, 
0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x01, 0xe0, 0x3c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0xab, 0xc2, 
0x10, 0x84, 0x21, 0x00, 0x00, 0x00, 0x00, 0x02, 0x04, 0x01, 0x42, 0x00, 0x20, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0x07, 0x0f, 0xff, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x00, 0x42, 0x00, 0x80, 0xa5, 0x40, 
0x00, 

0x01, 0x29, 0x43, 0x8a, 0x20, 0x00, 0x14, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x02, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x00, 0x04, 0x04, 0x04, 0x00, 0x00, 0x42, 0x20, 0x80, 0x20, 0x80, 0x21, 0x00, 
0x42, 0x00, 0x00, 0x00, 0x21, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x00, 0x01, 0x40, 0x00, 0x30, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x04, 0x10, 0x80, 0x14, 0x00, 0x00, 
0x04, 0x21, 0xa5, 0x2a, 0x52, 0x10, 0x88, 0x28, 0x08, 0x22, 0x08, 0x22, 0x08, 0x45, 0x10, 0x80, 
0x00, 0x10, 0x80, 0x00, 0x04, 0x04, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x10, 0x10, 0x00, 0x00, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x50, 0x84, 
0x21, 0x08, 0x42, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x81, 0xff, 0xdf, 0xfc, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x10, 0x42, 0x20, 0x84, 0xa5, 0x40, 
0x00, 

0xff, 0xf9, 0xbc, 0x11, 0xff, 0xfb, 0xef, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x04, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x1c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0xc3, 0xc3, 0xc0, 0x01, 0x8c, 0x1f, 0x00, 0x1f, 0x00, 0x1e, 0x00, 
0x3c, 0x00, 0x00, 0x00, 0x1e, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1c, 0x01, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x80, 0xf0, 0xf0, 0xf0, 0xf0, 0xf0, 0xf0, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x18, 0xc6, 0x58, 0xc5, 0x8c, 0x63, 0x07, 0xc0, 0x07, 0xc0, 0x07, 0xc0, 0x07, 0x80, 0x0f, 0x00, 
0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x63, 0x18, 
0xc6, 0x31, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x18, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x3c, 0x00, 0x78, 0x08, 0x00, 
0x00, 


};

xFONTYY Tahoma08nArabic_Font = {0x01, 16, 0, 16, 0, 0, 16, 257, 0x0600, 0x06ff,
(PEGUSHORT *) Tahoma08nArabic_offset_table, &Tahoma08nArabic1, 
(PEGUBYTE *) Tahoma08nArabic_data_table};


