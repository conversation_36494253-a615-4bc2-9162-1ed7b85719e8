/*...........................................................................*/
/*.                  File Name : SYSPWM.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.06                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSPWM_H__
#define  __SYSPWM_H__

//=============================================================================
#if defined(__POLLUX__)
    #define  PWM_LCD_CHANNEL                                      0
    #define  PWM_KEY_CHANNEL                                      1
    #define  PWM_BUZ_CHANNEL                                      2

    #define  PWM_CLK_DIV                                         64       // 44
//  #define  PWM_IN_FREQUENCY    (CPU_PLL0_FREQUENCY / PWM_CLK_DIV)       // 12.00 MHz
    #define  PWM_IN_FREQUENCY    (CPU_PLL0_FREQUENCY / PWM_CLK_DIV)       //  8.25 MHz

//  #define  PWM0_OUT_FREQUENCY                              600000       // 600.0 KHz
    #define  PWM0_OUT_FREQUENCY                              275000       // 275.0 KHz
    #define  PWM0_PRE_SCALER     (PWM_IN_FREQUENCY / PWM0_OUT_FREQUENCY)  // 30 // 20

//  #define  PWM1_OUT_FREQUENCY                              600000       // 600 KHz
    #define  PWM1_OUT_FREQUENCY                              275000       // 275.0 KHz
//  #define  PWM1_OUT_FREQUENCY                             8250000
    #define  PWM1_PRE_SCALER     (PWM_IN_FREQUENCY / PWM1_OUT_FREQUENCY)  // 30 // 20

//  #define  PWM2_OUT_FREQUENCY                              600000       // 600 KHz
    #define  PWM2_OUT_FREQUENCY                              275000       // 275.0 KHz
    #define  PWM2_PRE_SCALER     (PWM_IN_FREQUENCY / PWM2_OUT_FREQUENCY)  // 30 // 20
#else                           // SPICA
    #define  PWM_LCD_CHANNEL                                      0
    #define  PWM_KEY_CHANNEL                                      1
    #define  PWM_BUZ_CHANNEL                                      2

    #define  PWM_CLK_DIV                                         16

    #define  PWM_IN_FREQUENCY    (CPU_PLL1_FREQUENCY / PWM_CLK_DIV)       //  8.25 MHz

    #define  PWM0_OUT_FREQUENCY                              275000       // 275.0 KHz
    #define  PWM0_PRE_SCALER     (PWM_IN_FREQUENCY / PWM0_OUT_FREQUENCY)

    #define  PWM1_OUT_FREQUENCY                              275000       // 275.0 KHz
    #define  PWM1_PRE_SCALER     (PWM_IN_FREQUENCY / PWM1_OUT_FREQUENCY)

    #define  PWM2_OUT_FREQUENCY                              275000       // 275.0 KHz
    #define  PWM2_PRE_SCALER     (PWM_IN_FREQUENCY / PWM2_OUT_FREQUENCY)
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  PWM_PCLKMODE_DYNAMIC            0    // PCLK is provided only when CPU has access to registers of this module.
    #define  PWM_PCLKMODE_ALWAYS             1    // PCLK is always provided for this module.

    #define  PWM_CLKSRCSEL_PLL0              0
    #define  PWM_CLKSRCSEL_PLL1              1
    #define  PWM_CLKSRCSEL_PLL2              2
#else                           // SPICA
    #define  PWM_PCLKMODE_DYNAMIC            0    // PCLK is provided only when CPU has access to registers of this module.
    #define  PWM_PCLKMODE_ALWAYS             1    // PCLK is always provided for this module.

    #define  PWM_CLKSRCSEL_PLL0              0
    #define  PWM_CLKSRCSEL_PLL1              1
#endif
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SysInitPWM(int nLcdOffMode);
void  SysSetPWMClockPClkMode(DWORD dPclkMode);
void  SysSetPWMClockDivisorEnable(int nDisableEnableMode);
void  SysSetPWMClockSource(DWORD dClkSrc);
void  SysSetPWMClockDivisor(DWORD dDivisor);
void  SysSetPWMPreScale(int nIndex,HWORD wPreScaler);
void  SysSetPWMPolarity(int nIndex,int nByPassMode);
void  SysSetPWMPeriod(int nIndex,HWORD wPeriod);
HWORD SysGetPWMPeriod(int nIndex);
void  SysSetPWMDutyCycle(int nIndex,HWORD wDuty);
HWORD SysGetPWMDutyCycle(int nIndex);

#ifdef  __cplusplus
}
#endif

#endif

