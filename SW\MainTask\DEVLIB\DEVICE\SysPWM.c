/*...........................................................................*/
/*.                  File Name : SYSPWM.C                                    .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.06                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysPWM.h"

#include <string.h>

//=============================================================================
static xSYS_PWM *G_pSysPWM = (xSYS_PWM *)PWM_PHSY_BASE_ADDR;
//=============================================================================

void  SysInitPWM(int nLcdOffMode)
{
#if defined(__N500_MODEL__)
	//    HWORD dLcdFreq =   269;
	//    HWORD dLcdFreq = 27000;
	HWORD dLcdFreq =  8250;
	HWORD dLcdDvdr = PWM0_OUT_FREQUENCY / dLcdFreq;
#else
	HWORD dLcdFreq = 269;
	HWORD dLcdDvdr = PWM0_OUT_FREQUENCY / dLcdFreq;
#endif
	HWORD dKeyFreq =  99;
	HWORD dKeyDvdr = PWM1_OUT_FREQUENCY / dKeyFreq;
	HWORD dBuzFreq = 3000;
	//HWORD dBuzDvdr = PWM2_OUT_FREQUENCY / dBuzFreq;
	DWORD dPwm0Out = PWM0_OUT_FREQUENCY;
	DWORD dPwm0Pre = PWM0_PRE_SCALER;
	int   nNavisType = SysGetNavisModelType();

	if (nNavisType != NAVIS_TYPE_700)
	{
		dLcdFreq = 22000;
		dLcdDvdr = PWM0_OUT_FREQUENCY / dLcdFreq;
	}

	//    if (nNavisType == NAVIS_TYPE_3800)
	if (nNavisType != NAVIS_TYPE_700)
	{
		dPwm0Out = PWM_IN_FREQUENCY / 4;
		//        dLcdFreq = 160000;                    // 160 KHz
		//        dLcdDvdr = dPwm0Out / dLcdFreq;
		dLcdDvdr = dPwm0Out / 160000;

		if ((nNavisType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode()) ||
			(nNavisType == NAVIS_TYPE_800  && SysGetNavis5100AModelMode()) ||
			(nNavisType == NAVIS_TYPE_3800 && SysIsLedBackLight5100()))
			dLcdDvdr = dPwm0Out / 1600;

		dPwm0Pre = (PWM_IN_FREQUENCY / dPwm0Out);
	}

	if (nNavisType == NAVIS_TYPE_3800 && SysCheckLCD3800Mitsubishi())
	{
		dPwm0Out = PWM_IN_FREQUENCY / 4;
		//        dLcdFreq = 400;                       // 400 Hz
		//        dLcdDvdr = dPwm0Out / dLcdFreq;
		dLcdDvdr = dPwm0Out /  400;
		if (dLcdDvdr > 1023)
		dLcdDvdr = 1023;

		//        dPwm0Pre = (PWM_IN_FREQUENCY / dPwm0Out);
		//        dPwm0Pre =  20;                       // 400 Hz
		dPwm0Pre =   4;                       //2001 Hz
		//        dPwm0Pre =   1;                       //8000 Hz
	}


	SysSetPWMClockSource(PWM_CLKSRCSEL_PLL1);
	SysSetPWMClockDivisor(PWM_CLK_DIV);
	SysSetPWMClockPClkMode(PWM_PCLKMODE_ALWAYS);
	SysSetPWMClockDivisorEnable(1);

	//    SysSetPWMPreScale(PWM_LCD_CHANNEL,PWM0_PRE_SCALER);
	SysSetPWMPreScale(PWM_LCD_CHANNEL,dPwm0Pre);
	SysSetPWMPeriod(PWM_LCD_CHANNEL,dLcdDvdr);            // 5.6" LCD PWM Freq = 100 -- 300 Hz
	SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr / 2);     // dutycycle/period = 50%
	
	if (nLcdOffMode)
	{
		if (nNavisType == NAVIS_TYPE_700)
			SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
		else
			SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr + 1); // L=100%
	}
	else
	{
		if (nNavisType == NAVIS_TYPE_700)
			SysSetPWMDutyCycle(PWM_LCD_CHANNEL,dLcdDvdr + 1); // L=100%
		else
			SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
	}
	
	if (nNavisType == NAVIS_TYPE_3800 && SysCheckLCD3800Mitsubishi())
		SysSetPWMPolarity(PWM_LCD_CHANNEL,0);
	else
		SysSetPWMPolarity(PWM_LCD_CHANNEL,1);

	SysSetPWMPreScale(PWM_KEY_CHANNEL,PWM1_PRE_SCALER);
	SysSetPWMPeriod(PWM_KEY_CHANNEL,dKeyDvdr);
	SysSetPWMDutyCycle(PWM_KEY_CHANNEL,dKeyDvdr / 2);     // dutycycle/period = 50%
	SysSetPWMPolarity(PWM_KEY_CHANNEL,1);

	//    SysSetPWMPreScale(PWM_BUZ_CHANNEL,PWM2_PRE_SCALER);
	//    SysSetPWMPeriod(PWM_BUZ_CHANNEL,dBuzDvdr);
	//    SysSetPWMDutyCycle(PWM_BUZ_CHANNEL,dBuzDvdr / 2);     // dutycycle/period = 50%
	//    SysSetPWMPolarity(PWM_BUZ_CHANNEL,1);
}
void  SysSetPWMClockPClkMode(DWORD dPclkMode)
{
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKENB;

      dTempX = dTempX & ~(1 <<  3);
      dTempX = dTempX |  (dPclkMode <<  3);

      G_pSysPWM->dCLKENB = dTempX;
}
void  SysSetPWMClockDivisorEnable(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKENB;

      if (nDisableEnableMode)
          dTempX = dTempX |  (1 <<  2);
      else
          dTempX = dTempX & ~(1 <<  2);

      G_pSysPWM->dCLKENB = dTempX;
}
void  SysSetPWMClockSource(DWORD dClkSrc)
{
      DWORD dTempX;

      dTempX = G_pSysPWM->dCLKGEN;

      dTempX = dTempX & ~(7 <<  2);
      dTempX = dTempX |  (dClkSrc <<  2);

      G_pSysPWM->dCLKGEN = dTempX;
}
void  SysSetPWMClockDivisor(DWORD dDivisor)
{
      DWORD dTempX;

      if (dDivisor > 0)
          dDivisor--;

      dTempX = G_pSysPWM->dCLKGEN;

      dTempX = dTempX & ~(0x3f <<  5);
      dTempX = dTempX |  (dDivisor <<  5);

      G_pSysPWM->dCLKGEN = dTempX;
}
void  SysSetPWMPreScale(int nIndex,HWORD wPreScaler)
{
      HWORD wTempX;
      int   nChannel,nShift;

      nChannel = (nIndex / 2);
      nShift   = (nIndex & 1) * 8;

      wTempX = G_pSysPWM->xLayer[nChannel].wPREPOL;

      wTempX = wTempX & ~(0x7f << nShift);
      wTempX = wTempX |  ((wPreScaler - 1) << nShift);

      G_pSysPWM->xLayer[nChannel].wPREPOL = wTempX;
}
void  SysSetPWMPolarity(int nIndex,int nByPassMode)
{
      HWORD wTempX;
      int   nChannel,nShift;

      nChannel = (nIndex / 2);

      if (nIndex & 1)
          nShift = 15;
      else
          nShift =  7;

      wTempX = G_pSysPWM->xLayer[nChannel].wPREPOL;

      wTempX = wTempX & ~(1 << nShift);

      if (nByPassMode)
          wTempX = wTempX |  (1 << nShift);
      else
          wTempX = wTempX & ~(1 << nShift);

      G_pSysPWM->xLayer[nChannel].wPREPOL = wTempX;
}
void  SysSetPWMPeriod(int nIndex,HWORD wPeriod)
{
      int   nChannel;

      nChannel = (nIndex / 2);

      G_pSysPWM->xLayer[nChannel].wPERIOD[nIndex % 2] = wPeriod;
}
HWORD SysGetPWMPeriod(int nIndex)
{
      int   nChannel;

      nChannel = (nIndex / 2);

      return(G_pSysPWM->xLayer[nChannel].wPERIOD[nIndex % 2]);
}
void  SysSetPWMDutyCycle(int nIndex,HWORD wDuty)
{
      int   nChannel;

      nChannel = (nIndex / 2);

      G_pSysPWM->xLayer[nChannel].wDUTY[nIndex % 2] = wDuty;
}
HWORD SysGetPWMDutyCycle(int nIndex)
{
      int   nChannel;

      nChannel = (nIndex / 2);

      return(G_pSysPWM->xLayer[nChannel].wDUTY[nIndex % 2]);
}

