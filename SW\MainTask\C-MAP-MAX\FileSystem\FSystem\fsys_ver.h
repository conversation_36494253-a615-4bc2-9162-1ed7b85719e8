/*##################################################################
  FILE    : FSYS_VER.H

  USE     :	File System version File.
  PROJECT : C-Map File System.

  AUTHOR  : SiS[030604].
  UPDATED : GE[080110].
  ##################################################################


*/


#ifndef __FSYS_VER__
	#define __FSYS_VER__


/************************************************************************
  Constants definition section.
 ************************************************************************/


#define FSYS_VERSION         		3   	// Version number.
#define FSYS_SUBVERSION      		26		// Sub version number.
#define FSYS_BUILD           		1 		// Build number.

#define FSYS_STATUS         		'B'  	// 'R' - Released.
											// 'A' - Alpha (not in SSafe/Documented).
                                   			// 'B' - Beta version.
											// 'X' - Critical bug inside.
											// 'M' - Modified.

#define FSYS_DAY            		23
#define FSYS_MONTH          		9
#define FSYS_YEAR         			2008



/************************************************************************
  END of Code.
 ************************************************************************/

#endif /* #ifndef __FSYS_VER__ */
