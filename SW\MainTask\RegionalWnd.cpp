#include <stdio.h>
#include "RegionalWnd.hpp"
#include "Regional.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "DblList.h"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Font.h"

#ifdef _EN_DBG_MSG_
#include "Uart.hpp"
extern cUART    *G_pUart3;
#endif

#define RGN_LIST_X_POS		10
#define RGN_LIST_Y_POS		54

#define RGN_LIST_W			580
#define RGN_LIST_H			370

#define RGN_LIST_ITEM_H		37
#define RGN_LIST_COL_W		145

extern CDocMgr             *g_pDocMgr;

/*********************************************************************************************************/
// Name		: CRegionalWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CRegionalWnd::CRegionalWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID), m_nCurSel(0)
{
	m_bInitSel = FALSE;
	m_nCurSelRegionDataIdx = -1;
	m_bShowDeftRegion = FALSE;
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CRegionalWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	CRegionalArray *pRegionDataArray = g_pDocMgr->GetRegionalArray();

	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
			m_bInitSel = FALSE;
#ifdef _EN_DBG_MSG_
			G_pUart3->OutputDbgMsg("[CRegionalWnd::OnKeyEvent:KBD_SCAN_CODE_UP]ShowDeflt=%d, m_nCurSel=%d\r\n",m_bShowDeftRegion,m_nCurSel);
#endif
			if( m_nCurSel > 0 )
			{
				m_nCurSel--;
			}
			DrawWnd();
			break;
			
		case KBD_SCAN_CODE_DOWN:
			m_bInitSel = FALSE;
#ifdef _EN_DBG_MSG_			
			G_pUart3->OutputDbgMsg("[CRegionalWnd::OnKeyEvent:KBD_SCAN_CODE_DOWN]ShowDeflt=%d, m_nCurSel=%d\r\n",m_bShowDeftRegion,m_nCurSel);
#endif
			if(m_bShowDeftRegion == TRUE)
			{
				if( m_nCurSel < pRegionDataArray->size())
				{
					m_nCurSel++;
				}
			}
			else
			{
				if( m_nCurSel < (pRegionDataArray->size()-1))
				{
					m_nCurSel++;
				}
			}
			DrawWnd();
			break;
			
		default:
			break;
	}
}

/*********************************************************************************************************/
// Name		: OnActivate
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CRegionalWnd::OnActivate()
{
	g_pDocMgr->SetStopUpdataROSData(FALSE);
#ifdef _EN_DBG_MSG_
	G_pUart3->OutputDbgMsg("[OnActivate::OnActivate]\r\n");
#endif
}

/*********************************************************************************************************/
// Name		: DrawRegionList
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CRegionalWnd::DrawWnd(BOOL bRedraw/*TRUE*/)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	int nYOffset = 0;
	HWORD *pUniCodeStr = NULL;
	int nLangMode = g_pDocMgr->GetLangMode();
	CRegionalArray *pRegionDataArray = g_pDocMgr->GetRegionalArray();

#if 0
	g_pDocMgr->RemoveAllRegionalArea();
	g_pDocMgr->RequestACA();
#endif

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pFont = &MyriadPro30bRus;
			nYOffset = 0;
			break;	

		default:
			pFont = &MyriadPro30bEng;
			nYOffset = 2;
			break;
	}

	CWnd::DrawWnd(bRedraw);

	if( bRedraw )
	{
		m_pScreen->Rect(RGN_LIST_X_POS,RGN_LIST_Y_POS,RGN_LIST_X_POS + RGN_LIST_W - 1,RGN_LIST_Y_POS + RGN_LIST_ITEM_H -1,COLORSCHEME[m_nScheme].crFore);
		m_pScreen->Line(RGN_LIST_X_POS + RGN_LIST_COL_W,   RGN_LIST_Y_POS, RGN_LIST_X_POS + RGN_LIST_COL_W, RGN_LIST_Y_POS + RGN_LIST_ITEM_H -1, COLORSCHEME[m_nScheme].crFore);
		m_pScreen->Line(RGN_LIST_X_POS + RGN_LIST_COL_W*2, RGN_LIST_Y_POS, RGN_LIST_X_POS + RGN_LIST_COL_W*2, RGN_LIST_Y_POS + RGN_LIST_ITEM_H -1, COLORSCHEME[m_nScheme].crFore);
		m_pScreen->Line(RGN_LIST_X_POS + RGN_LIST_COL_W*3, RGN_LIST_Y_POS, RGN_LIST_X_POS + RGN_LIST_COL_W*3, RGN_LIST_Y_POS + RGN_LIST_ITEM_H -1, COLORSCHEME[m_nScheme].crFore);

		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_LAT_NE[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_LAT_NE[nLangMode];
		
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_LON_NE[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_LON_NE[nLangMode];
		
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_LAT_SW[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_LAT_SW[nLangMode];
		
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W*2 + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)STR_LON_SW[nLangMode],nLangMode);
		pUniCodeStr = (HWORD *)STR_LON_SW[nLangMode];
		
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W*3 + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		m_pScreen->SetFont(pOldFont);

	}

	DrawRegionList();
		
	m_pScreen->Rect(RGN_LIST_X_POS,RGN_LIST_Y_POS,RGN_LIST_X_POS + RGN_LIST_W - 1,RGN_LIST_Y_POS + RGN_LIST_H -1,COLORSCHEME[m_nScheme].crFore);

	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
	DrawButton(1, (BYTE *)FK_CREATE[nLangMode]);

	if( pRegionDataArray->size() > 0 )
	{
		DrawButton(2, (BYTE *)FK_VIEW[nLangMode]);
		EraseButton(3);
	}
	else
	{
		EraseButton(2);
		EraseButton(3);
	}
}

/*********************************************************************************************************/
// Name		: DrawRegionList
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CRegionalWnd::DrawRegionList()
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	int nYOffset = 0;
	HWORD *pUniCodeStr = NULL;
	
	int nLangMode = g_pDocMgr->GetLangMode();
	int i = 0;
	BOOL bFindUsedRegion = FALSE;
	CRegional oReg;
	CRegionalArray *pRegionDataArray = g_pDocMgr->GetRegionalArray();
	COLORT backClr;
	COLORT txtClr;

	int   nPos = 46;
	BYTE  szLatNE[12], szLonNE[12];
	BYTE  szLatSW[12], szLonSW[12];
	BYTE  szTemp[60];
	
	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pFont = &MyriadPro28bRus;
			nYOffset = 0;
			break;	

		default:
			pFont = &MyriadPro28bEng;
			nYOffset = 2;
			break;
	}


	if(	pRegionDataArray->size() <= 0)
	{
		m_nCurSel = 0;
		m_bShowDeftRegion = TRUE;
	}
	else if(pRegionDataArray->size() >= MAX_USER_REGION_NUM)
	{
		m_bShowDeftRegion = FALSE;
		if(m_nCurSel > (MAX_USER_REGION_NUM-1))
		{
			m_nCurSel = (MAX_USER_REGION_NUM-1); 
		}
	}
	else
	{
		m_bShowDeftRegion = TRUE;

		if(m_nCurSel > pRegionDataArray->size())
		{
			m_nCurSel = pRegionDataArray->size(); 
		}
	}
	
#ifdef _EN_DBG_MSG_
	G_pUart3->OutputDbgMsg("[DrawRegionList]Size=%d,CurSel=%d,ShowDeft=%d, Init=%d\r\n\r\n",pRegionDataArray->size(),m_nCurSel,m_bShowDeftRegion,m_bInitSel);
#endif

	if(m_bInitSel == TRUE)
	{
		for(i=0; i<pRegionDataArray->size(); i++)
		{
			oReg = (*pRegionDataArray)[i];
			if(oReg.GetUseFlag())
			{
				m_nCurSel = i;
				bFindUsedRegion = TRUE;
			}
		}

		if((bFindUsedRegion == FALSE) && (m_bShowDeftRegion == TRUE))
		{
			m_nCurSel = pRegionDataArray->size();
		}
	}
	
	for(i=0; i<pRegionDataArray->size(); i++)
	{
		oReg = (*pRegionDataArray)[i];

		oReg.GetNEPosition(szLatNE, szLonNE);
		oReg.GetSWPosition(szLatSW, szLonSW);

#if 0
		if(m_bInitSel == TRUE)
		{
			if(oReg.GetUseFlag())
			{
				m_nCurSel = i;
				backClr = COLORSCHEME[m_nScheme].crFore;
				txtClr = COLORSCHEME[m_nScheme].crBack;
				bFindUsedRegion = TRUE;
			}
			else
			{
				backClr = COLORSCHEME[m_nScheme].crBack;
				txtClr = COLORSCHEME[m_nScheme].crFore;
			}
		}
		else
#endif			
		{
			if(m_nCurSel == i)
			{
				backClr = COLORSCHEME[m_nScheme].crFore;
				txtClr = COLORSCHEME[m_nScheme].crBack;

			}
			else
			{
				backClr = COLORSCHEME[m_nScheme].crBack;
				txtClr = COLORSCHEME[m_nScheme].crFore;
			}
		}

#ifdef _EN_DBG_MSG_
		//G_pUart3->OutputDbgMsg("1.[RegionData]Idx=%d UseFlag=%d\r\n",oReg.GetIndexNum(),oReg.GetUseFlag());
		//G_pUart3->OutputDbgMsg("2.[RegionData]LatNE=%s, LonNE=%s\r\n",szLatNE,szLonNE);
		//G_pUart3->OutputDbgMsg("3.[RegionData]LatSW=%s, LonSW=%s\r\n",szLatSW,szLonSW);
		//G_pUart3->OutputDbgMsg("\r\n");
#endif
		m_pScreen->FillRect(RGN_LIST_X_POS,RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(i+1),RGN_LIST_X_POS+RGN_LIST_W-1,RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(i+1) + RGN_LIST_ITEM_H -1,backClr);
		

		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		sprintf((char *)szTemp, "%10s", szLatNE);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(i+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		sprintf((char *)szTemp, "%10s", szLonNE);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(i+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		sprintf((char *)szTemp, "%9s", szLatSW);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W*2 + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(i+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		sprintf((char *)szTemp, "%10s",szLonSW);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W*3 + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(i+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		m_pScreen->SetFont(pOldFont);
		
		nPos += RGN_LIST_ITEM_H;
	}
	// Update Default Region
	if(m_bShowDeftRegion)
	{
		oReg.SetNEPosition(LAT_NULL_VALUE,LON_NULL_VALUE);
		oReg.SetSWPosition(LAT_NULL_VALUE,LON_NULL_VALUE);

		oReg.GetNEPosition(szLatNE, szLonNE);
		oReg.GetSWPosition(szLatSW, szLonSW);

		if(m_nCurSel == pRegionDataArray->size())
		{
			backClr = COLORSCHEME[m_nScheme].crFore;
			txtClr = COLORSCHEME[m_nScheme].crBack;
		}
		else
		{
			backClr = COLORSCHEME[m_nScheme].crBack;
			txtClr = COLORSCHEME[m_nScheme].crFore;
		}

		m_pScreen->FillRect(RGN_LIST_X_POS,RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(pRegionDataArray->size()+1),RGN_LIST_X_POS+RGN_LIST_W-1,RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(pRegionDataArray->size()+1) + RGN_LIST_ITEM_H -1,backClr);
		
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;
		
		sprintf((char *)szTemp, "%10s", szLatNE);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(pRegionDataArray->size()+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		sprintf((char *)szTemp, "%10s", szLonNE);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(pRegionDataArray->size()+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		sprintf((char *)szTemp, "%9s", szLatSW);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W*2 + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(pRegionDataArray->size()+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		sprintf((char *)szTemp, "%10s",szLonSW);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = RGN_LIST_X_POS + RGN_LIST_COL_W*3 + (RGN_LIST_COL_W - nStrW)/2;
		nYPos = RGN_LIST_Y_POS + RGN_LIST_ITEM_H*(pRegionDataArray->size()+1) + (RGN_LIST_ITEM_H - nFontH)/2 + nYOffset;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
		m_pScreen->SetFont(pOldFont);

	}
	
}

void CRegionalWnd::RemoveCurRegionData()
{
	//g_pRegional->RemoveAt(m_nCurSel);
}

/*********************************************************************************************************/
// Name		: GetRegionDataCount
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CRegionalWnd::GetRegionDataCount()
{
	CRegionalArray *pRegionDataArray = g_pDocMgr->GetRegionalArray();
	
	return pRegionDataArray->size();
}

/*********************************************************************************************************/
// Name		: GetInitSel
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
bool CRegionalWnd::GetInitSel()
{
	return m_bInitSel;
}

/*********************************************************************************************************/
// Name		: SetInitSel
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CRegionalWnd::SetInitSel(bool bSet)
{
	m_bInitSel = bSet;
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CRegionalWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);

	if( nResult == AL_YES )
	{
		RemoveCurRegionData();
	}		
	return nResult;
}
