/*...........................................................................*/
/*.                  File Name : WinInwa14bMiy.cpp                          .*/
/*.                                                                         .*/
/*.                       Date : 2010.02.28                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma09bBengali_Font;

ROMDATA PEGUSHORT WinInwa14bMiy_Font_offset_table[225] = {
0x0000,0x0007,0x0010,0x0019,0x0022,0x0036,0x0046,0x004f,0x0058,0x005e,0x0064,0x006d,0x0076,0x0085,0x008e,0x0093,
0x009a,0x00a3,0x00ac,0x00b4,0x00bd,0x00c6,0x00cf,0x00d8,0x00e1,0x00ea,0x00f3,0x00fc,0x0101,0x0111,0x011a,0x0124,
0x0128,0x0138,0x0141,0x0151,0x0160,0x0166,0x016f,0x0175,0x017b,0x0180,0x0187,0x018d,0x0191,0x0197,0x01a7,0x01b1,
0x01ba,0x01cb,0x01d6,0x01df,0x01e5,0x01ee,0x01f2,0x01fb,0x0207,0x0210,0x0214,0x021d,0x022c,0x023c,0x0240,0x0248,
0x0256,0x0260,0x0268,0x0277,0x0280,0x0287,0x0290,0x0296,0x029e,0x02a4,0x02ad,0x02b7,0x02bb,0x02c0,0x02c8,0x02d7,
0x02e6,0x02ef,0x02fe,0x0307,0x030d,0x031b,0x032a,0x0339,0x0348,0x0357,0x0360,0x0369,0x0372,0x037b,0x037f,0x038f,
0x039a,0x03a5,0x03b0,0x03bb,0x03c6,0x03d1,0x03dc,0x03e7,0x03f2,0x03fd,0x0408,0x0413,0x041e,0x0429,0x0434,0x043f,
0x044a,0x0455,0x0460,0x046b,0x0476,0x0481,0x048c,0x0497,0x04a2,0x04ad,0x04b8,0x04c3,0x04ce,0x04d9,0x04e4,0x04ef,
0x04fa,0x0505,0x050e,0x0518,0x0527,0x0530,0x0539,0x0544,0x054a,0x0551,0x0558,0x0560,0x0565,0x0571,0x057c,0x0583,
0x058e,0x05af,0x05c0,0x05cd,0x05d4,0x05db,0x05e1,0x05f1,0x05f9,0x0600,0x0609,0x0612,0x0617,0x061e,0x0628,0x062f,
0x063b,0x0648,0x064f,0x065d,0x066a,0x0673,0x067e,0x0685,0x068f,0x069d,0x06a4,0x06b2,0x06c0,0x06c7,0x06d0,0x06d7,
0x06de,0x06e9,0x06f2,0x0702,0x0711,0x0721,0x0731,0x073e,0x0747,0x0751,0x0760,0x076d,0x077c,0x0783,0x0790,0x079c,
0x07a2,0x07af,0x07bf,0x07cd,0x07da,0x07e4,0x07f0,0x07f7,0x07fc,0x080b,0x0812,0x0823,0x082f,0x0835,0x0841,0x0847,
0x084d,0x0857,0x0866,0x0873,0x0888,0x0895,0x08a2,0x08a9,0x08b2,0x08bb,0x08c7,0x08d3,0x08de,0x08e7,0x08f2,0x0904,
0x090f};



ROMDATA PEGUBYTE WinInwa14bMiy_Font_data_table[5510] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 
0x00, 0x00, 0xff, 0x9f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x00, 0x07, 0xf8, 0xff, 0x1f, 0xe3, 
0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 
0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x7f, 0xe0, 0x03, 0xff, 0x0f, 0xfc, 0x30, 0x00, 0x30, 0x60, 0x00, 0x00, 0x03, 0xf8, 
0x00, 0x00, 0x07, 0xf0, 0x07, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x01, 0x80, 0xc0, 
0x0c, 0x0e, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0xe0, 0x00, 0x19, 0xf8, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 
0x00, 0x07, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 
0x03, 0xf0, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xf0, 
0x3f, 0xff, 0x00, 0x1f, 0xe0, 0x00, 0x00, 0x00, 0x3f, 0xff, 0x00, 0x00, 0x78, 0x01, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0xff, 0xfc, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x8c, 0x00, 0x00, 0x01, 0xfe, 0x00, 0x00, 0x00, 0x00, 
0x78, 0x00, 0x71, 0xe0, 0x00, 0x03, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 
0xff, 0xfc, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 
0x07, 0xe0, 0x00, 0x07, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3b, 0x9c, 0x00, 0x00, 0x00, 0x73, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x30, 0x63, 0xfc, 0x00, 0x07, 0x1c, 
0x00, 0x00, 0x0e, 0x38, 0x0e, 0x38, 0x00, 0x00, 0x01, 0xef, 0x18, 0x0c, 0x00, 0x01, 0x80, 0xc0, 
0x0e, 0x0c, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1f, 0xff, 0x83, 0xf0, 0x70, 0x1e, 0x3d, 0xfb, 0xc0, 0x00, 0x00, 0x1c, 0x00, 0x00, 
0x00, 0x1d, 0x81, 0x9c, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x0f, 0xf0, 0xe0, 0x18, 0x00, 0x03, 
0xe3, 0xf0, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x31, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x19, 0xb0, 
0x70, 0x03, 0x80, 0x38, 0x30, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0xcc, 0x01, 0xc0, 0x1e, 
0x01, 0x80, 0x01, 0xc0, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x0c, 0x00, 0x00, 0x03, 0x83, 0x00, 0x00, 0x00, 0x00, 
0xcc, 0x00, 0xdb, 0x30, 0x00, 0x07, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 
0xc0, 0x06, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x61, 0xcf, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x06, 0x00, 0x00, 
0x00, 0x31, 0x8c, 0x00, 0x00, 0x00, 0xe0, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x30, 0x63, 0xe6, 0x00, 0x0c, 0x06, 
0x00, 0x00, 0x18, 0x0c, 0x18, 0x0c, 0x00, 0x00, 0x01, 0xd9, 0x98, 0x0c, 0x00, 0x01, 0x80, 0xc0, 
0x06, 0x18, 0x3f, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x38, 0x00, 0xc0, 0x00, 0x00, 0x30, 0x7c, 0x06, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x06, 0x00, 0x00, 0x1c, 0x19, 0xb0, 0x00, 0x00, 0x03, 
0x60, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x60, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xce, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x60, 0x01, 0x80, 0x30, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xfc, 0x01, 0xe0, 0x1e, 
0x00, 0xc0, 0x01, 0x80, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x0e, 0x06, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 
0xcc, 0x00, 0xc0, 0x30, 0x00, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 
0x80, 0x06, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x7f, 0x8f, 0x80, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x1c, 0x00, 0x00, 
0x00, 0x31, 0x8c, 0x00, 0x00, 0x00, 0xc0, 0xc0, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x30, 0x63, 0xfe, 0x00, 0x0c, 0x06, 
0x00, 0x00, 0x18, 0x0c, 0x18, 0x0c, 0x00, 0x00, 0x01, 0xf9, 0x98, 0x0c, 0x00, 0x01, 0x80, 0xc0, 
0x03, 0x38, 0x33, 0x80, 0x07, 0x78, 0x70, 0x00, 0x00, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0xc3, 0xe0, 0x60, 0x30, 0x00, 0x66, 0x7e, 0x00, 0x00, 0x7e, 0x00, 0x00, 
0x00, 0x1f, 0x80, 0xfc, 0x00, 0x00, 0x3b, 0xc6, 0x18, 0x00, 0x18, 0x19, 0x81, 0xc1, 0x80, 0x03, 
0x0e, 0x0c, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xf0, 0xc0, 0x00, 0x00, 0x60, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x86, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0xfc, 0x01, 0xe0, 0x1e, 
0x00, 0x60, 0x01, 0x80, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0e, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xcc, 0x00, 0x60, 0x30, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xf6, 0xef, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x3c, 0x00, 0x00, 
0x00, 0x3b, 0x8c, 0x00, 0x00, 0x00, 0x00, 0xc1, 0xc0, 0x00, 0x00, 0x30, 0x3c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x30, 0x63, 0x7e, 0x00, 0x18, 0x03, 
0x00, 0x00, 0x30, 0x06, 0x30, 0x06, 0x00, 0x00, 0x01, 0xf9, 0x98, 0x0c, 0x00, 0x01, 0x80, 0xc0, 
0x03, 0xb0, 0x30, 0xc0, 0x0f, 0xf8, 0x78, 0x00, 0x39, 0xc0, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x07, 0x38, 0x60, 0x38, 0x18, 0x66, 0x7e, 0x00, 0x00, 0xe7, 0x00, 0x00, 
0x00, 0x39, 0xc1, 0xce, 0x00, 0x00, 0x33, 0xc6, 0x18, 0x00, 0x18, 0x00, 0xc0, 0xc3, 0x00, 0x01, 
0x86, 0x18, 

0x00, 0x00, 0x00, 0x00, 0x1e, 0xf6, 0x01, 0x99, 0x80, 0x00, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x78, 0x01, 0xe0, 0x00, 
0x00, 0x30, 0x01, 0x80, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x78, 0x00, 0x30, 0x30, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xfb, 0xff, 0x80, 0x00, 0x7f, 0xf8, 0x00, 0x00, 0x00, 0x0e, 0x00, 0xf8, 0x0e, 0x00, 
0x00, 0x1f, 0x0c, 0x00, 0x00, 0x00, 0x01, 0x81, 0xe0, 0x00, 0x00, 0x70, 0x3e, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x30, 0x61, 0xfc, 0x00, 0x18, 0x03, 
0x00, 0x00, 0x30, 0x06, 0x30, 0x06, 0x00, 0x00, 0x00, 0xef, 0x18, 0x0c, 0x00, 0x01, 0x80, 0xc0, 
0x01, 0xf0, 0x30, 0xc0, 0x0f, 0xfc, 0x6e, 0x00, 0x3b, 0xc0, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x06, 0x18, 0x60, 0x0c, 0x18, 0x63, 0xc0, 0x00, 0x00, 0xc3, 0x00, 0x00, 
0x00, 0x30, 0xc1, 0x86, 0x00, 0x00, 0x3c, 0x06, 0x18, 0x00, 0x18, 0x00, 0x60, 0xc3, 0x00, 0x01, 
0xc6, 0x18, 

0x00, 0x00, 0x00, 0x00, 0x3b, 0xbf, 0xd9, 0x9b, 0x00, 0x00, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x30, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x1f, 0x80, 
0x00, 0x01, 0xfb, 0xcf, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x03, 0xb0, 0x0e, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x03, 0x03, 0xf0, 0x00, 0x31, 0xe0, 0x3e, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x7f, 0xc0, 0x03, 0xfe, 0x0f, 0xf8, 0x30, 0x00, 0x30, 0x60, 0x00, 0x00, 0x18, 0x03, 
0x00, 0x00, 0x30, 0x06, 0x30, 0x06, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x01, 0x80, 0xc0, 
0x01, 0xe0, 0x30, 0xc0, 0x0f, 0xf8, 0x33, 0x80, 0x3f, 0x80, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x0c, 0x18, 0x60, 0x06, 0x18, 0x60, 0x00, 0x00, 0x01, 0x81, 0x80, 0x00, 
0x00, 0x60, 0x63, 0x03, 0x00, 0x00, 0x00, 0x06, 0x18, 0x00, 0x18, 0x00, 0x00, 0x67, 0x00, 0x00, 
0xe3, 0x38, 

0x00, 0xf8, 0x3c, 0x1e, 0x33, 0x1f, 0x98, 0xf6, 0x00, 0xf0, 0x7c, 0xc0, 0x67, 0xe0, 0xc1, 0x9b, 
0xc0, 0x00, 0x0f, 0x0f, 0x07, 0xc0, 0x63, 0xe1, 0xf0, 0xf1, 0xbc, 0x3e, 0x1e, 0x0f, 0x01, 0x8f, 
0x60, 0x00, 0x00, 0x30, 0x06, 0x7f, 0xfc, 0x6e, 0x60, 0x00, 0x7b, 0x78, 0x00, 0xf0, 0x00, 0x00, 
0x00, 0x06, 0x79, 0x80, 0x01, 0x80, 0x1f, 0x0f, 0xff, 0x80, 0x18, 0x06, 0x00, 0x00, 0x1f, 0x00, 
0x0c, 0x7c, 0x07, 0xc3, 0x3f, 0x81, 0xfd, 0x80, 0x18, 0x31, 0x83, 0x00, 0x3c, 0x3e, 0xf0, 0xf8, 
0x00, 0x3c, 0x00, 0x30, 0x03, 0xe6, 0x00, 0x00, 0x3e, 0x3e, 0x78, 0x7d, 0xe1, 0xf0, 0x7d, 0xf1, 
0xb8, 0x31, 0xff, 0x87, 0xfe, 0x07, 0xde, 0x1f, 0x78, 0x3e, 0xf8, 0xdc, 0x6e, 0x1f, 0x0f, 0x81, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x07, 0xf0, 0x00, 0x3e, 0xf0, 0x7c, 0x7c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xdf, 0xc0, 0xe7, 0x8c, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x06, 0x30, 0x00, 0x00, 
0xfe, 0x3f, 0x8c, 0x00, 0x78, 0x00, 0x06, 0x07, 0xf8, 0x00, 0x7b, 0xc1, 0xff, 0x9f, 0x80, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x7c, 0x30, 0x60, 0x00, 0x01, 0x98, 0x03, 
0x0f, 0xbe, 0x30, 0x06, 0x30, 0x06, 0x00, 0x01, 0xf0, 0x00, 0x18, 0x0c, 0x7c, 0x01, 0x80, 0xc0, 
0x00, 0xc0, 0x30, 0xc1, 0x87, 0xf8, 0x31, 0xc0, 0x1f, 0x01, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x0f, 0xfc, 0x60, 0xf3, 0x18, 0x60, 0x00, 0x7c, 0xf1, 0x81, 0x87, 0xcf, 
0x38, 0x60, 0x63, 0x03, 0x00, 0x1c, 0x00, 0x06, 0x18, 0x00, 0x18, 0x00, 0xf0, 0x66, 0x07, 0xbe, 
0x63, 0x30, 

0x01, 0x9c, 0xe7, 0x30, 0x33, 0x1f, 0x98, 0x06, 0x03, 0x98, 0xc6, 0xc0, 0x6e, 0x70, 0xc3, 0x0c, 
0x70, 0x00, 0x0f, 0x39, 0xcc, 0xe0, 0x66, 0x73, 0x99, 0x99, 0xbc, 0x77, 0x33, 0x99, 0x81, 0x8f, 
0x60, 0x00, 0x00, 0x30, 0x06, 0xc7, 0xc6, 0xff, 0x60, 0x00, 0x19, 0x8e, 0x00, 0xc0, 0x00, 0x00, 
0x00, 0x06, 0x79, 0x80, 0x01, 0x80, 0x33, 0x98, 0xf8, 0xc0, 0x18, 0x06, 0x00, 0x00, 0x39, 0x80, 
0x0c, 0xe6, 0x0e, 0xe6, 0x1c, 0xe7, 0xb7, 0x80, 0x18, 0x1b, 0x03, 0x00, 0xe6, 0x63, 0xdd, 0x8e, 
0x00, 0x30, 0x00, 0x30, 0x07, 0x36, 0x00, 0x00, 0x63, 0x67, 0x0c, 0xce, 0x3b, 0xdc, 0xc7, 0xbb, 
0x1c, 0x33, 0x18, 0xcc, 0xf3, 0x9c, 0xe7, 0x73, 0xce, 0x73, 0xdd, 0x86, 0xdf, 0x3d, 0x9c, 0xc1, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x01, 0x98, 0x00, 0x67, 0x9c, 0xe6, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xff, 0xc1, 0xbd, 0x9c, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x1c, 0x60, 0x00, 0x00, 
0x33, 0x00, 0x0c, 0x01, 0xcc, 0x00, 0x0c, 0x07, 0xf8, 0x00, 0x7f, 0x81, 0xff, 0x8f, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0xce, 0x30, 0x60, 0x00, 0x01, 0x98, 0x03, 
0x19, 0xe3, 0x30, 0x06, 0x30, 0x06, 0x00, 0x00, 0x18, 0x00, 0x18, 0x0c, 0xce, 0x01, 0x80, 0xc0, 
0x00, 0xc0, 0x33, 0x81, 0x83, 0xf0, 0x18, 0x70, 0x1f, 0x01, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x0c, 0x00, 0x63, 0x9b, 0x18, 0x60, 0x00, 0xce, 0x19, 0x81, 0x8c, 0x61, 
0x8e, 0x60, 0x63, 0x03, 0x00, 0x1c, 0x00, 0x06, 0x18, 0x00, 0x18, 0x01, 0x9c, 0x76, 0x0c, 0xf3, 
0x63, 0xb0, 

0x00, 0x0c, 0xc3, 0x60, 0x33, 0x3f, 0xd8, 0x0c, 0xf3, 0x0c, 0x1e, 0xc0, 0x6c, 0x37, 0xfb, 0x0c, 
0x37, 0xf8, 0x0f, 0x30, 0xc0, 0x60, 0x60, 0x33, 0x03, 0xcd, 0xbc, 0x63, 0x71, 0xb0, 0xc1, 0x80, 
0x60, 0x00, 0x7f, 0xb0, 0x06, 0xc3, 0x86, 0xff, 0x60, 0x00, 0x71, 0x86, 0x00, 0x70, 0x00, 0x00, 
0x00, 0x06, 0x79, 0x80, 0x01, 0x80, 0x01, 0x98, 0x70, 0xc0, 0x18, 0x06, 0x00, 0x00, 0x30, 0x00, 
0x0c, 0xc0, 0x0f, 0xe6, 0x18, 0x66, 0xf7, 0x80, 0x30, 0x0e, 0x03, 0x00, 0xc0, 0x01, 0x8c, 0xf6, 
0x00, 0x1c, 0x00, 0x30, 0x06, 0x06, 0x00, 0x00, 0xc3, 0x03, 0x0c, 0x06, 0x1b, 0x6c, 0x03, 0x1b, 
0x0c, 0x30, 0x78, 0xd8, 0x61, 0x98, 0x63, 0x61, 0x86, 0x61, 0x8d, 0x86, 0xdb, 0x37, 0x98, 0x01, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x07, 0x18, 0x00, 0xc3, 0x0c, 0xc0, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xf7, 0xe1, 0xb1, 0xbc, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x1f, 0xe0, 0x00, 0x00, 
0xff, 0x00, 0x0d, 0xf9, 0x86, 0x00, 0x0c, 0x03, 0xf0, 0x00, 0x7f, 0x01, 0xff, 0xbf, 0xc0, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x06, 0x30, 0x60, 0x00, 0x01, 0x8c, 0x06, 
0x00, 0xc3, 0x18, 0x0c, 0x18, 0x0c, 0x00, 0x00, 0x18, 0x00, 0x18, 0x0c, 0x06, 0x01, 0x80, 0xc0, 
0x00, 0xc0, 0x3f, 0x01, 0x83, 0xe0, 0x0f, 0xf0, 0x1f, 0x01, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x0e, 0x00, 0x63, 0xcf, 0x18, 0x60, 0x00, 0x06, 0x19, 0x81, 0x80, 0x61, 
0x86, 0x60, 0x63, 0x03, 0x00, 0x7f, 0x80, 0x06, 0x18, 0x00, 0x18, 0x00, 0x0c, 0x3c, 0x18, 0x61, 
0xe1, 0xe0, 

0x00, 0x0c, 0xff, 0x60, 0x18, 0xf6, 0xf0, 0x19, 0x9b, 0xcc, 0x06, 0xc0, 0x6c, 0x30, 0xc3, 0x0c, 
0x30, 0x00, 0x0f, 0x30, 0xc0, 0x60, 0x60, 0x33, 0x03, 0xcd, 0x86, 0x63, 0x79, 0xb3, 0xc1, 0x80, 
0x60, 0x00, 0x00, 0x30, 0x06, 0xc3, 0x86, 0xc3, 0x60, 0x00, 0x61, 0x86, 0x03, 0xd8, 0x00, 0x00, 
0x00, 0x06, 0x79, 0x80, 0x01, 0x80, 0x01, 0x98, 0x70, 0xc0, 0x18, 0x06, 0x00, 0x00, 0x30, 0x00, 
0x0c, 0xc0, 0x0f, 0xe6, 0x18, 0x66, 0xf7, 0x80, 0x30, 0x0e, 0x03, 0x00, 0xde, 0x01, 0x8d, 0x9e, 
0x00, 0xf6, 0x00, 0x30, 0x06, 0x06, 0x00, 0x00, 0x03, 0x03, 0x0c, 0x06, 0x1b, 0x6c, 0xfb, 0x1b, 
0xfc, 0x30, 0x1f, 0xd8, 0x61, 0x98, 0x63, 0x61, 0x86, 0x61, 0x8d, 0x86, 0xcf, 0x37, 0x98, 0x01, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x06, 0x18, 0x00, 0xc3, 0x0c, 0xc0, 0xe6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xde, 0xf1, 0xb3, 0x6c, 0x03, 0x00, 0x00, 0x00, 0x00, 0x06, 0x07, 0xf0, 0x00, 0x00, 
0xef, 0x00, 0x0c, 0x01, 0xe6, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x7e, 0x00, 0xff, 0x8f, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x06, 0x30, 0x60, 0x00, 0x01, 0x8c, 0x06, 
0x00, 0xc3, 0x18, 0x0c, 0x18, 0x0c, 0x00, 0x03, 0x38, 0x00, 0x18, 0x0c, 0x06, 0x01, 0x80, 0xc0, 
0x00, 0xc0, 0x30, 0x01, 0x81, 0xc0, 0x1f, 0xc0, 0x3f, 0x80, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x06, 0x18, 0x63, 0xef, 0x18, 0x60, 0x00, 0x06, 0x19, 0xc3, 0x00, 0x61, 
0x86, 0x70, 0xc3, 0x86, 0x00, 0x00, 0x00, 0x06, 0x18, 0x00, 0x18, 0x00, 0x0c, 0x3c, 0x18, 0x61, 
0xe1, 0xe0, 

0x01, 0x9c, 0xe7, 0x63, 0x00, 0x36, 0x00, 0x39, 0x9b, 0x6c, 0xc6, 0xc0, 0x6c, 0x30, 0xc1, 0x9e, 
0x70, 0x03, 0xcf, 0x39, 0xcc, 0xe0, 0x60, 0x73, 0x83, 0xcc, 0xce, 0x03, 0x79, 0xb7, 0x81, 0x8f, 
0x60, 0x00, 0x7f, 0xb0, 0x06, 0xe3, 0xce, 0x66, 0x60, 0x00, 0x73, 0xce, 0x03, 0x78, 0x00, 0x00, 
0x00, 0x06, 0x79, 0x80, 0x01, 0x80, 0x03, 0x9c, 0x79, 0xc0, 0x18, 0x06, 0x00, 0x00, 0x38, 0x00, 
0x0c, 0xe0, 0x0e, 0x37, 0x38, 0xc7, 0xb7, 0x80, 0x60, 0x1b, 0x03, 0x00, 0xf6, 0x61, 0x8d, 0xde, 
0x00, 0xde, 0x00, 0x30, 0x07, 0x36, 0x00, 0x00, 0x07, 0x07, 0x9c, 0xcf, 0x3b, 0xdc, 0xcf, 0xb9, 
0x98, 0x33, 0x1c, 0xd8, 0x63, 0x1c, 0x77, 0x73, 0x8c, 0x73, 0xdd, 0xcc, 0x66, 0x3d, 0x9c, 0x01, 
0x80, 0x00, 0xc1, 0x98, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x00, 0x06, 0x18, 0xc3, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 
0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x00, 
0x07, 0x30, 0x00, 0xc3, 0x0c, 0xe0, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x03, 0xde, 0xf1, 0xb6, 0x7c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0x00, 0x00, 
0xfe, 0x00, 0x0c, 0x01, 0xb6, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x7c, 0x00, 0x3c, 0x1f, 0x80, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x0e, 0x30, 0x60, 0x00, 0x01, 0x87, 0x1c, 
0x01, 0xc3, 0x0e, 0x38, 0x0e, 0x38, 0x00, 0x03, 0xf0, 0x00, 0x0e, 0x38, 0x0e, 0x00, 0xe3, 0x80, 
0x00, 0xc0, 0x30, 0x01, 0x80, 0xc0, 0x1f, 0x80, 0x7f, 0x80, 0x3c, 0x00, 0x00, 0x00, 0x00, 0xe0, 
0x00, 0x00, 0x30, 0x00, 0x07, 0x38, 0x63, 0xef, 0x18, 0x60, 0x00, 0x0f, 0x38, 0xe7, 0x0c, 0xf3, 
0xce, 0x39, 0xc1, 0xce, 0x00, 0x1c, 0x00, 0x07, 0x38, 0x00, 0x18, 0x00, 0x1c, 0x3c, 0x18, 0x61, 
0xe1, 0xe0, 

0x01, 0xf8, 0x3c, 0x33, 0x00, 0x36, 0x00, 0x30, 0xf1, 0xec, 0x7c, 0xc0, 0x66, 0x60, 0xc0, 0xfb, 
0xc0, 0x03, 0xcf, 0x0f, 0x07, 0xc0, 0x67, 0xe1, 0xf8, 0x0c, 0x78, 0x06, 0x3f, 0x37, 0x81, 0x8f, 
0x60, 0x00, 0x00, 0x30, 0x06, 0x7b, 0xbc, 0x3c, 0x60, 0x00, 0x1e, 0xf8, 0x01, 0xf8, 0x00, 0x00, 
0x00, 0x06, 0x79, 0x80, 0x01, 0x80, 0x3f, 0x0f, 0x7f, 0x80, 0x18, 0x06, 0x00, 0x00, 0x0f, 0xc0, 
0x0c, 0x7e, 0x07, 0xc3, 0xe7, 0x83, 0xe7, 0x80, 0x60, 0x31, 0x83, 0x00, 0x7c, 0x73, 0x18, 0x78, 
0x00, 0x7e, 0x00, 0x30, 0x01, 0xe6, 0x00, 0x00, 0x3c, 0x7e, 0xf8, 0x7d, 0xe1, 0xf0, 0x7f, 0xe0, 
0xf1, 0xb1, 0xff, 0x8c, 0xde, 0x0f, 0x3e, 0x1e, 0x78, 0x3e, 0xf8, 0x78, 0x3c, 0x1f, 0x07, 0x81, 
0x80, 0x00, 0xff, 0x9f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x00, 0x07, 0xf8, 0xff, 0x1f, 0xe3, 
0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 
0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x00, 
0x01, 0xe0, 0x00, 0x60, 0x18, 0x7e, 0xce, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x9e, 0x71, 0xb7, 0x8c, 0x7f, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x18, 
0x3c, 0x00, 0x0c, 0x00, 0xf6, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x38, 0x00, 0x7f, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x7f, 0xe0, 0x03, 0xff, 0x0f, 0xfc, 0x30, 0xfc, 0x30, 0x60, 0x00, 0x01, 0x83, 0xf8, 
0x1f, 0xbe, 0x07, 0xf0, 0x07, 0xf0, 0x00, 0x03, 0x38, 0x00, 0x07, 0xf0, 0xfc, 0x00, 0x7f, 0x00, 
0x00, 0xc0, 0x30, 0x01, 0x80, 0x00, 0x3e, 0x00, 0x73, 0x00, 0xff, 0x00, 0x00, 0x00, 0x00, 0xe0, 
0x00, 0x00, 0x30, 0x00, 0x03, 0xe0, 0x61, 0xef, 0x18, 0x60, 0x00, 0xfd, 0xf0, 0x7e, 0x07, 0xdf, 
0x78, 0x1f, 0x80, 0xfc, 0x00, 0x1c, 0x00, 0x03, 0xf8, 0x00, 0x18, 0x03, 0xf0, 0x18, 0x0c, 0xc1, 
0xe0, 0xc0, 

0x01, 0x80, 0x00, 0x7f, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x60, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x30, 0x0c, 0x00, 0x06, 0x00, 0x30, 0x00, 0x00, 
0x60, 0x0f, 0x00, 0x33, 0xc0, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x03, 0xc0, 
0x78, 0x06, 0x79, 0x80, 0x01, 0x80, 0x30, 0x00, 0x00, 0x00, 0xd9, 0xe6, 0xc0, 0x78, 0x30, 0xc1, 
0xec, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x00, 0x1e, 0x30, 0x00, 
0x00, 0x18, 0x00, 0x00, 0x00, 0x06, 0x01, 0x9e, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc1, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xfe, 0x00, 0x06, 0x00, 0x00, 0x7e, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xce, 0x60, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x3c, 0x00, 0x1e, 
0x0e, 0x00, 0x0c, 0x00, 0x06, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 
0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf8, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x78, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 
0x00, 0x00, 0x30, 0x01, 0x80, 0x00, 0x00, 0x0f, 0x18, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x33, 0x18, 0x18, 0x00, 0x01, 
0xe0, 0xc0, 

0x00, 0xfc, 0x00, 0x7e, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x60, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x30, 0x0c, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x00, 
0x60, 0x19, 0x80, 0x37, 0x70, 0x1e, 0x00, 0x00, 0x60, 0x01, 0x80, 0x00, 0x00, 0x00, 0x06, 0xe0, 
0x78, 0x06, 0x79, 0x80, 0x07, 0x81, 0xb0, 0x00, 0x00, 0x01, 0xdb, 0x76, 0xc0, 0xdf, 0xff, 0x83, 
0x7c, 0x06, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x00, 0x03, 0xc0, 0x06, 0x07, 0x9e, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xc1, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0xfe, 0x1f, 0xe0, 0x06, 0x7e, 0x3f, 0xe6, 0x1e, 0x3c, 0x7e, 0x63, 0xfe, 0x00, 0x07, 
0x80, 0x01, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0xc7, 0x83, 0xcf, 0x80, 0x00, 0x1c, 0x00, 0x06, 
0x00, 0x00, 0x0c, 0x00, 0x06, 0x78, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x79, 
0xe7, 0xfc, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x3d, 0x80, 0x00, 
0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xe0, 0x78, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x07, 
0x80, 0x00, 0x00, 0x01, 0x80, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xe7, 0xfc, 0x78, 0x00, 
0x00, 0x0f, 0x30, 0x07, 0x80, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0xff, 0x98, 0xf1, 0xf8, 0x30, 0x00, 0x01, 
0xe1, 0x80, 

0x00, 0x0c, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x31, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x63, 0x0c, 0x00, 0x0c, 0x00, 0x30, 0xc0, 0x00, 
0x60, 0x19, 0x80, 0x36, 0x30, 0x33, 0xf0, 0x00, 0x60, 0x01, 0x80, 0x00, 0x00, 0x00, 0x06, 0x60, 
0x78, 0x06, 0x79, 0x80, 0x07, 0x81, 0xb0, 0x00, 0x00, 0x1b, 0xdb, 0x36, 0xc1, 0xcf, 0xf0, 0x1e, 
0x3c, 0x7e, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x00, 0x03, 0xc0, 0x06, 0x07, 0x9e, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xb3, 0x33, 0x30, 0x06, 0x3e, 0x66, 0x3e, 0x33, 0x06, 0x7e, 0x66, 0x63, 0x00, 0x0c, 
0xc0, 0x01, 0x8e, 0x70, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xc6, 0x0d, 0x80, 0x00, 0x06, 0x00, 0x1e, 
0x00, 0x00, 0x0c, 0x00, 0x06, 0xcc, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x19, 0xdf, 
0xf6, 0x66, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x7f, 0x80, 0x00, 
0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x30, 0x78, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x0c, 
0xc0, 0x00, 0x00, 0x01, 0x80, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x33, 0x3c, 0xee, 0xfc, 0x00, 
0x00, 0x0f, 0x30, 0x07, 0x80, 0x00, 0x01, 0xef, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x01, 0x9c, 0xd8, 0xf0, 0x18, 0xf0, 0x0f, 0xf1, 
0xe7, 0x80, 

0x00, 0x0c, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x3b, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x60, 0xc0, 0x63, 0x0c, 0x00, 0x0c, 0x00, 0x30, 0xc0, 0x00, 
0x60, 0x19, 0x80, 0x36, 0x30, 0x33, 0xc0, 0x00, 0x60, 0x01, 0x80, 0x00, 0x00, 0x00, 0x06, 0x60, 
0x78, 0x06, 0x79, 0x80, 0x07, 0x81, 0xb0, 0x00, 0x00, 0x1f, 0xdb, 0x36, 0xc7, 0xcf, 0xf1, 0xde, 
0x3c, 0xc6, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x00, 0x03, 0xc0, 0x06, 0x07, 0x9e, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0xc1, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x33, 0x33, 0x70, 0x06, 0x6f, 0xe6, 0x30, 0x3f, 0x7e, 0x7e, 0x66, 0x63, 0x00, 0x0f, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xc3, 0xc3, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x19, 0x8f, 
0xe0, 0x66, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x7f, 0x80, 0x00, 
0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x31, 0xfc, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x0c, 
0xc0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x3c, 0xc6, 0xfc, 0x00, 
0x00, 0x1f, 0xb0, 0x07, 0x80, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x01, 0x98, 0xd8, 0xf3, 0x98, 0x00, 0x18, 0x01, 
0xe0, 0x00, 

0x00, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xe0, 0xc0, 0xc1, 0x98, 0x00, 0x0c, 0x00, 0x19, 0x80, 0x00, 
0x70, 0x1b, 0x80, 0x3b, 0x70, 0x33, 0xc0, 0x00, 0x70, 0x03, 0x80, 0x00, 0x00, 0x00, 0x07, 0xe0, 
0x78, 0x06, 0x79, 0xc0, 0x07, 0xc3, 0xb1, 0x80, 0x00, 0x0e, 0x7b, 0xfe, 0xc0, 0xfc, 0x30, 0xc3, 
0x7c, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x00, 0x00, 0x00, 0x07, 0x0d, 0x9e, 0x00, 0x60, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xfe, 0x00, 0x70, 0x06, 0x7c, 0x66, 0x30, 0x3b, 0x6e, 0xff, 0x66, 0x63, 0x00, 0x0c, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xc7, 0xed, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x19, 0x8d, 
0xe6, 0xc6, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x77, 0x80, 0x00, 
0x18, 0x03, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xf1, 0xe0, 0x00, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x0c, 
0xc0, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x3c, 0xc6, 0xcc, 0x0f, 
0xff, 0xfb, 0xb8, 0x0f, 0x80, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x01, 0x99, 0xdd, 0xf3, 0x18, 0x00, 0x18, 0x01, 
0xe0, 0x00, 

0x00, 0x0e, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x61, 0x80, 0xf0, 0x00, 0x06, 0x00, 0x0f, 0x00, 0x00, 
0x3f, 0xff, 0x00, 0x1f, 0xe0, 0x1b, 0xc0, 0x00, 0x3f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 
0xfe, 0x07, 0xfe, 0xff, 0xfc, 0xff, 0x1f, 0x80, 0x00, 0x00, 0x01, 0xef, 0xc0, 0x78, 0x1f, 0xc1, 
0xdc, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x00, 0x00, 0x00, 0x03, 0xfd, 0xdf, 0x00, 0x3f, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x80, 
0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x3f, 0xe0, 0x1e, 0x3c, 0x00, 0x73, 0xfe, 0x00, 0x07, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x78, 
0x03, 0xdc, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 
0x0f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x7f, 0xf8, 0x00, 0x07, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xe7, 0xbc, 0x78, 0x00, 
0x00, 0x0f, 0x9f, 0xfc, 0xe0, 0x00, 0x03, 0xff, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0xf7, 0x8f, 0x9f, 0xf8, 0x00, 0x0f, 0xff, 
0xe0, 0x00, 


};

xFONTYY WinInwa14bMiy_Font = {0x01, 19, 0, 19, 0, 0, 19, 290, 0x1020, 0x10ff,
(PEGUSHORT *) WinInwa14bMiy_Font_offset_table, &Tahoma09bBengali_Font,
(PEGUBYTE *) WinInwa14bMiy_Font_data_table};


