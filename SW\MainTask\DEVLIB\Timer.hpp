/*...........................................................................*/
/*.                  File Name : TIMER.HPP                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.11                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __TIMER_HPP__
#define  __TIMER_HPP__

//=============================================================================
//=============================================================================

//=============================================================================
class cTimer
{
   protected:
      DWORD m_dBaseAddr;
      DWORD m_dMatchVal;
      DWORD m_dCounter;

   public:
      cTimer(DWORD dBaseAddr,DWORD dMatchVal);
      virtual ~cTimer(void);

   public:
      DWORD GetTimerCounter(void);
      void  SetTimerCounter(DWORD dValue);
      virtual DWORD ReadTimerValue(void) = 0;
      virtual void  RunTimerIsrHandler(void) = 0;
};
//=============================================================================

//=============================================================================
class cTimerSYS : public cTimer
{
   protected:
      xSYS_TIMER *m_pSysTimer;

   public:
      cTimerSYS(DWORD dBaseAddr,DWORD dMatchVal);
      virtual ~cTimerSYS(void);

   public:
      virtual DWORD ReadTimerValue(void);
      virtual void  RunTimerIsrHandler(void);
};
//=============================================================================

#endif


