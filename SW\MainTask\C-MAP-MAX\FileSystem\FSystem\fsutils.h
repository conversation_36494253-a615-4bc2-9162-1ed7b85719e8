/*##################################################################
  FILE    : FSUTILS.H

  USE     : File System utility functions header file.
  PROJECT : C-Map File System

  AUTHOR  : SiS[030606].
  UPDATED :
  ##################################################################

*/

#ifndef __FS_UTILS__
	#define __FS_UTILS__
#ifdef _WIN32
#include <windows.h>
#endif


#include <CMapType.h>


/*****************************************************************************
  Global data definition.
 *****************************************************************************/

extern SWord Support128KBUser;




/*****************************************************************************
  Interface Functions prototypes.
 *****************************************************************************/


void FS_Support128KBUser	( SWord Mode );


#ifdef __cplusplus
extern "C"
{
#endif


#ifdef _DEBUG

#ifdef _WIN32
	extern void _DBG_TRACE ( const TCHAR* pFormat, ... );
#else
	#define _DBG_TRACE	printf
#endif

	extern void _DBG_FileTable ( Word idx );
	extern void _DBG_CheckTable ( Word idx );

#else // #ifdef _DEBUG
	
	#define _DBG_FileTable( idx )	/* */
	#define _DBG_CheckTable( idx )	/* */
	#define _DBG_TRACE()			/* */
#endif

#ifdef __cplusplus
}
#endif



/*****************************************************************************
  END of Code.
 *****************************************************************************/

#endif /* #ifndef __FS_UTILS__ */