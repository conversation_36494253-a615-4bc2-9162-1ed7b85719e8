#include "Wnd.hpp"

#ifndef __SECURITY_LOG_WND_HPP__
#define __SECURITY_LOG_WND_HPP__

class CSecurityLogWnd : public CWnd {
	protected:
		int m_nCurSel;
		int m_nStartViewPos;

	public:
		CSecurityLogWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		void DrawSecurityLogListScrollBar();
		void DrawSecurityLogList();
		void ResetSecurityLogList();

		void SetFocus(int nFocus)   { m_nFocus = nFocus;   }
		void SetCurSel(int nCurSel) { m_nCurSel = nCurSel; }
		int  GetCurSel() { return m_nCurSel; }

	private:
		void DrawListOutLine();
};

#endif
