#============================================================================
#
#         makefile.mak
#
#============================================================================

#============================================================================
GNUARM_PATH=/cygdrive/c/GNUARM-4.2.2/bin/
CC     = $(GNUARM_PATH)arm-elf-gcc
LD     = $(GNUARM_PATH)arm-elf-ld
OC     = $(GNUARM_PATH)arm-elf-objcopy
OD     = $(GNUARM_PATH)arm-elf-objdump
AR     = $(GNUARM_PATH)arm-elf-ar
RANLIB = $(GNUARM_PATH)arm-elf-ranlib
GCCVER = 4.2.2
#============================================================================

#============================================================================
CPU_NAME = __POLLUX__
#============================================================================

#============================================================================
ENDIAN_MODE = EL
OBJ_FORMAT  = elf32-littlearm
#ENDIAN_MODE = EB
#OBJ_FORMAT  = elf32-bigarm
ifeq ($(ENDIAN_MODE),EB)
  COMP_MODE = big-endian
endif
ifeq ($(ENDIAN_MODE),EL)
  COMP_MODE = little-endian
endif
#============================================================================

#============================================================================
ZIP_LIB     = Zip
FILE_LIB    = SDFILE
#============================================================================

#============================================================================
ROOT    = .
SRC_DIR = $(ROOT)
BIN_DIR = $(ROOT)
#============================================================================


#============================================================================
IMAGE_NAME = MainTask
IMAGE_BIN  = $(IMAGE_NAME).bin
IMAGE_REC  = $(IMAGE_NAME).rec
IMAGE_ELF  = $(IMAGE_NAME).elf
IMAGE_MAP  = $(IMAGE_NAME).map
IMAGE_DIS  = $(IMAGE_NAME).dis
#============================================================================

#============================================================================
TEXT_BASE  = 0x00000000
#============================================================================

#============================================================================
INCLUDE    = -I ./                                                          \
             -I ./DEVLIB/                                                   \
             -I ./DEVLIB/DEVICE/                                            \
             -I ./SYSLIB/                                                   \
             -I ./CHART/                                                    \
             -I ./JPGLIB/                                                   \
             -I ./Fonts/                                                    \
             -I ./GFC/                                                      \
             -I ./NMEA/                                                     \
             -I ./SMapLib/                                                  \
             -I /cygdrive/c/GNUARM-4.2.2/arm-elf/include/                         \
             -I /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/$(GCCVER)/include/       \
             -I /cygdrive/c/GNUARM-4.2.2/arm-elf/include/c++/$(GCCVER)/           \
             -I /cygdrive/c/GNUARM-4.2.2/arm-elf/include/c++/$(GCCVER)/arm-elf/   \
             -I /cygdrive/c/GNUARM-4.2.2/arm-elf/include/c++/$(GCCVER)/bits/
#============================================================================

#============================================================================
LD_LIB_PATH1 = /cygdrive/c/GNUARM-4.2.2/arm-elf/lib
LD_LIB_PATH2 = /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/$(GCCVER)
LD_LIB_PATH3 = ./ALLLIB
#============================================================================

#============================================================================
C_FLAGS   = -O2 -Wall -nostdinc -msoft-float -m$(COMP_MODE) -static         \
            -mcpu=arm920t -fomit-frame-pointer -fPIC                        \
            -funsigned-char                                                 \
            -D$(CPU_NAME)                                                   \
            -D__USE_FIXED_MEM__                                             \
            -DNAND_BLOCK_SMALL=0                                            \
            -DNAND_BLOCK_LARGE=1                                            \
            -DNAND_BLOCK_TYPE=1                                             \
            -D$(ENDIAN_MODE) $(INCLUDE)
CPP_FLAGS = $(C_FLAGS)
#============================================================================

#============================================================================
LD_SCRIPT = $(ROOT)/link_POLLUX.xn
LD_OPTION = -o $(IMAGE_ELF) -Map $(IMAGE_MAP) --oformat $(OBJ_FORMAT)  \
            -T $(LD_SCRIPT)                                            \
            -L $(LD_LIB_PATH1)                                         \
            -L $(LD_LIB_PATH2)                                         \
            -L $(LD_LIB_PATH3)                                         \
            -$(ENDIAN_MODE)  -static                                   \
            --wrap malloc                                              \
            --wrap free                                                \
            --wrap calloc                                              \
            --wrap abort                                               \
            --wrap close                                               \
            --wrap fstat                                               \
            --wrap isatty                                              \
            --wrap lseek                                               \
            --wrap read                                                \
            --wrap sbrk                                                \
            --wrap write                                               \
            --wrap fputc                                               \
            --wrap fputs                                               \
            --wrap printf                                              \
            --wrap _open_r                                             \
            --wrap _close_r                                            \
            --wrap _fstat_r                                            \
            --wrap _lseek_r                                            \
            --wrap _read_r                                             \
            --wrap _sbrk_r                                             \
            --wrap _write_r


ifeq ($(ENDIAN_MODE),EB)
   LD_LIBS = -lstdc++ -lm -lc -lgcc -l$(ZIP_LIB) -l$(FILE_LIB) -lstdc++ -lm -lc -lgcc
endif
ifeq ($(ENDIAN_MODE),EL)
   LD_LIBS = -lstdc++ -lm -lc -lgcc -l$(ZIP_LIB) -l$(FILE_LIB) -lstdc++ -lm -lc -lgcc
endif
#============================================================================

#============================================================================
# Files to be compiled
#============================================================================

OBJS   =  start_POLLUX.o          \
        DEVLIB/SysIntrPollux.o    \
        DEVLIB/NandFlash.o        \
        DEVLIB/DataBack.o         \
        DEVLIB/Timer.o            \
        DEVLIB/LcdPollux.o        \
        DEVLIB/LcdPollux640.o        \
        DEVLIB/DEVICE/Nand64.o    \
        DEVLIB/DEVICE/SysMMU.o    \
        DEVLIB/DEVICE/SysPLL.o    \
        DEVLIB/DEVICE/SysMCUS.o   \
        DEVLIB/DEVICE/SysGPIO.o   \
        DEVLIB/DEVICE/SysAlive.o  \
        DEVLIB/DEVICE/SysTimer.o  \
        DEVLIB/DEVICE/SdCardDRV.o \
        DEVLIB/DEVICE/SysSDCD.o   \
        DEVLIB/DEVICE/SysLCD.o    \
        DEVLIB/DEVICE/SysPWM.o    \
        DEVLIB/DEVICE/SysMLC.o    \
        DEVLIB/DEVICE/SysDPC.o    \
        DEVLIB/DEVICE/SysUART.o   \
        SYSLIB/SysLibPollux.o     \
        JPGLIB/jpegdecr.o         \
        JPGLIB/jpegidct.o         \
        JPGLIB/jpegh2v2.o         \
         comlib.o             \
          syslib.o             \
          keybd.o              \
          lcdcon.o             \
          screen.o             \
          sysintr.o            \
          time.o               \
          uart.o               \
          buzzer.o             \
          ship.o               \
		  mempcx.o             \
		  sendpcx.o            \
		  downprg.o            \
		  sendprg.o            \
		  flash.o              \
          Wnd.o                \
          WndMgr.o             \
		  TargetListWnd.o      \
		  TargetPlotWnd.o      \
		  TargetInfoWnd.o      \
		  OwnShipWnd.o         \
		  SRMWnd.o             \
		  MenuWnd.o            \
		  MsgMenuWnd.o         \
		  InitSetupWnd.o       \
		  MsgListWnd.o         \
		  FavorMsgWnd.o        \
		  InterrogationWnd.o   \
		  AlarmListWnd.o       \
		  StatusListWnd.o      \
		  StaticDataWnd.o      \
		  ShoreStationWnd.o	   \
		  VoyageDataWnd.o      \
		  RegionalWnd.o        \
		  RegionalViewWnd.o    \
		  LongRangeWnd.o       \
		  LongRangeMsgWnd.o    \
		  AntPosWnd.o          \
		  SystemSetupWnd.o     \
		  PortWnd.o            \
		  DisplayWnd.o         \
		  BuzzerWnd.o          \
		  PasswordSetWnd.o     \
		  PasswordCheckWnd.o   \
		  SetEtcWnd.o          \
		  InitializeWnd.o      \
		  MaintenanceMenuWnd.o \
		  ProgramVersionWnd.o  \
		  KeyTestWnd.o         \
		  LcdTestWnd.o         \
		  ViewInputDataWnd.o   \
		  SecurityLogWnd.o     \
		  TpTestMenuWnd.o      \
		  TxRxTestWnd.o        \
		  ReceiverTestWnd.o    \
		  SetTpParameterWnd.o  \
		  ProgramUploadWnd.o   \
		  EditCtrl.o           \
		  ComboCtrl.o          \
		  CheckCtrl.o          \
		  Regional.o           \
		  CnStrWnd.o           \
		  RSSListWnd.o         \
		  NMEA/Decoder.o       \
		  NMEA/NMEA.o          \
		  NMEA/Sentence.o      \
		  NMEA/ABK.o           \
		  NMEA/ABM.o           \
		  NMEA/ACA.o           \
		  NMEA/ACK.o           \
		  NMEA/ACS.o           \
		  NMEA/AIR.o           \
		  NMEA/ALR.o           \
		  NMEA/BBM.o           \
		  NMEA/LR1.o           \
		  NMEA/LR2.o           \
		  NMEA/LR3.o           \
		  NMEA/LRF.o           \
		  NMEA/LRI.o           \
		  NMEA/SSD.o           \
		  NMEA/SYC.o           \
		  NMEA/TXT.o           \
		  NMEA/VDM.o           \
		  NMEA/VDO.o           \
		  NMEA/VSD.o           \
		  NMEA/SPW.o           \
		  NMEA/TEST.o          \
		  NMEA/GSV.o           \
		  GFC/CEarthCoordinate.o \
		  GFC/CPolarCoordinate.o \
		  GFC/CEarth.o           \
		  CHART/maplib.o         \
		  CHART/mapdata.o        \
		  CHART/mapdraw.o        \
		  SMapLib/CodeSet.o      \
		  SMapLib/SMapCanvas.o   \
		  SMapLib/SMapDataBase.o \
		  SMapLib/SMapDataNCE.o  \
		  SMapLib/SMapDraw.o     \
		  SMapLib/SMapDrawMKD.o  \
		  SMapLib/SMapFunc.o     \
		  SMapLib/SMapManager.o  \
		  SMapLib/SMapMarkMgr.o  \
		  Fonts/UniCode16Font.o   \
 		  Fonts/F09N_Tahoma.o     \
 		  Fonts/F14B_Dotum_Han.o  \
 		  Fonts/F16B_Simsun_Chi.o \
		  DocMgr.o               \
          main.o


.c.o:
	$(CC) -c $(C_FLAGS) $< -o $@

.cpp.o:
	$(CC) -c $(CPP_FLAGS) $< -o $@

.S.o:
	$(CC) -c $(C_FLAGS) -D_ASSEMBLER_ $< -o $@



BASE_HDR = comlib.h sysconst.h syslib.h

start_POLLUX.o            : start_POLLUX.S

DEVLIB/SysIntrPollux.o    : $(BASE_HDR)  DEVLIB/SysIntrPollux.hpp     DEVLIB/SysIntrPollux.cpp
DEVLIB/NandFlash.o        : $(BASE_HDR)  DEVLIB/NandFlash.hpp         DEVLIB/NandFlash.cpp
DEVLIB/DataBack.o         : $(BASE_HDR)  DEVLIB/DataBack.hpp          DEVLIB/DataBack.cpp
DEVLIB/Timer.o            : $(BASE_HDR)  DEVLIB/Timer.hpp             DEVLIB/Timer.cpp
DEVLIB/LcdPollux.o        : $(BASE_HDR)  DEVLIB/LcdPollux.hpp         DEVLIB/LcdPollux.cpp
DEVLIB/LcdPollux640.o     : $(BASE_HDR)  DEVLIB/LcdPollux640.hpp      DEVLIB/LcdPollux640.cpp
DEVLIB/DEVICE/Nand64.o    : $(BASE_HDR)  DEVLIB/DEVICE/Nand64.h       DEVLIB/DEVICE/Nand64.c
DEVLIB/DEVICE/SysMMU.o    : $(BASE_HDR)  DEVLIB/DEVICE/SysMMU.h       DEVLIB/DEVICE/SysMMU.c
DEVLIB/DEVICE/SysPLL.o    : $(BASE_HDR)  DEVLIB/DEVICE/SysPLL.h       DEVLIB/DEVICE/SysPLL.c
DEVLIB/DEVICE/SysMCUS.o   : $(BASE_HDR)  DEVLIB/DEVICE/SysMCUS.h      DEVLIB/DEVICE/SysMCUS.c
DEVLIB/DEVICE/SysGPIO.o   : $(BASE_HDR)  DEVLIB/DEVICE/SysGPIO.h      DEVLIB/DEVICE/SysGPIO.c
DEVLIB/DEVICE/SysAlive.o  : $(BASE_HDR)  DEVLIB/DEVICE/SysAlive.h     DEVLIB/DEVICE/SysAlive.c
DEVLIB/DEVICE/SysTimer.o  : $(BASE_HDR)  DEVLIB/DEVICE/SysTimer.h     DEVLIB/DEVICE/SysTimer.c
DEVLIB/DEVICE/SdCardDRV.o : $(BASE_HDR)  DEVLIB/DEVICE/SdCardDRV.h    DEVLIB/DEVICE/SdCardDRV.c
DEVLIB/DEVICE/SysSDCD.o   : $(BASE_HDR)  DEVLIB/DEVICE/SysSDCD.h      DEVLIB/DEVICE/SysSDCD.c
DEVLIB/DEVICE/SysLCD.o    : $(BASE_HDR)  DEVLIB/DEVICE/SysLCD.h       DEVLIB/DEVICE/SysLCD.c
DEVLIB/DEVICE/SysPWM.o    : $(BASE_HDR)  DEVLIB/DEVICE/SysPWM.h       DEVLIB/DEVICE/SysPWM.c
DEVLIB/DEVICE/SysMLC.o    : $(BASE_HDR)  DEVLIB/DEVICE/SysMLC.h       DEVLIB/DEVICE/SysMLC.c
DEVLIB/DEVICE/SysDPC.o    : $(BASE_HDR)  DEVLIB/DEVICE/SysDPC.h       DEVLIB/DEVICE/SysDPC.c
DEVLIB/DEVICE/SysUART.o   : $(BASE_HDR)  DEVLIB/DEVICE/SysUART.h      DEVLIB/DEVICE/SysUART.c
SYSLIB/SysLibPollux.o     : $(BASE_HDR)  SYSLIB/SysLibPollux.h        SYSLIB/SysLibPollux.c
JPGLIB/jpegdecr.o    : $(BASE_HDR) JPGLIB/jpegdecr.hpp JPGLIB/jpegdecr.cpp
JPGLIB/jpegidct.o    : $(BASE_HDR) JPGLIB/jpegdecr.hpp JPGLIB/jpegidct.cpp
JPGLIB/jpegh2v2.o    : $(BASE_HDR) JPGLIB/jpegdecr.hpp JPGLIB/jpegh2v2.cpp


comlib.o             : $(BASE_HDR) comlib.h               comlib.c
syslib.o             : $(BASE_HDR) syslib.h               syslib.c

keybd.o              : $(BASE_HDR) keybd.hpp              keybd.cpp
lcdcon.o             : $(BASE_HDR) lcdcon.hpp             lcdcon.cpp
LcdPollux.o          : $(BASE_HDR) lcdcon.hpp             LcdPollux.hpp   LcdPollux.cpp
LcdPollux640.o       : $(BASE_HDR) lcdcon.hpp             LcdPollux640.hpp LcdPollux640.cpp
screen.o             : $(BASE_HDR) screen.hpp             screen.cpp
sysintr.o            : $(BASE_HDR) sysintr.hpp            sysintr.cpp
time.o               : $(BASE_HDR) sysintr.hpp            time.cpp
uart.o               : $(BASE_HDR) uart.hpp               uart.cpp
buzzer.o             : $(BASE_HDR) buzzer.hpp             buzzer.cpp
ship.o               : $(BASE_HDR) ship.hpp               ship.cpp
mempcx.o             : $(BASE_HDR) mempcx.hpp             mempcx.cpp
sendpcx.o            : $(BASE_HDR) sendpcx.hpp            sendpcx.cpp
downprg.o            : $(BASE_HDR) downprg.hpp            downprg.cpp
sendprg.o            : $(BASE_HDR) sendprg.hpp            sendprg.cpp
flash.o              : $(BASE_HDR) flash.hpp              flash.cpp
Wnd.o                : $(BASE_HDR) Wnd.hpp                Wnd.cpp
WndMgr.o             : $(BASE_HDR) WndMgr.hpp             WndMgr.cpp
TargetListWnd.o      : $(BASE_HDR) TargetListWnd.hpp      TargetListWnd.cpp
TargetPlotWnd.o      : $(BASE_HDR) TargetPlotWnd.hpp      TargetPlotWnd.cpp
TargetInfoWnd.o      : $(BASE_HDR) TargetInfoWnd.hpp      TargetInfoWnd.cpp
OwnShipWnd.o         : $(BASE_HDR) OwnShipWnd.hpp         OwnShipWnd.cpp
SRMWnd.o             : $(BASE_HDR) SRMWnd.hpp             SRMWnd.cpp
MenuWnd.o            : $(BASE_HDR) MenuWnd.hpp            MenuWnd.cpp
MsgMenuWnd.o         : $(BASE_HDR) MsgMenuWnd.hpp         MsgMenuWnd.cpp
MsgMenuWnd.o         : $(BASE_HDR) MsgMenuWnd.hpp         MsgMenuWnd.cpp
InitSetupWnd.o       : $(BASE_HDR) InitSetupWnd.hpp       InitSetupWnd.cpp
FavorMsgWnd.o        : $(BASE_HDR) FavorMsgWnd.hpp        FavorMsgWnd.cpp
InterrogationWnd.o   : $(BASE_HDR) InterrogationWnd.hpp   InterrogationWnd.cpp
AlarmListWnd.o       : $(BASE_HDR) AlarmListWnd.hpp       AlarmListWnd.cpp
StatusListWnd.o      : $(BASE_HDR) StatusListWnd.hpp      StatusListWnd.cpp
ShoreStationWnd.o    : $(BASE_HDR) ShoreStationWnd.hpp    ShoreStationWnd.cpp
StaticDataWnd.o      : $(BASE_HDR) StaticDataWnd.hpp      StaticDataWnd.cpp
VoyageDataWnd.o      : $(BASE_HDR) VoyageDataWnd.hpp      VoyageDataWnd.cpp
RegionalWnd.o        : $(BASE_HDR) RegionalWnd.hpp        RegionalWnd.cpp
RegionalViewWnd.o    : $(BASE_HDR) RegionalViewWnd.hpp    RegionalViewWnd.cpp
LongRangeWnd.o       : $(BASE_HDR) LongRangeWnd.hpp       LongRangeWnd.cpp
LongRangeMsgWnd.o    : $(BASE_HDR) LongRangeMsgWnd.hpp    LongRangeMsgWnd.cpp
AntPosWnd.o          : $(BASE_HDR) AntPosWnd.hpp          AntPosWnd.cpp
SystemSetupWnd.o     : $(BASE_HDR) SystemSetupWnd.hpp     SystemSetupWnd.cpp
PortWnd.o            : $(BASE_HDR) PortWnd.hpp            PortWnd.cpp
DisplayWnd.o         : $(BASE_HDR) DisplayWnd.hpp         DisplayWnd.cpp
BuzzerWnd.o          : $(BASE_HDR) BuzzerWnd.hpp          BuzzerWnd.cpp
PasswordSetWnd.o     : $(BASE_HDR) PasswordSetWnd.hpp     PasswordSetWnd.cpp
PasswordCheckWnd.o   : $(BASE_HDR) PasswordCheckWnd.hpp   PasswordCheckWnd.cpp
SetEtcWnd.o          : $(BASE_HDR) SetEtcWnd.hpp          SetEtcWnd.cpp
InitializeWnd.o      : $(BASE_HDR) InitializeWnd.hpp      InitializeWnd.cpp
MaintenanceMenuWnd.o : $(BASE_HDR) MaintenanceMenuWnd.hpp MaintenanceMenuWnd.cpp
ProgramVersionWnd.o  : $(BASE_HDR) ProgramVersionWnd.hpp  ProgramVersionWnd.cpp
KeyTestWnd.o         : $(BASE_HDR) KeyTestWnd.hpp         KeyTestWnd.cpp
LcdTestWnd.o         : $(BASE_HDR) LcdTestWnd.hpp         LcdTestWnd.cpp
ViewInputData.o      : $(BASE_HDR) ViewInputData.hpp      ViewInputData.cpp
SecurityLogWnd.o     : $(BASE_HDR) SecurityLogWnd.hpp     SecurityLogWnd.cpp
TpTestMenuWnd.o      : $(BASE_HDR) TpTestMenuWnd.hpp      TpTestMenuWnd.cpp
TxRxTestWnd.o        : $(BASE_HDR) TxRxTestWnd.hpp        TxRxTestWnd.cpp
ReceiverTestWnd.o    : $(BASE_HDR) ReceiverTestWnd.hpp    ReceiverTestWnd.cpp
SetTpParameterWnd.o  : $(BASE_HDR) SetTpParameterWnd.hpp  SetTpParameterWnd.cpp
ProgramUploadWnd.o   : $(BASE_HDR) ProgramUploadWnd.hpp   ProgramUploadWnd.cpp
EditCtrl.o           : $(BASE_HDR) EditCtrl.hpp           EditCtrl.cpp
ComboCtrl.o          : $(BASE_HDR) ComboCtrl.hpp          ComboCtrl.cpp
CheckCtrl.o          : $(BASE_HDR) CheckCtrl.hpp          CheckCtrl.cpp
Regional.o           : $(BASE_HDR) Regional.hpp           Regional.cpp
CnStrWnd.o           : $(BASE_HDR) CnStrWnd.hpp           CnStrWnd.cpp
RSSListWnd.o         : $(BASE_HDR) RSSListWnd.hpp         RSSListWnd.cpp
NMEA/Decoder.o       : $(BASE_HDR) NMEA/Decoder.hpp       NMEA/Decoder.cpp
NMEA/NMEA.o          : $(BASE_HDR) NMEA/NMEA.hpp          NMEA/NMEA.cpp
NMEA/Sentence.o      : $(BASE_HDR) NMEA/Sentence.hpp      NMEA/Sentence.cpp
NMEA/ABK.o           : $(BASE_HDR) NMEA/ABK.hpp           NMEA/ABK.cpp
NMEA/ABM.o           : $(BASE_HDR) NMEA/ABM.hpp           NMEA/ABM.cpp
NMEA/ACA.o           : $(BASE_HDR) NMEA/ACA.hpp           NMEA/ACA.cpp
NMEA/ACK.o           : $(BASE_HDR) NMEA/ACK.hpp           NMEA/ACK.cpp
NMEA/ACS.o           : $(BASE_HDR) NMEA/ACS.hpp           NMEA/ACS.cpp
NMEA/AIR.o           : $(BASE_HDR) NMEA/AIR.hpp           NMEA/AIR.cpp
NMEA/ALR.o           : $(BASE_HDR) NMEA/ALR.hpp           NMEA/ALR.cpp
NMEA/BBM.o           : $(BASE_HDR) NMEA/BBM.hpp           NMEA/BBM.cpp
NMEA/LR1.o           : $(BASE_HDR) NMEA/LR1.hpp           NMEA/LR1.cpp
NMEA/LR2.o           : $(BASE_HDR) NMEA/LR2.hpp           NMEA/LR2.cpp
NMEA/LR3.o           : $(BASE_HDR) NMEA/LR3.hpp           NMEA/LR3.cpp
NMEA/LRF.o           : $(BASE_HDR) NMEA/LRF.hpp           NMEA/LRF.cpp
NMEA/LRI.o           : $(BASE_HDR) NMEA/LRI.hpp           NMEA/LRI.cpp
NMEA/SSD.o           : $(BASE_HDR) NMEA/SSD.hpp           NMEA/SSD.cpp
NMEA/SYC.o           : $(BASE_HDR) NMEA/SYC.hpp           NMEA/SYC.cpp
NMEA/TXT.o           : $(BASE_HDR) NMEA/TXT.hpp           NMEA/TXT.cpp
NMEA/VDM.o           : $(BASE_HDR) NMEA/VDM.hpp           NMEA/VDM.cpp
NMEA/VDO.o           : $(BASE_HDR) NMEA/VDO.hpp           NMEA/VDO.cpp
NMEA/VSD.o           : $(BASE_HDR) NMEA/VSD.hpp           NMEA/VSD.cpp
NMEA/SPW.o           : $(BASE_HDR) NMEA/SPW.hpp           NMEA/SPW.cpp
NMEA/TEST.o          : $(BASE_HDR) NMEA/TEST.hpp          NMEA/TEST.cpp
NMEA/GSV.o           : $(BASE_HDR) NMEA/GSV.h             NMEA/GSV.cpp
GFC/CEarthCoordinate.o : $(BASE_HDR) GFC/GFC.h              GFC/CEarthCoordinate.cpp
GFC/CPolarCoordinate.o : $(BASE_HDR) GFC/GFC.h              GFC/CPolarCoordinate.cpp
GFC/CEarth.o           : $(BASE_HDR) GFC/CEarth.hpp         GFC/CEarth.cpp
CHART/maplib.o         : $(BASE_HDR) CHART/maptypes.hpp     CHART/maplib.hpp CHART/maplib.cpp
CHART/mapdata.o        : $(BASE_HDR) CHART/maptypes.hpp     CHART/maplib.hpp CHART/mapdata.hpp CHART/mapdata.cpp
CHART/mapdraw.o        : $(BASE_HDR) CHART/maptypes.hpp     CHART/maplib.hpp CHART/mapdraw.hpp CHART/mapdraw.cpp
SMapLib/CodeSet.o      : $(BASE_HDR) SMapLib/CodeSet.h      SMapLib/CodeSet.cpp
SMapLib/SMapCanvas.o   : $(BASE_HDR) SMapLib/SMapCanvas.h   SMapLib/SMapCanvas.cpp
SMapLib/SMapDataBase.o : $(BASE_HDR) SMapLib/SMapDataBase.h SMapLib/SMapDataBase.cpp
SMapLib/SMapDataNCE.o  : $(BASE_HDR) SMapLib/SMapDataNCE.h  SMapLib/SMapDataNCE.cpp
SMapLib/SMapDraw.o     : $(BASE_HDR) SMapLib/SMapDraw.h     SMapLib/SMapDraw.cpp
SMapLib/SMapDrawMKD.o  : $(BASE_HDR) SMapLib/SMapDrawMKD.h  SMapLib/SMapDrawMKD.cpp
SMapLib/SMapFunc.o     : $(BASE_HDR) SMapLib/SMapFunc.h     SMapLib/SMapFunc.cpp
SMapLib/SMapManager.o  : $(BASE_HDR) SMapLib/SMapManager.h  SMapLib/SMapManager.cpp
SMapLib/SMapMarkMgr.o  : $(BASE_HDR) SMapLib/SMapMarkMgr.h  SMapLib/SMapMarkMgr.cpp
Fonts/UniCode16Font.o  : $(BASE_HDR) Fonts/UniCode16Font.cpp
Fonts/F09N_Tahoma.o    : $(BASE_HDR) Fonts/F09N_Tahoma.cpp
Fonts/F14B_Dotum_Han.o : $(BASE_HDR) Fonts/F14B_Dotum_Han.cpp
Fonts/F16B_Simsun_Chi.o : $(BASE_HDR) Fonts/F16B_Simsun_Chi.cpp
DocMgr.o             : $(BAES_HDR) DocMgr.hpp             DocMgr.cpp
main.o               : $(BASE_HDR) main.hpp               main.cpp


#============================================================================
# Rules   
#============================================================================
          
rebuild: clean all

all: $(OBJS)
	$(LD) $(LD_OPTION) $(LD_LIBS) $(OBJS) $(LD_LIBS)
	$(OC) -O binary $(IMAGE_ELF) $(IMAGE_BIN)

clean :
	rm -f *.o
	rm -f NMEA/*.o
	rm -f GFC/*.o
	rm -f CHART/*.o
	rm -f SMapLib/*.o
	rm -f Fonts/*.o
	rm -f SYSLIB/*.o
	rm -f DEVLIB/*.o
	rm -f DEVLIB/DEVICE/*.o
	rm -f JPGLIB/*.o
	rm -f *.map
	rm -f *.elf
	rm -f *.bin
	rm -f *.rec
	rm -f *.dis
#rm -f $(BINDIR)\depend.mk

