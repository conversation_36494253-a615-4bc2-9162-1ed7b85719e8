/*...........................................................................*/
/*.                  File Name : SYSMCUS.C                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysMCUS.h"

#include <string.h>

//=============================================================================
static xSYS_MCUS *G_pSysMCUS = (xSYS_MCUS *)MCUS_PHSY_BASE_ADDR;
//=============================================================================

void  SysInitNAND(void)
{
      volatile DWORD *pNandCTRL = (volatile DWORD *)(NAND_CTRL_PHSY_BASE_ADDR + NAND_NFCONTROL);

      *pNandCTRL &= ~(1 <<  8);   // IRQ disable
      *pNandCTRL |=  (1 << 15);   // IRQ clear
      *pNandCTRL &= ~(1 <<  0);   // NFBANK = nNCS[0]

  #if defined(__N500_MODEL__)
//    SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTACS[1]) , 11 - 8, 1);
//    SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOS[1]) , 11 - 8, 4);   //  3
//    SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTACC[2]) , 11 - 8,24);   // 10
//    SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOH[1]) , 11 - 8, 4);   //  3
//    SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCAH[1]) , 11 - 8, 4);   //  3

      SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTACS[1]) , 11 - 8, 1);
      SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOS[1]) , 11 - 8, 3);   //  3
      SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTACC[2]) , 11 - 8,15);   // 10
      SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOH[1]) , 11 - 8, 3);   //  3
      SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCAH[1]) , 11 - 8, 3);   //  3
  #endif
}
void  SysInitMCUS(int nBitWidth)
{
      if (nBitWidth == 8)
         {
          SysSetMCUsCommon(MCUS_FPGA_BUS_NO,MCUS_BUS_WIDTH_08,MCUS_TACS_3CYCLES,MCUS_TCOS_3CYCLES,0xf,0x0f,MCUS_TCOH_3CYCLES,MCUS_TCAH_3CYCLES,MCUS_BURST_DISABLE,MCUS_WAIT_ENABLE ,MCUS_WAIT_POL_LOW);
          SysSetMCUsCommon(MCUS_LAN_BUS_NO ,MCUS_BUS_WIDTH_16,MCUS_TACS_3CYCLES,MCUS_TCOS_3CYCLES,0xf,0x0f,MCUS_TCOH_3CYCLES,MCUS_TCAH_3CYCLES,MCUS_BURST_DISABLE,MCUS_WAIT_ENABLE ,MCUS_WAIT_POL_LOW);
         }
      else
         {
          SysSetMCUsCommon(MCUS_FPGA_BUS_NO,MCUS_BUS_WIDTH_16,MCUS_TACS_3CYCLES,MCUS_TCOS_3CYCLES,0xf,0x0f,MCUS_TCOH_3CYCLES,MCUS_TCAH_3CYCLES,MCUS_BURST_DISABLE,MCUS_WAIT_ENABLE ,MCUS_WAIT_POL_LOW);
          SysSetMCUsCommon(MCUS_LAN_BUS_NO ,MCUS_BUS_WIDTH_16,MCUS_TACS_3CYCLES,MCUS_TCOS_3CYCLES,0xf,0x0f,MCUS_TCOH_3CYCLES,MCUS_TCAH_3CYCLES,MCUS_BURST_DISABLE,MCUS_WAIT_ENABLE ,MCUS_WAIT_POL_LOW);
         }
}
void  SysSetMCUsCommon(int nNo,DWORD dBusWidth,DWORD dTACS,DWORD dTCOS,DWORD dTACC,DWORD dTSACC,DWORD dTCOH,DWORD dTCAH,DWORD dBurstMode,DWORD dWaitMode,DWORD dWaitPol)
{
      if (nNo < 8)       SysSetMCUsBit1Data(&(G_pSysMCUS->dMEMBW)      ,nNo,dBusWidth);

      if (nNo < 8)       SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTACS[0]) ,nNo - 0,dTACS);
      else               SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTACS[1]) ,nNo - 8,dTACS);

      if (nNo < 8)       SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOS[0]) ,nNo - 0,dTCOS);
      else               SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOS[1]) ,nNo - 8,dTCOS);

      if (nNo < 4)       SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTACC[0]) ,nNo - 0,dTACC);
      else if (nNo < 8)  SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTACC[1]) ,nNo - 4,dTACC);
           else          SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTACC[2]) ,nNo - 8,dTACC);

      if (nNo < 4)       SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTSACC[0]),nNo - 0,dTSACC);
      else if (nNo < 8)  SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTSACC[1]),nNo - 4,dTSACC);
           else          SysSetMCUsBit8Data(&(G_pSysMCUS->dMEMTSACC[2]),nNo - 8,dTSACC);

      if (nNo < 8)       SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOH[0])  ,nNo - 0,dTCOH);
      else               SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCOH[1])  ,nNo - 8,dTCOH);

      if (nNo < 8)       SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCAH[0])  ,nNo - 0,dTCAH);
      else               SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMTCAH[1])  ,nNo - 8,dTCAH);

      if (nNo < 8)       SysSetMCUsBit4Data(&(G_pSysMCUS->dMEMBURST),nNo,(dBurstMode << 2) | dBurstMode);

      SysSetMCUsBit2Data(&(G_pSysMCUS->dMEMWAIT)  ,nNo,(dWaitMode  << 1) | dWaitPol);
}
void  SysSetMCUsBit1Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData)
{
      DWORD dMaskX;

      dMaskX = 1 << nBitNo;
      if (dBitData)
          *pMemX |= dMaskX;
      else
          *pMemX &= (~dMaskX);
}
void  SysSetMCUsBit2Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData)
{
      DWORD dMaskX;
      int   nShift;

      nShift= nBitNo * 2;
      dMaskX = 3 << nShift;
      *pMemX = (*pMemX & ~dMaskX) | (dBitData << nShift);
}
void  SysSetMCUsBit4Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData)
{
      DWORD dMaskX;
      int   nShift;

      nShift= nBitNo * 4;
      dMaskX = 15 << nShift;
      *pMemX = (*pMemX & ~dMaskX) | (dBitData << nShift);
}

void  SysSetMCUsBit8Data(volatile DWORD *pMemX,int nBitNo,DWORD dBitData)
{
      DWORD dMaskX;
      int   nShift;

      nShift= nBitNo * 8;
      dMaskX = 255 << nShift;
      *pMemX = (*pMemX & ~dMaskX) | (dBitData << nShift);
}
