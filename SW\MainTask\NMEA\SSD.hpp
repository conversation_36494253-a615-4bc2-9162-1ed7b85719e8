#include "Sentence.hpp"

#ifndef __SSD_HPP__
#define __SSD_HPP__

/******************************************************************************
 * 
 * SSD - Ship Static Data
 *
 * $--SSD,c--c,c--c,xxx,xxx,xx,xx,c,aa*hh<CR><LF>
 *         |    |    |   |  |  |  | | 
 *         1    2    3   4  5  6  7 8
 *
 * 1. Ship's Call Sign, 1 to 7 characters
 * 2. Ship's Name, 1 to 20 characters
 * 3. Pos. ref. point distance, "A," from bow, 0 to 511 meters
 * 4. Pos. ref., "B," from stern, 0 to 511 meters
 * 5. Pos. ref., "C," from port beam, 0 to 63 meters
 * 6. Pos. ref., "D," from starboard beam, 0 to 63 meters
 * 7. DTE indicator flag
 * 8. Source identifier
 *
 * Add talkerId check : HSI 2012.04.26
 *
 ******************************************************************************/
class CSsd : public CSentence {
protected:
	char m_szTalkID[10];		  // 2 + 1(NULL)s
	BYTE m_szCallSign[100];  // 7 + 1(NULL)
	BYTE m_szName[100];      // 20 + 1(NULL)
	int  m_nPosA;
	int  m_nPosB;
	int  m_nPosC;
	int  m_nPosD;
	int  m_nDTE;
	char m_szSourceID[20];
    
public:
    CSsd();
    CSsd(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);
	int  MakeSentence(BYTE *pszSentence);
	
	void SetCallSign(const BYTE *pszCallSign) { strcpy((char *)m_szCallSign, (char *)pszCallSign); }
	void SetName(const BYTE *pszShipName) { strcpy((char *)m_szName, (char *)pszShipName); }
	void SetPosA(int nPos) { m_nPosA = nPos; }
	void SetPosB(int nPos) { m_nPosB = nPos; }
	void SetPosC(int nPos) { m_nPosC = nPos; }
	void SetPosD(int nPos) { m_nPosD = nPos; }
	void SetDTE(int nDTE)  { m_nDTE  = nDTE; }
	void SetSourceID(const BYTE *pszSourceID) { strcpy((char *)m_szSourceID, (char *)pszSourceID); }

	void GetTalkID(char *pszDest) { strcpy((char *)pszDest, (char *)m_szTalkID); }
	void GetCallSign(char *pszCallSign) { strcpy((char *)pszCallSign, (char *)m_szCallSign); }
	void GetName(char *pszName)         { strcpy((char *)pszName, (char *)m_szName); }
	int  GetPosA() { return m_nPosA; }
	int  GetPosB() { return m_nPosB; }
	int  GetPosC() { return m_nPosC; }
	int  GetPosD() { return m_nPosD; }
	int  GetDTE()  { return m_nDTE;  }
	void GetSourceID(char *pszSourceID) { strcpy(pszSourceID, m_szSourceID); }
};

#endif

