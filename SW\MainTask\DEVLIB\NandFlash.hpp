/*...........................................................................*/
/*.                  File Name : NANDFLASH.HPP                              .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.10                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "syslib.h"

#ifndef  __NANDFLASH_HPP__
#define  __NANDFLASH_HPP__

//=============================================================================
//#define  NAND_BLOCK_SMALL                                    0
//#define  NAND_BLOCK_LARGE                                    1
//#define  NAND_BLOCK_TYPE                     (NAND_BLOCK_SMALL)
//-----------------------------------------------------------------------------
#if  NAND_BLOCK_TYPE == NAND_BLOCK_SMALL
//-----------------------------------------------------------------------------
     #define  NAND_BYTES_TO_SECT_A                         256
     #define  NAND_BYTES_TO_SECT_B                         512
     #define  NAND_BYTES_TO_SECT_C                         528
//-----------------------------------------------------------------------------
     #define  NAND_BYTES_PER_PAGE                          512
     #define  NAND_EXTRA_PER_PAGE                           16
     #define  NAND_TOTAL_PER_PAGE       (NAND_BYTES_PER_PAGE + NAND_EXTRA_PER_PAGE)
//-----------------------------------------------------------------------------
     #define  NAND_PAGES_PER_BLOCK                          32
     #define  NAND_TOTAL_BLOCK_NO                         4096
     #define  NAND_COL_ADDR_MASK                    0x000000ff
     #define  NAND_ROW_ADDR_MASK                    0xffffff00
     #define  NAND_BLOCK_VALID_COL_ADDR                    517
     #define  NAND_BLOCK_VALID_INFO_SAVE_PAGE     (NAND_PAGES_PER_BLOCK - 1)
     #define  NAND_BLOCK_VALID_INFO_DIVR                     8
     #define  NAND_BLOCK_VALID_INFO_MASK                   0x7
//-----------------------------------------------------------------------------
     #define  NAND_BLOCK_SIZE                       (16 * 1024)
     #define  NAND_BLOCK_MASK                           0x3fff
     #define  NAND_VALID_BLOCK_INFO_SIZE     (NAND_TOTAL_BLOCK_NO / NAND_BLOCK_VALID_INFO_DIVR)
     #define  NAND_BLOCK_GOOD_COUNT_SIZE     (NAND_TOTAL_BLOCK_NO)
//-----------------------------------------------------------------------------
     #define  NAND_COL_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
     #define  NAND_ROW_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
     #define  NAND_ROW_ADDR1(ADDR)  ((UCHAR)((ADDR >> 8) & 0x000000ff))
     #define  NAND_ROW_ADDR2(ADDR)  ((UCHAR)((ADDR >>16) & 0x000000ff))
//-----------------------------------------------------------------------------
#else
//-----------------------------------------------------------------------------
#define  NAND_BYTES_PER_PAGE                         2048
#define  NAND_EXTRA_PER_PAGE                           64
#define  NAND_TOTAL_PER_PAGE       (NAND_BYTES_PER_PAGE + NAND_EXTRA_PER_PAGE)
//-----------------------------------------------------------------------------
#define  NAND_PAGES_PER_BLOCK                          64
#define  NAND_TOTAL_BLOCK_NO                         1024
#define  NAND_COL_ADDR_MASK                        0x0fff
#define  NAND_ROW_ADDR_MASK                        0xffff
#define  NAND_BLOCK_VALID_COL_ADDR                   2048
#define  NAND_BLOCK_VALID_INFO_SAVE_PAGE     (NAND_PAGES_PER_BLOCK - 1)
#define  NAND_BLOCK_VALID_INFO_DIVR                     8
#define  NAND_BLOCK_VALID_INFO_MASK                   0x7
//-----------------------------------------------------------------------------
#define  NAND_BLOCK_SIZE                      (128 * 1024)
#define  NAND_BLOCK_MASK                          0x1ffff
#define  NAND_VALID_BLOCK_INFO_SIZE                  2048  // (NAND_TOTAL_BLOCK_NO / NAND_BLOCK_VALID_INFO_DIVR)
#define  NAND_BLOCK_GOOD_COUNT_SIZE     (NAND_TOTAL_BLOCK_NO)
//-----------------------------------------------------------------------------
#define  NAND_COL_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
#define  NAND_COL_ADDR1(ADDR)  ((UCHAR)((ADDR >> 8) & 0x000000ff))
#define  NAND_ROW_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
#define  NAND_ROW_ADDR1(ADDR)  ((UCHAR)((ADDR >> 8) & 0x000000ff))
//-----------------------------------------------------------------------------
     #define  ECCX_BYTES_PER_BLOCK                         256
//   #define  ECCX_BYTES_PER_BLOCK                         512

     #define  ECCX_BLOCK_COUNT_NO   (NAND_BYTES_PER_PAGE / ECCX_BYTES_PER_BLOCK)
     #define  ECCX_CODE_COUNT_NO                             3
     #define  ECCX_SIGN_DATA                              (0xaa)
 #if ECCX_BYTES_PER_BLOCK == (512)
     #define  ECCX_SIGN_POS0        (NAND_BYTES_PER_PAGE + 0x02)
     #define  ECCX_DATA_POS0        (NAND_BYTES_PER_PAGE + 0x04)
     #define  ECCX_SIGN_POS1        (NAND_BYTES_PER_PAGE + 0x12)
     #define  ECCX_DATA_POS1        (NAND_BYTES_PER_PAGE + 0x14)
 #else
     #define  ECCX_SIGN_POS0        (NAND_BYTES_PER_PAGE + 0x02)
     #define  ECCX_DATA_POS0        (NAND_BYTES_PER_PAGE + 0x04)
     #define  ECCX_SIGN_POS1        (NAND_BYTES_PER_PAGE + 0x22)
     #define  ECCX_DATA_POS1        (NAND_BYTES_PER_PAGE + 0x24)
 #endif
//-----------------------------------------------------------------------------
#endif
//=============================================================================
#if  NAND_BLOCK_TYPE == NAND_BLOCK_SMALL
     #define  NAND_CMD_RESET                              0xff
     #define  NAND_CMD_ID                                 0x90
     #define	NAND_PAGE_READ                              0x00
     #define	NAND_PAGE_READA                             0x00
     #define	NAND_PAGE_READB                             0x01
     #define	NAND_PAGE_READC                             0x50
     #define	NAND_PAGE_WRITE1                            0x80
     #define	NAND_PAGE_WRITE2                            0x10
     #define	NAND_BLOCK_ERASE1                           0x60
     #define	NAND_BLOCK_ERASE2                           0xD0
     #define	NAND_READ_STATUS                            0x70
#else
#define NAND_CMD_RESET                              0xff
#define NAND_CMD_ID                                 0x90
#define	NAND_PAGE_READ                              0x00
#define	NAND_PAGE_READ1                             0x00
#define	NAND_PAGE_READ2                             0x30
#define	NAND_PAGE_WRITE1                            0x80
#define	NAND_PAGE_WRITE2                            0x10
#define	NAND_PART_READ1                             0x05
#define	NAND_PART_READ2                             0xe0
#define	NAND_PART_WRITE1                            0x85
#define	NAND_PART_WRITE2                            0x10
#define	NAND_BLOCK_ERASE1                           0x60
#define	NAND_BLOCK_ERASE2                           0xD0
#define	NAND_READ_STATUS                            0x70
#define NAND_UNLOCK_BEG                             0x23
#define NAND_UNLOCK_END                             0x24
#endif
//=============================================================================
#define NAND_STATUS_READY                           0x40 // Ready
#define NAND_STATUS_ERROR                           0x01 //  Error
//=============================================================================

//=============================================================================
class cNandFlash
{
   protected:
      volatile UCHAR *G_pNandDATA;
      volatile UCHAR *G_pNandCMND;
      volatile UCHAR *G_pNandADDR;

      int     m_nBytesPerPage;
      int     m_nExtraPerPage;

      int     m_nPagesPerBlock;
      int     m_nTotalBlockNo;

      DWORD   m_dColAddrMask;
      DWORD   m_dRowAddrMask;

      DWORD   m_dBlockSize;
      DWORD   m_dBlockMask;

      DWORD   m_dBlockValidInfoDivr;
      DWORD   m_dBlockValidInfoMask;
      DWORD   m_dValidBlockInfoSize;
      DWORD   m_dBlockGoodCountSize;

      UCHAR  *G_pValidBlockInfo;
      int    *G_pBlockGoodCount;

   public:
      cNandFlash(void);
      virtual ~cNandFlash(void);

   public:
      void  SetBytesPerPage(int nBytesPerPage);
      int   GetBytesPerPage(void);
      void  SetExtraPerPage(int nExtraPerPage);
      int   GetExtraPerPage(void);
      int   GetBytesPerBlock(void);
      void  SetPagesPerBlock(int nPagesPerBlock);
      int   GetPagesPerBlock(void);
      void  SetTotalBlockNo(int nTotalBlockNo);
      int   GetTotalBlockNo(void);
      void  SetColAddrMask(DWORD dMask);
      DWORD GetColAddrMask(void);
      void  SetRowAddrMask(DWORD dMask);
      DWORD GetRowAddrMask(void);
      void  SetBlockSize(DWORD dSize);
      DWORD GetBlockSize(void);
      void  SetBlockMask(DWORD dMask);
      DWORD GetBlockMask(void);
      void  SetBlockValidInfoDivr(DWORD dDivr);
      DWORD GetBlockValidInfoDivr(void);
      void  SetBlockValidInfoMask(DWORD dMask);
      DWORD GetBlockValidInfoMask(void);
      void  SetValidBlockInfoSize(DWORD dSize);
      DWORD GetValidBlockInfoSize(void);
      void  SetBlockGoodCountSize(DWORD dSize);
      DWORD GetBlockGoodCountSize(void);

      virtual DWORD GetPhyNandAddr(DWORD dBlockNo,DWORD dPageNo) = 0;
      virtual DWORD GetNandColAddr(DWORD dPhyAddr) = 0;
      virtual DWORD GetNandRowAddr(DWORD dPhyAddr) = 0;
      virtual int   MakeValidBlockInfo(int nReBuildMode,int nAlwaysGoods,UCHAR *pInfoAddr) = 0;
      virtual int   IsValidBlock(int nBlockNo) = 0;
      virtual void  MakeBlockAddress(void) = 0;
      virtual void  SetBlockToBadBlock(DWORD dCpuAddr,int nWriteMode) = 0;
      virtual void  WriteZeroBlock(void) = 0;
      virtual DWORD ConvertCpuAddrToVirNand(DWORD dCpuAddr) = 0;
      virtual DWORD ConvertVirNandAddrToPhyNand(DWORD dVirNandAddr) = 0;
      virtual void  ReadNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize) = 0;
      virtual void  SaveNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize) = 0;
      virtual void  WriteNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize)= 0;
      virtual void  EraseNandFlashByCpuAddr(DWORD dCpuAddr,DWORD dSize) = 0;
      virtual void  ReadNandBlockData(UCHAR *pData,DWORD dBlockNo) = 0;
      virtual void  ReadNandPageData(UCHAR *pData,DWORD dBlockNo,DWORD dPageNo,DWORD dOffset,int nSize) = 0;
      virtual void  WriteNandBlockData(UCHAR *pData,DWORD dBlockNo) = 0;
      virtual void  WriteNandPageData(UCHAR *pData,DWORD dBlockNo,DWORD dPageNo,DWORD dOffset,int nSize) = 0;
      virtual DWORD EraseOneFlashBlockNoWaitStart(DWORD dBlockAddr) = 0;
      virtual void  ResetFlash(void) = 0;
      virtual DWORD WaitFlashReady(void) = 0;
      virtual void  WaitBusy(void) = 0;
      virtual DWORD ReadFlashDeviceCode(void) = 0;
      virtual int   CheckFlashID(void) = 0;
      virtual void  EnableWriteProtect(void) = 0;
      virtual void  DisableWriteProtect(void)= 0;
};
//=============================================================================

//=============================================================================
class cNandFlashSYS : public cNandFlash
{
   protected:

   public:
      cNandFlashSYS(void);
      virtual ~cNandFlashSYS(void);

   public:
      virtual DWORD GetPhyNandAddr(DWORD dBlockNo,DWORD dPageNo);
      virtual DWORD GetNandColAddr(DWORD dPhyAddr);
      virtual DWORD GetNandRowAddr(DWORD dPhyAddr);
      virtual int   MakeValidBlockInfo(int nReBuildMode,int nAlwaysGoods,UCHAR *pInfoAddr);
      virtual int   IsValidBlock(int nBlockNo);
      virtual void  MakeBlockAddress(void);
      virtual void  SetBlockToBadBlock(DWORD dCpuAddr,int nWriteMode);
      virtual void  WriteZeroBlock(void);
      virtual DWORD ConvertCpuAddrToVirNand(DWORD dCpuAddr);
      virtual DWORD ConvertVirNandAddrToPhyNand(DWORD dVirNandAddr);
      virtual void  ReadNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize);
      virtual void  SaveNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize);
      virtual void  WriteNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize);
      virtual void  EraseNandFlashByCpuAddr(DWORD dCpuAddr,DWORD dSize);
      virtual void  ReadNandBlockData(UCHAR *pData,DWORD dBlockNo);
      virtual void  ReadNandPageData(UCHAR *pData,DWORD dBlockNo,DWORD dPageNo,DWORD dOffset,int nSize);
      virtual void  WriteNandBlockData(UCHAR *pData,DWORD dBlockNo);
      virtual void  WriteNandPageData(UCHAR *pData,DWORD dBlockNo,DWORD dPageNo,DWORD dOffset,int nSize);
      virtual DWORD EraseOneFlashBlockNoWaitStart(DWORD dBlockAddr);
      virtual void  ResetFlash(void);
      virtual DWORD WaitFlashReady(void);
      virtual void  WaitBusy(void);
      virtual DWORD ReadFlashDeviceCode(void);
      virtual int   CheckFlashID(void);
      virtual void  EnableWriteProtect(void);
      virtual void  DisableWriteProtect(void);
};
//=============================================================================
#endif

