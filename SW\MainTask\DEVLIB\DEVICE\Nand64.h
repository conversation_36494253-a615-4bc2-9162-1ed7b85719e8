/*...........................................................................*/
/*.                  File Name : NAND64.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.28                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __NAND64_H__
#define  __NAND64_H__

//=============================================================================
//#define  NAND_BLOCK_SMALL                                    0
//#define  NAND_BLOCK_LARGE                                    1
//#define  NAND_BLOCK_TYPE                     (NAND_BLOCK_SMALL)
//-----------------------------------------------------------------------------
#if  NAND_BLOCK_TYPE == NAND_BLOCK_SMALL
//-----------------------------------------------------------------------------
     #define  NAND_BYTES_TO_SECT_A                         256
     #define  NAND_BYTES_TO_SECT_B                         512
     #define  NAND_BYTES_TO_SECT_C                         528
//-----------------------------------------------------------------------------
     #define  NAND_BYTES_PER_PAGE                          512
     #define  NAND_EXTRA_PER_PAGE                           16
     #define  NAND_TOTAL_PER_PAGE       (NAND_BYTES_PER_PAGE + NAND_EXTRA_PER_PAGE)
//-----------------------------------------------------------------------------
     #define  NAND_PAGES_PER_BLOCK                          32
     #define  NAND_TOTAL_BLOCK_NO                         4096
     #define  NAND_COL_ADDR_MASK                    0x000000ff
     #define  NAND_ROW_ADDR_MASK                    0xffffff00
     #define  NAND_BLOCK_VALID_COL_ADDR                    517
     #define  NAND_BLOCK_VALID_INFO_SAVE_PAGE     (NAND_PAGES_PER_BLOCK - 1)
     #define  NAND_BLOCK_VALID_INFO_DIVR                     8
     #define  NAND_BLOCK_VALID_INFO_MASK                   0x7
//-----------------------------------------------------------------------------
     #define  NAND_BLOCK_SIZE                       (16 * 1024)
     #define  NAND_BLOCK_MASK                           0x3fff
     #define  NAND_VALID_BLOCK_INFO_SIZE     (NAND_TOTAL_BLOCK_NO / NAND_BLOCK_VALID_INFO_DIVR)
     #define  NAND_BLOCK_GOOD_COUNT_SIZE     (NAND_TOTAL_BLOCK_NO)
//-----------------------------------------------------------------------------
     #define  NAND_COL_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
     #define  NAND_ROW_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
     #define  NAND_ROW_ADDR1(ADDR)  ((UCHAR)((ADDR >> 8) & 0x000000ff))
     #define  NAND_ROW_ADDR2(ADDR)  ((UCHAR)((ADDR >>16) & 0x000000ff))
//-----------------------------------------------------------------------------
#else
//-----------------------------------------------------------------------------
     #define  NAND_BYTES_PER_PAGE                         2048
     #define  NAND_EXTRA_PER_PAGE                           64
     #define  NAND_TOTAL_PER_PAGE       (NAND_BYTES_PER_PAGE + NAND_EXTRA_PER_PAGE)
//-----------------------------------------------------------------------------
     #define  NAND_PAGES_PER_BLOCK                          64
     #define  NAND_TOTAL_BLOCK_NO                         1024
     #define  NAND_COL_ADDR_MASK                        0x0fff
     #define  NAND_ROW_ADDR_MASK                        0xffff
     #define  NAND_BLOCK_VALID_COL_ADDR                   2048
     #define  NAND_BLOCK_VALID_INFO_SAVE_PAGE     (NAND_PAGES_PER_BLOCK - 1)
     #define  NAND_BLOCK_VALID_INFO_DIVR                     8
     #define  NAND_BLOCK_VALID_INFO_MASK                   0x7
//-----------------------------------------------------------------------------
     #define  NAND_BLOCK_SIZE                      (128 * 1024)
     #define  NAND_BLOCK_MASK                          0x1ffff
     #define  NAND_VALID_BLOCK_INFO_SIZE                  2048  // (NAND_TOTAL_BLOCK_NO / NAND_BLOCK_VALID_INFO_DIVR)
     #define  NAND_BLOCK_GOOD_COUNT_SIZE     (NAND_TOTAL_BLOCK_NO)
//-----------------------------------------------------------------------------
     #define  NAND_COL_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
     #define  NAND_COL_ADDR1(ADDR)  ((UCHAR)((ADDR >> 8) & 0x000000ff))
     #define  NAND_ROW_ADDR0(ADDR)  ((UCHAR)((ADDR >> 0) & 0x000000ff))
     #define  NAND_ROW_ADDR1(ADDR)  ((UCHAR)((ADDR >> 8) & 0x000000ff))
//-----------------------------------------------------------------------------
     #define  ECCX_BYTES_PER_BLOCK                         256
//   #define  ECCX_BYTES_PER_BLOCK                         512

     #define  ECCX_BLOCK_COUNT_NO   (NAND_BYTES_PER_PAGE / ECCX_BYTES_PER_BLOCK)
     #define  ECCX_CODE_COUNT_NO                             3
     #define  ECCX_SIGN_DATA                              (0xaa)
 #if ECCX_BYTES_PER_BLOCK == (512)
     #define  ECCX_SIGN_POS0        (NAND_BYTES_PER_PAGE + 0x02)
     #define  ECCX_DATA_POS0        (NAND_BYTES_PER_PAGE + 0x04)
     #define  ECCX_SIGN_POS1        (NAND_BYTES_PER_PAGE + 0x12)
     #define  ECCX_DATA_POS1        (NAND_BYTES_PER_PAGE + 0x14)
 #else
     #define  ECCX_SIGN_POS0        (NAND_BYTES_PER_PAGE + 0x02)
     #define  ECCX_DATA_POS0        (NAND_BYTES_PER_PAGE + 0x04)
     #define  ECCX_SIGN_POS1        (NAND_BYTES_PER_PAGE + 0x22)
     #define  ECCX_DATA_POS1        (NAND_BYTES_PER_PAGE + 0x24)
 #endif
//-----------------------------------------------------------------------------
#endif
//=============================================================================
#if  NAND_BLOCK_TYPE == NAND_BLOCK_SMALL
     #define  NAND_CMD_RESET                              0xff
     #define  NAND_CMD_ID                                 0x90
     #define	NAND_PAGE_READ                              0x00
     #define	NAND_PAGE_READA                             0x00
     #define	NAND_PAGE_READB                             0x01
     #define	NAND_PAGE_READC                             0x50
     #define	NAND_PAGE_WRITE1                            0x80
     #define	NAND_PAGE_WRITE2                            0x10
     #define	NAND_BLOCK_ERASE1                           0x60
     #define	NAND_BLOCK_ERASE2                           0xD0
     #define	NAND_READ_STATUS                            0x70
#else
     #define  NAND_CMD_RESET                              0xff
     #define  NAND_CMD_ID                                 0x90
     #define	NAND_PAGE_READ                              0x00
     #define	NAND_PAGE_READ1                             0x00
     #define	NAND_PAGE_READ2                             0x30
     #define	NAND_PAGE_WRITE1                            0x80
     #define	NAND_PAGE_WRITE2                            0x10
     #define	NAND_PART_READ1                             0x05
     #define	NAND_PART_READ2                             0xe0
     #define	NAND_PART_WRITE1                            0x85
     #define	NAND_PART_WRITE2                            0x10
     #define	NAND_BLOCK_ERASE1                           0x60
     #define	NAND_BLOCK_ERASE2                           0xD0
     #define	NAND_READ_STATUS                            0x70
     #define  NAND_UNLOCK_BEG                             0x23
     #define  NAND_UNLOCK_END                             0x24
#endif
//=============================================================================
#define  NAND_STATUS_READY                                0x40 // Ready
#define  NAND_STATUS_ERROR                                0x01 //  Error
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

int   GetBytesPerPage(void);
int   GetPagesPerBlock(void);
int   GetBytesPerBlock(void);
int   GetTotalBlockNo(void);
DWORD GetPhyNandAddr(DWORD dBlockNo,DWORD dPageNo);
DWORD GetNandColAddr(DWORD dPhyAddr);
DWORD GetNandRowAddr(DWORD dPhyAddr);
int   MakeValidBlockInfo(int nReBuildMode,int nAlwaysGoods,UCHAR *pInfoAddr);
int   IsValidBlock(int nBlockNo);
void  MakeBlockAddress(void);
void  SetBlockToBadBlock(DWORD dCpuAddr,int nWriteMode);
void  WriteZeroBlock(void);
DWORD ConvertCpuAddrToVirNand(DWORD dCpuAddr);
DWORD ConvertVirNandAddrToPhyNand(DWORD dVirNandAddr);
void  ReadNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize);
void  SaveNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize);
void  WriteNandFlashByCpuAddr(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize);
void  EraseNandFlashByCpuAddr(DWORD dCpuAddr,DWORD dSize);
void  ReadNandBlockData(UCHAR *pData,DWORD dBlockNo);
void  ReadNandPageData(UCHAR *pData,DWORD dBlockNo,DWORD dPageNo,DWORD dOffset,int nSize);
void  WriteNandBlockData(UCHAR *pData,DWORD dBlockNo);
void  WriteNandPageData(UCHAR *pData,DWORD dBlockNo,DWORD dPageNo,DWORD dOffset,int nSize);
DWORD EraseOneFlashBlockNoWaitStart(DWORD dBlockAddr);
void  ResetFlash(void);
DWORD WaitFlashReady(void);
void  WaitBusy(void);
DWORD ReadFlashDeviceCode(void);
int   CheckFlashID(void);
void  EnableWriteProtect(void);
void  DisableWriteProtect(void);

void  SetMapLoadOrPrgUpdateMode(int nMode);
int   GetMapLoadOrPrgUpdateMode(void);
DWORD GetNandErrorCorrectCountX(void);

void  NandCalculateEccX(const UCHAR *dat, UCHAR *ecc_code);
int   CountAllOneBitsX(DWORD byte);
int   NandCorrectDataX(UCHAR *dat, UCHAR *read_ecc, UCHAR *calc_ecc);

void  NandCalculateEccY(const UCHAR *buf, DWORD eccsize, UCHAR *code);
int   NandCorrectDataY(UCHAR *buf, UCHAR *read_ecc, UCHAR *calc_ecc, DWORD eccsize);

#ifdef  __cplusplus
}
#endif

#endif

