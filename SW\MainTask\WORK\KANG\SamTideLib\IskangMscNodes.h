//cst_nodes[37][68] =
{1.155000, 1.132400, 1.097200, 1.050400, 0.994900, 0.935900, 0.881500, 0.842100, 0.826800, 0.839600, 0.877100, 0.930500, 0.989500, 1.045600, 1.093400, 1.129700, 1.153400, 1.164300, 1.162400, 1.147700, 1.120300, 1.080400, 1.029800, 0.972000, 0.913700, 
 0.864000, 0.832900, 0.828400, 0.851600, 0.896400, 0.953200, 1.012000, 1.065300, 1.108900, 1.140400, 1.159200, 1.165100, 1.158300, 1.138600, 1.106200, 1.061900, 1.008000, 0.949100, 0.892800, 0.849200, 0.827800, 0.834300, 0.866900, 0.917600, 
 0.976100, 1.033600, 1.083600, 1.122600, 1.149200, 1.163000, 1.164000, 1.152200, 1.127600, 1.090400, 1.041900, 0.985400, 0.926500, 0.873900, 0.837800, 0.826900, 0.844100, 0.884900, 0.939900, },
{1.105200, 1.088400, 1.062700, 1.029400, 0.991000, 0.951400, 0.916100, 0.891200, 0.881700, 0.889700, 0.913300, 0.947900, 0.987300, 1.026000, 1.060000, 1.086400, 1.104000, 1.112200, 1.110800, 1.099800, 1.079500, 1.050700, 1.015000, 0.975500, 0.936900, 
 0.905000, 0.885500, 0.882700, 0.897200, 0.925700, 0.962900, 1.002700, 1.039900, 1.071200, 1.094300, 1.108300, 1.112800, 1.107700, 1.093000, 1.069300, 1.037500, 0.999900, 0.960200, 0.923400, 0.895700, 0.882400, 0.886400, 0.906800, 0.939400, 
 0.978200, 1.017600, 1.052900, 1.081200, 1.100800, 1.111200, 1.111900, 1.103100, 1.084900, 1.057800, 1.023500, 0.984500, 0.945300, 0.911300, 0.888600, 0.881800, 0.892500, 0.918300, 0.954100, },
{1.288800, 1.231300, 1.149500, 1.053800, 0.956600, 0.869700, 0.802600, 0.761000, 0.746200, 0.758500, 0.797700, 0.862600, 0.948000, 1.044700, 1.141100, 1.224700, 1.284700, 1.314000, 1.308900, 1.269900, 1.202000, 1.113500, 1.015700, 0.921000, 0.840900, 
 0.783400, 0.752100, 0.747800, 0.770500, 0.819900, 0.893600, 0.984800, 1.082800, 1.175500, 1.251100, 1.300000, 1.316200, 1.297700, 1.246700, 1.169500, 1.076100, 0.978100, 0.887800, 0.815600, 0.768000, 0.747200, 0.753400, 0.786500, 0.845800, 
 0.927200, 1.022500, 1.120100, 1.207500, 1.273600, 1.310300, 1.313100, 1.281500, 1.219500, 1.134700, 1.037900, 0.941500, 0.857300, 0.794100, 0.756700, 0.746400, 0.763000, 0.806500, 0.875100, },
{0.883100, 0.662300, 1.122000, 1.274600, 1.015000, 0.805500, 0.988600, 1.179000, 1.170300, 0.994600, 0.845200, 1.001200, 1.240700, 1.158900, 0.744300, 0.810200, 1.266400, 1.249200, 0.748400, 0.745300, 1.219600, 1.249800, 0.897500, 0.798500, 1.078900, 
 1.210200, 1.107700, 0.919600, 0.891500, 1.096600, 1.240200, 1.058700, 0.704900, 0.942900, 1.309700, 1.155400, 0.639300, 0.867100, 1.291100, 1.186800, 0.778700, 0.848200, 1.164700, 1.204700, 1.021500, 0.878000, 0.970400, 1.165600, 1.204000, 
 0.958200, 0.733700, 1.065000, 1.314800, 1.039200, 0.591400, 1.000900, 1.328800, 1.090900, 0.685900, 0.940900, 1.230900, 1.161600, 0.927100, 0.883700, 1.059000, 1.199400, 1.138500, 0.876800, },
{1.990900, 2.182500, 1.507200, 0.998700, 1.540200, 1.780700, 1.429700, 0.869500, 0.871900, 1.363700, 1.660600, 1.472200, 0.966300, 1.324200, 2.050800, 2.035000, 1.290900, 1.364100, 2.150600, 2.130300, 1.332500, 1.154900, 1.779000, 1.832300, 1.284700, 
 0.791600, 1.085000, 1.490000, 1.562600, 1.214800, 0.880400, 1.483900, 2.051100, 1.835000, 1.127300, 1.565700, 2.250200, 2.015900, 1.180500, 1.381800, 1.970900, 1.815700, 1.123200, 0.878000, 1.320100, 1.557500, 1.404800, 0.965200, 0.934400, 
 1.611500, 1.981200, 1.593500, 1.058400, 1.758300, 2.283000, 1.846500, 1.099400, 1.626900, 2.102500, 1.733500, 0.991100, 1.089700, 1.530500, 1.559400, 1.204000, 0.792800, 1.082500, 1.689600, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{0.950200, 0.960400, 0.975100, 0.992900, 1.011700, 1.029500, 1.044000, 1.053700, 1.057300, 1.054300, 1.045200, 1.031000, 1.013400, 0.994600, 0.976700, 0.961600, 0.950900, 0.945700, 0.946600, 0.953500, 0.965600, 0.981800, 1.000200, 1.018800, 1.035600, 
 1.048500, 1.055900, 1.056900, 1.051500, 1.040200, 1.024500, 1.006200, 0.987500, 0.970400, 0.956800, 0.948200, 0.945400, 0.948600, 0.957600, 0.971500, 0.988700, 1.007500, 1.025700, 1.041100, 1.052000, 1.057100, 1.055600, 1.047700, 1.034500, 
 1.017600, 0.998900, 0.980500, 0.964600, 0.952900, 0.946400, 0.945900, 0.951500, 0.962500, 0.977900, 0.995900, 1.014700, 1.032100, 1.046000, 1.054700, 1.057300, 1.053200, 1.043200, 1.028300, },
{0.934100, 0.947500, 0.967000, 0.990500, 1.015600, 1.039400, 1.059100, 1.072200, 1.077100, 1.073000, 1.060600, 1.041400, 1.017900, 0.992800, 0.969000, 0.949000, 0.935000, 0.928300, 0.929500, 0.938500, 0.954400, 0.975700, 1.000200, 1.025200, 1.047700, 
 1.065100, 1.075100, 1.076600, 1.069200, 1.053900, 1.032700, 1.008200, 0.983300, 0.960700, 0.942800, 0.931500, 0.927800, 0.932000, 0.943900, 0.962100, 0.985000, 1.009900, 1.034300, 1.055200, 1.069900, 1.076800, 1.074700, 1.064100, 1.046300, 
 1.023500, 0.998500, 0.974100, 0.953100, 0.937600, 0.929100, 0.928500, 0.935800, 0.950300, 0.970600, 0.994600, 1.019600, 1.042900, 1.061700, 1.073600, 1.077000, 1.071600, 1.057900, 1.037900, },
{0.902800, 0.922300, 0.950900, 0.985800, 1.023500, 1.059700, 1.090000, 1.110300, 1.117800, 1.111500, 1.092300, 1.062800, 1.027000, 0.989300, 0.953900, 0.924600, 0.904100, 0.894400, 0.896100, 0.909100, 0.932400, 0.963800, 1.000300, 1.038000, 1.072400, 
 1.099200, 1.114800, 1.117000, 1.105500, 1.081900, 1.049500, 1.012300, 0.975000, 0.941600, 0.915500, 0.899000, 0.893700, 0.899800, 0.917000, 0.943800, 0.977500, 1.015000, 1.051900, 1.083900, 1.106700, 1.117300, 1.114100, 1.097700, 1.070200, 
 1.035400, 0.997700, 0.961400, 0.930500, 0.907900, 0.895600, 0.894700, 0.905200, 0.926300, 0.956200, 0.991800, 1.029600, 1.065100, 1.094000, 1.112400, 1.117700, 1.109300, 1.088200, 1.057300, },
{0.872500, 0.897700, 0.935000, 0.981200, 1.031500, 1.080400, 1.121700, 1.149700, 1.160100, 1.151400, 1.124900, 1.084600, 1.036100, 0.985700, 0.939000, 0.900700, 0.874300, 0.861700, 0.863900, 0.880700, 0.910900, 0.952000, 1.000400, 1.051000, 1.097700, 
 1.134400, 1.155900, 1.159000, 1.143100, 1.110700, 1.066500, 1.016500, 0.966900, 0.923000, 0.888900, 0.867700, 0.860800, 0.868700, 0.890900, 0.925700, 0.970200, 1.020000, 1.069800, 1.113400, 1.144800, 1.159400, 1.155000, 1.132300, 1.094700, 
 1.047500, 0.996900, 0.948900, 0.908400, 0.879100, 0.863300, 0.862100, 0.875700, 0.903000, 0.942000, 0.989100, 1.039600, 1.087700, 1.127300, 1.152600, 1.160000, 1.148300, 1.119300, 1.077200, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{1.170200, 1.142800, 1.101000, 1.047000, 0.984900, 0.920700, 0.862900, 0.821600, 0.805700, 0.819000, 0.858200, 0.915000, 0.978900, 1.041500, 1.096500, 1.139500, 1.168300, 1.181800, 1.179400, 1.161300, 1.128200, 1.081400, 1.023700, 0.959800, 0.897000, 
 0.844400, 0.812100, 0.807400, 0.831500, 0.878600, 0.939400, 1.003800, 1.064000, 1.114600, 1.152400, 1.175400, 1.182800, 1.174300, 1.150300, 1.111500, 1.060100, 0.999400, 0.934900, 0.874800, 0.829000, 0.806800, 0.813500, 0.847500, 0.901100, 
 0.964300, 1.027900, 1.085000, 1.130900, 1.163100, 1.180100, 1.181300, 1.166800, 1.137000, 1.093000, 1.037400, 0.974500, 0.910700, 0.854900, 0.817100, 0.805900, 0.823700, 0.866500, 0.925000, },
{1.712500, 1.572300, 1.377600, 1.156600, 0.938900, 0.749500, 0.605500, 0.516300, 0.484800, 0.511000, 0.595000, 0.734200, 0.920000, 1.136000, 1.357900, 1.556300, 1.702500, 1.774800, 1.762100, 1.666100, 1.501900, 1.293700, 1.070500, 0.860900, 0.687600, 
 0.564300, 0.497300, 0.488100, 0.536700, 0.642500, 0.801200, 1.001400, 1.222900, 1.438900, 1.620300, 1.740200, 1.780200, 1.734300, 1.609500, 1.424700, 1.207400, 0.986600, 0.788700, 0.633400, 0.531500, 0.486800, 0.500000, 0.571100, 0.698100, 
 0.874500, 1.085900, 1.309000, 1.515000, 1.675200, 1.765700, 1.772500, 1.694500, 1.543900, 1.343000, 1.120500, 0.905900, 0.722900, 0.587300, 0.507300, 0.485100, 0.520700, 0.613800, 0.761300, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{1.170200, 1.142800, 1.101000, 1.047000, 0.984900, 0.920700, 0.862900, 0.821600, 0.805700, 0.819000, 0.858200, 0.915000, 0.978900, 1.041500, 1.096500, 1.139500, 1.168300, 1.181800, 1.179400, 1.161300, 1.128200, 1.081400, 1.023700, 0.959800, 0.897000, 
 0.844400, 0.812100, 0.807400, 0.831500, 0.878600, 0.939400, 1.003800, 1.064000, 1.114600, 1.152400, 1.175400, 1.182800, 1.174300, 1.150300, 1.111500, 1.060100, 0.999400, 0.934900, 0.874800, 0.829000, 0.806800, 0.813500, 0.847500, 0.901100, 
 0.964300, 1.027900, 1.085000, 1.130900, 1.163100, 1.180100, 1.181300, 1.166800, 1.137000, 1.093000, 1.037400, 0.974500, 0.910700, 0.854900, 0.817100, 0.805900, 0.823700, 0.866500, 0.925000, },
{1.170200, 1.142800, 1.101000, 1.047000, 0.984900, 0.920700, 0.862900, 0.821600, 0.805700, 0.819000, 0.858200, 0.915000, 0.978900, 1.041500, 1.096500, 1.139500, 1.168300, 1.181800, 1.179400, 1.161300, 1.128200, 1.081400, 1.023700, 0.959800, 0.897000, 
 0.844400, 0.812100, 0.807400, 0.831500, 0.878600, 0.939400, 1.003800, 1.064000, 1.114600, 1.152400, 1.175400, 1.182800, 1.174300, 1.150300, 1.111500, 1.060100, 0.999400, 0.934900, 0.874800, 0.829000, 0.806800, 0.813500, 0.847500, 0.901100, 
 0.964300, 1.027900, 1.085000, 1.130900, 1.163100, 1.180100, 1.181300, 1.166800, 1.137000, 1.093000, 1.037400, 0.974500, 0.910700, 0.854900, 0.817100, 0.805900, 0.823700, 0.866500, 0.925000, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{1.170200, 1.142800, 1.101000, 1.047000, 0.984900, 0.920700, 0.862900, 0.821600, 0.805700, 0.819000, 0.858200, 0.915000, 0.978900, 1.041500, 1.096500, 1.139500, 1.168300, 1.181800, 1.179400, 1.161300, 1.128200, 1.081400, 1.023700, 0.959800, 0.897000, 
 0.844400, 0.812100, 0.807400, 0.831500, 0.878600, 0.939400, 1.003800, 1.064000, 1.114600, 1.152400, 1.175400, 1.182800, 1.174300, 1.150300, 1.111500, 1.060100, 0.999400, 0.934900, 0.874800, 0.829000, 0.806800, 0.813500, 0.847500, 0.901100, 
 0.964300, 1.027900, 1.085000, 1.130900, 1.163100, 1.180100, 1.181300, 1.166800, 1.137000, 1.093000, 1.037400, 0.974500, 0.910700, 0.854900, 0.817100, 0.805900, 0.823700, 0.866500, 0.925000, },
{1.068100, 1.059500, 1.045000, 1.024500, 0.998700, 0.970000, 0.942800, 0.922900, 0.915100, 0.921600, 0.940600, 0.967300, 0.996100, 1.022300, 1.043400, 1.058400, 1.067600, 1.071600, 1.070900, 1.065400, 1.054600, 1.037800, 1.015100, 0.987700, 0.959000, 
 0.933900, 0.918200, 0.915900, 0.927700, 0.950300, 0.978500, 1.006800, 1.031200, 1.049900, 1.062600, 1.069700, 1.071900, 1.069400, 1.061900, 1.048800, 1.029700, 1.004900, 0.976500, 0.948500, 0.926500, 0.915600, 0.918900, 0.935400, 0.960900, 
 0.989700, 1.016800, 1.039200, 1.055500, 1.066000, 1.071100, 1.071500, 1.067100, 1.057500, 1.042100, 1.020700, 0.994100, 0.965400, 0.939000, 0.920700, 0.915200, 0.923900, 0.944500, 0.972000, },
{1.032300, 1.031300, 1.027600, 1.019700, 1.006500, 0.988900, 0.970300, 0.955600, 0.949700, 0.954600, 0.968700, 0.987200, 1.005000, 1.018700, 1.027100, 1.031100, 1.032300, 1.032400, 1.032400, 1.032100, 1.030300, 1.025200, 1.015200, 1.000000, 0.981600, 
 0.963900, 0.952100, 0.950300, 0.959200, 0.975600, 0.994400, 1.010900, 1.022600, 1.029100, 1.031800, 1.032400, 1.032400, 1.032400, 1.031700, 1.028800, 1.021900, 1.009900, 0.993100, 0.974300, 0.958300, 0.950100, 0.952600, 0.965000, 0.982900, 
 1.001200, 1.016000, 1.025600, 1.030500, 1.032200, 1.032400, 1.032400, 1.032300, 1.030900, 1.026700, 1.017900, 1.003900, 0.985900, 0.967500, 0.954000, 0.949800, 0.956400, 0.971500, 0.990200, },
{0.934100, 0.947500, 0.967000, 0.990500, 1.015600, 1.039400, 1.059100, 1.072200, 1.077100, 1.073000, 1.060600, 1.041400, 1.017900, 0.992800, 0.969000, 0.949000, 0.935000, 0.928300, 0.929500, 0.938500, 0.954400, 0.975700, 1.000200, 1.025200, 1.047700, 
 1.065100, 1.075100, 1.076600, 1.069200, 1.053900, 1.032700, 1.008200, 0.983300, 0.960700, 0.942800, 0.931500, 0.927800, 0.932000, 0.943900, 0.962100, 0.985000, 1.009900, 1.034300, 1.055200, 1.069900, 1.076800, 1.074700, 1.064100, 1.046300, 
 1.023500, 0.998500, 0.974100, 0.953100, 0.937600, 0.929100, 0.928500, 0.935800, 0.950300, 0.970600, 0.994600, 1.019600, 1.042900, 1.061700, 1.073600, 1.077000, 1.071600, 1.057900, 1.037900, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{1.416400, 1.341200, 1.232200, 1.101000, 0.962200, 0.831200, 0.723200, 0.651700, 0.625300, 0.647300, 0.715000, 0.820000, 0.949500, 1.088300, 1.220900, 1.332400, 1.411100, 1.449000, 1.442400, 1.391800, 1.302400, 1.183400, 1.047400, 0.909500, 0.785800, 
 0.690700, 0.635800, 0.628100, 0.668400, 0.751700, 0.868000, 1.003200, 1.141300, 1.267100, 1.367200, 1.431000, 1.451800, 1.427900, 1.361400, 1.259100, 1.132000, 0.993500, 0.859200, 0.744800, 0.664200, 0.627100, 0.638200, 0.696100, 0.793600, 
 0.918800, 1.057100, 1.192400, 1.309700, 1.396600, 1.444300, 1.447800, 1.406900, 1.325600, 1.212200, 1.078700, 0.940100, 0.811800, 0.709000, 0.644200, 0.625600, 0.655200, 0.729700, 0.839700, },
{0.966500, 0.973400, 0.983300, 0.995300, 1.007800, 1.019500, 1.029100, 1.035500, 1.037800, 1.035900, 1.029900, 1.020500, 1.008900, 0.996400, 0.984400, 0.974200, 0.967000, 0.963500, 0.964100, 0.968700, 0.976900, 0.987800, 1.000100, 1.012500, 1.023600, 
 1.032000, 1.036900, 1.037600, 1.034000, 1.026600, 1.016200, 1.004100, 0.991600, 0.980200, 0.971000, 0.965100, 0.963200, 0.965400, 0.971500, 0.980900, 0.992500, 1.005000, 1.017000, 1.027200, 1.034400, 1.037700, 1.036700, 1.031500, 1.022900, 
 1.011700, 0.999200, 0.987000, 0.976300, 0.968300, 0.963900, 0.963600, 0.967400, 0.974800, 0.985200, 0.997300, 1.009800, 1.021200, 1.030400, 1.036100, 1.037800, 1.035200, 1.028600, 1.018800, },
{0.882600, 0.906300, 0.940500, 0.981700, 1.025400, 1.066500, 1.100500, 1.122900, 1.131200, 1.124300, 1.103100, 1.070000, 1.029300, 0.985700, 0.944100, 0.909000, 0.884300, 0.872400, 0.874400, 0.890400, 0.918400, 0.955800, 0.998600, 1.041900, 1.080800, 
 1.110700, 1.127900, 1.130400, 1.117700, 1.091500, 1.055000, 1.012500, 0.969100, 0.929500, 0.898100, 0.878000, 0.871500, 0.879000, 0.899900, 0.932000, 0.972000, 1.015500, 1.057700, 1.093700, 1.119000, 1.130700, 1.127200, 1.109000, 1.078300, 
 1.039000, 0.995500, 0.953000, 0.916200, 0.888800, 0.873800, 0.872700, 0.885600, 0.911100, 0.946800, 0.988700, 1.032300, 1.072600, 1.104900, 1.125300, 1.131200, 1.121800, 1.098400, 1.063900, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
{1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 
 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, },
