/*##################################################################
  FILE    :	cmu.h
  
  USE     :	Public header file for CMU software interface  
  
  AUTHOR  :	GB[000517]
  UPDATED :	SiS[010706]
  ##################################################################

*/


#ifndef __CMU_H__
	#define __CMU_H__


/*****************************************************************************
  #Include section.
 *****************************************************************************/
	
#include "cmaptype.h"


/*****************************************************************************
  Configuration defines.
 *****************************************************************************/



/*****************************************************************************
  Constants definition section.
 *****************************************************************************/

/* Access rights define */
#define	READ_ACCESS		0x1
#define	WRITE_ACCESS	0x2
#define FORMAT_ACCESS	0x4
#define TOTAL_ACCESS	( READ_ACCESS | WRITE_ACCESS | FORMAT_ACCESS )



#ifdef __cplusplus
extern "C"
{
#endif


/*****************************************************************************
  Types & Data Structure definition definition section.
 *****************************************************************************/

struct cmu_tm
{
	int not_used;
};

	
/* User cartridge Handlers type. */
typedef void* USERHANDLE;


   
/* Cartridge Information structure. */
typedef struct _UserInfo
{
	SLong 	IsFormatted;	   /* Formatted flag. 				*/
	Long 	CheckSum;		   /* Checksum.       				*/   
	String 	VolumeName[40];	   /* Volume name.		     		*/
	SLong 	GrantedAccess;	   /* Access rights guaranted. 		*/
	Long 	DataAreaStartAddr; /* Data area starting address. 	*/
	Long 	DataAreaEndAddr;   /* Data area ending address.		*/
	Long 	SectorSize;		   /* Size of a sector in bytes.	*/
} UserInfo;



/* Error type definition. */
typedef  enum _cmuError
{	
	cmu_NOERROR=0,
	cmu_INVALID_HANDLE,
	cmu_INVALID_PARAMETERS,
	cmu_CDG_NOT_PRESENT,
	cmu_CDG_NOT_FORMATTED,
	cmu_CDG_CHANGED,
	cmu_CDG_CORRUPTED,
	cmu_INVALID_ACCESS,
	cmu_NO_MORE_HANDLE,
	cmu_VERIFY_ERROR,
	cmu_NOT_SUPPORTED,
	cmu_GENERAL_FAILURE
} cmuError;

	
	

/*****************************************************************************
  Interface Functions prototypes.
 *****************************************************************************/

PRE_EXPORT_H cmuError IN_EXPORT_H cmuOpen			( Word  SlotNumber, SLong RequiredAccess, USERHANDLE* phu );
PRE_EXPORT_H cmuError IN_EXPORT_H cmuFormat			( USERHANDLE hu, const char* pVolumeName, const struct cmu_tm* pFormatDate );
PRE_EXPORT_H cmuError IN_EXPORT_H cmuSectorErase	( USERHANDLE hu, Long EraseAddr, Long NumberOfSector );
PRE_EXPORT_H cmuError IN_EXPORT_H cmuWrite			( USERHANDLE hu, Long DstAddr, const void* SrcBuf, Long ByteToWrite );
PRE_EXPORT_H cmuError IN_EXPORT_H cmuRead			( USERHANDLE hu, Long SrcAddr, void* pDstAddr, Long ByteToRead );
PRE_EXPORT_H cmuError IN_EXPORT_H cmuGetInfo		( USERHANDLE hu, UserInfo* pInfo );
PRE_EXPORT_H cmuError IN_EXPORT_H cmuClose			( USERHANDLE hu );
PRE_EXPORT_H cmuError IN_EXPORT_H cmuVerify			( Bool on_off );
	
#ifdef __cplusplus
}	/* extern "C"  */
#endif


#endif /* #ifndef __CMU_H__ */



/*****************************************************************************
  END of Code.
 *****************************************************************************/
