#include <stdio.h>
#include "LRF.hpp"

CLrf::CLrf() : CSentence()
{
}
    
CLrf::CLrf(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CLrf::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_LRF;
}

/******************************************************************************
*
* LRF - Long Range Function
*
* $--LRF,x,xxxxxxxxx,c--c,c--c,c--c*hh<CR><LF>
*        | |         |    |    |
*        1 2         3    4    5
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of requester
* 3.  Name of requester, 1 to 20 character string
* 4.  Function request , 1 to 26 characters
* 5.  Function reply status
*
******************************************************************************/
void CLrf::Parse()
{
	m_nSeqNumber     = GetFieldInteger(1);
	m_dwMMSIReq      = GetFieldMMSI(2);
	GetFieldString(3, m_szNameOfRequester);
	GetFieldString(4, m_szFunctionRequest);
	GetFieldString(5, m_szReplyStatus);
}

void CLrf::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}

/* Modify LRF sentence element : HSI 2012.05.01 */
int CLrf::MakeSentence(BYTE *pszSentence)
{
	/*
	sprintf(pszSentence, "$AILRF,%d,%09d,%s,%s,%c",
		m_nSeqNumber, m_dwMMSIReq, m_szNameOfRequester, m_szFunctionRequest, m_chReplyStatus);

	CSentence::SendMakeNmeaCsData(pszSentence);
	*/

	int checksum;
	int strleng;

	strncpy((char *)pszSentence,"$SY",3);
	checksum = CSentence::ComputeChecksum((char *)pszSentence);

	strleng = strlen((char *)pszSentence) - 2;
	pszSentence[strleng++] = NULL;
	pszSentence[strleng++] = NULL;
	//	pszSentence[strleng++] = NULL;
	//	pszSentence[strleng++] = NULL;

	sprintf((char *)pszSentence, "%s%02X\x0D\x0A", pszSentence, checksum);

	//CSentence::SendMakeNmeaCsData(pszSentence);
	return strlen((char *)pszSentence);

}

void  CLrf::SetReplyStatus(char *pSzStatus)
{
	strcpy(m_szReplyStatus,pSzStatus);
}
