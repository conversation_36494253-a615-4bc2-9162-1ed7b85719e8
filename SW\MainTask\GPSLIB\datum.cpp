/*...........................................................................*/
/*.                  File Name : DATUM.CPP                                  .*/
/*.                                                                         .*/
/*.                       Date : 2005.10.01                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "AllConst.h"
#include "KeyConst.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "SysLib.h"
#include "_AllStringY.hpp"
#include "datum.hpp"

#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

///////////////////////////////////////////////////////////////////////////////
static  xDATUM  G_xDatumTable[CHART_DATUM_NO_SIZE] = {
        {  0,(char *)"WGS84"                  , 6378137.00000, 6356752.31425, 0.0033528106647475,    0.0,    0.0,     0.0, 0.0066943799901414,(char *)"WGS84"},
        {  1,(char *)"WGS72"                  , 6378135.00000, 6356750.52002, 0.0033527794541675,    0.0,    0.0,     4.5, 0.0066943177782666,(char *)"WGS72"},
    #if defined(__N500R_MODEL__)
        {  2,(char *)"PZ-90"                  , 6378136.00000, 6356751.36175, 0.0033528037430195,    0.0,    0.0,     4.0, 0.0066943661930999,(char *)"P90"  },   // PZ-90 (=PE-90)
        {  3,(char *)"CK-42"                  , 6378245.00000, 6356863.01877, 0.0033523298692591,   28.0, -130.0,   -95.0, 0.0066934216229659,(char *)"S42"  },   // CK-42
        {  4,(char *)"CK-95"                  , 6378245.00000, 6356863.01877, 0.0033523298692591,  24.82, -131.21,  -82.66,0.0066934216239182,(char *)"S95"  },   // CK-95
    #else
        {  2,(char *)"Earth-90"               , 6378136.00000, 6356751.36175, 0.0033528037430195,    0.0,    0.0,     4.0, 0.0066943661930999,(char *)"(999)"},
        {  3,(char *)"Adindan-Mean"           , 6378249.14500, 6356514.86955, 0.0034075613786993, -166.0,  -15.0,   204.0, 0.0068035112828491,(char *)"(999)"},
        {  4,(char *)"Adindan-Burkina"        , 6378249.14500, 6356514.86955, 0.0034075613786993, -118.0,  -14.0,   218.0, 0.0068035112828491,(char *)"(999)"},
    #endif
        {  5,(char *)"Adindan-Cameroon"       , 6378249.14500, 6356514.86955, 0.0034075613786993, -134.0,   -2.0,   210.0, 0.0068035112828491,(char *)"(999)"},
        {  6,(char *)"Adindan-Ethiopia"       , 6378249.14500, 6356514.86955, 0.0034075613786993, -165.0,  -11.0,   206.0, 0.0068035112828491,(char *)"(999)"},
        {  7,(char *)"Adindan-Mali"           , 6378249.14500, 6356514.86955, 0.0034075613786993, -123.0,  -20.0,   220.0, 0.0068035112828491,(char *)"(999)"},
        {  8,(char *)"Adindan-Senegal"        , 6378249.14500, 6356514.86955, 0.0034075613786993, -128.0,  -18.0,   224.0, 0.0068035112828491,(char *)"(999)"},
        {  9,(char *)"Adindan-Sudan"          , 6378249.14500, 6356514.86955, 0.0034075613786993, -161.0,  -14.0,   205.0, 0.0068035112828491,(char *)"(999)"},
        { 10,(char *)"Afgooye-Somalia"        , 6378245.00000, 6356863.01877, 0.0033523298692591,  -43.0, -163.0,    45.0, 0.0066934216229659,(char *)"(999)"},
        { 11,(char *)"ARC1950-Mean"           , 6378249.14500, 6356514.86955, 0.0034075613786993, -143.0,  -90.0,  -294.0, 0.0068035112828491,(char *)"(999)"},
        { 12,(char *)"ARC1950-Botswana"       , 6378249.14500, 6356514.86955, 0.0034075613786993, -138.0, -105.0,  -289.0, 0.0068035112828491,(char *)"(999)"},
        { 13,(char *)"ARC1950-Burundi"        , 6378249.14500, 6356514.86955, 0.0034075613786993, -153.0,   -5.0,  -292.0, 0.0068035112828491,(char *)"(999)"},
        { 14,(char *)"ARC1950-Lesotho"        , 6378249.14500, 6356514.86955, 0.0034075613786993, -125.0, -108.0,  -295.0, 0.0068035112828491,(char *)"(999)"},
        { 15,(char *)"ARC1950-Malawi"         , 6378249.14500, 6356514.86955, 0.0034075613786993, -161.0,  -73.0,  -317.0, 0.0068035112828491,(char *)"(999)"},
        { 16,(char *)"ARC1950-Swaziland"      , 6378249.14500, 6356514.86955, 0.0034075613786993, -134.0, -105.0,  -295.0, 0.0068035112828491,(char *)"(999)"},
        { 17,(char *)"ARC1950-Zaire"          , 6378249.14500, 6356514.86955, 0.0034075613786993, -169.0,  -19.0,  -278.0, 0.0068035112828491,(char *)"(999)"},
        { 18,(char *)"ARC1950-Zambia"         , 6378249.14500, 6356514.86955, 0.0034075613786993, -147.0,  -74.0,  -283.0, 0.0068035112828491,(char *)"(999)"},
        { 19,(char *)"ARC1950-Zimbabwe"       , 6378249.14500, 6356514.86955, 0.0034075613786993, -142.0,  -96.0,  -293.0, 0.0068035112828491,(char *)"(999)"},
        { 20,(char *)"ARC1960-Mean"           , 6378249.14500, 6356514.86955, 0.0034075613786993, -160.0,   -6.0,  -302.0, 0.0068035112828491,(char *)"(999)"},
        { 21,(char *)"Ayabelle Lighthouse"    , 6378249.14500, 6356514.86955, 0.0034075613786993,  -79.0, -129.0,   145.0, 0.0068035112828491,(char *)"(999)"},
        { 22,(char *)"Bissau-Guinea"          , 6378388.00000, 6356911.94613, 0.0033670033670034, -173.0,  253.0,    27.0, 0.0067226700223332,(char *)"(999)"},
        { 23,(char *)"Cape-South Africa"      , 6378249.14500, 6356514.86955, 0.0034075613786993, -136.0, -108.0,  -292.0, 0.0068035112828491,(char *)"(999)"},
        { 24,(char *)"Carthage-Tunisia"       , 6378249.14500, 6356514.86955, 0.0034075613786993, -263.0,    6.0,   431.0, 0.0068035112828491,(char *)"(999)"},
        { 25,(char *)"Dabola-Guinea"          , 6378249.14500, 6356514.86955, 0.0034075613786993,  -83.0,   37.0,   124.0, 0.0068035112828491,(char *)"(999)"},
        { 26,(char *)"Leigon-Ghana"           , 6378249.14500, 6356514.86955, 0.0034075613786993, -130.0,   29.0,   364.0, 0.0068035112828491,(char *)"(999)"},
        { 27,(char *)"Liberia1964"            , 6378249.14500, 6356514.86955, 0.0034075613786993,  -90.0,   40.0,    88.0, 0.0068035112828491,(char *)"(999)"},
        { 28,(char *)"Massawa-Eritrea"        , 6377397.15500, 6356078.96282, 0.0033427731821748,  639.0,  405.0,    60.0, 0.0066743722318021,(char *)"(999)"},
        { 29,(char *)"Merchich-Morocco"       , 6378249.14500, 6356514.86955, 0.0034075613786993,   31.0,  146.0,    47.0, 0.0068035112828491,(char *)"(999)"},
        { 30,(char *)"Minna-Cameroon"         , 6378249.14500, 6356514.86955, 0.0034075613786993,  -81.0,  -84.0,   115.0, 0.0068035112828491,(char *)"(999)"},
        { 31,(char *)"Minna-Nigeria"          , 6378249.14500, 6356514.86955, 0.0034075613786993,  -92.0,  -93.0,   122.0, 0.0068035112828491,(char *)"(999)"},
        { 32,(char *)"M'Poraloko-Gabon"       , 6378249.14500, 6356514.86955, 0.0034075613786993,  -74.0, -130.0,    42.0, 0.0068035112828491,(char *)"(999)"},
        { 33,(char *)"North Sahara 1959"      , 6378249.14500, 6356514.86955, 0.0034075613786993, -186.0,  -93.0,   310.0, 0.0068035112828491,(char *)"(999)"},
        { 34,(char *)"Old Egyptian 1907"      , 6378200.00000, 6356818.16963, 0.0033523298692591, -130.0,  110.0,   -13.0, 0.0066934216229661,(char *)"(999)"},
        { 35,(char *)"Point 58 - Mean"        , 6378249.14500, 6356514.86955, 0.0034075613786993, -106.0, -129.0,   165.0, 0.0068035112828491,(char *)"(999)"},
        { 36,(char *)"Pointe Noire1948"       , 6378249.14500, 6356514.86955, 0.0034075613786993, -148.0,   51.0,  -291.0, 0.0068035112828491,(char *)"(999)"},
        { 37,(char *)"Schwarzeck-Namibia"     , 6377397.15500, 6356078.96282, 0.0033427731821748,  616.0,   97.0,  -251.0, 0.0066743722318021,(char *)"(999)"},
        { 38,(char *)"Voirol1960-Algeria"     , 6378249.14500, 6356514.86955, 0.0034075613786993, -123.0, -206.0,   219.0, 0.0068035112828491,(char *)"(999)"},
        { 39,(char *)"Ain El Abd-Bahrain"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -150.0, -250.0,    -1.0, 0.0067226700223332,(char *)"(999)"},
        { 40,(char *)"Ain El Abd-Saudi"       , 6378388.00000, 6356911.94613, 0.0033670033670034, -143.0, -236.0,     7.0, 0.0067226700223332,(char *)"(999)"},
        { 41,(char *)"Djakarta (Batavia)"     , 6377397.15500, 6356078.96282, 0.0033427731821748, -377.0,  681.0,   -50.0, 0.0066743722318021,(char *)"(999)"},
        { 42,(char *)"Hong Kong 1963"         , 6378388.00000, 6356911.94613, 0.0033670033670034, -156.0, -271.0,  -189.0, 0.0067226700223332,(char *)"(999)"},
        { 43,(char *)"Hu-Tzu-Shan-Taiwan"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -637.0, -549.0,  -203.0, 0.0067226700223332,(char *)"(999)"},
        { 44,(char *)"Indian-Bangladesh"      , 6377276.34500, 6356075.41314, 0.0033244492966629,  282.0,  726.0,   254.0, 0.0066378466301998,(char *)"(999)"},
        { 45,(char *)"Indian-India"           , 6377301.24300, 6356100.22837, 0.0033244492966629,  295.0,  736.0,   257.0, 0.0066378466301998,(char *)"(999)"},
        { 46,(char *)"Indian1954-Thailand"    , 6377276.34500, 6356075.41314, 0.0033244492966629,  217.0,  823.0,   299.0, 0.0066378466301998,(char *)"(999)"},
        { 47,(char *)"Indian1960-Vietnam"     , 6377276.34500, 6356075.41314, 0.0033244492966629,  198.0,  881.0,   317.0, 0.0066378466301998,(char *)"(999)"},
        { 48,(char *)"Indian1960-Con Son"     , 6377276.34500, 6356075.41314, 0.0033244492966629,  182.0,  915.0,   344.0, 0.0066378466301998,(char *)"(999)"},
        { 49,(char *)"Indian1975-Thailand"    , 6377276.34500, 6356075.41314, 0.0033244492966629,  209.0,  818.0,   290.0, 0.0066378466301998,(char *)"(999)"},
        { 50,(char *)"Indonesian 1974"        , 6378160.00000, 6356774.50409, 0.0033529255952281,  -24.0,  -15.0,     5.0, 0.0066946090804091,(char *)"(999)"},
        { 51,(char *)"Kandawala-Sri Lanka"    , 6377276.34500, 6356075.41314, 0.0033244492966629,  -97.0,  787.0,    86.0, 0.0066378466301998,(char *)"(999)"},
        { 52,(char *)"Kertau 1948"            , 6377304.06300, 6356103.03899, 0.0033244492966629,  -11.0,  851.0,     5.0, 0.0066378466301996,(char *)"(999)"},
        { 53,(char *)"Nahrwan-Masirah"        , 6378249.14500, 6356514.86955, 0.0034075613786993, -247.0, -148.0,   369.0, 0.0068035112828491,(char *)"(999)"},
        { 54,(char *)"Nahrwan-United Arab"    , 6378249.14500, 6356514.86955, 0.0034075613786993, -249.0, -156.0,   381.0, 0.0068035112828491,(char *)"(999)"},
        { 55,(char *)"Nahrwan-SaudiArabia"    , 6378249.14500, 6356514.86955, 0.0034075613786993, -243.0, -192.0,   477.0, 0.0068035112828491,(char *)"(999)"},
        { 56,(char *)"Oman"                   , 6378249.14500, 6356514.86955, 0.0034075613786993, -346.0,   -1.0,   224.0, 0.0068035112828491,(char *)"(999)"},
        { 57,(char *)"Qatar National"         , 6378388.00000, 6356911.94613, 0.0033670033670034, -128.0, -283.0,    22.0, 0.0067226700223332,(char *)"(999)"},
        { 58,(char *)"Singapore"              , 6378155.00000, 6356773.32048, 0.0033523298692591,    7.0,  -10.0,   -26.0, 0.0066934216229659,(char *)"(999)"},
        { 59,(char *)"Timbalai 1948"          , 6377298.55600, 6356097.55030, 0.0033244492966629, -679.0,  669.0,   -48.0, 0.0066378466301998,(char *)"(999)"},
        { 60,(char *)"Tokyo-Mean"             , 6377397.15500, 6356078.96282, 0.0033427731821748, -148.0,  507.0,   685.0, 0.0066743722318021,(char *)"(999)"},
        { 61,(char *)"Tokyo-Japan"            , 6377397.15500, 6356078.96282, 0.0033427731821748, -148.0,  507.0,   685.0, 0.0066743722318021,(char *)"(999)"},
        { 62,(char *)"Tokyo-Okinawa"          , 6377397.15500, 6356078.96282, 0.0033427731821748, -158.0,  507.0,   676.0, 0.0066743722318021,(char *)"(999)"},
        { 63,(char *)"Tokyo-South Korea"      , 6377397.15500, 6356078.96282, 0.0033427731821748, -146.0,  507.0,   687.0, 0.0066743722318021,(char *)"(999)"},
        { 64,(char *)"Australian 1966"        , 6378160.00000, 6356774.71920, 0.0033528918692372, -133.0,  -48.0,   148.0, 0.0066945418545875,(char *)"(999)"},
        { 65,(char *)"Australian 1984"        , 6378160.00000, 6356774.71920, 0.0033528918692372, -134.0,  -48.0,   149.0, 0.0066945418545875,(char *)"(999)"},
        { 66,(char *)"European1950-Mean"      , 6378388.00000, 6356911.94613, 0.0033670033670034,  -87.0,  -98.0,  -121.0, 0.0067226700223332,(char *)"(999)"},
        { 67,(char *)"European1950-Western"   , 6378388.00000, 6356911.94613, 0.0033670033670034,  -87.0,  -96.0,  -120.0, 0.0067226700223332,(char *)"(999)"},
        { 68,(char *)"European1950-Cyprus"    , 6378388.00000, 6356911.94613, 0.0033670033670034, -104.0, -101.0,  -140.0, 0.0067226700223332,(char *)"(999)"},
        { 69,(char *)"European1950-Egypt"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -130.0, -117.0,  -151.0, 0.0067226700223332,(char *)"(999)"},
        { 70,(char *)"European1950-England"   , 6378388.00000, 6356911.94613, 0.0033670033670034,  -86.0,  -96.0,  -120.0, 0.0067226700223332,(char *)"(999)"},
        { 71,(char *)"European1950-Ireland"   , 6378388.00000, 6356911.94613, 0.0033670033670034,  -86.0,  -96.0,  -120.0, 0.0067226700223332,(char *)"(999)"},
        { 72,(char *)"European1950-Greece"    , 6378388.00000, 6356911.94613, 0.0033670033670034,  -84.0,  -95.0,  -130.0, 0.0067226700223332,(char *)"(999)"},
        { 73,(char *)"European1950-Iran"      , 6378388.00000, 6356911.94613, 0.0033670033670034, -117.0, -132.0,  -164.0, 0.0067226700223332,(char *)"(999)"},
        { 74,(char *)"European1950-Italy"     , 6378388.00000, 6356911.94613, 0.0033670033670034,  -97.0, -103.0,  -120.0, 0.0067226700223332,(char *)"(999)"},
        { 75,(char *)"European1950-Sicily"    , 6378388.00000, 6356911.94613, 0.0033670033670034,  -97.0,  -88.0,  -135.0, 0.0067226700223332,(char *)"(999)"},
        { 76,(char *)"European1950-Malta"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -107.0,  -88.0,  -149.0, 0.0067226700223332,(char *)"(999)"},
        { 77,(char *)"European1950-Norway"    , 6378388.00000, 6356911.94613, 0.0033670033670034,  -87.0,  -95.0,  -120.0, 0.0067226700223332,(char *)"(999)"},
        { 78,(char *)"European1950-Spain"     , 6378388.00000, 6356911.94613, 0.0033670033670034,  -84.0, -107.0,  -120.0, 0.0067226700223332,(char *)"(999)"},
        { 79,(char *)"European1950-Tunisia"   , 6378388.00000, 6356911.94613, 0.0033670033670034, -112.0,  -77.0,  -145.0, 0.0067226700223332,(char *)"(999)"},
        { 80,(char *)"European1979-Mean"      , 6378388.00000, 6356911.94613, 0.0033670033670034,  -86.0,  -98.0,  -119.0, 0.0067226700223332,(char *)"(999)"},
        { 81,(char *)"Hjorsey1955-Iceland"    , 6378388.00000, 6356911.94613, 0.0033670033670034,  -73.0,   46.0,   -86.0, 0.0067226700223332,(char *)"(999)"},
        { 82,(char *)"Ireland1965"            , 6377340.18900, 6356034.44794, 0.0033408506414971,  506.0, -122.0,   611.0, 0.0066705399999854,(char *)"(999)"},
        { 83,(char *)"Ordnance1936-Mean"      , 6377563.39600, 6356256.90924, 0.0033408506414971,  375.0, -111.0,   431.0, 0.0066705399999853,(char *)"(999)"},
        { 84,(char *)"Ordnance1936-England"   , 6377563.39600, 6356256.90924, 0.0033408506414971,  371.0, -112.0,   434.0, 0.0066705399999853,(char *)"(999)"},
        { 85,(char *)"Ordnance1936-England"   , 6377563.39600, 6356256.90924, 0.0033408506414971,  371.0, -111.0,   434.0, 0.0066705399999853,(char *)"(999)"},
        { 86,(char *)"Ordnance1936-Shetland"  , 6377563.39600, 6356256.90924, 0.0033408506414971,  384.0, -111.0,   425.0, 0.0066705399999853,(char *)"(999)"},
        { 87,(char *)"Ordnance1936-Wales"     , 6377563.39600, 6356256.90924, 0.0033408506414971,  370.0, -108.0,   434.0, 0.0066705399999853,(char *)"(999)"},
        { 88,(char *)"Rome 1940"              , 6378388.00000, 6356911.94613, 0.0033670033670034, -225.0,  -65.0,     9.0, 0.0067226700223332,(char *)"(999)"},
        { 89,(char *)"S-42 (Pulkovo 1942)"    , 6378245.00000, 6356863.01877, 0.0033523298692591,   28.0, -121.0,   -77.0, 0.0066934216229659,(char *)"(999)"},
        { 90,(char *)"S-JTSK Czechoslavakia"  , 6377397.15500, 6356078.96282, 0.0033427731821748,  589.0,   76.0,   480.0, 0.0066743722318021,(char *)"(999)"},
        { 91,(char *)"Cape Canaveral - Mean"  , 6378206.40000, 6356583.80000, 0.0033900753040885,   -2.0,  151.0,   181.0, 0.0067686579976097,(char *)"(999)"},
        { 92,(char *)"N.America1927-Mean"     , 6378206.40000, 6356583.80000, 0.0033900753040885,   -8.0,  160.0,   176.0, 0.0067686579976097,(char *)"(999)"},
        { 93,(char *)"N.America1927-Western"  , 6378206.40000, 6356583.80000, 0.0033900753040885,   -8.0,  159.0,   175.0, 0.0067686579976097,(char *)"(999)"},
        { 94,(char *)"N.America1927-Eastern"  , 6378206.40000, 6356583.80000, 0.0033900753040885,   -9.0,  161.0,   179.0, 0.0067686579976097,(char *)"(999)"},
        { 95,(char *)"N.America1927-Alaska"   , 6378206.40000, 6356583.80000, 0.0033900753040885,   -5.0,  135.0,   172.0, 0.0067686579976097,(char *)"(999)"},
        { 96,(char *)"N.America1927-Aleutian" , 6378206.40000, 6356583.80000, 0.0033900753040885,   -2.0,  152.0,   149.0, 0.0067686579976097,(char *)"(999)"},
        { 97,(char *)"N.America1927-Aleutian" , 6378206.40000, 6356583.80000, 0.0033900753040885,    2.0,  204.0,   105.0, 0.0067686579976097,(char *)"(999)"},
        { 98,(char *)"N.America1927-Bahamas"  , 6378206.40000, 6356583.80000, 0.0033900753040885,   -4.0,  154.0,   178.0, 0.0067686579976097,(char *)"(999)"},
        { 99,(char *)"N.America1927-Salvador" , 6378206.40000, 6356583.80000, 0.0033900753040885,    1.0,  140.0,   165.0, 0.0067686579976097,(char *)"(999)"},
        {100,(char *)"N.America1927-Canada"   , 6378206.40000, 6356583.80000, 0.0033900753040885,  -10.0,  158.0,   187.0, 0.0067686579976097,(char *)"(999)"},
        {101,(char *)"N.America1927-Alberta"  , 6378206.40000, 6356583.80000, 0.0033900753040885,   -7.0,  162.0,   188.0, 0.0067686579976097,(char *)"(999)"},
        {102,(char *)"N.America1927-E.Canada" , 6378206.40000, 6356583.80000, 0.0033900753040885,  -22.0,  160.0,   190.0, 0.0067686579976097,(char *)"(999)"},
        {103,(char *)"N.America1927-Manitoba" , 6378206.40000, 6356583.80000, 0.0033900753040885,   -9.0,  157.0,   184.0, 0.0067686579976097,(char *)"(999)"},
        {104,(char *)"N.America1927-Territo." , 6378206.40000, 6356583.80000, 0.0033900753040885,    4.0,  159.0,   188.0, 0.0067686579976097,(char *)"(999)"},
        {105,(char *)"N.America1927-Yukon"    , 6378206.40000, 6356583.80000, 0.0033900753040885,   -7.0,  139.0,   181.0, 0.0067686579976097,(char *)"(999)"},
        {106,(char *)"N.America1927-Canal"    , 6378206.40000, 6356583.80000, 0.0033900753040885,    0.0,  125.0,   201.0, 0.0067686579976097,(char *)"(999)"},
        {107,(char *)"N.America1927-Caribean" , 6378206.40000, 6356583.80000, 0.0033900753040885,   -3.0,  142.0,   183.0, 0.0067686579976097,(char *)"(999)"},
        {108,(char *)"N.America1927-Central"  , 6378206.40000, 6356583.80000, 0.0033900753040885,    0.0,  125.0,   194.0, 0.0067686579976097,(char *)"(999)"},
        {109,(char *)"N.America1927-Cuba"     , 6378206.40000, 6356583.80000, 0.0033900753040885,   -9.0,  152.0,   178.0, 0.0067686579976097,(char *)"(999)"},
        {110,(char *)"N.America1927-Greenland", 6378206.40000, 6356583.80000, 0.0033900753040885,   11.0,  114.0,   195.0, 0.0067686579976097,(char *)"(999)"},
        {111,(char *)"N.America1927-Mexico"   , 6378206.40000, 6356583.80000, 0.0033900753040885,  -12.0,  130.0,   190.0, 0.0067686579976097,(char *)"(999)"},
        {112,(char *)"N.America1983-Alaska"   , 6378137.00000, 6356752.31414, 0.0033528106811823,    0.0,    0.0,     0.0, 0.0066943800229007,(char *)"(999)"},
        {113,(char *)"N.America1983-Aleutian" , 6378137.00000, 6356752.31414, 0.0033528106811823,   -2.0,    0.0,     4.0, 0.0066943800229007,(char *)"(999)"},
        {114,(char *)"N.America1983-Canada"   , 6378137.00000, 6356752.31414, 0.0033528106811823,    0.0,    0.0,     0.0, 0.0066943800229007,(char *)"(999)"},
        {115,(char *)"N.America1983-Mean"     , 6378137.00000, 6356752.31414, 0.0033528106811823,    0.0,    0.0,     0.0, 0.0066943800229007,(char *)"(999)"},
        {116,(char *)"N.America1983-Hawaii"   , 6378137.00000, 6356752.31414, 0.0033528106811823,    1.0,    1.0,    -1.0, 0.0066943800229007,(char *)"(999)"},
        {117,(char *)"N.America1983-Mexico"   , 6378137.00000, 6356752.31414, 0.0033528106811823,    0.0,    0.0,     0.0, 0.0066943800229007,(char *)"(999)"},
        {118,(char *)"Bogota - Colombia"      , 6378388.00000, 6356911.94613, 0.0033670033670034,  307.0,  304.0,  -318.0, 0.0067226700223332,(char *)"(999)"},
        {119,(char *)"Campo 1969 - Argentina" , 6378388.00000, 6356911.94613, 0.0033670033670034, -148.0,  136.0,    90.0, 0.0067226700223332,(char *)"(999)"},
        {120,(char *)"Chua Astro - Paraguay"  , 6378388.00000, 6356911.94613, 0.0033670033670034, -134.0,  229.0,   -29.0, 0.0067226700223332,(char *)"(999)"},
        {121,(char *)"Corrego Alegre - Brazil", 6378388.00000, 6356911.94613, 0.0033670033670034, -206.0,  172.0,    -6.0, 0.0067226700223332,(char *)"(999)"},
        {122,(char *)"S.America1956-Mean"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -288.0,  175.0,  -376.0, 0.0067226700223332,(char *)"(999)"},
        {123,(char *)"S.America1956-Bolivia"  , 6378388.00000, 6356911.94613, 0.0033670033670034, -270.0,  188.0,  -388.0, 0.0067226700223332,(char *)"(999)"},
        {124,(char *)"S.America1956-N. Chile" , 6378388.00000, 6356911.94613, 0.0033670033670034, -270.0,  183.0,  -390.0, 0.0067226700223332,(char *)"(999)"},
        {125,(char *)"S.America1956-S. Chile" , 6378388.00000, 6356911.94613, 0.0033670033670034, -305.0,  243.0,  -442.0, 0.0067226700223332,(char *)"(999)"},
        {126,(char *)"S.America1956-Colombia" , 6378388.00000, 6356911.94613, 0.0033670033670034, -282.0,  169.0,  -371.0, 0.0067226700223332,(char *)"(999)"},
        {127,(char *)"S.America1956-Ecuador"  , 6378388.00000, 6356911.94613, 0.0033670033670034, -278.0,  171.0,  -367.0, 0.0067226700223332,(char *)"(999)"},
        {128,(char *)"S.America1956-Guyana"   , 6378388.00000, 6356911.94613, 0.0033670033670034, -298.0,  159.0,  -369.0, 0.0067226700223332,(char *)"(999)"},
        {129,(char *)"S.America1956-Peru"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -279.0,  175.0,  -379.0, 0.0067226700223332,(char *)"(999)"},
        {130,(char *)"S.America1956-Venezuela", 6378388.00000, 6356911.94613, 0.0033670033670034, -295.0,  173.0,  -371.0, 0.0067226700223332,(char *)"(999)"},
        {131,(char *)"S.Chilean 1963"         , 6378388.00000, 6356911.94613, 0.0033670033670034,   16.0,  196.0,    93.0, 0.0067226700223332,(char *)"(999)"},
        {132,(char *)"S.American1969-Mean"    , 6378160.00000, 6356774.71920, 0.0033528918692372,  -57.0,    1.0,   -41.0, 0.0066945418545875,(char *)"(999)"},
        {133,(char *)"S.American1969-Argentin", 6378160.00000, 6356774.71920, 0.0033528918692372,  -62.0,   -1.0,   -37.0, 0.0066945418545875,(char *)"(999)"},
        {134,(char *)"S.American1969-Bolivia" , 6378160.00000, 6356774.71920, 0.0033528918692372,  -61.0,    2.0,   -48.0, 0.0066945418545875,(char *)"(999)"},
        {135,(char *)"S.American1969-Brazil"  , 6378160.00000, 6356774.71920, 0.0033528918692372,  -60.0,   -2.0,   -41.0, 0.0066945418545875,(char *)"(999)"},
        {136,(char *)"S.American1969-Chile"   , 6378160.00000, 6356774.71920, 0.0033528918692372,  -75.0,   -1.0,   -44.0, 0.0066945418545875,(char *)"(999)"},
        {137,(char *)"S.American1969-Colombia", 6378160.00000, 6356774.71920, 0.0033528918692372,  -44.0,    6.0,   -36.0, 0.0066945418545875,(char *)"(999)"},
        {138,(char *)"S.American1969-Ecuador" , 6378160.00000, 6356774.71920, 0.0033528918692372,  -48.0,    3.0,   -44.0, 0.0066945418545875,(char *)"(999)"},
        {139,(char *)"S.American1969-Baltra"  , 6378160.00000, 6356774.71920, 0.0033528918692372,  -47.0,   26.0,   -42.0, 0.0066945418545875,(char *)"(999)"},
        {140,(char *)"S.American1969-Guyana"  , 6378160.00000, 6356774.71920, 0.0033528918692372,  -53.0,    3.0,   -47.0, 0.0066945418545875,(char *)"(999)"},
        {141,(char *)"S.American1969-Paraguay", 6378160.00000, 6356774.71920, 0.0033528918692372,  -61.0,    2.0,   -33.0, 0.0066945418545875,(char *)"(999)"},
        {142,(char *)"S.American1969-Peru"    , 6378160.00000, 6356774.71920, 0.0033528918692372,  -58.0,    0.0,   -44.0, 0.0066945418545875,(char *)"(999)"},
        {143,(char *)"S.American1969-Trinidad", 6378160.00000, 6356774.71920, 0.0033528918692372,  -45.0,   12.0,   -33.0, 0.0066945418545875,(char *)"(999)"},
        {144,(char *)"S.American1969-Venezuel", 6378160.00000, 6356774.71920, 0.0033528918692372,  -45.0,    8.0,   -33.0, 0.0066945418545875,(char *)"(999)"},
        {145,(char *)"Zanderij - Suriname"    , 6378388.00000, 6356911.94613, 0.0033670033670034, -265.0,  120.0,  -358.0, 0.0067226700223332,(char *)"(999)"},
        {146,(char *)"Antigua Astro 1943"     , 6378249.14500, 6356514.86955, 0.0034075613786993, -270.0,   13.0,    62.0, 0.0068035112828491,(char *)"(999)"},
        {147,(char *)"Ascension Island 1958"  , 6378388.00000, 6356911.94613, 0.0033670033670034, -205.0,  107.0,    53.0, 0.0067226700223332,(char *)"(999)"},
        {148,(char *)"Astro Dos 71/4"         , 6378388.00000, 6356911.94613, 0.0033670033670034, -320.0,  550.0,  -494.0, 0.0067226700223332,(char *)"(999)"},
        {149,(char *)"Bermuda 1957-Bermuda"   , 6378206.40000, 6356583.80000, 0.0033900753040885,  -73.0,  213.0,   296.0, 0.0067686579976097,(char *)"(999)"},
        {150,(char *)"Deception , Antarctica" , 6378249.14500, 6356514.86955, 0.0034075613786993,  260.0,   12.0,  -147.0, 0.0068035112828491,(char *)"(999)"},
        {151,(char *)"Fort Thomas 1955-Nevis" , 6378249.14500, 6356514.86955, 0.0034075613786993,   -7.0,  215.0,   225.0, 0.0068035112828491,(char *)"(999)"},
        {152,(char *)"Graciosa Base SW 1948"  , 6378388.00000, 6356911.94613, 0.0033670033670034, -104.0,  167.0,   -38.0, 0.0067226700223332,(char *)"(999)"},
        {153,(char *)"ISTS 061 Astro 1968"    , 6378388.00000, 6356911.94613, 0.0033670033670034, -794.0,  119.0,  -298.0, 0.0067226700223332,(char *)"(999)"},
        {154,(char *)"L.C. 5 Astro 1961"      , 6378206.40000, 6356583.80000, 0.0033900753040885,   42.0,  124.0,   147.0, 0.0067686579976097,(char *)"(999)"},
        {155,(char *)"Montserrat Island"      , 6378249.14500, 6356514.86955, 0.0034075613786993,  174.0,  359.0,   365.0, 0.0068035112828491,(char *)"(999)"},
        {156,(char *)"Naparima, BWI"          , 6378388.00000, 6356911.94613, 0.0033670033670034,  -10.0,  375.0,   165.0, 0.0067226700223332,(char *)"(999)"},
        {157,(char *)"Meteorologico 1939"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -425.0, -169.0,    81.0, 0.0067226700223332,(char *)"(999)"},
        {158,(char *)"Pico De Las Nieves"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -307.0,  -92.0,   127.0, 0.0067226700223332,(char *)"(999)"},
        {159,(char *)"Porto Santo 1936"       , 6378388.00000, 6356911.94613, 0.0033670033670034, -499.0, -249.0,   314.0, 0.0067226700223332,(char *)"(999)"},
        {160,(char *)"Puerto Rico"            , 6378206.40000, 6356583.80000, 0.0033900753040885,   11.0,   72.0,  -101.0, 0.0067686579976097,(char *)"(999)"},
        {161,(char *)"Qornoq-South Greenland" , 6378388.00000, 6356911.94613, 0.0033670033670034,  164.0,  138.0,  -189.0, 0.0067226700223332,(char *)"(999)"},
        {162,(char *)"Sao Braz - Soa Miguel"  , 6378388.00000, 6356911.94613, 0.0033670033670034, -203.0,  141.0,    53.0, 0.0067226700223332,(char *)"(999)"},
        {163,(char *)"Sapper Hill 1943"       , 6378388.00000, 6356911.94613, 0.0033670033670034, -355.0,   21.0,    72.0, 0.0067226700223332,(char *)"(999)"},
        {164,(char *)"Selvagem Grande 1938"   , 6378388.00000, 6356911.94613, 0.0033670033670034, -289.0, -124.0,    60.0, 0.0067226700223332,(char *)"(999)"},
        {165,(char *)"Tristan Astro 1968"     , 6378388.00000, 6356911.94613, 0.0033670033670034, -632.0,  438.0,  -609.0, 0.0067226700223332,(char *)"(999)"},
        {166,(char *)"Anna 1 Astro 1965"      , 6378160.00000, 6356774.71920, 0.0033528918692372, -491.0,  -22.0,   435.0, 0.0066945418545875,(char *)"(999)"},
        {167,(char *)"Gandajika Base 1970"    , 6378388.00000, 6356911.94613, 0.0033670033670034, -133.0, -321.0,    50.0, 0.0067226700223332,(char *)"(999)"},
        {168,(char *)"ISTS 073 Astro 1969"    , 6378388.00000, 6356911.94613, 0.0033670033670034,  208.0, -435.0,  -229.0, 0.0067226700223332,(char *)"(999)"},
        {169,(char *)"Kerguelen Island 1949"  , 6378388.00000, 6356911.94613, 0.0033670033670034,  145.0, -187.0,   103.0, 0.0067226700223332,(char *)"(999)"},
        {170,(char *)"Mahe 1971-Mahe Island"  , 6378249.14500, 6356514.86955, 0.0034075613786993,   41.0, -220.0,  -134.0, 0.0068035112828491,(char *)"(999)"},
        {171,(char *)"Reunion-Mascarene"      , 6378388.00000, 6356911.94613, 0.0033670033670034,   94.0, -948.0, -1262.0, 0.0067226700223332,(char *)"(999)"},
        {172,(char *)"American Samoa 1962"    , 6378206.40000, 6356583.80000, 0.0033900753040885, -115.0,  118.0,   426.0, 0.0067686579976097,(char *)"(999)"},
        {173,(char *)"Astro Beacon1945"       , 6378388.00000, 6356911.94613, 0.0033670033670034,  145.0,   75.0,  -272.0, 0.0067226700223332,(char *)"(999)"},
        {174,(char *)"Astro Tern Island 1961" , 6378388.00000, 6356911.94613, 0.0033670033670034,  114.0, -116.0,  -333.0, 0.0067226700223332,(char *)"(999)"},
        {175,(char *)"Astronomical Station"   , 6378388.00000, 6356911.94613, 0.0033670033670034,  124.0, -234.0,   -25.0, 0.0067226700223332,(char *)"(999)"},
        {176,(char *)"Bellevue (IGN)"         , 6378388.00000, 6356911.94613, 0.0033670033670034, -127.0, -769.0,   472.0, 0.0067226700223332,(char *)"(999)"},
        {177,(char *)"Canton Astro 1966"      , 6378388.00000, 6356911.94613, 0.0033670033670034,  298.0, -304.0,  -375.0, 0.0067226700223332,(char *)"(999)"},
        {178,(char *)"Chatham Astro 1971"     , 6378388.00000, 6356911.94613, 0.0033670033670034,  175.0,  -38.0,   113.0, 0.0067226700223332,(char *)"(999)"},
        {179,(char *)"DOS1968 - Gizo Island"  , 6378388.00000, 6356911.94613, 0.0033670033670034,  230.0, -199.0,  -752.0, 0.0067226700223332,(char *)"(999)"},
        {180,(char *)"Easter Island 1967"     , 6378388.00000, 6356911.94613, 0.0033670033670034,  211.0,  147.0,   111.0, 0.0067226700223332,(char *)"(999)"},
        {181,(char *)"Geodetic Datum 1949"    , 6378388.00000, 6356911.94613, 0.0033670033670034,   84.0,  -22.0,   209.0, 0.0067226700223332,(char *)"(999)"},
        {182,(char *)"Guam 1963 - Guam"       , 6378206.40000, 6356583.80000, 0.0033900753040885, -100.0, -248.0,   259.0, 0.0067686579976097,(char *)"(999)"},
        {183,(char *)"GUX1 Astro-Guadalcanal" , 6378388.00000, 6356911.94613, 0.0033670033670034,  252.0, -209.0,  -751.0, 0.0067226700223332,(char *)"(999)"},
        {184,(char *)"Indonesian 1974"        , 6378160.00000, 6356774.50409, 0.0033529255952281,  -24.0,  -15.0,     5.0, 0.0066946090804091,(char *)"(999)"},
        {185,(char *)"Johnston Island 1961"   , 6378388.00000, 6356911.94613, 0.0033670033670034,  189.0,  -79.0,  -202.0, 0.0067226700223332,(char *)"(999)"},
        {186,(char *)"Kusaie Astro 1951"      , 6378388.00000, 6356911.94613, 0.0033670033670034,  647.0, 1777.0, -1124.0, 0.0067226700223332,(char *)"(999)"},
        {187,(char *)"Luzon-Philippines"      , 6378206.40000, 6356583.80000, 0.0033900753040885, -133.0,  -77.0,   -51.0, 0.0067686579976097,(char *)"(999)"},
        {188,(char *)"Luzon-Mindanao Island"  , 6378206.40000, 6356583.80000, 0.0033900753040885, -133.0,  -79.0,   -72.0, 0.0067686579976097,(char *)"(999)"},
        {189,(char *)"Midway Astro 1961"      , 6378388.00000, 6356911.94613, 0.0033670033670034,  912.0,  -58.0,  1227.0, 0.0067226700223332,(char *)"(999)"},
        {190,(char *)"Old Hawaiian-Mean"      , 6378206.40000, 6356583.80000, 0.0033900753040885,   61.0, -285.0,  -181.0, 0.0067686579976097,(char *)"(999)"},
        {191,(char *)"Old Hawaiian-Hawaii"    , 6378206.40000, 6356583.80000, 0.0033900753040885,   89.0, -279.0,  -183.0, 0.0067686579976097,(char *)"(999)"},
        {192,(char *)"Old Hawaiian-Kauai"     , 6378206.40000, 6356583.80000, 0.0033900753040885,   45.0, -290.0,  -172.0, 0.0067686579976097,(char *)"(999)"},
        {193,(char *)"Old Hawaiian-Maui"      , 6378206.40000, 6356583.80000, 0.0033900753040885,   65.0, -290.0,  -190.0, 0.0067686579976097,(char *)"(999)"},
        {194,(char *)"Old Hawaiian-Oahu"      , 6378206.40000, 6356583.80000, 0.0033900753040885,   58.0, -283.0,  -182.0, 0.0067686579976097,(char *)"(999)"},
        {195,(char *)"Pitcairn Astro 1967"    , 6378388.00000, 6356911.94613, 0.0033670033670034,  185.0,  165.0,    42.0, 0.0067226700223332,(char *)"(999)"},
        {196,(char *)"Santo (Dos) 1965"       , 6378388.00000, 6356911.94613, 0.0033670033670034,  170.0,   42.0,    84.0, 0.0067226700223332,(char *)"(999)"},
        {197,(char *)"Viti Levu 1916"         , 6378249.14500, 6356514.86955, 0.0034075613786993,   51.0,  391.0,   -36.0, 0.0068035112828491,(char *)"(999)"},
        {198,(char *)"Wake-Eniwetok 1960"     , 6378270.00000, 6356794.34343, 0.0033670033670034,  102.0,   52.0,   -38.0, 0.0067226700223334,(char *)"(999)"},
        {199,(char *)"Wake Astro 1952"        , 6378388.00000, 6356911.94613, 0.0033670033670034,  276.0,  -57.0,   149.0, 0.0067226700223332,(char *)"(999)"},
        {200,(char *)"Bukit Rimpah"           , 6377397.15500, 6356078.96282, 0.0033427731821748, -384.0,  664.0,   -48.0, 0.0066743722318021,(char *)"(999)"},
        {201,(char *)"Camp Area Astro"        , 6378388.00000, 6356911.94613, 0.0033670033670034, -104.0, -129.0,   239.0, 0.0067226700223332,(char *)"(999)"},
        {202,(char *)"European1950-Iraq"      , 6378388.00000, 6356911.94613, 0.0033670033670034, -103.0, -106.0,  -141.0, 0.0067226700223332,(char *)"(999)"},
        {203,(char *)"Gunung Segara"          , 6377397.15500, 6356078.96282, 0.0033427731821748, -403.0,  684.0,    41.0, 0.0066743722318021,(char *)"(999)"},
        {204,(char *)"Herat North"            , 6378388.00000, 6356911.94613, 0.0033670033670034, -333.0, -222.0,   114.0, 0.0067226700223332,(char *)"(999)"},
        {205,(char *)"Indian - Pakistan"      , 6377276.34500, 6356075.41314, 0.0033244492966629,  283.0,  682.0,   231.0, 0.0066378466301998,(char *)"(999)"},
        {206,(char *)"Pulkovo 1942 - Russia"  , 6378245.00000, 6356863.01877, 0.0033523298692591,   28.0, -130.0,   -95.0, 0.0066934216229659,(char *)"(999)"},
        {207,(char *)"Tananarive 1925"        , 6378388.00000, 6356911.94613, 0.0033670033670034, -189.0, -242.0,   -91.0, 0.0067226700223332,(char *)"(999)"},
        {208,(char *)"Yacare - Uruguay"       , 6378388.00000, 6356911.94613, 0.0033670033670034, -155.0,  171.0,    37.0, 0.0067226700223332,(char *)"(999)"},
        {209,(char *)"Krassovsky1942-Russia"  , 6378245.00000, 6356863.01877, 0.0033523298692591,   26.0, -139.0,   -80.0, 0.0066934216229659,(char *)"(999)"},
        {210,(char *)"Lommel Datum 1950"      , 6378388.00000, 6356911.94613, 0.0033670033670034,  -55.0,   49.0,  -158.0, 0.0067226700223332,(char *)"(999)"},
        {211,(char *)"Reseau Belge 1972"      , 6378388.00000, 6356911.94613, 0.0033670033670034, -104.0,   80.0,   -75.0, 0.0067226700223332,(char *)"(999)"},
        {212,(char *)"NTF - Nouvelle France"  , 6378249.14500, 6356514.86955, 0.0034075613786993, -168.0,  -60.0,   320.0, 0.0068035112828491,(char *)"(999)"},
        {213,(char *)"Netherlands 1921"       , 6377397.15500, 6356078.96282, 0.0033427731821748,  719.0,   47.0,   640.0, 0.0066743722318021,(char *)"(999)"},
        {214,(char *)"European Datum 1987"    , 6378388.00000, 6356911.94613, 0.0033670033670034,  -82.5,  -91.7,  -117.7, 0.0067226700223332,(char *)"(999)"},
        {215,(char *)"Swiss 1903+ (LV95)"     , 6377397.15500, 6356078.96282, 0.0033427731821748,  674.4,   15.1,   405.3, 0.0066743722318021,(char *)"(999)"}};

///////////////////////////////////////////////////////////////////////////////
int   cDATUM::m_nUsingDatumNo = WGS84_DATUM_NO;
UNICODE *cDATUM::m_pUniDatumName[CHART_DATUM_NO_SIZE + 1] = {NULL,};
///////////////////////////////////////////////////////////////////////////////

cDATUM::cDATUM(void)
{
}
cDATUM::~cDATUM(void)
{
}
void  cDATUM::InitAllData(void)
{
      int   i;

      for (i = 0;i < CHART_DATUM_NO_SIZE;i++)
          {
           m_pUniDatumName[i] = new UNICODE[32];
           CopyCharStrToUniStr(m_pUniDatumName[i],G_xDatumTable[i].pDatumName);
          }
      m_pUniDatumName[CHART_DATUM_NO_SIZE] = NULL;
      TestBackUpData();
}
int   cDATUM::GetDatumMaxNo(void)
{
      return(CHART_DATUM_NO_SIZE);
}
void  cDATUM::SetUsingDatumNo(int nDatumNo)
{
      m_nUsingDatumNo = nDatumNo;
}
int   cDATUM::GetUsingDatumNo(void)
{
      return(m_nUsingDatumNo);
}
CHAR *cDATUM::GetOneDatumName(int nDatumNo)
{
      return(G_xDatumTable[nDatumNo].pDatumName);
}
void  cDATUM::GetAllDatumName(CHAR **pDatumName)
{
      int  i;

      for (i = 0;i < CHART_DATUM_NO_SIZE;i++)
           pDatumName[i] = G_xDatumTable[i].pDatumName;
}
UNICODE **cDATUM::GetAllDatumName(void)
{
      return(m_pUniDatumName);
}
CHAR *cDATUM::GetOneDatumAbbr(int nDatumNo)
{
      return(G_xDatumTable[nDatumNo].pDatumAbbr);
}
void  cDATUM::WGS84ToLocalDatum(int nDatumNo,LREAL rW84Lat,LREAL rW84Lon,LREAL *pLocalLat,LREAL *pLocalLon)
{
      REAL fA,fF,fE,fH;
      REAL tA,tF,tE,tH;
      REAL dX,dY,dZ;
      REAL bda;
      REAL dA,dF,dH;
      REAL sP,cP,sL,cL;
      REAL Rn,Rm;
      REAL dP,dL;

      if (nDatumNo == WGS84_DATUM_NO)
         {
          *pLocalLat = rW84Lat;
          *pLocalLon = rW84Lon;
          return;
         }

      if (rW84Lon > 180.0)
          rW84Lon = rW84Lon - 360.0;
      rW84Lat = rW84Lat * TO_RADIAN;
      rW84Lon = rW84Lon * TO_RADIAN;
      fA = G_xDatumTable[WGS84_DATUM_NO].rA;
      fF = G_xDatumTable[WGS84_DATUM_NO].rF;
      fE = G_xDatumTable[WGS84_DATUM_NO].rE;
      fH = 0.0;
      tA = G_xDatumTable[nDatumNo].rA;
      tF = G_xDatumTable[nDatumNo].rF;
      tE = G_xDatumTable[nDatumNo].rE;
      tH = 0.0;
      dX = -G_xDatumTable[nDatumNo].rX;
      dY = -G_xDatumTable[nDatumNo].rY;
      dZ = -G_xDatumTable[nDatumNo].rZ;
      bda= 1.0 - fF;
      dA = tA - fA;
      dF = tF - fF;
      sP = sin(rW84Lat);
      cP = cos(rW84Lat);
      sL = sin(rW84Lon);
      cL = cos(rW84Lon);
      Rn = fA / sqrt(1.0 - fE * sP * sP);
      Rm = exp((3.0 / 2.0) * log(1.0 - fE * sP * sP));
      Rm = fA * (1.0 - fE) / Rm;
      dP = (((-dX * sP * cL - dY * sP * sL) + dZ * cP) + dA * Rn * fE * sP * cP / fA + dF * (Rm / bda + Rn * bda) * sP * cP) / (Rm + fH);
      dL = (-dX * sL + dY * cL) / ((Rn + fH) * cP);
      dH = dX * cP * cL + dY * cP * sL + dZ * sP - dA * fA / Rn + dF * bda * Rn * sP * sP;
      *pLocalLat = (rW84Lat + dP) * TO_DEGREE;
      *pLocalLon = (rW84Lon + dL) * TO_DEGREE;
      *pLocalLon = CheckRealLonRange(*pLocalLon);
}
void  cDATUM::LocalDatumToWGS84(int nDatumNo,LREAL rLocalLat,LREAL rLocalLon,LREAL *pW84Lat,LREAL *pW84Lon)
{
      REAL fA,fF,fE,fH;
      REAL tA,tF,tE,tH;
      REAL dX,dY,dZ;
      REAL bda;
      REAL dA,dF,dH;
      REAL sP,cP,sL,cL;
      REAL Rn,Rm;
      REAL dP,dL;

      if (rLocalLon > 180.0)
          rLocalLon = rLocalLon - 360.0;
      rLocalLat = rLocalLat * TO_RADIAN;
      rLocalLon = rLocalLon * TO_RADIAN;
      fA = G_xDatumTable[nDatumNo].rA;
      fF = G_xDatumTable[nDatumNo].rF;
      fE = G_xDatumTable[nDatumNo].rE;
      fH = 0.0;
      tA = G_xDatumTable[WGS84_DATUM_NO].rA;
      tF = G_xDatumTable[WGS84_DATUM_NO].rF;
      tE = G_xDatumTable[WGS84_DATUM_NO].rE;
      tH = 0.0;
      dX = G_xDatumTable[nDatumNo].rX;
      dY = G_xDatumTable[nDatumNo].rY;
      dZ = G_xDatumTable[nDatumNo].rZ;
      bda= 1.0 - fF;
      dA = tA - fA;
      dF = tF - fF;
      sP = sin(rLocalLat);
      cP = cos(rLocalLat);
      sL = sin(rLocalLon);
      cL = cos(rLocalLon);
      Rn = fA / sqrt(1.0 - fE * sP * sP);
      Rm = exp((3.0 / 2.0) * log(1.0 - fE * sP * sP));
      Rm = fA * (1.0 - fE) / Rm;
      dP = (((-dX * sP * cL - dY * sP * sL) + dZ * cP) + dA * Rn * fE * sP * cP / fA + dF * (Rm / bda + Rn * bda) * sP * cP) / (Rm + fH);
      dL = (-dX * sL + dY * cL) / ((Rn + fH) * cP);
      dH = dX * cP * cL + dY * cP * sL + dZ * sP - dA * fA / Rn + dF * bda * Rn * sP * sP;
      *pW84Lat = (rLocalLat + dP) * TO_DEGREE;
      *pW84Lon = (rLocalLon + dL) * TO_DEGREE;
      *pW84Lon = CheckRealLonRange(*pW84Lon);
}
void  cDATUM::LocalDatumToLocal(int nFrDatumNo,int nToDatumNo,REAL rFrLat,REAL rFrLon,REAL *pToLat,REAL *pToLon)
{
      REAL rW84Lat,rW84Lon;

      LocalDatumToWGS84(nFrDatumNo,rFrLat,rFrLon,&rW84Lat,&rW84Lon);
      WGS84ToLocalDatum(nToDatumNo,rW84Lat,rW84Lon,pToLat,pToLon);
}

void  cDATUM::ClearAllData(void)
{
      m_nUsingDatumNo = WGS84_DATUM_NO;
}
int   cDATUM::SaveBackUpData(UCHAR *pBackData)
{
      int   nSize;
      UCHAR *pTemp;

      pTemp = pBackData;

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);
      return(nSize);
}
int   cDATUM::RestBackUpData(UCHAR *pBackData)
{
      int   nSize;
      UCHAR *pTemp;

      pTemp = pBackData;

      TestBackUpData();
      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);
      return(nSize);
}
void  cDATUM::TestBackUpData(void)
{
}

