/*...........................................................................*/
/*.                  File Name : SYSLCD.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.04                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysGPIO.h"
#include "SysAlive.h"
#include "SysPWM.h"
#include "SysLCD.h"

#include <string.h>

void  SysInitLCD(void)
{
      SysSetLcdOn();
      SysSetLcdLvdsPwrDownMode(1);
}
void  SysSetLcdOn(void)
{
      HWORD wPeriod;
      int   nNavisType = SysGetNavisModelType();

      SysSetLcdLvdsPwrDownMode(1);
      SysSetLcdBackLightMode(1);

  #if defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR, LCD_ON_BIT, 1);
  #else
      SysSetALIVExBitData(LCD_ON_BIT,1);
  #endif

      wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);

      if (nNavisType == NAVIS_TYPE_700)
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wPeriod + 1);  // L=100%
      else
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
}
void  SysSetLcdOff(void)
{
      HWORD wPeriod;
      int   nNavisType = SysGetNavisModelType();

      SysSetLcdLvdsPwrDownMode(0);
      SysSetLcdBackLightMode(0);

  #if defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR, LCD_ON_BIT, 0);
  #else
      SysSetALIVExBitData(LCD_ON_BIT,0);
  #endif

      wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);

      if (nNavisType == NAVIS_TYPE_700)
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);            // H=100%
      else
          SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wPeriod + 1);  // L=100%
}
void  SysSetLcdSEN(int nBitData,DWORD dDelay)
{
}
void  SysSetLcdRST(int nBitData,DWORD dDelay)
{
}
void  SysSetLcdPWM(int nPercent)
{
	HWORD wPeriod;
	HWORD wDuty;
	int   nNavisType = SysGetNavisModelType();

	if (nNavisType == NAVIS_TYPE_700)
	{
		wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
		wDuty   = nPercent * wPeriod / 1000;
		SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wDuty);
		//sprintf((char *)0x86000000, "%15d,%7d,%7d,%15d,%15d,", nPercent, wPeriod, wDuty, CPU_PLL1_FREQUENCY, PWM0_OUT_FREQUENCY);
	}
	else
	{
		wPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
		wDuty   = (1000 - nPercent) * wPeriod / 1000;
		SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wDuty);
	}
}
void  SysSetLedPWMbyPercent(int nPercent)
{
      HWORD wPeriod;
      HWORD wDuty;

      wPeriod = SysGetPWMPeriod(PWM_KEY_CHANNEL);
      wDuty   = (1000 - nPercent) * wPeriod / 1000;
//    if (nPercent == 0)
//        wDuty = wPeriod - 1;
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,wDuty);
}
void  SysSetLedPWMbyDutyVal(int nDutyVal)
{
      SysSetPWMDutyCycle(PWM_KEY_CHANNEL,nDutyVal);
}
int   SysWriteDataToLcdI2C(UCHAR bAddr,UCHAR bData)
{
      return(1);
}
void  SysSetLcdSDA(int nBitData,DWORD dDelay)
{
}
void  SysSetLcdCLK(int nBitData,DWORD dDelay)
{
}

void  SysSetLcdLvdsPwrDownMode(int nMode)
{
  #if !defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR,LCD_LVDS_PWR_DOWN_BIT,nMode);
  #endif
}
void  SysSetLcdBackLightMode(int nMode)
{
  #if !defined(__N500_MODEL__)
      SysSetGPIOxBitData((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR,LCD_BACK_LIGHT_BIT,nMode);
  #endif
}
