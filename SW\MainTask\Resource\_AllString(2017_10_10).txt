_EXTERN CHAR *CS_CAP_GPS[LANG_SIZE] = {"\x47\x00\x50\x00\x53\x00","\x47\x00\x50\x00\x53\x00","\x47\x00\x50\x00\x53\x00"};
_EXTERN CHAR *CS_CAP_AIS[LANG_SIZE] = {"\x41\x00\x49\x00\x53\x00","\x41\x00\x49\x00\x53\x00","\x41\x00\x49\x00\x53\x00"};
_EXTERN CHAR *CS_TARGET_LIST[LANG_SIZE] = {"\x54\x00\x41\x00\x52\x00\x47\x00\x45\x00\x54\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00","\x3c\xbb\x20\x00\x5c\xd4\x20\x00\xa9\xba\x20\x00\x5d\xb8","\xee\x76\x07\x68\x05\x6e\x55\x53"};
_EXTERN CHAR *CS_TARGET_PLOT[LANG_SIZE] = {"\x54\x00\x41\x00\x52\x00\x47\x00\x45\x00\x54\x00\x20\x00\x50\x00\x4c\x00\x4f\x00\x54\x00","\x3c\xbb\x20\x00\x5c\xd4\x20\x00\x5c\xd4\x20\x00\xdc\xc2","\xee\x76\x07\x68\x2a\x82\xf9\x8f"};
_EXTERN CHAR *CS_TARGET_INFO[LANG_SIZE] = {"\x54\x00\x41\x00\x52\x00\x47\x00\x45\x00\x54\x00\x20\x00\x53\x00\x48\x00\x49\x00\x50\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00","\xc0\xd0\x20\x00\x20\xc1\x20\x00\x15\xc8\x20\x00\xf4\xbc","\xee\x76\x07\x68\x39\x82\x36\x82\xe1\x4f\x6f\x60"};
_EXTERN CHAR *CS_MENU[LANG_SIZE] = {"\x4d\x00\x20\x00\x45\x00\x20\x00\x4e\x00\x20\x00\x55\x00","\x54\xba\x20\x00\x20\x00\x20\x00\x74\xb2","\xdc\x83\x55\x53"};
_EXTERN CHAR *CS_OWN_SHIP[LANG_SIZE] = {"\x4f\x00\x57\x00\x4e\x00\x20\x00\x53\x00\x48\x00\x49\x00\x50\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00","\x90\xc7\x20\x00\x20\xc1\x20\x00\x15\xc8\x20\x00\xf4\xbc","\x2c\x67\x39\x82\x70\x65\x6e\x63"};
_EXTERN CHAR *CS_MSG_MENU[LANG_SIZE] = {"\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x20\x00\x26\x00\x20\x00\x4c\x00\x4f\x00\x47\x00","\x54\xba\xdc\xc2\xc0\xc9\x20\x00\x26\x00\x20\x00\x5c\xb8\xf8\xad","\xe1\x4f\x6f\x60\x20\x00\x26\x00\x20\x00\xe5\x65\xd7\x5f"};
_EXTERN CHAR *CS_SRM[LANG_SIZE] = {"\x53\x00\x41\x00\x46\x00\x45\x00\x54\x00\x59\x00\x20\x00\x52\x00\x45\x00\x4c\x00\x41\x00\x54\x00\x45\x00\x44\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00","\x48\xc5\x20\x00\x04\xc8\x20\x00\x20\x00\x00\xad\x20\x00\x28\xb8\x20\x00\x20\x00\x54\xba\x20\x00\xdc\xc2\x20\x00\xc0\xc9","\x89\x5b\x68\x51\xe1\x4f\x6f\x60"};
_EXTERN CHAR *CS_NEW_MSG[LANG_SIZE] = {"\x4e\x00\x45\x00\x57\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00","\xc8\xc0\x20\x00\x54\xba\xdc\xc2\xc0\xc9","\xb0\x65\xe1\x4f\x6f\x60"};
_EXTERN CHAR *CS_RX_LIST[LANG_SIZE] = {"\x52\x00\x78\x00\x44\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00","\x18\xc2\xe0\xc2\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\xa9\xba\x5d\xb8","\xa5\x63\x36\x65\x70\x65\x6e\x63\xe1\x4f\x6f\x60\x17\x52\x68\x88"};
_EXTERN CHAR *CS_TX_LIST[LANG_SIZE] = {"\x54\x00\x78\x00\x44\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00","\xa1\xc1\xe0\xc2\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\xa9\xba\x5d\xb8","\xd1\x53\x04\x5c\x70\x65\x6e\x63\xe1\x4f\x6f\x60\x17\x52\x68\x88"};
_EXTERN CHAR *CS_FAVOR_MSG[LANG_SIZE] = {"\x46\x00\x41\x00\x56\x00\x4f\x00\x52\x00\x49\x00\x54\x00\x45\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00","\x90\xc9\xa8\xac\xf0\xc4\x94\xb2\x20\x00\x54\xba\xdc\xc2\xc0\xc9","\x20\x00"};
_EXTERN CHAR *CS_INTERROGATION[LANG_SIZE] = {"\x49\x00\x4e\x00\x54\x00\x45\x00\x52\x00\x52\x00\x4f\x00\x47\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00","\xc1\xc0\x20\x00\xdc\xd0\x20\x00\x20\x00\x94\xc6\x20\x00\xad\xcc","\xe2\x8b\xee\x95"};
_EXTERN CHAR *CS_LONG_RANGE_MSG[LANG_SIZE] = {"\x4c\x00\x4f\x00\x4e\x00\x47\x00\x20\x00\x52\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00","\xd0\xc6\x20\x00\x70\xac\x20\x00\xac\xb9\x20\x00\x20\x00\x54\xba\x20\x00\xdc\xc2\x20\x00\xc0\xc9","\xdc\x8f\x0b\x7a\xe1\x4f\x6f\x60\x17\x52\x68\x88"};
_EXTERN CHAR *CS_ALARM_LIST[LANG_SIZE] = {"\x41\x00\x4c\x00\x41\x00\x52\x00\x4d\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00","\x4c\xc5\x20\x00\x8c\xb7\x20\x00\xa9\xba\x20\x00\x5d\xb8","\xa5\x62\x66\x8b\x17\x52\x68\x88"};
_EXTERN CHAR *CS_STATUS_LIST[LANG_SIZE] = {"\x53\x00\x54\x00\x41\x00\x54\x00\x55\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00","\xc1\xc0\x20\x00\xdc\xd0\x20\x00\xa9\xba\x20\x00\x5d\xb8","\xb6\x72\x01\x60\x17\x52\x68\x88"};
_EXTERN CHAR *CS_WEATHER_LIST[LANG_SIZE] = {"\x53\x00\x48\x00\x4f\x00\x52\x00\x45\x00\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00","\x00\xad\x20\x00\x21\xce\x20\x00\x15\xc8\x20\x00\xf4\xbc","\x53\x00\x48\x00\x4f\x00\x52\x00\x45\x00\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00"};
_EXTERN CHAR *CS_INIT_SETUP[LANG_SIZE] = {"\x49\x00\x4e\x00\x49\x00\x54\x00\x20\x00\x53\x00\x45\x00\x54\x00\x55\x00\x50\x00","\x08\xcd\x20\x00\x30\xae\x20\x00\x24\xc1\x20\x00\x15\xc8","\x1d\x52\xcb\x59\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_STATIC_DATA[LANG_SIZE] = {"\x53\x00\x48\x00\x49\x00\x50\x00\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x49\x00\x43\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00","\x20\xc1\x15\xbc\x20\x00\x15\xc8\x01\xc8\x20\x00\x15\xc8\xf4\xbc","\x59\x97\x01\x60\x70\x65\x6e\x63\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_VOYAGE_DATA[LANG_SIZE] = {"\x53\x00\x48\x00\x49\x00\x50\x00\x20\x00\x56\x00\x4f\x00\x59\x00\x41\x00\x47\x00\x45\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00","\x20\xc1\x15\xbc\x20\x00\x6d\xd5\x74\xd5\x20\x00\x15\xc8\xf4\xbc","\x2a\x82\x4c\x88\x70\x65\x6e\x63\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_REGIONAL[LANG_SIZE] = {"\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x53\x00","\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\x24\xc1\x15\xc8","\x3a\x53\xdf\x57\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_REGIONAL_VIEW[LANG_SIZE] = {"\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x53\x00\x20\x00\x45\x00\x44\x00\x49\x00\x54\x00","\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\xb8\xd3\xd1\xc9","\x3a\x53\xdf\x57\x16\x7f\x91\x8f"};
_EXTERN CHAR *CS_REGIONAL_CREATE[LANG_SIZE] = {"\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x53\x00\x20\x00\x43\x00\x52\x00\x45\x00\x41\x00\x54\x00\x45\x00","\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\xdd\xc0\x31\xc1","\x3a\x53\xdf\x57\x1b\x52\xfa\x5e"};
_EXTERN CHAR *CS_LONG_RANGE[LANG_SIZE] = {"\x4c\x00\x4f\x00\x4e\x00\x47\x00\x20\x00\x52\x00\x41\x00\x4e\x00\x47\x00\x45\x00","\xd0\xc6\x70\xac\xac\xb9\x20\x00\x24\xc1\x15\xc8","\xdc\x8f\x0b\x7a\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_ANTENNA_POS[LANG_SIZE] = {"\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x41\x00\x4e\x00\x54\x00\x45\x00\x4e\x00\x4e\x00\x41\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00","\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x48\xc5\x4c\xd1\x98\xb0\x20\x00\x04\xc7\x58\xce","\x47\x00\x4e\x00\x53\x00\x53\x00\x29\x59\xbf\x7e\x4d\x4f\x6e\x7f\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_SYSTEM_SETUP[LANG_SIZE] = {"\x53\x00\x59\x00\x53\x00\x54\x00\x45\x00\x4d\x00\x20\x00\x53\x00\x45\x00\x54\x00\x55\x00\x50\x00","\xdc\xc2\xa4\xc2\x5c\xd1\x20\x00\x24\xc1\x15\xc8","\xfb\x7c\xdf\x7e\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_PORT[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x20\x00\x49\x00\x2f\x00\x4f\x00\x20\x00\x50\x00\x4f\x00\x52\x00\x54\x00","\x85\xc7\x9c\xcd\x25\xb8\x20\x00\xec\xd3\xb8\xd2\x20\x00\x24\xc1\x15\xc8","\x93\x8f\x65\x51\x2f\x00\x93\x8f\xfa\x51\x20\x00\xef\x7a\xe3\x53\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_DISPLAY[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x20\x00\x44\x00\x49\x00\x53\x00\x50\x00\x4c\x00\x41\x00\x59\x00","\x54\xd6\x20\x00\x74\xba\x20\x00\x20\x00\x24\xc1\x20\x00\x15\xc8","\x3e\x66\x3a\x79\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_BUZZER[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x20\x00\x42\x00\x55\x00\x5a\x00\x5a\x00\x45\x00\x52\x00","\x80\xbd\x20\x00\x00\xc8\x20\x00\x20\x00\x24\xc1\x20\x00\x15\xc8","\x02\x87\x23\x9e\x68\x56\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_PASSWORD_SET[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x20\x00\x50\x00\x41\x00\x53\x00\x53\x00\x57\x00\x4f\x00\x52\x00\x44\x00","\x44\xbe\x00\xbc\x88\xbc\x38\xd6\x20\x00\x24\xc1\x15\xc8","\xc6\x5b\x01\x78\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_SET_ETC[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x20\x00\x45\x00\x74\x00\x63\x00\x2e\x00","\x30\xae\xc0\xd0\x20\x00\x24\xc1\x15\xc8","\x76\x51\xd6\x4e\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_INITIALIZE[LANG_SIZE] = {"\x49\x00\x4e\x00\x49\x00\x54\x00\x49\x00\x41\x00\x4c\x00\x49\x00\x5a\x00\x45\x00\x20\x00\x53\x00\x59\x00\x53\x00\x54\x00\x45\x00\x4d\x00","\xdc\xc2\x20\x00\xa4\xc2\x20\x00\x5c\xd1\x20\x00\x20\x00\x08\xcd\x20\x00\x30\xae\x20\x00\x54\xd6","\xfb\x7c\xdf\x7e\x1d\x52\xcb\x59\x16\x53"};
_EXTERN CHAR *CS_MAINTENANCE[LANG_SIZE] = {"\x4d\x00\x41\x00\x49\x00\x4e\x00\x54\x00\x45\x00\x4e\x00\x41\x00\x4e\x00\x43\x00\x45\x00","\x00\xad\x20\x00\xac\xb9","\xf4\x7e\xa4\x62"};
_EXTERN CHAR *CS_PROGRAM_VERSION[LANG_SIZE] = {"\x50\x00\x52\x00\x4f\x00\x47\x00\x52\x00\x41\x00\x4d\x00\x20\x00\x56\x00\x45\x00\x52\x00\x53\x00\x49\x00\x4f\x00\x4e\x00","\x04\xd5\x5c\xb8\xf8\xad\xa8\xb7\x20\x00\x84\xbc\x04\xc8","\xe5\x67\x0b\x77\x0b\x7a\x8f\x5e\x48\x72\x2c\x67"};
_EXTERN CHAR *CS_KEY_TEST[LANG_SIZE] = {"\x4b\x00\x45\x00\x59\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00","\xa4\xd0\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2","\x2e\x95\xd8\x76\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *CS_LCD_TEST[LANG_SIZE] = {"\x4c\x00\x43\x00\x44\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00","\x4c\x00\x43\x00\x44\x00\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2","\xb2\x6d\x76\x66\x3e\x66\x3a\x79\x68\x56\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *CS_VIEW_INPUT_DATA[LANG_SIZE] = {"\x43\x00\x4f\x00\x4d\x00\x20\x00\x4d\x00\x4f\x00\x4e\x00\x49\x00\x54\x00\x4f\x00\x52\x00\x49\x00\x4e\x00\x47\x00","\x85\xc7\x25\xb8\x20\x00\xa8\xba\xc8\xb2\x30\xd1\xc1\xb9","\x1a\x90\xe1\x4f\xd1\x76\x4b\x6d"};
_EXTERN CHAR *CS_SECURITY_LOG[LANG_SIZE] = {"\x53\x00\x45\x00\x43\x00\x55\x00\x52\x00\x49\x00\x54\x00\x59\x00\x20\x00\x4c\x00\x4f\x00\x47\x00","\xf4\xbc\x20\x00\x48\xc5\x20\x00\x20\x00\x5c\xb8\x20\x00\xf8\xad","\x89\x5b\x68\x51\xb0\x8b\x55\x5f"};
_EXTERN CHAR *CS_TP_TEST_MENU[LANG_SIZE] = {"\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x50\x00\x4f\x00\x4e\x00\x44\x00\x45\x00\x52\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00","\xb8\xd2\x9c\xb7\xa4\xc2\xf0\xd3\x54\xb3\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2","\x94\x5e\x54\x7b\x68\x56\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *CS_TRANSCEIVER_TEST[LANG_SIZE] = {"\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x43\x00\x45\x00\x49\x00\x56\x00\x45\x00\x52\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00","\xa1\xc1\x20\x00\xe0\xc2\x20\x00\x20\x00\x4c\xd1\x20\x00\xa4\xc2\x20\x00\xb8\xd2","\xd1\x53\x04\x5c\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *CS_RECEIVER_TEST[LANG_SIZE] = {"\x52\x00\x45\x00\x43\x00\x45\x00\x49\x00\x56\x00\x45\x00\x52\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00","\x18\xc2\x20\x00\xe0\xc2\x20\x00\x20\x00\x4c\xd1\x20\x00\xa4\xc2\x20\x00\xb8\xd2","\xa5\x63\x36\x65\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *CS_VDL_TEST[LANG_SIZE] = {"\x56\x00\x44\x00\x4c\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00","\x56\x00\x44\x00\x4c\x00\x20\x00\x4c\xd1\x20\x00\xa4\xc2\x20\x00\xb8\xd2","\x56\x00\x44\x00\x4c\x00\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *CS_FIND_TGT[LANG_SIZE] = {"\x53\x00\x45\x00\x41\x00\x52\x00\x43\x00\x48\x00\x20\x00\x54\x00\x41\x00\x52\x00\x47\x00\x45\x00\x54\x00","\x3c\xbb\x5c\xd4\x3e\xcc\x30\xae","\xee\x76\x07\x68\xd6\x53\xde\x56"};
_EXTERN CHAR *CS_SET_TP_PARAMETER[LANG_SIZE] = {"\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x50\x00\x4f\x00\x4e\x00\x44\x00\x45\x00\x52\x00\x20\x00\x50\x00\x41\x00\x52\x00\x41\x00\x4d\x00\x45\x00\x54\x00\x45\x00\x52\x00","\xb8\xd2\x9c\xb7\xa4\xc2\xf0\xd3\x54\xb3\x20\x00\x24\xc1\x15\xc8\x20\x00\xc0\xbc\xbd\xac","\xc2\x53\x70\x65\xbe\x8b\x6e\x7f"};
_EXTERN CHAR *CS_PASSWORD_CHECK[LANG_SIZE] = {"\x50\x00\x41\x00\x53\x00\x53\x00\x57\x00\x4f\x00\x52\x00\x44\x00\x20\x00\x43\x00\x48\x00\x45\x00\x43\x00\x4b\x00","\x44\xbe\x00\xbc\x88\xbc\x38\xd6\x20\x00\x55\xd6\x78\xc7","\xc0\x68\xe5\x67\xc6\x5b\x01\x78"};
_EXTERN CHAR *CS_PROGRAM_UPLOAD[LANG_SIZE] = {"\x50\x00\x52\x00\x4f\x00\x47\x00\x52\x00\x41\x00\x4d\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00","\x04\xd5\x5c\xb8\xf8\xad\xa8\xb7\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4","\x0b\x7a\x8f\x5e\x0a\x4e\x7d\x8f"};
_EXTERN CHAR *CS_CN_STR[LANG_SIZE] = {"\x43\x00\x48\x00\x49\x00\x4e\x00\x45\x00\x53\x00\x45\x00","\x11\xc9\x38\xbb\x20\x00\x85\xc7\x25\xb8","\x2d\x4e\x87\x65\x93\x8f\x65\x51"};
_EXTERN CHAR *CS_RSS_STR[LANG_SIZE] = {"\x52\x00\x53\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00","\x52\x00\x53\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00","\x52\x00\x53\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00"};


_EXTERN CHAR *FK_MENU[LANG_SIZE] = {"\x4d\x00\x45\x00\x4e\x00\x55\x00","\x54\xba\x74\xb2","\xdc\x83\x55\x53"};
_EXTERN CHAR *FK_MMSI[LANG_SIZE] = {"\x4d\x00\x4d\x00\x53\x00\x49\x00","\x4d\x00\x4d\x00\x53\x00\x49\x00","\x4d\x00\x4d\x00\x53\x00\x49\x00"};
_EXTERN CHAR *FK_NAME[LANG_SIZE] = {"\x4e\x00\x41\x00\x4d\x00\x45\x00","\x74\xc7\x84\xb9","\x39\x82\x0d\x54"};
_EXTERN CHAR *FK_TARGET_LIST[LANG_SIZE] = {"\x4c\x00\x49\x00\x53\x00\x54\x00","\xa9\xba\x5d\xb8","\x17\x52\x68\x88"};
_EXTERN CHAR *FK_TARGET_PLOT[LANG_SIZE] = {"\x50\x00\x4c\x00\x4f\x00\x54\x00","\x5c\xd4\xdc\xc2","\x2a\x82\xf9\x8f\xfe\x56"};
_EXTERN CHAR *FK_OWN_SHIP[LANG_SIZE] = {"\x4f\x00\x57\x00\x4e\x00","\x90\xc7\x20\xc1","\x2c\x67\x39\x82"};
_EXTERN CHAR *FK_SORT[LANG_SIZE] = {"\x53\x00\x4f\x00\x52\x00\x54\x00","\x15\xc8\x2c\xb8","\x06\x52\x7b\x7c"};
_EXTERN CHAR *FK_EXT_INFO[LANG_SIZE] = {"\x49\x00\x4e\x00\x46\x00\x4f\x00","\x15\xc8\xf4\xbc","\xe1\x4f\x6f\x60"};
_EXTERN CHAR *FK_EXIT[LANG_SIZE] = {"\x45\x00\x58\x00\x49\x00\x54\x00","\xf5\xbc\xc0\xad","\x00\x90\xfa\x51"};
_EXTERN CHAR *FK_NEXT[LANG_SIZE] = {"\x4e\x00\x45\x00\x58\x00\x54\x00","\xe4\xb2\x4c\xc7","\x0b\x4e\x00\x4e\x2a\x4e"};
_EXTERN CHAR *FK_PREV[LANG_SIZE] = {"\x50\x00\x52\x00\x45\x00\x56\x00","\x74\xc7\x04\xc8","\x0a\x4e\x00\x4e\xa7\x7e"};
_EXTERN CHAR *FK_SEND[LANG_SIZE] = {"\x53\x00\x45\x00\x4e\x00\x44\x00","\x04\xc8\xa1\xc1","\xd1\x53\x01\x90"};
_EXTERN CHAR *FK_FORWARD[LANG_SIZE] = {"\x46\x00\x52\x00\x57\x00\x44\x00","\x04\xc8\xec\xb2","\x11\x54\x4d\x52"};
_EXTERN CHAR *FK_REPLY[LANG_SIZE] = {"\x52\x00\x45\x00\x50\x00\x4c\x00\x59\x00","\xf5\xb2\xa5\xc7","\x54\x7b\x0d\x59"};
_EXTERN CHAR *FK_DELETE_MSG[LANG_SIZE] = {"\x44\x00\x45\x00\x4c\x00\x45\x00\x54\x00\x45\x00","\xad\xc0\x1c\xc8","\x20\x52\x64\x96"};
_EXTERN CHAR *FK_SAVE[LANG_SIZE] = {"\x53\x00\x41\x00\x56\x00\x45\x00","\x00\xc8\xa5\xc7","\x58\x5b\xa8\x50"};
_EXTERN CHAR *FK_DEFT[LANG_SIZE] = {"\x44\x00\x45\x00\x46\x00\x41\x00\x55\x00\x4c\x00\x54\x00","\x08\xcd\x30\xae\x54\xd6","\x1d\x52\xcb\x59\x16\x53"};
_EXTERN CHAR *FK_SEARCH[LANG_SIZE] = {"\x53\x00\x45\x00\x41\x00\x52\x00\x43\x00\x48\x00","\x3e\xcc\x30\xae","\xd6\x53\xde\x56"};
_EXTERN CHAR *FK_SELECT[LANG_SIZE] = {"\x53\x00\x45\x00\x4c\x00\x45\x00\x43\x00\x54\x00","\x20\xc1\xdd\xd0","\x09\x90\xe9\x62"};
_EXTERN CHAR *FK_DELETE[LANG_SIZE] = {"\x44\x00\x45\x00\x4c\x00\x45\x00\x54\x00\x45\x00","\xad\xc0\x1c\xc8","\x20\x52\x64\x96"};
_EXTERN CHAR *FK_YES[LANG_SIZE] = {"\x59\x00\x45\x00\x53\x00","\x08\xc6","\x2f\x66"};
_EXTERN CHAR *FK_NO[LANG_SIZE] = {"\x4e\x00\x4f\x00","\x44\xc5\xc8\xb2\x24\xc6","\x26\x54"};
_EXTERN CHAR *FK_OK[LANG_SIZE] = {"\x4f\x00\x4b\x00","\x55\xd6\x78\xc7","\x4f\x00\x4b\x00"};
_EXTERN CHAR *FK_CANCEL[LANG_SIZE] = {"\x43\x00\x41\x00\x4e\x00\x43\x00\x45\x00\x4c\x00","\xe8\xcd\x8c\xc1","\x20\x00"};
_EXTERN CHAR *FK_CHANGE[LANG_SIZE] = {"\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00","\xc0\xbc\xbd\xac","\x39\x65\xd8\x53"};
_EXTERN CHAR *FK_APPLY[LANG_SIZE] = {"\x41\x00\x50\x00\x50\x00\x4c\x00\x59\x00","\x01\xc8\xa9\xc6","\x94\x5e\x28\x75"};
_EXTERN CHAR *FK_VIEW[LANG_SIZE] = {"\x45\x00\x44\x00\x49\x00\x54\x00","\xb8\xd3\xd1\xc9","\x16\x7f\x91\x8f"};
_EXTERN CHAR *FK_CREATE[LANG_SIZE] = {"\x43\x00\x52\x00\x45\x00\x41\x00\x54\x00\x45\x00","\xdd\xc0\x31\xc1","\x1b\x52\xfa\x5e"};
_EXTERN CHAR *FK_ACK[LANG_SIZE] = {"\x41\x00\x43\x00\x4b\x00","\x55\xd6\x78\xc7","\x94\x5e\x54\x7b"};
_EXTERN CHAR *FK_HEAD_UP[LANG_SIZE] = {"\x48\x00\x20\x00\x55\x00\x50\x00","\x48\x00\x20\x00\x55\x00\x50\x00","\x4f\x82\x11\x54\x0a\x4e"};
_EXTERN CHAR *FK_NORTH_UP[LANG_SIZE] = {"\x4e\x00\x20\x00\x55\x00\x50\x00","\x4e\x00\x20\x00\x55\x00\x50\x00","\x17\x53\x11\x54\x0a\x4e"};
_EXTERN CHAR *FK_SEND_MSG[LANG_SIZE] = {"\x4d\x00\x53\x00\x47\x00","\x54\xba\xdc\xc2\xc0\xc9","\xe1\x4f\x6f\x60"};
_EXTERN CHAR *FK_LOAD[LANG_SIZE] = {"\x4c\x00\x4f\x00\x41\x00\x44\x00","\x5c\xb8\x29\xb5","\xa0\x52\x7d\x8f"};
_EXTERN CHAR *FK_PAUSE[LANG_SIZE] = {"\x50\x00\x41\x00\x55\x00\x53\x00\x45\x00","\x48\xba\xa4\xcd","\x82\x66\x5c\x50"};
_EXTERN CHAR *FK_START[LANG_SIZE] = {"\x53\x00\x54\x00\x41\x00\x52\x00\x54\x00","\xdc\xc2\x91\xc7","\x00\x5f\xcb\x59"};
_EXTERN CHAR *FK_STOP[LANG_SIZE] = {"\x53\x00\x54\x00\x4f\x00\x50\x00","\x11\xc9\xc0\xc9","\x5c\x50\x62\x6b"};
_EXTERN CHAR *FK_TEST[LANG_SIZE] = {"\x54\x00\x45\x00\x53\x00\x54\x00","\x4c\xd1\xa4\xc2\xb8\xd2","\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *FK_REBOOT[LANG_SIZE] = {"\x52\x00\x45\x00\x42\x00\x4f\x00\x4f\x00\x54\x00","\xac\xc7\x00\xac\xd9\xb3","\xcd\x91\x2f\x54\xa8\x52"};
_EXTERN CHAR *FK_RANDOM[LANG_SIZE] = {"\x52\x00\x41\x00\x4e\x00\x44\x00\x4f\x00\x4d\x00","\x84\xc7\x58\xc7","\x8f\x96\x3a\x67"};
_EXTERN CHAR *FK_PARAM[LANG_SIZE] = {"\x50\x00\x41\x00\x52\x00\x41\x00\x4d\x00","\x24\xc1\x15\xc8","\xc2\x53\x70\x65"};
_EXTERN CHAR *FK_TEST_MODE[LANG_SIZE] = {"\x54\x00\x2d\x00\x4d\x00\x4f\x00\x44\x00\x45\x00","\x4c\xd1\xa4\xc2\xb8\xd2","\x4b\x6d\xd5\x8b"};
_EXTERN CHAR *FK_PROGRAM[LANG_SIZE] = {"\x50\x00\x52\x00\x4f\x00\x47\x00","\x50\x00\x52\x00\x4f\x00\x47\x00","\x0b\x7a\x8f\x5e"};
_EXTERN CHAR *FK_MAP[LANG_SIZE] = {"\x4d\x00\x41\x00\x50\x00","\x4d\x00\x41\x00\x50\x00","\x77\x6d\xfe\x56"};
_EXTERN CHAR *FK_UPLOAD[LANG_SIZE] = {"\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00","\xc5\xc5\x5c\xb8\xdc\xb4","\x0a\x4e\x7d\x8f"};
_EXTERN CHAR *FK_ALARM[LANG_SIZE] = {"\x41\x00\x6c\x00\x61\x00\x72\x00\x6d\x00","\x4c\xc5\x8c\xb7","\x41\x00\x6c\x00\x61\x00\x72\x00\x6d\x00"};


_EXTERN CHAR *MNU_MESSAGE[LANG_SIZE] = {"\x20\x00\x31\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x20\x00\x26\x00\x20\x00\x4c\x00\x4f\x00\x47\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\x26\x00\x20\x00\x5c\xb8\xf8\xad\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\xe1\x4f\x6f\x60\x20\x00\x26\x00\x20\x00\xe5\x65\xd7\x5f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_INIT_SETUP[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x49\x00\x4e\x00\x49\x00\x54\x00\x20\x00\x53\x00\x45\x00\x54\x00\x55\x00\x50\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x08\xcd\x30\xae\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x1d\x52\xcb\x59\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_SYSTEM_SETUP[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x53\x00\x59\x00\x53\x00\x54\x00\x45\x00\x4d\x00\x20\x00\x53\x00\x45\x00\x54\x00\x55\x00\x50\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\xdc\xc2\xa4\xc2\x5c\xd1\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\xfb\x7c\xdf\x7e\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENACE[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x4d\x00\x41\x00\x49\x00\x4e\x00\x54\x00\x45\x00\x4e\x00\x41\x00\x4e\x00\x43\x00\x45\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x00\xad\x20\x00\xac\xb9\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xf4\x7e\xa4\x62\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_TRANSMIT_ON[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x4d\x00\x49\x00\x54\x00\x54\x00\x45\x00\x52\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x4f\x00\x4e\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\xa1\xc1\xe0\xc2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x4f\x00\x4e\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x4d\x00\x49\x00\x54\x00\x54\x00\x45\x00\x52\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x4f\x00\x4e\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_TRANSMIT_OFF[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x4d\x00\x49\x00\x54\x00\x54\x00\x45\x00\x52\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x4f\x00\x46\x00\x46\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\xa1\xc1\xe0\xc2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x4f\x00\x46\x00\x46\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x4d\x00\x49\x00\x54\x00\x54\x00\x45\x00\x52\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x4f\x00\x46\x00\x46\x00\x20\x00\x20\x00\x20\x00\x20\x00"};


_EXTERN CHAR *MNU_MSG_NEW[LANG_SIZE] = {"\x20\x00\x31\x00\x20\x00\x4e\x00\x45\x00\x57\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\xc8\xc0\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\xb0\x65\xe1\x4f\x6f\x60\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_FAVOR[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x46\x00\x41\x00\x56\x00\x4f\x00\x52\x00\x49\x00\x54\x00\x45\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x90\xc9\xa8\xac\xf0\xc4\x94\xb2\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x59\x75\x28\x75\x84\x76\xe1\x4f\x6f\x60\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_INTERROGATION[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x49\x00\x4e\x00\x54\x00\x45\x00\x52\x00\x52\x00\x4f\x00\x47\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x15\xc8\xf4\xbc\x20\x00\x94\xc6\xad\xcc\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\xe2\x8b\xee\x95\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_LRM[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x4c\x00\x4f\x00\x4e\x00\x47\x00\x20\x00\x52\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\xd0\xc6\x70\xac\xac\xb9\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\xa9\xba\x5d\xb8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\xdc\x8f\x0b\x7a\xe1\x4f\x6f\x60\x17\x52\x68\x88\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_RX_LIST[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x52\x00\x78\x00\x44\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x18\xc2\xe0\xc2\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\xa9\xba\x5d\xb8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xa5\x63\x36\x65\x70\x65\x6e\x63\xe1\x4f\x6f\x60\x17\x52\x68\x88\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_TX_LIST[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x54\x00\x78\x00\x44\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\xa1\xc1\xe0\xc2\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\xa9\xba\x5d\xb8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\xd1\x53\x04\x5c\x70\x65\x6e\x63\xe1\x4f\x6f\x60\x17\x52\x68\x88\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_ALARM_LIST[LANG_SIZE] = {"\x20\x00\x36\x00\x20\x00\x41\x00\x4c\x00\x41\x00\x52\x00\x4d\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x36\x00\x20\x00\x4c\xc5\x8c\xb7\x20\x00\xa9\xba\x5d\xb8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x36\x00\x20\x00\xa5\x62\x66\x8b\x17\x52\x68\x88\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_STATUS_LIST[LANG_SIZE] = {"\x20\x00\x37\x00\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x55\x00\x53\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x37\x00\x20\x00\xc1\xc0\xdc\xd0\x20\x00\xa9\xba\x5d\xb8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x37\x00\x20\x00\xb6\x72\x01\x60\x17\x52\x68\x88\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MSG_WEATHER_LIST[LANG_SIZE] = {"\x20\x00\x38\x00\x20\x00\x57\x00\x45\x00\x41\x00\x54\x00\x48\x00\x45\x00\x52\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x38\x00\x20\x00\x30\xae\xc1\xc0\x20\x00\x04\xd6\x69\xd6\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x38\x00\x20\x00\x57\x00\x45\x00\x41\x00\x54\x00\x48\x00\x45\x00\x52\x00\x20\x00\x4c\x00\x49\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};


_EXTERN CHAR *MNU_INIT_VOYAGE[LANG_SIZE] = {"\x20\x00\x31\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x56\x00\x4f\x00\x59\x00\x41\x00\x47\x00\x45\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\x6d\xd5\x74\xd5\x20\x00\x15\xc8\xf4\xbc\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\x2a\x82\x4c\x88\x70\x65\x6e\x63\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_INIT_STATIC[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x49\x00\x43\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x15\xc8\x01\xc8\x20\x00\x15\xc8\xf4\xbc\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x59\x97\x01\x60\x70\x65\x6e\x63\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_INIT_REGIONAL[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x53\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x3a\x53\xdf\x57\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_INIT_LONGRANGE[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x4c\x00\x4f\x00\x4e\x00\x47\x00\x20\x00\x52\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xd0\xc6\x70\xac\xac\xb9\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xdc\x8f\x0b\x7a\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_INIT_ANTENNA[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x41\x00\x4e\x00\x54\x00\x45\x00\x4e\x00\x4e\x00\x41\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x48\xc5\x4c\xd1\x98\xb0\x20\x00\x04\xc7\x58\xce\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x47\x00\x4e\x00\x53\x00\x53\x00\x29\x59\xbf\x7e\x4d\x4f\x6e\x7f\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_INIT_RESTORE[LANG_SIZE] = {"\x20\x00\x36\x00\x20\x00\x53\x00\x59\x00\x53\x00\x54\x00\x45\x00\x4d\x00\x20\x00\x52\x00\x45\x00\x53\x00\x54\x00\x4f\x00\x52\x00\x45\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x36\x00\x20\x00\xdc\xc2\xa4\xc2\x5c\xd1\x20\x00\xf5\xbc\xd0\xc6\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x36\x00\x20\x00\xfb\x7c\xdf\x7e\xcd\x91\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_INIT_VHF[LANG_SIZE] = {"\x20\x00\x37\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x56\x00\x48\x00\x46\x00\x20\x00\x52\x00\x41\x00\x44\x00\x49\x00\x4f\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x37\x00\x20\x00\x56\x00\x48\x00\x46\x00\x20\x00\x48\xc5\x4c\xd1\x98\xb0\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x37\x00\x20\x00\x1a\x75\xd8\x9a\x91\x98\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};


_EXTERN CHAR *MNU_SYSTEM_IO_PORT[LANG_SIZE] = {"\x20\x00\x31\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x49\x00\x2f\x00\x4f\x00\x20\x00\x50\x00\x4f\x00\x52\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\x85\xc7\x9c\xcd\x25\xb8\x20\x00\xec\xd3\xb8\xd2\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\x93\x8f\x65\x51\x2f\x00\x93\x8f\xfa\x51\x20\x00\xef\x7a\xe3\x53\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_SYSTEM_BRIGHTNESS[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x44\x00\x49\x00\x53\x00\x50\x00\x4c\x00\x41\x00\x59\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x54\xd6\x74\xba\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x3e\x66\x3a\x79\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_SYSTEM_BUZZER[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x42\x00\x55\x00\x5a\x00\x5a\x00\x45\x00\x52\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x80\xbd\x00\xc8\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x02\x87\x23\x9e\x68\x56\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_SYSTEM_PASSWORD[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x50\x00\x41\x00\x53\x00\x53\x00\x57\x00\x4f\x00\x52\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x44\xbe\x00\xbc\x88\xbc\x38\xd6\x20\x00\xc0\xbc\xbd\xac\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xc6\x5b\x01\x78\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_SYSTEM_ETC[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x45\x00\x54\x00\x43\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x30\xae\xc0\xd0\x20\x00\x24\xc1\x15\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x76\x51\xd6\x4e\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};


_EXTERN CHAR *MNU_MAINTENANCE_VERSION[LANG_SIZE] = {"\x20\x00\x31\x00\x20\x00\x50\x00\x52\x00\x4f\x00\x47\x00\x52\x00\x41\x00\x4d\x00\x20\x00\x56\x00\x45\x00\x52\x00\x53\x00\x49\x00\x4f\x00\x4e\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\x04\xd5\x5c\xb8\xf8\xad\xa8\xb7\x20\x00\x84\xbc\x04\xc8\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\xe5\x67\x0b\x77\x0b\x7a\x8f\x5e\x48\x72\x2c\x67\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENANCE_KEY[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x4b\x00\x45\x00\x59\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\xa4\xd0\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x2e\x95\xd8\x76\x4b\x6d\xd5\x8b\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENANCE_LCD[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x4c\x00\x43\x00\x44\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x4c\x00\x43\x00\x44\x00\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\xb2\x6d\x76\x66\x3e\x66\x3a\x79\x68\x56\x4b\x6d\xd5\x8b\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENANCE_INPUT[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x43\x00\x4f\x00\x4d\x00\x20\x00\x4d\x00\x4f\x00\x4e\x00\x49\x00\x54\x00\x4f\x00\x52\x00\x49\x00\x4e\x00\x47\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x85\xc7\x25\xb8\x20\x00\xa8\xba\xc8\xb2\x30\xd1\xc1\xb9\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x1a\x90\xe1\x4f\xd1\x76\x4b\x6d\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENANCE_SECURITY[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x53\x00\x45\x00\x43\x00\x55\x00\x52\x00\x49\x00\x54\x00\x59\x00\x20\x00\x4c\x00\x4f\x00\x47\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\xf4\xbc\x48\xc5\x20\x00\x5c\xb8\xf8\xad\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x89\x5b\x68\x51\xb0\x8b\x55\x5f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENANCE_TP_TEST[LANG_SIZE] = {"\x20\x00\x36\x00\x20\x00\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x50\x00\x4f\x00\x4e\x00\x44\x00\x45\x00\x52\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x36\x00\x20\x00\xb8\xd2\x9c\xb7\xa4\xc2\xf0\xd3\x54\xb3\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x36\x00\x20\x00\x94\x5e\x54\x7b\x68\x56\x4b\x6d\xd5\x8b\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENANCE_DOWNLOAD[LANG_SIZE] = {"\x20\x00\x37\x00\x20\x00\x50\x00\x52\x00\x4f\x00\x47\x00\x52\x00\x41\x00\x4d\x00\x20\x00\x44\x00\x4f\x00\x57\x00\x4e\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x37\x00\x20\x00\x04\xd5\x5c\xb8\xf8\xad\xa8\xb7\x20\x00\xe4\xb2\xb4\xc6\x5c\xb8\xdc\xb4\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x37\x00\x20\x00\x0b\x7a\x8f\x5e\x0b\x4e\x7d\x8f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_MAINTENANCE_UPLOAD[LANG_SIZE] = {"\x20\x00\x38\x00\x20\x00\x50\x00\x52\x00\x4f\x00\x47\x00\x52\x00\x41\x00\x4d\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x38\x00\x20\x00\x04\xd5\x5c\xb8\xf8\xad\xa8\xb7\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x38\x00\x20\x00\x0b\x7a\x8f\x5e\x0a\x4e\x7d\x8f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};


_EXTERN CHAR *MNU_UPLOAD_TRANSPONDER[LANG_SIZE] = {"\x20\x00\x31\x00\x20\x00\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x50\x00\x4f\x00\x4e\x00\x44\x00\x45\x00\x52\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\xb8\xd2\x9c\xb7\xb8\xd2\xf0\xd3\x54\xb3\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\x36\x65\xd1\x53\x3a\x67\x0a\x4e\x7d\x8f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_UPLOAD_MKD[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x4d\x00\x4b\x00\x44\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x4d\x00\x4b\x00\x44\x00\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x3e\x66\x3a\x79\x68\x56\x0a\x4e\x7d\x8f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_UPLOAD_MKD_CCFL[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x4d\x00\x4b\x00\x44\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x28\x00\x43\x00\x43\x00\x46\x00\x4c\x00\x29\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x4d\x00\x4b\x00\x44\x00\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x28\x00\x43\x00\x43\x00\x46\x00\x4c\x00\x29\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x3e\x66\x3a\x79\x68\x56\x0a\x4e\x7d\x8f\x28\x00\x43\x00\x43\x00\x46\x00\x4c\x00\x29\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_UPLOAD_MKD_LED[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x4d\x00\x4b\x00\x44\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x28\x00\x4c\x00\x45\x00\x44\x00\x29\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x4d\x00\x4b\x00\x44\x00\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x28\x00\x4c\x00\x45\x00\x44\x00\x29\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x3e\x66\x3a\x79\x68\x56\x0a\x4e\x7d\x8f\x28\x00\x4c\x00\x45\x00\x44\x00\x29\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_UPLOAD_MAP[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x4d\x00\x41\x00\x50\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xc0\xc9\xc4\xb3\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x77\x6d\xfe\x56\x0a\x4e\x7d\x8f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_UPLOAD_BOOTER[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x42\x00\x4f\x00\x4f\x00\x54\x00\x45\x00\x52\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x42\x00\x4f\x00\x4f\x00\x54\x00\x45\x00\x52\x00\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\x15\x5f\xfc\x5b\x3a\x53\x0a\x4e\x7d\x8f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_UPLOAD_LOADER[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x45\x00\x52\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x45\x00\x52\x00\x20\x00\xc5\xc5\x5c\xb8\xdc\xb4\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x45\x00\x52\x00\x20\x00\x55\x00\x50\x00\x4c\x00\x4f\x00\x41\x00\x44\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};


_EXTERN CHAR *MNU_TP_TRANSCEIVER_TEST[LANG_SIZE] = {"\x20\x00\x31\x00\x20\x00\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x43\x00\x45\x00\x49\x00\x56\x00\x45\x00\x52\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\xa1\xc1\xe0\xc2\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x31\x00\x20\x00\xd1\x53\x04\x5c\x4b\x6d\xd5\x8b\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_TP_RECEIVER_TEST[LANG_SIZE] = {"\x20\x00\x32\x00\x20\x00\x52\x00\x45\x00\x43\x00\x45\x00\x49\x00\x56\x00\x45\x00\x52\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\x18\xc2\xe0\xc2\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x32\x00\x20\x00\xa5\x63\x36\x65\x4b\x6d\xd5\x8b\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_TP_VDL_COMM_TEST[LANG_SIZE] = {"\x20\x00\x33\x00\x20\x00\x56\x00\x44\x00\x4c\x00\x20\x00\x54\x00\x45\x00\x53\x00\x54\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x56\x00\x44\x00\x4c\x00\x20\x00\x4c\xd1\xa4\xc2\xb8\xd2\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x33\x00\x20\x00\x56\x00\x44\x00\x4c\x00\x4b\x6d\xd5\x8b\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_TP_SET_PARAMETER[LANG_SIZE] = {"\x20\x00\x34\x00\x20\x00\x53\x00\x45\x00\x54\x00\x20\x00\x50\x00\x41\x00\x52\x00\x41\x00\x4d\x00\x45\x00\x54\x00\x45\x00\x52\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xb8\xd2\x9c\xb7\xa4\xc2\xf0\xd3\x54\xb3\x20\x00\x24\xc1\x15\xc8\x12\xac\x20\x00\xc0\xbc\xbd\xac\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x34\x00\x20\x00\xc2\x53\x70\x65\xbe\x8b\x6e\x7f\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};
_EXTERN CHAR *MNU_SYSTEM_INITIALIZE[LANG_SIZE] = {"\x20\x00\x35\x00\x20\x00\x49\x00\x4e\x00\x49\x00\x54\x00\x49\x00\x41\x00\x4c\x00\x49\x00\x5a\x00\x45\x00\x20\x00\x53\x00\x59\x00\x53\x00\x54\x00\x45\x00\x4d\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\xdc\xc2\xa4\xc2\x5c\xd1\x20\x00\x08\xcd\x30\xae\x54\xd6\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00","\x20\x00\x35\x00\x20\x00\xfb\x7c\xdf\x7e\x1d\x52\xcb\x59\x16\x53\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00\x20\x00"};


_EXTERN CHAR *STR_SHORE_ALERT[LANG_SIZE] = {"\x52\x00\x65\x00\x63\x00\x65\x00\x69\x00\x76\x00\x65\x00\x20\x00\x44\x00\x61\x00\x74\x00\x61\x00","\x18\xc2\xe0\xc2\x4c\xc5\xbc\xb9","\x52\x00\x65\x00\x63\x00\x65\x00\x69\x00\x76\x00\x65\x00\x20\x00\x44\x00\x61\x00\x74\x00\x61\x00"};


_EXTERN CHAR *STR_SHOREDATA[LANG_SIZE] = {"\x49\x00\x6e\x00\x66\x00\x6f\x00\x72\x00\x6d\x00\x61\x00\x74\x00\x69\x00\x6f\x00\x6e\x00","\x18\xc2\xe0\xc2\x15\xc8\xf4\xbc","\x49\x00\x6e\x00\x66\x00\x6f\x00\x72\x00\x6d\x00\x61\x00\x74\x00\x69\x00\x6f\x00\x6e\x00"};
_EXTERN CHAR *STR_LOCAL[LANG_SIZE] = {"\x41\x00\x52\x00\x45\x00\x41\x00","\xc0\xc9\xed\xc5","\x41\x00\x52\x00\x45\x00\x41\x00"};


_EXTERN CHAR *STR_SHORESECT[LANG_SIZE] = {"\x53\x00\x45\x00\x43\x00\x54\x00","\x6c\xad\x84\xbd","\x53\x00\x45\x00\x43\x00\x54\x00"};
_EXTERN CHAR *STR_SHOREMEMB[][LANG_SIZE] = {{"\x20\x00\x20\x00\x20\x00","\x20\x00\x20\x00\x20\x00","\x20\x00\x20\x00\x20\x00"},
                                            {"\x4c\x00\x2e\x00\x48\x00\x6f\x00\x75\x00\x73\x00\x65\x00\x20\x00","\xf1\xb4\x00\xb3","\x4c\x00\x2e\x00\x48\x00\x6f\x00\x75\x00\x73\x00\x65\x00\x20\x00"},
                                            {"\x4c\x00\x2e\x00\x42\x00\x65\x00\x61\x00\x63\x00\x6f\x00\x6e\x00","\xf1\xb4\x5c\xd4","\x4c\x00\x2e\x00\x42\x00\x65\x00\x61\x00\x63\x00\x6f\x00\x6e\x00"},
                                            {"\x4c\x00\x2e\x00\x42\x00\x75\x00\x6f\x00\x79\x00","\xf1\xb4\x80\xbd\x5c\xd4","\x4c\x00\x2e\x00\x42\x00\x75\x00\x6f\x00\x79\x00"},
                                            {"\x42\x00\x75\x00\x6f\x00\x79\x00","\x80\xbd\x74\xc7","\x42\x00\x75\x00\x6f\x00\x79\x00"}};
_EXTERN CHAR *STR_SHORELAT[LANG_SIZE] = {"\x4c\x00\x41\x00\x54\x00","\x04\xc7\xc4\xb3","\x4c\x00\x41\x00\x54\x00"};
_EXTERN CHAR *STR_SHORELON[LANG_SIZE] = {"\x4c\x00\x4f\x00\x4e\x00","\xbd\xac\xc4\xb3","\x4c\x00\x4f\x00\x4e\x00"};
_EXTERN CHAR *STR_SHORETIME[LANG_SIZE] = {"\x54\x00\x49\x00\x4d\x00\x45\x00","\x18\xc2\xe0\xc2\xdc\xc2\x04\xac","\x54\x00\x49\x00\x4d\x00\x45\x00"};
_EXTERN CHAR *STR_SHORETEMP[LANG_SIZE] = {"\x41\x00\x2e\x00\x54\x00\x45\x00\x4d\x00\x50\x00","\x30\xae\x28\xc6","\x41\x00\x2e\x00\x54\x00\x45\x00\x4d\x00\x50\x00"};
_EXTERN CHAR *STR_SHOREPRES[LANG_SIZE] = {"\x50\x00\x52\x00\x45\x00\x53\x00\x53\x00","\x30\xae\x55\xc5","\x50\x00\x52\x00\x45\x00\x53\x00\x53\x00"};
_EXTERN CHAR *STR_SHOREHUMI[LANG_SIZE] = {"\x52\x00\x2e\x00\x48\x00\x55\x00\x4d\x00","\xb5\xc2\xc4\xb3","\x52\x00\x2e\x00\x48\x00\x55\x00\x4d\x00"};
_EXTERN CHAR *STR_SHOREWINDDIRECT[LANG_SIZE] = {"\x57\x00\x2e\x00\x44\x00\x52\x00\x54\x00","\x8d\xd4\xa5\xd5","\x57\x00\x2e\x00\x44\x00\x52\x00\x54\x00"};
_EXTERN CHAR *STR_SHOREWINDSPEED[LANG_SIZE] = {"\x57\x00\x2e\x00\x53\x00\x50\x00\x44\x00","\x8d\xd4\x8d\xc1","\x57\x00\x2e\x00\x53\x00\x50\x00\x44\x00"};
_EXTERN CHAR *STR_SHORECURDIRECT[LANG_SIZE] = {"\x43\x00\x75\x00\x72\x00\x2e\x00\x44\x00\x52\x00\x54\x00","\x5c\xd4\x74\xba\x20\xc7\xa5\xd5","\x43\x00\x75\x00\x72\x00\x2e\x00\x44\x00\x52\x00\x54\x00"};
_EXTERN CHAR *STR_SHORECURSPEED[LANG_SIZE] = {"\x43\x00\x75\x00\x72\x00\x2e\x00\x53\x00\x50\x00\x44\x00","\x5c\xd4\x74\xba\x20\xc7\x8d\xc1","\x43\x00\x75\x00\x72\x00\x2e\x00\x53\x00\x50\x00\x44\x00"};
_EXTERN CHAR *STR_SHOREWATERTMP[LANG_SIZE] = {"\x57\x00\x2e\x00\x54\x00\x45\x00\x4d\x00\x50\x00","\x18\xc2\x28\xc6","\x57\x00\x2e\x00\x54\x00\x45\x00\x4d\x00\x50\x00"};
_EXTERN CHAR *STR_SHOREWAVEHIGH[LANG_SIZE] = {"\x57\x00\x2e\x00\x48\x00\x49\x00\x47\x00\x48\x00","\x0c\xd3\xe0\xac","\x57\x00\x2e\x00\x48\x00\x49\x00\x47\x00\x48\x00"};
_EXTERN CHAR *STR_SHOREWAVEDRT[LANG_SIZE] = {"\x57\x00\x2e\x00\x44\x00\x52\x00\x54\x00","\x0c\xd3\xa5\xd5","\x57\x00\x2e\x00\x44\x00\x52\x00\x54\x00"};


_EXTERN CHAR FK_CNSTR[] = { "", "","\x2d\x4e\x87\x65"};
_EXTERN CHAR STR_PINYIN[] = { "", "","\x2d\x4e\x87\x65\xfc\x62\xf3\x97"};
_EXTERN CHAR STR_ALPHABET[] = { "", "","\xf1\x82\x87\x65\x57\x5b\xcd\x6b"};
_EXTERN CHAR STR_CNI_CHAR[] = { "", "","\x2d\x4e\x87\x65\x93\x8f\x65\x51"};
_EXTERN CHAR STR_CNI_EDIT[] = { "", "","\x07\x52\x62\x63"};
_EXTERN CHAR STR_CNI_MSG[] = { "", "","\x88\x6d\x6f\x60\x85\x51\xb9\x5b"};
_EXTERN CHAR STR_CNI_ERRORMSG[] = { "", "","\x20\x00\x20\x00\x20\x00\x20\x00\x5b\x00\x20\x00\x19\x95\xef\x8b\xfc\x62\xf3\x97\x20\x00\x5d\x00"};


_EXTERN CHAR *STR_ACK[LANG_SIZE] = {"\x41\x00\x43\x00\x4b\x00","\x55\xd6\x78\xc7","\x94\x5e\x54\x7b"};
_EXTERN CHAR *STR_IMO_NO[LANG_SIZE] = {"\x49\x00\x4d\x00\x4f\x00\x20\x00\x4e\x00\x6f\x00\x2e\x00","\x49\x00\x4d\x00\x4f\x00\x20\x00\x88\xbc\x38\xd6","\x49\x00\x4d\x00\x4f\x00\x20\x00\xf7\x53\x01\x78"};
_EXTERN CHAR *STR_LAT_NE[LANG_SIZE] = {"\x4e\x00\x45\x00\x20\x00\x4c\x00\x61\x00\x74\x00","\x4e\x00\x45\x00\x04\xc7\xc4\xb3","\x4e\x00\x45\x00\xac\x7e\xa6\x5e"};
_EXTERN CHAR *STR_LAT_SW[LANG_SIZE] = {"\x53\x00\x57\x00\x20\x00\x4c\x00\x61\x00\x74\x00","\x53\x00\x57\x00\x04\xc7\xc4\xb3","\x53\x00\x57\x00\xac\x7e\xa6\x5e"};
_EXTERN CHAR *STR_LON_NE[LANG_SIZE] = {"\x4e\x00\x45\x00\x20\x00\x4c\x00\x6f\x00\x6e\x00","\x4e\x00\x45\x00\xbd\xac\xc4\xb3","\x4e\x00\x45\x00\xcf\x7e\xa6\x5e"};
_EXTERN CHAR *STR_LON_SW[LANG_SIZE] = {"\x53\x00\x57\x00\x20\x00\x4c\x00\x6f\x00\x6e\x00","\x53\x00\x57\x00\xbd\xac\xc4\xb3","\x53\x00\x57\x00\xcf\x7e\xa6\x5e"};
_EXTERN CHAR *STR_MODE[LANG_SIZE] = {"\x4d\x00\x4f\x00\x44\x00\x45\x00","\xa8\xba\xdc\xb4","\x21\x6a\x0f\x5f"};
_EXTERN CHAR *STR_PERSONS_TITLE[LANG_SIZE] = {"\x50\x00\x45\x00\x52\x00\x53\x00\x4f\x00\x4e\x00\x53\x00","\xb9\xc2\x20\xc1\x78\xc7\xd0\xc6","\xba\x4e\x58\x54"};
_EXTERN CHAR *STR_POWER[LANG_SIZE] = {"\x50\x00\x6f\x00\x77\x00\x65\x00\x72\x00","\x04\xc8\x25\xb8","\x9f\x52\x87\x73"};
_EXTERN CHAR *STR_SHIP_NAME[LANG_SIZE] = {"\x53\x00\x68\x00\x69\x00\x70\x00\x20\x00\x4e\x00\x61\x00\x6d\x00\x65\x00","\x20\xc1\x85\xba","\x39\x82\x0d\x54"};
_EXTERN CHAR *STR_SHIP_STATUS[LANG_SIZE] = {"\x53\x00\x54\x00\x41\x00\x54\x00\x55\x00\x53\x00","\xc1\xc0\xdc\xd0","\xb6\x72\x01\x60"};
_EXTERN CHAR *STR_TYPE[LANG_SIZE] = {"\x54\x00\x59\x00\x50\x00\x45\x00","\x15\xd6\xdd\xc2","\xcd\x79\x7b\x7c"};
_EXTERN CHAR *STR_ZONE_SIZE[LANG_SIZE] = {"\x5a\x00\x6f\x00\x6e\x00\x65\x00\x20\x00\x53\x00\x69\x00\x7a\x00\x65\x00","\x01\xc6\xed\xc5\x20\x00\x6c\xd0\x30\xae","\x3a\x53\xdf\x57\x27\x59\x0f\x5c"};
_EXTERN CHAR *STR_MOVE[LANG_SIZE] = {"\x4d\x00\x4f\x00\x56\x00\x45\x00","\x4d\x00\x4f\x00\x56\x00\x45\x00","\x4d\x00\x4f\x00\x56\x00\x45\x00"};
_EXTERN CHAR *STR_ZOOM[LANG_SIZE] = {"\x5a\x00\x4f\x00\x4f\x00\x4d\x00","\x5a\x00\x4f\x00\x4f\x00\x4d\x00","\x5a\x00\x4f\x00\x4f\x00\x4d\x00"};


_EXTERN CHAR *STR_NAME[LANG_SIZE] = {"\x4e\x00\x41\x00\x4d\x00\x45\x00","\x74\xc7\x84\xb9","\x39\x82\x0d\x54"};
_EXTERN CHAR *STR_RANGE[LANG_SIZE] = {"\x52\x00\x4e\x00\x47\x00","\x70\xac\xac\xb9","\x03\x83\xf4\x56"};
_EXTERN CHAR *STR_BEARING[LANG_SIZE] = {"\x42\x00\x52\x00\x47\x00","\x29\xbc\x04\xc7","\xb9\x65\x4d\x4f"};
_EXTERN CHAR *STR_ETIM[LANG_SIZE] = {"\x45\x00\x54\x00\x49\x00\x4d\x00","\xdc\xc2\x04\xac","\xf6\x65\xf4\x95"};
_EXTERN CHAR *STR_COG[LANG_SIZE] = {"\x43\x00\x4f\x00\x47\x00","\x54\xcf\xa4\xc2","\xf9\x5b\x30\x57\x2a\x82\x11\x54"};
_EXTERN CHAR *STR_HEADING[LANG_SIZE] = {"\x48\x00\x44\x00\x47\x00","\x20\xc1\x18\xc2","\x39\x82\x4f\x82\x11\x54"};
_EXTERN CHAR *STR_SOG[LANG_SIZE] = {"\x53\x00\x4f\x00\x47\x00","\x8d\xc1\xc4\xb3","\xf9\x5b\x30\x57\x1f\x90\xa6\x5e"};
_EXTERN CHAR *STR_ROT[LANG_SIZE] = {"\x52\x00\x4f\x00\x54\x00","\x8c\xd6\x04\xc8\x60\xb9","\x6c\x8f\x11\x54\x1f\x90\x87\x73"};
_EXTERN CHAR *STR_POS_STATUS[LANG_SIZE] = {"\x50\x00\x6f\x00\x73\x00\x69\x00\x74\x00\x69\x00\x6f\x00\x6e\x00\x20\x00\x53\x00\x74\x00\x61\x00\x74\x00\x75\x00\x73\x00","\x04\xc7\x58\xce\x20\x00\xc1\xc0\xdc\xd0","\x50\x00\x6f\x00\x73\x00\x69\x00\x74\x00\x69\x00\x6f\x00\x6e\x00\x20\x00\x53\x00\x74\x00\x61\x00\x74\x00\x75\x00\x73\x00"};
_EXTERN CHAR *STR_LAT[LANG_SIZE] = {"\x4c\x00\x61\x00\x74\x00","\x04\xc7\xc4\xb3","\xac\x7e\xa6\x5e"};
_EXTERN CHAR *STR_LON[LANG_SIZE] = {"\x4c\x00\x6f\x00\x6e\x00","\xbd\xac\xc4\xb3","\xcf\x7e\xa6\x5e"};
_EXTERN CHAR *STR_LOCAL_TIME[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x20\x00\x4c\x00\x4f\x00\x43\x00\x41\x00\x4c\x00\x20\x00\x54\x00\x49\x00\x4d\x00\x45\x00","\xc0\xc9\xed\xc5\x20\x00\xdc\xc2\x04\xac\x20\x00\x24\xc1\x15\xc8","\x53\x5f\x30\x57\xf6\x65\xf4\x95"};
_EXTERN CHAR *STR_ETA[LANG_SIZE] = {"\x45\x00\x54\x00\x41\x00","\xc4\xb3\x29\xcc\xdc\xc2\x04\xac","\xb5\x62\xbe\x8f\xf6\x65\xf4\x95"};
_EXTERN CHAR *STR_CALLSIGN[LANG_SIZE] = {"\x43\x00\x2e\x00\x53\x00\x49\x00\x47\x00\x4e\x00","\x38\xd6\x9c\xcd\x80\xbd\x38\xd6","\x7c\x54\xf7\x53"};
_EXTERN CHAR *STR_DRAUGHT[LANG_SIZE] = {"\x44\x00\x52\x00\x41\x00\x55\x00\x47\x00\x48\x00\x54\x00","\x58\xd7\x18\xc2","\x03\x54\x34\x6c"};
_EXTERN CHAR *STR_DEST[LANG_SIZE] = {"\x44\x00\x45\x00\x53\x00\x54\x00\x49\x00\x4e\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00","\xa9\xba\x01\xc8\xc0\xc9","\xee\x76\x84\x76\x30\x57"};
_EXTERN CHAR *STR_SHIP_CARGO[LANG_SIZE] = {"\x53\x00\x2f\x00\x43\x00\x20\x00\x54\x00\x59\x00\x50\x00\x45\x00","\x20\xc1\x15\xbc\x85\xc8\x58\xb9","\x27\x8d\x69\x72\xcd\x79\x7b\x7c"};
_EXTERN CHAR *STR_NAV_STATUS[LANG_SIZE] = {"\x4e\x00\x41\x00\x56\x00\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x55\x00\x53\x00","\x6d\xd5\x74\xd5\x20\x00\xc1\xc0\xdc\xd0","\x2a\x82\x4c\x88\xb6\x72\x01\x60"};
_EXTERN CHAR *STR_EPFS_TYPE[LANG_SIZE] = {"\x45\x00\x50\x00\x46\x00\x53\x00","\x04\xc7\x58\xce\xa5\xc7\x58\xce","\x45\x00\x50\x00\x46\x00\x53\x00"};
_EXTERN CHAR *STR_INT_EPFS_TYPE[LANG_SIZE] = {"\x49\x00\x6e\x00\x74\x00\x2e\x00\x45\x00\x50\x00\x46\x00\x53\x00","\xb4\xb0\x80\xbd\x20\x00\x04\xc7\x58\xce\xa5\xc7\x58\xce","\x49\x00\x6e\x00\x74\x00\x2e\x00\x45\x00\x50\x00\x46\x00\x53\x00"};
_EXTERN CHAR *STR_EXT_EPFS_TYPE[LANG_SIZE] = {"\x45\x00\x78\x00\x74\x00\x2e\x00\x45\x00\x50\x00\x46\x00\x53\x00","\x78\xc6\x80\xbd\x20\x00\x04\xc7\x58\xce\xa5\xc7\x58\xce","\x45\x00\x78\x00\x74\x00\x2e\x00\x45\x00\x50\x00\x46\x00\x53\x00"};
_EXTERN CHAR *STR_HAS_DTE[LANG_SIZE] = {"\x48\x00\x61\x00\x73\x00\x20\x00\x44\x00\x54\x00\x45\x00","\x44\x00\x54\x00\x45\x00\x20\x00\xf0\xc5\xb0\xac","\x48\x00\x61\x00\x73\x00\x20\x00\x44\x00\x54\x00\x45\x00"};
_EXTERN CHAR *STR_ENTER_PASSWORD[LANG_SIZE] = {"\x45\x00\x6e\x00\x74\x00\x65\x00\x72\x00\x20\x00\x74\x00\x68\x00\x65\x00\x20\x00\x70\x00\x61\x00\x73\x00\x73\x00\x77\x00\x6f\x00\x72\x00\x64\x00\x2e\x00\x2e\x00\x2e\x00","\x44\xbe\x00\xbc\x88\xbc\x38\xd6\x7c\xb9\x20\x00\x85\xc7\x25\xb8\x58\xd5\xdc\xc2\x24\xc6\x2e\x00\x2e\x00\x2e\x00","\x93\x8f\x65\x51\xc6\x5b\x01\x78\x2e\x00\x2e\x00\x2e\x00"};
_EXTERN CHAR *STR_PASSWORD_OLD[LANG_SIZE] = {"\x4f\x00\x4c\x00\x44\x00\x20\x00\x50\x00\x41\x00\x53\x00\x53\x00\x57\x00\x4f\x00\x52\x00\x44\x00","\x30\xae\x74\xc8\x20\x00\x44\xbe\x00\xbc\x88\xbc\x38\xd6","\xe7\x65\xc6\x5b\x01\x78"};
_EXTERN CHAR *STR_PASSWORD_NEW[LANG_SIZE] = {"\x4e\x00\x45\x00\x57\x00\x20\x00\x50\x00\x41\x00\x53\x00\x53\x00\x57\x00\x4f\x00\x52\x00\x44\x00","\xc8\xc0\x20\x00\x44\xbe\x00\xbc\x88\xbc\x38\xd6","\xb0\x65\xc6\x5b\x01\x78"};
_EXTERN CHAR *STR_PASSWORD_CONFIRM[LANG_SIZE] = {"\x43\x00\x4f\x00\x4e\x00\x46\x00\x49\x00\x52\x00\x4d\x00\x20\x00\x4e\x00\x45\x00\x57\x00\x20\x00\x50\x00\x41\x00\x53\x00\x53\x00\x57\x00\x4f\x00\x52\x00\x44\x00","\xc8\xc0\x20\x00\x44\xbe\x00\xbc\x88\xbc\x38\xd6\x20\x00\x55\xd6\x78\xc7","\x6e\x78\xa4\x8b\xb0\x65\xc6\x5b\x01\x78"};
_EXTERN CHAR *STR_STATION1[LANG_SIZE] = {"\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x31\x00\x20\x00","\x20\x00\x20\xc1\x20\x00\x15\xbc\x20\x00\x31\x00\x20\x00","\x20\x00\xf0\x53\xd9\x7a\x31\x00\x20\x00"};
_EXTERN CHAR *STR_STATION2[LANG_SIZE] = {"\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x32\x00\x20\x00","\x20\x00\x20\xc1\x20\x00\x15\xbc\x20\x00\x32\x00\x20\x00","\x20\x00\xf0\x53\xd9\x7a\x32\x00\x20\x00"};
_EXTERN CHAR *STR_DSP_VER[LANG_SIZE] = {"\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x50\x00\x4f\x00\x4e\x00\x44\x00\x45\x00\x52\x00\x28\x00\x44\x00\x53\x00\x50\x00\x29\x00\x20\x00\x56\x00\x45\x00\x52\x00\x53\x00\x49\x00\x4f\x00\x4e\x00","\xb8\xd2\x9c\xb7\xa4\xc2\xf0\xd3\x54\xb3\x28\x00\x44\x00\x53\x00\x50\x00\x29\x00\x20\x00\x84\xbc\x04\xc8","\x36\x65\xd1\x53\x3a\x67\x48\x72\x2c\x67\x28\x00\x44\x00\x53\x00\x50\x00\x29\x00"};
_EXTERN CHAR *STR_FPGA_VER[LANG_SIZE] = {"\x54\x00\x52\x00\x41\x00\x4e\x00\x53\x00\x50\x00\x4f\x00\x4e\x00\x44\x00\x45\x00\x52\x00\x28\x00\x46\x00\x50\x00\x47\x00\x41\x00\x29\x00\x20\x00\x56\x00\x45\x00\x52\x00\x53\x00\x49\x00\x4f\x00\x4e\x00","\xb8\xd2\x9c\xb7\xa4\xc2\xf0\xd3\x54\xb3\x28\x00\x46\x00\x50\x00\x47\x00\x41\x00\x29\x00\x20\x00\x84\xbc\x04\xc8","\x36\x65\xd1\x53\x3a\x67\x48\x72\x2c\x67\x28\x00\x46\x00\x50\x00\x47\x00\x41\x00\x29\x00"};
_EXTERN CHAR *STR_MKD_VER[LANG_SIZE] = {"\x4d\x00\x4b\x00\x44\x00\x20\x00\x56\x00\x45\x00\x52\x00\x53\x00\x49\x00\x4f\x00\x4e\x00","\x4d\x00\x4b\x00\x44\x00\x20\x00\x84\xbc\x04\xc8","\x3e\x66\x3a\x79\x68\x56\x48\x72\x2c\x67"};
_EXTERN CHAR *STR_LANG_MODE[LANG_SIZE] = {"\x4c\x00\x41\x00\x4e\x00\x47\x00\x55\x00\x41\x00\x47\x00\x45\x00\x20\x00\x4d\x00\x4f\x00\x44\x00\x45\x00","\xb8\xc5\xb4\xc5\x20\xc1\xdd\xd0\x28\x00\x4c\x00\x41\x00\x4e\x00\x47\x00\x55\x00\x41\x00\x47\x00\x45\x00\x29\x00","\xed\x8b\x00\x8a\xcd\x79\x7b\x7c\x28\x00\x4c\x00\x41\x00\x4e\x00\x47\x00\x55\x00\x41\x00\x47\x00\x45\x00\x29\x00"};
_EXTERN CHAR *STR_ANT_POSITION[LANG_SIZE] = {"\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x41\x00\x6e\x00\x74\x00\x65\x00\x6e\x00\x6e\x00\x61\x00\x20\x00\x50\x00\x6f\x00\x73\x00\x69\x00\x74\x00\x69\x00\x6f\x00\x6e\x00","\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x48\xc5\x4c\xd1\x98\xb0\x20\x00\x04\xc7\x58\xce","\x47\x00\x4e\x00\x53\x00\x53\x00\x29\x59\xbf\x7e\x4d\x4f\x6e\x7f"};
_EXTERN CHAR *STR_EXTERNAL[LANG_SIZE] = {"\x45\x00\x78\x00\x74\x00\x65\x00\x72\x00\x6e\x00\x61\x00\x6c\x00","\x78\xc6\x80\xbd\x20\x00\x04\xc7\x58\xce","\x16\x59\xe8\x90"};
_EXTERN CHAR *STR_INTERNAL[LANG_SIZE] = {"\x49\x00\x6e\x00\x74\x00\x65\x00\x72\x00\x6e\x00\x61\x00\x6c\x00","\xb4\xb0\x80\xbd\x20\x00\x04\xc7\x58\xce","\x85\x51\xe8\x90"};
_EXTERN CHAR *STR_MSG_DEST[LANG_SIZE] = {"\x44\x00\x45\x00\x53\x00\x54\x00\x49\x00\x4e\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00","\x18\xc2\xe0\xc2\x90\xc7","\xee\x76\x84\x76\x30\x57"};
_EXTERN CHAR *STR_CHANNEL[LANG_SIZE] = {"\x43\x00\x48\x00\x41\x00\x4e\x00\x4e\x00\x45\x00\x4c\x00","\x44\xcc\x10\xb1","\x91\x98\x53\x90"};
_EXTERN CHAR *STR_MSG_TYPE[LANG_SIZE] = {"\x4d\x00\x53\x00\x47\x00\x20\x00\x54\x00\x59\x00\x50\x00\x45\x00","\x15\xd6\xdd\xc2","\xe1\x4f\x6f\x60\xcd\x79\x7b\x7c"};
_EXTERN CHAR *STR_MESSAGE[LANG_SIZE] = {"\x4d\x00\x65\x00\x73\x00\x73\x00\x61\x00\x67\x00\x65\x00","\x54\xba\xdc\xc2\xc0\xc9","\xe1\x4f\x6f\x60"};
_EXTERN CHAR *STR_FAVOR_MSG[LANG_SIZE] = {"\x4d\x00\x65\x00\x73\x00\x73\x00\x61\x00\x67\x00\x65\x00\x73\x00","\x54\xba\xdc\xc2\xc0\xc9","\xe1\x4f\x6f\x60"};
_EXTERN CHAR *STR_SENDER[LANG_SIZE] = {"\x53\x00\x45\x00\x4e\x00\x44\x00\x45\x00\x52\x00","\xf4\xbc\xb8\xb0\x74\xc7","\xd1\x53\x01\x90\x05\x80"};
_EXTERN CHAR *STR_RECIEVER[LANG_SIZE] = {"\x52\x00\x45\x00\x43\x00\x45\x00\x49\x00\x56\x00\x45\x00\x52\x00","\x1b\xbc\x94\xb2\x74\xc7","\xa5\x63\x36\x65\x05\x80"};
_EXTERN CHAR *STR_ARRIVED[LANG_SIZE] = {"\x41\x00\x52\x00\x52\x00\x49\x00\x56\x00\x45\x00\x44\x00","\x1b\xbc\x40\xc7\xa0\xb0\xdc\xc9","\xb5\x62\xbe\x8f"};
_EXTERN CHAR *STR_SENT[LANG_SIZE] = {"\x53\x00\x45\x00\x4e\x00\x54\x00","\xf4\xbc\xb8\xb0\xa0\xb0\xdc\xc9","\xf2\x5d\xd1\x53\x01\x90"};
_EXTERN CHAR *STR_LR_MODE[LANG_SIZE] = {"\x4d\x00\x4f\x00\x44\x00\x45\x00","\xa8\xba\xdc\xb4","\x21\x6a\x0f\x5f"};
_EXTERN CHAR *STR_DATE[LANG_SIZE] = {"\x44\x00\x41\x00\x54\x00\x45\x00","\xa0\xb0\xdc\xc9","\xe5\x65\x1f\x67"};
_EXTERN CHAR *STR_TIME[LANG_SIZE] = {"\x54\x00\x49\x00\x4d\x00\x45\x00","\xdc\xc2\x04\xac","\xf6\x65\xf4\x95"};
_EXTERN CHAR *STR_REQUESTER[LANG_SIZE] = {"\x52\x00\x45\x00\x51\x00\x55\x00\x45\x00\x53\x00\x54\x00\x45\x00\x52\x00","\x94\xc6\x20\x00\xad\xcc\x20\x00\x6d\xad","\xf7\x8b\x42\x6c\x05\x80"};
_EXTERN CHAR *STR_DESCRIPTION[LANG_SIZE] = {"\x44\x00\x45\x00\x53\x00\x43\x00\x52\x00\x49\x00\x50\x00\x54\x00\x49\x00\x4f\x00\x4e\x00","\x24\xc1\x20\x00\x85\xba","\xcf\x63\xf0\x8f"};
_EXTERN CHAR *STR_REPLY_VALUE[LANG_SIZE] = {"\x52\x00\x65\x00\x70\x00\x6c\x00\x79\x00\x20\x00\x56\x00\x61\x00\x6c\x00\x75\x00\x65\x00","\x51\xc7\xf5\xb2\x20\x00\x6d\xd5\xa9\xba","\x54\x7b\x0d\x59\x3c\x50"};
_EXTERN CHAR *STR_RETRY[LANG_SIZE] = {"\x52\x00\x65\x00\x74\x00\x72\x00\x79\x00","\xac\xc7\xdc\xc2\xc4\xb3","\xcd\x91\xd5\x8b"};
_EXTERN CHAR *STR_BRIGHTNESS[LANG_SIZE] = {"\x4c\x00\x43\x00\x44\x00\x20\x00\x42\x00\x52\x00\x49\x00\x47\x00\x48\x00\x54\x00\x4e\x00\x45\x00\x53\x00\x53\x00","\x4c\x00\x43\x00\x44\x00\x20\x00\x1d\xbc\x30\xae","\x3e\x66\x3a\x79\x4f\x5c\xae\x4e\xa6\x5e"};
_EXTERN CHAR *STR_LED_BRIGHT[LANG_SIZE] = {"\x4c\x00\x45\x00\x44\x00\x20\x00\x42\x00\x52\x00\x49\x00\x47\x00\x48\x00\x54\x00\x4e\x00\x45\x00\x53\x00\x53\x00","\x4c\x00\x45\x00\x44\x00\x20\x00\x1d\xbc\x30\xae","\x07\x63\x3a\x79\x6f\x70\xae\x4e\xa6\x5e"};
_EXTERN CHAR *STR_CONTRAST[LANG_SIZE] = {"\x4c\x00\x43\x00\x44\x00\x20\x00\x43\x00\x4f\x00\x4e\x00\x54\x00\x52\x00\x41\x00\x53\x00\x54\x00","\x4c\x00\x43\x00\x44\x00\x20\x00\x00\xb3\x44\xbe","\x3e\x66\x3a\x79\x4f\x5c\xf9\x5b\xd4\x6b\xa6\x5e\x54\x00"};
_EXTERN CHAR *STR_CONTRAST_OFFSET[LANG_SIZE] = {"\x4c\x00\x43\x00\x44\x00\x20\x00\x43\x00\x4f\x00\x4e\x00\x54\x00\x52\x00\x41\x00\x53\x00\x54\x00\x20\x00\x4f\x00\x46\x00\x46\x00\x53\x00\x45\x00\x54\x00","\x4c\x00\x43\x00\x44\x00\x20\x00\x00\xb3\x44\xbe\x20\x00\x70\xc8\x15\xc8","\x3e\x66\x3a\x79\x4f\x5c\xf9\x5b\xd4\x6b\xa6\x5e\xd6\x53\x88\x6d"};
_EXTERN CHAR *STR_LCD_REVERSE[LANG_SIZE] = {"\x4c\x00\x43\x00\x44\x00\x20\x00\x52\x00\x65\x00\x76\x00\x65\x00\x72\x00\x73\x00\x65\x00\x20\x00\x4d\x00\x6f\x00\x64\x00\x65\x00","\x4c\x00\x43\x00\x44\x00\x20\x00\x18\xbc\x04\xc8\x20\x00\xa8\xba\xdc\xb4","\x3e\x66\x3a\x79\x4f\x5c\xfb\x7f\x6c\x8f\x21\x6a\x0f\x5f"};
_EXTERN CHAR *STR_BUZZER_KEY[LANG_SIZE] = {"\x4b\x00\x65\x00\x79\x00\x20\x00\x42\x00\x75\x00\x7a\x00\x7a\x00\x65\x00\x72\x00","\xa4\xd0\x20\x00\x80\xbd\x00\xc8","\x09\x63\x2e\x95\xf0\x58\xf3\x97"};
_EXTERN CHAR *STR_BUZZER_ALARM[LANG_SIZE] = {"\x41\x00\x6c\x00\x61\x00\x72\x00\x6d\x00\x20\x00\x42\x00\x75\x00\x7a\x00\x7a\x00\x65\x00\x72\x00","\x4c\xc5\x8c\xb7\x20\x00\x80\xbd\x00\xc8","\xa5\x62\x66\x8b\x02\x87\x23\x9e"};
_EXTERN CHAR *STR_PRESS_ENT_KEY[LANG_SIZE] = {"\x50\x00\x72\x00\x65\x00\x73\x00\x73\x00\x20\x00\x45\x00\x4e\x00\x54\x00\x20\x00\x4b\x00\x65\x00\x79\x00\x2e\x00\x2e\x00\x2e\x00","\x45\x00\x4e\x00\x54\x00\x20\x00\xa4\xd0\x7c\xb9\x20\x00\x04\xb2\x74\xb9\xdc\xc2\x24\xc6\x2e\x00\x2e\x00\x2e\x00","\x09\x63\x6e\x78\xa4\x8b\x2e\x95\x2e\x00\x2e\x00\x2e\x00"};
_EXTERN CHAR *STR_ALARM[LANG_SIZE] = {"\x41\x00\x4c\x00\x41\x00\x52\x00\x4d\x00","\x4c\xc5\x8c\xb7","\xa5\x62\x66\x8b"};
_EXTERN CHAR *STR_DURATION[LANG_SIZE] = {"\x44\x00\x55\x00\x52\x00\x2e\x00","\x30\xae\x04\xac","\x01\x63\xed\x7e\xf6\x65\xf4\x95"};
_EXTERN CHAR *STR_RX_FREQ[LANG_SIZE] = {"\x52\x00\x58\x00\x20\x00\x46\x00\x52\x00\x45\x00\x51\x00","\x52\x00\x58\x00\x20\x00\xfc\xc8\x0c\xd3\x18\xc2","\xa5\x63\x36\x65\x91\x98\x87\x73"};
_EXTERN CHAR *STR_TX_FREQ[LANG_SIZE] = {"\x54\x00\x58\x00\x20\x00\x46\x00\x52\x00\x45\x00\x51\x00","\x54\x00\x58\x00\x20\x00\xfc\xc8\x0c\xd3\x18\xc2","\xd1\x53\x04\x5c\x91\x98\x87\x73"};
_EXTERN CHAR *STR_FREQ[LANG_SIZE] = {"\x46\x00\x52\x00\x45\x00\x51\x00\x55\x00\x45\x00\x4e\x00\x43\x00\x59\x00","\xfc\xc8\x0c\xd3\x18\xc2","\x91\x98\x87\x73"};
_EXTERN CHAR *STR_SIG_TYPE[LANG_SIZE] = {"\x53\x00\x49\x00\x47\x00\x2e\x00\x20\x00\x54\x00\x59\x00\x50\x00\x45\x00","\xe0\xc2\x38\xd6\x20\x00\x15\xd6\xdd\xc2","\xe1\x4f\xf7\x53\xcd\x79\x7b\x7c"};
_EXTERN CHAR *STR_TX_POWER[LANG_SIZE] = {"\x54\x00\x58\x00\x20\x00\x50\x00\x4f\x00\x57\x00\x45\x00\x52\x00","\x54\x00\x58\x00\x20\x00\x9c\xcd\x25\xb8","\xd1\x53\x04\x5c\x9f\x52\x87\x73"};
_EXTERN CHAR *STR_PATTERN[LANG_SIZE] = {"\x50\x00\x41\x00\x54\x00\x54\x00\x45\x00\x52\x00\x4e\x00","\x28\xd3\x34\xd1","\x21\x6a\x0f\x5f"};
_EXTERN CHAR *STR_POWER_SET[LANG_SIZE] = {"\x50\x00\x4f\x00\x57\x00\x45\x00\x52\x00\x20\x00\x53\x00\x45\x00\x54\x00","\x9c\xcd\x25\xb8\x24\xc1\x15\xc8","\x50\x00\x4f\x00\x57\x00\x45\x00\x52\x00\x20\x00\x53\x00\x45\x00\x54\x00"};
_EXTERN CHAR *STR_VSWR_REGULATION[LANG_SIZE] = {"\x56\x00\x53\x00\x57\x00\x52\x00\x20\x00\x52\x00\x45\x00\x47\x00\x55\x00\x4c\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00","\x56\x00\x53\x00\x57\x00\x52\x00\x20\x00\x70\xc8\x15\xc8","\x56\x00\x53\x00\x57\x00\x52\x00\x20\x00\x52\x00\x45\x00\x47\x00\x55\x00\x4c\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00"};
_EXTERN CHAR *STR_VARIATION[LANG_SIZE] = {"\x56\x00\x61\x00\x72\x00\x69\x00\x61\x00\x74\x00\x69\x00\x6f\x00\x6e\x00","\xb8\xd3\x28\xcc","\xd8\x53\x02\x5f"};
_EXTERN CHAR *STR_INIT_MODE[LANG_SIZE] = {"\x49\x00\x6e\x00\x69\x00\x74\x00\x20\x00\x4d\x00\x6f\x00\x64\x00\x65\x00","\x08\xcd\x30\xae\x54\xd6\x20\x00\xa8\xba\xdc\xb4","\x1d\x52\xcb\x59\x21\x6a\x0f\x5f"};
_EXTERN CHAR *STR_GPS_MODE[LANG_SIZE] = {"\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x4d\x00\x6f\x00\x64\x00\x65\x00","\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\xa8\xba\xdc\xb4","\x47\x00\x4e\x00\x53\x00\x53\x00\x21\x6a\x0f\x5f"};
_EXTERN CHAR *STR_INIT_POS[LANG_SIZE] = {"\x49\x00\x6e\x00\x69\x00\x74\x00\x20\x00\x50\x00\x6f\x00\x73\x00\x69\x00\x74\x00\x69\x00\x6f\x00\x6e\x00","\x08\xcd\x30\xae\x20\x00\x8c\xc8\x5c\xd4","\x1d\x52\xcb\x59\x39\x82\x4d\x4f"};


_EXTERN CHAR *STR_SELECT_FIELD[LANG_SIZE] = {"\x53\x00\x65\x00\x6c\x00\x65\x00\x63\x00\x74\x00\x20\x00\x66\x00\x69\x00\x65\x00\x6c\x00\x64\x00\x73\x00\x20\x00\x74\x00\x6f\x00\x20\x00\x69\x00\x6e\x00\x69\x00\x74\x00\x69\x00\x61\x00\x6c\x00\x69\x00\x7a\x00\x65\x00","\x08\xcd\x30\xae\x54\xd6\x20\x00\x60\xd5\x20\x00\x6d\xd5\xa9\xba\x44\xc7\x20\x00\x20\xc1\xdd\xd0\x58\xd5\xdc\xc2\x24\xc6","\x1d\x52\xcb\x59\x09\x90\xe9\x62\x46\x68"};
_EXTERN CHAR *STR_NO_FAVOR_MSG[LANG_SIZE] = {"\x4e\x00\x6f\x00\x20\x00\x46\x00\x61\x00\x76\x00\x6f\x00\x72\x00\x69\x00\x74\x00\x65\x00\x20\x00\x4d\x00\x65\x00\x73\x00\x73\x00\x61\x00\x67\x00\x65\x00","\xf1\xb4\x5d\xb8\x1c\xb4\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x00\xac\x20\x00\xc6\xc5\xb5\xc2\xc8\xb2\xe4\xb2\x2e\x00","\x7a\x7a\x7d\x76\x36\x65\xcf\x85"};
_EXTERN CHAR *STR_ALERT[LANG_SIZE] = {"\x41\x00\x4c\x00\x45\x00\x52\x00\x54\x00","\xbd\xac\xe0\xac","\x66\x8b\xa5\x62"};
_EXTERN CHAR *STR_ALARM_ALERT[LANG_SIZE] = {"\x41\x00\x4c\x00\x41\x00\x52\x00\x4d\x00","\xbd\xac\xe0\xac","\xa5\x62\x66\x8b"};
_EXTERN CHAR *STR_SUCCESS[LANG_SIZE] = {"\x53\x00\x55\x00\x43\x00\x43\x00\x45\x00\x53\x00\x53\x00","\x31\xc1\xf5\xac","\x10\x62\x9f\x52"};
_EXTERN CHAR *STR_CONFIRM[LANG_SIZE] = {"\x43\x00\x4f\x00\x4e\x00\x46\x00\x49\x00\x52\x00\x4d\x00","\x55\xd6\x78\xc7","\x6e\x78\xa4\x8b"};
_EXTERN CHAR *STR_FAIL[LANG_SIZE] = {"\x46\x00\x41\x00\x49\x00\x4c\x00","\xe4\xc2\x28\xd3","\x31\x59\x25\x8d"};
_EXTERN CHAR *STR_NEW_MSG[LANG_SIZE] = {"\x4e\x00\x45\x00\x57\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00","\xc8\xc0\x5c\xb8\xb4\xc6\x20\x00\x54\xba\xdc\xc2\xc0\xc9","\xb0\x65\xe1\x4f\x6f\x60"};
_EXTERN CHAR *STR_ALERT_FAVOR_SAVE[LANG_SIZE] = {"\x41\x00\x20\x00\x46\x00\x41\x00\x56\x00\x4f\x00\x52\x00\x49\x00\x54\x00\x45\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x49\x00\x53\x00\x20\x00\x53\x00\x41\x00\x56\x00\x45\x00\x44\x00\x0a\x00","\x90\xc9\xa8\xac\xf0\xc4\x94\xb2\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x00\xac\x0a\x00\x00\xc8\xa5\xc7\x18\xb4\xc8\xc5\xb5\xc2\xc8\xb2\xe4\xb2\x2e\x00\x0a\x00","\x58\x5b\xa8\x50\x36\x65\xcf\x85\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ALERT_FAVOR_FULL[LANG_SIZE] = {"\x46\x00\x41\x00\x56\x00\x4f\x00\x52\x00\x49\x00\x54\x00\x45\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x53\x00\x0a\x00\x46\x00\x55\x00\x4c\x00\x4c\x00\x0a\x00","\x00\xc8\xa5\xc7\x60\xd5\x20\x00\xf5\xac\x04\xac\x74\xc7\x0a\x00\x80\xbd\x71\xc8\x69\xd5\xc8\xb2\xe4\xb2\x2e\x00\x0a\x00","\x36\x65\xcf\x85\xe1\x4f\x6f\x60\xe1\x6e\x7d\x8f\x0a\x00"};
_EXTERN CHAR *STR_ALERT_DELETE_MSG[LANG_SIZE] = {"\x53\x00\x45\x00\x4c\x00\x45\x00\x43\x00\x54\x00\x45\x00\x44\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x44\x00\x45\x00\x4c\x00\x45\x00\x54\x00\x45\x00\x0a\x00","\x20\xc1\xdd\xd0\x1c\xb4\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x00\xac\x0a\x00\xad\xc0\x1c\xc8\x18\xb4\xc8\xc5\xb5\xc2\xc8\xb2\xe4\xb2\x2e\x00\x0a\x00","\x20\x52\x64\x96\x09\x90\xe9\x62\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ALERT_DEL_FAVOR[LANG_SIZE] = {"\x46\x00\x41\x00\x56\x00\x4f\x00\x52\x00\x49\x00\x54\x00\x45\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x44\x00\x45\x00\x4c\x00\x45\x00\x54\x00\x45\x00\x0a\x00","\x20\xc1\xdd\xd0\x1c\xb4\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x7c\xb9\x0a\x00\xad\xc0\x1c\xc8\x58\xd5\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x20\x52\x64\x96\x36\x65\xcf\x85\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ALERT_STA_CHANGE[LANG_SIZE] = {"\x53\x00\x48\x00\x49\x00\x50\x00\x20\x00\x53\x00\x54\x00\x41\x00\x54\x00\x49\x00\x43\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00","\x20\xc1\x15\xbc\x20\x00\x15\xc8\x01\xc8\x20\x00\x15\xc8\xf4\xbc\x7c\xb9\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x39\x65\xd8\x53\x39\x82\x36\x82\x59\x97\x01\x60\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ALERT_VOY_CHANGE[LANG_SIZE] = {"\x56\x00\x4f\x00\x59\x00\x41\x00\x47\x00\x45\x00\x20\x00\x52\x00\x45\x00\x4c\x00\x41\x00\x54\x00\x45\x00\x44\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00","\x20\xc1\x15\xbc\x20\x00\x6d\xd5\x74\xd5\x20\x00\x15\xc8\xf4\xbc\x7c\xb9\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x39\x65\xd8\x53\x2a\x82\x21\x6b\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ALERT_DEL_REGAREA[LANG_SIZE] = {"\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x0a\x00\x44\x00\x45\x00\x4c\x00\x45\x00\x54\x00\x45\x00\x0a\x00","\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\x70\xb3\x74\xc7\xc0\xd0\x7c\xb9\x0a\x00\xad\xc0\x1c\xc8\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x20\x52\x64\x96\x3a\x53\xdf\x57\x70\x65\x6e\x63\x0a\x00"};
_EXTERN CHAR *STR_ALERT_UP_REGAREA[LANG_SIZE] = {"\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x0a\x00\x55\x00\x50\x00\x44\x00\x41\x00\x54\x00\x45\x00\x0a\x00","\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\x70\xb3\x74\xc7\x30\xd1\x7c\xb9\x0a\x00\x18\xc2\x15\xc8\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\xf4\x66\xb0\x65\x3a\x53\xdf\x57\x70\x65\x6e\x63\x0a\x00"};
_EXTERN CHAR *STR_ALERT_DEFT_REGAREA[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x20\x00\x44\x00\x45\x00\x46\x00\x41\x00\x55\x00\x4c\x00\x54\x00\x0a\x00\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x3f\x00\x0a\x00","\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\x70\xb3\x74\xc7\x30\xd1\x7c\xb9\x0a\x00\x08\xcd\x30\xae\x54\xd6\x20\x00\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x53\x00\x45\x00\x54\x00\x20\x00\x44\x00\x45\x00\x46\x00\x41\x00\x55\x00\x4c\x00\x54\x00\x0a\x00\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x3f\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CR_REGAREA[LANG_SIZE] = {"\x52\x00\x45\x00\x47\x00\x49\x00\x4f\x00\x4e\x00\x41\x00\x4c\x00\x20\x00\x41\x00\x52\x00\x45\x00\x41\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x0a\x00\x43\x00\x52\x00\x45\x00\x41\x00\x54\x00\x45\x00\x0a\x00","\xc0\xc9\xed\xc5\x20\x00\x01\xc6\xed\xc5\x20\x00\x70\xb3\x74\xc7\x30\xd1\x7c\xb9\x0a\x00\xdd\xc0\x31\xc1\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x1b\x52\xfa\x5e\x3a\x53\xdf\x57\x70\x65\x6e\x63\x0a\x00"};
_EXTERN CHAR *STR_ALERT_INV_CHA[LANG_SIZE] = {"\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x4e\x00\x45\x00\x4c\x00\x0a\x00\x41\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x21\x00\x0a\x00","\x20\xc7\xa8\xd6\x58\xd5\xc0\xc9\x20\x00\x4a\xc5\x94\xb2\x0a\x00\x20\x00\x41\x00\x44\xcc\x10\xb1\x21\x00\x0a\x00","\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x4e\x00\x45\x00\x4c\x00\x0a\x00\x41\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x21\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_INV_CHB[LANG_SIZE] = {"\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x4e\x00\x45\x00\x4c\x00\x0a\x00\x42\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x21\x00\x0a\x00","\x20\xc7\xa8\xd6\x58\xd5\xc0\xc9\x20\x00\x4a\xc5\x94\xb2\x0a\x00\x20\x00\x42\x00\x44\xcc\x10\xb1\x21\x00\x0a\x00","\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x4e\x00\x45\x00\x4c\x00\x0a\x00\x42\x00\x20\x00\x44\x00\x41\x00\x54\x00\x41\x00\x21\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_INV_NE_LAT[LANG_SIZE] = {"\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00","\x8c\xc8\x5c\xd4\x12\xac\x44\xc7\x20\x00\x55\xd6\x78\xc7\x58\xd5\x38\xc1\x94\xc6\x21\x00\x0a\x00","\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_INV_NE_LON[LANG_SIZE] = {"\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00","\x8c\xc8\x5c\xd4\x12\xac\x44\xc7\x20\x00\x55\xd6\x78\xc7\x58\xd5\x38\xc1\x94\xc6\x21\x00\x0a\x00","\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_INV_SW_LAT[LANG_SIZE] = {"\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00","\x8c\xc8\x5c\xd4\x12\xac\x44\xc7\x20\x00\x55\xd6\x78\xc7\x58\xd5\x38\xc1\x94\xc6\x21\x00\x0a\x00","\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_INV_SW_LON[LANG_SIZE] = {"\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00","\x8c\xc8\x5c\xd4\x12\xac\x44\xc7\x20\x00\x55\xd6\x78\xc7\x58\xd5\x38\xc1\x94\xc6\x21\x00\x0a\x00","\x49\x00\x4e\x00\x56\x00\x41\x00\x4c\x00\x49\x00\x44\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x21\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CHANGE_APOS[LANG_SIZE] = {"\x41\x00\x4e\x00\x54\x00\x45\x00\x4e\x00\x4e\x00\x41\x00\x20\x00\x50\x00\x4f\x00\x53\x00\x49\x00\x54\x00\x4f\x00\x49\x00\x4e\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00","\x47\x00\x4e\x00\x53\x00\x53\x00\x20\x00\x48\xc5\x4c\xd1\x98\xb0\x20\x00\x04\xc7\x58\xce\x7c\xb9\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x39\x65\xd8\x53\x29\x59\xbf\x7e\x4d\x4f\x6e\x7f\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CHANGE_PORT[LANG_SIZE] = {"\x50\x00\x4f\x00\x52\x00\x54\x00\x20\x00\x53\x00\x45\x00\x54\x00\x54\x00\x49\x00\x4e\x00\x47\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00","\xec\xd3\xb8\xd2\x20\x00\x24\xc1\x15\xc8\x44\xc7\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x39\x65\xd8\x53\x2f\x6e\xe3\x53\xbe\x8b\x6e\x7f\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CHANGE_LCD[LANG_SIZE] = {"\x4c\x00\x43\x00\x44\x00\x20\x00\x53\x00\x45\x00\x54\x00\x54\x00\x49\x00\x4e\x00\x47\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00","\x4c\x00\x43\x00\x44\x00\x20\x00\x24\xc1\x15\xc8\x44\xc7\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x39\x65\xd8\x53\x3e\x66\x3a\x79\x4f\x5c\xbe\x8b\x6e\x7f\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CHANGE_BUZ[LANG_SIZE] = {"\x42\x00\x55\x00\x5a\x00\x5a\x00\x45\x00\x52\x00\x20\x00\x53\x00\x45\x00\x54\x00\x54\x00\x49\x00\x4e\x00\x47\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00","\x80\xbd\x00\xc8\x20\x00\x24\xc1\x15\xc8\x44\xc7\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x39\x65\xd8\x53\x02\x87\x23\x9e\xbe\x8b\x6e\x7f\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CHANGE_PW[LANG_SIZE] = {"\x50\x00\x41\x00\x53\x00\x53\x00\x57\x00\x4f\x00\x52\x00\x44\x00\x20\x00\x53\x00\x45\x00\x54\x00\x54\x00\x49\x00\x4e\x00\x47\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00","\x44\xbe\x00\xbc\x88\xbc\x38\xd6\x7c\xb9\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x39\x65\xd8\x53\xc6\x5b\x01\x78\xbe\x8b\x6e\x7f\x0a\x00"};
_EXTERN CHAR *STR_ALERT_SAVE_VALUE[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x54\x00\x49\x00\x4e\x00\x47\x00\x20\x00\x56\x00\x41\x00\x4c\x00\x55\x00\x45\x00\x0a\x00\x53\x00\x41\x00\x56\x00\x45\x00\x0a\x00","\x30\xae\xc0\xd0\x20\x00\x24\xc1\x15\xc8\x20\x00\x12\xac\xe4\xb4\x44\xc7\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x58\x5b\xa8\x50\xbe\x8b\x6e\x7f\x3c\x50\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CONFIRM_INIT[LANG_SIZE] = {"\x41\x00\x52\x00\x45\x00\x20\x00\x59\x00\x4f\x00\x55\x00\x20\x00\x53\x00\x55\x00\x52\x00\x45\x00\x0a\x00\x49\x00\x4e\x00\x49\x00\x54\x00\x49\x00\x41\x00\x4c\x00\x49\x00\x5a\x00\x45\x00\x20\x00\x53\x00\x59\x00\x53\x00\x54\x00\x45\x00\x4d\x00\x0a\x00","\xdc\xc2\xa4\xc2\x5c\xd1\x44\xc7\x20\x00\x15\xc8\xd0\xb9\x5c\xb8\x0a\x00\x08\xcd\x30\xae\x54\xd6\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x2f\x66\x26\x54\x1d\x52\xcb\x59\xbe\x8b\x6e\x7f\x3f\x00\x0a\x00"};
_EXTERN CHAR *STR_ALERT_CONFIRM_PAR[LANG_SIZE] = {"\x41\x00\x52\x00\x45\x00\x20\x00\x59\x00\x4f\x00\x55\x00\x20\x00\x53\x00\x55\x00\x52\x00\x45\x00\x3f\x00\x0a\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x20\x00\x50\x00\x41\x00\x52\x00\x41\x00\x4d\x00\x45\x00\x54\x00\x45\x00\x52\x00\x0a\x00","\x24\xc1\x15\xc8\x12\xac\x44\xc7\x20\x00\x15\xc8\xd0\xb9\x5c\xb8\x0a\x00\xc0\xbc\xbd\xac\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x2f\x66\x26\x54\x39\x65\xd8\x53\xc2\x53\x70\x65\x3f\x00\x0a\x00"};
_EXTERN CHAR *STR_ADDR_BIN_SUCCESS[LANG_SIZE] = {"\x41\x00\x44\x00\x44\x00\x52\x00\x45\x00\x53\x00\x53\x00\x45\x00\x44\x00\x0a\x00\x42\x00\x49\x00\x4e\x00\x41\x00\x52\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x53\x00\x55\x00\x43\x00\x43\x00\x45\x00\x53\x00\x53\x00\x46\x00\x55\x00\x4c\x00\x0a\x00","\xfc\xc8\x8c\xc1\x20\x00\xc0\xc9\x15\xc8\x0a\x00\x74\xc7\xc4\xc9\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\x31\xc1\xf5\xac\x0a\x00","\x10\x62\x9f\x52\xd1\x53\x03\x5e\xe5\x65\x38\x5e\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ADDR_SAFE_SUCCESS[LANG_SIZE] = {"\x41\x00\x44\x00\x44\x00\x52\x00\x45\x00\x53\x00\x53\x00\x45\x00\x44\x00\x0a\x00\x53\x00\x41\x00\x46\x00\x45\x00\x54\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x53\x00\x55\x00\x43\x00\x43\x00\x45\x00\x53\x00\x53\x00\x46\x00\x55\x00\x4c\x00\x0a\x00","\xfc\xc8\x8c\xc1\x20\x00\xc0\xc9\x15\xc8\x0a\x00\x48\xc5\x04\xc8\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\x31\xc1\xf5\xac\x0a\x00","\x10\x62\x9f\x52\xd1\x53\x03\x5e\x89\x5b\x68\x51\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ADDR_BIN_NOT[LANG_SIZE] = {"\x41\x00\x44\x00\x44\x00\x52\x00\x45\x00\x53\x00\x53\x00\x45\x00\x44\x00\x0a\x00\x42\x00\x49\x00\x4e\x00\x41\x00\x52\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x43\x00\x4f\x00\x55\x00\x4c\x00\x44\x00\x20\x00\x4e\x00\x4f\x00\x54\x00\x20\x00\x42\x00\x45\x00\x20\x00\x53\x00\x45\x00\x4e\x00\x54\x00\x0a\x00","\xfc\xc8\x8c\xc1\x20\x00\xc0\xc9\x15\xc8\x0a\x00\x74\xc7\xc4\xc9\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\xe4\xc2\x28\xd3\x0a\x00","\xe0\x65\xd5\x6c\xd1\x53\x03\x5e\xe5\x65\x38\x5e\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_ADDR_SAFE_NOT[LANG_SIZE] = {"\x41\x00\x44\x00\x44\x00\x52\x00\x45\x00\x53\x00\x53\x00\x45\x00\x44\x00\x0a\x00\x53\x00\x41\x00\x46\x00\x45\x00\x54\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x43\x00\x4f\x00\x55\x00\x4c\x00\x44\x00\x20\x00\x4e\x00\x4f\x00\x54\x00\x20\x00\x42\x00\x45\x00\x20\x00\x53\x00\x45\x00\x4e\x00\x54\x00\x0a\x00","\xfc\xc8\x8c\xc1\x20\x00\xc0\xc9\x15\xc8\x0a\x00\x48\xc5\x04\xc8\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\xe4\xc2\x28\xd3\x0a\x00","\xe0\x65\xd5\x6c\xd1\x53\x03\x5e\x89\x5b\x68\x51\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_BROAD_BIN_SUCCESS[LANG_SIZE] = {"\x42\x00\x52\x00\x4f\x00\x41\x00\x44\x00\x43\x00\x41\x00\x53\x00\x54\x00\x0a\x00\x42\x00\x49\x00\x4e\x00\x41\x00\x52\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x53\x00\x55\x00\x43\x00\x43\x00\x45\x00\x53\x00\x53\x00\x46\x00\x55\x00\x4c\x00\x0a\x00","\x29\xbc\xa1\xc1\x0a\x00\x74\xc7\xc4\xc9\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\x31\xc1\xf5\xac\x0a\x00","\x10\x62\x9f\x52\x7f\x5e\xad\x64\xe5\x65\x38\x5e\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_BROAD_SAFE_SUCCESS[LANG_SIZE] = {"\x42\x00\x52\x00\x4f\x00\x41\x00\x44\x00\x43\x00\x41\x00\x53\x00\x54\x00\x0a\x00\x53\x00\x41\x00\x46\x00\x45\x00\x54\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x53\x00\x55\x00\x43\x00\x43\x00\x45\x00\x53\x00\x53\x00\x46\x00\x55\x00\x4c\x00\x0a\x00","\x29\xbc\xa1\xc1\x0a\x00\x48\xc5\x04\xc8\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\x31\xc1\xf5\xac\x0a\x00","\x10\x62\x9f\x52\x7f\x5e\xad\x64\x89\x5b\x68\x51\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_BROAD_BIN_NOT[LANG_SIZE] = {"\x42\x00\x52\x00\x4f\x00\x41\x00\x44\x00\x43\x00\x41\x00\x53\x00\x54\x00\x0a\x00\x42\x00\x49\x00\x4e\x00\x41\x00\x52\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x43\x00\x4f\x00\x55\x00\x4c\x00\x44\x00\x20\x00\x4e\x00\x4f\x00\x54\x00\x20\x00\x42\x00\x45\x00\x20\x00\x53\x00\x45\x00\x4e\x00\x54\x00\x0a\x00","\x29\xbc\xa1\xc1\x0a\x00\x74\xc7\xc4\xc9\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\xe4\xc2\x28\xd3\x0a\x00","\xe0\x65\xd5\x6c\x7f\x5e\xad\x64\xe5\x65\x38\x5e\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_BROAD_SAFE_NOT[LANG_SIZE] = {"\x42\x00\x52\x00\x4f\x00\x41\x00\x44\x00\x43\x00\x41\x00\x53\x00\x54\x00\x0a\x00\x53\x00\x41\x00\x46\x00\x45\x00\x54\x00\x59\x00\x20\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x43\x00\x4f\x00\x55\x00\x4c\x00\x44\x00\x20\x00\x4e\x00\x4f\x00\x54\x00\x20\x00\x42\x00\x45\x00\x20\x00\x53\x00\x45\x00\x4e\x00\x54\x00\x0a\x00","\x29\xbc\xa1\xc1\x0a\x00\x48\xc5\x04\xc8\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x0a\x00\x04\xc8\xa1\xc1\x20\x00\xe4\xc2\x28\xd3\x0a\x00","\xe0\x65\xd5\x6c\x7f\x5e\xad\x64\x89\x5b\x68\x51\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_INTRRO_SUCCUESS[LANG_SIZE] = {"\x49\x00\x4e\x00\x54\x00\x45\x00\x52\x00\x52\x00\x4f\x00\x47\x00\x41\x00\x54\x00\x49\x00\x4f\x00\x4e\x00\x0a\x00\x53\x00\x55\x00\x43\x00\x43\x00\x45\x00\x53\x00\x53\x00\x46\x00\x55\x00\x4c\x00\x0a\x00","\x15\xc8\xf4\xbc\x20\x00\x94\xc6\xad\xcc\x0a\x00\x04\xc8\xa1\xc1\x20\x00\x31\xc1\xf5\xac\x0a\x00","\x10\x62\x9f\x52\xe2\x8b\xee\x95\x0a\x00"};
_EXTERN CHAR *STR_SYSTEM_RESTORE[LANG_SIZE] = {"\x44\x00\x4f\x00\x20\x00\x59\x00\x4f\x00\x55\x00\x20\x00\x57\x00\x41\x00\x4e\x00\x54\x00\x20\x00\x54\x00\x4f\x00\x0a\x00\x52\x00\x45\x00\x53\x00\x4f\x00\x54\x00\x52\x00\x45\x00\x20\x00\x53\x00\x59\x00\x53\x00\x54\x00\x45\x00\x4d\x00\x0a\x00\x52\x00\x45\x00\x41\x00\x4c\x00\x4c\x00\x59\x00\x3f\x00\x0a\x00","\x15\xc8\xd0\xb9\x5c\xb8\x20\x00\xdc\xc2\xa4\xc2\x5c\xd1\x20\x00\xf5\xbc\xd0\xc6\x44\xc7\x0a\x00\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x2f\x66\x26\x54\x6e\x78\xa4\x8b\x62\x60\x0d\x59\xfb\x7c\xdf\x7e\x3f\x00\x0a\x00"};
_EXTERN CHAR *STR_LR_MSG_CHANGE[LANG_SIZE] = {"\x53\x00\x45\x00\x54\x00\x54\x00\x49\x00\x4e\x00\x47\x00\x20\x00\x56\x00\x41\x00\x4c\x00\x55\x00\x45\x00\x0a\x00\x53\x00\x41\x00\x56\x00\x45\x00\x0a\x00","\x24\xc1\x15\xc8\x20\x00\x12\xac\xe4\xb4\x44\xc7\x0a\x00\x00\xc8\xa5\xc7\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x58\x5b\xa8\x50\xbe\x8b\x6e\x7f\x3c\x50\x0a\x00"};
_EXTERN CHAR *STR_LR_MSG_RECV[LANG_SIZE] = {"\x4c\x00\x4f\x00\x4e\x00\x47\x00\x20\x00\x52\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00\x4d\x00\x45\x00\x53\x00\x53\x00\x41\x00\x47\x00\x45\x00\x0a\x00\x52\x00\x45\x00\x43\x00\x45\x00\x49\x00\x56\x00\x45\x00\x44\x00\x0a\x00","\xd0\xc6\x70\xac\xac\xb9\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x7c\xb9\x0a\x00\x18\xc2\xe0\xc2\x58\xd5\x00\xc6\xb5\xc2\xc8\xb2\xe4\xb2\x2e\x00\x0a\x00","\x36\x65\x30\x52\xdc\x8f\x0b\x7a\xe1\x4f\x6f\x60\x0a\x00"};
_EXTERN CHAR *STR_Q_LR_MSG_ACK[LANG_SIZE] = {"\x44\x00\x4f\x00\x20\x00\x59\x00\x4f\x00\x55\x00\x20\x00\x41\x00\x43\x00\x4b\x00\x0a\x00\x4c\x00\x52\x00\x20\x00\x52\x00\x45\x00\x51\x00\x55\x00\x45\x00\x53\x00\x54\x00\x0a\x00","\xd0\xc6\x70\xac\xac\xb9\x20\x00\x54\xba\xdc\xc2\xc0\xc9\x20\x00\x94\xc6\xad\xcc\xd0\xc5\x0a\x00\x51\xc7\xf5\xb2\x58\xd5\xdc\xc2\xa0\xac\xb5\xc2\xc8\xb2\x4c\xae\x3f\x00\x0a\x00","\x2f\x66\x26\x54\x94\x5e\x54\x7b\xdc\x8f\x0b\x7a\xf7\x8b\x42\x6c\x3f\x00\x0a\x00"};
_EXTERN CHAR *STR_ERR_CHANGE_TANKER[LANG_SIZE] = {"\x43\x00\x41\x00\x4e\x00\x27\x00\x54\x00\x20\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00\x54\x00\x4f\x00\x20\x00\x54\x00\x41\x00\x4e\x00\x4b\x00\x45\x00\x52\x00\x20\x00\x4d\x00\x4f\x00\x44\x00\x45\x00\x2e\x00\x0a\x00","\x54\x00\x41\x00\x4e\x00\x4b\x00\x45\x00\x52\x00\x20\x00\xa8\xba\xdc\xb4\x5c\xb8\x0a\x00\xc0\xbc\xbd\xac\x60\xd5\x20\x00\x18\xc2\x20\x00\xc6\xc5\xb5\xc2\xc8\xb2\xe4\xb2\x2e\x00\x0a\x00","\x43\x00\x41\x00\x4e\x00\x27\x00\x54\x00\x20\x00\x43\x00\x48\x00\x41\x00\x4e\x00\x47\x00\x45\x00\x0a\x00\x54\x00\x4f\x00\x20\x00\x54\x00\x41\x00\x4e\x00\x4b\x00\x45\x00\x52\x00\x20\x00\x4d\x00\x4f\x00\x44\x00\x45\x00\x2e\x00\x0a\x00"};




































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































