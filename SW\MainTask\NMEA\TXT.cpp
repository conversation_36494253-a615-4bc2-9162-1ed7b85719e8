#include "TXT.hpp"

CTxt::CTxt() : CSentence()
{
}
    
CTxt::CTxt(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CTxt::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_TXT;
}

/******************************************************************************
 * 
 * TXT - Text Transmission
 *
 * $--TXT,xx,xx,xx,c--c*hh<CR><LF>
 *        |  |  |   |
 *        1  2  3   4
 *
 * 1. Total number of sentences, 01 to 99
 * 2. Sentence number, 01 to 99
 * 3. Text identifier
 * 4. Text message(maximum : 61 characters)
 *
 ******************************************************************************/
void CTxt::Parse()
{
	m_nTotalNo = GetFieldInteger(1);
	m_nSentNo  = GetFieldInteger(2);
	m_nTextID  = GetFieldInteger(3);
	GetFieldString(4, (char *)m_szTextMsg, sizeof(m_szTextMsg));
}

void CTxt::GetPlainText(char *pszPlainText)
{
	pszPlainText[0] = '\0';
}
