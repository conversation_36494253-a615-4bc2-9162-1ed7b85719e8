/*************************************************************************/
/* File:		 File1MB.h                                               */
/* Author:       <PERSON>                                         */
/* Date:         26/Mar/02			                                     */
/* Description:                                                          */
/*************************************************************************/

#ifndef __FAT_IMAGE__
#define __FAT_IMAGE__

/* #include <FS_Types.h> */
#include <cmaptype.h>
#include <filesysp.h>

#ifdef _cplusplus
extern "C"
{
#endif

PRE_EXPORT_H struct DeviceNew *FAT_GetDevicePtr ( SWord Index );

#ifdef _cplusplus
}
#endif

#endif /* __FAT_IMAGE__ */
