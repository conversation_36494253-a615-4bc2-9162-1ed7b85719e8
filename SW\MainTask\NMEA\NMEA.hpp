#ifndef __NMEA_HPP__
#define __NMEA_HPP__

#include "Sentence.hpp"

#include "ABK.hpp"
#include "ABM.hpp"
#include "ACA.hpp"
#include "ACK.hpp"
#include "ACS.hpp"
#include "AIR.hpp"
#include "ALR.hpp"
#include "BBM.hpp"
#include "LR1.hpp"
#include "LR2.hpp"
#include "LR3.hpp"
#include "LRF.hpp"
#include "LRI.hpp"
#include "SSD.hpp"
#include "SYC.hpp"
#include "TXT.hpp"
#include "VDM.hpp"
#include "VDO.hpp"
#include "VSD.hpp"
#include "SPW.hpp"
#include "TEST.hpp"
#include "GSA.h"
#include "GSV.h"
#include "ALF.hpp"

class CNmea {
private:
	CSentence *m_pCurParser;
	CSentence *abk;
	CSentence *abm;
	CSentence *aca;
	CSentence *ack;
	CSentence *acs;
	CSentence *air;
	CSentence *alr;
	CSentence *alf;		// IEC62923-2(BAM) applied
	CSentence *bbm;
	CSentence *gsa;
	CSentence *gsv;
	CSentence *lr1;
	CSentence *lr2;
	CSentence *lr3;
	CSentence *lrf;
	CSentence *lri;
	CSentence *ssd;
	CSentence *syc;
	CSentence *txt;
	CSentence *vdo;
	CSentence *vdm;
	CSentence *vsd;
	CSentence *spw;
	CSentence *test;
	
public:
	CNmea();
	~CNmea();

	void       Parse();
	int        SetSentence(char *pszSentence);
	CSentence *GetCurParser() { return m_pCurParser; }
	int        GetFormat() { return (m_pCurParser) ? m_pCurParser->GetFormat() : CSentence::NMEA_UNKNOWN; }
	void       GetPlainText(char *pszPlainText);
	BOOL       IsValidChecksum() { return (m_pCurParser) ? m_pCurParser->IsValidChecksum() : 0; }

	inline int FORMAT_COMP(const char *pszText, const char *pszComp) {
		int nOffset = (pszText[1] == 'P') ? 1 : 0;
		return strncmp(pszText+(3-nOffset), pszComp, 3);
	}
};

#endif

