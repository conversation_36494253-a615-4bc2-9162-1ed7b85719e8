#include <stdio.h>
#include "LR2.hpp"

CLr2::CLr2() : CSentence()
{
}
    
CLr2::CLr2(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CLr2::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_LR2;
}

/******************************************************************************
*
* LR2 - Long-range Reply for function requests "B, C, E, and F"
*
* $--LR2,x,xxxxxxxxx,xxxxxxxx,hhmmss.ss,llll.ll,a,yyyyy.yy,a,x.x,T,x.x,N*hh<CR><LF>
*        | |         |        |         |       | |        | |   | |   |
*        1 2         3        4         5       6 7        8 9  10 11  12
*
* 1.     Sequence Number , 0 to 9
* 2.     MMSI of responder
* 3.     Date: ddmmyyyy, 8 digits
* 4.     UTC time of position
* 5.     Latitude, N/S(position co-ordinate, to 1 min.)
* 6.     'N' or 'S'
* 7.     Longitude, E/W (position co-ordinate, to 1 min.)
* 8.     'E' or 'W'
* 9.10.  Course over ground True, value to nearest degree
* 11.12. Speed over ground value to 0.1 knot
*
******************************************************************************/
void CLr2::Parse()
{
	char szTemp[5];
	m_nSeqNum      = GetFieldInteger(1);
	m_nMMSIResp    = GetFieldMMSI(2);

	GetFieldString(3, m_szDate);
	
	strncpy(szTemp, m_szDate, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nDay);

	strncpy(szTemp, m_szDate+2, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nMonth);
	
	strncpy(szTemp, m_szDate+4, 4);
	szTemp[4] = '\0';
	sscanf(szTemp, "%d", &m_nYear);
	
	GetFieldString(4,m_szUTC);

	strncpy(szTemp, m_szUTC, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nUTCHour);

	strncpy(szTemp, m_szUTC+2, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nUTCMin);

	strncpy(szTemp, m_szUTC+4, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nUTCSec);

	m_dblLat       = GetFieldLat(5);
	m_dblLon       = GetFieldLat(7);
	m_dblCourseDeg = GetFieldDouble(9);
	m_dblSpeedKnot = GetFieldDouble(11);
}

void CLr2::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}
