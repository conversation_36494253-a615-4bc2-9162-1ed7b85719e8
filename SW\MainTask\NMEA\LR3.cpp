#include <stdio.h>
#include "LR3.hpp"

CLr3::CLr3() : CSentence()
{
}
    
CLr3::CLr3(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CLr3::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_LR3;
}

/******************************************************************************
*
* LR3 - Long-range Reply for function requests "I,O,P,U and W"
*
* $--LR3,x,xxxxxxxxx,c--c,xxxxxx,hhmmss.ss,x.x,cc,x.x,x.x,x.x,x.x*hh<CR><LF>
*        | |         |    |      |         |   |  |   |   |   |
*        1 2         3    4      5         6   7  8   9   10  11
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of responder
* 3.  Voyage destination , 1 to 20 characters
* 4.  ETA Date:ddmmyy
* 5.  ETA Time , value to nearest second
* 6.  Draught , value to 0,1 meter
* 7.  Ship/cargo
* 8.  Ship length , value to nearest meter
* 9.  Ship breadth , value to nearest meter
* 10. Ship type
* 11. Persons , 0 to 8191
*
******************************************************************************/
void CLr3::Parse()
{
	char szTemp[3];
	m_nSeqNumber     = GetFieldInteger(1);
	m_nMMSIResp      = GetFieldMMSI(2);

	GetFieldString(3, m_szVoyageDest);
	GetFieldString(4, m_szETADate);
	
	strncpy(szTemp, m_szETADate, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nETADay);

	strncpy(szTemp, m_szETADate+2, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nETAMonth);
	
	strncpy(szTemp, m_szETADate+4, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nETAYear);
	
	GetFieldString(5, m_szETATime);
	
	strncpy(szTemp, m_szETATime, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nETAHour);

	strncpy(szTemp, m_szETATime+2, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nETAMin);

	strncpy(szTemp, m_szETATime+4, 2);
	szTemp[2] = '\0';
	sscanf(szTemp, "%d", &m_nETASec);

	m_dblDraught     = GetFieldDouble(6);
	GetFieldString(7, m_szShipCargo);
	m_dblShipLen     = GetFieldDouble(8);
	m_dblShipBreadth = GetFieldDouble(9);;
	m_nShipType      = GetFieldInteger(10);
	m_nPersons       = GetFieldInteger(11);
}

void CLr3::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}
