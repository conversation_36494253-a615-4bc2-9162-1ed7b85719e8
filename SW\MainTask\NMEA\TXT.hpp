#include "Sentence.hpp"

#ifndef __TXT_HPP__
#define __TXT_HPP__

#define MAX_TXT_LENGTH	61

/******************************************************************************
 * 
 * TXT - Text Transmission
 *
 * $--TXT,xx,xx,xx,c--c*hh<CR><LF>
 *        |  |  |   |
 *        1  2  3   4
 *
 * 1. Total number of sentences, 01 to 99
 * 2. Sentence number, 01 to 99
 * 3. Text identifier
 * 4. Text message(maximum : 61 characters)
 *
 ******************************************************************************/
class CTxt : public CSentence {
	protected:
		int  m_nTotalNo;
		int  m_nSentNo;
		int  m_nTextID;
		BYTE m_szTextMsg[MAX_TXT_LENGTH + 1];
        
    public:
        CTxt();
        CTxt(char *pszSentence);

		void Parse();
		void SetSentence(char *pszSentence);
		int  GetFormat() { return m_nFormat; }
		void GetPlainText(char *pszPlainText);
		
		int  GetTotalNo() { return m_nTotalNo; }
		int  GetSentNo()  { return m_nSentNo;  }
		int  GetTextID()  { return m_nTextID;  }
		void GetTextMsg(BYTE *pszTextMsg) { strcpy((char *)pszTextMsg, (char *)m_szTextMsg); }
};

#endif

