#ifndef __PORT_WND_HPP__
#define __PORT_WND_HPP__

#include "Wnd.hpp"
#include "ComboCtrl.hpp"

#define MAX_LBT_WAIT_SEC		  5	
#define MAX_PORT_WND_DATA_LINE	10
#define LPT_BAUD_RATE 1	// 38400 bps

class CPortWnd : public CWnd {
public:
	enum{
		PORT_SENS1 = 0,
		PORT_SENS2 = 1,
		PORT_SENS3 = 2,
		PORT_LONG  = 3,
		PORT_EXT   = 4,
		PORT_PILOT = 5,
		MAX_PORT
	};
	
private:
		enum {
		FOCUS_SENSOR1 = 0,
		FOCUS_SENSOR2,
		FOCUS_SENSOR3,
		FOCUS_LONG_RANGE,
		FOCUS_EXT,
		FOCUS_PILOT,
		FOCUS_COUNT
	};

	enum{
		BAUD_4800   = 0,
		BAUD_38400  = 1,
		BAUD_AUTO   = 2,
		MAX_BAUD
	};
	
	CComboCtrl *m_pLongRange;
	CComboCtrl *m_pExt;
	CComboCtrl *m_pPilot;
	CComboCtrl *m_pSensor1;
	CComboCtrl *m_pSensor2;
	CComboCtrl *m_pSensor3;
	
	int         m_nFocus;
	DWORD       m_dBaudRate;

	BOOL m_bPause;
	BOOL m_bAutoDetect;
	BOOL m_nAutoDetectPort;
	
	BOOL m_bLoopBackTest;
	int m_nCurLBTPort;
	int m_nLBTRemainSec;

	
	int  m_nLineNum;
	BYTE *m_pSentBuf[MAX_PORT_WND_DATA_LINE];
	int m_nBufCnt;
	BOOL m_bClosedByAlert;
	CComboCtrl *m_pControls[FOCUS_COUNT];

	int m_nBackLPTBaud[MAX_PORT];
	int m_nPrevPortBaud[MAX_PORT];
	int m_nCurPortBaud[MAX_PORT];

private:
	void InitVar();
	void SetPortSpeedForLoopBackTest();
	void RestorePortSpeedUserSet();
	
	int  GetPortBaudRate(int nPortNo);
	BOOL SetPortBaudRate(int nPortNo, int nBaudRate /* 0:4800, 1:38400  2: Auto*/);
	
public:
	CPortWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	void OnKeyEvent(int nKey, DWORD nFlags);
	void DrawFuncBtn();
	void UpdateControls();
	void DrawWnd(BOOL bRedraw = TRUE);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

	void SetFocus(int nFocus);
	BOOL SendKeyEvent(int nKey, DWORD nFlags);
	void ClearSentBuf();
	void RedrawSentBuf();
	void UpdateLoopBackTestResult(int nStat);
	void OutPutMsg(BYTE *pMsg);
	void PrintSentence(BYTE *pszSentence);
	void SendTestMsgToOutPort();
	void StartPortMonitoring();
	void StopPortMonitoring();

	void SetCurLBTPort(int nPort);
	int GetCurLBTPort();
	void SetLBTRemainSec(int nSec);
	int GetLBTRemainSec();
	void StartLoopBackTest();
	void FinishLoopBackTest();
	void CancelLoopBackTest();
	
	void StartAutoDetecting(int nPort);
	void CancelAutoDetecting(int nPort);
	void SuccessToAutoDetection(int nPort);
	void FailToAutoDetect(int nPort);
	
	void CloseComboCtrl();
	BOOL GetPauseStatus();
	void SetPauseStatus(BOOL bPause);

	BOOL GetLoopBackTestStatus();
	void SetLoopBackTestStatus(BOOL bLoopBackTest);
	
	BOOL GetAutoDetectStatus();
	void SetAutoDetectStatus(BOOL bAutoDetect);
	int  GetAutoDetectPort();
	void SetAutoDetectPort(int nPort);
	
	void SetAndSaveBaudRate();
	void InitPortSetting();

};

#endif


