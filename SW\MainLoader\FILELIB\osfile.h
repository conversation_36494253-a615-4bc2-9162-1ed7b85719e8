/*...........................................................................*/
/*.                  File Name : OSFILE.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.03.15                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __OSFILE_H__
#define  __OSFILE_H__

#ifdef  __cplusplus
extern "C" {
#endif

//============================================================================
#define OSF_NO_ERROR                           0
#define OSF_ERROR_GENERAL                     -1
//
//----- File Open Flags
//
#define OSF_READ_WRITE              0x00000000UL
#define OSF_READ_ONLY               0x00000100UL
#define OSF_CREATE                  0x00010000UL // create if it does not exist
#define OSF_CREATE_ALWAYS           0x00020000UL // always create
#define OSF_COMMITTED               0x01000000UL // commit all buffers after every write
#define OSF_CACHE_DATA              0x02000000UL // do not discard data buffers
//
//----- File Seek Options
//
#define OSF_FILE_BEGIN                         0
#define OSF_FILE_CURRENT                       1
#define OSF_FILE_END                           2

#define OSF_ATTR_READ_ONLY                 0x01U
#define OSF_ATTR_HIDDEN                    0x02U
#define OSF_ATTR_SYSTEM                    0x04U
#define OSF_ATTR_VOLUME                    0x08U
#define OSF_ATTR_DIR                       0x10U
#define OSF_ATTR_ARCHIVE                   0x20U
#define OSF_LONGNAME_ATTR  (OSF_ATTR_READ_ONLY | OSF_ATTR_HIDDEN | OSF_ATTR_SYSTEM | OSF_ATTR_VOLUME)
//============================================================================

//============================================================================
typedef int   FHANDLE;
//============================================================================

//============================================================================
#pragma pack(1)

typedef struct  __PACK__ {              // 32 bits
#if defined(EB)
   unsigned int Hour:5;
   unsigned int Minute:6;
   unsigned int Second2:5;
   unsigned int Year1980:7;
   unsigned int Month:4;
   unsigned int Day:5;
#else
   unsigned int Second2:5;
   unsigned int Minute:6;
   unsigned int Hour:5;
   unsigned int Day:5;
   unsigned int Month:4;
   unsigned int Year1980:7;
#endif
} OSFDOSDateTime;

typedef struct  __PACK__ {
   char           FileName[8];
   char           Extension[3];
   BYTE           Attributes;
   BYTE           NTReserved;
   BYTE           CreateTimeTenthSecond;  // range 0..199
   OSFDOSDateTime CreateDateTime;
   HWORD          LastAccessDate;         // not used
   HWORD          FirstClusterHi;         // FAT-32 only
   OSFDOSDateTime DateTime;               // of last modification
   HWORD          FirstCluster;
   DWORD          FileSize;
}  OSFDOSDirEntry;

#pragma pack()
//============================================================================

//============================================================================
FHANDLE OsfOpen(const char *pFileName,DWORD dFlags);
int     OsfClose(FHANDLE hFile);
int     OsfRead(FHANDLE hFile,void *pDataPtr,DWORD dLength,DWORD *pReadSize);
int     OsfWrite(FHANDLE hFile,const void *pDataPtr,DWORD dLength,DWORD *pWrittenSize);
long    OsfSeek(FHANDLE hFile,DWORD dOffset,int nWhence);

int     OsfCheckDir(const char *pDirName);
int     OsfCreateDir(const char *pDirName);
int     OsfRemoveDir(const char *pDirName);

FHANDLE OsfFindFirstEx(const char *pNamePattern,OSFDOSDirEntry *pDirEntry,char *pFileName,DWORD dMaxLength);
int     OsfFindNextEx(FHANDLE hFile,OSFDOSDirEntry *pDirEntry,char *pFileName,DWORD dMaxLength);
int     OsfFindClose(FHANDLE hFile);

int     OsfRename(const char *pFileName,const char *pNewName);
int     OsfFDelete(const char *pFileName);

int     OsfMountDisk(int nDiskNo);
int     OsfResetDisk(int nDiskNo);
int     OsfCommitAll(int nDiskNo);
int     OsfCloseAll(void);
int     OsfRawMediaChanged(int nDiskNo);
int     OsfGetDiskFreeSpace(int nDiskNo,QWORD *pDiskFreeSpace);
void    OsfGetFileDateTime(OSFDOSDateTime *pDosDateTime,int *pYear,int *pMonth,int *pDay,int *pHour,int *pMin,int *pSec);
//============================================================================
char    OsfGetSdCardDriveChar(void);
char   *OsfGetSdCardDriveStr(void);
char    OsfGetUsbDiskDriveChar(void);
char   *OsfGetUsbDiskDriveStr(void);
void    OsfAppendDriveName(int nDiskNo,char *pFileName);
int     OsfGetDriveMountNo(const char *pFileName);
char    OsfGetDrvNameCharByMountNo(int nDiskNo);
char   *OsfGetDrvNameStrByMountNo(int nDiskNo);
//============================================================================

#ifdef  __cplusplus
}
#endif

#endif

