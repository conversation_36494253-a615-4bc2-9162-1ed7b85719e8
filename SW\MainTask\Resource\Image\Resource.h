
#ifndef __RESOURCE_HPP__
#define __RESOURCE_HPP__

extern unsigned char G_BmpArrDayOn[];
extern unsigned char G_BmpArrDayOff[];
extern unsigned char G_BmpArrNightOn[];
extern unsigned char G_BmpArrNightOff[];

extern unsigned char G_BmpBtnOff[];
extern unsigned char G_BmpBtnOn[];
extern unsigned char G_BmpBtnOnClick[];
extern unsigned char G_BmpRightBG[];
extern unsigned char G_BmpTitleBG[];
extern unsigned char G_BmpTopBG[];
extern unsigned char G_BmpCloseMailNormal[];
extern unsigned char G_BmpOpenMailNormal[];
extern unsigned char G_BmpCloseMailWarn[];
extern unsigned char G_BmpOpenMailWarn[];

#ifdef EN_61993_ED3
extern unsigned char G_BmpAlrCautionDay[];
extern unsigned char G_BmpAlrCautionNight[];
#endif	// End of (EN_61993_ED3)

extern unsigned char G_BmpAlrAANormal[];
extern unsigned char G_BmpAlrAVNoSndInv[];
extern unsigned char G_BmpAlrAVNoSndNor[];
extern unsigned char G_BmpAlrAVSndInv[];
extern unsigned char G_BmpAlrAVSndNor[];
extern unsigned char G_BmpAlrVVInv[];
extern unsigned char G_BmpAlrVVNor[];
extern unsigned char G_BmpAlarmResponsibilityTransferredInv[];
extern unsigned char G_BmpAlarmResponsibilityTransferredNor[];

extern unsigned char G_BmpSpkOff[];
extern unsigned char G_BmpSpkOn[];
extern unsigned char G_BmpTriangle01[];
extern unsigned char G_BmpTriangle02[];

#endif	// End of __RESOURCE_HPP__

