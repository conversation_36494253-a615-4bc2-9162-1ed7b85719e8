#ifndef __PRE_SHARED_KEY_WND_HPP__
#define __PRE_SHARED_KEY_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"

class CPreSharedKeyWnd : public CWnd 
{
	private:
		enum {
			FOCUS_PASSWORD_NEW = 0,
			FOCUS_PASSWORD_CONFIRM
		};
		
		CEditCtrl *m_pNewPassword;
		CEditCtrl *m_pConfirmPassword;

	public:
		CPreSharedKeyWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void OnCursorEvent(int nState);
		void DrawWnd(BOOL bRedraw = TRUE);
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		BOOL IsNewConfirmEqual();
		void ResetPasswordBox();
};

#endif	// End of __PRE_SHARED_KEY_WND_HPP__

