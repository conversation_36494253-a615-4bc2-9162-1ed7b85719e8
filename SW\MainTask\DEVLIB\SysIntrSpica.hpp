/*...........................................................................*/
/*.                  File Name : SYSINTR.HPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.11                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __INTR_HPP__
#define  __INTR_HPP__

//=============================================================================
    #define IRQ_PHY_PDISPLAY              0
    #define IRQ_PHY_SDISPLAY              1
    #define IRQ_PHY_VIP                   2
    #define IRQ_PHY_DMA                   3
    #define IRQ_PHY_SYSTIMER0             4
    #define IRQ_PHY_SYSCTRL               5
    #define IRQ_PHY_RESERVED06            6
    #define IRQ_PHY_MPEGTSI               7
    #define IRQ_PHY_RESERVED08            8
    #define IRQ_PHY_RESERVED09            9
    #define IRQ_PHY_UART0                10
    #define IRQ_PHY_SYSTIMER1            11
    #define IRQ_PHY_SSPSPI               12
    #define IRQ_PHY_GPIO                 13
    #define IRQ_PHY_SDMMC0               14
    #define IRQ_PHY_SYSTIMER2            15
    #define IRQ_PHY_H264                 16
    #define IRQ_PHY_MPEG                 17
    #define IRQ_PHY_RESERVED18           18
    #define IRQ_PHY_VLC                  19
    #define IRQ_PHY_UDC                  20
    #define IRQ_PHY_SYSTIMER3            21
    #define IRQ_PHY_DEINTERLACE          22
    #define IRQ_PHY_PPM                  23
    #define IRQ_PHY_AUDIOIF              24
    #define IRQ_PHY_ADC                  25
    #define IRQ_PHY_MCUSTATIC            26
    #define IRQ_PHY_GRP3D                27
    #define IRQ_PHY_UHC                  28
    #define IRQ_PHY_ROTATOR              29
    #define IRQ_PHY_SCALER               30
    #define IRQ_PHY_RTC                  31
    #define IRQ_PHY_I2C0                 32
    #define IRQ_PHY_I2C1                 33
    #define IRQ_PHY_UART1                34
    #define IRQ_PHY_UART2                35
    #define IRQ_PHY_UART3                36
    #define IRQ_PHY_UART4                37
    #define IRQ_PHY_UART5                38
    #define IRQ_PHY_SSPSPI1              39
    #define IRQ_PHY_SSPSPI2              40
    #define IRQ_PHY_CSC                  41
    #define IRQ_PHY_SDMMC1               42
    #define IRQ_PHY_SYSTIMER4            43
    #define IRQ_PHY_RESERVED44           44
    #define IRQ_PHY_I2C2                 45
    #define IRQ_PHY_I2S                  46
    #define IRQ_PHY_MPEGTSP_TSI          47
    #define IRQ_PHY_MPEGTSP_TSP          48
    #define IRQ_PHY_CDROM                49
    #define IRQ_PHY_ALIVE                50
    #define IRQ_PHY_EHCI                 51
    #define IRQ_PHY_OHCI                 52
    #define IRQ_PHY_RESERVED53           53
    #define IRQ_PHY_RESERVED54           54
    #define IRQ_PHY_RESERVED55           55
    #define IRQ_PHY_RESERVED56           56
    #define IRQ_PHY_RESERVED57           57
    #define IRQ_PHY_RESERVED58           58
    #define IRQ_PHY_RESERVED59           59
    #define IRQ_PHY_RESERVED60           60
    #define IRQ_PHY_RESERVED61           61
    #define IRQ_PHY_RESERVED62           62
    #define IRQ_PHY_RESERVED63           63
//----------------------------------------------------------------------
#define INT_MODE_IRQ                      0
#define INT_MODE_FIQ                      1
//=============================================================================

//=============================================================================
#ifdef  __cplusplus
extern "C" {
#endif

#if defined(__USE_RTOS__)
    void  IsrHandlerIRQ(void);
    void  IsrHandlerFIQ(void);
#else
    void  IsrHandlerIRQ(void) __attribute__ ((interrupt ("IRQ")));
    void  IsrHandlerFIQ(void) __attribute__ ((interrupt ("FIQ")));
#endif
void  IsrHandlerSWI(void) __attribute__ ((interrupt ("SWI")));
void  IsrHandlerABORT(void) __attribute__ ((interrupt ("ABORT")));
void  IsrHandlerUNDEF(void) __attribute__ ((interrupt ("UNDEF")));
void  ClearWatchDogCounter(void);
void  RunWatchDogCounter(void);
void  SetWatchDogRunMode(int nMode);
void  SetDrawDogCounter(DWORD dCounter);
void  SetReStartRunMode(int nMode);
void  ReStartMainBooter(void);

#ifdef  __cplusplus
}
#endif
//=============================================================================
void  IsrHandlerTimer0(void);
void  IsrHandlerTimer1(void);
void  IsrHandlerUART0(void);
void  IsrHandlerUART1(void);
void  IsrHandlerUART2(void);
void  IsrHandlerUART3(void);
void  IsrHandlerUART4(void);
void  IsrHandlerUART5(void);

void  IsrHandlerGPIO(void);
void  IsrHandlerSSPSPI0(void);
//=============================================================================
void  SysSetAllInterrupt(void);
void  SysSetOneInterrupt(int nIntNo,int nIRQ_FIQ);
void  SysSetAllInterruptDisable(void);
//=============================================================================

#endif

