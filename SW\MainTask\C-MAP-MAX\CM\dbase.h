#ifndef __DBASE
#define __DBASE

#ifdef DEBUG_INCLUDES
#pragma message( "+++++++++ including dbase.h +++++++++" )
#endif


#include "cmaptype.h"
#include "cmg/cmg.h"

#define MeasList_ID     0
#define GreatList_ID    1

#define LibMeasList_ID		2
#define HARDCODED1_ID		39
#define NATQUAValues_ID		41
#define NATSURValues_ID		43
#define COLOURValues_ID		42
#define LITCHRListE_ID		4
#define COLOURListE_ID		6
#define CATFOGListE_ID		8
#define CATLAMListE_ID		10
#define CATCAMListE_ID		12
#define BCNSHPListE_ID		14
#define COLMARListE_ID		16
#define BOYSHPListE_ID		18
#define CATINBListE_ID		20
#define CATRTBListE_ID		22
#define CATROSListE_ID		24
#define BUOYBCNListE_ID		26
#define BNE_ID				28
#define BUOYE_ID			30
#define RADRFLE_ID			32
#define RADSTAE_ID			34
#define TOWERE_ID			36
#define PLATFORME_ID		38
#define LibCARDINALGRID_ID	47

/* the following NUMBER_OF_STRINGS value must be equal to the number of hard coded strings tags the
assembler defined in the cartridge data dic (look at TABLES.H/.CPP of ASS_CF95). The function
CF95_GetDataDic reads the appropriate tag from data dic when needed (look for this function all
around the library as an example of management.
 ##SSMLQI ##SSMLGR
*/
#define NUMBER_OF_STRINGS	24

/* ----------------------------------------------------- */
/* Find Near Geoms Masks */
#define G_1D         		0x00001
#define G_2DO        		0x00002
#define G_2DC        		0x00004
#define G_3D         		0x00008
#define G_COMP       		0x00010
#define G_MULTI_1D   		0x00020
#define G_MULTI_2DC  		0x00040
#define G_MACRO_NF			0x00080 /* Reserved, not to use */
#define G_MACRO_F			0x00100 /* Reserved, not to use */
#define G_BASE_2DC			0x00200
#define G_BASE_MULTI_2DC	0x00400
#define G_MULTI_COMPLEX     0x00800
#define G_MULTI_COMPLEX_MM  0x01000
#define G_QT_AREAS			0x02000
#define G_MORAS				0x04000

#define G_GRAPH_NODE		0x08000
#define G_MULTI_3D			0x10000
#define G_ALL_POINTS		(G_1D | G_3D | G_MULTI_3D | G_COMP | G_MULTI_1D | G_MULTI_COMPLEX | G_MULTI_COMPLEX_MM)
#define G_ALL_LINES			(G_2DO | G_GRAPH_NODE )
#define G_ALL_AREAS			(G_2DC | G_MULTI_2DC | G_BASE_2DC | G_BASE_MULTI_2DC | G_QT_AREAS | G_MORAS )

#define G_ALL        		0x1ffff

/* Find near result constants */
#define GEOM1D          		0
#define GEOM2DO         		1
#define GEOM2DC        			2
#define GEOM3D          		3
#define GEOM_COMP       		4
#define GEOM_MULTI_1D    		5
#define GEOM_MULTI_2DC   		6
#define GEOM_MACRO_NOT_FILLED	7
#define GEOM_MACRO_FILLED 		8
#define GEOM_BASE_2DC			9
#define GEOM_BASE_MULTI_2DC	    10
#define GEOM_MULTI_COMPLEX      11
#define GEOM_MULTI_COMPLEX_MM   12
#define GEOM_QT_AREAS			13
#define GEOM_MORAS				14
#define GEOM_GRAPH_NODE			15
#define GEOM_MULTI_3D			16
#define GEOM_COMP_AS_MULTI_COMP 17

#define MAX_GEOMS               18
#define NO_LIMIT				17

#define GEOM_ELVARE_FAKE_VAL			MAX_GEOMS+1		
/* ----------------------------------------------------- */
#define SMALL_INFO

#ifdef SMALL_INFO
	#define  ATTR_BITS_ARRAY_SIZE       16
	#define  ATTR_LABELS_ARRAY_SIZE     128
#else
	#define  ATTR_BITS_ARRAY_SIZE       32
	#define  ATTR_LABELS_ARRAY_SIZE     256
#endif

#define  DESCR_LEN                  30

#define  SHORT_STRING_LEN           16

#define  OVERWRITE_MODE             0
#define  APPEND_MODE                1

#define  FROM_DATA_DICTIONARY       0x80
/* ----------------------------------------------------- */
/* DEFINES FOR ATTRIBUTE EXPANSION LEVEL */
#define ATTR_LEVEL_A 0
#define ATTR_LEVEL_B 1
#define ATTR_LEVEL_C 2
#define ATTR_LEVEL_D 3
/* ----------------------------------------------------- */

/* List of greatnesses */

#define UNIT_Number		  0
#define UNIT_Height       1
#define UNIT_Depths       2
#define UNIT_Frequency    3
#define UNIT_Force        4
#define UNIT_Angle        5
#define UNIT_Time         6
#define UNIT_NautDistance 7
#define UNIT_SmallLength  8
#define UNIT_DimLess      9
#define UNIT_Speed        10
#define UNIT_Temperature  11
#define UNIT_Mass         12
#define UNIT_Distance     13
#define UNIT_Elevation    14

/* List of measuring units */

#define UNIT_NUMBER     0
#define UNIT_METERS     1
#define UNIT_HERTZ      2
#define UNIT_KILONEWTON 3
#define UNIT_TONS       4
#define UNIT_DEGREES    5
#define UNIT_SECONDS    6
#define UNIT_MILES      7
#define UNIT_MILLIMETER 8
#define UNIT_NOUNIT     9
#define UNIT_CELSIUS    10
#define UNIT_KMH        11
#define UNIT_KILOGRAM   12

#define UNIT_STATUE_MILES  256
#define UNIT_KILOMETER     257
#define UNIT_FEET          258
#define UNIT_FATHOMS       259
#define UNIT_FLIGHT_LEVEL  260

#define UNIT_KILO_HERTZ    261
#define UNIT_MEGA_HERTZ    262

#define UNIT_POUND         263

#define UNIT_FARENHEIT     264

#define UNIT_MPH           265
#define UNIT_KNOT          266

#define UNIT_RADIANTS      267
#define UNIT_MERC_METER    268
#define UNIT_PASSI_BRACCIO 269




/* ------------------- eBytesList Type --------------------*/
#define EBYTESLIST_BANNER      0
#define EBYTESLIST_PNG         1
#define EBYTESLIST_JPG_PALETTE 2
#define EBYTESLIST_JPG         3
#define EBYTESLIST_WI          4


/*-------------------------------------------------------------------------------*/
/*-  External interfaces  -------------------------------------------------------*/
/*-------------------------------------------------------------------------------*/

typedef struct 
{
	Word X;
	Word Y;
} sCellID;


typedef struct 
{
	sCellID P1;
	sCellID P2;
} sCellIDRect;

typedef struct
{
	SLong x1;
	SLong y1;
	SLong x2;
	SLong y2;
}sMercatorRectangle;


#ifdef USE_GRAPH_NODE


/* FAST DB */
/* Defines GN_MASK_PTR_TYPE already present in cf95lib.h */

#define GN_MASK_PTR_TYPE					0x0000000e

	#define GN_MASK_PTR_TYPE_DIRECT_NO_INTER		0x00	//1: 0000 000 0
	#define GN_MASK_PTR_TYPE_DIRECT_W_INTER 		0x08	//2: 0000 100 0
	#define GN_MASK_PTR_TYPE_BACK					0x02	//3: 0000 001 0
	#define GN_MASK_PTR_TYPE_FAKE					0x0a	//4: 0000 101 0
	#define GN_MASK_PTR_TYPE_DRAW_NO_INTER			0x04	//5: 0000 010 0
	#define GN_MASK_PTR_TYPE_DRAW_W_INTER			0x0c	//6: 0000 110 0
	#define GN_MASK_PTR_TYPE_NOPTRS					0x06	//7: 0000 011 0
/* END of GN_MASK_PTR_TYPE defines */

#define GN_NET_CLASS_MASK	0x00000007

#define GN_LENGTH_MASK		0x000007f8
#define GN_LENGTH_SHIFT		3
#define GN_LENGTH_MASK_X	0x00003800
#define GN_LENGTH_SHIFT_X	11
		
#define GN_KPH_MASK			0x0003c000
#define GN_KPH_SHIFT		14

#define GN_FOW_MASK			0x001c0000
#define GN_FOW_SHIFT		18

#define GN_FRC_MASK			0x00e00000
#define GN_FRC_SHIFT		21

/* Set masks */
#define GN_COND_BAD			0x01000000

#define GN_TOLL				0x02000000

#define GN_CONSTRUCTION		0x04000000

#define GN_REVERT_HN_SIDE	0x08000000

#define GN_IS_A_FAKE		0x80000000

typedef struct NodePointer
{
	SWord		Level;
	sCellID		ID;
	Long		NodeOffset;
} sNodePointer;

typedef struct
{
	SLong	LatMt;
	SLong	LonMt;
} sNodePoint;

#define NP_HAS_NODE2		0x01
#define NP_HAS_NODEPOS		0x02
#define NP_HAS_ARC			0x04
#define NP_HAS_ARC_ONEWAY	0x08
#define NP_HAS_VEHICLE_DIR	0x10

typedef struct NodePointerEx
{
	Byte			Mask;
	sNodePointer	Node1;
	sNodePointer	Node2;
	sNodePoint		NodePos;
	Long			ArcLength;
	Long			ArcPortion;
	SLong			ArcSegCount;
	Long			ArcOffset;
	Word			VehicleDirection;
}sNodePointerEx;

#define MAX_MANOEUVRES	16
#define MAX_SIGNPOSTS	16
typedef struct aManoeuvre
{
	Byte								Manoeuvre;	/* 0 - no Manouver, 1 first index of manouver, 2 next */
	Byte								ManoeuvreType;
	Long								ManoeuvrePrevNodeAddress;
	Byte								ManoeuvreDestinationArcCounter;
} sManoeuvre;

typedef struct aSignpost
{
	Byte								Signpost;	/* 0 - no Manouver, 1 first index of manouver, 2 next */
	Long								SignpostPrevNodeAddress;
	Byte								SignpostDestinationArcCounter;
} sSignpost;

#define ARC_HAS_VIA_POINT	0x80

typedef struct ArcPointer
{
	Long		NodeOffset;
	Long		ArcOffset;
	Long		DBPtr;
	Long		EncodedFastDB;

	Byte		ExtFastDB;		/*
									Bit 0: Arc is bad for truck
									Bit 1: Arc is in built up area
								*/

	SLong		turnCost4Prec;
	SLong		turnCost4FRCChange;
	SLong		turnCost4FRCCross;
	Long		Length_Time;
	SLong		SegCount;
	sNodePoint	NodePos;

	Byte		ExitingArcs;

	Byte		ManoeuvresCount;
	sManoeuvre	*Manoeuvres;

	Byte		SignpostsCount;
	sSignpost	*Signposts;

} sArcPointer;


typedef struct PathNode 
{
	sArcPointer			Arc;
	sMercatorRectangle	Rect;
	void				*CellPtr;
} sPathNode;


typedef struct PathArcGeometryPoint
{
	SLong MX;
	SLong MY;
} sPathArcGeometryPoint;

typedef struct PathArcGeometry
{
	Long					NumOfPoints;
	sPathArcGeometryPoint *Points;
} sPathArcGeometry;

typedef struct NodeExitingArcs
{
	Long NumOfExitingArcs;
	Long NumOfEnteringArcs;
	sPathNode *ExitingData;
	sPathNode *EnteringData;
} sNodeExitingArcs;
#endif /* USE_GRAPH_NODE */

#define ATTR_INTERNAL_VALUE_INVALID	0x0f0f0f0f

typedef struct
{
	Byte GreatCode;
	Word MeasCode;
    const char *name;
}sConvertUnit;


typedef struct
{
	SLong	mx;
	SLong	my;
} sPolyPoint;

#define VPM_INFO_ON_DBPTR	0x01
#define VPM_INFO_ON_AI		0x02
#define VPM_INFO_ON_GN		0x04

typedef struct
{
	Word	CdgNum;
	Word	Label;
	Long	DBPtr;
	Long	CompPtr;
	Word	count;
	Byte	Type;
	Byte	MCLevel;
	Byte    ValuePlaceMask; /*
													This new field is added due to the fact that we want to virtualize better
													the info functions to make the high level have more info to understand where 
													data must be retrieved from. This is a byte mask so structured:
													bit 0: object with information given by the DBPtr YES/NO
													bit 1: retrieve information from AI structure.
													bit 2: retrieve information from GN structure.
													bit 3: to be defined
													bit 4: to be defined
													bit 5: to be defined
													bit 6: to be defined
													bit 7: to be defined
												 */
	union
	{
		struct 
			{
			Byte	ACL;
			Long	Value;
			Word	Unit;
			} Multi3D;

		struct 
			{
			Double	AdditionalInfo;
			Long	QTPath;
			} AI;

		struct 
			{
			SLong LatMet;
			SLong LonMet;
			} LL;
		struct 
			{
			Pixel LatPix;
			Pixel LonPix;
			Byte  ACL;
			} LLPix;
#ifdef USE_GRAPH_NODE
		struct
			{
			SLong LatMet;
			SLong LonMet;
			
			Long FastDB; 
			Long Mask;
			Long SegmentIndex;
			Float RatioFirstLast;
			/* Release 172 */
			SLong HouseNumber;

			sNodePointer NodePtr;
			sNodePointer Node2Ptr;
			} GN;
#endif
		
	}U;
/* 
the bit 7 of the field MCLevel contains the bool flag 'selected'. If in multi complex tree
an item is seleted by a user's click, this flag is true
*/
}sObjInfo;

/**
 * sNearInfo structure.
 *	
 * Used to store results of a find near function.
 *
 *	typedef struct\n
 *	{\n
 *	\b sObjInfo area;\n
 *	\b sObjInfo father;\n
 *	\b sObjInfo itself;\n
 *	\b long LatMet;\n
 *	\b long LonMet;\n
 *	\b float dist;\n
 *	}\b sNearInfo;\n\n\n
 *	\b area is the the pointer to the sObjInfo that represents the father of the family type.\n
 *  For example if family type is ePortInfoFamily area represents the object with label P_AREA father of the found object.\n
 *	Note that this object may be different from \b father.\n\n
 *  \b father is the pointer to the father of the found object.\n\n
 *	\b itself is the pointer to the found object.\n\n
 *  \b LatMet is the latitude coordinate in mercator meters of the found object (\b itself).\n\n
 *  \b LonMet is the longitude coordinate in mercator meters of the found object (\b itself).\n\n
 *  \b dist is a float value indicating the distance between the input coordinates and the found object position.
 * \ingroup NEAREST_FEATURE
 */
typedef struct
{
	sObjInfo area;
	sObjInfo father;
	sObjInfo itself;
	long LatMet;
	long LonMet;
	float dist;
}sNearInfo;

typedef struct
{
	sObjInfo L1;
	sObjInfo L2;
	sObjInfo L3;
	long LatMet;
	long LonMet;
	float dist;
}sNearestInfo;

typedef struct
{
	Word     Label;
	Word     CdgNum;
	Long     AttrPtr;
}sReducedAttribute, *sReducedAttributePtr;

typedef struct
{
	Word     Label;
	Word     Type;
	Byte     ExtendedType;
	SLong    MinValue;
	Byte     Greatness;
	Byte     StoreUnit;
	Byte     DisplayUnit;
	Long     DescriptionPtr;
	Word     NumOfEntries;
	Long     ValueDescrPtr;
} sAttribute, *sAttributePtr;

#define DRAW_ATTR_STRING_LEN	200

/* The maximum number of depnum list is 255 */
#define DRAW_ATTR_LIST_LEN		255

#define NUM_OF_CONVERTABLES 20

//#ifdef MANAGE_MULTIMEDIA_SECTION
	#define MAX_NUM_OF_MULTIMEDIA_ELEMENTS 255
//#endif 

typedef struct  
	{
	 Byte          eTagVal;
	 SLong         eINTval;
	} sEByteListValue;

#define SIZEOF_BITSTREM_DESCRIPTOR		3
#define loclty_BITSTREAM_DESCRIPTOR		0

typedef enum
{
	eLcltyStart=0,
	eLcltySideStart,
	eLcltySideContinue,
	eLcltyTypeStart,
	eLcltyTypeContinue,
	eLcltyHuffmanStart,
	eLcltyHuffmanContinue		
}eLcltyBSMode;

typedef enum 
{
	ePO_PREFNAME,
	ePO_PERMNAME,
	ePPN,
	eFIPS,
	eMCD,
	eLAST_DESCRIPTOR_FIELD/*This value must be the last*/
}eLOCALITY_FIELD_ID;

typedef enum 
{
	eSIRight=0,
	eSILeft,
	eSILeftRight
}
eSideIndicator;

typedef struct 
{
	Byte				NumOfLocalityGroup;
	Byte				LocalityGroupReaded;
	eLcltyBSMode		Mode;
	/*SideIndicator Section*/
	eSideIndicator		SideIndicator;
	Byte				NumOfSideIndicatorCharWritten;
	/*Locality Type section*/
	eLOCALITY_FIELD_ID	LocalityType;
	Word				NumOfLocTypeCharWritten;
	Bool				FirstLocalityType;
	/*HuffmanSection*/
	Byte				HuffmanMode;
	sBitsPtr			HuffSringPtr;
	sBitsPtr			HuffTokenPtr;
	Byte				HuffSeparator;
}sLocalityBitStreaminfo;

typedef union
{
	sLocalityBitStreaminfo lbsInfo;

}sBitStreamInfo;

#define MAX_BITSTREAM_LEN 30
typedef struct
{
	UnicodeString	Stream[MAX_BITSTREAM_LEN];
	Bool			ModeStart;
	Byte			StremType;
	/*Private Section*/
	SWord			CdgNum;
	sBitsPtr		StreamPtr;
	sBitStreamInfo	StreamInfo;
}sBitStreamValue;

typedef struct
{
	Byte				AttrValueType;
    Word				ListLen;
	union
		{
		SLong				eINTval;
		Float				eFLOATval;
        UnicodeString       eSTRINGval[DRAW_ATTR_STRING_LEN];
		SLong				eLISTval[DRAW_ATTR_LIST_LEN];
		sEByteListValue     eByteListVal;
//#ifdef MANAGE_MULTIMEDIA_SECTION
		SLong				eBINLISTval[MAX_NUM_OF_MULTIMEDIA_ELEMENTS];//MAX NUM OF MULTIMEDIA ELEMENTS
//#endif
		sBitStreamValue		eBITSTREAMval;
		} v;
} sDrawAttrValue;

/* --------------------  INFO STRUCTURES  ----------------------------------- */
/* INTERNAL ENUM */
typedef enum
	{
	eINT,			/* 0 */
	eFLOAT,			/* 1 */
	eCSTRING,		/* 2 */
	ePSTRING,		/* 3 */
	eBITSTREAM=ePSTRING,
	eASCII6,		/* 4 */
	eLIST,			/* 5 */
	eBYTE,			/* 6 */
	eWORD,			/* 7 */
	eLONG,			/* 8 */
	eFLPNT,			/* 9 */
	eBYTESLIST,		/* 10 */
	eUNICODE,		/* 11 */
	eUNICODE_ML_STRING=eUNICODE,/* 11 */
	eEXTTYPE,		/* 12 */
	eBINLIST,		/* 13 */		
	eHUFFMANTOKEN	/* 14 */
   } eDBAttributeTypes;

typedef enum
	{
	AS_IS = 0,
	DIV_10,
	DIV_100,
	MUL_10,
	MUL_100,
	MUL_2,
	MUL_4,
	EXT_RULE
	} eDBAttributeRules;

typedef Byte               AttrBitsArray[ATTR_BITS_ARRAY_SIZE];
typedef sReducedAttribute  AttrArray[ATTR_LABELS_ARRAY_SIZE];

/* DATA STRUCTURE FOR UNLIMITED LENGHT STRINGS */
/* THE STRING IS READ IN THE BUFFER AND IF IT'S NOT ENOUGH CAN BE READ CHAR BY */
/* CHAR AFTER THE LAST READ */

/* EXTERNAL ENUM */
typedef enum
	{
	kINT,
	kFLOAT,
	kSTRING,
	kLIST,
	kBYTESLIST,
	kUNICODE_STRING,
	kEXTTYPE,
//#ifdef MANAGE_MULTIMEDIA_SECTION
	kBINLIST,
//#endif
	kBITSTREAM
	} eAttributeTypes;

/*************MULTILANGUAGE SUPPPORT******************/
#define ML_DEFAULT_LANGUAGE "EN"
#define ML_DEFAULT_LANGUAGE_ID 1

typedef enum
{
	MLS_START,
	MLS_CONTINUE,
	MLS_END
} MLStringMode;

typedef struct 
{
	MLStringMode MLS_MODE;	/* Start, Continue or End to read the string                                    */
	Byte NumOfLanguages;    /* Number of languages in the Multi Language String                        */
	Byte LanguagePos;		/* Position of the used language in the Multi Language String              */
	Byte StringType;		/* Type of the string to read: ePSTRING, eCSTRING, eASCII6, eHUFFMAN_TOKEN */
							/* If FROM_DATA_DICTIONARY flag is setted the EAttrValPtr is not moved     */
							/* Example StringType = (eASCII6 | FROM_DATA_DICTIONARY)                   */
	sBitsPtr TokenBitPtr;	/* Pointer used if the string read is a Huffman encoded */
	Byte Mode;
	Byte SepCode;

    Byte languageId;
} sMLStringInfo;
/******************************************************/

typedef struct
	{
	Byte  Type;             /* Type of the string to read: PSTRING, CSTRING, ASCII6 */
	UnicodeString Buf[DESCR_LEN];   /* Buffer for the first 30 chars of the string */
	Word  CdgNum;
	union
		{
		Long  Ptr;     	/* Pointer to the next char of the string in the DB */
		sBitsPtr  BitPtr;  	/* Pointer to the next bit of the ASCII6 in the DB */
		} s;
	sBitsPtr TokenBitPtr;	/* Pointer used if the string read is a Huffman encoded */
						/* string and the reading is interrupted in the middle */
						/* of a token. In this case this is the pointer to the */
						/* next caracter in the token to be read */
	Byte CharCount;
	Byte HuffReadMode;	/* Mode to use to read the next character in an huffman */
						/* encode string */
	Byte SepCode;
	sMLStringInfo MLS_Info;	/* MultiLanguage Information*/

	sBitStreamValue BitStream_Info;
	} sStringValue;

typedef struct
	{
	Word           Num;          /* Number of values in the list */
	Word           Index;        /* Actual value to read */
	sBitsPtr        Addr;         /* Pointer to value to read (in DB) */
	Byte           NumOfBits;    /* Number of bits to read for each value */
	Long           Value;

	Word           TotNum;

	Word           CdgNum;
	Long           DescrPtr;

	sStringValue   StrVal;  /* Unlimited string structure */
	} sListValue;

//#ifdef MANAGE_MULTIMEDIA_SECTION

typedef struct
{
	Word           Num; 
	Long		   Value;
	sBitsPtr	   Addr;
}
sBinListValue;

//#endif

typedef union
	{
	SLong          eINTval;
	Float          eFLOATval;
	sStringValue   eSTRINGval;
	sListValue     eLISTval;
	Long           eEXTTYPEval;
	sEByteListValue    eByteListValue;
//#ifdef MANAGE_MULTIMEDIA_SECTION
	sBinListValue eBINLISTval;
//#endif
	sBitStreamValue eBITSTREAMval;
	} sAttrValue;

typedef struct
	{
	Word			AttrLabel;
	sStringValue	AttrDescriptor;

	Byte			Greatness;
	Byte			StoreUnit;
	Byte			DisplayUnit;

	Byte			AttrValueType;
	sAttrValue		AttrValue;

	SLong			iAttrValue;

	} sAttrInfoExp, *sAttrInfoExpPtr;

typedef struct
	{
	Byte  IsComplex;

	sStringValue   	Desc;    /* Buffer for the first 30 chars of the string */
	Word           	CompNum; /* Number of Attributes of this object */

	AttrBitsArray     AttrBits;
	AttrArray         Attributes;
	sBitsPtr           AttrListPtr;

	} sObjInfoExp,*sObjInfoExpPtr;

/*********FIND NEAREST MULTIMEDIA OBJECT -CONTENT FLAG-***********/
#define	MULTIMEDIA_FIND_IMAGE 0x01
#define MULTIMEDIA_FIND_AUDIO 0x02
#define MULTIMEDIA_FIND_ALL   (MULTIMEDIA_FIND_IMAGE | MULTIMEDIA_FIND_AUDIO)
/*****************************************************************/


#ifdef __cplusplus
extern "C"
	{
#endif

/**
 * Multi complex family type enum.
 *
 * It represent the enumeration type to specify the multi complex family \n
 * \b ePortInfoFamily for port area
 * \b eOutdoorRecreationalArea for outdoor recreational areas family
 *
 * @version 1.0.
 * 
 * @see	\c cmGetNearestObjectsMC(), \c cmMCInitObjList, \c cmMcNearestAvailable().
 *
 * \ingroup MULTI_COMPLEX_FUNCTIONS
 */
typedef enum
{
	ePortInfoFamily = 261,
	eOutdoorRecreationalArea = 424
}eMC_FamilyType;

/* DataBase Functions */
PRE_EXPORT_H void IN_EXPORT_H cmFindNearGeoms(Pixel x, Pixel y, sObjInfo *Arr, Word *Num);
PRE_EXPORT_H void IN_EXPORT_H cmFindNear(Long GeomType, Pixel x, Pixel y, sObjInfo *Arr, Word *Num);
PRE_EXPORT_H Byte IN_EXPORT_H cmFindNearExt(Long GeomType, Pixel x, Pixel y, sObjInfo *Arr, Word *Num, Word *MaxLevel);
PRE_EXPORT_H void IN_EXPORT_H cmSetFindNearRange(SWord Range);
PRE_EXPORT_H void IN_EXPORT_H cmSetFindNearPointRange(SWord Range);

PRE_EXPORT_H void IN_EXPORT_H cmEnableTextBoxSearch(Bool flag);

PRE_EXPORT_H void IN_EXPORT_H cmFindNearLine(Long GeomType, SLong x1m, SLong y1m, SLong x2m, SLong y2m, SWord *SkipObj, SWord SkipSize, sObjInfo *Arr, Word *Num);
PRE_EXPORT_H void IN_EXPORT_H cmFindInPolygon(sPolyPoint *Polygon , Word PolyPoints, Long GeomType, SWord *SkipObj, SWord SkipSize, sObjInfo *Arr, Word *Num);

PRE_EXPORT_H Word IN_EXPORT_H cmExpandObject(sObjInfo *obj, sObjInfoExp *info);
PRE_EXPORT_H Word IN_EXPORT_H cmExpandComplexObject(sObjInfo *obj, sObjInfo *Arr, Word *Num);
PRE_EXPORT_H Bool IN_EXPORT_H cmObjIsSkipped(sObjInfo Obj);

PRE_EXPORT_H void IN_EXPORT_H cmInitAttributeExpansion(sObjInfoExp *info);
PRE_EXPORT_H Word IN_EXPORT_H cmExpandAttribute(sObjInfoExp *info, sAttrInfoExp *attrinfo);
PRE_EXPORT_H Word IN_EXPORT_H cmExpandAttributeNoAssoc(sObjInfoExp *info, sAttrInfoExp *attrinfo);
PRE_EXPORT_H Word IN_EXPORT_H cmSkipAttribute(sObjInfoExp *info, sAttrInfoExp *attrinfo);
PRE_EXPORT_H void IN_EXPORT_H cmSetAttributeExpansionLevel(Word Level);

PRE_EXPORT_H Word IN_EXPORT_H cmExpandSelectedAttribute(sObjInfoExp *info,Word AttrLabel, sAttrInfoExp *attrinfo);

PRE_EXPORT_H Word IN_EXPORT_H cmExpandSelectedTypeAttribute(sObjInfoExp *info,eAttributeTypes AttrType, sAttrInfoExp *attrinfo);

PRE_EXPORT_H Bool IN_EXPORT_H cmInitGetObjAttrVal(Word ObjLabel, sObjInfo *Object);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetObjAttrVal(Word AttrLabel, sObjInfo *Object, sDrawAttrValue *value);

PRE_EXPORT_H Word IN_EXPORT_H cmFastObjInfo(sObjInfo *obj, Word CompArrSize, sObjInfo *CompArr, UnicodeString *FastInfo,Word FastInfoLen);

PRE_EXPORT_H Word IN_EXPORT_H cmReadNextListValue(sAttrInfoExp *attrinfo);
PRE_EXPORT_H Word IN_EXPORT_H cmReadNextListValueNoAssoc(sAttrInfoExp *attrinfo);
PRE_EXPORT_H Word IN_EXPORT_H cmReadAttrDescString(sAttrInfoExp *attrinfo, UnicodeString *buffer, Word MaxLen, Word Mode);
PRE_EXPORT_H Word IN_EXPORT_H cmReadAttrValString(sAttrInfoExp *attrinfo, UnicodeString *buffer, Word MaxLen, Word Mode);
PRE_EXPORT_H Word IN_EXPORT_H cmReadAttrListString(sAttrInfoExp *attrinfo, UnicodeString *buffer, Word MaxLen, Word Mode);
PRE_EXPORT_H Word IN_EXPORT_H cmReadObjString(sObjInfoExp *objinfo, UnicodeString *buffer, Word MaxLen, Word Mode);

PRE_EXPORT_H Bool IN_EXPORT_H cmMapListValueToString(Word Label, Long Value, UnicodeString *Buffer, Word MaxLen);
PRE_EXPORT_H Long IN_EXPORT_H cmGetEnhancedAntiClutterScale(sObjInfo *ObjInfo);

PRE_EXPORT_H Word IN_EXPORT_H cmGetUnit(Word UnitCode , UnicodeString *buf);
PRE_EXPORT_H void IN_EXPORT_H cmGetNewUnit(Word GreatCode, Word MeasUnit, UnicodeString *buf);
PRE_EXPORT_H Word IN_EXPORT_H cmGetGreatness(Word GreatnessCode , UnicodeString *buf);
PRE_EXPORT_H Bool IN_EXPORT_H cmDrawPoint(Pixel x, Pixel y, sObjInfo *Obj, Bool IsComponent);
PRE_EXPORT_H void IN_EXPORT_H cmGetNearestPorts(SLong MetX, SLong MetY, Word label, sNearInfo *arr, Word *num);
PRE_EXPORT_H void IN_EXPORT_H cmGetNearestObjectsMC(SLong MetX, SLong MetY, Word label, sNearInfo *arr, Word *num, eMC_FamilyType FatherObjLabel);

PRE_EXPORT_H Bool IN_EXPORT_H cmPortSearchFirst(UnicodeString *Match, sNearInfo *Obj);
PRE_EXPORT_H Bool IN_EXPORT_H cmPortSearchLast(UnicodeString *Match, sNearInfo *Obj);
PRE_EXPORT_H Bool IN_EXPORT_H cmPortSearchNext( sNearInfo *Obj);
PRE_EXPORT_H Bool IN_EXPORT_H cmPortSearchPrevious(sNearInfo *Obj);

PRE_EXPORT_H Bool IN_EXPORT_H cmORASearchFirst(UnicodeString *Match, sNearInfo *Obj);
PRE_EXPORT_H Bool IN_EXPORT_H cmORASearchLast(UnicodeString *Match, sNearInfo *Obj);
PRE_EXPORT_H Bool IN_EXPORT_H cmORASearchNext( sNearInfo *Obj);
PRE_EXPORT_H Bool IN_EXPORT_H cmORASearchPrevious(sNearInfo *Obj);

PRE_EXPORT_H Bool	IN_EXPORT_H cmMCInitObjList(eMC_FamilyType Type);
PRE_EXPORT_H Bool	IN_EXPORT_H cmMcNearestAvailable(eMC_FamilyType Type);
PRE_EXPORT_H Bool	IN_EXPORT_H cmPortInfoNearestAvailable(void);
PRE_EXPORT_H Bool	IN_EXPORT_H cmPortInfoInitNearestObjList(void);
PRE_EXPORT_H Word	IN_EXPORT_H cmGetTotalNumOfNearestObj(void);
PRE_EXPORT_H Word	IN_EXPORT_H cmGetNearestObjLabel(SWord index);

typedef struct
{
	Word	CdgNum;
	Word	ObjLabel;
	Word	AttrLabel;
	SLong	AttrValue;
	Byte	CartographicLevel;
} sGenericNearestListItem;

typedef PRE_EXPORT_H Bool (IN_EXPORT_H * NearestFilter)(sObjInfo *object);
PRE_EXPORT_H void IN_EXPORT_H cmSetNearestSearchLevel(SWord Level);
PRE_EXPORT_H void IN_EXPORT_H cmSetNearestSearchLimit(Double Limit );
PRE_EXPORT_H void IN_EXPORT_H cmSetNearestFilterAttribute(SWord AttrLabel,SLong AttrValue);
PRE_EXPORT_H void IN_EXPORT_H cmGetNearestObjects(SLong MetX, SLong MetY, Word label, sNearestInfo *arr, Word *num);
PRE_EXPORT_H void IN_EXPORT_H cmGetNearestMultiMediaObjects(SLong MetX, SLong MetY, Word Label,Bool AllObj,Byte MMContent, sNearestInfo *Arr, Word *num);

PRE_EXPORT_H void IN_EXPORT_H cmSetNearestFilterFunction(NearestFilter filter);
PRE_EXPORT_H void IN_EXPORT_H cmGetNearestFilterFunction(NearestFilter *filter);

PRE_EXPORT_H Bool IN_EXPORT_H cmNearestSearchAvailable(void);

PRE_EXPORT_H Bool IN_EXPORT_H cmGenericNearestAvailable(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmInitGenericNearestObjList(void);
PRE_EXPORT_H Word IN_EXPORT_H cmGetTotalNumOfGenericNearestObj(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetGenericNearestObjInfo(SWord index,sGenericNearestListItem *Info);

PRE_EXPORT_H Bool IN_EXPORT_H cmIsRestrictedArea(Word Label);
PRE_EXPORT_H Bool IN_EXPORT_H cmResHasCentralSymbol(sObjInfo *InfoArr);
PRE_EXPORT_H Bool IN_EXPORT_H cmObjectHasSymbol(sObjInfo *Obj);

PRE_EXPORT_H Bool IN_EXPORT_H cmIsResArea(Word Label);
PRE_EXPORT_H Bool IN_EXPORT_H cmFindResArea(Pixel x, Pixel y);

PRE_EXPORT_H void IN_EXPORT_H cmDrawROADPTStem(Pixel x, Pixel y, sObjInfo *Obj);
PRE_EXPORT_H void IN_EXPORT_H cmEnableROADPTNames(Bool flag);

PRE_EXPORT_H void IN_EXPORT_H cmDrawQTArea(sObjInfo *area);

typedef enum neAlarmRiskType
{
	eART_ALARM_ON_HIGH_RISK=0,
	eART_ALARM_ON_MEDIUM_RISK,
	eART_ALARM_ON_LOW_RISK
}eAlarmRiskType;

PRE_EXPORT_H Bool IN_EXPORT_H cmSetTerrainAwareness(Bool OnOff);
PRE_EXPORT_H void IN_EXPORT_H cmSetTerrainAwarenessFixAltitude(SLong fixA,Bool fixV);
PRE_EXPORT_H Bool IN_EXPORT_H cmSetConsiderVEROBS4TerrainAwareness(Bool OnOff);
PRE_EXPORT_H void IN_EXPORT_H cmSetOCLimits(SLong ocllU3,SLong oclmU3,SLong oclhU3,SLong ocllO3,SLong oclmO3,SLong oclhO3);
PRE_EXPORT_H void IN_EXPORT_H cmGetOCLimits(SLong* ocllU3,SLong* oclmU3,SLong* oclhU3,SLong* ocllO3,SLong* oclmO3,SLong* oclhO3);
PRE_EXPORT_H void IN_EXPORT_H cmRestoreOCDefaultLimits( void );
PRE_EXPORT_H Long IN_EXPORT_H cmSetVerticalObstructionRay( Long meters );

PRE_EXPORT_H eAlarmRiskType IN_EXPORT_H cmSetTAAlarmRiskType(eAlarmRiskType art);
PRE_EXPORT_H void			IN_EXPORT_H cmSetTAAlarmDistance(Long d);
PRE_EXPORT_H void			IN_EXPORT_H cmSetTAAlarmAngle(Long a);
PRE_EXPORT_H Bool			IN_EXPORT_H cmTAAlarmCheck(SLong fLat,SLong fLon,Long t);
PRE_EXPORT_H Bool			IN_EXPORT_H cmSetTAAlarmDebugMode(Bool mode);
PRE_EXPORT_H void			IN_EXPORT_H cmGetTAAlarmSettings(eAlarmRiskType* rType,Long* dist,Long* angle);


/* -------------------------------------------------------------- */
/* -------------    ALPHA LINK ( LOCATE ) ----------------------- */
/* -------------------------------------------------------------- */

#define AL_FORWARD   1           /* Alpha link search dir */
#define AL_EXACT     0
#define AL_BACKWARD -1

#define DB_NO_ERROR        0x00
#define OBJECT_NOT_FOUND   0x80
#define NO_MORE_OBJECTS    0x81


typedef struct 
{
	sObjInfo ObjInfo;
	SLong Lat;
	SLong Lon;
	Word LinkID;
	Word Level;
} sLocateInfo;


/* Test or Draw Function Type */
typedef PRE_EXPORT_H Bool (IN_EXPORT_H * LIFilter)(sLocateInfo *object, UnicodeString *Result);
PRE_EXPORT_H void IN_EXPORT_H cmLISetFilterFunction(LIFilter filter);
PRE_EXPORT_H void IN_EXPORT_H cmLIGetFilterFunction(LIFilter *filter);


PRE_EXPORT_H int IN_EXPORT_H cmLIstrcmp ( UnicodeString * src, UnicodeString * dst );
PRE_EXPORT_H int IN_EXPORT_H cmLIstrncmp ( UnicodeString * src, UnicodeString * dst, Word Count );
PRE_EXPORT_H SWord IN_EXPORT_H cmLIGetCloseChar ( const Word ch , SWord direction);

PRE_EXPORT_H Bool IN_EXPORT_H cmLIFindResultWithMultiWord(Bool Flag);
PRE_EXPORT_H Bool IN_EXPORT_H cmLIExtensionAvailable(void);
PRE_EXPORT_H Word IN_EXPORT_H cmLIIndexAvailable(Word ObjLabel, Word AttrLabel, Word *CdgArray);
PRE_EXPORT_H Bool IN_EXPORT_H cmLIFindInCdg(Word ObjLabel, Word AttrLabel, UnicodeString *Match, SByte SearchMode, sLocateInfo *Obj, UnicodeString *Result, Word cdg);
PRE_EXPORT_H Bool IN_EXPORT_H cmLIFind(Word ObjLabel, Word AttrLabel, UnicodeString *Match, SByte SearchMode, sLocateInfo *Obj, UnicodeString *Result);
PRE_EXPORT_H Long IN_EXPORT_H cmLIGetIndexSize(Word ObjLabel, Word AttrLabel);
PRE_EXPORT_H Bool IN_EXPORT_H cmLIGetObjAtIndexInCdg(Word ObjLabel, Word AttrLabel, Long Index, sLocateInfo *Obj, UnicodeString *Result, Word Cdg);

PRE_EXPORT_H Bool IN_EXPORT_H cmLIGetObjAtIndex(Word ObjLabel, Word AttrLabel, Long Index, sLocateInfo *Obj, UnicodeString *Result);
PRE_EXPORT_H Bool IN_EXPORT_H cmLIGetNextObj( sLocateInfo *Obj, UnicodeString *Result);
PRE_EXPORT_H Bool IN_EXPORT_H cmLIGetPreviousObj( sLocateInfo *Obj, UnicodeString *Result);

// Used to show some info to the user while waiting
typedef PRE_EXPORT_H Bool (IN_EXPORT_H * LIFindCallBack)(void);
PRE_EXPORT_H void IN_EXPORT_H cmSetLIFindCallBack(LIFindCallBack callback);

typedef enum
{
	eFM_Normal,
	eFM_RemapAccents
}eFindMode;

PRE_EXPORT_H void IN_EXPORT_H cmLISetFindMode(eFindMode mode);
PRE_EXPORT_H eFindMode IN_EXPORT_H cmLIGetFindMode(void);
PRE_EXPORT_H void IN_EXPORT_H cmLIRemapAccents(UnicodeString *str);

PRE_EXPORT_H Long IN_EXPORT_H cmLIGetCurrentObjIndexInCdg(Word Cdg);
PRE_EXPORT_H Long IN_EXPORT_H cmLIGetCurrentObjIndex(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmLISetCurrentObjIndex( Long Index );

PRE_EXPORT_H Word IN_EXPORT_H cmLIGetCurrentCdg(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmLISetCurrentCdg(Word cdg);

PRE_EXPORT_H Word IN_EXPORT_H cmLIInitIndexList(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmLIGetIndexItem(Word index, Word *ObjLabel, Word *AttrLabel);

PRE_EXPORT_H void IN_EXPORT_H cmDraw2DOObject(sLocateInfo *obj,Word Mode);

typedef enum
{
	eAsciiExtendedCodePage,
	eCmapCodePage

}eCodePage;

PRE_EXPORT_H eCodePage IN_EXPORT_H cmGetCodePageBasedOnCdg(Word cdg);
PRE_EXPORT_H eCodePage IN_EXPORT_H cmSetCodePage(eCodePage cp);
PRE_EXPORT_H UnicodeString IN_EXPORT_H  cmToUpper(UnicodeString c);
PRE_EXPORT_H UnicodeString IN_EXPORT_H  cmToLower(UnicodeString c);
PRE_EXPORT_H Bool IN_EXPORT_H  cmIsAlpha(Byte c);


/* Segment Distance Parameters */

PRE_EXPORT_H void IN_EXPORT_H cmPoint2CellID(SLong Lat, SLong Lon, SWord Level, Word CdgNum,sCellID *ID);


#define POINT_ON_SEGMENT	0
#define POINT_ON_A_SIDE		1
#define POINT_ON_B_SIDE		2

PRE_EXPORT_H Word IN_EXPORT_H cmPointToSegmentDistance(SLong PX, SLong PY,SLong AX, SLong AY, SLong BX, SLong BY, Long *Dist, SLong *QX, SLong *QY);
PRE_EXPORT_H Word IN_EXPORT_H cmPointToSegmentDistanceEx(SLong PX, SLong PY,SLong RX, SLong RY,SLong AX, SLong AY, SLong BX, SLong BY, Long *Dist, SLong *QX, SLong *QY);

#ifdef USE_GRAPH_NODE
/*-------------------------------------------------------------------------------*/
/*-  External interfaces  -------------------------------------------------------*/
/*-------------------------------------------------------------------------------*/


#define SP_SEARCH_DIJKSTRA			0x00000001
#define SP_SEARCH_BIDI				0x00000002
#define SP_SEARCH_HEURISTIC			0x00000004
#define SP_SEARCH_DISTANCE			0x00000008
#define SP_SEARCH_TIME				0x00000010
#define SP_SHOW_DEBUG				0x00000020
#define SP_AVOID_TOLL				0x00000040
#define SP_AVOID_U_TURN				0x00000080
#define SP_AVOID_FERRY				0x00000100
#define SP_CONSIDER_WALKWAYS		0x00000200
#define SP_USE_TMC_DATA				0x00000400
#define SP_AVOID_FREEWAYS			0x00000800
#define SP_PREFER_MAJOR_ROADS		0x00001000
#define SP_APPLY_TRUCK_RESTRICTION	0x00002000
#define SP_APPLY_BUARE_RESTRICTION	0x00004000
#define SP_APPLY_TURN_COST			0x00008000
#define SP_USE_FIXED_SPEED			0x00010000
#define SP_AVOID_UNPAVED			0x00020000
#define SP_DISCARD_WALKWAYS			0x00040000
#define SP_PREFER_MAJOR_ROADS_TRUCK	0x00080000


typedef enum neGNM_Info
{
	eGNM_HasManeuvers=0,
	eGNM_HasSignposts,
	eGNM_HasHouseNumbers,
	eGNM_HasVehicleTypeRestrictions,
	eGNM_IsOneWay,
	eGNM_HasSGID,
	eGNM_IsFake,
	eGNM_IsNoPtr,
	eGNM_HasPtrOffsetAfterNode,
	eGNM_IsInBadCondition,
	eGNM_IsToll,
	eGNM_IsInConstruction,
	eGNM_HasTurningCost,
	eGNM_IsBlockedAtStart,
	eGNM_IsBlockedAtEnd,
	eGNM_GeomType,
	eGNM_PointerType
}eGNM_Info;


PRE_EXPORT_H SLong IN_EXPORT_H cmGNMaskExtractInfo(Long Mask,Word CdgNum,eGNM_Info infoRequired);

typedef enum neGNE_Info
{
	eGNE_IsBadForTruck=0,
	eGNE_IsInBuiltUpArea
}eGNE_Info;

PRE_EXPORT_H SLong IN_EXPORT_H cmGNExtFastDBExtractInfo(Long ExtFastDB,Word CdgNum,eGNE_Info infoRequired);


PRE_EXPORT_H SWord IN_EXPORT_H cmCalcSP(sNodePointer *StartNode, sNodePointer *EndNode,sPathNode** Path,Long SearchMode);
PRE_EXPORT_H SWord IN_EXPORT_H cmCalcSPEx(sNodePointerEx *StartNode,
										  sNodePointerEx *EndNode,
										  sPathNode** Path,
										  Long SearchMode);

PRE_EXPORT_H SLong IN_EXPORT_H cmCalcSP_VIA(sNodePointerEx* ViaPoints, SWord ViaPointsNum,sPathNode** Path, Long SearchMode);
PRE_EXPORT_H SLong IN_EXPORT_H cmCalcSP_FAST(sNodePointerEx* StartPoint,sNodePointerEx* EndPoint,sPathNode **CurrentPath,Long CurrentPathLen, Long ArcOffset, Long Distance, Long SearchMode);

// Return values for cmCalcSP_VIA2
#define SP_VIA2_OK			0x00
#define SP_VIA2_FAIL_1		0x01
#define SP_VIA2_FAIL_2		0x02
PRE_EXPORT_H Word  IN_EXPORT_H cmCalcSP_VIA2(sNodePointerEx* ViaPoints, SWord ViaPointsNum,sPathNode** Path1,SLong *Path1Len,sPathNode** Path2,SLong *Path2Len, Long SearchMode1, Long SearchMode2);

PRE_EXPORT_H void IN_EXPORT_H cmDrawSP(sPathNode *Path, SLong PathLen);
PRE_EXPORT_H void IN_EXPORT_H cmReleaseSP(sPathNode *Path, SLong PathLen);
PRE_EXPORT_H void IN_EXPORT_H cmReleaseSPEx(sPathNode *Path, SLong PathLen,Bool KeepCellCache);

#define DRAWSP_CALLBACK_MODE_BEFORE	0
#define DRAWSP_CALLBACK_MODE_AFTER	1

PRE_EXPORT_H void IN_EXPORT_H cmSetDrawSPCallBack( void (*DSP)(Word Mode) , Word Mode );
PRE_EXPORT_H void IN_EXPORT_H cmSetHeuristics( Float Space, Float Time );
PRE_EXPORT_H void IN_EXPORT_H cmGetHeuristics( Float *Space, Float *Time );
PRE_EXPORT_H void IN_EXPORT_H cmIncreaseHeuristics( void );
PRE_EXPORT_H void IN_EXPORT_H cmResetHeuristics( void );


PRE_EXPORT_H void IN_EXPORT_H cmSetDeltaT( SWord DeltaT );
PRE_EXPORT_H void IN_EXPORT_H cmSetNetClassRadius( Long MinStartRadius, Long MaxStartRadius,Long MinEndRadius, Long MaxEndRadius);

PRE_EXPORT_H void IN_EXPORT_H cmSetCalcSPTimeoutCallBack( Bool (*CSPTO)(void) );

typedef struct 
{
	SWord Percent;
	SLong Arcs;
}sCalcCallBackEx;

PRE_EXPORT_H void IN_EXPORT_H cmSetCalcSPTimeoutCallBackEx( Bool (*CSPTO)(sCalcCallBackEx* info) );

typedef enum
{
	eIsArcOneWay

}eFDBMessage;

PRE_EXPORT_H SWord IN_EXPORT_H cmGetInfoField(sObjInfo *Obj,eFDBMessage msg);

PRE_EXPORT_H void IN_EXPORT_H cmSetAvoided(sNodePointerEx* Avoid, SWord NumOfAvoid);
PRE_EXPORT_H void IN_EXPORT_H cmAddToAvoided(sNodePointerEx* Avoid,Bool IsDetour);
PRE_EXPORT_H void IN_EXPORT_H cmRemoveFromAvoided(sNodePointerEx* Avoid);
PRE_EXPORT_H Bool IN_EXPORT_H cmIsAvoided(Long Node1,Long Node2, Bool *IsDetour);

#define FIND_CLOSEST_POINT				0x01
#define FIND_CLOSEST_NODE				0x02
#define FIND_CLOSEST_CALC_LEN			0x04
#define FIND_CLOSEST_DISCARD_WALKWAY	0x08
#define FIND_CLOSEST_USE_DIRECTION		0x10
#define FIND_CLOSEST_HOUSE_NUMBER		0x20

PRE_EXPORT_H SLong IN_EXPORT_H  cmFindClosestLineM(SLong mx, SLong my, sObjInfo *Arr, Word *Num, Word Mode);
PRE_EXPORT_H SLong IN_EXPORT_H  cmFindClosestLineMEx(SLong mx, SLong my, SLong mx2, SLong my2, sObjInfo *Arr, Word *Num, Word Mode);
PRE_EXPORT_H SLong IN_EXPORT_H  cmFindClosestLineMExFilt(SLong mx, SLong my,SLong mx2, SLong my2, sObjInfo *Arr, Word *Num, Word Mode,sObjInfo *Filter);
PRE_EXPORT_H void IN_EXPORT_H cmCalcFiltObjFromPath(sPathNode *Path, SLong PathIndex, sObjInfo *obj);

PRE_EXPORT_H Bool IN_EXPORT_H cmGetArcGeometry(sObjInfo *Obj,sPathArcGeometry *ArcGeometry);

PRE_EXPORT_H Bool IN_EXPORT_H cmGetPathArcGeometry(sPathNode *Path,SLong Index,sPathArcGeometry *ArcGeometry);
PRE_EXPORT_H void IN_EXPORT_H cmReleaseArcGeometry(sPathArcGeometry *ArcGeometry);

PRE_EXPORT_H Bool IN_EXPORT_H cmGetExitingArcs(sPathNode *Path, SLong Index, sNodeExitingArcs *ExitingArcs);
PRE_EXPORT_H void IN_EXPORT_H cmReleaseExitingArcs(sNodeExitingArcs *ExitingArcs);

PRE_EXPORT_H void IN_EXPORT_H cmGetRoadCrossingArcs(sObjInfo* Arc, sObjInfo *Arr1, Word *Num1, sObjInfo *Arr2, Word *Num2);


PRE_EXPORT_H SLong IN_EXPORT_H cmGetPathArcSpeed(sPathNode* Path, SLong Index, Bool *FromTMC );

PRE_EXPORT_H void IN_EXPORT_H cmInitGetRandomNode( Long val );
PRE_EXPORT_H Bool IN_EXPORT_H cmGetRandomNode( sNodePointerEx *Node ,SLong *Lat, SLong *Lon );

#define TMC_SPEED_VALUE 0
#define TMC_SPEED_PERCENTAGE 1

typedef struct 
{
	DrawStyleTypeExt TMCStyle;
	SByte TMCAlpha;
	SByte TMCSpeedType;
	SWord TMCSpeed;
}sTMCInfo;


PRE_EXPORT_H void IN_EXPORT_H cmTMCDisplayEnable(Bool flag);
PRE_EXPORT_H Bool IN_EXPORT_H cmTMCAddSentence(char *InputSentence, sTMCInfo *info, Long *TMC_Location, Word *TMC_Ext, SLong *MetX, SLong *MetY, Long *DBPtr, SByte *OffsX, SByte *OffsY);
PRE_EXPORT_H Bool IN_EXPORT_H cmTMCRemoveSentence(char *InputSentence);
PRE_EXPORT_H Bool IN_EXPORT_H cmTMCCacheIndexes(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetTMCPointCoordinate(String *LocationID, Bool DirectionForward, SLong *MetX, SLong *MetY, Long *DBPtr, Long *NodePtr1, Long *NodePtr2, SByte *OffsX, SByte *OffsY);
PRE_EXPORT_H SWord IN_EXPORT_H cmSetTMCMinSpeedThreshold(SWord MinSpeed);

PRE_EXPORT_H Bool IN_EXPORT_H cmIsTMCLocationOnPath(String *LocationID,sPathNode *Path, SLong PathLen, SLong StartIndex);
PRE_EXPORT_H Bool IN_EXPORT_H cmFindFirstTMCOnPath(sPathNode *Path, SLong PathLen, SLong Start, SLong *PathIndexStart, SLong *PathIndexEnd, char *Sentence);
PRE_EXPORT_H Bool IN_EXPORT_H cmFindNextTMCOnPath(sPathNode *Path, SLong PathLen, SLong *PathIndexStart, SLong *PathIndexEnd, char *Sentence);
PRE_EXPORT_H Pixel IN_EXPORT_H cmGetTMCOffsetFromPercent(Word zoomStep,Byte Percent);
/* -------------------------------------------------------------------------------------- */
/* -------------    ALPHA LINK ( LOCATE ) EXTENSION FOR GEOCODING ----------------------- */
/* -------------------------------------------------------------------------------------- */

#define GN_GC_NOT_APPLICABLE	0
#define GN_GC_EVEN				1
#define GN_GC_ODD				2
#define GN_GC_MIXED				3
#define GN_GC_IRREGULAR			4

typedef struct GCPoolElement
{
	Byte				Index;
	Byte				Type[2];
	Long				rMin[2];
	Long				rMax[2];
	SWord				IrregularCount[2];
	Long				*Irregular[2];
	sObjInfo			Obj;
	sPathArcGeometry	ArcGeometry;
}sGCPoolElement;

typedef struct GCPoolElementNode
{
	sGCPoolElement		GCElement;
	struct GCPoolElementNode *Next;
}sGCPoolElementNode;

typedef struct GCPool
{
	Long	NumOfGCElements;	// Output
	sGCPoolElement *GCElements;	// Output
}sGCPool;

PRE_EXPORT_H Bool IN_EXPORT_H cmLIFindGC(Word ObjLabel, Word AttrLabel, UnicodeString *Match, SByte aSearchMode, UnicodeString *Result,sGCPool* pool);

PRE_EXPORT_H Bool IN_EXPORT_H cmLIFindGCPosition(SLong MatchNumber, sGCPool *Pool, SWord Index, SLong *Lat, SLong *Lon, SWord* Side, SWord *Orientation, sObjInfo *Obj);
PRE_EXPORT_H void IN_EXPORT_H cmLIReleaseGCPool(sGCPool *pool);

#define DRAW_GC_CALC_SUBGRAPH_RECT	0
#define DRAW_GC_DRAW_SUBGRAPH		1


PRE_EXPORT_H Bool IN_EXPORT_H cmLIDrawGC(UnicodeString *Match, SWord SubGraph,  SWord Mode, sMercatorRectangle *aRect);


#endif /* USE_GRAPH_NODE */



typedef struct SPDebug
{
	/* Memory */
	Long MallocResult;
	
	Long MallocAVL;
	Long MallocFIB;
	Long MallocZoneQT;

	Long MaxMallocAVL;
	Long MaxMallocFIB;
	Long MaxMallocZoneQT;

	Long CellCount;

	Long MaxOpenListElements;
	Long MaxClosedListElements;

	Long ReadDirectCount;
	Long ReadDirect;
	Long ReadBackCount;
	Long ReadBack;
	Long ReadOther;
	Long ReadNode;
	Long ReadTotal;

}sSPDebug;

PRE_EXPORT_H void IN_EXPORT_H cmGetSPDebug(sSPDebug *dbg);


PRE_EXPORT_H void IN_EXPORT_H CF95_DrawStr2(Word Angle,UnicodeString *str);
PRE_EXPORT_H void IN_EXPORT_H CF95_DrawStr3(Word Angle, UnicodeString *str);
PRE_EXPORT_H void IN_EXPORT_H cmVerifyRoadIndex(void);

/* MULTIMEDIA DEFINITIONS */
typedef enum
{
	eIMAGE_PNG=1,			/* 1 */
    eIMAGE_JPG_PALETTE,		/* 2 */
	eIMAGE_JPG,				/* 3 */		
	eIMAGE_WL,				/* 4 */
	eAUDIO_MP3				/* 5 */
} eMultimediaTypes;

#define MAX_DESCRIPTION_LEN 40

typedef struct nsMultimediaElement
{
	Byte type;
	UnicodeString description[MAX_DESCRIPTION_LEN];
	Long mmEntityPtr; //address + dimType + DimDescription + 4 byte for len of len
}sMultimediaElement;

Long CF95_GetFileMp3Size(sMultimediaElement* mmElement,sAttrInfoExp* attrinfo);
void CF95_SetCartridgePosition(Long Ptr);
void CF95_GetSoundBlock(Byte* pHeaderMp3, Word dim);

PRE_EXPORT_H Byte IN_EXPORT_H cmGetMultimediaElementType(Long Ptr);
PRE_EXPORT_H Byte IN_EXPORT_H cmGetMultimediaElementDescription(Long Ptr, UnicodeString* buff);
PRE_EXPORT_H Bool IN_EXPORT_H cmReadNextBinListElement(sAttrInfoExp * attrinfo);//, Byte currentElementInList);
PRE_EXPORT_H void IN_EXPORT_H cmGetMultimediaElement(sAttrInfoExp* attrinfo, sMultimediaElement* mmElement);

#ifdef USE_GRAPH_NODE

typedef enum neFA_MODE
{
	eFA_MODE_START=0,
	eFA_MODE_CONTINUE
}eFA_MODE;

typedef enum nsInitMode
{
	eINIT_ALL,
	eINIT_FIND_ADDRESS_ELEMENT_COUNTER,
	eINIT_FILTER_STRINGS,
	eINIT_ROAD_NAME
}eInitMode;

typedef struct nsLocateEntry
{
	Long id;
	struct nsLocateEntry* next;
}sLocateEntry;

typedef struct nsFindAddressTreeElem
{
	UnicodeString* state;
	UnicodeString* province;
	UnicodeString* pref;
	UnicodeString* objn;
	UnicodeString* suff;
	UnicodeString* zip;
	UnicodeString* sol;
	UnicodeString* loc;
	sGCPoolElement* gcElem;
	sLocateEntry* eList;
	Long wID;
	struct nsFindAddressTreeElem* left;
	struct nsFindAddressTreeElem* right;
}sFindAddressTreeElem;

typedef struct nsFindAddressPoolElem
{
	sGCPoolElement* gcElem;
	struct nsFindAddressPoolElem* next;
}sFindAddressPoolElem;

typedef struct nsFindIntersectionTreeElem
{
	SLong metX;
	SLong metY;
	sObjInfo road1;
	sObjInfo road2;
	
	UnicodeString* state1;
	UnicodeString* province1;
	UnicodeString* pref1;
	UnicodeString* objn1;
	UnicodeString* suff1;
	UnicodeString* zipCode1;
	UnicodeString* cLocBuff1;

	UnicodeString* state2;
	UnicodeString* province2;
	UnicodeString* pref2;
	UnicodeString* objn2;
	UnicodeString* suff2;
	UnicodeString* zipCode2;
	UnicodeString* cLocBuff2;

	struct nsFindIntersectionTreeElem* left;
	struct nsFindIntersectionTreeElem* right;
}sFindIntersectionTreeElem;

typedef struct nsFindPoiElem
{
	sObjInfo poiObj;
	Long indexPosition;
	Long distInCell;
	Double distInMiles;
	Bool usePreciseDistance;
	struct nsFindPoiElem* left;
	struct nsFindPoiElem* right;
}sFindPoiElem;

typedef enum neFindPoiByNameResult
{
	eFPN_NO_ERROR=0,
	eFPN_INTERRUPTED,
	eFPN_FOUND_NOTHING,
	eFPN_ERROR
}eFindPoiByNameResult;

typedef struct nsHouseNumberElement
{
	sGCPoolElement hnElem;
	struct nsHouseNumberElement* next;
}sHouseNumberElement;

typedef enum neLocParserSol
{
	eLOCPARSER_LEFT_RIGHT=0,
	eLOCPARSER_LEFT,
	eLOCPARSER_RIGHT,
	eLOCPARSER_ALL,
	eLOCPARSER_MOST_IMPORTANT
}eLocParserSol;

typedef enum neLocParserWorkingMode
{
	eLOCPARSER_SIMPLE=0,
	eLOCPARSER_ADVANCED
}eLocParserWorkingMode;

typedef enum neResearchResult
{
	eFA_RESEARCH_FINISHED=0,
	eFA_RESEARCH_TO_BE_CONTINUED,
	eFA_RESEARCH_FOUND_NOTHING
}eResearchResult;

typedef enum neFindIntersectionResult
{
	eFI_ROADINTERSECTIONS_FOUND=0,
	eFI_ROADINTERSECTIONS_NOT_FOUND,
	eFI_RESEARCH_INTERRUPTED
}eFindIntersectionResult;


typedef enum neGraphInfoType
{
	eGIT_Unknown=0,
	eGIT_FAEuropean,
	eGIT_FAAmerican
}eGraphInfoType;

typedef enum neGIT_Info
{
	eFA_Type=0
}eGIT_Info;

typedef enum neFindZipMode
{
	eZIP_EXACT=0,
	eZIP_GET_FIRST,
	eZIP_GET_LAST
}eFindZipMode;


PRE_EXPORT_H eGraphInfoType IN_EXPORT_H cmGetGraphInfoType(Word cdg,eGIT_Info i);

typedef PRE_EXPORT_H Bool (IN_EXPORT_H * FindAddressCallBack)(void);
typedef PRE_EXPORT_H Bool (IN_EXPORT_H * FindIntersectionCallBack)(void);
typedef PRE_EXPORT_H Bool (IN_EXPORT_H * FindPOIByNameCallBack)(void);
typedef PRE_EXPORT_H Bool (IN_EXPORT_H * FindPOIByNameCobpoiFilterCallBack)(Long cobpoiVal);

PRE_EXPORT_H void IN_EXPORT_H cmSetFindAddressCallBack(FindAddressCallBack funct);

PRE_EXPORT_H Bool IN_EXPORT_H cmLIGetObjAtIndexInCdg_FA(Word ObjLabel, Word AttrLabel, Long Index, sLocateInfo *Obj, UnicodeString *Result, Word Cdg);
PRE_EXPORT_H Bool IN_EXPORT_H cmInitFindGC_FA(UnicodeString* localityName,UnicodeString* houseNumber,UnicodeString* zipCode,
																							UnicodeString* insertedRoadName,UnicodeString* searchRoadName,sLocateInfo* locInfo,
												Long* idFirstIndex,eInitMode mode);

PRE_EXPORT_H void IN_EXPORT_H cmGetZipInformation(UnicodeString* zipIn,
													UnicodeString* state,
													UnicodeString* zipOut);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetMunicipalityListBasedOnZipCode(	UnicodeString* state,
																	UnicodeString* zipCode,
																	UnicodeString* regList,
																	UnicodeString* munList,
																	Long* maxNumberOfMun,
																	Long maxSingleMunRegLen);

PRE_EXPORT_H Bool IN_EXPORT_H cmInitZipCodeIndex(void);

PRE_EXPORT_H eResearchResult IN_EXPORT_H cmLIFindGC_FA(UnicodeString* matchString,Long* startIndex,Long* endIndex,Word cdg,eFA_MODE mode);

PRE_EXPORT_H Long IN_EXPORT_H cmGetFindAddressElementsNum(void);
PRE_EXPORT_H sFindAddressTreeElem** IN_EXPORT_H cmGetFindAddressElements(void);
PRE_EXPORT_H void IN_EXPORT_H cmReleaseFindAddressElements(void);

PRE_EXPORT_H eLocParserWorkingMode IN_EXPORT_H cmLocalityParserSetWorkingMode(eLocParserWorkingMode workingMode);
PRE_EXPORT_H void IN_EXPORT_H cmLocalityParserInitEngine(UnicodeString* localityStringAttributeValue,eLocParserSol sol);
PRE_EXPORT_H Bool IN_EXPORT_H cmLocalityParserGetLocality(UnicodeString* buff);
PRE_EXPORT_H void IN_EXPORT_H cmLocalityParserResetEngine(void);

PRE_EXPORT_H void IN_EXPORT_H cmZipCodeParserInitEngine(UnicodeString* zipCodeStringAttributeValue);
PRE_EXPORT_H Bool IN_EXPORT_H cmZipCodeParserGetZips(UnicodeString* zipLeftSide, UnicodeString* zipRightSide,Bool* areEquals);
PRE_EXPORT_H void IN_EXPORT_H cmZipCodeParserResetEngine(void);
PRE_EXPORT_H Byte IN_EXPORT_H cmGetNumOfZipCode7(UnicodeString *buff);
PRE_EXPORT_H void IN_EXPORT_H cmGetZipCode7(UnicodeString *buff, Byte count, UnicodeString *outLeft, UnicodeString *outRight, Bool *Equal);


PRE_EXPORT_H void IN_EXPORT_H cmBlockMallocFreeBlocks(Long section);
PRE_EXPORT_H void* IN_EXPORT_H cmBlockMalloc(Long size,Long section);

PRE_EXPORT_H Bool IN_EXPORT_H cmLIFindGCPosition_FA(SLong MatchNumber, sFindAddressTreeElem* elem, SLong *Lat, SLong *Lon, SWord* Side, SWord *Orientation, sObjInfo *Obj);

PRE_EXPORT_H void IN_EXPORT_H cmMaxFindAddressElementsSetMax(Long numOfMaxFindAddressElements);
PRE_EXPORT_H Long IN_EXPORT_H cmMaxFindAddressElementsGetMax(void);

PRE_EXPORT_H Long IN_EXPORT_H cmGetNumberOfExaminedArcs(void);
PRE_EXPORT_H SWord IN_EXPORT_H cmIsInPoolRange(SLong MatchNumber, sGCPoolElement *element, SWord *percent, SWord *Side);

extern FindAddressCallBack FA_CallBack;


extern FindIntersectionCallBack FI_CallBack;
PRE_EXPORT_H Bool IN_EXPORT_H cmInitFindRoadIntersection(UnicodeString* firstRoad, UnicodeString* secondRoad,Long maxNumOfInters);
PRE_EXPORT_H Bool IN_EXPORT_H cmInitFindRoadIntersection_EU(UnicodeString* firstRoad, UnicodeString* secondRoad, UnicodeString* zipCode,Long maxNumOfInters);
PRE_EXPORT_H void IN_EXPORT_H cmSetFindIntersectionCallback(FindIntersectionCallBack funct);
PRE_EXPORT_H eFindIntersectionResult IN_EXPORT_H cmFindRoadIntersections(void);
PRE_EXPORT_H Long IN_EXPORT_H cmGetRoadIntersectionNum(void);
PRE_EXPORT_H sFindIntersectionTreeElem** IN_EXPORT_H cmGetRoadIntersections(void);
PRE_EXPORT_H void IN_EXPORT_H cmReleaseRoadIntersections(void);


PRE_EXPORT_H void IN_EXPORT_H cmInitFindHouseNumberPool(void);
PRE_EXPORT_H void IN_EXPORT_H cmFindHouseNumberPool(sFindAddressTreeElem* elem);
PRE_EXPORT_H Long IN_EXPORT_H cmGetHouseNumberPoolNum(void);
PRE_EXPORT_H sHouseNumberElement* IN_EXPORT_H cmGetHouseNumberPool(void);
PRE_EXPORT_H void IN_EXPORT_H cmReleaseHouseNumberPool(void);

PRE_EXPORT_H Bool IN_EXPORT_H cmSetRdNoHNAccepted(Bool OnOff);

extern FindPOIByNameCallBack FPN_CallBack;
Bool CF95_InitializePOILocateIndex(SLong* lastObj);

PRE_EXPORT_H void IN_EXPORT_H cmFindPOIByNameSetCallBack(FindPOIByNameCallBack funct);
PRE_EXPORT_H eFindPoiByNameResult IN_EXPORT_H cmFindPOIByName(FindPOIByNameCobpoiFilterCallBack cpFilter,UnicodeString* matchString,SLong lat,SLong lon);
PRE_EXPORT_H void IN_EXPORT_H cmFindPOIByNameCollectElements();
PRE_EXPORT_H sFindPoiElem** IN_EXPORT_H cmFindPOIByNameGetElements();
PRE_EXPORT_H void IN_EXPORT_H cmFindPOIByNameReleaseElements();
PRE_EXPORT_H Long IN_EXPORT_H cmFindPOIByNameGetElementsNum();
PRE_EXPORT_H Long IN_EXPORT_H cmFindPOIByNameSetSearchRange(Long metersRange);
PRE_EXPORT_H Word IN_EXPORT_H cmFindPOIByNameSetMaxNumberOfPOIsWanted(Word maxVal);

/*Avoid Arcs Management Start*/

#define NUM_OF_WALKWAY_TABLE_ELEMENTS						5
#define NUM_OF_TOLL_TABLE_ELEMENTS							5
#define NUM_OF_HIGHWAY_TABLE_ELEMENTS						5
#define NUM_OF_FERRY_TABLE_ELEMENTS							5

typedef struct nsTimeMultiplier
{
	Long multiplier;
	Long divisor;
}sTimeMultiplier;

typedef struct nsSeDistance
{
	Long distMin;
	Long distMax;
}sSeDistance;

typedef struct nsDistanceTimeMultiplier
{
	sSeDistance distMinMax;
	sTimeMultiplier timeMult;
}sDistanceTimeMultiplier;

typedef enum neTypeOfRestriction
{
	eTOR_WALKWAY=0,
	eTOR_HIGHWAY,
	eTOR_TOLL,
	eTOR_FERRY
}eTypeOfRestriction;

PRE_EXPORT_H void IN_EXPORT_H cmSetAvoidRoadInfo(eTypeOfRestriction tor,sDistanceTimeMultiplier* dtm);
PRE_EXPORT_H void IN_EXPORT_H cmGetAvoidRoadInfo(eTypeOfRestriction tor,sDistanceTimeMultiplier* dtm);
PRE_EXPORT_H void IN_EXPORT_H cmGetOutsideRangesTimeMultiplier(sTimeMultiplier* tm);
PRE_EXPORT_H void IN_EXPORT_H cmSetOutsideRangesTimeMultiplier(sTimeMultiplier* tm);


/*Avoid Arcs Management End*/


PRE_EXPORT_H void IN_EXPORT_H cmSetTurnCostDelayValues(Long st,Long ri,Long le,Long ut);


#endif	/* #ifdef USE_GRAPH_NODE */

/*#define CONNECTIVITY*/
#ifdef CONNECTIVITY
	#define WRITE_SUBGRAPHS				0x01
	#define WRITE_MAINGRAPH				0x02
	#define WRITE_SUSPICIUS				0x04
	#define IGNORE_NETCLASS_0			0x08
	#define IGNORE_NETCLASS_1			0x10
	#define IGNORE_NETCLASS_2			0x20
	#define IGNORE_NETCLASS_3			0x40
	#define IGNORE_NETCLASS_4			0x80
	#define IGNORE_NETCLASS_5			0x0100
	#define IGNORE_NETCLASS_6			0x0200
	#define DISPLAY_MULTICOLOR_SUBGRAPHS	0x01
	#define DISPLAY_REMOVE_NETCLASS_0		0x02
	#define DISPLAY_REMOVE_NETCLASS_1		0x04
	#define DISPLAY_REMOVE_NETCLASS_2		0x08
	#define DISPLAY_REMOVE_NETCLASS_3		0x10
	#define DISPLAY_REMOVE_NETCLASS_4		0x20
	#define DISPLAY_REMOVE_NETCLASS_5		0x40
	#define DISPLAY_REMOVE_NETCLASS_6		0x80
	#define DISPLAY_REMOVE_SUSPICIUS			0x100
	#define DISPLAY_REMOVE_SUSPICIUS_FERRIES	0x200

	PRE_EXPORT_H void IN_EXPORT_H cmDisplaySubGraphs(unsigned short displayFlags);
	PRE_EXPORT_H void IN_EXPORT_H cmbuildGraphConnectivityResult(unsigned short writeFlags);
#endif
PRE_EXPORT_H Word IN_EXPORT_H cmReadBitStream(sBitStreamValue *Stream, Byte *DataStream, Long MaxStreamLen);

PRE_EXPORT_H void IN_EXPORT_H  cmGetRoadRteNum(sObjInfo *Obj, UnicodeString *Str, Word MaxLen);

#ifdef __cplusplus
	}
#endif

#endif


