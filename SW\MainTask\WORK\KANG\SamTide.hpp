#ifndef _SAMTIDE_H_
#define	_SAMTIDE_H_

#ifdef  __cplusplus
extern "C" {
#endif

#include "EVERYTHI.h"
#include "LOCLIB.h"

//extern DWORD *pTideDbg;

time_t	GetTideSysTime();
IDX_entry*	GetStIndexData(int iStIndex);
TIDE_INDEX* GetTcdIndexData(int iIndex);
void	GetTcdRecordFileData(DWORD nOffset, BYTE *pBuff, int nRecordSize);
int		GetTimeBias(char *tz);
#ifdef  __cplusplus
}
#endif

void SamTideInitialize();
void SetTideSysTime(int nYear, int nMonth, int nDay, int nHour, int nMin);
int  GetSamTidalData(IDX_entry *pTideStation,int nUTCYear,int nUTCMonth,int nUTCDay,float *pData,int nPointPerHour,int nDepthUnit);
#endif
