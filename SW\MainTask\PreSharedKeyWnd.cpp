#include <stdio.h>
#include "PreSharedKeyWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Uart.hpp"
#include "Font.h"

#define PSK_WND_X_POS		10
#define PSK_WND_Y_POS		54

#define PSK_WND_W			580
#define PSK_WND_H			370

#define PSK_WND_ROW_H		37

#define PSK_WND_CAP_END_X_POS	(PSK_WND_X_POS + 270)
#define PSK_WND_CTRL_X_POS		(PSK_WND_X_POS + 20)
#define PSK_WND_CTRL_W			430
#define PSK_WND_CTRL_H			30

extern CDocMgr *g_pDocMgr;
extern cUART    *G_pUart3;

/*********************************************************************************************************/
// Name		: CPreSharedKeyWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CPreSharedKeyWnd::CPreSharedKeyWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	m_nFocus = 0;
	
	m_pNewPassword     = new CEditCtrl(pScreen);
	m_pConfirmPassword = new CEditCtrl(pScreen);

	m_pNewPassword->Create(	PSK_WND_CTRL_X_POS, 
							PSK_WND_Y_POS + PSK_WND_ROW_H*3 + (PSK_WND_ROW_H - PSK_WND_CTRL_H)/2, 
							PSK_WND_CTRL_W, 
							PSK_WND_CTRL_H, MAX_PRE_SHARED_KEY-1, 0, 1);
	
	m_pConfirmPassword->Create(	PSK_WND_CTRL_X_POS, 
								PSK_WND_Y_POS + PSK_WND_ROW_H*5 + (PSK_WND_ROW_H - PSK_WND_CTRL_H)/2, 
								PSK_WND_CTRL_W, 
								PSK_WND_CTRL_H, MAX_PRE_SHARED_KEY-1, 0, 1);

}

/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPreSharedKeyWnd::DrawWnd(BOOL bRedraw)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 2;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 4;
			break;
			
		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 4;
			break;
	}
	
    CWnd::DrawWnd(bRedraw);
	
	if( bRedraw )
	{
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		pUniCodeStr = (HWORD *)STR_PASSWORD_NEW[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);

		nXPos = PSK_WND_CTRL_X_POS;
		nYPos = PSK_WND_Y_POS + PSK_WND_ROW_H*2 + (PSK_WND_ROW_H - pFont->uHeight)/2 + nYOffset;
		
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		pUniCodeStr = (HWORD *)STR_PASSWORD_CONFIRM[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);

		nXPos = PSK_WND_CTRL_X_POS;
		nYPos = PSK_WND_Y_POS + PSK_WND_ROW_H*4 + (PSK_WND_ROW_H - pFont->uHeight)/2 + nYOffset;
		
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
	
		m_pScreen->SetFont(pOldFont);
		
		DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
		DrawButton(1, (BYTE *)FK_SAVE[nLangMode]);
		EraseButton(2);
		EraseButton(3);
	}

	switch( m_nFocus )
	{
		case FOCUS_PASSWORD_NEW:
			m_pNewPassword->SetFocus(1);
			m_pConfirmPassword->SetFocus(0);
			break;
			
		case FOCUS_PASSWORD_CONFIRM:
			m_pNewPassword->SetFocus(0);
			m_pConfirmPassword->SetFocus(1);
			break;
	}
	
	m_pNewPassword->DrawWnd();
	m_pConfirmPassword->DrawWnd();
}

/*********************************************************************************************************/
// Name		: OnCursorEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPreSharedKeyWnd::OnCursorEvent(int nState)
{
	switch( m_nFocus )
	{
		case FOCUS_PASSWORD_NEW:
			m_pNewPassword->OnCursorEvent(nState);
			break;
		
		case FOCUS_PASSWORD_CONFIRM:
			m_pConfirmPassword->OnCursorEvent(nState);
			break;
	}
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPreSharedKeyWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	BYTE  szPassword[8];
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	HWORD *pUniCodeStr = NULL;
	int nFontH = 0, nStrW = 0;
	int nXPos = 0, nYPos = 0, nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 2;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 2;
			break;
			
		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 2;
			break;
	}

	switch( nKey )
	{
		case KBD_SCAN_CODE_DOWN:
		case KBD_SCAN_CODE_UP:
			switch(m_nFocus)
			{
				case FOCUS_PASSWORD_NEW:
					if(m_pNewPassword->GetEditMode())
					{
						return;
					}
					else
					{
						m_nFocus = FOCUS_PASSWORD_CONFIRM;
						DrawWnd(0);
					}
					break;

				case FOCUS_PASSWORD_CONFIRM:
					if(m_pConfirmPassword->GetEditMode())
					{
						return;
					}
					else
					{
						m_nFocus = FOCUS_PASSWORD_NEW;
						DrawWnd(0);
					}
					break;						
			}
			break;			

		default:
			switch( m_nFocus )
			{
				case FOCUS_PASSWORD_NEW:
					m_pScreen->FillRect(PSK_WND_X_POS,
										PSK_WND_Y_POS + PSK_WND_ROW_H*7,
										PSK_WND_X_POS + PSK_WND_W-1,
										PSK_WND_Y_POS + PSK_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);
					
					m_pNewPassword->OnKeyEvent(nKey, nFlags);
					break;
				
				case FOCUS_PASSWORD_CONFIRM:
					m_pConfirmPassword->OnKeyEvent(nKey, nFlags);
					break;
			}
			break;
	}
}

/*********************************************************************************************************/
// Name		: IsNewConfirmEqual
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
BOOL CPreSharedKeyWnd::IsNewConfirmEqual()
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0, nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();
	BYTE  szNewPassword[MAX_PRE_SHARED_KEY], szConfirmPassword[MAX_PRE_SHARED_KEY];

	memset(szNewPassword,0x00,sizeof(BYTE)*MAX_PRE_SHARED_KEY);
	memset(szConfirmPassword,0x00,sizeof(BYTE)*MAX_PRE_SHARED_KEY);
	
	if( m_nFocus != FOCUS_PASSWORD_CONFIRM )
	{
		return FALSE;
	}		

	m_pNewPassword->GetText(szNewPassword);
	m_pConfirmPassword->GetText(szConfirmPassword);
	
	if( strcmp((char *)szNewPassword,(char *)szConfirmPassword) == 0) 
	{
		return TRUE;
	}		
	else
	{
		m_pNewPassword->Reset();
		m_pConfirmPassword->Reset();
		m_nFocus = FOCUS_PASSWORD_NEW;

		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:
				pFont = &NewGulLim18bCJK;
				nYOffset = 2;
				break;

			case LANG_RUS:
				pFont = &MyriadPro24bRus;
				nYOffset = 2;
				break;
				
			default:
				pFont = &MyriadPro24bEng;
				nYOffset = 2;
				break;
		}
	
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		
		pUniCodeStr = (HWORD *)STR_UNI_WRONG_PWD[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		
		nXPos = PSK_WND_X_POS + (PSK_WND_W - nStrW)/2;
		nYPos = PSK_WND_Y_POS + PSK_WND_ROW_H*7 + (PSK_WND_ROW_H - pFont->uHeight)/2;

		m_pScreen->FillRect(PSK_WND_X_POS,
							PSK_WND_Y_POS + PSK_WND_ROW_H*7,
							PSK_WND_X_POS + PSK_WND_W-1,
							PSK_WND_Y_POS + PSK_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);
		
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crWarn);

		m_pScreen->SetFont(pOldFont);

		DrawWnd(0);
		return FALSE;
	}
}

/*********************************************************************************************************/
// Name		: ResetPasswordBox
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPreSharedKeyWnd::ResetPasswordBox()
{
	m_pNewPassword->Reset();
	m_pConfirmPassword->Reset();

	m_pNewPassword->SetEditMode(0);
	m_pConfirmPassword->SetEditMode(0);
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CPreSharedKeyWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int   nResult = CWnd::CloseAlert(nKey, bMkdAlert);

	BYTE  szNewPassword[MAX_PRE_SHARED_KEY];
	memset(szNewPassword,0x00,sizeof(BYTE)*MAX_PRE_SHARED_KEY);
	
	m_pNewPassword->GetText(szNewPassword);
	szNewPassword[MAX_PRE_SHARED_KEY-1] = '\0';

	switch( nResult )
	{
		case AL_YES:
			//G_pUart3->OutputDbgMsg("[PRE-SHARED KEY]1)%s\r\n",szNewPassword);
			g_pDocMgr->SetPreSharedKey(szNewPassword);
			g_pDocMgr->SendSSA(szNewPassword);
			g_pDocMgr->SaveSettingValue();
			g_pDocMgr->RequestSSA();
			break;
		
		case AL_NO:
			break;
	}

	return nResult;
}
