/*...........................................................................*/
/*.                  File Name : MyriadPro08nEng.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.11.01                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

#if defined(_FONT_PLASTIMO_)
ROMDATA PEGUSHORT MyriadPro08nExt_offset_table[337] = {
0x0000,0x0007,0x000c,0x0013,0x0018,0x001f,0x0024,0x002a,0x002f,0x0035,0x003a,0x0040,0x0045,0x004b,0x0050,0x0057,
0x005e,0x0065,0x006b,0x0070,0x0076,0x007b,0x0081,0x0087,0x008d,0x0092,0x0098,0x009d,0x00a3,0x00aa,0x00b0,0x00b7,
0x00bd,0x00c4,0x00ca,0x00d1,0x00d7,0x00de,0x00e4,0x00eb,0x00f1,0x00f4,0x00f7,0x00fa,0x00fd,0x0100,0x0103,0x0106,
0x0109,0x010c,0x010f,0x0116,0x011b,0x011f,0x0122,0x0128,0x012e,0x0133,0x0138,0x013b,0x0140,0x0143,0x0148,0x014b,
0x0150,0x0154,0x0159,0x015c,0x0163,0x0169,0x0170,0x0176,0x017d,0x0183,0x0189,0x0190,0x0196,0x019e,0x01a4,0x01ac,
0x01b2,0x01ba,0x01c0,0x01ca,0x01d3,0x01d9,0x01dd,0x01e3,0x01e7,0x01ed,0x01f1,0x01f6,0x01fa,0x01ff,0x0203,0x0208,
0x020c,0x0211,0x0215,0x021a,0x021e,0x0223,0x0227,0x022c,0x0230,0x0237,0x023d,0x0244,0x024a,0x0251,0x0257,0x025e,
0x0264,0x026b,0x0271,0x0278,0x027e,0x0287,0x028f,0x0295,0x029a,0x02a0,0x02a6,0x02ab,0x02b1,0x02b6,0x02bc,0x02c1,
0x02c6,0x02c8,0x02ca,0x02cc,0x02ce,0x02d0,0x02d2,0x02d4,0x02d6,0x02d8,0x02da,0x02dc,0x02de,0x02e0,0x02e2,0x02e4,
0x02e6,0x02e8,0x02ea,0x02f0,0x02f2,0x02f4,0x02f6,0x02f8,0x02fa,0x02fc,0x02fe,0x0300,0x0302,0x0304,0x0306,0x0308,
0x030a,0x0312,0x0318,0x031a,0x031c,0x031e,0x0320,0x0322,0x0324,0x0326,0x0328,0x032a,0x032c,0x032e,0x0330,0x0332,
0x033a,0x0340,0x0342,0x0344,0x0346,0x0348,0x034a,0x034c,0x034e,0x0350,0x0352,0x0354,0x0356,0x0358,0x035a,0x035c,
0x035e,0x0360,0x0362,0x0364,0x0366,0x0368,0x036a,0x036c,0x036e,0x0370,0x0372,0x0374,0x0376,0x0378,0x037a,0x037c,
0x037e,0x0380,0x0382,0x0384,0x0386,0x0388,0x038a,0x038c,0x038e,0x0390,0x0392,0x0394,0x0396,0x0398,0x039a,0x039c,
0x039e,0x03a0,0x03a2,0x03a4,0x03a6,0x03a8,0x03aa,0x03ac,0x03ae,0x03b0,0x03b2,0x03b4,0x03b6,0x03b8,0x03ba,0x03bc,
0x03be,0x03c0,0x03c2,0x03c4,0x03c6,0x03c8,0x03ca,0x03cc,0x03ce,0x03d0,0x03d2,0x03d9,0x03de,0x03e7,0x03f0,0x03f8,
0x03fe,0x0400,0x0402,0x0404,0x0406,0x0408,0x040a,0x040c,0x040e,0x0410,0x0412,0x0414,0x0416,0x0418,0x041a,0x041c,
0x041e,0x0420,0x0422,0x0424,0x0426,0x0428,0x042a,0x042c,0x042e,0x0433,0x0437,0x043c,0x0440,0x0445,0x044a,0x044c,
0x044e,0x0450,0x0452,0x0454,0x0456,0x0458,0x045a,0x045c,0x045e,0x0460,0x0462,0x0464,0x0466,0x0468,0x046a,0x046c,
0x046e,0x0470,0x0472,0x0478,0x047d,0x047f,0x0481,0x0483,0x0485,0x0487,0x0489,0x048b,0x048d,0x048f,0x0491,0x0493,
0x0495,0x0497,0x0499,0x049b,0x049d,0x049f,0x04a1,0x04a3,0x04a5,0x04a7,0x04a9,0x04ab,0x04ad,0x04af,0x04b1,0x04b3,
0x04b5,
};


ROMDATA PEGUBYTE MyriadPro08nExt_data_table[1963] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x06, 0x00, 0xc0, 
0x08, 0x00, 0x00, 0x70, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xe0, 0x01, 0x80, 0x0d, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x08, 0x00, 0x00, 0x0a, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x02, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x30, 0x03, 0x00, 0x00, 0x01, 0x80, 0x70, 0x04, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x18, 0x00, 0x80, 0x00, 0x00, 0x23, 0x80, 0x00, 0x71, 0xc0, 
0x00, 0x20, 0x00, 0x0e, 0x00, 0x00, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x04, 
0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x80, 0x04, 0x00, 0x70, 0x02, 0x80, 0x00, 0x00, 
0x70, 0x00, 0x40, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x1c, 0x30, 0x00, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x0e, 0x10, 0x03, 0x8c, 0x33, 0x8e, 0x20, 0xce, 0x78, 0x15, 0xe0, 0x4f, 0x01, 0xee, 
0x3c, 0x43, 0xc0, 0x7b, 0x81, 0x9c, 0x1c, 0xe1, 0xe1, 0x07, 0x18, 0x88, 0x04, 0x48, 0x0e, 0x02, 
0xe9, 0x00, 0x89, 0x41, 0xd1, 0x40, 0x00, 0x08, 0x4a, 0x28, 0x44, 0x24, 0x42, 0x22, 0x01, 0x11, 
0x90, 0x22, 0x00, 0xe0, 0xc3, 0x87, 0x8e, 0x0b, 0x3f, 0x80, 0x0e, 0x1b, 0xc0, 0xe3, 0x90, 0xc8, 
0xe6, 0x02, 0x3f, 0xc3, 0xe3, 0xf0, 0x44, 0xf2, 0x20, 0x11, 0x18, 0x00, 0x80, 0x05, 0x22, 0x02, 
0x02, 0x21, 0x13, 0xa0, 0x7c, 0x8f, 0x91, 0xf6, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0e, 0x40, 0x00, 0x00, 0x00, 0x11, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x7c, 0x18, 0x3a, 0x18, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0xf0, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x30, 0x63, 0x00, 0x18, 0x04, 0x40, 0xc8, 0x11, 0x03, 0x20, 0x44, 0x15, 0x11, 0xe8, 0x31, 0x00, 
0x20, 0x02, 0x00, 0x40, 0x0e, 0x40, 0x62, 0x02, 0x10, 0x18, 0x80, 0x89, 0x04, 0x5e, 0x20, 0xba, 
0x08, 0x20, 0x88, 0x04, 0x12, 0x40, 0x08, 0x48, 0x4a, 0x48, 0x44, 0x26, 0x40, 0x32, 0x01, 0x90, 
0x00, 0x32, 0x01, 0x10, 0x04, 0x40, 0x11, 0x12, 0x44, 0x00, 0x09, 0x02, 0x20, 0x90, 0x2c, 0x16, 
0x09, 0x05, 0x81, 0x10, 0x88, 0x44, 0x44, 0x02, 0x21, 0x11, 0x00, 0x88, 0xc4, 0x44, 0x22, 0x02, 
0x22, 0x31, 0x10, 0x22, 0x08, 0x01, 0x00, 0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 
0x00, 0x11, 0x80, 0x00, 0x00, 0x00, 0x11, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x18, 0x60, 0x00, 0x44, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x20, 0x44, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x29, 0xe2, 0x9e, 0x29, 0xe8, 0x1d, 0x03, 0xa0, 0x34, 0x0e, 0x42, 0xf1, 0x0b, 0xc8, 0x31, 0x06, 
0x20, 0x62, 0x0c, 0x41, 0x90, 0x1e, 0x80, 0xf4, 0x03, 0xa0, 0x3c, 0x89, 0xcf, 0xee, 0x24, 0x82, 
0x49, 0x24, 0x89, 0x44, 0x94, 0x4d, 0x28, 0x48, 0x48, 0x48, 0x45, 0x35, 0x4e, 0x2a, 0x71, 0x53, 
0x8e, 0x2a, 0x72, 0x08, 0xc8, 0x23, 0x20, 0x84, 0x84, 0x08, 0xc9, 0x3a, 0x2e, 0x93, 0xa0, 0xd0, 
0x68, 0x34, 0x19, 0x38, 0x9c, 0x4e, 0x44, 0x92, 0x24, 0x11, 0x24, 0x88, 0x04, 0x41, 0x22, 0x49, 
0x55, 0x02, 0xa4, 0x54, 0x0b, 0xc1, 0x38, 0x2f, 0x30, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x20, 0x8d, 0x00, 0x00, 0x00, 0x11, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x78, 0xa0, 0xec, 0x8a, 0x74, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x06, 0x4e, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x48, 0x14, 0x81, 0x28, 0x18, 0x21, 0x04, 0x20, 0xc4, 0x10, 0x43, 0x11, 0x0c, 0x48, 0x49, 0x09, 
0x20, 0x92, 0x12, 0x42, 0x50, 0x22, 0x81, 0x14, 0x04, 0xa0, 0x44, 0x89, 0x24, 0x49, 0x24, 0x92, 
0x49, 0x24, 0x89, 0x44, 0x98, 0x51, 0x48, 0x48, 0x48, 0x4a, 0x56, 0x25, 0x49, 0x2a, 0x49, 0x52, 
0x49, 0x2a, 0x4a, 0x09, 0x28, 0x2c, 0xa0, 0xba, 0x84, 0x15, 0x29, 0x22, 0x68, 0x92, 0x11, 0x08, 
0x84, 0x42, 0x21, 0x10, 0x88, 0x44, 0x44, 0x92, 0x24, 0x91, 0x24, 0x89, 0x24, 0x49, 0x22, 0x49, 
0x55, 0x34, 0xa2, 0x94, 0x10, 0x82, 0x08, 0x42, 0x10, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 
0x00, 0x20, 0xb3, 0x00, 0x00, 0x00, 0x11, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x04, 0xa1, 0x12, 0x92, 0x88, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x44, 0x20, 0x80, 0x00, 0x00, 0x00, 0x00, 0x14, 0x48, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x48, 0xf4, 0x8f, 0x24, 0xf8, 0x21, 0x04, 0x20, 0x84, 0x10, 0x43, 0x13, 0xcc, 0x4f, 0x89, 0xf1, 
0x3d, 0x13, 0xe2, 0x7c, 0x51, 0xe2, 0x8f, 0x14, 0x78, 0xa3, 0xc4, 0xf9, 0x27, 0xc9, 0x24, 0x92, 
0x49, 0x24, 0x89, 0x44, 0x94, 0x61, 0x88, 0x48, 0x48, 0x48, 0x4c, 0x65, 0x49, 0x2a, 0x49, 0x52, 
0x49, 0x2a, 0x4a, 0x0a, 0x18, 0x28, 0x60, 0xa1, 0x87, 0xa2, 0x2e, 0x23, 0x88, 0xe2, 0x09, 0x84, 
0xc2, 0x61, 0x31, 0x10, 0x88, 0xee, 0x44, 0x92, 0x24, 0x91, 0x24, 0x89, 0x24, 0x49, 0x22, 0x49, 
0x54, 0xb4, 0x42, 0x88, 0x10, 0x82, 0x10, 0x42, 0x10, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x20, 0xa1, 0x00, 0x00, 0x00, 0x11, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x3c, 0xfc, 0x12, 0x92, 0x94, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4c, 0x44, 0x70, 0x80, 0x00, 0x00, 0x00, 0x00, 0x08, 0x28, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x79, 0x17, 0x91, 0x3d, 0x18, 0x21, 0x04, 0x20, 0x84, 0x10, 0x43, 0x11, 0x0c, 0x48, 0xf9, 0x1f, 
0x21, 0xf2, 0x3e, 0x47, 0xd0, 0x62, 0x83, 0x14, 0x18, 0xa0, 0xc4, 0x89, 0x24, 0x49, 0x24, 0x92, 
0x49, 0x24, 0x89, 0x44, 0x92, 0x51, 0x48, 0x48, 0x48, 0x48, 0x44, 0x25, 0x49, 0x2a, 0x49, 0x52, 
0x49, 0x26, 0x4a, 0x0a, 0x18, 0x28, 0x60, 0xa1, 0x84, 0x23, 0xea, 0x22, 0x48, 0xa2, 0x04, 0x42, 
0x21, 0x10, 0x89, 0x10, 0x88, 0x44, 0x44, 0x92, 0x24, 0x91, 0x24, 0x89, 0x24, 0x49, 0x22, 0x49, 
0x54, 0xb4, 0x42, 0x88, 0x21, 0x04, 0x10, 0x84, 0x10, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x20, 0xa1, 0x00, 0x00, 0x00, 0x11, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x45, 0x20, 0xfe, 0xa2, 0xa4, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x44, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x49, 0x34, 0x93, 0x45, 0x74, 0x30, 0x86, 0x10, 0xc2, 0x18, 0x45, 0x11, 0x14, 0x48, 0x81, 0x10, 
0x21, 0x02, 0x20, 0x44, 0x08, 0x62, 0x43, 0x12, 0x1c, 0x90, 0xc4, 0x89, 0x24, 0x49, 0x24, 0x92, 
0x49, 0x24, 0x89, 0x44, 0x92, 0x49, 0x48, 0x48, 0x48, 0x48, 0x44, 0x24, 0xc9, 0x26, 0x49, 0x32, 
0x49, 0x22, 0x49, 0x11, 0x24, 0x48, 0xd1, 0x23, 0x44, 0x22, 0x09, 0x22, 0x28, 0x92, 0x04, 0x42, 
0x21, 0x10, 0x89, 0x10, 0x88, 0x44, 0x44, 0x92, 0x24, 0x91, 0x24, 0x89, 0x24, 0x49, 0x22, 0x49, 
0x98, 0xcc, 0x41, 0x88, 0x21, 0x08, 0x20, 0x84, 0x10, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x11, 0x23, 0x00, 0x00, 0x00, 0x11, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x4d, 0x21, 0x10, 0x44, 0x4c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x44, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x08, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x84, 0xd8, 0x4d, 0x42, 0x93, 0xcc, 0x79, 0x8f, 0x31, 0xe6, 0x78, 0xf1, 0xe3, 0xcf, 0x79, 0xef, 
0x3e, 0xf3, 0xde, 0x7b, 0xc7, 0x9e, 0x3c, 0xf1, 0xe3, 0x8f, 0x3c, 0x89, 0x24, 0x49, 0x24, 0x92, 
0x49, 0x24, 0xb1, 0x58, 0x91, 0x4d, 0x2f, 0x4f, 0x0f, 0x4f, 0x47, 0xa4, 0x49, 0x22, 0x41, 0x12, 
0x49, 0x22, 0x48, 0xe0, 0xc3, 0x87, 0x0e, 0x1c, 0x3f, 0x9d, 0xe9, 0x22, 0x20, 0x92, 0x39, 0x9c, 
0xce, 0x67, 0x30, 0x00, 0x84, 0x42, 0x38, 0x71, 0xc3, 0x8e, 0x1c, 0x70, 0xe3, 0x87, 0x1c, 0x38, 
0x88, 0x48, 0x41, 0x08, 0x7f, 0xcf, 0xfd, 0xff, 0x10, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x00, 0x0e, 0x1c, 0x00, 0x00, 0x00, 0x0e, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xb6, 0x1c, 0xee, 0xb8, 0xb0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xcc, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 0x00, 0x08, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x04, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x88, 0x00, 0x00, 0x02, 0x00, 0x10, 0x00, 0x80, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x12, 0x00, 0x00, 0x40, 0x84, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x08, 0x10, 0x00, 
0x00, 0x02, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x00, 0x00, 0x00, 
0x02, 0x20, 0x01, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x08, 
0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x02, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x44, 0x00, 0x00, 0x3c, 0x01, 0xe0, 0x0f, 0x06, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x09, 0x00, 0x00, 0x81, 0x04, 0x10, 0x00, 0x02, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x08, 0x10, 0x00, 
0x00, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x00, 0x00, 0x00, 
0x04, 0x20, 0x00, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x04, 
0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc4, 0x22, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x08, 0x20, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
};
#else
ROMDATA PEGUSHORT MyriadPro08nExt_offset_table[337] = {
0x0000,0x0007,0x000d,0x0014,0x001a,0x0021,0x0027,0x002e,0x0033,0x003a,0x003f,0x0046,0x004b,0x0052,0x0057,0x005e,
0x0066,0x006e,0x0074,0x007a,0x0080,0x0086,0x008c,0x0092,0x0098,0x009e,0x00a4,0x00aa,0x00b0,0x00b7,0x00bd,0x00c4,
0x00ca,0x00d1,0x00d7,0x00de,0x00e4,0x00eb,0x00f1,0x00f9,0x0100,0x0104,0x0108,0x010c,0x010f,0x0113,0x0116,0x011a,
0x011c,0x0120,0x0122,0x012a,0x0130,0x0135,0x0138,0x013e,0x0143,0x0148,0x014d,0x014f,0x0154,0x0157,0x015c,0x0160,
0x0165,0x016a,0x0170,0x0174,0x017b,0x0181,0x0188,0x018e,0x0195,0x019b,0x01a3,0x01aa,0x01b0,0x01b8,0x01be,0x01c6,
0x01cc,0x01d4,0x01da,0x01e5,0x01ef,0x01f6,0x01fa,0x0201,0x0206,0x020d,0x0211,0x0217,0x021c,0x0222,0x0227,0x022d,
0x0232,0x0238,0x023d,0x0243,0x0247,0x024d,0x0252,0x0258,0x025c,0x0263,0x0269,0x0270,0x0276,0x027d,0x0283,0x028a,
0x0290,0x0297,0x029d,0x02a4,0x02aa,0x02b4,0x02bc,0x02c3,0x02c9,0x02d0,0x02d6,0x02db,0x02e1,0x02e6,0x02ec,0x02f1,
0x02f5,0x0300,0x030b,0x0316,0x0321,0x032c,0x0337,0x0342,0x034d,0x0358,0x0363,0x036e,0x0379,0x0384,0x038f,0x039a,
0x03a2,0x03ad,0x03b8,0x03be,0x03c9,0x03d4,0x03df,0x03ea,0x03f5,0x0400,0x040b,0x0416,0x0421,0x042c,0x0437,0x0442,
0x044d,0x0455,0x045b,0x0466,0x0471,0x047c,0x0487,0x0492,0x049d,0x04a8,0x04b3,0x04be,0x04c9,0x04d4,0x04df,0x04ea,
0x04f2,0x04f9,0x0504,0x050f,0x051a,0x0525,0x0530,0x053b,0x0541,0x054c,0x0557,0x0562,0x056d,0x0578,0x0583,0x058e,
0x0599,0x05a4,0x05af,0x05ba,0x05c5,0x05d0,0x05db,0x05e6,0x05f1,0x05fc,0x0607,0x0612,0x061d,0x0628,0x0634,0x0640,
0x064c,0x0658,0x0664,0x0670,0x067c,0x0688,0x0694,0x06a0,0x06ac,0x06b8,0x06c4,0x06d0,0x06dc,0x06e8,0x06f3,0x06fe,
0x0709,0x0714,0x071f,0x072a,0x0735,0x073c,0x0742,0x0749,0x074f,0x0755,0x075a,0x0762,0x0768,0x0773,0x077e,0x0784,
0x0789,0x0794,0x079f,0x07aa,0x07b5,0x07c0,0x07cc,0x07d7,0x07e2,0x07ed,0x07f3,0x07fa,0x0800,0x080a,0x0814,0x081c,
0x0822,0x082d,0x0838,0x0843,0x084e,0x0859,0x0864,0x086f,0x087a,0x0885,0x0890,0x089b,0x08a6,0x08b1,0x08bc,0x08c7,
0x08d2,0x08dd,0x08e8,0x08f3,0x08fe,0x0909,0x0914,0x091f,0x092a,0x0930,0x0935,0x093b,0x093f,0x094a,0x0955,0x0960,
0x096b,0x0976,0x0981,0x098c,0x0997,0x09a2,0x09ad,0x09b8,0x09c3,0x09c9,0x09cf,0x09da,0x09e5,0x09f0,0x09fb,0x0a06,
0x0a11,0x0a1c,0x0a27,0x0a32,0x0a3d,0x0a48,0x0a53,0x0a5e,0x0a69,0x0a74,0x0a7f,0x0a8a,0x0a95,0x0aa0,0x0aab,0x0ab6,
0x0ac1,0x0acc,0x0ad7,0x0ae2,0x0aed,0x0af8,0x0b03,0x0b0e,0x0b19,0x0b24,0x0b2f,0x0b3a,0x0b45,0x0b50,0x0b5b,0x0b66,
0x0b71,
};
ROMDATA PEGUBYTE MyriadPro08nExt_data_table[4771] = {
0x00, 0x02, 0x40, 0x00, 0x00, 0x10, 0x03, 0x00, 0x00, 0x04, 0x80, 0x90, 0x00, 0x00, 0x00, 0x00, 
0x48, 0x00, 0x00, 0x00, 0x04, 0x80, 0x30, 0x02, 0x40, 0x00, 0x00, 0x00, 0x03, 0x09, 0x00, 0x00, 
0xa0, 0x01, 0x40, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 
0x00, 0x01, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0x01, 0x40, 0x00, 0x00, 0x00, 0x20, 0x00, 
0x01, 0x20, 0x08, 0x03, 0x00, 0x00, 0x12, 0x00, 0x00, 0x90, 0x00, 0x06, 0x80, 0x00, 0x01, 0x20, 
0x02, 0x00, 0x28, 0x00, 0x00, 0x03, 0x00, 0x01, 0x80, 0x00, 0x10, 0x00, 0x01, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x01, 0x22, 0x40, 0x00, 0x00, 0x00, 0x01, 
0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x02, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x78, 0x01, 0x80, 0x00, 0x00, 0x20, 0x04, 0x80, 0x20, 0x03, 0x00, 0x60, 0x00, 0x00, 0x07, 0x80, 
0x30, 0x02, 0x00, 0x00, 0x03, 0x00, 0x48, 0x01, 0x80, 0x04, 0x00, 0x00, 0x04, 0x80, 0x00, 0x00, 
0xe0, 0xe1, 0xc0, 0x04, 0x00, 0x00, 0x48, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x60, 0x02, 0x80, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0xc0, 0x10, 0x04, 0x80, 0x00, 0x0c, 0x00, 0x00, 0x60, 0x00, 0x05, 0x80, 0x3c, 0x00, 0xc0, 
0x05, 0x00, 0x50, 0x00, 0x00, 0x04, 0x80, 0x02, 0x40, 0x14, 0x20, 0x04, 0x00, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x0f, 0x00, 
0x00, 0x0f, 0x00, 0x00, 0x03, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0xc1, 0x80, 0x00, 0x00, 0x00, 0x00, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 
0x04, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x04, 0x80, 0x00, 0x00, 0x40, 0x18, 0x00, 0x00, 0x12, 0x00, 0x24, 0x00, 0x20, 0x00, 
0x01, 0x20, 0x00, 0x00, 0x00, 0x12, 0x00, 0x60, 0x04, 0x80, 0x00, 0x00, 0x40, 0x10, 0x00, 0x20, 
0x0d, 0x00, 0x14, 0x00, 0x00, 0x00, 0x02, 0x02, 0x00, 0x04, 0x04, 0x29, 0x04, 0x00, 0x40, 0x02, 
0x00, 0x00, 0x02, 0x48, 0x00, 0x00, 0x00, 0x00, 0x01, 0x20, 0x02, 0x80, 0x00, 0x00, 0x00, 0x80, 
0x00, 0x04, 0x80, 0x20, 0x18, 0x00, 0x00, 0x90, 0x00, 0x00, 0x40, 0x00, 0x0d, 0x00, 0x00, 0x02, 
0x42, 0x08, 0x00, 0x50, 0x00, 0x00, 0x01, 0x80, 0x06, 0x00, 0x00, 0x80, 0x00, 0x09, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x90, 0x02, 0x00, 0x90, 0x06, 0x00, 0x90, 0x10, 0x80, 
0x90, 0x09, 0x00, 0xf0, 0x0d, 0x00, 0x30, 0x09, 0x00, 0xf0, 0x0b, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x08, 
0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x30, 0xf1, 0x83, 0x0c, 0x00, 0x78, 0x87, 0xa4, 0x78, 0x87, 0x8d, 0xe0, 0x25, 0xe0, 0xff, 0x9e, 
0xf8, 0xcf, 0x88, 0xf8, 0x0f, 0x8c, 0x3c, 0x91, 0xe3, 0x0f, 0x10, 0x78, 0x88, 0x50, 0x21, 0x78, 
0xeb, 0xef, 0xdf, 0xae, 0x39, 0x92, 0x75, 0x8a, 0x00, 0x85, 0x05, 0x29, 0x84, 0x10, 0x4c, 0x44, 
0x62, 0x03, 0x11, 0x88, 0x18, 0x80, 0x38, 0x78, 0xe0, 0xc3, 0x85, 0x0f, 0xf0, 0x01, 0xe1, 0x3c, 
0x03, 0xc3, 0x3c, 0x47, 0xa4, 0xf0, 0x1e, 0x67, 0xd1, 0xf4, 0x7e, 0x88, 0x4b, 0x42, 0x7a, 0x11, 
0x90, 0x94, 0x84, 0xa4, 0x20, 0x22, 0x22, 0x44, 0x49, 0x22, 0xf9, 0x1f, 0x13, 0xe6, 0x23, 0xfc, 
0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 
0xc7, 0xf8, 0xff, 0x1e, 0x1f, 0xe3, 0xfc, 0x0d, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 
0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe1, 0xc8, 0x0f, 0xf1, 0xfe, 0x3f, 0xc7, 
0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x21, 0x80, 0x3f, 
0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x9f, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 
0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 
0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x06, 0x00, 0x60, 0x02, 0x00, 0x60, 0x19, 0x80, 0x60, 0x10, 0x80, 
0x60, 0x10, 0x80, 0x90, 0x10, 0x80, 0xd0, 0x10, 0x80, 0x90, 0x10, 0x40, 0xb0, 0x7f, 0x8f, 0xf1, 
0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe1, 0xe0, 0x0f, 0x19, 0x14, 0x0e, 0x00, 0x7f, 0x8f, 0xf3, 
0xe6, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x04, 0x07, 0xf8, 0xff, 0x1f, 0xe1, 0x06, 0x04, 
0x3f, 0x82, 0x03, 0xa2, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 
0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 
0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1e, 0x07, 0xd0, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 
0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0x00, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 
0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 
0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 
0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x00, 

0x30, 0x01, 0x80, 0x0c, 0x00, 0x80, 0x08, 0x00, 0x80, 0x08, 0x01, 0x10, 0x29, 0x10, 0x28, 0x00, 
0x80, 0x08, 0x00, 0x80, 0x08, 0x00, 0x40, 0x02, 0x00, 0x10, 0x00, 0x80, 0x08, 0x50, 0x7f, 0xa0, 
0x40, 0x40, 0x81, 0x04, 0x10, 0x80, 0x10, 0x92, 0x00, 0x85, 0x05, 0x4a, 0x84, 0x10, 0x4c, 0x40, 
0x62, 0x03, 0x10, 0x10, 0x18, 0x80, 0x44, 0x01, 0x10, 0x04, 0x40, 0x11, 0x00, 0x01, 0x10, 0x22, 
0x02, 0x20, 0x40, 0x08, 0x01, 0x00, 0x20, 0x01, 0x10, 0x44, 0x88, 0x88, 0x40, 0x42, 0x02, 0x10, 
0x10, 0x88, 0x84, 0x04, 0x20, 0x22, 0x20, 0x04, 0x40, 0x22, 0x08, 0x01, 0x00, 0x20, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 
0x44, 0x08, 0x81, 0x11, 0x10, 0x22, 0x04, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x30, 0x28, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x21, 0x00, 0xa0, 
0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x81, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x0e, 0x00, 0x60, 0x02, 0x00, 0x20, 0x30, 0xc0, 0x60, 0x10, 0x81, 
0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x41, 0x08, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x00, 0x10, 0x01, 0x24, 0x11, 0x00, 0x40, 0x88, 0x10, 
0x20, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x06, 0x84, 0x08, 0x81, 0x10, 0x21, 0x06, 0x0a, 
0x28, 0x00, 0x04, 0x40, 0x90, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 
0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x20, 0x01, 0x10, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 
0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x00, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x00, 

0x48, 0xe2, 0x47, 0x12, 0x39, 0x01, 0xd0, 0x1d, 0x01, 0xd0, 0x1d, 0x09, 0xe1, 0x09, 0xe8, 0x1c, 
0x81, 0xc8, 0x1c, 0x81, 0xc8, 0x1c, 0x80, 0xf4, 0x07, 0xa0, 0x3d, 0x01, 0xe8, 0x5e, 0x21, 0x3c, 
0x44, 0x44, 0x89, 0x24, 0x90, 0x96, 0x16, 0xa2, 0x52, 0x85, 0x05, 0x08, 0x84, 0x18, 0x6a, 0x5e, 
0x52, 0xf2, 0x97, 0x87, 0x94, 0xbc, 0x82, 0x72, 0x09, 0xc8, 0x27, 0x21, 0x03, 0xb9, 0x12, 0xa2, 
0x3a, 0x25, 0x40, 0xe8, 0x1d, 0x03, 0xa0, 0x71, 0x1c, 0x47, 0x08, 0xe8, 0x51, 0x42, 0x8a, 0x14, 
0x50, 0xa2, 0x85, 0x14, 0x28, 0xa2, 0x29, 0x22, 0x91, 0x14, 0x13, 0xc2, 0x78, 0x4f, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 
0x44, 0x08, 0x81, 0x00, 0x90, 0x22, 0x04, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0x13, 0xc8, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x21, 0x23, 0x20, 
0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x82, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 0x01, 0x90, 0x02, 0x00, 0x20, 0x20, 0x41, 0x90, 0x10, 0x81, 
0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x41, 0x08, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0x07, 0xa0, 0x3d, 0x44, 0xa0, 0x9c, 0x40, 0x88, 0x10, 
0x4f, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 0x04, 0x08, 0x81, 0x10, 0x22, 0x89, 0x1c, 
0x48, 0x1d, 0xc8, 0xa7, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 
0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x20, 0x71, 0x1c, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 
0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x38, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x00, 

0x48, 0x12, 0x40, 0x92, 0x05, 0x02, 0x10, 0x21, 0x02, 0x10, 0x21, 0x0a, 0x23, 0xca, 0x2f, 0x22, 
0xf2, 0x2f, 0x22, 0xf2, 0x2f, 0x22, 0x81, 0x14, 0x08, 0xa0, 0x45, 0x02, 0x2f, 0xd1, 0x3f, 0x22, 
0x44, 0x44, 0x89, 0x24, 0x90, 0x92, 0x12, 0xc2, 0x94, 0x85, 0x05, 0x08, 0x85, 0x10, 0x4a, 0x51, 
0x52, 0x8a, 0x94, 0x44, 0x54, 0xa2, 0x82, 0x8a, 0x0a, 0x28, 0x28, 0xa1, 0xf4, 0x45, 0x13, 0x22, 
0x22, 0x26, 0x39, 0x07, 0x20, 0xe4, 0x1c, 0x81, 0x10, 0x44, 0x1c, 0x88, 0x51, 0x42, 0x8a, 0x14, 
0x50, 0xa2, 0x85, 0x14, 0x28, 0x95, 0x49, 0x22, 0x91, 0x14, 0x20, 0x44, 0x08, 0x81, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 
0x44, 0x08, 0x81, 0x3f, 0x90, 0x22, 0x04, 0x39, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0x14, 0x48, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x21, 0x22, 0x20, 
0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x84, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 0x00, 0x18, 0x02, 0x00, 0x20, 0x20, 0x41, 0x08, 0x10, 0x81, 
0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x41, 0x08, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0xe8, 0xa0, 0x45, 0x85, 0x20, 0xa2, 0x40, 0x88, 0x10, 
0x81, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x11, 0x04, 0x08, 0x81, 0x10, 0x23, 0x49, 0x02, 
0x4f, 0x02, 0x29, 0x29, 0x90, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 
0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x1c, 0x81, 0x10, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 
0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x8f, 0x44, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x00, 

0x48, 0xf2, 0x47, 0x92, 0x3d, 0x02, 0x10, 0x21, 0x02, 0x10, 0x21, 0x0a, 0x21, 0x0a, 0x28, 0x3e, 
0x83, 0xe8, 0x3e, 0x83, 0xe8, 0x3e, 0x9d, 0x14, 0xe8, 0xa7, 0x45, 0x3a, 0x28, 0x51, 0x21, 0x22, 
0x44, 0x44, 0x89, 0x24, 0x90, 0x92, 0x12, 0xc3, 0x18, 0x85, 0x05, 0x08, 0x95, 0x30, 0xc9, 0x51, 
0x4a, 0x8a, 0x54, 0x44, 0x52, 0xa2, 0x82, 0x8a, 0x0a, 0x28, 0x28, 0xa1, 0x04, 0x7d, 0xe2, 0x3c, 
0x23, 0xc4, 0x05, 0x80, 0xb0, 0x16, 0x02, 0xc1, 0x10, 0x44, 0x08, 0xe8, 0x51, 0x42, 0x8a, 0x14, 
0x50, 0xa2, 0x85, 0x14, 0x28, 0x95, 0x4a, 0xa1, 0x0a, 0x08, 0x20, 0x84, 0x10, 0x82, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 
0x44, 0x08, 0x81, 0x20, 0x90, 0x22, 0x04, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0x14, 0x48, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x21, 0x22, 0x20, 
0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x86, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x1f, 0x80, 0xf8, 0x02, 0x00, 0x20, 0x20, 0x41, 0x08, 0x10, 0x81, 
0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x41, 0x08, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0x28, 0xa7, 0x45, 0x86, 0x20, 0xa2, 0x40, 0x88, 0x10, 
0xc2, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 0x04, 0x08, 0x81, 0x10, 0x22, 0x49, 0x1e, 
0x78, 0x1f, 0xe9, 0x2a, 0x90, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 
0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x02, 0xc1, 0x10, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 
0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x7c, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x00, 

0xfd, 0x17, 0xe8, 0xbf, 0x45, 0x02, 0x10, 0x21, 0x02, 0x10, 0x21, 0x0a, 0x21, 0x0a, 0x28, 0x20, 
0x82, 0x08, 0x20, 0x82, 0x08, 0x20, 0x85, 0x14, 0x28, 0xa1, 0x45, 0x0a, 0x28, 0x51, 0x21, 0x22, 
0x44, 0x44, 0x89, 0x24, 0x90, 0x92, 0x12, 0xa2, 0x94, 0x85, 0x05, 0x08, 0x94, 0x10, 0x49, 0x51, 
0x4a, 0x8a, 0x54, 0x44, 0x52, 0xa2, 0x82, 0x8a, 0x0a, 0x28, 0x28, 0xa1, 0x04, 0x41, 0x22, 0x24, 
0x22, 0x44, 0x04, 0x60, 0x8c, 0x11, 0x82, 0x31, 0x10, 0x44, 0x08, 0x88, 0x51, 0x42, 0x8a, 0x14, 
0x50, 0xa2, 0x85, 0x14, 0x28, 0x95, 0x4a, 0xa1, 0x0a, 0x08, 0x41, 0x08, 0x21, 0x04, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 
0x44, 0x08, 0x81, 0x20, 0x90, 0x22, 0x04, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0x14, 0x48, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x21, 0x22, 0x20, 
0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x81, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x10, 0x81, 0x08, 0x02, 0x00, 0x20, 0x20, 0x41, 0x08, 0x10, 0x81, 
0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x10, 0x81, 0x08, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x24, 0xf7, 0xa1, 0x45, 0x45, 0x20, 0xa2, 0x40, 0x88, 0x10, 
0x26, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x16, 0x04, 0x08, 0x81, 0x10, 0x22, 0x5f, 0xa2, 
0x88, 0x22, 0x0a, 0x2a, 0x90, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 
0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x02, 0x31, 0x10, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 
0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x40, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x00, 

0x85, 0x14, 0x28, 0xa1, 0x44, 0x82, 0x08, 0x20, 0x82, 0x08, 0x21, 0x12, 0x21, 0x12, 0x28, 0x22, 
0x82, 0x28, 0x22, 0x82, 0x08, 0x22, 0x45, 0x12, 0x28, 0x91, 0x44, 0x8a, 0x28, 0x51, 0x21, 0x22, 
0x44, 0x44, 0x89, 0x24, 0x90, 0x92, 0x12, 0x92, 0x54, 0x85, 0x05, 0x08, 0x84, 0x10, 0x48, 0xd1, 
0x46, 0x8a, 0x34, 0x44, 0x51, 0xa2, 0x44, 0x89, 0x12, 0x24, 0x48, 0x91, 0x04, 0x41, 0x12, 0x22, 
0x22, 0x24, 0x04, 0x20, 0x84, 0x10, 0x82, 0x11, 0x10, 0x44, 0x08, 0x88, 0x51, 0x42, 0x8a, 0x14, 
0x50, 0xa2, 0x85, 0x14, 0x28, 0x88, 0x84, 0x41, 0x04, 0x08, 0x82, 0x10, 0x42, 0x08, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 
0x44, 0x08, 0x81, 0x11, 0x10, 0x22, 0x04, 0x21, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 
0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x24, 0x48, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x21, 0x22, 0x20, 
0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x81, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 
0x08, 0x81, 0x10, 0x22, 0x04, 0x30, 0xc1, 0x18, 0x02, 0x00, 0x20, 0x10, 0x81, 0x98, 0x10, 0x81, 
0x18, 0x10, 0x81, 0x18, 0x10, 0x81, 0x18, 0x10, 0x81, 0x18, 0x10, 0x81, 0x18, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x20, 0x91, 0x45, 0x24, 0x91, 0x22, 0x40, 0x88, 0x10, 
0x21, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x1f, 0x04, 0x08, 0x81, 0x10, 0x22, 0x50, 0xa2, 
0x88, 0x23, 0x24, 0x4c, 0x90, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 
0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 
0x02, 0x20, 0x44, 0x08, 0x81, 0x02, 0x11, 0x10, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 
0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x44, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 
0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x00, 

0x84, 0xf4, 0x27, 0xa1, 0x3c, 0x79, 0xc7, 0x9c, 0x79, 0xc7, 0x9d, 0xe1, 0xe1, 0xe1, 0xef, 0x9c, 
0xf9, 0xcf, 0x9c, 0xf9, 0xef, 0x9c, 0x3c, 0xf1, 0xe7, 0x8f, 0x3c, 0x79, 0xe8, 0x51, 0x21, 0x22, 
0xe4, 0xe5, 0xcb, 0xae, 0xbb, 0x12, 0xe2, 0x8a, 0x32, 0xf5, 0xe5, 0xe8, 0xf4, 0x1e, 0x48, 0xd1, 
0x46, 0x8a, 0x34, 0x44, 0x51, 0xa2, 0x38, 0x70, 0xe1, 0xc3, 0x87, 0x0f, 0xf3, 0xbd, 0x0a, 0x21, 
0x22, 0x14, 0x79, 0xcf, 0x39, 0xe7, 0x3c, 0xe1, 0x0c, 0x43, 0x08, 0x67, 0x8f, 0x3c, 0x79, 0xe3, 
0xcf, 0x1e, 0x78, 0xf3, 0xc7, 0x88, 0x84, 0x41, 0x04, 0x08, 0xfb, 0xdf, 0x7b, 0xef, 0x23, 0xfc, 
0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 
0xc7, 0xf8, 0xff, 0x0e, 0x1f, 0xe3, 0xfc, 0x21, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 
0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe1, 0xc3, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 
0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1e, 0x1e, 0x3f, 
0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x81, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 
0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 
0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x20, 0x40, 0xe8, 0x02, 0x00, 0x20, 0x0f, 0x00, 0xf0, 0x0f, 0x00, 
0xe8, 0x0f, 0x00, 0xe8, 0x0f, 0x00, 0xe8, 0x0f, 0x00, 0xe8, 0x0f, 0x00, 0xe8, 0x7f, 0x8f, 0xf1, 
0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe1, 0xef, 0x8f, 0x3d, 0x14, 0x4e, 0x1c, 0x7f, 0x8f, 0xf0, 
0x21, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x10, 0x87, 0xf8, 0xff, 0x1f, 0xe2, 0x50, 0x9e, 
0x8f, 0x9c, 0xcb, 0x87, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 
0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 
0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x3c, 0xe1, 0x0c, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 
0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0x38, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 
0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 
0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 
0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x40, 0x00, 0x00, 0x10, 0x00, 0x80, 0x04, 0x20, 0x20, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x20, 0x00, 0x02, 0x02, 0x10, 0x80, 0x00, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x10, 0x00, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x04, 0x00, 0x04, 0x08, 0x00, 0x00, 0x02, 
0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x21, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x01, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x60, 0x00, 0x00, 0xe0, 0x07, 0x00, 0x38, 0xe1, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x90, 0x00, 0x0c, 0x04, 0x63, 0x00, 0x01, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x30, 0x60, 0x00, 0x00, 0x03, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
0x60, 0x00, 0x00, 0x00, 0x00, 0xc7, 0x00, 0x03, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x60, 0xc0, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x38, 0x00, 0x03, 0x06, 0x00, 0x00, 0x01, 
0xce, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x42, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
};
#endif

#if defined(_FONT_PLASTIMO_)
xFONTYY MyriadPro08nExt_Font = {0x01, 10, 3, 13, 0, 0, 13, 151, 0x0100, 0x024f,
(PEGUSHORT *) MyriadPro08nExt_offset_table,NULL,
(PEGUBYTE *) MyriadPro08nExt_data_table};
#else
xFONTYY MyriadPro08nExt_Font = {0x01, 11, 2, 13, 0, 0, 13, 367, 0x0100, 0x024f,
(PEGUSHORT *) MyriadPro08nExt_offset_table,NULL,
(PEGUBYTE *) MyriadPro08nExt_data_table};
#endif

#if defined(_FONT_PLASTIMO_)
ROMDATA PEGUSHORT MyriadPro08nEng_offset_table[257] = {
0x0000,0x0006,0x000d,0x0014,0x001b,0x0022,0x0029,0x0030,0x0037,0x003e,0x0045,0x004c,0x0053,0x0059,0x005f,0x0065,
0x0072,0x0078,0x007e,0x0084,0x008a,0x0090,0x0096,0x009c,0x00a2,0x00a8,0x00ae,0x00b4,0x00ba,0x00c0,0x00c6,0x00cc,
0x00d2,0x00d9,0x00dc,0x00e0,0x00e5,0x00eb,0x00f4,0x00fb,0x00fd,0x0100,0x0103,0x0108,0x010f,0x0111,0x0114,0x0116,
0x011a,0x0121,0x0128,0x012f,0x0136,0x013d,0x0144,0x014b,0x0152,0x0159,0x0160,0x0162,0x0164,0x016b,0x0172,0x0179,
0x017d,0x0185,0x018c,0x0192,0x0198,0x019f,0x01a6,0x01ab,0x01b2,0x01b9,0x01bc,0x01c0,0x01c6,0x01cb,0x01d4,0x01db,
0x01e3,0x01e9,0x01f1,0x01f7,0x01fe,0x0203,0x020a,0x0210,0x0217,0x021d,0x0223,0x0229,0x022c,0x0230,0x0233,0x023a,
0x0240,0x0243,0x0248,0x024e,0x0253,0x0259,0x025f,0x0263,0x0269,0x026f,0x0272,0x0275,0x027a,0x027d,0x0286,0x028c,
0x0292,0x0298,0x029e,0x02a2,0x02a6,0x02aa,0x02b0,0x02b5,0x02bd,0x02c2,0x02c7,0x02cc,0x02cf,0x02d2,0x02d5,0x02dc,
0x02e2,0x02e4,0x02e6,0x02e8,0x02ea,0x02ec,0x02ee,0x02f0,0x02f2,0x02f4,0x02f6,0x02f8,0x02fa,0x02fc,0x02fe,0x0300,
0x0302,0x0304,0x0306,0x0308,0x030a,0x030c,0x030e,0x0310,0x0312,0x0314,0x0316,0x0318,0x031a,0x031c,0x031e,0x0320,
0x0322,0x0324,0x0327,0x032d,0x0333,0x0339,0x033f,0x0342,0x0348,0x034b,0x0352,0x0356,0x035b,0x0362,0x0365,0x036b,
0x036e,0x0371,0x0378,0x037b,0x037e,0x0381,0x0387,0x038d,0x038f,0x0392,0x0395,0x0399,0x039e,0x03a6,0x03ae,0x03b7,
0x03bb,0x03c2,0x03c9,0x03d0,0x03d7,0x03de,0x03e5,0x03ee,0x03f4,0x03f9,0x03fe,0x0403,0x0408,0x040b,0x040e,0x0411,
0x0414,0x041b,0x0422,0x042a,0x0432,0x043a,0x0442,0x044a,0x0451,0x0459,0x0460,0x0467,0x046e,0x0475,0x047b,0x0481,
0x0487,0x048c,0x0491,0x0496,0x049b,0x04a0,0x04a5,0x04ae,0x04b3,0x04b9,0x04bf,0x04c5,0x04cb,0x04ce,0x04d1,0x04d4,
0x04d7,0x04dd,0x04e3,0x04e9,0x04ef,0x04f5,0x04fb,0x0501,0x0508,0x050e,0x0514,0x051a,0x0520,0x0526,0x052b,0x0531,
0x0536,
};


ROMDATA PEGUBYTE MyriadPro08nEng_data_table[2171] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 
0x42, 0x8f, 0x10, 0x01, 0x04, 0x10, 0x00, 0x1f, 0x7d, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x07, 0x1c, 
0xc0, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x00, 0x00, 0x00, 0x02, 0x07, 0x00, 0x18, 0x00, 0x10, 0x04, 
0xe2, 0x95, 0x10, 0x01, 0x0e, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 
0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x0e, 0x18, 0x38, 0x50, 0xe0, 0x00, 0x00, 0x00, 
0x0a, 0xcf, 0xd0, 0x02, 0x0c, 0x06, 0x0e, 0x0e, 0x00, 0x00, 0x00, 0x08, 0x10, 0x20, 0xa1, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0xc2, 0x85, 0x0a, 0x14, 0x28, 0x00, 0x00, 0x40, 0x85, 0x08, 0x80, 0x68, 0x00, 0x18, 0x0c, 
0x42, 0x95, 0x10, 0x01, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x01, 0x00, 0x03, 0x09, 
0x48, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 
0x01, 0x07, 0x0e, 0x78, 0x79, 0xe3, 0x91, 0x22, 0x45, 0x08, 0x44, 0x47, 0x0e, 0x1c, 0x38, 0x33, 
0xe8, 0xa1, 0x93, 0x8c, 0x4f, 0xb8, 0xc0, 0x00, 0x80, 0x40, 0x01, 0x00, 0x60, 0x20, 0x92, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xa0, 0x04, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x8c, 0x07, 0x00, 0x00, 0x03, 0xc1, 0x00, 0x28, 
0x80, 0x70, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x40, 0xf8, 0xf7, 0xbd, 
0xe7, 0x00, 0x07, 0x88, 0x8e, 0x0e, 0x06, 0x0e, 0x0e, 0x00, 0x1d, 0x22, 0x44, 0x88, 0x04, 0x48, 
0x18, 0xc3, 0xb9, 0xc0, 0x30, 0x00, 0x0c, 0x18, 0xe0, 0x19, 0xf0, 0xd3, 0xce, 0x1c, 0xf3, 0xc0, 
0x00, 0x00, 0x81, 0x04, 0x00, 0x48, 0x00, 

0x01, 0x21, 0x02, 0x04, 0x08, 0x10, 0x00, 0x00, 0xe0, 0x82, 0x08, 0x80, 0x58, 0x30, 0x1c, 0x1c, 
0x42, 0x95, 0x10, 0x01, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x51, 0xcc, 0x44, 0x89, 
0x4c, 0x00, 0x00, 0x8e, 0x0c, 0x38, 0x70, 0x20, 0xe1, 0x87, 0x86, 0x1c, 0x00, 0x00, 0x00, 0x08, 
0xe1, 0x84, 0x91, 0x44, 0x41, 0x0c, 0x51, 0x22, 0x49, 0x18, 0x66, 0x48, 0x89, 0x22, 0x24, 0x48, 
0x88, 0xa2, 0x92, 0x94, 0x41, 0x24, 0x42, 0x00, 0x40, 0x40, 0x01, 0x00, 0x80, 0x20, 0x02, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x04, 0x90, 0x04, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xc0, 0x44, 0x90, 0xa8, 0xb0, 0x00, 0x05, 0xae, 0x80, 0x45, 
0x00, 0xd0, 0x13, 0x03, 0x13, 0x13, 0x88, 0x06, 0x0c, 0x18, 0x30, 0x50, 0xa0, 0xc1, 0x04, 0x21, 
0x08, 0x49, 0x24, 0x4c, 0x91, 0x11, 0x19, 0x11, 0x11, 0x00, 0x22, 0x22, 0x44, 0x89, 0x14, 0x48, 
0x24, 0x00, 0x00, 0x0a, 0x60, 0x00, 0x00, 0x00, 0x02, 0x80, 0x0a, 0xa0, 0x00, 0x00, 0x00, 0x09, 
0x00, 0x00, 0x00, 0x0c, 0x28, 0x88, 0x28, 

0x01, 0x20, 0x61, 0xe1, 0xc3, 0x00, 0x08, 0x21, 0xf0, 0x81, 0xc7, 0x00, 0x68, 0x30, 0x1e, 0x3c, 
0x42, 0x8d, 0x10, 0x01, 0x04, 0x10, 0x22, 0x00, 0x00, 0x00, 0x00, 0x25, 0x52, 0x12, 0x84, 0x8a, 
0x3e, 0x10, 0x00, 0x91, 0x14, 0x04, 0x08, 0x61, 0x02, 0x00, 0x89, 0x22, 0x00, 0x40, 0x10, 0x0b, 
0x11, 0x44, 0xa0, 0x42, 0x41, 0x10, 0x11, 0x22, 0x51, 0x14, 0xa5, 0x50, 0x49, 0x41, 0x24, 0x40, 
0x88, 0x92, 0xaa, 0x62, 0x81, 0x24, 0x45, 0x00, 0x1e, 0x59, 0xcf, 0x19, 0xcf, 0x38, 0x92, 0x53, 
0xb1, 0xc3, 0x16, 0x79, 0xcf, 0x92, 0x8c, 0x4c, 0x63, 0xe4, 0x90, 0x04, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x71, 0x0f, 0x28, 0x90, 0x12, 0x48, 0x00, 0x05, 0x22, 0x88, 0xf8, 
0x25, 0xd0, 0x14, 0x81, 0x21, 0x21, 0x10, 0x45, 0x0a, 0x14, 0x28, 0x50, 0xa1, 0x42, 0x04, 0x21, 
0x08, 0x49, 0x24, 0x2a, 0xa0, 0xa0, 0xa0, 0xa0, 0xa0, 0xa1, 0x45, 0x22, 0x44, 0x89, 0x12, 0x8e, 
0x25, 0xef, 0x79, 0xde, 0xf3, 0xb0, 0xe6, 0x18, 0x60, 0x89, 0x20, 0x33, 0x86, 0x18, 0x63, 0x82, 
0x08, 0x75, 0x24, 0x80, 0x02, 0x2b, 0x00, 

0x00, 0xc0, 0x91, 0x00, 0x84, 0x91, 0x0c, 0x60, 0x40, 0x82, 0x22, 0x00, 0x48, 0x30, 0x1f, 0x7c, 
0x42, 0x85, 0x10, 0x01, 0x04, 0x11, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x20, 0xfa, 0x13, 0x43, 0x02, 
0x2c, 0x10, 0x00, 0x91, 0x04, 0x04, 0x08, 0xa1, 0xc7, 0x81, 0x09, 0x22, 0x51, 0x9f, 0x8c, 0x12, 
0x4a, 0x44, 0xa0, 0x42, 0x41, 0x10, 0x11, 0x22, 0x61, 0x14, 0xa5, 0x50, 0x49, 0x41, 0x24, 0x20, 
0x88, 0x92, 0xaa, 0x42, 0x82, 0x24, 0x45, 0x00, 0x01, 0x66, 0x11, 0x24, 0x91, 0x24, 0x92, 0x92, 
0x49, 0x2c, 0x99, 0x89, 0x11, 0x12, 0x54, 0xd2, 0x94, 0x44, 0x90, 0x04, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0x09, 0x7c, 0x9c, 0x14, 0x5a, 0x5f, 0x86, 0x61, 0x08, 0x00, 
0x25, 0xd0, 0x04, 0xc9, 0x49, 0x28, 0xa4, 0x09, 0x12, 0x24, 0x28, 0x50, 0xa1, 0x42, 0x04, 0x21, 
0x08, 0x49, 0x24, 0x2a, 0xa0, 0xa0, 0xa0, 0xa0, 0xa0, 0x92, 0x49, 0x22, 0x44, 0x89, 0x12, 0x89, 
0x28, 0x10, 0x84, 0x21, 0x0c, 0x49, 0x09, 0x24, 0x93, 0x49, 0x24, 0xca, 0x59, 0x65, 0x94, 0x5d, 
0x00, 0x89, 0x24, 0x92, 0x49, 0x4c, 0xc8, 

0x00, 0x01, 0x01, 0x00, 0x84, 0x9b, 0x7e, 0xfc, 0x40, 0x82, 0x0f, 0x80, 0x49, 0xfe, 0x1e, 0x3c, 
0x42, 0x85, 0xff, 0xff, 0x04, 0x1c, 0x22, 0x00, 0x00, 0x00, 0x00, 0x20, 0x51, 0x8d, 0xad, 0x22, 
0x28, 0x10, 0x01, 0x11, 0x04, 0x08, 0x31, 0x20, 0x24, 0x41, 0x0e, 0x22, 0x06, 0x00, 0x03, 0x24, 
0xaa, 0x47, 0xa0, 0x42, 0x79, 0xf1, 0xdf, 0x22, 0x51, 0x14, 0xa5, 0x50, 0x4e, 0x41, 0x38, 0x10, 
0x88, 0x92, 0xaa, 0x61, 0x02, 0x22, 0x45, 0x00, 0x0f, 0x46, 0x11, 0x44, 0x91, 0x24, 0x93, 0x12, 
0x49, 0x28, 0x51, 0x89, 0x19, 0x12, 0x52, 0xd1, 0x14, 0x48, 0x8b, 0x24, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x83, 0x89, 0x10, 0x12, 0x12, 0x7a, 0x80, 0x81, 0x80, 0x3e, 0x00, 
0x24, 0xd0, 0x03, 0x28, 0x59, 0x47, 0x2c, 0x49, 0x12, 0x24, 0x28, 0x51, 0x11, 0xfa, 0x07, 0xbd, 
0xef, 0x49, 0x2f, 0x2a, 0xa0, 0xa0, 0xa0, 0xa0, 0xa0, 0x8c, 0x49, 0x22, 0x44, 0x89, 0x11, 0x09, 
0x24, 0xf7, 0xbd, 0xef, 0x78, 0x4a, 0x11, 0x45, 0x14, 0x49, 0x25, 0x0a, 0x50, 0xc3, 0x0c, 0x30, 
0xbe, 0x95, 0x24, 0x92, 0x49, 0x48, 0xa8, 

0x00, 0x01, 0x01, 0xe0, 0x87, 0x1b, 0x0c, 0x60, 0x40, 0x81, 0xc2, 0x00, 0x59, 0xfe, 0x1c, 0x1c, 
0x40, 0x05, 0x00, 0x41, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf8, 0x42, 0xa8, 0xc2, 
0x20, 0x7c, 0x71, 0x11, 0x04, 0x08, 0x09, 0xf0, 0x24, 0x42, 0x11, 0x1e, 0x02, 0x00, 0x02, 0x25, 
0x6b, 0xc4, 0x60, 0x42, 0x41, 0x10, 0x51, 0x22, 0x49, 0x12, 0xa5, 0x50, 0x48, 0x41, 0x28, 0x08, 
0x88, 0x8a, 0xaa, 0x61, 0x04, 0x22, 0x48, 0x80, 0x11, 0x46, 0x11, 0x7c, 0x91, 0x24, 0x92, 0x92, 
0x49, 0x28, 0x51, 0x89, 0x05, 0x12, 0x52, 0xd1, 0x14, 0x84, 0x94, 0xc4, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x81, 0x09, 0x7c, 0x0a, 0x0a, 0x42, 0x80, 0xb8, 0x00, 0x08, 0x00, 
0x24, 0x52, 0x00, 0x28, 0xa8, 0x44, 0x54, 0x4f, 0x1e, 0x3c, 0x78, 0xf1, 0xf2, 0x42, 0x04, 0x21, 
0x08, 0x49, 0x24, 0x2a, 0xa0, 0xa0, 0xa0, 0xa0, 0xa0, 0x8c, 0x51, 0x22, 0x44, 0x89, 0x11, 0x0e, 
0x23, 0x18, 0xc6, 0x31, 0x8b, 0xfa, 0x1f, 0x7d, 0xf7, 0xc9, 0x25, 0x0a, 0x50, 0xc3, 0x0c, 0x30, 
0x80, 0xa5, 0x24, 0x92, 0x49, 0x48, 0xa8, 

0x00, 0x01, 0x01, 0x00, 0x84, 0x95, 0x08, 0x20, 0x43, 0xe0, 0x22, 0x00, 0xd8, 0x30, 0x18, 0x0c, 
0xe2, 0x85, 0x00, 0x41, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x42, 0xa8, 0xc2, 
0x20, 0x10, 0x01, 0x11, 0x04, 0x10, 0x08, 0x20, 0x24, 0x42, 0x11, 0x04, 0x01, 0x9f, 0x8c, 0x04, 
0xaa, 0x44, 0x50, 0x44, 0x41, 0x08, 0x51, 0x22, 0x49, 0x13, 0x24, 0xc8, 0x88, 0x22, 0x24, 0x08, 
0x88, 0x8c, 0xcc, 0x91, 0x04, 0x22, 0x40, 0x00, 0x13, 0x47, 0x11, 0x40, 0x91, 0x24, 0x92, 0x92, 
0x49, 0x28, 0xd1, 0x89, 0x05, 0x12, 0x53, 0x32, 0x8c, 0x84, 0x90, 0x04, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x71, 0x0f, 0x10, 0x86, 0x08, 0x82, 0x40, 0x00, 0x00, 0x00, 0x00, 
0x24, 0x50, 0x00, 0x48, 0xbc, 0x88, 0x9e, 0x89, 0x12, 0x24, 0x44, 0x89, 0x12, 0x41, 0x04, 0x21, 
0x08, 0x49, 0x24, 0x49, 0x91, 0x11, 0x11, 0x11, 0x11, 0x12, 0x22, 0x22, 0x44, 0x89, 0x11, 0x08, 
0x23, 0x39, 0xce, 0x73, 0x9c, 0x43, 0x10, 0x41, 0x04, 0x09, 0x25, 0x1a, 0x51, 0xc7, 0x1c, 0x71, 
0x88, 0x4d, 0x24, 0x92, 0x48, 0xc8, 0xb0, 

0x00, 0x01, 0x01, 0x00, 0x84, 0x95, 0x00, 0x00, 0x41, 0xc2, 0x22, 0x00, 0xc0, 0x30, 0x10, 0x04, 
0x42, 0x85, 0x00, 0x41, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x53, 0x84, 0x47, 0x61, 
0x40, 0x10, 0x86, 0x0e, 0x04, 0x3c, 0x70, 0x21, 0xc3, 0x84, 0x0e, 0x18, 0x50, 0x40, 0x10, 0x22, 
0x14, 0x27, 0x8f, 0x78, 0x79, 0x07, 0x91, 0x2c, 0x45, 0xf2, 0x24, 0x47, 0x08, 0x1c, 0x24, 0x70, 
0x87, 0x08, 0x45, 0x09, 0x0f, 0xa1, 0x40, 0x00, 0x0d, 0x78, 0xcf, 0x3c, 0x8f, 0x24, 0x92, 0x52, 
0x49, 0x27, 0x1e, 0x79, 0x18, 0x8e, 0x21, 0x24, 0x49, 0xe4, 0x90, 0x07, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x23, 0xc0, 0x10, 0x82, 0x07, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 
0x3a, 0x50, 0x80, 0x01, 0x09, 0x1c, 0x84, 0x90, 0xa1, 0x42, 0x87, 0x8a, 0x0c, 0x38, 0xf7, 0xbd, 
0xef, 0x49, 0x27, 0x88, 0x8e, 0x0e, 0x0e, 0x0e, 0x0e, 0x21, 0x5c, 0x1c, 0x38, 0x70, 0xe1, 0x08, 
0x2c, 0xd6, 0xb5, 0xad, 0x6b, 0xb8, 0xef, 0x3c, 0xf3, 0xc9, 0x24, 0xe2, 0x4e, 0x38, 0xe3, 0x8e, 
0x00, 0xb0, 0xe3, 0x8e, 0x38, 0x8f, 0x10, 

0x00, 0x00, 0x91, 0x00, 0x84, 0x95, 0x00, 0x00, 0x40, 0x81, 0xc0, 0x00, 0x00, 0x30, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x41, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 
0x40, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x01, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x10, 0x00, 
0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x08, 0x04, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x9c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x10, 

0x00, 0x00, 0x61, 0x00, 0x84, 0x91, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x41, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xc0, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x10, 0x02, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x20, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x40, 
};
#else
ROMDATA PEGUSHORT MyriadPro08nEng_offset_table[257] = {
0x0000,0x0006,0x000d,0x0014,0x001b,0x0022,0x0029,0x0030,0x0037,0x003e,0x0045,0x004c,0x0053,0x0059,0x005f,0x0065,
0x0072,0x0078,0x007e,0x0084,0x008a,0x0090,0x0096,0x009c,0x00a2,0x00a8,0x00ae,0x00b4,0x00ba,0x00c0,0x00c6,0x00cc,
0x00d2,0x00d9,0x00dd,0x00e1,0x00e9,0x00ef,0x00fa,0x0101,0x0103,0x0107,0x010b,0x0111,0x0119,0x011d,0x0121,0x0125,
0x0129,0x0130,0x0137,0x013e,0x0145,0x014c,0x0153,0x015a,0x0161,0x0168,0x016f,0x0173,0x0177,0x017f,0x0187,0x018f,
0x0194,0x019e,0x01a5,0x01ab,0x01b2,0x01b9,0x01c0,0x01c6,0x01cd,0x01d4,0x01d8,0x01dd,0x01e3,0x01e8,0x01f0,0x01f7,
0x01ff,0x0205,0x020d,0x0214,0x021b,0x0221,0x0228,0x022f,0x0236,0x023d,0x0244,0x024a,0x024e,0x0259,0x025d,0x0265,
0x026b,0x0271,0x0277,0x027d,0x0282,0x0288,0x028e,0x0292,0x0298,0x029e,0x02a0,0x02a3,0x02a8,0x02aa,0x02b2,0x02b8,
0x02be,0x02c4,0x02ca,0x02ce,0x02d3,0x02d7,0x02dd,0x02e3,0x02eb,0x02f1,0x02f7,0x02fc,0x0301,0x0305,0x030a,0x0312,
0x0318,0x0323,0x032e,0x0339,0x0344,0x034f,0x035a,0x0365,0x0370,0x037b,0x0386,0x0391,0x039c,0x03a7,0x03b2,0x03bd,
0x03c8,0x03d3,0x03de,0x03e9,0x03f4,0x03ff,0x040a,0x0415,0x0420,0x042b,0x0436,0x0441,0x044c,0x0457,0x0462,0x046d,
0x0478,0x047b,0x047f,0x0485,0x048b,0x0491,0x0497,0x049b,0x04a1,0x04a7,0x04b1,0x04b6,0x04bc,0x04c4,0x04c8,0x04d2,
0x04d8,0x04dd,0x04e5,0x04ea,0x04ef,0x04f5,0x04fb,0x0501,0x0505,0x050b,0x0510,0x0515,0x051b,0x0526,0x0531,0x053c,
0x0541,0x0548,0x054f,0x0556,0x055d,0x0564,0x056b,0x0575,0x057c,0x0582,0x0588,0x058e,0x0594,0x0598,0x059c,0x05a0,
0x05a4,0x05ac,0x05b3,0x05bb,0x05c3,0x05cb,0x05d3,0x05db,0x05e3,0x05eb,0x05f2,0x05f9,0x0600,0x0607,0x060e,0x0614,
0x061a,0x0620,0x0626,0x062c,0x0632,0x0638,0x063e,0x0648,0x064d,0x0653,0x0659,0x065f,0x0665,0x0668,0x066a,0x066d,
0x0670,0x0676,0x067c,0x0682,0x0688,0x068e,0x0694,0x069a,0x06a2,0x06a8,0x06ae,0x06b4,0x06ba,0x06c0,0x06c6,0x06cc,
0x06d2,
};

ROMDATA PEGUBYTE MyriadPro08nEng_data_table[2847] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x7d, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x61, 0xa0, 0x03, 0x00, 0x00, 0x02, 
0x04, 0x30, 0x08, 0x26, 0x00, 0x06, 0x84, 0x01, 0x03, 0x06, 0x80, 0x00, 0x00, 0x04, 0x04, 0x18, 
0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 
0x42, 0x8f, 0x10, 0x01, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x20, 0x91, 0x62, 0x44, 0x80, 0x00, 0x01, 
0x08, 0x49, 0x44, 0x49, 0xa0, 0x05, 0x82, 0x02, 0x04, 0x85, 0x85, 0x00, 0x00, 0x02, 0x08, 0x24, 
0x48, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0xc2, 0x85, 0x0a, 0x14, 0x28, 0x00, 0x00, 0x40, 0x85, 0x07, 0x00, 0x18, 0x38, 0x18, 0x04, 
0xe2, 0x95, 0x10, 0x01, 0x0e, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x10, 0x00, 0x00, 
0x45, 0x04, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x24, 0x00, 0x00, 0x39, 0x11, 0x70, 0x00, 0x08, 0x01, 0x00, 
0x02, 0x01, 0x80, 0x80, 0x10, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x24, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x07, 0x08, 0x10, 0xc6, 0x80, 0x48, 0x00, 0x02, 0x02, 0x18, 0x04, 0x50, 0x01, 0xa4, 
0x04, 0x31, 0xa0, 0x00, 0x00, 0x40, 0x43, 0x00, 0x12, 0x00, 0x00, 

0x01, 0x21, 0x02, 0x04, 0x08, 0x10, 0x00, 0x00, 0xe0, 0x82, 0x08, 0x80, 0x68, 0x38, 0x1c, 0x0c, 
0x42, 0x95, 0x10, 0x01, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x0a, 0x10, 0xc4, 0x18, 
0x48, 0x95, 0x00, 0x00, 0x01, 0x1c, 0x10, 0x70, 0xe0, 0x47, 0xc3, 0x1f, 0x1c, 0x38, 0x00, 0x00, 
0x00, 0x01, 0xc3, 0xe0, 0xc7, 0x87, 0xbc, 0x3e, 0xf8, 0xf4, 0x2e, 0x74, 0x50, 0xc6, 0x44, 0x71, 
0xe1, 0xc7, 0x83, 0xdf, 0x42, 0x45, 0x25, 0x12, 0x2f, 0xa1, 0x11, 0x10, 0x80, 0x04, 0x01, 0x00, 
0x02, 0x02, 0x00, 0x82, 0x50, 0x80, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x22, 0x00, 0x12, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0x00, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 
0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 
0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x08, 
0x41, 0xc0, 0x44, 0x8f, 0x28, 0x7c, 0x70, 0x00, 0x00, 0x3e, 0x00, 0x60, 0x83, 0x18, 0x40, 0x0f, 
0x00, 0x04, 0x60, 0x04, 0x20, 0x84, 0x38, 0x82, 0x18, 0x30, 0x60, 0xc1, 0x83, 0x07, 0xf1, 0xef, 
0xbe, 0xfb, 0xee, 0xee, 0xe7, 0x8c, 0x47, 0x07, 0x07, 0x07, 0x07, 0x00, 0x07, 0x50, 0xa1, 0x42, 
0x84, 0x8a, 0x08, 0x84, 0x21, 0x25, 0x94, 0x48, 0x00, 0x01, 0x04, 0x24, 0xa2, 0xad, 0x31, 0x62, 
0x08, 0x49, 0x65, 0x00, 0x00, 0x20, 0x84, 0x94, 0x22, 0x05, 0x00, 

0x01, 0x20, 0x61, 0xe1, 0xc3, 0x00, 0x08, 0x21, 0xf0, 0x81, 0xc8, 0x80, 0x58, 0x38, 0x1e, 0x1c, 
0x42, 0x95, 0x10, 0x01, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x0a, 0x3d, 0x24, 0x24, 
0x48, 0x8e, 0x08, 0x00, 0x01, 0x22, 0x30, 0x89, 0x10, 0xc4, 0x04, 0x01, 0x22, 0x44, 0x00, 0x04, 
0x00, 0x80, 0x24, 0x10, 0xc4, 0x48, 0x22, 0x20, 0x81, 0x04, 0x24, 0x14, 0x90, 0xc6, 0x64, 0x89, 
0x12, 0x24, 0x44, 0x04, 0x42, 0x45, 0x55, 0x12, 0x20, 0xa3, 0xff, 0x91, 0x40, 0x00, 0x01, 0x00, 
0x02, 0x02, 0x00, 0x80, 0x10, 0x80, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x22, 0x00, 0x12, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x00, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x00, 
0x42, 0x00, 0x28, 0x90, 0x00, 0x82, 0x08, 0x00, 0x00, 0x41, 0x00, 0x90, 0x80, 0x84, 0x00, 0x1d, 
0x00, 0x0c, 0x90, 0x0c, 0x41, 0x88, 0x05, 0x00, 0x18, 0x30, 0x60, 0xc1, 0x83, 0x05, 0x02, 0x08, 
0x20, 0x82, 0x04, 0x44, 0x44, 0x4c, 0x48, 0x88, 0x88, 0x88, 0x88, 0x80, 0x08, 0x90, 0xa1, 0x42, 
0x84, 0x8a, 0x08, 0x80, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x00, 
0x00, 0x00, 0x00, 0x04, 0x02, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 

0x00, 0xc0, 0x91, 0x00, 0x84, 0x91, 0x0c, 0x60, 0x40, 0x82, 0x27, 0x00, 0x69, 0xff, 0x1f, 0x3c, 
0x42, 0x8d, 0x10, 0x01, 0x04, 0x10, 0x22, 0x00, 0x00, 0x00, 0x00, 0x20, 0x3f, 0x51, 0x28, 0x24, 
0x10, 0x55, 0x08, 0x00, 0x02, 0x22, 0x10, 0x08, 0x11, 0x44, 0x08, 0x02, 0x22, 0x44, 0x88, 0x18, 
0x00, 0x60, 0x29, 0xc9, 0x24, 0x50, 0x21, 0x20, 0x82, 0x04, 0x24, 0x15, 0x10, 0xaa, 0x55, 0x05, 
0x14, 0x14, 0x44, 0x04, 0x42, 0x45, 0x54, 0xa1, 0x41, 0x20, 0xaa, 0x12, 0x20, 0x00, 0x39, 0xe3, 
0x9e, 0x73, 0x9e, 0xf2, 0xd2, 0xbb, 0x3c, 0x73, 0xc7, 0xa9, 0xdd, 0x14, 0x52, 0x51, 0x45, 0xe2, 
0x22, 0x00, 0x12, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x00, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x08, 
0xf2, 0x11, 0x28, 0x90, 0x01, 0x39, 0x38, 0xa0, 0x00, 0x9c, 0x80, 0x90, 0x80, 0x88, 0x04, 0x5d, 
0x20, 0x04, 0x95, 0x04, 0x40, 0x88, 0x19, 0x02, 0x24, 0x48, 0x91, 0x22, 0x44, 0x89, 0x04, 0x08, 
0x20, 0x82, 0x04, 0x44, 0x44, 0x2a, 0x50, 0x50, 0x50, 0x50, 0x50, 0x44, 0x51, 0x50, 0xa1, 0x42, 
0x84, 0x53, 0xcb, 0x1c, 0x71, 0xc7, 0x1c, 0x71, 0xdc, 0x73, 0x8e, 0x38, 0xe2, 0x92, 0x0b, 0xc7, 
0x1c, 0x71, 0xc7, 0x04, 0x1c, 0x8a, 0x28, 0xa2, 0x8b, 0xc8, 0x80, 

0x00, 0x01, 0x01, 0x00, 0x84, 0x9b, 0x7e, 0xfc, 0x40, 0x82, 0x02, 0x00, 0x49, 0xff, 0x1e, 0x7c, 
0x42, 0x85, 0x10, 0x01, 0x04, 0x11, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x20, 0x14, 0x50, 0xc8, 0x19, 
0x10, 0x44, 0x08, 0x00, 0x02, 0x22, 0x10, 0x10, 0x62, 0x47, 0x8f, 0x02, 0x1c, 0x44, 0x88, 0x61, 
0xfc, 0x18, 0x4a, 0x49, 0x27, 0x90, 0x21, 0x3c, 0xfa, 0x07, 0xe4, 0x16, 0x10, 0xaa, 0x55, 0x05, 
0x14, 0x14, 0x43, 0x84, 0x42, 0x29, 0x54, 0x41, 0x42, 0x20, 0xaa, 0x14, 0x10, 0x00, 0x05, 0x14, 
0x22, 0x8a, 0x22, 0x8a, 0x54, 0xa4, 0xa2, 0x8a, 0x28, 0xb2, 0x11, 0x14, 0x52, 0x4a, 0x44, 0x22, 
0x22, 0x18, 0x92, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x00, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 
0x42, 0x0e, 0x10, 0x0e, 0x01, 0x41, 0x49, 0x40, 0x00, 0x92, 0x80, 0x67, 0xf1, 0x04, 0x04, 0x5d, 
0x20, 0x04, 0x92, 0x84, 0x90, 0x96, 0x06, 0x42, 0x24, 0x48, 0x91, 0x22, 0x44, 0x89, 0xe4, 0x0f, 
0x3c, 0xf3, 0xc4, 0x44, 0x4f, 0x2a, 0x50, 0x50, 0x50, 0x50, 0x50, 0x42, 0x92, 0x50, 0xa1, 0x42, 
0x84, 0x52, 0x28, 0x82, 0x08, 0x20, 0x82, 0x08, 0x22, 0x84, 0x51, 0x45, 0x12, 0x92, 0x7a, 0x28, 
0xa2, 0x8a, 0x28, 0x80, 0x26, 0x8a, 0x28, 0xa2, 0x8a, 0x28, 0x80, 

0x00, 0x01, 0x01, 0xe0, 0x87, 0x1b, 0x0c, 0x60, 0x40, 0x81, 0xcf, 0x80, 0x49, 0xff, 0x1c, 0x3c, 
0x42, 0x85, 0xff, 0xff, 0x04, 0x1c, 0x22, 0x00, 0x00, 0x00, 0x00, 0x20, 0x14, 0x38, 0x13, 0x25, 
0x10, 0x40, 0x7f, 0x07, 0x02, 0x22, 0x10, 0x20, 0x13, 0xe0, 0x48, 0x84, 0x22, 0x3c, 0x00, 0x80, 
0x00, 0x04, 0x8a, 0x49, 0x24, 0x50, 0x21, 0x20, 0x82, 0x74, 0x24, 0x16, 0x10, 0x92, 0x55, 0x05, 
0xe4, 0x17, 0x80, 0x44, 0x42, 0x29, 0x54, 0x40, 0x82, 0x20, 0xaa, 0x10, 0x00, 0x00, 0x3d, 0x14, 
0x22, 0xfa, 0x22, 0x8a, 0x58, 0xa4, 0xa2, 0x8a, 0x28, 0xa3, 0x11, 0x12, 0x95, 0x44, 0x28, 0x4c, 
0x21, 0xa4, 0x92, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x00, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 
0x47, 0x8a, 0x10, 0x11, 0x01, 0x41, 0x3a, 0x87, 0xee, 0x9c, 0x80, 0x00, 0x83, 0x98, 0x04, 0x4d, 
0x00, 0x0e, 0x61, 0x44, 0xb0, 0x91, 0x3a, 0xc4, 0x24, 0x48, 0x91, 0x22, 0x44, 0x8f, 0x04, 0x08, 
0x20, 0x82, 0x04, 0x44, 0x44, 0x29, 0x50, 0x50, 0x50, 0x50, 0x50, 0x41, 0x12, 0x50, 0xa1, 0x42, 
0x84, 0x22, 0x28, 0x9e, 0x79, 0xe7, 0x9e, 0x79, 0xfe, 0x87, 0xdf, 0x7d, 0xf2, 0x92, 0x8a, 0x28, 
0xa2, 0x8a, 0x28, 0xbf, 0xaa, 0x8a, 0x28, 0xa2, 0x52, 0x25, 0x00, 

0x00, 0x01, 0x01, 0x00, 0x84, 0x95, 0x08, 0x20, 0x43, 0xe0, 0x22, 0x00, 0x58, 0x38, 0x18, 0x1c, 
0x40, 0x05, 0x00, 0x41, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x7e, 0x14, 0x14, 0xa2, 
0x10, 0x40, 0x08, 0x00, 0x02, 0x22, 0x10, 0x40, 0x10, 0x40, 0x48, 0x84, 0x22, 0x04, 0x00, 0x61, 
0xfc, 0x18, 0x8a, 0x4b, 0xf4, 0x50, 0x21, 0x20, 0x82, 0x14, 0x24, 0x15, 0x10, 0x92, 0x55, 0x05, 
0x04, 0x14, 0x80, 0x44, 0x42, 0x29, 0x98, 0xa0, 0x84, 0x20, 0x44, 0x10, 0x00, 0x00, 0x45, 0x14, 
0x22, 0x82, 0x22, 0x8a, 0x54, 0xa4, 0xa2, 0x8a, 0x28, 0xa0, 0xd1, 0x12, 0x95, 0x44, 0x28, 0x82, 
0x22, 0x23, 0x12, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x00, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 
0x42, 0x0e, 0x7c, 0x91, 0x01, 0x41, 0x01, 0x40, 0x20, 0x94, 0x80, 0x00, 0x80, 0x00, 0x04, 0x45, 
0x00, 0x00, 0x02, 0x81, 0x50, 0x21, 0x05, 0x48, 0x7e, 0xfd, 0xfb, 0xf7, 0xef, 0xd1, 0x04, 0x08, 
0x20, 0x82, 0x04, 0x44, 0x44, 0x29, 0x50, 0x50, 0x50, 0x50, 0x50, 0x42, 0x94, 0x50, 0xa1, 0x42, 
0x84, 0x23, 0xc8, 0xa2, 0x8a, 0x28, 0xa2, 0x8a, 0x20, 0x84, 0x10, 0x41, 0x02, 0x92, 0x8a, 0x28, 
0xa2, 0x8a, 0x28, 0x80, 0x2a, 0x8a, 0x28, 0xa2, 0x52, 0x25, 0x00, 

0x00, 0x01, 0x01, 0x00, 0x84, 0x95, 0x00, 0x00, 0x41, 0xc2, 0x22, 0x00, 0xd8, 0x38, 0x10, 0x0c, 
0xe2, 0x85, 0x00, 0x41, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x14, 0x24, 0xa3, 
0x10, 0x40, 0x08, 0x20, 0x22, 0x22, 0x10, 0x81, 0x10, 0x44, 0x48, 0x88, 0x22, 0x08, 0x88, 0x18, 
0x00, 0x60, 0x09, 0xf2, 0x14, 0x48, 0x22, 0x20, 0x81, 0x14, 0x24, 0x14, 0x90, 0x82, 0x4c, 0x89, 
0x02, 0x24, 0x40, 0x44, 0x42, 0x10, 0x89, 0x10, 0x88, 0x20, 0x44, 0x10, 0x00, 0x00, 0x45, 0x14, 
0x22, 0x8a, 0x22, 0x8a, 0x52, 0xa4, 0xa2, 0x8a, 0x28, 0xa0, 0x51, 0x11, 0x08, 0x8a, 0x11, 0x02, 
0x22, 0x00, 0x12, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x00, 0x10, 0x22, 0x04, 0x40, 0x88, 
0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 
0x81, 0x10, 0x22, 0x04, 0x40, 0x88, 0x11, 0x02, 0x20, 0x44, 0x08, 0x81, 0x10, 0x22, 0x04, 0x09, 
0x42, 0x11, 0x10, 0x8e, 0x01, 0x39, 0x00, 0xa0, 0x20, 0x92, 0x80, 0x07, 0xf0, 0x00, 0x04, 0xc5, 
0x00, 0x00, 0x05, 0x01, 0x78, 0x22, 0x05, 0xe8, 0x42, 0x85, 0x0a, 0x14, 0x28, 0x51, 0x02, 0x08, 
0x20, 0x82, 0x04, 0x44, 0x44, 0x48, 0xc8, 0x88, 0x88, 0x88, 0x88, 0x84, 0x48, 0x90, 0xa1, 0x42, 
0x84, 0x22, 0x08, 0xa2, 0x8a, 0x28, 0xa2, 0x8a, 0x32, 0x84, 0x51, 0x45, 0x12, 0x92, 0x8a, 0x28, 
0xa2, 0x8a, 0x28, 0x84, 0x32, 0x8a, 0x28, 0xa2, 0x22, 0x22, 0x00, 

0x00, 0x00, 0x91, 0x00, 0x84, 0x95, 0x00, 0x00, 0x40, 0x81, 0xc2, 0x00, 0xc0, 0x38, 0x00, 0x04, 
0x42, 0x85, 0x00, 0x41, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x28, 0x78, 0x23, 0x1c, 
0x88, 0x80, 0x08, 0x20, 0x24, 0x1c, 0x38, 0xf8, 0xe0, 0x43, 0x87, 0x08, 0x1c, 0x30, 0x88, 0x04, 
0x00, 0x80, 0x84, 0x02, 0x17, 0x87, 0xbc, 0x3e, 0x80, 0xf4, 0x2e, 0xe4, 0x5e, 0x82, 0x44, 0x71, 
0x01, 0xc4, 0x27, 0x84, 0x3c, 0x10, 0x01, 0x10, 0x8f, 0xa0, 0x44, 0x10, 0x00, 0x00, 0x3d, 0xe3, 
0x9e, 0x72, 0x1e, 0x8a, 0x51, 0xa4, 0xa2, 0x73, 0xc7, 0xa3, 0x8c, 0xf1, 0x08, 0x91, 0x11, 0xe2, 
0x22, 0x00, 0x1e, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0x00, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 
0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 
0xff, 0x1f, 0xe3, 0xfc, 0x7f, 0x8f, 0xf1, 0xfe, 0x3f, 0xc7, 0xf8, 0xff, 0x1f, 0xe3, 0xfc, 0x08, 
0xf7, 0xc0, 0x10, 0x81, 0x00, 0x82, 0x00, 0x00, 0x20, 0x41, 0x00, 0x00, 0x00, 0x00, 0x07, 0x45, 
0x00, 0x00, 0x00, 0x02, 0x10, 0x47, 0x08, 0x47, 0x42, 0x85, 0x0a, 0x14, 0x28, 0x51, 0xf1, 0xef, 
0xbe, 0xfb, 0xee, 0xee, 0xe7, 0x88, 0xc7, 0x07, 0x07, 0x07, 0x07, 0x00, 0x17, 0x0f, 0x1e, 0x3c, 
0x78, 0x22, 0x0b, 0x1e, 0x79, 0xe7, 0x9e, 0x79, 0xcc, 0x73, 0x8e, 0x38, 0xe2, 0x92, 0x72, 0x27, 
0x1c, 0x71, 0xc7, 0x04, 0x1c, 0x79, 0xe7, 0x9e, 0x23, 0xc2, 0x00, 

0x00, 0x00, 0x61, 0x00, 0x84, 0x95, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x41, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x08, 0x80, 0x00, 0x20, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 
0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x00, 0x40, 0x00, 0x00, 0x02, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x20, 0x02, 
0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x81, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x04, 0x05, 
0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x42, 0x04, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x41, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x05, 0x00, 0x00, 0x40, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x70, 0x07, 0xe0, 0x00, 0x00, 
0x00, 0x00, 0x1c, 0x00, 0x80, 0x00, 0x00, 0x02, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x20, 0x01, 
0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x40, 0x00, 0x00, 0x9e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x05, 
0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x04, 0x00, 
};
#endif

#if defined(_FONT_PLASTIMO_)
xFONTYY MyriadPro08nEng_Font = {0x01, 10, 3, 13, 0, 0, 13, 167, 0x0000, 0x00ff,
(PEGUSHORT *) MyriadPro08nEng_offset_table,&MyriadPro08nExt_Font,
(PEGUBYTE *) MyriadPro08nEng_data_table};
#else
xFONTYY MyriadPro08nEng_Font = {0x01, 11, 2, 13, 0, 0, 13, 219, 0x0000, 0x00ff,
(PEGUSHORT *) MyriadPro08nEng_offset_table,&MyriadPro08nExt_Font,
(PEGUBYTE *) MyriadPro08nEng_data_table};
#endif

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

