#include <stdio.h>
#include "ExtPwdChkWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Font.h"

#define EXT_PWD_CHK_WND_X_POS		10
#define EXT_PWD_CHK_WND_Y_POS		54

#define EXT_PWD_CHK_WND_W			580
#define EXT_PWD_CHK_WND_H			370

#define EXT_PWD_CHK_WND_ROW_H		37

#define EXT_PWD_CHK_WND_CAP_X_POS		(EXT_PWD_CHK_WND_X_POS + 10)
#define EXT_PWD_CHK_WND_CHK_X_POS		(EXT_PWD_CHK_WND_X_POS + 10)
#define EXT_PWD_CHK_WND_EDIT_X_POS		(EXT_PWD_CHK_WND_X_POS + 50)

#define EXT_PWD_CHK_WND_CHK_H			18
#define EXT_PWD_CHK_WND_EDIT_W			130
#define EXT_PWD_CHK_WND_EDIT_H			30

#define MAX_PWD_CHAR_LEN    6


extern CDocMgr *g_pDocMgr;

/*********************************************************************************************************/
// Name		: CExtPwdChkWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CExtPwdChkWnd::CExtPwdChkWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	m_nFocus        = FOCUS_CHK_MASTER;
	m_nPasswordType = PASSWORD_USER;
	
	InitControls(pScreen);
	m_pPassword->SetFocus(FALSE);
	m_pChkMaster->SetFocus(TRUE);
}

/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::InitControls(cSCREEN *pScreen)
{
	if(pScreen != NULL)
	{
		m_pChkMaster  = new CCheckCtrl(pScreen);
		m_pChkMaster->Create(	EXT_PWD_CHK_WND_CHK_X_POS, 
								EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*3 + (EXT_PWD_CHK_WND_ROW_H - EXT_PWD_CHK_WND_CHK_H)/2,
								(const BYTE **)STR_SEL_MASTER_MODE);
		
		m_pPassword     = new CEditCtrl(pScreen);
		m_pPassword->Create(EXT_PWD_CHK_WND_EDIT_X_POS, 
							EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*5 + (EXT_PWD_CHK_WND_ROW_H - EXT_PWD_CHK_WND_EDIT_H)/2,
							EXT_PWD_CHK_WND_EDIT_W, 
							EXT_PWD_CHK_WND_EDIT_H, MAX_PWD_CHAR_LEN, 1, 1);
	
		
	}
}

/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::DrawWnd(BOOL bRedraw)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 2;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 4;
			break;
			
		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 4;
			break;
	}
	
	CWnd::DrawWnd(bRedraw);
	
	if( bRedraw )
	{
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;
		nXPos = EXT_PWD_CHK_WND_CAP_X_POS;
		nYPos = EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*4 + (EXT_PWD_CHK_WND_ROW_H - nFontH)/2 + nYOffset;
		
		pUniCodeStr = (HWORD *)STR_ENTER_PASSWORD[nLangMode];
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
		
		m_pScreen->SetFont(pOldFont);
		
		DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
		EraseButton(1);
		EraseButton(2);
		EraseButton(3);
	}

	m_pChkMaster->DrawWnd();
	m_pPassword->DrawWnd();
}

/*********************************************************************************************************/
// Name		: OnCursorEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::OnCursorEvent(int nState)
{
	if(m_nFocus == FOCUS_EDIT_PWD)
	{
		m_pPassword->OnCursorEvent(nState);
	}		
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
#if 0
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &MyriadPro24bEng;
			nYOffset = 2;
			break;

		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 2;
			break;
	}
#endif	
	
	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
		case KBD_SCAN_CODE_DOWN:	
			if( m_nFocus == FOCUS_EDIT_PWD)
			{
				m_pPassword->SetEditMode(FALSE);
				m_pPassword->Reset();
				m_pPassword->SetFocus(FALSE);
				
				m_pChkMaster->SetFocus(TRUE);
				m_nFocus = FOCUS_CHK_MASTER;
			}
			else
			{
				m_pPassword->Reset();
				m_pPassword->SetFocus(TRUE);
				m_pPassword->SetEditMode(TRUE);
				
				m_pChkMaster->SetFocus(FALSE);
				m_nFocus = FOCUS_EDIT_PWD;
				
			}
			DrawWnd();
			break;


		default:
			switch(m_nFocus)
			{
				case FOCUS_CHK_MASTER:
					m_pChkMaster->OnKeyEvent(nKey,nFlags);
					DrawWnd(FALSE);
					break;

				case FOCUS_EDIT_PWD:
					m_pScreen->FillRect(EXT_PWD_CHK_WND_X_POS,
										EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*7,
										EXT_PWD_CHK_WND_X_POS + EXT_PWD_CHK_WND_W-1,
										EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);
					m_pPassword->OnKeyEvent(nKey, nFlags);
					break;
			}
			break;
	}
}	

/*********************************************************************************************************/
// Name		: DrawErrorMsg
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::DrawErrorMsg()
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0, nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
		pFont = &NewGulLim18bCJK;
		nYOffset = 2;
		break;

		case LANG_RUS:
		pFont = &MyriadPro24bRus;
		nYOffset = 4;
		break;

		default:
		pFont = &MyriadPro24bEng;
		nYOffset = 4;
		break;
	}
	
	pOldFont = m_pScreen->SetFont(pFont);
	nFontH = pFont->uHeight;


	pUniCodeStr = (HWORD *)STR_UNI_WRONG_PWD[nLangMode];
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);

	nXPos = EXT_PWD_CHK_WND_X_POS + (EXT_PWD_CHK_WND_W - nStrW)/2;
	nYPos = EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*7 + (EXT_PWD_CHK_WND_ROW_H - pFont->uHeight)/2;

	m_pScreen->FillRect(EXT_PWD_CHK_WND_X_POS,
						EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*7,
						EXT_PWD_CHK_WND_X_POS + EXT_PWD_CHK_WND_W-1,
						EXT_PWD_CHK_WND_Y_POS + EXT_PWD_CHK_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);

	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crWarn);

	m_pScreen->SetFont(pOldFont);

}


/*********************************************************************************************************/
// Name		: SetPasswordType
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::SetPasswordType(int nPasswordType) 
{
	m_nPasswordType = nPasswordType; 
}

/*********************************************************************************************************/
// Name		: IsPasswordOK
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
BOOL CExtPwdChkWnd::IsPasswordOK()
{
	BYTE  szPassword[8];
	DWORD dPassword, dUserPassword;

	m_pPassword->GetText(szPassword);
	sscanf((char *)szPassword, "%d", &dPassword);

	dUserPassword = g_pDocMgr->GetUserPassword();

	switch( m_nPasswordType )
	{
		case PASSWORD_USER:
			if(dPassword == dUserPassword) 
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(0);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetManufacturerPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
			
		case PASSWORD_USER_MASTER:
			if( (dPassword == dUserPassword) || (dPassword == g_pDocMgr->GetUserMasterPassword())) 
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(0);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetManufacturerPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
			
		case PASSWORD_MASTER:
			if( dPassword == g_pDocMgr->GetMasterPassword() ) 
			{
				g_pDocMgr->SetLoginWithUserPassword(0);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(0);
				return 1;
			}
			else if(dPassword == g_pDocMgr->GetManufacturerPassword())
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
			
		case PASSWORD_MANUFACTURER:
			if( dPassword == g_pDocMgr->GetManufacturerPassword() ) 
			{
				g_pDocMgr->SetLoginWithUserPassword(1);
				g_pDocMgr->SetLoginWithMasterPassword(1);
				g_pDocMgr->SetLoninWithManufaturerPassword(1);
				return 1;
			}
			break;
		
		case PASSWORD_GBCODE:
			if( dPassword == g_pDocMgr->GetGBCodePassword() ) 
			{
				g_pDocMgr->SetLoginWithGBCodePassword(1);
				return 1;
			}
			else
			{
				return 0;
			}
			break;

		case PASSWORD_MASTER_OR_USER:
			if(m_pChkMaster->GetCheck() == TRUE)
			{
				if(dPassword == g_pDocMgr->GetMasterPassword())
				{
					g_pDocMgr->SetLoginWithUserPassword(0);
					g_pDocMgr->SetLoginWithMasterPassword(1);
					g_pDocMgr->SetLoninWithManufaturerPassword(0);
					return 1;
				}
				else if(dPassword == g_pDocMgr->GetManufacturerPassword())
				{
					g_pDocMgr->SetLoginWithUserPassword(0);
					g_pDocMgr->SetLoginWithMasterPassword(0);
					g_pDocMgr->SetLoninWithManufaturerPassword(1);
					return 1;
				}
			}
			else
			{
				if(dPassword == dUserPassword) 
				{
					g_pDocMgr->SetLoginWithUserPassword(1);
					g_pDocMgr->SetLoginWithMasterPassword(0);
					g_pDocMgr->SetLoninWithManufaturerPassword(0);
					return 1;
				}
				else if(dPassword == g_pDocMgr->GetManufacturerPassword())
				{
					g_pDocMgr->SetLoginWithUserPassword(0);
					g_pDocMgr->SetLoginWithMasterPassword(0);
					g_pDocMgr->SetLoninWithManufaturerPassword(1);
					return 1;
				}
			}
			break;
	}

	return 0;
}

/*********************************************************************************************************/
// Name		: SetFocus
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::SetFocus(int nFocus) 
{
	m_nFocus = nFocus; 
}

/*********************************************************************************************************/
// Name		: ResetPasswordBox
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CExtPwdChkWnd::ResetPasswordBox() 
{
	m_pPassword->Reset();
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CExtPwdChkWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);
	return nResult;
}

