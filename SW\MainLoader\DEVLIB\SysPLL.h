/*...........................................................................*/
/*.                  File Name : SYSPLL.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSPLL_H__
#define  __SYSPLL_H__

//=============================================================================
#if defined(__SPICA__)
    #define  PLL_CLKSELCPU0_PLL0             0   // PLL0
    #define  PLL_CLKSELCPU0_PLL1             1   // PLL1

    #define  PLL_CLKSELMCLK_PLL0             0   // PLL0
    #define  PLL_CLKSELMCLK_PLL1             1   // PLL1
    #define  PLL_CLKSELMCLK_FCLK             3   // FCLK
#endif
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SysInitPLLs(void);
void  SysDoPLLChange(void);
DWORD SysIsPLLStable(void);
void  SysSetPLLCPU0(void);
void  SysSetPLLCPU1(void);
void  SysSetPLLSdRamDIV(void);
void  SysSetPLLBCLK(void);
void  SysSetPLL1PowerDownMode(int nDisableEnableMode);
void  SysSetPLL2PowerDownMode(int nDisableEnableMode);
void  SysSetPLL0DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV);
void  SysSetPLL1DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV);
void  SysSetPLL2DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV);

#ifdef  __cplusplus
}
#endif

#endif

