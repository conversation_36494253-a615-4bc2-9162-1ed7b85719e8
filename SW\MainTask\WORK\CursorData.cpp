/*...........................................................................*/
/*.                  File Name : CursorData.cpp                             .*/
/*.                                                                         .*/
/*.                       Date : 2008.11.01                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "DataType.h"
//#include "DataType.hpp"
#include "_AllStringY.hpp"
#include "AllVarY.hpp"
//#include "AllResource.hpp"
#include "CpuAddr.h"
#include "SysConst.h"
#include "AllConst.h"
#include "KeyConst.h"
#include "SysLib.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "GrLib.h"
#include "datum.hpp"
#include "KeyBD.hpp"
#include "DataBack.hpp"
//#include "MainWin.hpp"

#ifdef  __SAMYUNG__
  #include "SamMapLib.h"
#endif

#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
    //#include "WatchData.cpp"
    //#include "SymViewWin.cpp"
#endif

#include "CursorData.hpp"


#ifdef _SI_70AM_
#include "Uart.hpp"
#include "DocMgr.hpp"

#define DEG_CHAR 0x08
extern CDocMgr *g_pDocMgr;
extern cUART *G_pUart3;
#endif
//===========================================================================
#define  CURSOR_IMAGE_SIZE_X                            24
#define  CURSOR_IMAGE_SIZE_Y                            24
//===========================================================================
#define  CURSOR_BACK_MOVE_STEP                           0
//===========================================================================

//===========================================================================
static UCHAR G_vCusorMarkData[CURSOR_IMAGE_SIZE_X * CURSOR_IMAGE_SIZE_Y] = {
              0,0,0,0,0,0,0,0,0,2,2,2,2,2,2,0,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,2,1,1,1,1,2,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,1,1,1,1,1,1,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,1,1,1,1,1,1,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,2,2,1,1,2,2,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,0,0,2,1,1,2,0,0,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,0,0,2,1,1,2,0,0,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,0,0,2,1,1,2,0,0,0,0,0,0,0,0,0,0,
              0,2,2,2,2,0,0,0,0,0,2,1,1,2,0,0,0,0,0,2,2,2,2,0,
              2,2,1,1,2,0,0,0,0,0,2,1,1,2,0,0,0,0,0,2,1,1,2,2,
              2,1,1,1,2,2,2,2,2,2,2,1,1,2,2,2,2,2,2,2,1,1,1,2,
              2,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,
              2,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,
              2,1,1,1,2,2,2,2,2,2,2,1,1,2,2,2,2,2,2,2,1,1,1,2,
              2,2,1,1,2,0,0,0,0,0,2,1,1,2,0,0,0,0,0,2,1,1,2,2,
              0,2,2,2,2,0,0,0,0,0,2,1,1,2,0,0,0,0,0,2,2,2,2,0,
              0,0,0,0,0,0,0,0,0,0,2,1,1,2,0,0,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,0,0,2,1,1,2,0,0,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,0,0,2,1,1,2,0,0,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,2,2,1,1,2,2,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,1,1,1,1,1,1,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,1,1,1,1,1,1,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,2,2,1,1,1,1,2,2,0,0,0,0,0,0,0,0,
              0,0,0,0,0,0,0,0,0,2,2,2,2,2,2,0,0,0,0,0,0,0,0,0};
//===========================================================================

/*********************************************************************************************************/
// Name		: cCursorData
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
cCursorData::cCursorData(CWnd *pParent, cSCREEN *pScreen, sRect clientRect)
{
	int	nMaxWidth;
	DWORD dVirtScrnWidth,dVirtScrnHeight;


	dVirtScrnWidth = SysGetVirtScreenWidth();
	dVirtScrnHeight= SysGetVirtScreenHeight();

	if (dVirtScrnWidth >= dVirtScrnHeight)
		nMaxWidth = dVirtScrnWidth;
	else
		nMaxWidth = dVirtScrnHeight;

#ifdef _SI_70AM_
	m_pParent 	= pParent;
	m_pScreen = pScreen;
	mClient = clientRect;
#endif	

	m_nBrgToCursor = 0;
	m_rDstToCursor = 0.0;

	m_pSaveMemMark = new CLRMENU[CURSOR_IMAGE_SIZE_X * CURSOR_IMAGE_SIZE_Y + 128];
	m_pSaveMemLine = new CLRMENU[nMaxWidth * 4];

#ifndef _SI_70AM_
	SetWinBlendMode(MODE_VAL_ON);
#endif

	this->InitAllData();
	this->SetCursorMode(MODE_VAL_OFF);
	this->SetBackMercMode(0);

#ifndef _SI_70AM_
	this->AddStatus(PSF_VIEWPORT);
	this->AddStatus(PSF_ALWAYS_ON_TOP);
	this->RemoveStatus(PSF_ACCEPTS_FOCUS);
#endif	
}

/*********************************************************************************************************/
// Name		: ~cCursorData
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
cCursorData::~cCursorData(void)
{
      delete [] m_pSaveMemMark;
      delete [] m_pSaveMemLine;
}

#ifndef _SI_70AM_
int cCursorData::Message(const sMessage &xMsg)
{
	switch (xMsg.Type)
	{
		case  PM_SHOW :
			cMyWin::Message(xMsg);
			break;
		
		case  PM_KEY :
			break;
		
		case  PM_HIDE :
			cMyWin::Message(xMsg);
			break;

		default :
			return(cMyWin::Message(xMsg));
	}
	return(0);
}

void cCursorData::Draw(const sRect &Invalid)
{
      CHAR    vTextC[128];
      UNICODE vTextW[128];
      UNICODE vTextX[128];
      PegBrush xBrushX(PegResourceManager::GetColor(CID_BLACK),PegResourceManager::GetColor(mColorId[PCI_NORMAL]), 0);
      PegPoint xPointX;
      int   nFontID;
      int   nRunJob;
      LGRID nGridLat,nGridLon;

      RunCheckLangCode();
      RunCheckNightMode();

      nRunJob = ((cChartMainWin *)m_pParent)->GetRunOtherJobs();
      if (nRunJob == OTHER_JOB_ROUTE_CREATE || nRunJob == OTHER_JOB_CALC_DIST)
          nRunJob = 1;
      else
          nRunJob = 0;

   #if defined(__N430_MODEL__)
      if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
   #else
      if (SysGetDeviceType() == DEVICE_TYPE_05_0)
   #endif
          nFontID = FID_MyriadPro10bEng;
      else
          nFontID = FID_MyriadPro12bEng;

      BeginDraw(Invalid);

      cMyWin::Draw(Invalid);

      this->CalcDstBrgToShip();

#ifdef  __PLASTIMO__
      if (nRunJob)
         {
          this->GetCurrGridPos(&nGridLat,&nGridLon);

          strcpy((char *)vTextC,GetLatChrStr(nGridLat));
          CopyCharStrToUniStr(vTextW,vTextC);
         }
      else
         {
          sprintf(vTextC,"%7.2f %s",m_rDstToCursor,GetDistUnitChrStr());
          CopyCharStrToUniStr(vTextX,vTextC);

          UniStrCpy(vTextW,(UNICODE *)G_pCursorDSTMsgX[m_nLangCode]);
          UniStrCat(vTextW,vTextX);
         }

      xPointX.Set(this->mClient.Left + 1,this->mClient.Top +  1);
      DrawText(xPointX,vTextW,xBrushX,nFontID);


      if (nRunJob)
         {
          this->GetCurrGridPos(&nGridLat,&nGridLon);

          strcpy((char *)vTextC,GetLonChrStr(nGridLon));
          CopyCharStrToUniStr(vTextW,vTextC);
         }
      else
         {
          sprintf(vTextC," %03d.%d %s",m_nBrgToCursor / 10,m_nBrgToCursor % 10,GetCompassUnitChrStr());
          CopyCharStrToUniStr(vTextX,vTextC);

          UniStrCpy(vTextW,(UNICODE *)G_pCursorBRGMsgX[m_nLangCode]);
          UniStrCat(vTextW,vTextX);
         }

      xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 18);
      DrawText(xPointX,vTextW,xBrushX,nFontID);
#else
      if (SysGetGlobalNightMode() == MODE_VAL_ON)
         {
//        xBrushX.LineColor = PegResourceManager::GetColor(CID_RED);
//        xBrushX.FillColor = PegResourceManager::GetColor(CID_RED);
          xBrushX.LineColor = PegResourceManager::GetColor(CID_WHITE);
          xBrushX.FillColor = PegResourceManager::GetColor(CID_WHITE);
         }

   #if defined(__SAMYUNG__) && !defined(_ICON_GME_)
      nFontID = FID_MyriadPro16bEng;
      if (this->IsDrawingFontLarge())
          nFontID = FID_MyriadPro18bEng;

    #if defined(__N500_MODEL__)
      nFontID = FID_MyriadPro26bEng;
    #endif

      this->GetCurrGridPos(&nGridLat,&nGridLon);

      strcpy((char *)vTextC,GetLatChrStr(nGridLat));
      CopyCharStrToUniStr(vTextW,vTextC);
    #if defined(__N500_MODEL__)
      xPointX.Set(this->mClient.Left +13,this->mClient.Top +  0);
    #else
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top +  0);
    #endif
      DrawText(xPointX,vTextW,xBrushX,nFontID);

      strcpy((char *)vTextC,GetLonChrStr(nGridLon));
      CopyCharStrToUniStr(vTextW,vTextC);
    #if defined(__N500_MODEL__)
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 26);
    #else
      if (this->IsDrawingFontLarge())
          xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 19);
      else
          xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 17);
    #endif
      DrawText(xPointX,vTextW,xBrushX,nFontID);

      if (m_rDstToCursor >= 10.0)
          sprintf(vTextC,"%7.2f%s",m_rDstToCursor,GetDistUnitChrStr());
      else
          sprintf(vTextC,"%7.3f%s",m_rDstToCursor,GetDistUnitChrStr());
      CopyCharStrToUniStr(vTextX,vTextC);

//    UniStrCpy(vTextW,(UNICODE *)G_pCursorDSTMsgX[m_nLangCode]);
//    UniStrCat(vTextW,vTextX);
//    xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 37);
//    DrawText(xPointX,vTextW,xBrushX,nFontID);

    #if defined(__N500_MODEL__)
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 64);
      DrawText(xPointX,(UNICODE *)G_pCursorDSTMsgX[m_nLangCode],xBrushX,FID_MyriadPro16bEng);
      xPointX.Set(this->mClient.Right -  5 - TextWidth(vTextX, nFontID), this->mClient.Top + 54);
      DrawText(xPointX,vTextX,xBrushX,nFontID);
    #else
      if (this->IsDrawingFontLarge())
          xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 46);
      else
          xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 42);
      DrawText(xPointX,(UNICODE *)G_pCursorDSTMsgX[m_nLangCode],xBrushX,FID_MyriadPro10bEng);
      if (this->IsDrawingFontLarge())
          xPointX.Set(this->mClient.Right -  5 - TextWidth(vTextX, nFontID), this->mClient.Top + 40);
      else
          xPointX.Set(this->mClient.Right -  5 - TextWidth(vTextX, nFontID), this->mClient.Top + 37);
      DrawText(xPointX,vTextX,xBrushX,nFontID);
    #endif

      sprintf(vTextC," %03d.%d%s",m_nBrgToCursor / 10,m_nBrgToCursor % 10,GetCompassUnitChrStr());
      CopyCharStrToUniStr(vTextX,vTextC);

//    UniStrCpy(vTextW,(UNICODE *)G_pCursorBRGMsgX[m_nLangCode]);
//    UniStrCat(vTextW,vTextX);
//    xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 54);
//    DrawText(xPointX,vTextW,xBrushX,nFontID);

    #if defined(__N500_MODEL__)
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 92);
      DrawText(xPointX,(UNICODE *)G_pCursorBRGMsgX[m_nLangCode],xBrushX,FID_MyriadPro16bEng);
      xPointX.Set(this->mClient.Right -  5 - TextWidth(vTextX, nFontID),this->mClient.Top + 82);
      DrawText(xPointX,vTextX,xBrushX,nFontID);
    #else
      if (this->IsDrawingFontLarge())
          xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 66);
      else
          xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 60);
      DrawText(xPointX,(UNICODE *)G_pCursorBRGMsgX[m_nLangCode],xBrushX,FID_MyriadPro10bEng);
      if (this->IsDrawingFontLarge())
          xPointX.Set(this->mClient.Right -  5 - TextWidth(vTextX, nFontID),this->mClient.Top + 60);
      else
          xPointX.Set(this->mClient.Right -  5 - TextWidth(vTextX, nFontID),this->mClient.Top + 54);
      DrawText(xPointX,vTextX,xBrushX,nFontID);
    #endif
   #else
      this->GetCurrGridPos(&nGridLat,&nGridLon);

      strcpy((char *)vTextC,GetLatChrStr(nGridLat));
      CopyCharStrToUniStr(vTextW,vTextC);
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top +  1);
      DrawText(xPointX,vTextW,xBrushX,nFontID);

      strcpy((char *)vTextC,GetLonChrStr(nGridLon));
      CopyCharStrToUniStr(vTextW,vTextC);
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 18);
      DrawText(xPointX,vTextW,xBrushX,nFontID);

      sprintf(vTextC,"%7.2f %s",m_rDstToCursor,GetDistUnitChrStr());
      CopyCharStrToUniStr(vTextX,vTextC);
      UniStrCpy(vTextW,(UNICODE *)G_pCursorDSTMsgX[m_nLangCode]);
      UniStrCat(vTextW,vTextX);
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 41);
      DrawText(xPointX,vTextW,xBrushX,nFontID);

      sprintf(vTextC," %03d.%d %s",m_nBrgToCursor / 10,m_nBrgToCursor % 10,GetCompassUnitChrStr());
      CopyCharStrToUniStr(vTextX,vTextC);
      UniStrCpy(vTextW,(UNICODE *)G_pCursorBRGMsgX[m_nLangCode]);
      UniStrCat(vTextW,vTextX);
      xPointX.Set(this->mClient.Left + 1,this->mClient.Top + 58);
      DrawText(xPointX,vTextW,xBrushX,nFontID);
   #endif
#endif

      EndDraw();
}
#else
/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cCursorData::DrawWnd(BOOL bRedraw)
{
	CHAR    vTextC[128];
	  
	COLORT wndBackClr;
	COLORT wndOutLineClr;
	COLORT txtClr;
	int nLangMode = g_pDocMgr->GetLangMode();
	
	FONT *pTxtFont = NULL;
	FONT *pCapFont = NULL;
	FONT *pOldFont = NULL;
	
	int nRowH = mClient.Height()/4;
	int nStrW = 0;
	int nFontH = 0;
	int nXPos = 0, nYPos= 0, nYOffset = 0, nXOffset = 0;
	LGRID nGridLat,nGridLon;
	HWORD *pUniCodeStr = NULL;

	wndBackClr = COLORSCHEME[m_pParent->GetColorScheme()].crBack;
	txtClr = COLORSCHEME[m_pParent->GetColorScheme()].crFore;
	wndOutLineClr = COLORSCHEME[m_pParent->GetColorScheme()].crFore;

	RunCheckLangCode();
	RunCheckNightMode();

	if(m_pScreen != NULL)
	{
		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:	
				pTxtFont = &MyriadPro24bEng;
				pCapFont = &NewGulLim18bCJK;
				break;

			case LANG_RUS:
				pTxtFont = &MyriadPro24bEng;
				pCapFont = &MyriadPro24bRus;
				break;
				
			default:
				pTxtFont = &MyriadPro24bEng;
				pCapFont = &MyriadPro24bEng;
				break;
		}
		
		
		pOldFont = m_pScreen->SetFont(pTxtFont);
		nFontH = pTxtFont->uHeight;
		
		m_pScreen->FillRect(mClient.Left,mClient.Top,mClient.Right,mClient.Bottom,wndBackClr);

		this->CalcDstBrgToShip();
		this->GetCurrGridPos(&nGridLat,&nGridLon);
		
		// Draw Cursor Latitude
		strcpy((char *)vTextC,GetLatChrStr2(nGridLat));
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)vTextC,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = mClient.Left + (mClient.Width() - nStrW) - 5;
		nYPos = mClient.Top + nRowH*0 + (nRowH - nFontH)/2;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
				

		// Draw Cursor Longitude
		strcpy((char *)vTextC,GetLonChrStr2(nGridLon));
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)vTextC,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = mClient.Left + (mClient.Width() - nStrW) - 5;
		nYPos = mClient.Top + nRowH*1 + (nRowH - nFontH)/2;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		m_pScreen->SetFont(pCapFont);
		nFontH = pCapFont->uHeight;
		// Draw Cursor Range
		pUniCodeStr = (HWORD *)STR_RANGE[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = mClient.Left + 5;
		nYPos = mClient.Top + nRowH*2 + (nRowH - nFontH)/2;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		if (m_rDstToCursor >= 10.0)
		{
          sprintf(vTextC,"%7.2f%s",m_rDstToCursor,GetDistUnitChrStr());
		}		  
      	else
      	{
          sprintf(vTextC,"%7.3f%s",m_rDstToCursor,GetDistUnitChrStr());
      	}		  
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)vTextC,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = mClient.Left + (mClient.Width() - nStrW) - 5;
		nYPos = mClient.Top + nRowH*2 + (nRowH - nFontH)/2;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);
		
		
		// Draw Cursor Bearing
		pUniCodeStr = (HWORD *)STR_BEARING[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = mClient.Left + 5;
		nYPos = mClient.Top + nRowH*3 + (nRowH - nFontH)/2;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		//sprintf(vTextC," %03d.%d %s",m_nBrgToCursor / 10,m_nBrgToCursor % 10,GetCompassUnitChrStr());
		sprintf(vTextC," %03d.%d%c",m_nBrgToCursor / 10,m_nBrgToCursor % 10,DEG_CHAR);
		
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)vTextC,nLangMode);
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		nXPos = mClient.Left + (mClient.Width() - nStrW) - 5;
		nYPos = mClient.Top + nRowH*3 + (nRowH - nFontH)/2;
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,txtClr);

		
		m_pScreen->Rect(mClient.Left,mClient.Top,mClient.Right,mClient.Bottom,wndOutLineClr);

		m_pScreen->SetFont(pOldFont);
	}
}
#endif

int cCursorData::RunCheckLangCode(void)
{
#if 0	// Temp
      if (!cMyWin::RunCheckLangCode())
          return(0);
#endif
      return(1);
}
int cCursorData::RunCheckNightMode(void)
{
#if 0	// Temp
      if (!cMyWin::RunCheckNightMode())
          return(0);


#ifdef  __PLASTIMO__
      this->SetColor(PCI_NORMAL  ,GetBackGroundColorID(TRUE));
#else
      if (SysGetGlobalNightMode() == MODE_VAL_ON)
          this->SetColor(PCI_NORMAL  ,CID_BLACK);
      else
          this->SetColor(PCI_NORMAL  ,GetBackGroundColorID(TRUE));
#endif
#endif	  
      return(1);
}

void  cCursorData::InitAllData(void)
{
}
void  cCursorData::SetCursorMode(int nMode)
{
      m_nCursorMode = nMode;
}
int   cCursorData::GetCursorMode(void)
{
      return(m_nCursorMode);
}

void  cCursorData::GetGridPosByScrnPos(LGRID *pLat,LGRID *pLon)
{
#ifdef  __SAMYUNG__
	int  nScrnX,nScrnY;

	if (SysIsUsingMapSAMYUNG())
	{
		this->GetSamMapScrnPos(&nScrnX,&nScrnY);

		CalcLongByRotatedScrn(nScrnY,nScrnX,(INT32 *)pLat,(INT32 *)pLon);

#if defined(__NEW_MERCATOR__)
		*pLat = MRC32ToLongLat(*pLat);
		*pLon = MRC32ToLongLon(*pLon);
#endif
		return;
	}
#endif

	GetMercPosByScrnPos(pLat,pLon);

	*pLat = MercLatToGrid(*pLat);
	*pLon = MercLonToGrid(*pLon);
}

void  cCursorData::GetMercPosByScrnPos(LMERC *pLat,LMERC *pLon)
{
	if (SysIsUsingMapCMAP())
	{
#if defined(__NAVIONICS__)
		((CTargetPlotWnd *)m_pParent)->CalcLatLonByImgPos(m_xCurrVirtPos.nX,m_xCurrVirtPos.nY,(int *)pLon,(int *)pLat);
#else
		cmScreen2Merc(m_xCurrVirtPos.nX,m_xCurrVirtPos.nY,pLon,pLat);
#endif
	}
}

void cCursorData::SetMercPos(LMERC nLat,LMERC nLon)
{
	m_xCurrMercPos.nLat = nLat;
	m_xCurrMercPos.nLon = nLon;

	m_xCurrGridPos.nLat = MercLatToGrid(nLat);
	m_xCurrGridPos.nLon = MercLonToGrid(nLon);
}

void cCursorData::SetGridPos(LGRID nLat,LGRID nLon)
{
	m_xCurrGridPos.nLat = nLat;
	m_xCurrGridPos.nLon = nLon;
}

void  cCursorData::CalcScrnToMercPos(void)
{
	LGRID nLat,nLon;

	if (SysIsUsingMapCMAP())
	{
		this->SetCurrToVirtScrnPos();

#if defined(__NAVIONICS__)
		((CTargetPlotWnd *)m_pParent)->CalcLatLonByImgPos(m_xCurrVirtPos.nX,m_xCurrVirtPos.nY,(int *)&m_xCurrMercPos.nLon,(int *)&m_xCurrMercPos.nLat);
#else
		cmScreen2Merc(m_xCurrVirtPos.nX,m_xCurrVirtPos.nY,&m_xCurrMercPos.nLon,&m_xCurrMercPos.nLat);
#endif

		this->GetCurrGridPos(&nLat,&nLon);
		SetChartCursorGridLat(nLat);
		SetChartCursorGridLon(nLon);
	}
#ifdef  __SAMYUNG__
	else
	{
		this->SetCurrToVirtScrnPos();

		this->GetGridPosByScrnPos(&nLat,&nLon);
		this->SetMercPos(GridLatToMerc(nLat),GridLonToMerc(nLon));
		//        this->SetGridPos(nLat,nLon);
		SetChartCursorGridLat(nLat);
		SetChartCursorGridLon(nLon);
	}
#endif
}

void cCursorData::CalcMercToScrnPos(void)
{
	if (SysIsUsingMapCMAP())
	{
#if defined(__NAVIONICS__)
		((CTargetPlotWnd *)m_pParent)->CalcImgPosByLatLon(m_xCurrMercPos.nLon,m_xCurrMercPos.nLat,(int *)&m_xCurrVirtPos.nX,(int *)&m_xCurrVirtPos.nY);
#else
		cmMerc2Screen(m_xCurrMercPos.nLon,m_xCurrMercPos.nLat,(SLong *)&m_xCurrVirtPos.nX,(SLong *)&m_xCurrVirtPos.nY);
#endif
		this->SetVirtToCurrScrnPos();
	}
#ifdef  __SAMYUNG__
	else
	{
		this->CalcGridToScrnPos();
	}
#endif
}

void cCursorData::CalcGridToScrnPos(void)
{
#ifdef  __SAMYUNG__
	if (SysIsUsingMapSAMYUNG())
	{
#if defined(__NEW_MERCATOR__)
		CalcWndwCoorByScrnOrigLong(LongLatToMRC32(m_xCurrGridPos.nLat),LongLonToMRC32(m_xCurrGridPos.nLon),&m_xCurrVirtPos.nY,&m_xCurrVirtPos.nX,0);
#else
		CalcWndwCoorByScrnOrigLong(m_xCurrGridPos.nLat,m_xCurrGridPos.nLon,&m_xCurrVirtPos.nY,&m_xCurrVirtPos.nX,0);
#endif

		this->SetVirtToCurrScrnPos();
	}
#endif
}

void cCursorData::GetCurrScrnPos(int *pScrnX,int *pScrnY)
{
	*pScrnX = m_xCurrScrnPos.nX;
	*pScrnY = m_xCurrScrnPos.nY;
}

int cCursorData::GetCurrScrnX(void)
{
	return(m_xCurrScrnPos.nX);
}

int cCursorData::GetCurrScrnY(void)
{
	return(m_xCurrScrnPos.nY);
}

#if defined(__NAVIONICS__)
int cCursorData::GetPrevScrnX(void)
{
	return(m_xPrevScrnPos.nX);
}
int cCursorData::GetPrevScrnY(void)
{
	return(m_xPrevScrnPos.nY);
}
#endif

int cCursorData::GetVirtScrnX(void)
{
	return(m_xCurrVirtPos.nX);
}
int cCursorData::GetVirtScrnY(void)
{
	return(m_xCurrVirtPos.nY);
}

#if defined(__NAVIONICS__)
void cCursorData::SetPrevScrnX(int nScrnX)
{
	m_xPrevScrnPos.nX = nScrnX;
}
void cCursorData::SetPrevScrnY(int nScrnY)
{
	m_xPrevScrnPos.nY = nScrnY;
}

void cCursorData::SetPrevScrnXY(int nScrnX,int nScrnY)
{
	m_xPrevScrnPos.nX = nScrnX;
	m_xPrevScrnPos.nY = nScrnY;
}
#endif

void cCursorData::GetSamMapScrnPos(int *pScrnX,int *pScrnY)
{
#ifdef  __SAMYUNG__
	if (SysIsUsingMapSAMYUNG())
	{
		*pScrnX = GetScrnPosByWndwX(m_xCurrScrnPos.nX);
		*pScrnY = GetScrnPosByWndwY(m_xCurrScrnPos.nY);
	}
#endif
}
void  cCursorData::SetCurrScrnPosCenter(int nCalcMode)
{
#if defined(__NAVIONICS__)
	this->SetPrevScrnXY(m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);
#endif
	this->SetCurrScrnPos(m_pParent->mClient.Left + m_pParent->mClient.Width() / 2,m_pParent->mClient.Top + m_pParent->mClient.Height() / 2,nCalcMode);
}

void cCursorData::SetCurrScrnPos(int nScrnX,int nScrnY,int nCalcMode)
{
	m_xCurrScrnPos.nX = nScrnX;
	m_xCurrScrnPos.nY = nScrnY;

	this->SetCurrToVirtScrnPos();

	if (nCalcMode)
		this->CalcScrnToMercPos();
}
void  cCursorData::SetCurrToVirtScrnPos(void)
{
#ifdef  __SAMYUNG__
	if (SysIsUsingMapSAMYUNG())
	{
		this->GetSamMapScrnPos(&m_xCurrVirtPos.nX,&m_xCurrVirtPos.nY);

		return;
	}
#endif

#if defined(__NAVIONICS__)
	((CTargetPlotWnd *)m_pParent)->CalcImgPosByScrPos(m_xCurrScrnPos.nX,m_xCurrScrnPos.nY,&m_xCurrVirtPos.nX,&m_xCurrVirtPos.nY);
#else
	m_xCurrVirtPos.nX = cmgWinGetVirtScrX(m_pParent->mClient.Left,m_xCurrScrnPos.nX);
	m_xCurrVirtPos.nY = cmgWinGetVirtScrY(m_pParent->mClient.Bottom,m_xCurrScrnPos.nY);
#endif
}

void cCursorData::SetVirtToCurrScrnPos(void)
{
	if (SysIsUsingMapCMAP())
	{
#if defined(__NAVIONICS__)
		((CTargetPlotWnd *)m_pParent)->CalcScrPosByImgPos(m_xCurrVirtPos.nX,m_xCurrVirtPos.nY,&m_xCurrScrnPos.nX,&m_xCurrScrnPos.nY);
#else
		m_xCurrScrnPos.nX = cmgWinGetRealScrX(m_pParent->mClient.Left,m_xCurrVirtPos.nX);
		m_xCurrScrnPos.nY = cmgWinGetRealScrY(m_pParent->mClient.Bottom,m_xCurrVirtPos.nY);
#endif
	}
#ifdef  __SAMYUNG__
	else
	{
		m_xCurrScrnPos.nX = GetWndwPosByScrnX(m_xCurrVirtPos.nX);
		m_xCurrScrnPos.nY = GetWndwPosByScrnY(m_xCurrVirtPos.nY);
	}
#endif
}

void cCursorData::GetCurrMercPos(LMERC *pLat,LMERC *pLon)
{
	*pLat = m_xCurrMercPos.nLat;
	*pLon = m_xCurrMercPos.nLon;
}

void cCursorData::GetCurrGridPos(LGRID *pLat,LGRID *pLon)
{
	if (SysIsUsingMapCMAP())
	{
		*pLat = MercLatToGrid(m_xCurrMercPos.nLat);
		*pLon = MercLonToGrid(m_xCurrMercPos.nLon);
	}
#ifdef  __SAMYUNG__
	else
	{
		*pLat = m_xCurrGridPos.nLat;
		*pLon = m_xCurrGridPos.nLon;
	}
#endif
}

void cCursorData::GetCurrRealPos(LREAL *pLat,LREAL *pLon)
{
	if (SysIsUsingMapCMAP())
	{
		*pLat = MercLatToReal(m_xCurrMercPos.nLat);
		*pLon = MercLonToReal(m_xCurrMercPos.nLon);
	}
#ifdef  __SAMYUNG__
	else
	{
		*pLat = GridToReal(m_xCurrGridPos.nLat);
		*pLon = GridToReal(m_xCurrGridPos.nLon);
	}
#endif
}

void cCursorData::CalcDstBrgToShip(void)
{
	LREAL rLat,rLon;
	REAL  rDeg;

	this->GetCurrRealPos(&rLat,&rLon);

	GetDistanceAndCourse(GetShipRealLat(),GetShipRealLon(),rLat,rLon,&m_rDstToCursor,&rDeg);
	if (m_rDstToCursor > 9999.9)
		m_rDstToCursor = 9999.9;

	m_nBrgToCursor = (int)(rDeg * 10.0);
	m_nBrgToCursor = CheckCompassValue(m_nBrgToCursor);
}

int cCursorData::LeftMove(int nStep)
{
	int  nMinX = this->GetMinScrnX();

#if defined(__NAVIONICS__)
	this->SetPrevScrnXY(m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);
#endif

	m_xCurrScrnPos.nX -= nStep;

	if (m_xCurrScrnPos.nX <= nMinX)
	{
		m_xCurrScrnPos.nX  = nMinX + CURSOR_BACK_MOVE_STEP;

		this->CalcScrnToMercPos();

		return(1);
	}

	this->CalcScrnToMercPos();

	return(0);
}

int cCursorData::RightMove(int nStep)
{
	int  nMaxX = this->GetMaxScrnX();

#if defined(__NAVIONICS__)
	this->SetPrevScrnXY(m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);
#endif

	m_xCurrScrnPos.nX += nStep;

	if (m_xCurrScrnPos.nX >= nMaxX)
	{
		m_xCurrScrnPos.nX  = nMaxX - CURSOR_BACK_MOVE_STEP;

		this->CalcScrnToMercPos();

		return(1);
	}

	this->CalcScrnToMercPos();

	return(0);
}

int cCursorData::UpMove(int nStep)
{
	int  nMinY = this->GetMinScrnY();

#if defined(__NAVIONICS__)
	this->SetPrevScrnXY(m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);
#endif

	m_xCurrScrnPos.nY -= nStep;

	if (m_xCurrScrnPos.nY <= nMinY)
	{
		m_xCurrScrnPos.nY  = nMinY + CURSOR_BACK_MOVE_STEP;

		this->CalcScrnToMercPos();

		return(1);
	}

	this->CalcScrnToMercPos();

	return(0);
}

int cCursorData::DownMove(int nStep)
{
	int  nMaxY = this->GetMaxScrnY();

#if defined(__NAVIONICS__)
	this->SetPrevScrnXY(m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);
#endif

	m_xCurrScrnPos.nY += nStep;

	if (m_xCurrScrnPos.nY >= nMaxY)
	{
		m_xCurrScrnPos.nY  = nMaxY - CURSOR_BACK_MOVE_STEP;

		this->CalcScrnToMercPos();

		return(1);
	}

	this->CalcScrnToMercPos();

	return(0);
}

int cCursorData::IsCursorCenterPos(void)
{
	if(	m_xCurrScrnPos.nX == (m_pParent->mClient.Left + m_pParent->mClient.Width() / 2) &&
		m_xCurrScrnPos.nX == (m_pParent->mClient.Top + m_pParent->mClient.Height() / 2))
		return(1);
	return(0);
}

int cCursorData::IsErrorScrnX(void)
{
	if (m_xCurrScrnPos.nX <= this->GetMinScrnX() || m_xCurrScrnPos.nX >= this->GetMaxScrnX())
		return(1);
	return(0);
}

int cCursorData::IsErrorScrnY(void)
{
	if (m_xCurrScrnPos.nY <= this->GetMinScrnY() || m_xCurrScrnPos.nY >= this->GetMaxScrnY())
		return(1);
	return(0);
}

int cCursorData::GetMinScrnX(void)
{
#ifdef _SI_70AM_
	return(m_pParent->mClient.Left + CURSOR_IMAGE_SIZE_X / 2);
#else
	cCalcDstWin *pCalcDstWin = ((CTargetPlotWnd *)m_pParent)->GetCalcDstWinPtr();
	int  nMinY;

	if (pCalcDstWin->StatusIs(PSF_VISIBLE))
	{
		nMinY = pCalcDstWin->mReal.Top;
	}		
	else
	{
		nMinY = this->mReal.Top;
	}

	if (m_xCurrScrnPos.nY >= nMinY && m_xCurrScrnPos.nY <= this->mReal.Bottom)
		return(this->mClient.Right + CURSOR_IMAGE_SIZE_X / 2);

	return(m_pParent->mClient.Left + CURSOR_IMAGE_SIZE_X / 2);
#endif	
	
}

int cCursorData::GetMinScrnY(void)
{
	return(m_pParent->mClient.Top + CURSOR_IMAGE_SIZE_Y / 2);
}

int cCursorData::GetMaxScrnX(void)
{
	return(m_pParent->mClient.Right - CURSOR_IMAGE_SIZE_X / 2);
}
int cCursorData::GetMaxScrnY(void)
{
#ifdef _SI_70AM_
	return(m_pParent->mClient.Bottom - CURSOR_IMAGE_SIZE_Y / 2);
#else
	cCalcDstWin *pCalcDstWin = ((CTargetPlotWnd *)m_pParent)->GetCalcDstWinPtr();
	int  nMaxY;

	if (pCalcDstWin->StatusIs(PSF_VISIBLE))
	{
		nMaxY = pCalcDstWin->mClient.Top;
	}		
	else
	{
		nMaxY = this->mClient.Top;
	}

	if (m_xCurrScrnPos.nX >= this->mReal.Left && m_xCurrScrnPos.nX <= this->mReal.Right)
		return(nMaxY - CURSOR_IMAGE_SIZE_Y / 2);
	return(m_pParent->mClient.Bottom - CURSOR_IMAGE_SIZE_Y / 2);
#endif	
}

void cCursorData::SetBackMercMode(int nMode)
{
	if (nMode && m_nCursorMode == MODE_VAL_ON)
	{
		m_nBackMercMode = 1;
		m_xBackMercPos  = m_xCurrMercPos;
		m_xBackGridPos  = m_xCurrGridPos;
	}
	else
	{
		m_nBackMercMode = 0;
		m_xBackMercPos.nLat = 0;
		m_xBackMercPos.nLon = 0;
	}
}

int cCursorData::GetBackMercMode(void)
{
	int  nMode = m_nBackMercMode;

	if (m_nBackMercMode)
	{
		m_xCurrMercPos = m_xBackMercPos;
		m_xCurrGridPos = m_xBackGridPos;
	}

	m_nBackMercMode = 0;

	return(nMode);
}

void cCursorData::DrawCursorMark(int nEraseDraw)
{
	this->DrawCursorMark(nEraseDraw,m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);

	this->DrawCursorLine(nEraseDraw,m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);
}

void  cCursorData::DrawCursorMark(int nEraseDraw,int nScrnX,int nScrnY)
{
	if (nEraseDraw == DRAW_MODE_DRAW)
	{
		WinGrCursorMarkSaveDraw(CURSOR_IMAGE_SIZE_X,CURSOR_IMAGE_SIZE_Y,G_vCusorMarkData,nScrnX - CURSOR_IMAGE_SIZE_X / 2,nScrnY - CURSOR_IMAGE_SIZE_Y / 2,m_pSaveMemMark,m_pParent);
	}		
	else
	{
		WinGrCursorMarkRestDraw(CURSOR_IMAGE_SIZE_X,CURSOR_IMAGE_SIZE_Y,G_vCusorMarkData,nScrnX - CURSOR_IMAGE_SIZE_X / 2,nScrnY - CURSOR_IMAGE_SIZE_Y / 2,m_pSaveMemMark,m_pParent);
	}		
}

void  cCursorData::DrawCursorLine(int nEraseDraw,int nScrnX,int nScrnY)
{
#ifdef  __SAMYUNG__
	CLRMENU *pSaveMemLine;
	int   nLtX,nRtX;
	int   nUpY,nDnY;

#define  _L_OFF_  13
#define  _R_OFF_  12
#define  _U_OFF_  13
#define  _D_OFF_  12

#define CID_VGA_COLOR00 0
#define CID_VGA_COLOR09 9

	nLtX = m_pParent->mClient.Left;
	nRtX = m_pParent->mClient.Right;

	nUpY = m_pParent->mClient.Top;
	nDnY = m_pParent->mClient.Bottom;

	pSaveMemLine = m_pSaveMemLine;

	if (nEraseDraw == DRAW_MODE_DRAW)
	{
		pSaveMemLine = WinGrDnToUpLineSaveDraw(nScrnY - _U_OFF_,nUpY,nScrnX,CID_VGA_COLOR09,CID_VGA_COLOR00,pSaveMemLine,m_pParent);
		pSaveMemLine = WinGrUpToDnLineSaveDraw(nScrnY + _D_OFF_,nDnY,nScrnX,CID_VGA_COLOR09,CID_VGA_COLOR00,pSaveMemLine,m_pParent);

		pSaveMemLine = WinGrRtToLtLineSaveDraw(nScrnX - _L_OFF_,nLtX,nScrnY,CID_VGA_COLOR09,CID_VGA_COLOR00,pSaveMemLine,m_pParent);
		pSaveMemLine = WinGrLtToRtLineSaveDraw(nScrnX + _R_OFF_,nRtX,nScrnY,CID_VGA_COLOR09,CID_VGA_COLOR00,pSaveMemLine,m_pParent);
	}
	else
	{
		pSaveMemLine = WinGrDnToUpLineRestDraw(nScrnY - _U_OFF_,nUpY,nScrnX,pSaveMemLine,m_pParent);
		pSaveMemLine = WinGrUpToDnLineRestDraw(nScrnY + _D_OFF_,nDnY,nScrnX,pSaveMemLine,m_pParent);

		pSaveMemLine = WinGrRtToLtLineRestDraw(nScrnX - _L_OFF_,nLtX,nScrnY,pSaveMemLine,m_pParent);
		pSaveMemLine = WinGrLtToRtLineRestDraw(nScrnX + _R_OFF_,nRtX,nScrnY,pSaveMemLine,m_pParent);
	}
#endif
}

int   cCursorData::IsDrawingFontLarge(void)
{
//    if (SysGetVirtScreenWidth() >= 800)
          return(1);

      return(0);
}

