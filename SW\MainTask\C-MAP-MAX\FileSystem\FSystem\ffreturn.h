/*##################################################################
  FILE    : FFRETURN.H

  USE     : File System routines return codes defines.
  PROJECT : C-Map File System.

  AUTHOR  : MLP[950818].
  UPDATED : SiS[030606].
  ##################################################################

  	
*/


#ifndef __FFRETURN__
	#define __FFRETURN__


/************************************************************************
  Constants definition section.
 ************************************************************************/

/* 
 * Device type defines. These values are used to set the letter field of the
 * DeviceNew structure. If you change or add values to this list, you must
 * modify the FS_GetDeviceType() routine.
 */
#define DRIVE_A 				0
#define DRIVE_B 				1
#define CDG_16K 				2	/* Not used. 			*/
#define CDG_32K 				3	/* Not used. 			*/
#define CDG_64K 				4	/* Not used. 			*/
#define MEM_CDG_16K 			5	/* Not used. 			*/
#define MEM_CDG_32K 			6	/* Not used. 			*/
#define MEM_CDG_64K 			7	/* Not used. 			*/
#define PPCR2_CDG_32K 			8	/* Not used. 			*/
#define PPCR2_CDG_64K 			9	/* Not used. 			*/
#define CCARD0					10
#define CCARD1					11
#define CCARD2					12
#define CCARD3					13
#define FILE1MB					14	/* Simulation mode only. */
#define USER_IMAGE				15
#define USER_IMAGE1				16
#define MMR_CCARD				17
#define MMR_SD					18
#define MMR_CF					19
#define DPS2_CCARD				20
#define DPS2_SD					21
#define DPS2_CF					22
#define MMR2_CCARD				DPS2_CCARD
#define MMR2_SD					DPS2_SD
#define MMR2_CF					DPS2_CF


/* 
 * Empty field value.
 */
#define FSYS_FIELD_EMPTY		-1


/* 
 * Values returned by FS_GetDeviceType.
 */
#define FS_DEVICE_NOT_VALID		0
#define FS_DEVICE_CCARD			1
#define FS_DEVICE_DISK			2
#define FS_DEVICE_IMAGE			3




/************************************************************************
  Types & Data Structure definition definition section.
 ************************************************************************/


typedef enum 
{
	NO_ERRORS 					= -1,
	ERR_DIR_FULL 				= -2,
	ERR_DISK_FULL 				= -3,
	ERR_FILE_NOT_FOUND 			= -4,
	ERR_FILE_EXISTS 			= -5,
	DEVICE_NOT_FORMATTED 		= -6,		/* ERR_DISK_NOT_FORMATTED 		*/

	ERR_NEWER_FILESYSTEM		= -8,
	DEVICE_VERIFY_ERROR			= -10,

	DEVICE_NOT_VALID 			= -17,		/* Not a Valid User C-Card 		*/
	DEVICE_NOT_PRESENT 			= -18,		/* ERR_DISK_NOT_PRESENT, 		*/
	DISK_NOT_PRESENT 			= -34,	 	/* ERR_DISK_NOT_PRESENT, 		*/
	
	DISK_WRITE_PROTECTED 		= -43,
	SECTOR_NOT_ERASED 			= -44,
	FILE_WRITE_PROTECTED 		= -45,

	FILETABLE_FULL 				= -51,		/* ERR_NO_MEMORY,  				*/
	ERR_REN_ACROSS_DEVS 		= -52,
	DIR_NOT_FOUND				= -53,
	DEVICE_OVERFLOW				= -54,
	GENERAL_ERROR				= -55,
	FILE_NOT_OPENED				= -56,
	END_OF_FILE					= -57,
	EMPTY_DEV_TAB				= -58,
	OPENW_TOO_MANY				= -59,
	UNDEFINED_DEVICE			= -60,
	MAP_CARTRIDGE				= -61,
	INTERRUPT_RUNNING			= -62,

	ERR_SYSTEM_ERROR           	= -100,
    ERR_DEVICE_NOT_INITIALISED 	= -101,
    ERR_DISK_CHANGED          	= -102,
    ERR_INVALID_ARGUMENT       	= -103,
    
    ERR_WRONG_FORMAT			= -104,
	ERR_WRONG_FILE_TYPE			= -105,
	END_OF_SECTION				= -106,
	ERR_SECTION_ALREADY_OPEN	= -107,
	ERR_SECTION_NOT_OPEN		= -108


} ERRTYPE;



/************************************************************************
  Preprocessor MACROS definition.
 ************************************************************************/

#define _FS_FAILED( iErr )	( (iErr) != NO_ERRORS )




/************************************************************************
  END of Code.
 ************************************************************************/

#endif /* ifndef __FFRETURN__ */


/*------------------------------------------------------------------------
 * $Log: /CplotTools/FileSystem/SourceFiles/H/FFRETURN.H $
 * 
 * 2     23-09-99 13:20 Andrea
 * Moved the LOG to the end of the file.
 * 
 * 1     22-09-99 18:30 Andrea
 * 
 *-----------------------------------------------------------------------*/
