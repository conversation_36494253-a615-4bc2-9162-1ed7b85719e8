/*...........................................................................*/
/*.                  File Name : MEMCONST.HPP                               .*/
/*.                                                                         .*/
/*.                       Date : 2004.02.12                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __MEMCONST_HPP
#define  __MEMCONST_HPP

//=========================================================================
#define  LCD_BASE_ADDR           0xe0000000
#define  LCD_MEM_BASE_ADDR       0x00200000
#define  FPGA_BASE_ADDR          0xe8000000
#define  MEM_SHIFT_FACTOR                 0
#define  FPGA_VER_ADDR       (FPGA_BASE_ADDR + (0x00000 << MEM_SHIFT_FACTOR))
#define  ADC_TUNE_L_ADDR     (FPGA_BASE_ADDR + (0x00010 << MEM_SHIFT_FACTOR))
#define  ADC_TUNE_H_ADDR     (FPGA_BASE_ADDR + (0x00011 << MEM_SHIFT_FACTOR))
#define  ADC_GAIN_L_ADDR     (FPGA_BASE_ADDR + (0x00012 << MEM_SHIFT_FACTOR))
#define  ADC_GAIN_H_ADDR     (FPGA_BASE_ADDR + (0x00013 << MEM_SHIFT_FACTOR))
#define  ADC_STC_L_ADDR      (FPGA_BASE_ADDR + (0x00014 << MEM_SHIFT_FACTOR))
#define  ADC_STC_H_ADDR      (FPGA_BASE_ADDR + (0x00015 << MEM_SHIFT_FACTOR))
#define  ADC_FTC_L_ADDR      (FPGA_BASE_ADDR + (0x00016 << MEM_SHIFT_FACTOR))
#define  ADC_FTC_H_ADDR      (FPGA_BASE_ADDR + (0x00017 << MEM_SHIFT_FACTOR))
#define  ADC_TUNE_M_L_ADDR   (FPGA_BASE_ADDR + (0x00018 << MEM_SHIFT_FACTOR))
#define  ADC_TUNE_M_H_ADDR   (FPGA_BASE_ADDR + (0x00019 << MEM_SHIFT_FACTOR))
#define  KBD_PORT_L_ADDR     (FPGA_BASE_ADDR + (0x00020 << MEM_SHIFT_FACTOR))
#define  KBD_PORT_H_ADDR     (FPGA_BASE_ADDR + (0x00021 << MEM_SHIFT_FACTOR))
#define  KBD_LCD_DIM_ADDR    (FPGA_BASE_ADDR + (0x00022 << MEM_SHIFT_FACTOR))
#define  DAC_LCD_DIM_ADDR    (FPGA_BASE_ADDR + (0x00030 << MEM_SHIFT_FACTOR))
#define  DAC_FTC_ADDR        (FPGA_BASE_ADDR + (0x00031 << MEM_SHIFT_FACTOR))
#define  DAC_TUNE_ADDR       (FPGA_BASE_ADDR + (0x00032 << MEM_SHIFT_FACTOR))
#define  DAC_TUNE_L_ADDR     (FPGA_BASE_ADDR + (0x00033 << MEM_SHIFT_FACTOR))
#define  DAC_TUNE_H_ADDR     (FPGA_BASE_ADDR + (0x00034 << MEM_SHIFT_FACTOR))
#define  DAC_TUNE_P_ADDR     (FPGA_BASE_ADDR + (0x00035 << MEM_SHIFT_FACTOR))
#define  DAC_GAIN_ADDR       (FPGA_BASE_ADDR + (0x00036 << MEM_SHIFT_FACTOR))
#define  DAC_GAIN_L_ADDR     (FPGA_BASE_ADDR + (0x00037 << MEM_SHIFT_FACTOR))
#define  DAC_STC_ADDR        (FPGA_BASE_ADDR + (0x00038 << MEM_SHIFT_FACTOR))
#define  DAC_STC_H_ADDR      (FPGA_BASE_ADDR + (0x00039 << MEM_SHIFT_FACTOR))
#define  HW_CONTROL_ADDR     (FPGA_BASE_ADDR + (0x00040 << MEM_SHIFT_FACTOR))
#define  RING_RANGE_ADDR     (FPGA_BASE_ADDR + (0x00041 << MEM_SHIFT_FACTOR))
#define  PULSE_WIDTH_ADDR    (FPGA_BASE_ADDR + (0x00042 << MEM_SHIFT_FACTOR))
#define  ZERO_MILE_ADDR      (FPGA_BASE_ADDR + (0x00043 << MEM_SHIFT_FACTOR))
#define  PRT_STATUS_ADDR     (FPGA_BASE_ADDR + (0x00070 << MEM_SHIFT_FACTOR))
#define  PRT_CONTROL_ADDR    (FPGA_BASE_ADDR + (0x00071 << MEM_SHIFT_FACTOR))
#define  PRT_DATA_ADDR       (FPGA_BASE_ADDR + (0x00072 << MEM_SHIFT_FACTOR))
#define  FIFO_DATA_ADDR      (FPGA_BASE_ADDR + (0x01010 << MEM_SHIFT_FACTOR))
#define  FIFO_DATA_CLOCK     (FPGA_BASE_ADDR + (0x01011 << MEM_SHIFT_FACTOR))
//-----------------------------------------------------------------------------
#define  FLASH_BASE_ADDR_04M 0xbfc00000
#define  FLASH_BASE_ADDR_16M 0xbf000000
#define  FLASH_BASE_ADDR_32M 0xbe000000
//=========================================================================

#endif

