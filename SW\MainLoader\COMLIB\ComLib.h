/*...........................................................................*/
/*.                  File Name : COMLIB.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.31                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "datatype.h"

#ifndef  __COMLIB_H__
#define  __COMLIB_H__

#ifdef  __cplusplus
extern "C" {
#endif

int   MakeSizeOfDoubleWord(int nSize);
void  RangeCheckBack16(BACK16 *pDataValue,BACK16 nLower,BACK16 nUpper,BACK16 nSetValue);
void  RangeCheckBack32(BACK32 *pDataValue,BACK32 nLower,BACK32 nUpper,BACK32 nSetValue);
DWORD GetCrc32(const UCHAR *pData,DWORD dSize);
DWORD GetCrc32Cont(const UCHAR *pData,DWORD dSize,DWORD dPrevCRC);
int   compress(CHAR *pTargetData,DWORD *nTargetSize,const CHAR *pSourceData,DWORD nSourceSize);
int   uncompress(CHAR *pTargetData,DWORD *nTargetSize,const CHAR *pSourceData,DWORD nSourceSize);
int   UnPackFileData(const UCHAR *pSourceData,UCHAR **pData,DWORD *pSize,UCHAR *pPackAddr,UCHAR *pBuffMemP,DWORD dBuffSize);
INT32 PowerOf10(int nPower);
void  SwapInt(int *pX,int *pY);
void  SwapLong(long *pX,long *pY);
void  SwapReal(REAL *pX,REAL *pY);
int   CirCularDec(int nValue,int nLast);
int   CirCularInc(int nValue,int nLast);
int   GetCircularSize(int nHead,int nTail,int nSize);
int   GetMinInt(int X,int Y);
int   GetMaxInt(int X,int Y);
int   IsAllSameCharacters(CHAR *pStr,CHAR bChr);
int   IsAllDigit(UCHAR *pData,int nSize,UCHAR bFill);
int   IsAllAscii(UCHAR *pData,int nSize,UCHAR bFill);
void  IsOneAscii(UCHAR *pData,int nSize,UCHAR bFill);
void  RemoveNullChar(UCHAR *pData,int nSize,UCHAR bFill);
CHAR *FullAllTrimStr(CHAR *pStr);
CHAR *RightTrimStr(CHAR *pStr,CHAR bTrimChar);
CHAR *RightAlignStr(CHAR *pStr,int nLen);
int   StrToInt(CHAR *pStr,int nStart,int nLen);
UCHAR UpperChar(UCHAR bData);
UCHAR LowerChar(UCHAR bData);
void  UpperString(CHAR *pStr);
void  LowerString(CHAR *pStr);
DWORD HexStrToLong(UCHAR *pHexStr,int nSize);
UCHAR GetHexDigit(int nData,int nFlag);
UCHAR *ByteToBinStr(UCHAR bData);
UCHAR *ByteToHexStr(UCHAR bData,int nFlag);
UCHAR *WordToHexStr(HWORD wData,int nFlag);
UCHAR *LongToHexStr(DWORD dData,int nFlag);
int   SplitTextData(UCHAR *pSource,UCHAR *pOne,UCHAR *pTwo,int nChars);
void  MemScrollLeft(CHAR *pMem,int nWidth,int nHeight);
HWORD HanCodeChrConvertKSToCombi(HWORD wCode);
void  HanCodeStrConvert(UCHAR *pSource,UCHAR *pTarget);

HWORD JohabToUniCode(HWORD wJohabCode);
HWORD UniCodeToJohab(HWORD wUniCode);
CHAR  *UniStrToChohab(const HWORD *pSource,CHAR *pTarget,int nHanMode);
CHAR  *UniStrToChrStr(const HWORD *pSource,CHAR *pTarget);
int   UniCodeStrLen(const HWORD *pUniCode);
int   IsBasicAsciiCodeStr(const HWORD *pUniCode);
void  CopyCharStrToUniStr(HWORD *pTarget,CHAR *pSource);

#ifdef  __cplusplus
}
#endif

#endif

