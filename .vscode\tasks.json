{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "C:\\cygwin64\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "C:\\cygwin64\\bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "compiler: C:\\cygwin64\\bin\\g++.exe"}]}