/*...........................................................................*/
/*.                  File Name : MyriadPro16bRus.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma16bTai_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro16bRus_offset_table[257] = {
0x0000,0x0004,0x000f,0x001d,0x0027,0x0033,0x003e,0x0044,0x004b,0x0053,0x0066,0x0079,0x0088,0x0094,0x0098,0x00a4,
0x00b1,0x00be,0x00ca,0x00d6,0x00e0,0x00ee,0x00f9,0x010b,0x0116,0x0124,0x0132,0x013e,0x014b,0x015c,0x016a,0x0178,
0x0186,0x0192,0x019e,0x01a9,0x01b5,0x01c5,0x01d1,0x01df,0x01ec,0x01fe,0x0211,0x021f,0x0230,0x023c,0x0248,0x025a,
0x0266,0x0271,0x027d,0x0288,0x0290,0x029c,0x02a7,0x02b6,0x02c0,0x02cc,0x02d8,0x02e3,0x02ee,0x02fc,0x0308,0x0314,
0x0320,0x032c,0x0335,0x033e,0x0348,0x0356,0x0360,0x036c,0x0377,0x0387,0x0398,0x03a5,0x03b5,0x03c0,0x03ca,0x03d9,
0x03e4,0x03e8,0x03f3,0x03ff,0x0407,0x0410,0x0419,0x041e,0x0425,0x042b,0x043b,0x044c,0x0458,0x0463,0x0467,0x0471,
0x047d,0x0481,0x0485,0x0489,0x048d,0x0491,0x0495,0x0499,0x049d,0x04a1,0x04a5,0x04a9,0x04ad,0x04b1,0x04b5,0x04b9,
0x04bd,0x04c1,0x04c5,0x04c9,0x04cd,0x04d1,0x04d5,0x04d9,0x04dd,0x04e1,0x04e5,0x04e9,0x04ed,0x04f1,0x04f5,0x04f9,
0x04fd,0x0501,0x0505,0x0509,0x050d,0x0511,0x0515,0x0519,0x051d,0x0521,0x0525,0x0529,0x052d,0x0531,0x0535,0x0539,
0x053d,0x0547,0x0550,0x0554,0x0558,0x055c,0x0560,0x0564,0x0568,0x056c,0x0570,0x0574,0x0578,0x057c,0x0580,0x0584,
0x0588,0x058c,0x0590,0x0594,0x0598,0x059c,0x05a0,0x05a4,0x05a8,0x05ac,0x05b0,0x05b4,0x05b8,0x05bc,0x05c0,0x05c4,
0x05c8,0x05cc,0x05d0,0x05d4,0x05d8,0x05dc,0x05e0,0x05e4,0x05e8,0x05ec,0x05f0,0x05f4,0x05f8,0x05fc,0x0600,0x0604,
0x0608,0x060c,0x0610,0x0614,0x0618,0x061c,0x0620,0x0624,0x0628,0x062c,0x0630,0x0634,0x0638,0x063c,0x0640,0x0644,
0x0648,0x064c,0x0650,0x0654,0x0658,0x065c,0x0660,0x0664,0x0668,0x066c,0x0676,0x067a,0x067e,0x0682,0x0686,0x068a,
0x068e,0x0692,0x0696,0x069a,0x069e,0x06a2,0x06a6,0x06aa,0x06ae,0x06b2,0x06b6,0x06ba,0x06be,0x06c2,0x06c6,0x06ca,
0x06ce,0x06d2,0x06d6,0x06da,0x06de,0x06e2,0x06e6,0x06ea,0x06ee,0x06f2,0x06f6,0x06fa,0x06fe,0x0702,0x0706,0x070a,
0x070e};



ROMDATA PEGUBYTE MyriadPro16bRus_data_table[5650] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x03, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x03, 0xb8, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x0e, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x80, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0e, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0xf8, 0xff, 0xe3, 0xfc, 0x0f, 0x81, 0xe1, 0xc3, 0x81, 0xc7, 0xfc, 0x01, 0xc3, 0x80, 0x3f, 
0xf0, 0x70, 0xf0, 0xf0, 0x77, 0x07, 0x07, 0x81, 0xff, 0x1f, 0xe1, 0xfe, 0x1f, 0xf1, 0xfe, 0x78, 
0xe3, 0xc3, 0xe1, 0xc1, 0xe7, 0x07, 0x9c, 0x3c, 0xff, 0xc7, 0x83, 0xc7, 0x07, 0x01, 0xf0, 0x7f, 
0xf1, 0xfc, 0x01, 0xf3, 0xff, 0xf8, 0x38, 0x1c, 0x07, 0x87, 0xb8, 0x38, 0xe0, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x3f, 0x00, 0xe0, 0x1c, 0x70, 0x03, 0xe0, 0x70, 0x3c, 0x03, 0xf8, 0x00, 0x00, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3b, 0x87, 0x00, 
0x1c, 0x00, 0x00, 0x3b, 0xb9, 0xc0, 0x00, 0x00, 0x00, 0x03, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0xf8, 0xff, 0xe3, 0xfc, 0x1f, 0xc7, 0xf1, 0xc3, 0x81, 0xc7, 0xfc, 0x01, 0xc3, 0x80, 0x3f, 
0xf0, 0x71, 0xe0, 0x70, 0xe7, 0x07, 0x07, 0x81, 0xff, 0x1f, 0xf1, 0xfe, 0x1f, 0xf1, 0xfe, 0x38, 
0xe3, 0x8f, 0xf1, 0xc1, 0xe7, 0x07, 0x9c, 0x78, 0xff, 0xc7, 0x83, 0xc7, 0x07, 0x07, 0xfc, 0x7f, 
0xf1, 0xff, 0x07, 0xfb, 0xff, 0xb8, 0x70, 0x3f, 0x03, 0x8f, 0x38, 0x38, 0xe0, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x3f, 0x00, 0xe0, 0x1c, 0x70, 0x07, 0xf8, 0x70, 0xff, 0x0f, 0xf8, 0x00, 0x07, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3b, 0x8f, 0xf0, 
0x18, 0x00, 0x00, 0x3b, 0xb9, 0xc0, 0x00, 0x00, 0x00, 0x07, 0xf8, 0x0e, 0x00, 0x66, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1c, 0x03, 0x80, 0x78, 0x4e, 0x11, 0xc3, 0x81, 0xc7, 0x1c, 0x01, 0xc3, 0x80, 0x07, 
0x00, 0x73, 0xc0, 0x70, 0xe7, 0x07, 0x0f, 0xc1, 0xc0, 0x1c, 0x79, 0xc0, 0x1c, 0x71, 0xc0, 0x1c, 
0xe7, 0x04, 0x79, 0xc3, 0xe7, 0x0f, 0x9c, 0xf0, 0xe1, 0xc6, 0xc6, 0xc7, 0x07, 0x0f, 0x1e, 0x70, 
0x71, 0xc7, 0x87, 0x08, 0x38, 0x38, 0x70, 0xff, 0xc1, 0xce, 0x38, 0x38, 0xe0, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x00, 0xe0, 0x1c, 0x70, 0x04, 0x3c, 0x71, 0xe7, 0x9e, 0x38, 0x00, 0x0f, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xf0, 
0x30, 0x00, 0x00, 0x38, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x07, 0xf8, 0x0c, 0x00, 0x3c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1c, 0x03, 0x80, 0x70, 0x0e, 0x01, 0xc3, 0x81, 0xc7, 0x1c, 0x01, 0xc3, 0x80, 0x07, 
0x00, 0x73, 0x80, 0x38, 0xe7, 0x07, 0x0f, 0xc1, 0xc0, 0x1c, 0x39, 0xc0, 0x1c, 0x71, 0xc0, 0x1c, 
0xe7, 0x00, 0x39, 0xc3, 0xe7, 0x0f, 0x9c, 0xe0, 0xe1, 0xc6, 0xc6, 0xc7, 0x07, 0x0e, 0x0e, 0x70, 
0x71, 0xc3, 0x8e, 0x00, 0x38, 0x1c, 0x71, 0xdd, 0xe1, 0xce, 0x38, 0x38, 0xe0, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x00, 0xe0, 0x1c, 0x70, 0x00, 0x1c, 0x71, 0xc3, 0x9c, 0x38, 0x00, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1c, 0x03, 0x80, 0xe0, 0x0f, 0x01, 0xc3, 0x81, 0xc7, 0x1c, 0x01, 0xc3, 0x80, 0x07, 
0x00, 0x77, 0x00, 0x38, 0xc7, 0x07, 0x0d, 0xc1, 0xc0, 0x1c, 0x39, 0xc0, 0x1c, 0x71, 0xc0, 0x0e, 
0xee, 0x00, 0x39, 0xc7, 0xe7, 0x1f, 0x9d, 0xc0, 0xe1, 0xc6, 0xc6, 0xc7, 0x07, 0x1c, 0x07, 0x70, 
0x71, 0xc3, 0x9c, 0x00, 0x38, 0x1c, 0xe1, 0x9c, 0xe0, 0xfc, 0x38, 0x38, 0xe0, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x00, 0xe0, 0x1c, 0x70, 0x00, 0x0e, 0x71, 0x81, 0xdc, 0x38, 0x78, 0x3b, 0xc3, 
0xf8, 0x7f, 0x3f, 0xc0, 0xf1, 0xe7, 0x3c, 0x78, 0x71, 0xe7, 0x1e, 0x73, 0xc7, 0xf8, 0xf1, 0xe7, 
0x1c, 0x0f, 0x07, 0xfc, 0x77, 0x80, 0xff, 0xff, 0x87, 0x0f, 0xe3, 0xcf, 0x71, 0xc7, 0x1c, 0xe3, 
0x9c, 0xe3, 0x9c, 0xfc, 0x03, 0x80, 0xe3, 0x80, 0xfc, 0x1c, 0x3c, 0x0f, 0xe0, 0x0f, 0x07, 0x70, 
0xfe, 0x1f, 0x1f, 0x38, 0xe1, 0xc7, 0xf8, 0x0e, 0x38, 0x03, 0xb8, 0x73, 0xc1, 0xc3, 0xb8, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1f, 0xe3, 0x80, 0xe0, 0x0f, 0x81, 0xc3, 0x81, 0xc7, 0x1f, 0xc1, 0xc3, 0xf8, 0x07, 
0xf0, 0x77, 0x00, 0x1d, 0xc7, 0x07, 0x1c, 0xc1, 0xfc, 0x1c, 0x71, 0xc0, 0x1c, 0x71, 0xc0, 0x06, 
0xec, 0x00, 0x71, 0xc6, 0xe7, 0x1b, 0x9d, 0xc0, 0xe1, 0xc6, 0xee, 0xc7, 0x07, 0x1c, 0x07, 0x70, 
0x71, 0xc3, 0x9c, 0x00, 0x38, 0x0e, 0xe3, 0x9c, 0x70, 0xfc, 0x38, 0x38, 0xe0, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0xe0, 0xfe, 0x1c, 0x7f, 0x00, 0x0e, 0x73, 0x81, 0xdc, 0x38, 0xfc, 0x37, 0xe3, 
0xfe, 0x7f, 0x3f, 0xc3, 0xf8, 0xe7, 0x39, 0xfe, 0x73, 0xe7, 0x3e, 0x73, 0x87, 0xf8, 0xf1, 0xe7, 
0x1c, 0x3f, 0xc7, 0xfc, 0x7f, 0xc3, 0xf7, 0xfd, 0xce, 0x1f, 0xf1, 0xce, 0x71, 0xc7, 0x1c, 0xe3, 
0x9c, 0xe3, 0x9c, 0xfc, 0x03, 0x80, 0xe3, 0x80, 0x7f, 0x1c, 0x7f, 0x1f, 0xe0, 0x3f, 0x87, 0xf8, 
0xfe, 0x7f, 0x3e, 0x38, 0xe1, 0xc7, 0xf8, 0x0e, 0x38, 0x03, 0xfc, 0x73, 0x81, 0xe3, 0xb8, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xf8, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0xf8, 0x1f, 0xf3, 0x80, 0xff, 0x87, 0xe1, 0xc3, 0x81, 0xc7, 0x1f, 0xf1, 0xff, 0xfe, 0x07, 
0xf8, 0x7e, 0x00, 0x1d, 0xc7, 0x07, 0x1c, 0xe1, 0xff, 0x1f, 0xe1, 0xc0, 0x1c, 0x71, 0xfe, 0x07, 
0xfc, 0x03, 0xe1, 0xce, 0xe7, 0x3b, 0x9f, 0x80, 0xe1, 0xc6, 0xec, 0xe7, 0xff, 0x1c, 0x07, 0x70, 
0x71, 0xc7, 0x9c, 0x00, 0x38, 0x0e, 0xe3, 0x9c, 0x70, 0x78, 0x38, 0x38, 0xf0, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0xf8, 0xff, 0x9c, 0x7f, 0xc3, 0xfe, 0x7f, 0x81, 0xce, 0x38, 0x8e, 0x3c, 0xe3, 
0x8e, 0x70, 0x39, 0xc3, 0x98, 0x77, 0x70, 0x8e, 0x73, 0xe7, 0x3e, 0x77, 0x07, 0x38, 0xfb, 0xe7, 
0x1c, 0x39, 0xc7, 0x1c, 0x79, 0xc3, 0x80, 0xe1, 0xce, 0x3b, 0xb8, 0xec, 0x71, 0xc7, 0x1c, 0xe3, 
0x9c, 0xe3, 0x9c, 0x1c, 0x03, 0x80, 0xe3, 0x80, 0x07, 0x1c, 0xe7, 0x38, 0xe0, 0x39, 0x87, 0x38, 
0xe0, 0x70, 0x72, 0x38, 0xe1, 0xc7, 0x38, 0x0e, 0x38, 0x03, 0x8e, 0x77, 0x00, 0xe7, 0x38, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0xf8, 0x1c, 0x73, 0x80, 0xff, 0x83, 0xf1, 0xc3, 0x81, 0xc7, 0x1c, 0x79, 0xff, 0x8f, 0x07, 
0x1c, 0x7f, 0x80, 0x1d, 0x87, 0x07, 0x1c, 0xe1, 0xc7, 0x9f, 0xf1, 0xc0, 0x1c, 0x71, 0xfe, 0x1f, 
0xff, 0x03, 0xe1, 0xcc, 0xe7, 0x33, 0x9f, 0xe0, 0xe1, 0xce, 0x6c, 0xe7, 0xff, 0x1c, 0x07, 0x70, 
0x71, 0xff, 0x1c, 0x00, 0x38, 0x0e, 0xc3, 0x9c, 0x70, 0x78, 0x38, 0x38, 0x7f, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x3c, 0xe3, 0xdc, 0x71, 0xe3, 0xfe, 0x7f, 0x81, 0xc7, 0xf8, 0x3e, 0x38, 0x73, 
0x8e, 0x70, 0x39, 0xc7, 0x1c, 0x37, 0x60, 0x0e, 0x77, 0xe7, 0x7e, 0x76, 0x07, 0x38, 0xdb, 0x67, 
0x1c, 0x70, 0xe7, 0x1c, 0x70, 0xe7, 0x00, 0xe1, 0xce, 0x73, 0x9c, 0xfc, 0x71, 0xc7, 0x1c, 0xe3, 
0x9c, 0xe3, 0x9c, 0x1f, 0xc3, 0xf8, 0xe3, 0xf8, 0x07, 0x9c, 0xe3, 0xb8, 0xe0, 0x71, 0xc7, 0x1c, 
0xe0, 0xe0, 0x78, 0x38, 0xe1, 0xc7, 0x3f, 0x8e, 0x3f, 0x83, 0x8e, 0x76, 0x00, 0xe7, 0x38, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1c, 0x3b, 0x80, 0xe0, 0x00, 0xf9, 0xc3, 0x81, 0xc7, 0x1c, 0x39, 0xc3, 0x87, 0x07, 
0x1c, 0x73, 0x80, 0x0f, 0x87, 0x07, 0x1f, 0xe1, 0xc3, 0x9c, 0x71, 0xc0, 0x18, 0x71, 0xc0, 0x1c, 
0xe7, 0x00, 0x71, 0xdc, 0xe7, 0x73, 0x9c, 0xe0, 0xe1, 0xce, 0x6c, 0xe7, 0x07, 0x1c, 0x07, 0x70, 
0x71, 0xfc, 0x1c, 0x00, 0x38, 0x07, 0xc3, 0x9c, 0x70, 0xfc, 0x38, 0x38, 0x3e, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x1c, 0xe1, 0xdc, 0x70, 0xe0, 0x0e, 0x73, 0x81, 0xc7, 0xf8, 0xfe, 0x38, 0x73, 
0xfc, 0x70, 0x39, 0xc7, 0xfc, 0x3f, 0xe0, 0x7c, 0x76, 0xe7, 0x6e, 0x7e, 0x07, 0x38, 0xdb, 0x77, 
0xfc, 0x70, 0xe7, 0x1c, 0x70, 0xe7, 0x00, 0xe0, 0xec, 0x73, 0x9c, 0x78, 0x71, 0xc7, 0x1c, 0xe3, 
0x9c, 0xe3, 0x9c, 0x1f, 0xe3, 0xfc, 0xe3, 0xfc, 0x7f, 0x9f, 0xc3, 0x9f, 0xe0, 0x7f, 0xc7, 0x1c, 
0xe0, 0xfe, 0x3e, 0x38, 0xe1, 0xc7, 0x3f, 0xcf, 0xff, 0xc3, 0x8e, 0x7e, 0x00, 0x77, 0x38, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xfc, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1c, 0x3b, 0x80, 0xe0, 0x00, 0x39, 0xc3, 0x81, 0xc7, 0x1c, 0x39, 0xc3, 0x87, 0x07, 
0x1c, 0x71, 0xc0, 0x0f, 0x87, 0x07, 0x3f, 0xf1, 0xc3, 0x9c, 0x39, 0xc0, 0x18, 0x71, 0xc0, 0x38, 
0xe3, 0x80, 0x39, 0xd8, 0xe7, 0x63, 0x9c, 0x70, 0xe1, 0xce, 0x7c, 0xe7, 0x07, 0x1c, 0x07, 0x70, 
0x71, 0xc0, 0x1c, 0x00, 0x38, 0x07, 0x83, 0x9c, 0xe0, 0xfc, 0x38, 0x38, 0x00, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x1c, 0xe1, 0xdc, 0x70, 0xe0, 0x0e, 0x73, 0x81, 0xcf, 0x39, 0xce, 0x38, 0x73, 
0xfc, 0x70, 0x39, 0xc7, 0xfc, 0x7f, 0xf0, 0xfc, 0x76, 0xe7, 0x6e, 0x7f, 0x07, 0x39, 0xdb, 0x77, 
0xfc, 0x70, 0xe7, 0x1c, 0x70, 0xe7, 0x00, 0xe0, 0xec, 0x73, 0x9c, 0x78, 0x71, 0xc3, 0xfc, 0xe3, 
0x9c, 0xe3, 0x9c, 0x1c, 0x73, 0x8e, 0xe3, 0x8e, 0x7f, 0x9f, 0xc3, 0x8f, 0xe0, 0x7f, 0xc7, 0x1c, 
0xe0, 0xfe, 0x1e, 0x38, 0xe1, 0xc7, 0x38, 0xef, 0xf8, 0xe3, 0x8e, 0x7f, 0x00, 0x76, 0x38, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xfc, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1c, 0x3b, 0x80, 0x70, 0x00, 0x39, 0xc3, 0x81, 0xce, 0x1c, 0x39, 0xc3, 0x87, 0x07, 
0x1c, 0x71, 0xc0, 0x07, 0x07, 0x07, 0x38, 0x71, 0xc3, 0x9c, 0x39, 0xc0, 0x38, 0x71, 0xc0, 0x38, 
0xe3, 0x80, 0x39, 0xf8, 0xe7, 0xe3, 0x9c, 0x71, 0xc1, 0xce, 0x78, 0xe7, 0x07, 0x0e, 0x0e, 0x70, 
0x71, 0xc0, 0x0e, 0x00, 0x38, 0x03, 0x81, 0xdd, 0xe1, 0xce, 0x38, 0x38, 0x00, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x1c, 0xe1, 0xdc, 0x70, 0xe0, 0x1c, 0x71, 0xc3, 0x8e, 0x39, 0xce, 0x18, 0x73, 
0x8e, 0x70, 0x71, 0xc7, 0x00, 0xf7, 0x78, 0x0e, 0x7c, 0xe7, 0xce, 0x73, 0x87, 0x39, 0xde, 0x77, 
0x1c, 0x70, 0xe7, 0x1c, 0x70, 0xe7, 0x00, 0xe0, 0x7c, 0x73, 0x9c, 0xfc, 0x71, 0xc3, 0xfc, 0xe3, 
0x9c, 0xe3, 0x9c, 0x1c, 0x73, 0x8e, 0xe3, 0x8e, 0x07, 0x9d, 0xe3, 0x9c, 0xe0, 0x70, 0x07, 0x1c, 
0xe0, 0xe0, 0x07, 0x38, 0xe1, 0xc7, 0x38, 0xee, 0x38, 0xe3, 0x8e, 0x73, 0x80, 0x7e, 0x38, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x1c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0x00, 0x1c, 0x3b, 0x80, 0x78, 0x48, 0x39, 0xc3, 0x83, 0xde, 0x1c, 0x79, 0xc3, 0x8f, 0x07, 
0x1c, 0x71, 0xe0, 0x0f, 0x07, 0x07, 0x38, 0x71, 0xc7, 0x9c, 0x79, 0xc0, 0x38, 0x71, 0xc0, 0x38, 
0xe3, 0x88, 0x79, 0xf0, 0xe7, 0xc3, 0x9c, 0x7b, 0xc1, 0xce, 0x38, 0xe7, 0x07, 0x0f, 0x1e, 0x70, 
0x71, 0xc0, 0x0f, 0x00, 0x38, 0x07, 0x80, 0xff, 0xc1, 0xce, 0x38, 0x38, 0x00, 0xe7, 0x0e, 0x39, 
0xc3, 0x8e, 0x07, 0x3c, 0xe3, 0xdc, 0x71, 0xe4, 0x3c, 0x71, 0xe7, 0x9c, 0x39, 0xce, 0x1c, 0xe3, 
0x8e, 0x70, 0x71, 0xc3, 0x80, 0xe7, 0x39, 0x0e, 0x7c, 0xe7, 0xce, 0x73, 0x8e, 0x39, 0xce, 0x77, 
0x1c, 0x39, 0xc7, 0x1c, 0x79, 0xc3, 0x80, 0xe0, 0x78, 0x3b, 0xb8, 0xfc, 0x71, 0xc0, 0x1c, 0xe3, 
0x9c, 0xe3, 0x9c, 0x1c, 0x73, 0x9e, 0xe3, 0x8e, 0x07, 0x1c, 0xe7, 0x38, 0xe0, 0x38, 0x07, 0x1c, 
0xe0, 0x70, 0x47, 0x38, 0xe1, 0xce, 0x38, 0xee, 0x38, 0xe3, 0x8e, 0x73, 0x80, 0x3e, 0x38, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x38, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0xf8, 0x1c, 0x3b, 0x80, 0x3f, 0xcf, 0xf1, 0xc3, 0x9f, 0x9c, 0x1f, 0xf1, 0xc3, 0xfe, 0x07, 
0x1c, 0x70, 0xe0, 0x3e, 0x07, 0xff, 0x70, 0x79, 0xff, 0x1f, 0xf1, 0xc0, 0x7f, 0xf9, 0xfe, 0x70, 
0xe1, 0xcf, 0xf1, 0xf0, 0xe7, 0xc3, 0x9c, 0x3b, 0x81, 0xce, 0x38, 0xe7, 0x07, 0x07, 0xfc, 0x70, 
0x71, 0xc0, 0x07, 0xf8, 0x38, 0x1f, 0x00, 0x7f, 0x03, 0x87, 0x3f, 0xfc, 0x00, 0xe7, 0xff, 0xf9, 
0xff, 0xff, 0x07, 0xf8, 0xff, 0x9c, 0x7f, 0xc7, 0xf8, 0x70, 0xff, 0x1c, 0x39, 0xfe, 0x0f, 0xe3, 
0xfc, 0x70, 0xff, 0xe3, 0xf8, 0xe7, 0x39, 0xfe, 0x78, 0xe7, 0x8e, 0x71, 0xde, 0x39, 0xce, 0x77, 
0x1c, 0x3f, 0xc7, 0x1c, 0x7f, 0xc3, 0xf8, 0xe0, 0x78, 0x1f, 0xf1, 0xce, 0x7f, 0xe0, 0x1c, 0xff, 
0xfc, 0xff, 0xfe, 0x1f, 0xe3, 0xfc, 0xe3, 0xfc, 0xfe, 0x1c, 0x7f, 0x38, 0xe0, 0x3f, 0x87, 0x1c, 
0xe0, 0x7f, 0x7e, 0x38, 0xe1, 0xde, 0x3f, 0xce, 0x3f, 0xc3, 0x8e, 0x71, 0xc0, 0x3c, 0x3f, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xf0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x07, 0xf8, 0x1c, 0x73, 0x80, 0x0f, 0xc7, 0xc1, 0xc3, 0x9f, 0x18, 0x1f, 0xc1, 0xc3, 0xf8, 0x07, 
0x1c, 0x70, 0xf0, 0x38, 0x07, 0xff, 0x70, 0x39, 0xfc, 0x1f, 0xc1, 0xc0, 0x7f, 0xf9, 0xfe, 0x70, 
0xe1, 0xc7, 0xc1, 0xe0, 0xe7, 0x83, 0x9c, 0x3f, 0x01, 0xce, 0x30, 0xe7, 0x07, 0x01, 0xf0, 0x70, 
0x71, 0xc0, 0x01, 0xf8, 0x38, 0x1c, 0x00, 0x1c, 0x07, 0x87, 0xbf, 0xfc, 0x00, 0xe7, 0xff, 0xf9, 
0xff, 0xff, 0x07, 0xe0, 0xfe, 0x1c, 0x7f, 0x07, 0xe0, 0x70, 0x7c, 0x3c, 0x38, 0xee, 0x07, 0x83, 
0xf8, 0x70, 0xff, 0xe0, 0xf9, 0xc7, 0x1c, 0xf8, 0x78, 0xe7, 0x8e, 0x71, 0xcc, 0x39, 0xce, 0x77, 
0x1c, 0x0f, 0x07, 0x1c, 0x77, 0x80, 0xf8, 0xe0, 0x38, 0x0f, 0xe3, 0xcf, 0x7f, 0xe0, 0x1c, 0xff, 
0xfc, 0xff, 0xfe, 0x1f, 0x83, 0xf0, 0xe3, 0xf0, 0x7c, 0x1c, 0x3c, 0x78, 0xe0, 0x0f, 0x87, 0x38, 
0xe0, 0x1f, 0x7c, 0x38, 0xe1, 0xcc, 0x3f, 0x0e, 0x3f, 0x03, 0x8e, 0x71, 0xc0, 0x1c, 0x3f, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x70, 0x03, 0x80, 0x00, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x07, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xf0, 0x03, 0x80, 0x00, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 
0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x07, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xe0, 0x03, 0x80, 0x00, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x07, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xc0, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 


};

xFONTYY MyriadPro16bRus_Font = {0x01, 25, 0, 25, 0, 0, 25, 226, 0x0400, 0x04ff,
(PEGUSHORT *) MyriadPro16bRus_offset_table, &Tahoma16bTai_Font,
(PEGUBYTE *) MyriadPro16bRus_data_table};


