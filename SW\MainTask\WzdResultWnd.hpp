#ifndef __WIZARD_RESULT_WND_HPP__
#define __WIZARD_RESULT_WND_HPP__

#include "Wnd.hpp"
#include "Ship.hpp"

class CWzdResultWnd : public CWnd {
	private:
		int m_nCurPageNo;
		
	public:
		enum {
			WZD_RES_STATIC_PAGE = 0,
			WZD_RES_VOYAGE_PAGE,
			WZD_RES_ANT_POS_PAGE,
			MAX_WZD_RES_PAGE
		};

	private:
		void InitVar();
		
	public:
		CWzdResultWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		virtual void OnActivate();
		void DrawFuncBtn();

		void DrawMMSI(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawIMO(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawTargetShipName(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawCallSign(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawStaticDataPage();

		void DrawDestination(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawETA(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawShipCargoType(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawNavStatus(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawDraught(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawPersons(CShip *pShip, FONT *pFont, int nLangMode);
		void DrawVoyageDataPage();

		void DrawShipShape(int nLangMode);
		void DrawAntPosData(CShip *pShip, int nLangMode);
		void DrawAntPosPage();
		
		void DrawWnd(BOOL bRedraw = TRUE);
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
};

#endif	// End of __WIZARD_RESULT_WND_HPP__

