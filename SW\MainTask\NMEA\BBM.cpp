#include <stdio.h>
#include "BBM.hpp"

CBbm::CBbm() : CSentence()
{
}
    
CBbm::CBbm(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CBbm::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_BBM;
}

/******************************************************************************
*
* BBM - Broadcast Binary Message
*
* $--BBM,x,x,x,x,x.x,s--s,x*hh<CR><LF>
*        | | | | |   |    |
*        1 2 3 4 5   6    7
*
* 1. Total number of sentence needed to transfer the message , 1 to 9
* 2. Sentence number ,1 to 9
* 3. Sequential message identifier , 0 to 9
* 4. AIS channel for broadcast of the radio message
* 5. ITU-R M.1371 message ID , 8 or 14
* 6. Encapsulated data
* 7. Number of fill-bits , 0 to 5
*
******************************************************************************/
void CBbm::Parse()
{
	m_nTotalNo     = GetFieldInteger(1);     // 1 to 9
	m_nSentNo      = GetFieldInteger(2);     // 1 to 9
	m_nSeqId       = GetFieldInteger(3);     // 0 to 3
	m_nChannel     = GetFieldInteger(5);     // 0 to 3 
	m_nMsgID       = GetFieldInteger(6);     // 8 or 14
	GetFieldString(7, m_szMessage);
	m_nFillBitsNo  = GetFieldInteger(8);     // 0 to 5
}

int CBbm::MakeSentence(BYTE *pszSentence)
{
	sprintf((char *)pszSentence, "!AIBBM,%d,%d,%d,%d,%d,%s,%d",
		m_nTotalNo, m_nSentNo, m_nSeqId, m_nChannel,
		m_nMsgID, m_szMessage, m_nFillBitsNo);
	CSentence::SendMakeNmeaCsData(pszSentence);
	
	return strlen((char *)pszSentence);
}

void CBbm::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}
