#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "ComboCtrl.hpp"
#include "CheckCtrl.hpp"
#include "Regional.hpp"

#ifndef __REGIONAL_VIEW_WND_HPP__
#define __REGIONAL_VIEW_WND_HPP__

class CRegionalViewWnd : public CWnd {
public:
	enum {
		MODE_RXTX = 0,
		MODE_RX,
		MODE_NONE
	};

	enum {
		MODE_CREATE = 0,
		MODE_VIEW_EDIT
	};

	enum{
		ALARM_MODE_NONE = 0,
		ALARM_MODE_CONF_SAVE,
		ALARM_MODE_CONF_DEF,
		ALARM_MODE_CONF_INVALID_CHA,
		ALARM_MODE_CONF_INVALID_CHB,
		ALARM_MODE_CONF_INVALID_NE_LAT,
		ALARM_MODE_CONF_INVALID_NE_LON,
		ALARM_MODE_CONF_INVALID_SW_LAT,
		ALARM_MODE_CONF_INVALID_SW_LON,
		ALARM_MODE_CONF_ROS_AREA_TOO_NARROW,
		ALARM_MODE_CONF_ROS_AREA_TOO_WIDE,
		MAX_ALARM_MODE			
	};

protected:
	enum {
		FOCUS_CH_A = 0,
		FOCUS_BW_A,
		FOCUS_MODE_A,
		FOCUS_CH_B,
		FOCUS_BW_B,
		FOCUS_MODE_B,
		FOCUS_POWER,
		FOCUS_ZONE_SIZE,
		FOCUS_LAT_NE,
		FOCUS_LON_NE,
		FOCUS_LAT_SW,
		FOCUS_LON_SW
	};
	
	int  m_nFocus;
	int  m_nMode;   /*0:CREATE MODE, 1:VIEW&EDIT MODE*/
	int  m_nRegionalDataIndex;

	int  m_nCurAlarmMode;
	BOOL m_bSetDefByUser;
	CRegional *m_pCurRegionData;

	BOOL m_bInUse;
	char m_chInfoSource;
	BYTE m_szUpdateTime[12];
	BYTE m_szUseTime[8];

	CEditCtrl *m_pChAEdit;
	CEditCtrl *m_pChBEdit;
	CEditCtrl *m_pNELatEdit, *m_pNELonEdit;
	CEditCtrl *m_pSWLatEdit, *m_pSWLonEdit;

	CComboCtrl *m_pBWACombo, *m_pBWBCombo;
	CComboCtrl *m_pModeACombo, *m_pModeBCombo;
	CComboCtrl *m_pPowerCombo;
	CComboCtrl *m_pZoneSizeCombo;

public:
	CRegionalViewWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
	~CRegionalViewWnd();

	void OnKeyEvent(int nKey, DWORD nFlags);
	void OnCursorEvent(int nState);

	virtual void OnActivate();
	void DrawRegionStatus();

	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	int  GetFocus() { return m_nFocus; }
	void SetMode(int nMode/*0:CREATE MODE, 1:VIEW&EDIT MODE*/);/* {
		m_nMode = nMode;
		if( nMode == MODE_CREATE )
			SetCaption(CS_REGIONAL_CREATE);
		else
			SetCaption(CS_REGIONAL_VIEW);
	}*/
	int  GetMode() { return m_nMode; }

	void SetDefByUser(BOOL bSet);
	void SetAlarmMode(int nMode) { m_nCurAlarmMode = nMode; }
	int GetAlarmMode() { return m_nCurAlarmMode; }
	
	BOOL CheckLat(BYTE *pszData);
	BOOL CheckLon(BYTE *pszData);

// 	int  GetSameRegionalAreaOnBackupData(CRegional &region);
// 	int  GetSameRegionalAreaOnBackupData(int nLatNE, int nLonNE, int nLatSW, int nLonSW);
// 	int  GetNotUsedRegionalBackData();
	void SetRegionalData(int nRegionalDataIndex);
	int  GetCurRegionDataIdx() { return m_nRegionalDataIndex; }
	int CheckChannel();
	int CheckRoaSizeIsValid(float fNELat, float fNELon, float fSWLat, float fSWLon);
	int CheckPos();
	int CheckInputData();
	void ProcConfInvChAVal(int nResult);
	void ProcConfInvChBVal(int nResult);
	void ProcConfInvNELatVal(int nResult);
	void ProcConfInvNELonVal(int nResult);
	void ProcConfInvSWLatVal(int nResult);
	void ProcConfInvSWLonVal(int nResult);
	void ProcConfROSAreaTooNarrow(int nResult);
	void ProcConfROSAreaTooWide(int nResult);
	void SendRegionDataEditStatus(int nIdx, int nInUse);

	void ResetRegionalData();
	void ProcConfSaveAlarm(int nResult);
	void ProcConfDeftAlarm(int nResult);

	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	void ComboCollapse() {
		m_pBWACombo->Collapse();
		m_pBWBCombo->Collapse();
		m_pModeACombo->Collapse();
		m_pModeBCombo->Collapse();
		m_pPowerCombo->Collapse();
		m_pZoneSizeCombo->Collapse();
	}
};

#endif



