/*...........................................................................*/
/*.                  File Name : cTIME.HPP                                  .*/
/*.                                                                         .*/
/*.                       Date : 2004.02.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"

#ifndef  __TIME_HPP
#define  __TIME_HPP

///////////////////////////////////////////////////////////////////////////////
#define  WATCH_DOG_RESET_CHECK_MEMORY      (0x03feff00 +  0 * 1024 * 1024)

#define  WATCH_DOG_RESET_CHECK_DATA0       (0xaabbccdd)
#define  WATCH_DOG_RESET_CHECK_DATA1       (0xeeff3030)
#define  WATCH_DOG_RESET_CHECK_DATA2       (0x01234567)
#define  WATCH_DOG_RESET_CHECK_DATA3       (0x89abcdef)
///////////////////////////////////////////////////////////////////////////////

class cTIME
{
   private:
      static DWORD m_dTickCount;
      static DWORD m_dDownTickX;
      static DWORD m_dDownTickY;
      static DWORD m_dWatchMode;
      static DWORD m_dWatchDogC;
   private:

   public:
      cTIME(void);
      virtual ~cTIME(void);
   public:
      static DWORD GetSysTickCounter(void);
      static int   Get100thSecond(void);
      static DWORD GetDiffMiliTime(DWORD Tick);
      static void  ClearWatchDogCounter(void);
      static void  ClearWatchDogCheckMemory(void);
      static int   IsWatchDogReset(void);
      static DWORD SetWatchDogEnable(void);
      static DWORD SetWatchDogDisable(void);
      static void  RunIntHandler(void);
      static void  ReStartSystem(void);
};

#endif

