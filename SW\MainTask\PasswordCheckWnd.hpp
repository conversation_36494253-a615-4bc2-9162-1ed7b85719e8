#include "Wnd.hpp"
#include "EditCtrl.hpp"

#ifndef __PASSWORD_CHECK_WND_HPP__
#define __PASSWORD_CHECK_WND_HPP__

class CPasswordCheckWnd : public CWnd {
public:
	enum {
		PASSWORD_MANUFACTURER = 0,
		PASSWORD_MASTER,
		PASSWORD_USER_MASTER,
		PASSWORD_USER,
		PASSWORD_GBCODE,
		PASSWORD_MASTER_OR_USER
	};
	
private:
	CEditCtrl *m_pPassword;
	
	int m_nPasswordType;
	
public:
	CPasswordCheckWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
	
	void OnKeyEvent(int nKey, DWORD nFlags);
	void OnCursorEvent(int nState);
	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	
	void SetPasswordType(int nPasswordType=PASSWORD_USER) { m_nPasswordType = nPasswordType; }
	BOOL IsPasswordOK();
	
	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	void DrawErrorMsg();
	void ResetPasswordBox() {
		m_pPassword->Reset();
	}
};

#endif
