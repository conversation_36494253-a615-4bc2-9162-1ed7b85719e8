/***************************************************************************/
/*                                                                         */
/*              link script : link.xn                                      */
/*                                                                         */
/***************************************************************************/


ENTRY(__reset_handler)	         		/* Entry point of application		*/
OUTPUT_ARCH("mips:isa32")
/*OUTPUT_FORMAT("elf32-littlemips")*/
OUTPUT_FORMAT("elf32-bigmips")
__DYNAMIC  =  0;

SECTIONS
{
  /**** Code and read-only data ****/

  .text 0x80000000 :
  {
    _ftext = ABSOLUTE(.) ;	/* Start of code and read-only data	*/
    start.o (.text)		/* Entry point				*/
    *(.text)
    _ecode = ABSOLUTE(.) ;	/* End of code				*/

    *(.rodata)

    . = ALIGN(8);
    _etext = ABSOLUTE(.);	/* End of code and read-only data	*/
  }

  /**** Initialised data ****/

  . = ALIGN(16);

  .data :
  {
    _fdata = ABSOLUTE(.);	/* Start of initialised data		*/
    *(.data)

    . = ALIGN(8);

    _gp = ABSOLUTE(. + 0x7ff0); /* Base of small data			*/

    *(.lit8)
    *(.lit4)
    *(.sdata)

    . = ALIGN(8);

    _edata  = ABSOLUTE(.);	/* End of initialised data		*/
  }

  /**** Uninitialised data ****/

  _fbss = .;			/* Start of uninitialised data		*/

  .sbss :
  {
    *(.sbss)
    *(.scommon)
  } /* > RAM = 0 */
  .bss :
  {
    *(.bss)
    *(COMMON)
  }

  _end = . ;			/* End of unitialised data		*/

  .gptab.sdata : { *(.gptab.data) *(.gptab.sdata) }
  .gptab.sbss : { *(.gptab.bss) *(.gptab.sbss) }

  /DISCARD/ :
  {
    *(.reginfo)
  }

  /* Allocate room for heap */
  .   =  ALIGN(16);
  _heap_start = .;
  .  += 0x100000;         /* STACK_SIZE = 1 MB */
  _heap_last  = . - 16;

  /* Allocate room for stack */
  .   =  ALIGN(16);
  .  += 0x200000;         /* STACK_SIZE = 2 MB */
  _sp =  . - 16;

  . = ALIGN(16);
  _StartOfFreeRam = .;    /* Start of free ram */

  PROVIDE(etext = _etext);
  PROVIDE (edata = .);
  PROVIDE (end = .);
}


