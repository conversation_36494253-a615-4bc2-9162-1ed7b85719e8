//------------------------------------------------------------------------------
// jpegdecoder.h
// Small JPEG Decoder Library v0.93b
// Last updated: Dec. 28, 2001
// Copyright (C) 1994-2000 <PERSON>
// <EMAIL>
//
// This library is free software; you can redistribute it and/or
// modify it under the terms of the GNU Lesser General Public
// License as published by the Free Software Foundation; either
// version 2.1 of the License, or (at your option) any later version.
//
// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
// Lesser General Public License for more details.
//
// You should have received a copy of the GNU Lesser General Public
// License along with this library; if not, write to the Free Software
// Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
//------------------------------------------------------------------------------
#ifndef JPEG_DECODER_H
#define JPEG_DECODER_H
//------------------------------------------------------------------------------
#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include <string.h>
#include <ctype.h>
#include <setjmp.h>
#include <assert.h>

typedef   signed char  schar;       /*  8 bits     */
typedef unsigned char  uchar;       /*  8 bits     */
typedef   signed short int16;       /* 16 bits     */
typedef unsigned short uint16;      /* 16 bits     */
typedef unsigned short ushort;      /* 16 bits     */
typedef unsigned int   uint;        /* 16/32+ bits */
typedef unsigned long  ulong;       /* 32 bits     */
typedef   signed int   int32;       /* 32+ bits    */

#ifndef max
#define max(a,b) (((a)>(b)) ? (a) : (b))
#endif

#ifndef min
#define min(a,b) (((a)<(b)) ? (a) : (b))
#endif

#ifndef TRUE
#define TRUE (1)
#endif

#ifndef FALSE
#define FALSE (0)
#endif

//------------------------------------------------------------------------------
// Define SUPPORT_X86ASM to include the inline x86 assembler code.
//#define SUPPORT_X86ASM
//------------------------------------------------------------------------------
// Define SUPPORT_MMX to include MMX support.
//#define SUPPORT_MMX
//------------------------------------------------------------------------------
#define JPGD_INBUFSIZE       4096
//------------------------------------------------------------------------------
// May need to be adjusted if support for other colorspaces/sampling factors is added
#define JPGD_MAXBLOCKSPERMCU 10
//------------------------------------------------------------------------------
#define JPGD_MAXHUFFTABLES   8
#define JPGD_MAXQUANTTABLES  4
#define JPGD_MAXCOMPONENTS   4
#define JPGD_MAXCOMPSINSCAN  4
//------------------------------------------------------------------------------
// Increase this if you increase the max width!
#define JPGD_MAXBLOCKSPERROW 6144
//------------------------------------------------------------------------------
// Max. allocated blocks
#define JPGD_MAXBLOCKS    100
//------------------------------------------------------------------------------
#define JPGD_MAX_HEIGHT 8192
#define JPGD_MAX_WIDTH  8192
//------------------------------------------------------------------------------
/* JPEG specific errors */
#define JPGD_BAD_DHT_COUNTS              -200
#define JPGD_BAD_DHT_INDEX               -201
#define JPGD_BAD_DHT_MARKER              -202
#define JPGD_BAD_DQT_MARKER              -203
#define JPGD_BAD_DQT_TABLE               -204
#define JPGD_BAD_PRECISION               -205
#define JPGD_BAD_HEIGHT                  -206
#define JPGD_BAD_WIDTH                   -207
#define JPGD_TOO_MANY_COMPONENTS         -208
#define JPGD_BAD_SOF_LENGTH              -209
#define JPGD_BAD_VARIABLE_MARKER         -210
#define JPGD_BAD_DRI_LENGTH              -211
#define JPGD_BAD_SOS_LENGTH              -212
#define JPGD_BAD_SOS_COMP_ID             -213
#define JPGD_W_EXTRA_BYTES_BEFORE_MARKER -214
#define JPGD_NO_ARITHMITIC_SUPPORT       -215
#define JPGD_UNEXPECTED_MARKER           -216
#define JPGD_NOT_JPEG                    -217
#define JPGD_UNSUPPORTED_MARKER          -218
#define JPGD_BAD_DQT_LENGTH              -219
#define JPGD_TOO_MANY_BLOCKS             -221
#define JPGD_UNDEFINED_QUANT_TABLE       -222
#define JPGD_UNDEFINED_HUFF_TABLE        -223
#define JPGD_NOT_SINGLE_SCAN             -224
#define JPGD_UNSUPPORTED_COLORSPACE      -225
#define JPGD_UNSUPPORTED_SAMP_FACTORS    -226
#define JPGD_DECODE_ERROR                -227
#define JPGD_BAD_RESTART_MARKER          -228
#define JPGD_ASSERTION_ERROR             -229
#define JPGD_BAD_SOS_SPECTRAL            -230
#define JPGD_BAD_SOS_SUCCESSIVE          -231
#define JPGD_STREAM_READ                 -232
#define JPGD_NOTENOUGHMEM                -233
//------------------------------------------------------------------------------
#define JPGD_GRAYSCALE 0
#define JPGD_YH1V1     1
#define JPGD_YH2V1     2
#define JPGD_YH1V2     3
#define JPGD_YH2V2     4
//------------------------------------------------------------------------------
const int JPGD_FAILED = -1;
const int JPGD_DONE = 1;
const int JPGD_OKAY = 0;
//------------------------------------------------------------------------------
typedef enum
{
  M_SOF0  = 0xC0,
  M_SOF1  = 0xC1,
  M_SOF2  = 0xC2,
  M_SOF3  = 0xC3,

  M_SOF5  = 0xC5,
  M_SOF6  = 0xC6,
  M_SOF7  = 0xC7,

  M_JPG   = 0xC8,
  M_SOF9  = 0xC9,
  M_SOF10 = 0xCA,
  M_SOF11 = 0xCB,

  M_SOF13 = 0xCD,
  M_SOF14 = 0xCE,
  M_SOF15 = 0xCF,

  M_DHT   = 0xC4,

  M_DAC   = 0xCC,

  M_RST0  = 0xD0,
  M_RST1  = 0xD1,
  M_RST2  = 0xD2,
  M_RST3  = 0xD3,
  M_RST4  = 0xD4,
  M_RST5  = 0xD5,
  M_RST6  = 0xD6,
  M_RST7  = 0xD7,

  M_SOI   = 0xD8,
  M_EOI   = 0xD9,
  M_SOS   = 0xDA,
  M_DQT   = 0xDB,
  M_DNL   = 0xDC,
  M_DRI   = 0xDD,
  M_DHP   = 0xDE,
  M_EXP   = 0xDF,

  M_APP0  = 0xE0,
  M_APP15 = 0xEF,

  M_JPG0  = 0xF0,
  M_JPG13 = 0xFD,
  M_COM   = 0xFE,

  M_TEM   = 0x01,

  M_ERROR = 0x100
} JPEG_MARKER;
//------------------------------------------------------------------------------
#define RST0 0xD0
//------------------------------------------------------------------------------
typedef struct huff_tables_tag
{
  uint  look_up[256];
  uchar code_size[256];
  // FIXME: Is 512 tree entries really enough to handle _all_ possible
  // code sets? I think so but not 100% positive.
  uint  tree[512];
} huff_tables_t, *Phuff_tables_t;
//------------------------------------------------------------------------------
typedef struct coeff_buf_tag
{
  uchar *Pdata;

  int block_num_x, block_num_y;
  int block_len_x, block_len_y;

  int block_size;

} coeff_buf_t, *Pcoeff_buf_t;
//------------------------------------------------------------------------------
class jpeg_decoder;
typedef void (*Pdecode_block_func)(jpeg_decoder *, int, int, int);
//------------------------------------------------------------------------------
class progressive_block_decoder
{
public:
  static void decode_block_dc_first(
    jpeg_decoder *Pd,
    int component_id, int block_x, int block_y);
  static void decode_block_dc_refine(
    jpeg_decoder *Pd,
    int component_id, int block_x, int block_y);
  static void decode_block_ac_first(
    jpeg_decoder *Pd,
    int component_id, int block_x, int block_y);
  static void decode_block_ac_refine(
    jpeg_decoder *Pd,
    int component_id, int block_x, int block_y);
};
//------------------------------------------------------------------------------
// Input stream interface.
// Derive from this class to fetch input data from sources other than
// files. An important requirement is that you *must* set eof_flag to true
// when no more data is available to fetch!
// The decoder is rather "greedy": it will keep on calling this method until
// its internal input buffer is full, or until the EOF flag is set.
// It the input stream contains data after the JPEG stream's EOI (end of
// image) marker it will probably be pulled into the internal buffer.
// Call the get_total_bytes_read() method to determine the true
// size of the JPEG stream.
class jpeg_decoder_stream
{
public:

  jpeg_decoder_stream()
  {
  }

  virtual ~jpeg_decoder_stream()
  {
  }

  // The read() method is called when the internal input buffer is empty.
  // Pbuf - input buffer
  // max_bytes_to_read - maximum bytes that can be written to Pbuf
  // Peof_flag - set this to true if at end of stream (no more bytes remaining)
  // Return -1 on error, otherwise return the number of bytes actually
  // written to the buffer (which may be 0).
  // Notes: This method will be called in a loop until you set *Peof_flag to
  // true or the internal buffer is full.
  // The MMX state will be automatically saved/restored before this method is
  // called, unlike previous versions.
  virtual int read(uchar *Pbuf, int max_bytes_to_read, bool *Peof_flag) = 0;

  virtual void attach(void)
  {
  }

  virtual void detach(void)
  {
  }
};
//------------------------------------------------------------------------------
typedef jpeg_decoder_stream *Pjpeg_decoder_stream;
//------------------------------------------------------------------------------
// Here's an example FILE stream class.
class jpeg_decoder_file_stream : public jpeg_decoder_stream
{
//  FILE *Pfile;
  bool eof_flag, error_flag;
  uchar *m_pImageData;
  ulong  m_nImageSize;
  ulong  m_nImagePosX;
public:

  jpeg_decoder_file_stream()
  {
//  Pfile = NULL;
    m_pImageData = NULL;
    m_nImageSize = 0;
    m_nImagePosX = 0;
    eof_flag = false;
    error_flag = false;
  }

  void close(void)
  {
//  if (Pfile)
//  {
//    fclose(Pfile);
//    Pfile = NULL;
//  }
    m_pImageData = NULL;
    m_nImageSize = 0;
    m_nImagePosX = 0;

    eof_flag = false;
    error_flag = false;
  }

  virtual ~jpeg_decoder_file_stream()
  {
    close();
  }

  bool open(uchar *pImageData,ulong nImageSize)
  {
    close();

    eof_flag = false;
    error_flag = false;

//  Pfile = fopen(Pfilename, "rb");
//  if (!Pfile)
//    return (true);
    m_pImageData = pImageData;
    m_nImageSize = nImageSize;
    m_nImagePosX = 0;

    return (false);
  }

  virtual int read(uchar *Pbuf, int max_bytes_to_read, bool *Peof_flag)
  {
#if 0
// Empty/clear MMX state: For testing purposes only!
#ifdef _DEBUG
#ifdef SUPPORT_MMX
      _asm
      {
        pxor mm0, mm0
        pxor mm1, mm1
        pxor mm2, mm2
        pxor mm3, mm3
        pxor mm4, mm4
        pxor mm5, mm5
        pxor mm6, mm6
        pxor mm7, mm7
        emms
      }
#endif
#endif
#endif
//  if (!Pfile)
//    return (-1);

    if (eof_flag)
    {
      *Peof_flag = true;
      return (0);
    }

    if (error_flag)
      return (-1);

//  int bytes_read = fread(Pbuf, 1, max_bytes_to_read, Pfile);
    int bytes_read;
    memmove(Pbuf,m_pImageData + m_nImagePosX,max_bytes_to_read);
    bytes_read = max_bytes_to_read;
    m_nImagePosX += max_bytes_to_read;

    if (bytes_read < max_bytes_to_read)
    {
/*
      if (ferror(Pfile))
      {
        error_flag = true;
        return (-1);
      }
*/
      eof_flag = true;
      *Peof_flag = true;
    }

    return (bytes_read);
  }

  bool get_error_status(void)
  {
    return (error_flag);
  }

  bool reset(void)
  {
    if (error_flag)
      return (true);

//  fseek(Pfile, 0, SEEK_SET);
    m_nImagePosX = 0;

    eof_flag = false;

    return (false);
  }

  int get_size(void)
  {
//  if (!Pfile)
//    return (-1);

//  int loc = ftell(Pfile);

//  fseek(Pfile, 0, SEEK_END);

//  int size = ftell(Pfile);

//  fseek(Pfile, loc, SEEK_SET);
    int size = m_nImageSize;

    return (size);
  }
};
//------------------------------------------------------------------------------
typedef jpeg_decoder_file_stream *Pjpeg_decoder_file_stream;
//------------------------------------------------------------------------------
#define QUANT_TYPE int16
#define BLOCK_TYPE int16
//------------------------------------------------------------------------------
// Disable no return value warning, for rol() method
#pragma warning(push)
#pragma warning( disable : 4035 4799 )
//------------------------------------------------------------------------------
class jpeg_decoder
{
  friend class progressive_block_decoder;

private:

  void free_all_blocks(void);

  void terminate(int status);

  void *alloc(int n);

  void word_clear(void *p, ushort c, uint n);

  void prep_in_buffer(void);

  void read_dht_marker(void);

  void read_dqt_marker(void);

  void read_sof_marker(void);

  void skip_variable_marker(void);

  void read_dri_marker(void);

  void read_sos_marker(void);

  int next_marker(void);

  int process_markers(void);

  void locate_soi_marker(void);

  void locate_sof_marker(void);

  int locate_sos_marker(void);

  void init(Pjpeg_decoder_stream Pstream, bool use_mmx);

  void create_look_ups(void);

  void fix_in_buffer(void);

  void transform_row(void);

  Pcoeff_buf_t coeff_buf_open(
    int block_num_x, int block_num_y,
    int block_len_x, int block_len_y);

  void coeff_buf_read(
    Pcoeff_buf_t cb,
    int block_x, int block_y,
    BLOCK_TYPE *buffer);

  void coeff_buf_write(
    Pcoeff_buf_t cb,
    int block_x, int block_y,
    BLOCK_TYPE *buffer);

  BLOCK_TYPE *coeff_buf_getp(
    Pcoeff_buf_t cb,
    int block_x, int block_y);

  void load_next_row(void);

  void decode_next_row(void);
#ifdef SUPPORT_MMX
  void decode_next_row_mmx(void);
#endif

  void make_huff_table(
    int index,
    Phuff_tables_t hs);

  void check_quant_tables(void);

  void check_huff_tables(void);

  void calc_mcu_block_order(void);

  int init_scan(void);

  void init_frame(void);

  void process_restart(void);

  void decode_scan(
    Pdecode_block_func decode_block_func);

  void init_progressive(void);

  void init_sequential(void);

  void decode_start(void);

  void decode_init(Pjpeg_decoder_stream Pstream, bool use_mmx);

  void H2V2Convert(void);
  void H2V1Convert(void);
  void H1V2Convert(void);
  void H1V1Convert(void);
  void GrayConvert(void);

  void find_eoi(void);
//------------------
  inline uint rol(uint i, uchar j);
  inline uint get_char(void);
  inline uint get_char(bool *Ppadding_flag);
  inline void stuff_char(uchar q);
  inline uchar get_octet(void);
  inline uint get_bits_1(int num_bits);
  inline uint get_bits_2(int numbits);
  inline int huff_decode(Phuff_tables_t Ph);
#ifdef SUPPORT_X86ASM
  inline uint jpeg_decoder::huff_extend(uint i, int c);
#endif
  inline uchar clamp(int i);

#ifdef SUPPORT_MMX
  inline uint jpeg_decoder::get_high_byte_mmx(void);
  inline uint jpeg_decoder::get_high_word_mmx(void);
  inline void jpeg_decoder::get_bits_2_mmx_init(void);
  inline void jpeg_decoder::get_bits_2_mmx_deinit(void);
  inline uint jpeg_decoder::get_bits_2_mmx(int numbits);
  inline int jpeg_decoder::huff_decode_mmx(Phuff_tables_t Ph);
#endif
//------------------
  int   image_x_size;
  int   image_y_size;

  Pjpeg_decoder_stream Pstream;

  int   progressive_flag;

  uchar *huff_num[JPGD_MAXHUFFTABLES];  /* pointer to number of Huffman codes per bit size */
  uchar *huff_val[JPGD_MAXHUFFTABLES];  /* pointer to Huffman codes per bit size */

  QUANT_TYPE *quant[JPGD_MAXQUANTTABLES];    /* pointer to quantization tables */

  int   scan_type;                      /* Grey, Yh1v1, Yh1v2, Yh2v1, Yh2v2,
                                           CMYK111, CMYK4114 */

  int   comps_in_frame;                 /* # of components in frame */
  int   comp_h_samp[JPGD_MAXCOMPONENTS];     /* component's horizontal sampling factor */
  int   comp_v_samp[JPGD_MAXCOMPONENTS];     /* component's vertical sampling factor */
  int   comp_quant[JPGD_MAXCOMPONENTS];      /* component's quantization table selector */
  int   comp_ident[JPGD_MAXCOMPONENTS];      /* component's ID */

  int   comp_h_blocks[JPGD_MAXCOMPONENTS];
  int   comp_v_blocks[JPGD_MAXCOMPONENTS];

  int   comps_in_scan;                  /* # of components in scan */
  int   comp_list[JPGD_MAXCOMPSINSCAN];      /* components in this scan */
  int   comp_dc_tab[JPGD_MAXCOMPONENTS];     /* component's DC Huffman coding table selector */
  int   comp_ac_tab[JPGD_MAXCOMPONENTS];     /* component's AC Huffman coding table selector */

  int   spectral_start;                 /* spectral selection start */
  int   spectral_end;                   /* spectral selection end   */
  int   successive_low;                 /* successive approximation low */
  int   successive_high;                /* successive approximation high */

  int   max_mcu_x_size;                 /* MCU's max. X size in pixels */
  int   max_mcu_y_size;                 /* MCU's max. Y size in pixels */

  int   blocks_per_mcu;
  int   max_blocks_per_row;
  int   mcus_per_row, mcus_per_col;

  int   mcu_org[JPGD_MAXBLOCKSPERMCU];

  int   total_lines_left;               /* total # lines left in image */
  int   mcu_lines_left;                 /* total # lines left in this MCU */

  int   real_dest_bytes_per_scan_line;
  int   dest_bytes_per_scan_line;        /* rounded up */
  int   dest_bytes_per_pixel;            /* currently, 4 (RGB) or 1 (Y) */

  void  *blocks[JPGD_MAXBLOCKS];         /* list of all dynamically allocated blocks */

  Phuff_tables_t h[JPGD_MAXHUFFTABLES];

  Pcoeff_buf_t dc_coeffs[JPGD_MAXCOMPONENTS];
  Pcoeff_buf_t ac_coeffs[JPGD_MAXCOMPONENTS];

  int eob_run;

  int block_y_mcu[JPGD_MAXCOMPONENTS];

  uchar *Pin_buf_ofs;
  int in_buf_left;
  int tem_flag;
  bool eof_flag;

  uchar xDummyX[3];
  uchar padd_1[128];
  uchar in_buf[JPGD_INBUFSIZE + 128];
  uchar padd_2[128];

  int   bits_left;
  union
  {
    uint bit_buf;
    uint bit_buf_64[2];
  };

  uint  saved_mm1[2];

  bool  use_mmx_getbits;

  int   restart_interval;
  int   restarts_left;
  int   next_restart_num;

  int   max_mcus_per_row;
  int   max_blocks_per_mcu;

  int   max_mcus_per_col;

  uint *component[JPGD_MAXBLOCKSPERMCU];   /* points into the lastdcvals table */
  uint  last_dc_val[JPGD_MAXCOMPONENTS];

  Phuff_tables_t dc_huff_seg[JPGD_MAXBLOCKSPERMCU];
  Phuff_tables_t ac_huff_seg[JPGD_MAXBLOCKSPERMCU];

  BLOCK_TYPE *block_seg[JPGD_MAXBLOCKSPERROW];
  int   block_max_zag_set[JPGD_MAXBLOCKSPERROW];

  uchar *Psample_buf;
  //int   block_num[JPGD_MAXBLOCKSPERROW];

  int   crr[256];
  int   cbb[256];
  int   padd;
  long  crg[256];
  long  cbg[256];

  uchar *scan_line_0;
  uchar *scan_line_1;

  BLOCK_TYPE temp_block[64];

  bool use_mmx;
  bool use_mmx_idct;
  bool mmx_active;

  int error_code;
  bool ready_flag;

  jmp_buf jmp_state;

  int total_bytes_read;

public:

  // If SUPPORT_MMX is not defined, the use_mmx flag is ignored.
  jpeg_decoder(Pjpeg_decoder_stream Pstream,
               bool use_mmx);

  int begin(void);

  int decode(void * *Pscan_line_ofs, uint *Pscan_line_len);

  ~jpeg_decoder();

  int get_error_code(void)
  {
    return (error_code);
  }

  int get_width(void)
  {
    return (image_x_size);
  }

  int get_height(void)
  {
    return (image_y_size);
  }

  int get_num_components(void)
  {
    return (comps_in_frame);
  }

  int get_bytes_per_pixel(void)
  {
    return (dest_bytes_per_pixel);
  }

  int get_bytes_per_scan_line(void)
  {
    return (image_x_size * get_bytes_per_pixel());
  }

  int get_total_bytes_read(void)
  {
    return (total_bytes_read);
  }
};
//------------------------------------------------------------------------------

//------------------------------------------------------------------------------
// inlines-- moved from .h file for clarity
//------------------------------------------------------------------------------
// Logical rotate left operation.
inline uint jpeg_decoder::rol(uint i, uchar j)
{
#ifdef SUPPORT_X86ASM
  // Breaks the rules a bit.. return value is in eax.
  _asm
  {
    Mov eax, i
    Mov cl, j
    rol eax, cl
  }
#else
  return ((i << j) | (i >> (32 - j)));
#endif
}
//------------------------------------------------------------------------------
// Retrieve one character from the input stream.
inline uint jpeg_decoder::get_char(void)
{
  // Any bytes remaining in buffer?
  if (!in_buf_left)
  {
    // Try to get more bytes.
    prep_in_buffer();
    // Still nothing to get?
    if (!in_buf_left)
    {
      // Padd the end of the stream with 0xFF 0xD9 (EOI marker)
      // FIXME: Is there a better padding pattern to use?
      int t = tem_flag;
      tem_flag ^= 1;
      if (t)
        return (0xD9);
      else
        return (0xFF);
    }
  }

  uint c = *Pin_buf_ofs++;
  in_buf_left--;

  return (c);
}
//------------------------------------------------------------------------------
// Same as previus method, except can indicate if the character is
// a "padd" character or not.
inline uint jpeg_decoder::get_char(bool *Ppadding_flag)
{
  if (!in_buf_left)
  {
    prep_in_buffer();
    if (!in_buf_left)
    {
      *Ppadding_flag = true;
      int t = tem_flag;
      tem_flag ^= 1;
      if (t)
        return (0xD9);
      else
        return (0xFF);
    }
  }

  *Ppadding_flag = false;

  uint c = *Pin_buf_ofs++;
  in_buf_left--;

  return (c);
}
//------------------------------------------------------------------------------
// Inserts a previously retrieved character back into the input buffer.
inline void jpeg_decoder::stuff_char(uchar q)
{
  *(--Pin_buf_ofs) = q;
  in_buf_left++;
}
//------------------------------------------------------------------------------
// Retrieves one character from the input stream, but does
// not read past markers. Will continue to return 0xFF when a
// marker is encountered.
// FIXME: Bad name?
inline uchar jpeg_decoder::get_octet(void)
{
  bool padding_flag;
  int c = get_char(&padding_flag);

  if (c == 0xFF)
  {
    if (padding_flag)
      return (0xFF);

    c = get_char(&padding_flag);
    if (padding_flag)
    {
      stuff_char(0xFF);
      return (0xFF);
    }

    if (c == 0x00)
      return (0xFF);
    else
    {
      stuff_char(c);
      stuff_char(0xFF);
      return (0xFF);
    }
  }

  return (c);
}
//------------------------------------------------------------------------------
// Retrieves a variable number of bits from the input stream.
// Does not recognize markers.
inline uint jpeg_decoder::get_bits_1(int num_bits)
{
  uint i;

  i = (bit_buf >> (16 - num_bits)) & ((1 << num_bits) - 1);

  if ((bits_left -= num_bits) <= 0)
  {
    bit_buf = rol(bit_buf, num_bits += bits_left);

    uint c1 = get_char();
    uint c2 = get_char();

    bit_buf = (bit_buf & 0xFFFF) | (((ulong)c1) << 24) | (((ulong)c2) << 16);

    bit_buf = rol(bit_buf, -bits_left);

    bits_left += 16;
  }
  else
    bit_buf = rol(bit_buf, num_bits);

  return i;
}
//------------------------------------------------------------------------------
// Retrieves a variable number of bits from the input stream.
// Markers will not be read into the input bit buffer. Instead,
// an infinite number of all 1's will be returned when a marker
// is encountered.
// FIXME: Is it better to return all 0's instead, like the older implementation?
inline uint jpeg_decoder::get_bits_2(int numbits)
{
  uint i;

  i = (bit_buf >> (16 - numbits)) & ((1 << numbits) - 1);

  if ((bits_left -= numbits) <= 0)
  {
    bit_buf = rol(bit_buf, numbits += bits_left);

    uint c1 = get_octet();
    uint c2 = get_octet();

    bit_buf = (bit_buf & 0xFFFF) | (((ulong)c1) << 24) | (((ulong)c2) << 16);

    bit_buf = rol(bit_buf, -bits_left);

    bits_left += 16;
  }
  else
    bit_buf = rol(bit_buf, numbits);

  return i;
}
//------------------------------------------------------------------------------
// Decodes a Huffman encoded symbol.
inline int jpeg_decoder::huff_decode(Phuff_tables_t Ph)
{
  int symbol;

  // Check first 8-bits: do we have a complete symbol?
  if ((symbol = Ph->look_up[(bit_buf >> 8) & 0xFF]) < 0)
  {
    // Decode more bits, use a tree traversal to find symbol.
    get_bits_2(8);

    do
    {
      symbol = Ph->tree[~symbol + (1 - get_bits_2(1))];
    } while (symbol < 0);
  }
  else
    get_bits_2(Ph->code_size[symbol]);

  return symbol;
}
//------------------------------------------------------------------------------
// Tables and macro used to fully decode the DPCM differences.
// (Note: In x86 asm this can be done without using tables.)
const int extend_test[16] =   /* entry n is 2**(n-1) */
  { 0, 0x0001, 0x0002, 0x0004, 0x0008, 0x0010, 0x0020, 0x0040, 0x0080,
    0x0100, 0x0200, 0x0400, 0x0800, 0x1000, 0x2000, 0x4000 };

const int extend_offset[16] = /* entry n is (-1 << n) + 1 */
  { 0, ((-1)<<1) + 1, ((-1)<<2) + 1, ((-1)<<3) + 1, ((-1)<<4) + 1,
    ((-1)<<5) + 1, ((-1)<<6) + 1, ((-1)<<7) + 1, ((-1)<<8) + 1,
    ((-1)<<9) + 1, ((-1)<<10) + 1, ((-1)<<11) + 1, ((-1)<<12) + 1,
    ((-1)<<13) + 1, ((-1)<<14) + 1, ((-1)<<15) + 1 };

// used by huff_extend()
const int extend_mask[] =
{
  0,
  (1<<0), (1<<1), (1<<2), (1<<3),
  (1<<4), (1<<5), (1<<6), (1<<7),
  (1<<8), (1<<9), (1<<10), (1<<11),
  (1<<12), (1<<13), (1<<14), (1<<15),
  (1<<16),
};

#define HUFF_EXTEND_TBL(x,s) ((x) < extend_test[s] ? (x) + extend_offset[s] : (x))

#ifdef SUPPORT_X86ASM
// Use the inline ASM version instead to prevent jump misprediction issues
  #define HUFF_EXTEND(x,s) huff_extend(x, s)
  #define HUFF_EXTEND_P(x,s) Pd->huff_extend(x, s)
#else
  #define HUFF_EXTEND(x,s) HUFF_EXTEND_TBL(x,s)
  #define HUFF_EXTEND_P(x,s) HUFF_EXTEND_TBL(x,s)
#endif
//------------------------------------------------------------------------------
#ifdef SUPPORT_X86ASM
// This code converts the raw unsigned coefficient bits
// read from the data stream to the proper signed range.
// There are many ways of doing this, see the HUFF_EXTEND_TBL
// macro for an alternative way.
// It purposelly avoids any decision making that requires jumping.
inline uint jpeg_decoder::huff_extend(uint i, int c)
{
  _asm
  {
    mov ecx, c
    mov eax, i
    cmp eax, [ecx*4+extend_mask]
    sbb edx, edx
    shl edx, cl
    adc eax, edx
  }
}
#endif
//------------------------------------------------------------------------------
// Clamps a value between 0-255.
inline uchar jpeg_decoder::clamp(int i)
{
  if (i & 0xFFFFFF00)
    i = (((~i) >> 31) & 0xFF);

  return (i);
}
//------------------------------------------------------------------------------
#ifdef SUPPORT_MMX
//------------------------------------------------------------------------------
inline uint jpeg_decoder::get_high_byte_mmx(void)
{
  _asm
  {
    movq mm1, mm0
    psrlq mm1, 56
    movd eax, mm1
  }
}
//------------------------------------------------------------------------------
inline uint jpeg_decoder::get_high_word_mmx(void)
{
  _asm
  {
    movq mm1, mm0
    psrlq mm1, 48
    movd eax, mm1
  }
}
//------------------------------------------------------------------------------
inline void jpeg_decoder::get_bits_2_mmx_init(void)
{
  assert(!mmx_active);
  mmx_active = true;

  _asm
  {
    mov esi, this
    movq mm0, [esi].bit_buf
    movq mm1, [esi].saved_mm1
  }
}
//------------------------------------------------------------------------------
inline void jpeg_decoder::get_bits_2_mmx_deinit(void)
{
  assert(mmx_active);
  mmx_active = false;

  _asm
  {
    mov esi, this
    movq [esi].bit_buf, mm0
    movq [esi].saved_mm1, mm1
    emms
  }
}
//------------------------------------------------------------------------------
static __int64 cmp_mask = 0xFFFFFFFFFFFFFFFF;
static __int64 zero = 0;
//------------------------------------------------------------------------------
//FIXME: This function doesn't compile right with the Intel Compiler in Release builds.
//Something to do with funciton inlining.
inline uint jpeg_decoder::get_bits_2_mmx(int numbits)
{
  _asm
  {
    // is the "mov esi, this" really necessary?
    // this is safe but it's probably already "this" anyway
    mov esi, this
    mov ecx, numbits

    mov edx, 64
    movd mm3, ecx

    sub edx, ecx
    movq mm1, mm0

    movd mm2, edx
    sub [esi].bits_left, ecx

    psrlq mm1, mm2
    Jg gb2_done
//-----------------------------
    add ecx, [esi].bits_left
    cmp [esi].in_buf_left, 12

    movd mm4, ecx
    mov edi, [esi].Pin_buf_ofs

    psllq mm0, mm4
    jb gb2_slowload
//-----------------------------
// FIXME: Pair better!

    mov eax, [edi]
    mov ebx, [edi+4]

// FIXME: Is there a better way to do this other than using bswap?
    bswap eax
    bswap ebx

    movd mm4, eax
    movd mm3, ebx

    psllq mm4, 32
    add [esi].Pin_buf_ofs, 6

    por mm3, mm4
    mov ecx, [esi].bits_left

    psrlq mm3, 16
    neg ecx

    movq mm4, mm3
    sub [esi].in_buf_left, 6

    pcmpeqb mm4, cmp_mask
    por mm0, mm3

    pcmpeqd mm4, zero
    movd mm3, ecx

    pxor mm4, cmp_mask
    movd eax, mm1

    psrlq mm4, 1
    add [esi].bits_left, 48

    movd ebx, mm4
    psllq mm0, mm3

    test ebx, ebx
    jz gb2_return
//-----------------------------
    psrlq mm0, mm3
    sub [esi].bits_left, 48
    sub [esi].Pin_buf_ofs, 6
    add [esi].in_buf_left, 6

gb2_slowload:
    psrlq mm0, 48
  }

  for (int i = 0; i < 6; i++)
  {
    uint c = get_octet();

    _asm
    {
      movd mm3, c
      psllq mm0, 8
      por mm0, mm3
    }
  }

  _asm
  {
    mov esi, this
    mov ecx, [esi].bits_left
    neg ecx
    movd mm3, ecx
    add [esi].bits_left, 48

gb2_done:
    movd eax, mm1
    psllq mm0, mm3
  }
gb2_return:;
}
//------------------------------------------------------------------------------
inline int jpeg_decoder::huff_decode_mmx(Phuff_tables_t Ph)
{
  int symbol;
  //uint d = get_high_word_mmx();
  uint d;
  _asm
  {
    movq mm1, mm0
    psrlq mm1, 48
    movd eax, mm1
    mov d, eax
  }

  // Check first 8-bits: do we have a complete symbol?
  if ((symbol = Ph->look_up[(d >> 8) & 0xFF]) < 0)
  {
    uint ofs = 7;
    d = ~d; // invert d here so we don't have to do it inside the loop

    do
    {
      symbol = Ph->tree[~symbol + ((d >> ofs) & 1)];
      ofs--;
    } while (symbol < 0);

    // Decode more bits, use a tree traversal to find symbol.
    get_bits_2_mmx(8 + (7 - ofs));
  }
  else
    get_bits_2_mmx(Ph->code_size[symbol]);

  return symbol;
}
//------------------------------------------------------------------------------
#endif
//------------------------------------------------------------------------------
//------------------------------------------------------------------------------
#pragma warning(pop)
//------------------------------------------------------------------------------
typedef jpeg_decoder *Pjpeg_decoder;
//------------------------------------------------------------------------------
// idct.cpp
void idct(BLOCK_TYPE *data, uchar *Pdst_ptr);
//------------------------------------------------------------------------------
// fidctfst.cpp
void jpeg_idct_ifast (
  BLOCK_TYPE* inptr,
  short *quantptr,
  uchar * *outptr,
  int output_col);

void jpeg_idct_ifast_deinit(void);

bool jpeg_idct_ifast_avail(void);
//------------------------------------------------------------------------------
#endif
//------------------------------------------------------------------------------

