//#include <windows.h>
//#include <windowsx.h>  // <- nothing to do with Xwindows ;-) just some handy macros
//#include <commdlg.h>
//#include <shellapi.h>
#include <stdio.h>
//#include <commctrl.h>
//#include <winuser.h>
//#include <assert.h>
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
//#include "Iskang.h"

static  FILE *pCfgFile = NULL;
static  FILE *pStListFile = NULL;
static  FILE *pIdxFile = NULL;
static  FILE *pTcdIndexFile = NULL;
static  FILE *pTcdRecordFile = NULL;
static  FILE *pMscFile = NULL;

FILE* OpenCfgOut(void)
{
	pCfgFile = fopen("iskangCfg.txt", "wt");
	return pCfgFile;
}

void CloseCfgOut()
{
	fclose(pCfgFile);
}

int IskangCfgPrint(const char *fstring, ...)
{
	int         nReturn;
	va_list     marker;

	//if(!pCfgFile)
	//	return;

	va_start(marker, fstring);
	nReturn = vfprintf(pCfgFile, fstring, marker);
	return nReturn;
}

int IskangCfgIntOut(const char *pStrVarName, int nData)
{
	return IskangCfgPrint("%s = %d;\n", pStrVarName, nData);
}

int IskangCfgFloatOut(const char *pStrVarName, float fData)
{
	return IskangCfgPrint("%s = %f;\n", pStrVarName, fData);
}

int IskangCfgStrOut(const char *pStrVarName, const char *pData)
{
	return IskangCfgPrint("strcpy(%s, \"%s\");\n", pStrVarName, (!pData || strlen(pData) <= 0) ? "" : pData);
}

FILE* OpenStListOut(void)
{
	pStListFile = fopen("iskangStList.txt", "wt");
	return pStListFile;
}

void CloseStListOut()
{
	fclose(pStListFile);
}

int IskangStListPrint(const char *fstring, ...)
{
	int         nReturn;
	va_list     marker;

	//if(!pStListFile)
	//	return;

	va_start(marker, fstring);
	nReturn = vfprintf(pStListFile, fstring, marker);
	return nReturn;
}

FILE* OpenIdxOut(void)
{
	pIdxFile = fopen("iskangIdxList.txt", "wt");
	return pIdxFile;
}

void CloseIdxOut()
{
	fclose(pIdxFile);
}

int IskangIdxPrint(const char *fstring, ...)
{
	int         nReturn;
	va_list     marker;

	//if(!pIdxFile)
	//	return;

	va_start(marker, fstring);
	nReturn = vfprintf(pIdxFile, fstring, marker);
	return nReturn;
}
/*
int IskangIdxIntOut(const char *pStrVarName, int nData)
{
	return IskangIdxPrint("%s = %d;\n", pStrVarName, nData);
}

int IskangIdxFloatOut(const char *pStrVarName, float fData)
{
	return IskangIdxPrint("%s = %f;\n", pStrVarName, fData);
}

int IskangIdxStrOut(const char *pStrVarName, const char *pData)
{
	return IskangIdxPrint("strcpy(%s, \"%s\");\n", pStrVarName, pData);
}
*/

FILE* OpenTcdIndexOut(void)
{
	pTcdIndexFile = fopen("iskangTcdIndex.txt", "wt");
	return pTcdIndexFile;
}

void CloseTcdIndexOut()
{
	fclose(pTcdIndexFile);
}

int IskangTcdIndexPrint(const char *fstring, ...)
{
	int         nReturn;
	va_list     marker;

	//if(!pTcdIndexFile)
	//	return;

	va_start(marker, fstring);
	nReturn = vfprintf(pTcdIndexFile, fstring, marker);
	return nReturn;
}

int IskangTcdIndexIntOut(const char *pStrVarName, int nData)
{
	return IskangTcdIndexPrint("%s = %d;\n", pStrVarName, nData);
}

int IskangTcdIndexFloatOut(const char *pStrVarName, float fData)
{
	return IskangTcdIndexPrint("%s = %f;\n", pStrVarName, fData);
}

int IskangTcdIndexStrOut(const char *pStrVarName, const char *pData)
{
	return IskangTcdIndexPrint("strcpy(%s, \"%s\");\n", pStrVarName, pData);
}

int IskangTcdIndexArrayStr(const char *pData)
{
	if(pData)
		return IskangTcdIndexPrint("\"%s\", ", pData);
	return IskangTcdIndexPrint("\"\", ");
}

FILE* OpenTcdRecordOut(void)
{
	pTcdRecordFile = fopen("iskangTcdRecord.txt", "wt");
	return pTcdRecordFile;
}

void CloseTcdRecordOut()
{
	fclose(pTcdRecordFile);
}

int IskangTcdRecordPrint(const char *fstring, ...)
{
	int         nReturn;
	va_list     marker;

	//if(!pTcdIndexFile)
	//	return;

	va_start(marker, fstring);
	nReturn = vfprintf(pTcdRecordFile, fstring, marker);
	return nReturn;
}


FILE* OpenMscOut(void)
{
	pMscFile = fopen("iskangMsc.txt", "wt");
	return pMscFile;
}

void CloseMscOut()
{
	fclose(pMscFile);
}

int IskangMscPrint(const char *fstring, ...)
{
	int         nReturn;
	va_list     marker;

	//if(!pMscFile)
	//	return;

	va_start(marker, fstring);
	nReturn = vfprintf(pMscFile, fstring, marker);
	return nReturn;
}

int IskangMscArrayStr(const char *pData)
{
	if(pData)
		return IskangMscPrint("\"%s\", ", pData);
	return IskangMscPrint("\"\", ");
}

