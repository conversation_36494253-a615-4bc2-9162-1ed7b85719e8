/*...........................................................................*/
/*.                  File Name : SYSCONST.H                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef __SYSCONSTPOLLUX_H__
#define __SYSCONSTPOLLUX_H__

//=============================================================================
#define __USE_RTOS_MODE__                              0
//-----------------------------------------------------------------------------
#define CPU_TYPE_POLLUX                                0
#define CPU_TYPE_SPICA                                 1
//-----------------------------------------------------------------------------
#define NMEA2000_MODE_SPI                              0
#define NMEA2000_MODE_GPIO                             1
#define NMEA2000_MODE_RUN            (NMEA2000_MODE_GPIO)
//-----------------------------------------------------------------------------
#define DEVICE_TYPE_03_5                               0
#define DEVICE_TYPE_04_3                               1
#define DEVICE_TYPE_05_0                               2
#define DEVICE_TYPE_05_6                               3
#define DEVICE_TYPE_06_5                               4
#define DEVICE_TYPE_07_0                               5
#define DEVICE_TYPE_08_4                               6
#define DEVICE_TYPE_10_4                               7
#define DEVICE_TYPE_12_1                               8
#define DEVICE_TYPE_15_1                               9

#define MENU_SCRN_RGB_HORI_STRIDE        (sizeof(CLRMENU))
#define CHART_SCRN_RGB_HORI_STRIDE      (sizeof(CLRCHART))
#define RADAR_SCRN_RGB_HORI_STRIDE      (sizeof(CLRRADAR))
//#define SCRN_RGB_VERT_STRIDE                        4096

#define SCRN_YUV_Y_STRIDE                           4096
#define SCRN_YUV_CR_STRIDE                          4096
#define SCRN_YUV_CB_STRIDE                          4096

#define DVC_03_5_SCRN_WIDTH                          320
#define DVC_04_3_SCRN_WIDTH                          480
#define DVC_05_0_SCRN_WIDTH                          320
#define DVC_05_6_SCRN_WIDTH                          640
#define DVC_06_5_SCRN_WIDTH                          640
#define DVC_07_0_SCRN_WIDTH                          800
#define DVC_08_4_SCRN_WIDTH                          800
#define DVC_10_4_SCRN_WIDTH                          640
#define DVC_12_1_SCRN_WIDTH                          800
#define DVC_15_1_SCRN_WIDTH                         1024

#define DVC_03_5_SCRN_HEIGHT                         240
#define DVC_04_3_SCRN_HEIGHT                         272
#define DVC_05_0_SCRN_HEIGHT                         234
#define DVC_05_6_SCRN_HEIGHT                         480
#define DVC_06_5_SCRN_HEIGHT                         480
#define DVC_07_0_SCRN_HEIGHT                         600
#define DVC_08_4_SCRN_HEIGHT                         600
#define DVC_10_4_SCRN_HEIGHT                         480
#define DVC_12_1_SCRN_HEIGHT                         600
#define DVC_15_1_SCRN_HEIGHT                         768
//-----------------------------------------------------------------------------
#define CPU_VIRT_NAND_BASE_ADDR               0xb0000000
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  MCUA_SECT_TOTAL_SIZE             0x40000000
    #define  MCUB_SECT_TOTAL_SIZE             0x40000000
    #define  MCUS_SECT_TOTAL_SIZE             0x40000000
    #define  IORW_SECT_TOTAL_SIZE             0x20000000
    #define  MCUA_RAM_TOTAL_SIZE       (64 * 1024 * 1024)
    #define  MCUB_RAM_TOTAL_SIZE       ( 0 * 1024 * 1024)

    #define  RAM_TOTAL_SIZE            (60 * 1024 * 1024)
    #define  RAM_START_ADDRESS                0x00000000
    #define  RAM_VECTOR_BASE_ADDRESS          0xffff0000

    #define  VIDEO_MEM_TOTAL_SIZE      ( 4 * 1024 * 1024)
    #define  VIDEO_MEM_START_ADDRESS          0x03C00000

    #define  MMU_TTB_START_ADDRESS            0x03ff0000
#else                           // SPICA
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  UND_STACK_SIZE                   0x00000040
    #define  ABT_STACK_SIZE                   0x00000040
    #define  FIQ_STACK_SIZE                   0x00000080
    #define  IRQ_STACK_SIZE                   0x00002000
    #define  SYS_STACK_SIZE                   0x00002000
    #define  SVC_STACK_SIZE                   0x00000000
#else                           // SPICA
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  MEM_SECTION_SIZE                 0x00100000
#else                           // SPICA
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  CPU_INPUT_FREQUENCY                27000000

    #define  CPU_PLL0_FREQUENCY                533000000
    #define  CPU_PLL0_PDIV                            24
    #define  CPU_PLL0_MDIV                           948
    #define  CPU_PLL0_SDIV                             1

//  #define  CPU_PLL0_FREQUENCY                528000000
//  #define  CPU_PLL0_PDIV                             9
//  #define  CPU_PLL0_MDIV                           176
//  #define  CPU_PLL0_SDIV                             0

    #define  CPU_PLL1_FREQUENCY                132750000
    #define  CPU_PLL1_PDIV                             6
    #define  CPU_PLL1_MDIV                            59
    #define  CPU_PLL1_SDIV                             1

    #define  BCLK_SRC                                  0  // 0=PLL0
    #define  BCLK_DIV                                  4

    #define  ARM_FCLK                (CPU_PLL0_FREQUENCY)
    #define  ARM_HCLK                      (ARM_FCLK / 2)
    #define  ARM_BCLK               (ARM_FCLK / BCLK_DIV)
    #define  ARM_PCLK                      (ARM_BCLK / 2)
#else                           // SPICA
#endif

#define  ONE_SEC_TICK                                100
#define  CALC_SEC_TO_TICK(X)           (ONE_SEC_TICK * X)
#define  CALC_MILI_TO_TICK(X)   (ONE_SEC_TICK * X / 1000)
#define  CALC_TICK_TO_MILI(X)  (X * (1000 / ONE_SEC_TICK))

#define  CALC_MILI_TICK(X)        (ONE_SEC_TICK * X / 1000)
//-----------------------------------------------------------------------------
#define  NAND_BOOTER_ROM_START_ADDR       0xb0000000
#define  MAIN_BOOTER_ROM_START_ADDR       0xb0001000
#define  CMAP_WWB_ROM_START_ADDR          0xb1000000
#define  SAM_MAP_ROM_START_ADDR           0xb4000000
#define  LOGO1_DATA_ROM_START_ADDR        0xb7000000
#define  LOGO2_DATA_ROM_START_ADDR        0xb7100000
#define  LOAD_PRG_ROM_START_ADDR          0xb7200000
#define  MAIN_PRG_ROM_START_ADDR          0xb7400000
//=============================================================================

#endif

