#include "Sentence.hpp"

#ifndef __LR3_HPP__
#define __LR3_HPP__
/******************************************************************************
*
* LR3 - Long-range Reply for function requests "I,O,P,U and W"
*
* $--LR3,x,xxxxxxxxx,c--c,xxxxxx,hhmmss.ss,x.x,cc,x.x,x.x,x.x,x.x*hh<CR><LF>
*        | |         |    |      |         |   |  |   |   |   |
*        1 2         3    4      5         6   7  8   9   10  11
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of responder
* 3.  Voyage destination , 1 to 20 characters
* 4.  ETA Date:ddmmyy
* 5.  ETA Time , value to nearest second
* 6.  Draught , value to 0,1 meter
* 7.  Ship/cargo
* 8.  Ship length , value to nearest meter
* 9.  Ship breadth , value to nearest meter
* 10. Ship type
* 11. Persons , 0 to 8191
*
******************************************************************************/
class CLr3 : public CSentence {
    protected:
		
		int    m_nSeqNumber;
		int    m_nMMSIResp;
		char   m_szVoyageDest[21];
		char   m_szETADate[7];
		int    m_nETADay;
		int    m_nETAMonth;
		int    m_nETAYear;
		char   m_szETATime[10];
		int    m_nETAHour;
		int    m_nETAMin;
		int    m_nETASec;
		double m_dblDraught;
		char   m_szShipCargo[3];
		double m_dblShipLen;
		double m_dblShipBreadth;
		int    m_nShipType;
		int    m_nPersons;
    public:
        CLr3();
        CLr3(char *pszSentence);

		void Parse();
		void SetSentence(char *pszSentence);
		int  GetFormat() { return m_nFormat; }
		void GetPlainText(char *pszPlainText);

		int    GetSeqNumber()     { return m_nSeqNumber;     }
		int    GetMMSIResp()      { return m_nMMSIResp;      }
		void   GetVoyageDest(char szVoyageDest[21]) { strcpy(szVoyageDest, m_szVoyageDest); }
		void   GetETADate(char szETADate[7])        { strcpy(szETADate, m_szETADate);       }
		int    GetETADay()        { return m_nETADay;        }
		int    GetETAMonth()      { return m_nETAMonth;      }
		int    GetETAYear()       { return m_nETAYear;       }
		void   GetETATime(char szETATime[10])       { strcpy(szETATime, m_szETATime);       }
		int    GetETAHour()       { return m_nETAHour;       }
		int    GetETAMin()        { return m_nETAMin;        }
		int    GetETASec()        { return m_nETASec;        }
		double GetDraught()       { return m_dblDraught;     }
		void   GetShipCargo(char szShipCargo[3])    { strcpy(szShipCargo, m_szShipCargo);   }
		double GetShipLen()       { return m_dblShipLen;     }
		double GetShipBreadth()   { return m_dblShipBreadth; }
		int    GetShipType()      { return m_nShipType;      }
		int    GetPersons()       { return m_nPersons;       }
};

#endif
