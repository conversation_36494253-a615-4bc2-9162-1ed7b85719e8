#include "Sentence.hpp"

#ifndef __LR1_HPP__
#define __LR1_HPP__
/******************************************************************************
*
* LR1 - Long-range Reply with destination for function request "A"
*
* $--LR1,x,xxxxxxxxx,xxxxxxxxx,c--c,c--c,xxxxxxxxx*hh<CR><LF>
*        | |         |         |    |    |
*        1 2         3         4    5    6
*
* 1. Sequence Number , 0 to 9
* 2. MMSI of responder
* 3. MMSI of requestor(reply destination)
* 4. Ship's name , 1 to 20 characters
* 5. Call Sign , 1 to 7 characters
* 6. IMO Number , 9-digit number
*
******************************************************************************/
class CLr1 : public CSentence {
protected:
	
	int  m_nSeqNumber;
	int  m_nMMSIResp;
	int  m_nMMSIReq;
	char m_szShipName[21];
	char m_szCallSign[8];
	char m_nIMONum;          // 9-digit number
    
public:
    CLr1();
    CLr1(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);
	int  MakeSentence(BYTE *pszSentence) { return 0; }

	int  GetSeqNumumber()   { return m_nSeqNumber; }
	int  GetMMSIResp()      { return m_nMMSIResp;  }
	int  GetMMSIReq()       { return m_nMMSIReq;   }
	void GetShipName(char szShipName[21]) { strcpy(szShipName, m_szShipName); }
	void GetCallSign(char szCallSign[8])  { strcpy(szCallSign, m_szCallSign); }
	int  GetIMONum()        { return m_nIMONum;    }
};
		
#endif
