#include <stdio.h>
#include "MenuWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "AllConst.h"
#include "Font.h"
#include "Resource.h"
#include "Uart.hpp"

extern CDocMgr* g_pDocMgr;
extern cUART *G_pUart3;

CMenuWnd::CMenuWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
	, m_nSelNum(1)
{
#ifdef _EN_POLICE_MODE_
	m_bPrevTransmitterStat = TRUE;
#endif // End of _EN_POLICE_MODE_

	InitSpecialKey();
}

/*********************************************************************************************************/
// Name		: InitSpecialKey
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CMenuWnd::InitSpecialKey()
{
	int i = 0;
	m_nCurSpecialKeyCnt = 0;	
	for(i=0; i<4; i++)
	{
		m_nSpecialKeyBuf[i] = 0;
	}
}

/*********************************************************************************************************/
// Name		: IsSpecialKey
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
BOOL CMenuWnd::IsSpecialKey()
{
	BOOL bRetVal = FALSE;

	if( (m_nCurSpecialKeyCnt == 4) 
		&& (m_nSpecialKeyBuf[0] == KBD_SCAN_CODE_7)
		&& (m_nSpecialKeyBuf[1] == KBD_SCAN_CODE_8) 
		&& (m_nSpecialKeyBuf[2] == KBD_SCAN_CODE_9) 
		&& (m_nSpecialKeyBuf[3] == KBD_SCAN_CODE_0) 
	)
	{
		bRetVal = TRUE;
	}
			
	return bRetVal;
}

void CMenuWnd::DrawWnd(BOOL bRedraw/*TRUE*/)
{
	int nLangMode = g_pDocMgr->GetLangMode();

	CWnd::DrawWnd(bRedraw);

	DrawSubMenu(m_nSelNum);
	DrawButton(0, (BYTE *)FK_EXIT[nLangMode]);

	EraseButton(1);
	EraseButton(2);
	EraseButton(3);
}

void CMenuWnd::DrawSubMenu(int nSelNum)
{
	int nLangMode = g_pDocMgr->GetLangMode();
	int i = 0;
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0, nYOffset = 0;
	HWORD *pUniCodeStr = NULL;
	UCHAR *pArrBmp = NULL;
	COLORT clrTxt;
	
	COLORT clrUpLine;
	COLORT clrDnLine;

	if(m_pScreen != NULL)
	{
		// Draw Menu
		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:
				pFont = &NewGulLim18bCJK;
				nYOffset = 2;
				break;

			case LANG_RUS:
				pFont = &MyriadPro30bRus;
				nYOffset = 2;
				break;
				
			default:
				pFont = &MyriadPro30bEng;
				nYOffset = 6;
				break;
		}
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;
 
		for(i = 0; i < MAX_MENU_BAR; i++)
		{
			if(m_nScheme == CS_DAY_BRIGHT)
			{
				clrUpLine = MNU_DAY_UP_LINE;
				clrDnLine = MNU_DAY_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrDayOn;
				}
				else
				{
					pArrBmp = G_BmpArrDayOff;
				}
			}
			else
			{
				clrUpLine = MNU_NIGHT_UP_LINE;
				clrDnLine = MNU_NIGHT_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrNightOn;
				}
				else
				{
					pArrBmp = G_BmpArrNightOff;
				}
			}
			
			if(nSelNum == (i+1))
			{
				clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crLetterIcon);	
				
			}
			else
			{
				clrTxt = COLORSCHEME[m_nScheme].crFore;
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crBack);	
			}

			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							clrUpLine);
			
			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							clrDnLine);

			nXPos = BASE_WND_AREA_X_POS + 5;
			nYPos = WND_BACK_Y_POS + i*MNU_BAR_H + (MENU_ITEM_H - nFontH)/2 + nYOffset;

			switch(i)
			{
				case 0:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MESSAGE[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MESSAGE[nLangMode];
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);	

					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;

				case 1:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_INIT_SETUP[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_INIT_SETUP[nLangMode];
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);
					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;

				case 2:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_SYSTEM_SETUP[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_SYSTEM_SETUP[nLangMode];
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);
					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;

				case 3:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MAINTENACE[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MAINTENACE[nLangMode];
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);
					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;

#ifdef _EN_POLICE_MODE_
				case 4:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)(g_pDocMgr->IsEnableTransmiter() ? MNU_TRANSMIT_ON[nLangMode] : MNU_TRANSMIT_OFF[nLangMode]),nLangMode);
					pUniCodeStr = (HWORD *)(g_pDocMgr->IsEnableTransmiter() ? MNU_TRANSMIT_ON[nLangMode] : MNU_TRANSMIT_OFF[nLangMode]);
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);
					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;
#endif					
			}
		}
		m_pScreen->SetFont(pOldFont);
	}		
}

void CMenuWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	int nLastMenuNum = 4;

#ifdef _EN_POLICE_MODE_
	++nLastMenuNum;
#else	
	if( g_pDocMgr->IsEnableTxFunc())
	{
		++nLastMenuNum;
	}		
#endif // End of _EN_POLICE_MODE_

	m_nCurSpecialKeyCnt++;
	m_nSpecialKeyBuf[m_nCurSpecialKeyCnt-1] = nKey;
	if(m_nCurSpecialKeyCnt >= 4)
	{
		if(IsSpecialKey() == TRUE)
		{
			g_pDocMgr->SetSuperPass(TRUE);
		}
		InitSpecialKey();
	}

	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
		case KBD_SCAN_CODE_LEFT:
			if( m_nSelNum <= 1 ) m_nSelNum = nLastMenuNum;
			else m_nSelNum--;
			DrawSubMenu(m_nSelNum);
			break;
			
		case KBD_SCAN_CODE_DOWN:
		case KBD_SCAN_CODE_RIGHT:
			if( m_nSelNum >= nLastMenuNum ) m_nSelNum = 1;
			else m_nSelNum++;
			DrawSubMenu(m_nSelNum);
			break;
			
		case KBD_SCAN_CODE_2:
			m_nSelNum = 2;
			DrawSubMenu(m_nSelNum);
			break;
			
		case KBD_SCAN_CODE_3:
			m_nSelNum = 3;
			DrawSubMenu(m_nSelNum);
			break;
			
		case KBD_SCAN_CODE_4:
			m_nSelNum = 4;
			DrawSubMenu(m_nSelNum);
			break;
			
#ifdef _EN_POLICE_MODE_
		case KBD_SCAN_CODE_5:
			m_nSelNum = 5;
			DrawSubMenu(m_nSelNum);
			break;
#else
#if 0
		case KBD_SCAN_CODE_5:
			if( g_pDocMgr->IsEnableTxFunc() )
			{
				m_nSelNum = 5;
				DrawSubMenu(m_nSelNum);
			}
			break;
#endif			
#endif // End of _EN_POLICE_MODE_
		default:
			break;
	}
}

void CMenuWnd::OnActivate()
{
#ifdef _EN_POLICE_MODE_
	m_bPrevTransmitterStat = g_pDocMgr->IsEnableTransmiter();
#endif // End of _EN_POLICE_MODE_

	InitSpecialKey();
}
