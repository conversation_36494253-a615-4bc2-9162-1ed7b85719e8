#include "Sentence.hpp"

#ifndef __ALR_HPP__
#define __ALR_HPP__

/******************************************************************************
 * 
 * ALR - Set alarm state
 *
 * $--ALR,hhmmss.ss,xxx,A,A,c--c*hh<CR><LF>
 *         |         |  | |   |
 *         1         2  3 4   5
 *
 * 1. Time of alarm condition change, UTC
 * 2. Local alarm number(identifier)[identification number of alarm source]
 * 3. Alarm condition (A=threshold exceeded, V=not exceeded)
 * 4. <PERSON><PERSON>'s acknowledge state, A=acknowledged V=unacknowledge
 * 5. <PERSON><PERSON>'s description text
 *
 ******************************************************************************/
class CAlr : public CSentence {
protected:
	char m_szUTC[7];    // 6 + 1(NULL)
	int  m_nUTCHour;
	int  m_nUTCMin;
	int  m_nUTCSec;
	int  m_nID;
	char m_chCondition; // 'A' or 'V'
	char m_chAck;       // 'A' or 'V'
	char m_szText[256];
    
public:
    CAlr();
    CAlr(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);

	int  GetUTCHour()   { return m_nUTCHour;    }
	int  GetUTCMin()    { return m_nUTCMin;     }
	int  GetUTCSec()    { return m_nUTCSec;     }
	void GetUTC(char szUTC[7]) { strcpy(szUTC, m_szUTC); }
	int  GetAlarmID()   { return m_nID;         }
	char GetCondition() { return m_chCondition; }
	char GetAck()       { return m_chAck;       }
	void GetAlarmText(char szText[256]) { strcpy(szText, m_szText); }
};

#endif

