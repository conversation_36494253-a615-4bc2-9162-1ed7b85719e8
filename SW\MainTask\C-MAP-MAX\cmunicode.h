#ifndef __UNICODE_H__
#define __UNICODE_H__

#include "cmaptype.h"
#ifdef __cplusplus
extern "C"
{
#endif

PRE_EXPORT_H UnicodeString* IN_EXPORT_H cmUnicodeStrtok(UnicodeString* source, UnicodeString* match);
PRE_EXPORT_H int  IN_EXPORT_H cmUnicodeStrcmp(UnicodeString* s1, UnicodeString* s2);
PRE_EXPORT_H int  IN_EXPORT_H cmUnicodeStrncmp(UnicodeString* s1, UnicodeString* s2, int size);
PRE_EXPORT_H int  IN_EXPORT_H cmUnicodeStrcpy(UnicodeString* dest, UnicodeString* from);
PRE_EXPORT_H int  IN_EXPORT_H cmUnicodeStrncpy(UnicodeString* dest, UnicodeString* from, int size);
PRE_EXPORT_H int IN_EXPORT_H cmUnicodeToAscii(String* dest, UnicodeString* from);
PRE_EXPORT_H int IN_EXPORT_H cmUnicodeStrlen(UnicodeString* str);
PRE_EXPORT_H UnicodeString* IN_EXPORT_H cmAsciiToUnicode(String* str);
PRE_EXPORT_H UnicodeString* IN_EXPORT_H cmUnicodeStrstr(UnicodeString* source, UnicodeString* match);
PRE_EXPORT_H UnicodeString* IN_EXPORT_H cmUnicodeStrcat(UnicodeString* source, UnicodeString* nextPart);
PRE_EXPORT_H UnicodeString* IN_EXPORT_H cmUnicodeStrncat(UnicodeString* source, const UnicodeString* nextPart, unsigned int count);
PRE_EXPORT_H String* IN_EXPORT_H cmUnicodeConvertToAscii(UnicodeString* from);
PRE_EXPORT_H UnicodeString* IN_EXPORT_H cmUnicodeStrchr(UnicodeString* source, UnicodeString c);
PRE_EXPORT_H SLong IN_EXPORT_H cmUnicodeAtoL(UnicodeString* s);
PRE_EXPORT_H int IN_EXPORT_H cmUnicodeSprintf( UnicodeString *str, const UnicodeString *format, ... );
PRE_EXPORT_H int IN_EXPORT_H cmUnicodeStrspn( const UnicodeString *str, const UnicodeString *accept );
#ifdef __cplusplus
}
#endif

#endif