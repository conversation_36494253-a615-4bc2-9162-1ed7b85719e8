#ifndef __EXTEND_PASSWORD_CHECK_WND_HPP__
#define __EXTEND_PASSWORD_CHECK_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "CheckCtrl.hpp"

class CExtPwdChkWnd : public CWnd {
	public:
		enum {
			PASSWORD_MANUFACTURER = 0,
			PASSWORD_MASTER,
			PASSWORD_USER_MASTER,
			PASSWORD_USER,
			PASSWORD_GBCODE,
			PASSWORD_MASTER_OR_USER
		};

		enum{
			FOCUS_CHK_MASTER = 0,
			FOCUS_EDIT_PWD	
		};	
	
	private:
		CEditCtrl *m_pPassword;
		CCheckCtrl *m_pChkMaster;
		int m_nPasswordType;

	private:
		void InitControls(cSCREEN *pScreen);
		
	public:
		CExtPwdChkWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
		
		void OnKeyEvent(int nKey, DWORD nFlags);
		void OnCursorEvent(int nState);
		void DrawWnd(BOOL bRedraw = TRUE);
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
		
		void SetPasswordType(int nPasswordType=PASSWORD_USER);
		BOOL IsPasswordOK();
		
		void SetFocus(int nFocus);
		void DrawErrorMsg();
		void ResetPasswordBox();
};

#endif	// End of __EXTEND_PASSWORD_CHECK_WND_HPP__
