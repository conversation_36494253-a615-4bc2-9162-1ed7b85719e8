/*...........................................................................*/
/*.                  File Name : GrLib.h                                    .*/
/*.                                                                         .*/
/*.                       Date : 2008.09.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "datatype.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "KeyConst.h"
#include "AllConst.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "SysLib.h"

#include "cmaptype.h"
#include "cm.h"
#include "cmc.h"
#include "cmg.h"
#include "datumlib.h"
#include "dbase.h"
#include "mapset.h"
#include "sp57.h"
#include "tidesprd.h"
#include "cmu.h"
#include "image.h"

#ifndef  __GRLIB_H
#define  __GRLIB_H

//=============================================================================
#define  NEAREST_SEARCH_DIST         500.0
#define	 TEXT_ATTRIB_SIZE				(16 * 1024)
#define	 TEXT_INFO_SIZE					(16 * 1024)
//-----------------------------------------------------------------------------
typedef  struct _xSBNINFO {
    #if  defined(__NAVIONICS__)
         UCHAR  vName[64];
    #else
         UCHAR  vName[DRAW_ATTR_STRING_LEN];
    #endif
         LMERC  nLat;
         LMERC  nLon;
         REAL   rDst;
         REAL   rCrs;
         DWORD  dDBPtr;
         HWORD  wCdgNum;
        }xSBNINFO;
//=============================================================================

//=============================================================================
#define SCR_ROTATE_NONE                0
#define SCR_ROTATE_CW                  1
#define SCR_ROTATE_CCW                 2

#if defined(_SCR_ROTATE_NONE_)
    #define SCR_ROTATE_MODE  SCR_ROTATE_NONE
#endif

#if defined(_SCR_ROTATE_CCW_)
    #define SCR_ROTATE_MODE  SCR_ROTATE_CCW
#endif

#if defined(_SCR_ROTATE_CW_)
    #define SCR_ROTATE_MODE  SCR_ROTATE_CW
#endif
//-----------------------------------------------------------------------------
#define  GetRgbColorByPalNo(X)        G_vMapPaletteDataHWORD[X]
//-----------------------------------------------------------------------------
#define  GetRadarNormColorByPalNo(X)  G_vRadarNormColorTable[X] 
#define  GetRadarOverColorByPalNo(X)  G_vRadarOverColorTable[X] 
//-----------------------------------------------------------------------------
#define  POLY_MAX_SIZE            16384
//-----------------------------------------------------------------------------
#if !defined(cmgOVER)
    #define  cmgOVER                  0
#endif

#if !defined(cmgCOPY)
    #define  cmgCOPY            cmgOVER
#endif

#if !defined(cmgXOR)
    #define  cmgXOR                   1
#endif

#if !defined(cmgTRANSP)
    #define  cmgTRANSP                2
#endif

#if !defined(cmgFILLSTYLE)
    #define	 cmgFILLSTYLE             3
#endif

#if !defined(cmgMASKPEN)
    #define  cmgMASKPEN               4
#endif

#if !defined(cmgTLINESTYLE)
    #define  cmgTLINESTYLE            5
#endif

#if !defined(cmgPLUSLINESTYLE)
    #define  cmgPLUSLINESTYLE         6
#endif
//-----------------------------------------------------------------------------
#define  SIN_COS_SHIFT_FACTOR        16
#define  SIN_COS_MULTI_FACTOR (1 << SIN_COS_SHIFT_FACTOR)
//-----------------------------------------------------------------------------
typedef  struct {
         INT32  nLeft;
         INT32  nRight;
        }LINKX;
//=============================================================================
#define  NEW_PEC_CUR_MARK_DEGREE_SIZE         360
#define  NEW_PEC_CUR_MARK_DATA_SIZE_X          24
#define  NEW_PEC_CUR_MARK_DATA_SIZE_Y          32
#define  NEW_PEC_CUR_MARK_DATA_SIZE_Z          48
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

extern UCHAR G_vMapPaletteDataUCHAR[];
extern HWORD G_vMapPaletteDataHWORD[];
extern CLRRADAR G_vRadarNormColorTable[];
extern CLRRADAR G_vRadarOverColorTable[];

//=============================================================================
void  GrSetNewPecChartMode(int nMode);
int   GrGetNewPecChartMode(void);
void  GrSetNewPecExpandedMode(int nMode);
int   GrGetNewPecExpandedMode(void);
void  GrSetNewPecCanSetExpandedMode(int nMode);
int   GrGetNewPecCanSetExpandedMode(void);
void  GrSetNewPecRealModeX(int nMode);
int   GrGetNewPecRealModeX(void);
DWORD GrGetNewPec040MarkAddr(void);
DWORD GrGetNewPec064MarkAddr(void);
DWORD GrGetNewPec088MarkAddr(void);
DWORD GrGetNewPec168MarkAddr(void);
DWORD GrGetNewPec169MarkAddr(void);
void  GrSetNewPecDirMarkDraw(int nMarkNo, int nMarkDir);
void  GrGetLastDrawNewPecDirMarkInfo(UCHAR *pWidthX, UCHAR *pWidthY, UCHAR *pPivotX, UCHAR *pPivotY);
void  GrInitAllData(DWORD **pAddrOfLines,int nScrnWidth,int nScrnHeight);
void  GrSetEng06x04Font(UCHAR (*pFont)[6]);
void  GrSetEng07x05Font(UCHAR (*pFont)[7]);
void  GrSetEng09x06Font(UCHAR (*pFont)[9]);
void  GrSetEng10x08Font(UCHAR (*pFont)[10]);
void  GrSetEng16x08Font(UCHAR (*pFont)[16]);
void  GrSetEng16x09Font(UCHAR (*pFont)[16]);
void  GrSetEng20x12Font(UCHAR (*pFont)[40]);
void  GrSetEng29x16Font(UCHAR (*pFont)[58]);
void  GrSetOldHavenMarkFont(UCHAR (*pFont)[72]);
void  GrSetNewHavenMarkFont(UCHAR (*pFont)[72]);
void  GrSetNewChartMarkFont(UCHAR *pFont);
void  GrSetUniCodeFont16x16(xFONTYY *pFont);
void  GrSetUniCodeFont19x21(xFONTYY *pFont);
void  GrSetUniCodeFont29x30(xFONTYY *pFont);
UCHAR *GrGetEng06x04Font(void);
UCHAR *GrGetEng07x05Font(void);
UCHAR *GrGetEng09x06Font(void);
UCHAR *GrGetEng10x08Font(void);
UCHAR *GrGetEng16x08Font(void);
UCHAR *GrGetEng16x09Font(void);
UCHAR *GrGetEng20x12Font(void);
UCHAR *GrGetEng29x16Font(void);
UCHAR *GrGetOldHavenMarkFont(void);
UCHAR *GrGetNewHavenMarkFont(void);
UCHAR *GrGetNewChartMarkFont(void);
xFONTYY *GrGetUniCodeFont16x16(void);
xFONTYY *GrGetUniCodeFont19x21(void);
xFONTYY *GrGetUniCodeFont29x30(void);
XSCRN *GrGetScrnMetric(XSCRN *pScrnMetric);
void  GrSetScrnClipArea(int nLeftX,int nTopY,int nRightX,int nBottomY);
void  GrGetScrnClipArea(int *pLeftX,int *pTopY,int *pRightX,int *pBottomY);
void  GrSetOriginScrnClipArea(void);
int   GrGetScrnClipLeftX(void);
int   GrGetScrnClipRightX(void);
int   GrGetScrnClipUpY(void);
int   GrGetScrnClipBottomY(void);
DWORD GrGetLineAddrOfY(int nScrnY);
int   GrGetScrnWidth(void);
int   GrGetScrnHeight(void);
int   GrGetScrnPitch(void);
int   GrGetScrnLastX(void);
int   GrGetScrnLastY(void);
DWORD GrGetMemSizePerScreen(void);
void  GrSetDrawingAddr(DWORD dAddr);
DWORD GrGetDrawingAddr(void);
void  GrSetPolygonIndex(void);
LINKX *GrGetPolygonIndex(void);
void  GrFillFullScreen(CLRCHART nColorID);
void  GrScrollLeft(int nTargetX,int nTargetY,int nSourceX,int nSourceY,int nSizeX,int nSizeY);
void  GrSaveVideoMem(CLRCHART *pSaveMem,int nScrnX0,int nScrnY0,int nScrnX1,int nScrnY1);
void  GrRestVideoMem(CLRCHART *pSaveMem,int nScrnX0,int nScrnY0,int nScrnX1,int nScrnY1);
void  GrCopyChartScrnRectToScrn(int nTargetPage,int nSourcePage,int nScrnX0,int nScrnY0,int nScrnX1,int nScrnY1);
void  GrCopyChartScrnFullToScrn(int nTargetPage);
void  GrCopyMenuScrnFullToScrn(int nTargetPage);
#ifdef _SI_70AM_
void  GrSaveChartScrn();
void  GrRestoreChartToScrn();
#endif
void  GrSetMenuDrawingPage(int nPage);
int   GrGetMenuDrawingPage(void);
void  GrSetMenuViewingPage(int nPage);
int   GrGetMenuViewingPage(void);
CLRCHART GrGetChartScrnColor(int nScrnX,int nScrnY);
CLRMENU GrGetMenuScrnColor(int nScrnX,int nScrnY);
void  GrGetBlendedScrnColor(int nScrnX,int nScrnY,UCHAR *pR,UCHAR *pG,UCHAR *pB);
void  GrPlot(int X,int Y,CLRCHART nColorXX);
CLRCHART *GrSavePlot(int X,int Y,CLRCHART nColorXX,CLRCHART *pSaveMem);
CLRCHART *GrRestPlot(int X,int Y,CLRCHART *pSaveMem);
void  GrXorPlot(int X,int Y,CLRCHART nColorXX);
void  GrFastLine(int X1,int Y1,int X2,int Y2,CLRCHART nColorID);
void  GrLine(int X1,int Y1,int X2,int Y2,CLRCHART nColorID);
CLRCHART *GrSaveLine(int X1,int Y1,int X2,int Y2,CLRCHART nColorID,CLRCHART *pSaveMem);
CLRCHART *GrRestLine(int X1,int Y1,int X2,int Y2,CLRCHART *pSaveMem);
void  GrTwoDotLine(int X1,int Y1,int X2,int Y2,CLRCHART nColorID);
void  GrThickLine(int X1,int Y1,int X2,int Y2,CLRCHART nColorID,int nThick);
CLRCHART *GrSaveThickLine(int X1,int Y1,int X2,int Y2,CLRCHART nColorID,int nThick,CLRCHART *pSaveMem);
CLRCHART *GrRestThickLine(int X1,int Y1,int X2,int Y2,int nThick,CLRCHART *pSaveMem);
void  GrDotThickLine(int X1,int Y1,int X2,int Y2,CLRCHART nColorID,int nThick,int nDrawDot,int nSkipDot);
void  GrContDotThickLine(int X1,int Y1,int X2,int Y2,int nResetMode,CLRCHART nColorID,int nThick,int nDrawDot,int nSkipDot);
void  GrContDashDotThickLine(int X1,int Y1,int X2,int Y2,int nResetMode,CLRCHART nColorID,int nThick,HWORD wDotPattern,HWORD wDotMask);
void  GrContPatternLine(int X1,int Y1,int X2,int Y2,int nResetMode,CLRCHART nColorID,DWORD *pPatternData,int nPatternLines);
void  GrDrawRect(int X1,int Y1,int X2,int Y2,CLRCHART nColorID,int nThick);
void  GrFillRect(int X1,int Y1,int X2,int Y2,CLRCHART nColorID);
void  GrPatternRect(int X1,int Y1,int X2,int Y2,CLRCHART nColorID);
void  GrFillCircle(int nPointX,int nPointY,int nRadius,CLRCHART nColorID);
void  GrCircle(int nPointX,int nPointY,int nRadius,CLRCHART nColorID);
void  GrThickCircle(int nPointX,int nPointY,int nRadius,int nThick,CLRCHART nColorID);
CLRCHART *GrSaveCircle(int nPointX,int nPointY,int nRadius,CLRCHART nColorID,CLRCHART *pSaveMem);
CLRCHART *GrRestCircle(int nPointX,int nPointY,int nRadius,CLRCHART *pSaveMem);
void  GrEllipse(int x0,int y0,int a0,int b0,CLRCHART nColorID);
int   GrDrawArc(int nPointX,int nPointY,int nRadius,CLRCHART nColorID,int nWidth,int nStartAngle,int nLastAngle,int nDrawDot,int nSkipDot,int *pStartX,int *pStartY,int *pEndX,int *pEndY);
void  GrVertLine(int Y0,int Y1,int X,CLRCHART nColorID);
void  GrHoriLine(int X0,int X1,int Y,CLRCHART nColorID);
void  GrAddrHoriAlphaLine(int X0,int X1,DWORD dScrnHoriAddr);
void  GrAddrHoriLine(int X0,int X1,DWORD dScrnHoriAddr,CLRCHART nColorXX);
void  GrAddrHoriXorLine(int X0,int X1,DWORD dScrnHoriAddr,CLRCHART nColorXX);
void  GrAddrHoriHatchLine(int X0,int X1,DWORD dScrnHoriAddr,int nStartX,int nSkipDot,CLRCHART nColorXX);
void  GrAddrHoriPatternLine(int X0,int X1,DWORD dScrnHoriAddr,HWORD wPattern,CLRCHART nForeClrXX,HWORD wForeOpr,CLRCHART nBackClrXX,HWORD wBackOpr);
void  GrAddrFillPatternLine(int X0,int X1,DWORD dScrnHoriAddr,HWORD wPattern,CLRCHART nForeClrXX,CLRCHART nBackClrXX,int nBackFill);

int   GrDrawFillPoly(POLY *pPolygon,int nPointNo,CLRCHART nColorID,int nMaxLineY);
int   GrDrawGridPoly(POLY *pPolygon,int nPointNo,CLRCHART nColorID,int nMaxLineY,int nSpaceDot);
int   GrDrawPatternPoly(POLY *pPolygon,int nPointNo,CLRCHART nColorID,int nMaxLineY,HWORD *pPattern,int nPatternLines,CLRCHART nBackClrID,int nBackFill);
int   GrDrawLinePoly(POLY *pPolygon,int nPointNo,CLRCHART nColorID,int nThick);

int   GrDrawLineSegment(POLY *pPolygon,int nPointNo,CLRCHART nColorID,int nThick,int nStyle);
int   GrDrawContLineSegment(POLY *pPolygon,int nPointNo,CLRCHART nColorID,int nThick,int nDrawDot,int nSkipDot);
int   GrDrawContDashLineSegment(POLY *pPolygon,int nPointNo,CLRCHART nColorID,int nThick,HWORD wDotPattern,HWORD wDotMask);
int   GrDrawContPatternLineSegment(POLY *pPolygon,int nPointNo,CLRCHART nColorID,DWORD *pPatternData,int nPatternLines);

void  GrContPatternForwardLine(int X1,int Y1,int X2,int Y2,int nResetMode,CLRCHART nColorID,DWORD *pPatternData,int nPatternLines);
int   GrDrawContPatternForwardLineSegment(POLY *pPolygon,int nPointNo,CLRCHART nColorID,DWORD *pPatternData,int nPatternLines);

void  GrPolygonQuickSort(POLY *pData,int nDataNo);
void  GrIntegerQuickSort(int *pData,int nDataNo);
void  GrIntegerInsertSort(int *pData,int nDataNo);
int   GrClippingScreenLong(INT32 *pX0,INT32 *pY0,INT32 *pX1,INT32 *pY1);
void  GrAlarm16x16SymbolDraw(HWORD wSymbolNo,int X,int Y,CLRCHART nColorID);
void  GrWayPnt16x16MarkDraw(HWORD wWayPntNo,int X,int Y,CLRCHART nColorID);
void  GrWayPnt32x32MarkDraw(HWORD wWayPntNo,int X,int Y,CLRCHART nColorID);
void  GrTidalSymbolMarkDraw(int X,int Y);
CLRCHART *GrShipMarkSaveDrawInUCHAR(int nMarkWidth,int nMarkHeight,UCHAR *pShipMarkFont,int X,int Y,CLRCHART *pSaveMem);
CLRCHART *GrShipMarkRestDrawInUCHAR(int nMarkWidth,int nMarkHeight,UCHAR *pShipMarkFont,int X,int Y,CLRCHART *pSaveMem);
CLRCHART *GrShipMarkSaveDrawInHWORD(int nMarkWidth,int nMarkHeight,HWORD *pShipMarkFont,int X,int Y,CLRCHART *pSaveMem);
CLRCHART *GrShipMarkRestDrawInHWORD(int nMarkWidth,int nMarkHeight,HWORD *pShipMarkFont,int X,int Y,CLRCHART *pSaveMem);
void  GrDrawTextStr06x04(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrDrawTextStr07x05(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrDrawTextStr09x06(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrDrawTextStr10x08(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrDrawTextStr16x08(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrDrawTextStr16x09(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrMenuTextStr16x09(const UCHAR *pStr,int X,int Y,CLRMENU dForeColor);
void  GrDrawTextStr20x12(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrDrawTextStr29x16(const UCHAR *pStr,int X,int Y,CLRCHART nForeID);
void  GrDrawTextEng06x04(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrDrawTextEng07x05(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrDrawTextEng09x06(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrDrawTextEng10x08(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrDrawTextEng16x08(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrDrawTextEng16x09(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrMenuTextEng16x09(UCHAR bChr,int X,int Y,CLRMENU dForeColor);
void  GrDrawTextEng20x12(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrDrawTextEng29x16(UCHAR bChr,int X,int Y,CLRCHART nForeXX);
void  GrDrawTextUniStrX(int X,int Y,HWORD *pText,CLRCHART nForeID,CLRCHART nBackID,xFONTYY *pFont,int nBackFillMode);
void  GrDrawMapStr16x16(const UCHAR *pStr,int X,int Y,CLRCHART nForeID,int nLngMode,int nUniCodeMode);
void  GrDrawMapStr19x21(const HWORD *pUniStr,int X,int Y,CLRCHART nForeID);
void  GrDrawMapStr29x30(const HWORD *pUniStr,int X,int Y,CLRCHART nForeID);
int   GrDrawTextUniWWxHH(HWORD wUniCode,xFONTYY *pFont,int X,int Y,CLRCHART nForeXX,CLRCHART nBackXX,int nTransparent);
int   GrGetUniImageWWxHH(HWORD wCode,xFONTYY *pFont);
void  GrDrawTextDepthDigit(const UCHAR *pStr,int X,int Y,CLRCHART nColorID);
int   GrMakePolygonByScrnPos(POLY *pPolyData,COORX *pPolyCoor,int nCoorSize,int *pMaxYline);
REAL  GrGetRealCosValByTable(int nMul10Deg);
REAL  GrGetRealSinValByTable(int nMul10Deg);
int   GrGetIntCosValByTable(int nMul10Deg);
int   GrGetIntSinValByTable(int nMul10Deg);
void  GrRotateImageInUCHAR(UCHAR *pImgSource,int nImgWidth,int nImgHeight,UCHAR *pImgTarget,int nRotateDeg);
void  GrRotateImageInHWORD(HWORD *pImgSource,int nImgWidth,int nImgHeight,HWORD *pImgTarget,int nRotateDeg,HWORD wFillColor);
void  GrGetReflectedImageInHWORD(HWORD *pImgSource,int nImgWidth,int nImgHeight,HWORD *pImgTarget);

void  GrNewFishHavenMarkDraw(HWORD wMark,int X,int Y,CLRCHART nColorXX);
DWORD GrGetNewChartMarkMetaOffset(HWORD wMark,int nType,UCHAR bNextData);
UCHAR *GrGetNewChartMarkDataHead(HWORD wMark,int nType,UCHAR bNextData);
UCHAR *GrGetNewChartMarkDataReal(HWORD wMark,int nType,UCHAR bNextData);
int   GrGetNewChartMarkWidth(HWORD wMark,int nType,UCHAR bNextData,int *pWidthX,int *pWidthY);
int   GrGetNewChartMarkPivot(HWORD wMark,int nType,UCHAR bNextData,int *pPivotX,int *pPivotY);
void  GrNewChartMarkDraw(HWORD wMark,UCHAR bNextData,int X,int Y,int nDrawTop,int nDrawLamp,int nDayToNightMode);
void  GrNewChartMarkDrawReal(UCHAR *pData,int nWidthX,int nWidthY,int nPivotX,int nPivotY,int X,int Y,int nDayToNightMode);

xFONTYY *GrGetUniFontStart(xFONTYY *pFont,HWORD wCode);
int   GrGetUniStrFontWidth(xFONTYY *pFont,HWORD *pUniStr);
int   GrGetUniFontWidth(xFONTYY *pFont,HWORD wCode);
int   GrGetUniFontHeight(xFONTYY *pFont,HWORD wCode);

#if  defined(__SAMYUNG__) && !defined(_ICON_GME_)
void GrFillUniFontCharData(xFONTYY *pFont,HWORD wCode);
#endif
//=============================================================================

//=============================================================================
void  GrSetChartRotationMode(int nMode);
int   GrGetChartRotationMode(void);
void  GrSetPlotterMode(int nMode);
void  GrSetMixingLevelMode(int nMode);
void  GrSetAntiClutterMode(int nMode);
void  GrSetValueAddDataMode(int nMode);
void  GrSetLatLonGridMode(int nMode);
void  GrSetBoundaryMode(int nMode);
void  GrSetTxtIconSizeMode(int nMode);
void  GrSetNamesMode(int nMode);
void  GrSetLightsMode(int nMode);
void  GrSetNavAidsMode(int nMode);
void  GrSetAttentionAreaMode(int nMode);

void  GrSetWaterFeatureMode(int nMode);
void  GrSetBathyLinesMode(int nMode);
void  GrSetSpotSoundingMode(int nMode);
void  GrSetObjectDepthMode(int nMode);
void  GrSetBathyMinValue(int nMode);
void  GrSetBathyMaxValue(int nMode);
void  GrSetSoundingMinMax(void);
void  GrSetTidalStreamMode(int nMode);
void  GrSetLandFeatureMode(int nMode);
void  GrSetLandElevationMode(int nMode);
void  GrSetSecurityZoneMode(int nMode);
void  GrSetSecurityZoneValue(int nValue);
//-----------------------------------------------------------------------------
int   GetCmapZoomLevelSize(void);
int   cmgFuncGetRealScrX(int X);
int   cmgFuncGetRealScrY(int Y);
//int   cmgWinGetRealScrX(int nWinLeft,int X);
//int   cmgWinGetRealScrY(int nWinBottom,int Y);
//int   cmgWinGetVirtScrX(int nWinLeft,int X);
//int   cmgWinGetVirtScrY(int nWinBottom,int Y);
#define cmgWinGetRealScrX(nWinLeft,X)   (nWinLeft + X)
#define cmgWinGetRealScrY(nWinBottom,Y) (nWinBottom - Y)
#define cmgWinGetVirtScrX(nWinLeft,X)   (X - nWinLeft)
#define cmgWinGetVirtScrY(nWinBottom,Y) (nWinBottom - Y)
void  cmgSetRealScrnStartX(int nStartX);
void  cmgSetRealScrnLastY(int nLastY);
void  cmgSetChartWindowSize(int nSizeX,int nSizeY);
void  cmgSetRealClipRegion(void);
int   cmgClippingScreen(int *X0,int *Y0,int *X1,int *Y1);
void  cmgGrLine(int X1,int Y1,int X2,int Y2);
void  cmgGrttttLine(int X1,int Y1,int X2,int Y2);
void  cmgGrPlusLine(int X1,int Y1,int X2,int Y2);
int   cmgGrLinePoly(POLY *pPolygon,int nPointNo);
int   cmgGrFillPoly(POLY *pPolygon,int nPointNo);
void  ClearcMapScreen(void);
void  SetcMapBoundingsMode(int nMode);
void  UpdatecMapScreen(void);
void  SetStartMapZoomScale(void);
int   GetMapZoomScaleIndex(void);
int   MoveHomePosByLat(int nMode);
int   MoveHomePosByLon(int nMode);
int   CheckScrLatRangeError(void);
CLRCHART GetcMapPrintColor(CLRCHART nColor);
UCHAR GetcMapColorNo(UCHAR bColor);
void  SetDepthSoundingUnit(void);
HWORD FindObjIncMap(sObjInfo *pInfoArr,HWORD wMaxObj,Bool nFullInfo,int X,int Y);
int   FilterTidalStreamInfoObjects(sObjInfo *pInfoArr,HWORD wNumEl);
int   DisplayObjInfoIncMap(int X,int Y);
Bool  DrawInfoIconIncMap(int X,int Y,sObjInfo *pObj);
char *MakeAttrStringIncMap(sAttrInfoExp *pAttrinfo,char *pBuff,Word wBufSize);
//-----------------------------------------------------------------------------
void  SetCmapDepthUnit(int nUnit);
int   GetCmapDepthUnit(void);
void  SetCmapEmptyMode(int nMode);
int   GetCmapEmptyMode(void);
void  SetCmapZoomScale(int nScale);
int   GetCmapZoomScale(void);
ULONG *GetCmapZoomScaleTableLong(void);
REAL  *GetCmapZoomScaleTableReal(void);
REAL  *GetCmapDistRangeTableReal(int nDstUnit);
REAL  GetCmapDistRangeValueReal(int nZoomIndex,int nDstUnit); 
CHAR  **GetCmapDistRangeTable(int nDstUnit);
CHAR  *GetCmapDistRangeString(int nZoomIndex,int nDstUnit);

void  SetCmapPerspectiveMode(int nMode);
int   GetCmapPerspectiveMode(void);
void  SetCmapWorldMapAddr(UCHAR *pWWBstartAddr);
UCHAR *GetCmapWorldMapAddr(void);
int   GetCmapClipX0(void);
void  SetCmapClipX0(int X0);
int   GetCmapClipX1(void);
void  SetCmapClipX1(int X1);
int   GetCmapClipY0(void);
void  SetCmapClipY0(int Y0);
int   GetCmapClipY1(void);
void  SetCmapClipY1(int Y1);
REAL  GetCmapScrLatDist(void);
REAL  GetCmapScrLonDist(void);
REAL  GetCmapScrLatDiff(void);
REAL  GetCmapScrLonDiff(void);
long  GetCmapScrLatUpMerc(void);
long  GetCmapScrLatDnMerc(void);
long  GetCmapScrLonLfMerc(void);
long  GetCmapScrLonRtMerc(void);
long  GetCmapZoomedDisplayScale(void);
//=============================================================================
void  GetObjectTextString(sObjInfo *pObjInfo,CHAR *pObjText,int nTextMax);
//=============================================================================
Word  cMapSearchAvailablePortService(LMERC nMercLat,LMERC nMercLon);
int   cMapGetDescriptionAvailablePortService(SWord wIdxServiceAvailable,Word *pServiceLabel,UnicodeString *pLabelDescr,Word wDescrBuffLen);
int   cMapSearchNearestPort(LMERC nMercLat,LMERC nMercLon,Word *pNumofInfo,xSBNINFO *pPortInfo);
//=============================================================================
void  GrMakeNewPecCurMarkBase(UCHAR **pMarkText, int nSize, UCHAR *pMarkData);
void  GrDrawNewPecCurMarkDataX(int nCurDeg, int X, int Y, CLRCHART nColorID);
void  GrDrawNewPecCurMarkDataY(int nCurDeg, int X, int Y, CLRCHART nColorID);
void  GrDrawNewPecCurMarkDataZ(int nCurDeg, int X, int Y, CLRCHART nColorID);
//=============================================================================
void  SetCmapCrntDate(int nYear, int nMon, int nDay); // c-map-current
void  SetCmapCrntTime(int nHH, int nMM, int nSS);     // c-map-current
void  ClrCmapCrntDateTime(void);                      // c-map-current
void  GpsCmapCrntDateTime(void);                      // c-map-current
void  GetCmapCrntDate(xSYSDATE *pDate);               // c-map-current
void  GetCmapCrntTime(xSYSTIME *pTime);               // c-map-current
int   IsCmapCrntDateValid(void);                      // c-map-current
//=========================================================================

#ifdef  __cplusplus
}
#endif

#endif


