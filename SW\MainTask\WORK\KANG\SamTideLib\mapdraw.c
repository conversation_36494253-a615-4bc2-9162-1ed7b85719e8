/*  DrawMap routine
    Last modified 2005-12-13 to replace TrackMouseEvent with standard timer

    This module is used by the Station Locator interface (loclib) to handles
    drawing the world map bitmap (enclosed) or World Vector Showreline (WVS)
    data sets that can be user downloaded.

    The WVS support logic is a glaring plagerism of <PERSON>'s Xide 2.8
    module Xxmap.c, well, at least the actual drawing logic is from that source.

    The rest of the code is the contribution of <PERSON>.

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

*/
#include "config.h"

//#include <windows.h>
//#include <windowsx.h>  // <- nothing to do with Xwindows ;-) just some handy macros
//#include <commdlg.h>

#include "everythi.h"
#include "loclib.h"
#include "wxtidres.h"

extern HINSTANCE g_hinst;
extern HWND     hwndLoc, hwndMain;
extern COLORREF fgmapdot_color,fgcurdot_color;
extern RECT     WinDimMap;
extern char     HelpFileName[];
extern int      check_file_path(char *dst, char*filename);

/* Exported stuff */
HWND   hwndMap = NULL;
float  mapFindRadius, NMPerPixelX, NMPerPixelY;
int    PixelPerBAMX, PixelPerBAMY, mapMpy;
void   CreateMapWindow(HWND hwndParent, double lon, double lat, int zoom);

#define NMPerBAM    (360.0*60.0/65536.0) /* Nautical Miles per BAM conversion */

#define HOVER_SIZE_X 4                   /* Pixel range to trigger hover exit */
#define HOVER_SIZE_Y 4
#define CUE_HOVER    1                   /* map_cues value for Hover mode */
#define HOVER_DELAY  400                 /* Delay in milliseconds before popup */

#define MAP_COLOR   (RGB(128,128,128))   /* WVS Map grid and text color */
#define GRID_COLOR  (RGB(128,128,255))   /* WVS Map grid and text color */
#define GRID_BORDER  10                  /* Pixels to not use on map sides */

#define POPUP_BORDER 1                   /* Extra slop pixels around pop-up box */

/* Externals used */
//int     wvsrtv (char *file, int latd, int lond, float **latray, float **lonray, int **segray);
//void    XFillRectangle (HDC display, COLORREF color, int x, int y,int  w,int h);
//HBITMAP CaptureWindow(HWND hwndWindow, int width, int heigth);
//LRESULT CALLBACK TidesMapProc(HWND hwnd,UINT msg,WPARAM wParam,LPARAM lParam);

/* -----------------10/29/97 5:47PM------------------
   Start of map window and functions
 --------------------------------------------------*/
/* Locals */
static int hold_map_window, haveWVS;

static HBITMAP mapSaveHbm=NULL;

#define WVSCLOSE "clean"

#define MAX_ZOOM 12
#define MAX_WVS  5

char fullWVSFilenames[6][256], *WVSFileToUse;

static char *szWVSNames[]={
            {"wvs43.dat"  },            /* 0 - 1:43,000,000 */
            {"wvs12.dat"  },            /* 1 - 1:12,000,000 */
            {"wvs3.dat"   },            /* 2 - 1:3,000,000  */
            {"wvs1.dat"   },            /* 3 - 1:1,000,000  */
            {"wvs250k.dat"},            /* 4 - 1:250,000    */
            {"wvsfull.dat"},            /* 5 - 1:?????      */
            {""           } };

int availWVS[MAX_WVS+1], maxMpy;


static char     szMapName[] = "WorldMap";
static HBITMAP  hBitmap, hBmpOld;
static WNDCLASS wcm;
static int      MapWinX, MapWinY, MapWinH, MapWinW;
static int      map_cx, map_cy, gridMinY;
static BITMAP   bm;
static HWND     hwndStaPopup=NULL;
static int      usingWVS=FALSE;
static float    lonL, lonR, latT, latB, latC, lonC, lonShift;

typedef struct {
   int x;
   int y;
   int lon;
   int lat;
} map_fix_entry;

/*-----------------7/2/2005 10:31AM-----------------
 * Check for WVS files present
 * --------------------------------------------------*/
int CheckWVS() {
int i;

  haveWVS = FALSE;
  maxMpy  = 4;    // Default for bitmap
  for (i=0; i<=MAX_WVS && strlen(szWVSNames[i]); i++) {
    if (check_file_path(fullWVSFilenames[i], szWVSNames[i])) {
//    if((hfind = FindFirstFile( szWVSNames[i], &ffbuf)) != INVALID_HANDLE_VALUE) {
      availWVS[i] = 1;
      haveWVS = TRUE;
//      FindClose(hfind);
      if      (i < 1) maxMpy = 5;
      else if (i < 3) maxMpy = 7;
      else if (i < 4) maxMpy = 9;
      else            maxMpy = MAX_ZOOM;
    }
    else availWVS[i] = 0;
  }

  if (mapMpy < 0     ) mapMpy = 0;
  if (mapMpy > maxMpy) mapMpy = maxMpy;
  return haveWVS;
}

/* -----------------10/22/97 8:56AM------------------
Map point conversion routines.
 The following routines are used to convert to and from
 map coordinates (y,x) and longitude, latitude.
 Since any map we will use will be a flat earth map, any
 lat/lon point has a linear relationship with map coords.
 The map however, will always be slewed usually with emphasis
 on the northern hemisphere.
 So, what we do is define conversions for the four corners
 of the map (and world), then define control points for
 known locations on the map.  For any given point, we
 perform linear interpolation between points nearest
 less than and greater than the point of interest.
 --------------------------------------------------*/

static map_fix_entry map_fix[]= {
//   x   y   lon lat
   {  0,  0,-179, 89}, // Upper left
   {380,235,+179,-89}, // Lower right
   { 23, 75,-150, 61}, // Anchorage, AK
   { 53,116,-122, 38}, // San Fran
   { 67,133,-110, 23}, // Cabo San Lucas
   { 98,150, -80,  9}, // Balboa
   { 96,131, -82, 25}, // Key West
   {122,221, -58,-52}, // Stanley, Falkland Islands
   {202,195,  18,-34}, // Capetown, Africa
   {181, 96,   0, 51}, // Brighton, England
   {129,159, -51,  0}, // Amazon River
   {343,193, 153,-27}, // Brisbane, Aus
   {-1,-1,-1,-1}
};

float interpolate(int p, int ple, int pge, int rle, int rge) {
   if (ple == pge) return (rle);
   else return(((float)(p - ple) * (float)(rge - rle)) / (float)(pge - ple) + rle);
}

/* -----------------10/31/97 6:23PM------------------
   Convert a map (x,y) coordinate to LON/LAT coordinate.
 --------------------------------------------------*/
void cvt_map_2_lonlat( float *lon, float *lat, int map_x, int map_y ) {
int i, xle, xge, yle, yge;

   if (usingWVS) {
     *lon = (((float) map_x / MapWinW) * (lonR-lonL)) + lonL;
     *lat = (((float)-map_y / MapWinH) * (latT-latB)) + latT;
     if (*lon > 180.0) *lon -= 360.0;
     if (*lat >  90.0) *lat -= 180.0;
     if (*lat < -90.0) *lat += 180.0;
   }
   else {
     int X, Y;
     X = map_cx + ((map_x-(MapWinW/2)) * bm.bmWidth ) / (MapWinW*(mapMpy+1));
     Y = map_cy + ((map_y-(MapWinH/2)) * bm.bmHeight) / (MapWinH*(mapMpy+1));
     map_x = X;
     map_y = Y;
     if (map_x > bm.bmWidth ) map_x -= bm.bmWidth;  // Clip to edges
     if (map_x < 0          ) map_x += bm.bmWidth;
     if (map_y > bm.bmHeight) map_y -= bm.bmHeight; // Clip to edges
     if (map_y < 0          ) map_y += bm.bmHeight;
     xle = yle = 0; // Entry 0 has upper left corner
     xge = yge = 1; // Entry 1 has lower left corner
     for (i=0; map_fix[i].x >= 0; i++) {
        if ((map_fix[i].x <= map_x)&&(map_fix[xle].x < map_fix[i].x)) xle=i;// Find closest X <=
        if ((map_fix[i].x >= map_x)&&(map_fix[xge].x > map_fix[i].x)) xge=i;// Find closest X >=
        if ((map_fix[i].y <= map_y)&&(map_fix[yle].y < map_fix[i].y)) yle=i;// Find closest Y <=
        if ((map_fix[i].y >= map_y)&&(map_fix[yge].y > map_fix[i].y)) yge=i;// Find closest Y >=
     }
     *lon = interpolate(map_x,map_fix[xle].x, map_fix[xge].x, map_fix[xle].lon, map_fix[xge].lon);
     *lat = interpolate(map_y,map_fix[yle].y, map_fix[yge].y, map_fix[yle].lat, map_fix[yge].lat);
   }
   return;
}

/* -----------------10/31/97 6:23PM------------------
   Convert a map (x,y) coordinate to LON/LAT coordinate in BAMS
 --------------------------------------------------*/
LPARAM cvt_map_2_lonlat_pt( int map_x, int map_y ) {
  float lon, lat;

  cvt_map_2_lonlat( &lon, &lat, map_x, map_y );
  return ( MAKELONG(DEG2BAMS(lat*2.0)&0xfffe, DEG2BAMS(lon)) );
}

/* -----------------10/31/97 6:24PM------------------
   Convert a LON/LAT coordinate to map (x,y) coordinate.
 --------------------------------------------------*/
void cvt_lonlat_2_map( int *x1, int *y1, double lon, double lat ) {

   if (usingWVS) {
     float tlon;
     tlon = lon;
     if (tlon < 0.0) tlon += lonShift;              // Normal international date line
     if (tlon < 0.0 && lonR > 180.0) tlon += 360.0; // If we are in 0..360 world
     *x1 = (tlon-lonL) / (lonR-lonL) * MapWinW - PixelPerBAMX;
     *y1 = (latT-lat)  / (latT-latB) * MapWinH - PixelPerBAMY;
     return;
   }
   else {
     int i, ilon, ilat, xle, xge, yle, yge;

     ilon = lon;
     ilat = lat;
     xle = yge = 0;
     xge = yle = 1;
     for (i=0; map_fix[i].x >= 0; i++) {
        if ((map_fix[i].lon <= ilon)&&(map_fix[xle].lon < map_fix[i].lon)) xle=i;// Find closest X <=
        if ((map_fix[i].lon >= ilon)&&(map_fix[xge].lon > map_fix[i].lon)) xge=i;// Find closest X >=
        if ((map_fix[i].lat <= ilat)&&(map_fix[yle].lat < map_fix[i].lat)) yle=i;// Find closest Y <=
        if ((map_fix[i].lat >= ilat)&&(map_fix[yge].lat > map_fix[i].lat)) yge=i;// Find closest Y >=
     }
     *x1 = (int)interpolate(ilon,map_fix[xle].lon, map_fix[xge].lon, map_fix[xle].x, map_fix[xge].x);
     *y1 = (int)interpolate(ilat,map_fix[yle].lat, map_fix[yge].lat, map_fix[yle].y, map_fix[yge].y);
     return;
   }
}

/*-----------------7/4/2005 9:34AM------------------
 * Convert lon/lat point in BAMS to map (x,y)
 * --------------------------------------------------*/
void cvt_lonlat_pt_2_map( int *x1, int *y1, LPARAM point ) {
  cvt_lonlat_2_map(x1, y1, BAMS2DEG(HIWORD(point)), BAMS2DEG(LOWORD(point))*.5);
  if (!usingWVS) {
    int relx, rely;
    relx = *x1 - map_cx;
    rely = *y1 - map_cy;
    if ((relx) >  (bm.bmWidth/2)) relx = bm.bmWidth - relx;
    if ((relx) < -(bm.bmWidth/2)) relx = bm.bmWidth + relx;
    *x1 = ((relx * (mapMpy+1) * MapWinW) / bm.bmWidth) + (MapWinW/2);
    *y1 = ((rely * (mapMpy+1) * MapWinH) / bm.bmHeight)+ (MapWinH/2);
  }
}

/*-----------------7/2/2005 1:21PM------------------
 * Compute Map factors
 * Returns non-zero if there has been a change.
 * --------------------------------------------------*/
int ComputeWVSMapFactors() {
int zoom, fnum;
static int pvW, pvH, pvMpy=-1;
static float pvLonC, pvLatC;

/* if nothing has changed, then leave */
  if (pvW==MapWinW && pvH==MapWinH && pvMpy==mapMpy && pvLonC==lonC && pvLatC==latC)
    return(0);

/* From map multiplier, figure which WVS file is ideal. */
  switch (mapMpy) {
    case 0:
      latC = 0.0;
      lonC = 0.0;
      fnum = 0;
      gridMinY = 0;
      break;

    case 1:  fnum = 0; gridMinY =1800; break;  //180D
    case 2:  fnum = 1; gridMinY =1200; break;  // 90D
    case 3:  fnum = 2; gridMinY = 600; break;  // 45D 900
    case 4:  fnum = 2; gridMinY = 300; break;  // 22D 300
    case 5:  fnum = 3; gridMinY = 180; break;  // 11D 240
    case 6:  fnum = 3; gridMinY =  60; break;  //  6D 120
    case 7:  fnum = 4; gridMinY =  40; break;  //  3D  60 min
    case 8:  fnum = 5; gridMinY =  20; break;  //  1D  30
    case 9:  fnum = 5; gridMinY =  10; break;  // 30M  10
    case 10: fnum = 5; gridMinY =   5; break;  // 15M   5
    case 11: fnum = 5; gridMinY =   2; break;  //  7M   1 min invervals
    default: fnum = 5; gridMinY =   1;
  }

/* Save previous values */
  pvW    = MapWinW;
  pvH    = MapWinH;
  pvMpy  = mapMpy;
  pvLatC = latC;
  pvLonC = lonC;

/* If ideal file not available, find nearest one */
  if (!availWVS[fnum]) {
    int over, under, overcnt=0, undercnt=0;
    for (over=fnum;  over<=MAX_WVS  && !availWVS[over];  over++)  overcnt++;
    for (under=fnum; under>=0       && !availWVS[under]; under--) undercnt++;
    if (over>MAX_WVS) over=MAX_WVS;
    if (under<0)  undercnt=MAX_WVS;
    if      (overcnt<=undercnt && availWVS[over]) fnum=over;
    else fnum=under;

  }

  WVSFileToUse = fullWVSFilenames[fnum];

  zoom = 1 << mapMpy;
/* From map center, find limits */
  if (latC >  90.0) latC -= 180.0;
  if (latC < -90.0) latC += 180.0;
  if (lonC > 180.0) lonC -= 360.0;
  if (lonC <-180.0) lonC += 360.0;

  lonL = lonC - (180.0 / zoom);
  lonR = lonC + (180.0 / zoom);
  latT = latC + ( 90.0 / zoom);
  latB = latC - ( 90.0 / zoom);

/* If map spans +180..-180 international date line, shift to 180-360 */
  if (lonL <= -180.0 && lonR < 0.0) {
    lonShift = 360.0;
    lonL    += 360.0;
    lonR    += 360.0;
  }
  else lonShift = 0.0;

//printf("latC %f, lonC %f, L %f, R %f, S %f\n", latC, lonC, lonL, lonR, lonShift);
  return 1;
}

/* -----------------10/27/97 7:35AM------------------
   Draw map in map window, centered around a given point with zooming.
   Since the map is cylindrical, the hard part is filling in the left
   and right sides with residue from the right and left sides of the
   bitmap. ARG!
 --------------------------------------------------*/
void DrawMap( HDC mapDC, HDC mapmemDC, HWND hwnd) {
  static int haveBitBlt=0;

// Find constants and map hover station radius (NM) as 6 pixels. 1 NM = 1 minute.
  NMPerPixelX   = (360.0 * 60.0 / (1 << mapMpy)) / MapWinW;
  NMPerPixelY   = (180.0 * 60.0 / (1 << mapMpy)) / MapWinH;
  mapFindRadius = NMPerPixelY * 6 + NMPerBAM;
  PixelPerBAMX  = (NMPerBAM / NMPerPixelX) / 2;
  PixelPerBAMY  = (NMPerBAM / NMPerPixelY) / 2;

  if (haveWVS) {
    /* Draw WVS map */

    usingWVS = TRUE;

//  Map factors have changed or we have no previously saved image
    if (ComputeWVSMapFactors() || !haveBitBlt || !mapSaveHbm) {
      HPEN   mapLineGC;
      HPEN   hpen;
      int    startlat, endlat, startlon, endlon, ilat, ilon, x0, x1, y0, y1, cnt, maxLine = MapWinW/2;
      static float *latray=NULL, *lonray=NULL;
      static int   *segray=NULL;
      float  tlon;
      RECT   r;

      r.left = r.top = 0;
      r.right  = MapWinW;
      r.bottom = MapWinH;

      FillRect(mapDC, &r, GetStockObject(WHITE_BRUSH));

      startlat = floor(latB);
      endlat   = ceil( latT);
      startlon = floor(lonL);
      endlon   = ceil( lonR);

      if (startlat< -90.0) startlat = -90.0;
      if (endlat  >  90.0) endlat   =  90.0;

      mapLineGC = CreatePen(PS_SOLID,0,MAP_COLOR);
      hpen      = SelectPen(mapDC,mapLineGC);

      for (ilat = startlat ; ilat < endlat ; ilat++) {

        for (ilon = startlon ; ilon < endlon ; ilon++) {
          int k, nseg, offset = 0;

          nseg = wvsrtv (WVSFileToUse, ilat, ilon, &latray, &lonray, &segray);

          for (k = 0 ; k < nseg ; k++) {

            for (cnt = 0 ; cnt < segray[k] ; cnt++) {

              tlon = lonray[offset];
              if (tlon<0.0) tlon +=lonShift;  /* move -180 span to +180 range */

              x1 = (tlon-lonL)           / (lonR-lonL) * MapWinW;
              y1 = (latT-latray[offset]) / (latT-latB) * MapWinH;
              offset++;

              if (cnt &&                      /* Don't try to draw first point */
                  x1-x0 < maxLine      &&     /* AND not a map spanning line */
                  x0-x1 < maxLine      &&
                  (x0!=x1 || y0!=y1)   &&     /* AND not duplicate point */
                  x1>=0 && x1<=MapWinW &&     /* AND X is legal */
                  y1>=0 && y1<=MapWinH) {     /* AND Y is legal */

                MoveToEx(mapDC,x0,y0,NULL);
                LineTo  (mapDC,x1,y1);        /* THEN draw the line */
              }

              x0 = x1;
              y0 = y1;

            } /* End points in segment */
          } /* End of segments */
        } /* End all lons */
      } /* End all lats */
      wvsrtv ("clean", 0, 0, &latray, &lonray, &segray);

      if (gridMinY && map_grid) {

        /* Draw WVS map grid lines */

        int x0, y0, latmin, lonmin, gridMinX = gridMinY*2;
        char text[20], ch;
        HFONT oldfont = SelectFont(mapDC,GetStockObject(ANSI_VAR_FONT));

        SetTextColor(mapDC,GRID_COLOR);
        DeleteObject(mapLineGC);
        mapLineGC = CreatePen(PS_SOLID,0,GRID_COLOR);
        SelectPen(mapDC,mapLineGC);

        for (latmin = latB*60.0; latmin < (latT*60.0+gridMinY); latmin += gridMinY) {
          ilat = (latmin-(latmin%gridMinY));
          if (ilat <= 90*60 && ilat >= -90*60) {
            y0 = (latT-ilat/60.0) / (latT-latB) * MapWinH;
            if (y0 > GRID_BORDER && y0 < MapWinH-GRID_BORDER) {
              RECT purc={0,0,0,0};

              MoveToEx(mapDC,0,y0,NULL);
              LineTo  (mapDC,MapWinW,y0);

              ch=(ilat<0)? 'S' : 'N';
              if (ilat%60)
                   sprintf(text,"%c%d?r\n %2d' ", ch, (int)abs(ilat/60), (int)abs(ilat%60));
              else 
				  //sprintf(text,"%c%d?, ch, (int)abs(ilat/60));
					sprintf(text,"%c%d", ch, (int)abs(ilat/60));	// ISKANG

              DrawText(mapDC,text,strlen(text),&purc, DT_CALCRECT);
              purc.top    = y0 - purc.bottom/2;
              purc.bottom = y0 + purc.bottom/2;
              purc.left   = MapWinW-purc.right;
              purc.right  = MapWinW;
              DrawText(mapDC,text,strlen(text),&purc, DT_CENTER);
            }
          }
        }

        for (lonmin = lonL*60.0; lonmin < lonR*60.0+gridMinX; lonmin += gridMinX) {
          ilon = (lonmin-(lonmin%gridMinX));
          tlon = ilon/60.0;
          if (tlon>180.0) ilon -= 360*60;
          if (tlon<0.0) tlon +=lonShift;  /* move -180 span to +180 range */
          x0 = (tlon-lonL) / (lonR-lonL) * MapWinW;
          if (x0 > GRID_BORDER && x0 < MapWinW-GRID_BORDER) {
            RECT purc={0,0,0,0};

            MoveToEx(mapDC,x0,0,NULL);
            LineTo  (mapDC,x0,MapWinH);

            ch=(ilon<0)? 'W' : 'E';
            if (ilon%60)
                 sprintf(text,"%c%d?2d'", ch, (int)abs(ilon/60), (int)abs(ilon%60));
            else 
				//sprintf(text,"%c%d?, ch, (int)abs(ilon/60));
				sprintf(text,"%c%d", ch, (int)abs(ilon/60));	//ISKANG

            DrawText(mapDC,text,strlen(text),&purc, DT_CALCRECT);
            purc.top    = MapWinH - purc.bottom;
            purc.bottom = MapWinH;
            purc.left   = x0-purc.right/2;
            purc.right  = x0+purc.right/2;
            DrawText(mapDC,text,strlen(text),&purc, DT_CENTER);
          }
//printf("x=%d, W=%d, g=%d\n", x0, MapWinW, gridMinX);
        }

        SelectFont(mapDC,oldfont);
      }

// Clean up then save map for faster access
      DeleteObject(mapLineGC);
      SelectPen(mapDC,hpen);

      if (mapSaveHbm) DeleteObject(mapSaveHbm);
      haveBitBlt = (NULL != (mapSaveHbm = CaptureWindow(hwnd, MapWinW, MapWinH)));
    }

// Nothing has changed so just restore last drawn map
    else {
      hBmpOld=SelectObject(mapmemDC, mapSaveHbm);
      haveBitBlt = BitBlt(mapDC, 0, 0, MapWinW, MapWinH, mapmemDC, 0, 0, SRCCOPY);
      SelectObject(mapmemDC, hBmpOld);
    }

  }

  else {
    int map_lx, map_ty, map_win_lx, map_win_ty;
    int map_w,  map_h,  map_win_w,  map_win_h, map_ox, map_rx;

    /* Draw BMP world map */

    usingWVS = haveBitBlt = FALSE;
    mapFindRadius *= 2.0;               /* Yeah, the BMP world map is that coarse */
    if (mapFindRadius < 30.0) mapFindRadius = 30.0;

//  Find the left side offsets
    map_ox = map_lx = (map_cx - bm.bmWidth  / ((mapMpy+1)*2));
    if (map_lx < 0) {
       map_win_lx = (-map_lx * (mapMpy+1) * MapWinW) / bm.bmWidth;
       map_lx = 0;
    }
    else map_win_lx = 0;

//  Find the top offsets
    map_ty = (map_cy - bm.bmHeight / ((mapMpy+1)*2));
    if (map_ty < 0) {
       map_win_ty = (-map_ty * (mapMpy+1) * MapWinH) / bm.bmHeight;
       map_ty = 0;
    }
    else map_win_ty = 0;

//  Find the right side of the image
    map_rx = map_w = bm.bmWidth  / (mapMpy+1);
    if ((map_lx + map_w) > bm.bmWidth)  {
       map_w = bm.bmWidth - map_lx;
       map_win_w = (map_w * (mapMpy+1) * MapWinW) / bm.bmWidth;
    }
    else {
       map_win_w = MapWinW;
       map_rx = 0;
    }

//  Find the bottom of the image
    map_h = bm.bmHeight / (mapMpy+1);
    if ((map_ty + map_h) > bm.bmHeight) {
       map_h = bm.bmHeight - map_ty;
       map_win_h = (map_h * (mapMpy+1) * MapWinH) / bm.bmHeight;
    }
    else map_win_h = MapWinH;

    hBmpOld=SelectObject(mapmemDC, hBitmap);

    SetBkColor(  mapDC, RGB(255,255,255)); // Background is WHITE
    SetTextColor(mapDC, MAP_COLOR       ); // Black dots are GREY

//  If there is a hole on the left, fill it in with the right side of the bitmap.
    if (map_win_lx)
       StretchBlt(mapDC, 0, map_win_ty, map_win_lx, map_win_h, mapmemDC,
           bm.bmWidth+map_ox, map_ty, (-map_ox), map_h, SRCCOPY);

//  Show the image centered on the point
    StretchBlt(mapDC, map_win_lx, map_win_ty, map_win_w, map_win_h,
            mapmemDC, map_lx,     map_ty,     map_w,     map_h, SRCCOPY);

//  If there is a hole on the right, fill it in with the left side of the bitmap.
    if (map_rx)
       StretchBlt(mapDC, map_win_w, map_win_ty, MapWinW-map_win_w, map_win_h, mapmemDC,
           0, map_ty, (map_rx-map_w), map_h, SRCCOPY);

    SelectObject(mapmemDC, hBmpOld);
  }
}

/* -----------------10/29/97 5:51PM------------------
   Draw a dot on the map. In this case we draw a square
   2 units wide and high.  At the highest zoom factor,
   this leaves a little daylight between integer Lat/Lons.
 --------------------------------------------------*/
void DrawMapDot( HDC mapDC, HWND hwnd, LPARAM point) {
int x1, y1, halfx, halfy, relx, rely, isTide;

   isTide = point & 1;
   cvt_lonlat_pt_2_map( &x1, &y1, point & 0xfffffffe );

   halfx = halfy = (mapMpy < 6)? 2 : 4;
   if ((x1 > -halfx) && (x1 < halfx)) x1 = halfx;// Make all points JUST visible,
   if ((y1 > -halfy) && (y1 < halfy)) y1 = halfy;// .. ALL visible
   if ((x1 > MapWinW-halfx) && (x1 < MapWinW+halfx)) x1 = MapWinW-halfx;
   if ((y1 > MapWinH-halfy) && (y1 < MapWinH+halfy)) x1 = MapWinH-halfy;

   if ((x1 >= halfx) && (x1 <= (MapWinW-halfx))  && // Only display points in view
       (y1 >= halfy) && (y1 <= (MapWinH-halfy)) ) {
     if (!isTide) {
       x1-=halfx;
       y1-=halfy;
     }
     XFillRectangle (mapDC, (isTide)? fgmapdot_color : fgcurdot_color, x1-halfx, y1-halfy, halfx*2, halfy*2);
   }
}

/*-----------------7/3/2005 7:10PM------------------
 * Close map popup
 * --------------------------------------------------*/
void closeMapPopup() {
  if (hwndStaPopup) {
    DestroyWindow(hwndStaPopup);
    hwndStaPopup=NULL;
  }
}

/*-----------------7/3/2005 7:10PM------------------
 * Create map popup
 * --------------------------------------------------*/
void makeMapPopup(HWND hwnd, int wX, int wY, char * text) {
  PAINTSTRUCT ps;
  RECT        purc = {0,0,0,0};
  HFONT       oldfont;
  int         cxscreen, startX, startY;

  closeMapPopup();
// Open window with dummy position and size to get device context
  hwndStaPopup = CreateWindow("STATIC", "", WS_POPUP|WS_VISIBLE,//|WS_BORDER,
                              10,10,250,50, hwnd, NULL, NULL, NULL);

  BeginPaint(hwndStaPopup,&ps);
  oldfont = SelectFont(ps.hdc,GetStockObject(ANSI_VAR_FONT));
// Get extents for this text
  DrawText(ps.hdc, text, strlen(text), &purc, DT_CALCRECT);
  purc.right  += POPUP_BORDER*2;
  purc.bottom += POPUP_BORDER*2;

  cxscreen = GetSystemMetrics(SM_CXSCREEN);
  if (cxscreen > (wX + purc.right))
       startX = wX;
  else startX = wX - purc.right;
  if (0 < (wY - purc.bottom))
       startY = wY - purc.bottom;
  else startY = wY;
// Now make window the right size and in the right place
  MoveWindow(hwndStaPopup,startX,startY,purc.right,purc.bottom,FALSE);
  FillRect( ps.hdc, &purc, GetStockObject(WHITE_BRUSH));
  FrameRect(ps.hdc, &purc, GetStockObject(BLACK_BRUSH));
  purc.left   += POPUP_BORDER;
  purc.top    += POPUP_BORDER;
  purc.right  -= POPUP_BORDER;
  purc.bottom -= POPUP_BORDER;
  DrawText(ps.hdc,text,strlen(text),&purc, DT_WORDBREAK | DT_CENTER | DT_VCENTER);
  SelectFont(ps.hdc,oldfont);
  EndPaint(hwndStaPopup,&ps);
}
/* -----------------10/29/97 5:53PM------------------
   Create a window for the map.
 --------------------------------------------------*/
//void CreateMapWindow(HWND hwndParent, double lon, double lat, int zoom) {
//int cxscreen, cyscreen, MapWinX, MapWinY;
//
//   hold_map_window = TRUE;
//   CheckWVS();
//   lonC = lon;
//   latC = lat;
//   mapMpy = zoom;
//
//   hBitmap = LoadBitmap(g_hinst, szMapName);
//   GetObject(hBitmap, sizeof(BITMAP), &bm);
//
//   cxscreen = GetSystemMetrics(SM_CXSCREEN);
//   cyscreen = GetSystemMetrics(SM_CYSCREEN);
//   if ((WinDimMap.left+8   >= 0) &&
//       (WinDimMap.right  < cxscreen) &&
//       (WinDimMap.top+24    >= 0) &&
//       (WinDimMap.bottom < cyscreen) &&
//       (WinDimMap.right > WinDimMap.left) &&
//       (WinDimMap.bottom > WinDimMap.top) &&
//       (WinDimMap.left|WinDimMap.right|WinDimMap.top|WinDimMap.bottom) ) {
//      MapWinX = WinDimMap.left;
//      MapWinY = WinDimMap.top;
//      MapWinW = WinDimMap.right  - WinDimMap.left - 8;
//      MapWinH = WinDimMap.bottom - WinDimMap.top - 24;
//   }
//   else {
//      MapWinW = bm.bmWidth;
//      MapWinH = bm.bmHeight;
//      MapWinX = CW_USEDEFAULT;
//      MapWinY = CW_USEDEFAULT;
//   }
//
//   wcm.lpfnWndProc   = TidesMapProc;
//   wcm.hIcon         = LoadIcon(g_hinst, "WXTide");
//   wcm.hCursor       = LoadCursor(NULL, IDC_CROSS);
//   wcm.hInstance     = g_hinst;
//   wcm.lpszClassName = szMapName;
//   wcm.style         = CS_BYTEALIGNWINDOW;// |CS_DBLCLKS;
//   wcm.lpszMenuName  = NULL;
//   wcm.cbClsExtra    = 0;
//   wcm.cbWndExtra    = 0;
//   wcm.hbrBackground = GetStockObject(WHITE_BRUSH);
//
//   RegisterClass (&wcm);
//   hwndMap = CreateWindow(
//       szMapName,
//       "Station map LCLICK:Zoom In+Nearest  RCLICK:Zoom Out",
//       WS_CHILD |WS_POPUPWINDOW |WS_CAPTION |WS_SIZEBOX,
//       MapWinX, MapWinY,
////       CW_USEDEFAULT, // X coord - let windows decide
////       CW_USEDEFAULT, // Y coord - let windows decide
//       MapWinW+ 8,    // width
//       MapWinH+24,    // height
//       hwndMain,//NULL,//       hwndParent,    // hwndParent, who owns us
//       NULL,          // No menu
//       g_hinst,       // Handle of this instance of the program
//       NULL           // No additional arguments
//       );
//
//// Display the window
//   ShowWindow(hwndMap, SW_SHOW);
//   UpdateWindow(hwndMap);
//   hold_map_window = FALSE;
//   SetForegroundWindow(hwndParent);
//}

/*-----------------7/5/2005 1:49PM------------------
 * Send nearest station request
 * --------------------------------------------------*/
void sendStationRequest(long lParam) {
  int X, Y;
  X = (LOWORD(lParam)) - PixelPerBAMX*2;
  Y = (HIWORD(lParam)) - PixelPerBAMY*2;

  closeMapPopup();
  PostMessage(hwndLoc, WM_COMMAND, IDM_MAP_ASK_NEAR, cvt_map_2_lonlat_pt(X, Y));
  return;
}

/*-----------------7/5/2005 2:42PM------------------
 * Send focus back to station list
 * --------------------------------------------------*/
void focusStationList() {
  SetFocus(hwndLoc);
  PostMessage(hwndLoc,WM_NEXTDLGCTL, (WPARAM)GetDlgItem(hwndLoc, IDL_STATION), -1);
}

/* -----------------10/31/97 6:27PM------------------
   Message processing for the map.
 --------------------------------------------------*/
//LRESULT CALLBACK TidesMapProc(HWND hwnd,UINT msg,WPARAM wParam,LPARAM lParam)
//{
//PAINTSTRUCT ps;
//static HDC mapDC, mapmemDC;
//static int num_pnts=0, hoverX, hoverY;
//static long int *pmap_list = NULL, mouseloc;
//
//int X, Y;
//float lat, lon;
//
//
////   SetFocus(hwndLoc);
//   switch(msg) {
//     case WM_CREATE:
////          mapMpy = 0;
////          map_cx = bm.bmWidth  / 2;
////          map_cy = bm.bmHeight / 2;
//          cvt_lonlat_2_map( &map_cx, &map_cy, lonC, latC);
//          SetFocus(hwndLoc);
//          return FALSE;
//
//     case WM_SIZE:
//          MapWinW=LOWORD(lParam);
//          MapWinH=HIWORD(lParam);
//          InvalidateRect(hwnd,NULL,TRUE);
//          if (!hold_map_window && save_windows) new_params = TRUE;
////          focusStationList();
//          return FALSE;
//
//     case WM_MOVE:
//          MapWinX=(int)(short int)LOWORD(lParam);
//          MapWinY=(int)(short int)HIWORD(lParam);
//          if (!hold_map_window && save_windows) new_params = TRUE;
////          focusStationList();
//          return FALSE;
//
//     case WM_PAINT:
//          BeginPaint(hwnd,&ps);
//          mapDC=GetDC(hwnd);
//          mapmemDC=CreateCompatibleDC(mapDC);
//
//          DrawMap( mapDC, mapmemDC, hwnd );
//
//          if (pmap_list != NULL) {
//            num_pnts = -pmap_list[0];
//            while (num_pnts-- > 0)
//               DrawMapDot( mapDC, hwnd, (LPARAM)pmap_list[num_pnts+1]);
//          }
//          DeleteDC(mapmemDC);
//          ReleaseDC(hwnd, mapDC);
//          EndPaint(hwnd,&ps);
//          if (hold_map_window) SetFocus(hwndLoc);
////          SetFocus(hwndLoc);
//          return FALSE;
//
//     case WM_CLOSE:
//          GetWindowRect(hwndMap,&WinDimMap);
//          pmap_list = NULL;
//          DeleteObject(hBitmap);
//          hwndMap = NULL;
//          PostMessage(hwndLoc, WM_COMMAND, IDM_MAP_CHANGE, 0L);
//          DestroyWindow(hwnd);
//          if (mapSaveHbm) {DeleteObject(mapSaveHbm); mapSaveHbm=NULL;}
//          closeMapPopup();
//          KillTimer(hwnd, 2);
//          return FALSE;
//
//     case WM_KEYUP:
//        switch((int)wParam) {
//          case VK_F1:
//            WinHelp(hwnd,HelpFileName,HELP_KEY,(LPARAM)((LPSTR)"Station Locator Map"));
//            return FALSE;
//// Commented out because if we activate Help, then press escape to exit Help, this code also triggers!
////          case VK_ESCAPE:
////            PostMessage(hwndLoc, WM_CLOSE, 0, 0);
////            return FALSE;
//        }
//        return FALSE;
//
//     case WM_RBUTTONDOWN:
//     case WM_LBUTTONDOWN:
//		 {
//          closeMapPopup();
////           int X, Y;
////           float lat, lon;
//			X = Y = 0;
//		  lat = lon = 0;
//
//          X = (LOWORD(lParam));
//          Y = (HIWORD(lParam));
//          cvt_map_2_lonlat(&lon, &lat, X, Y);
//          makeMapPopup(hwnd, X+MapWinX+2, Y+MapWinY-2, makeLonLatStr(lon, lat,0));
//		 }
//          return FALSE;
//
//     case WM_RBUTTONUP:
//          closeMapPopup();
//          if (wParam != MK_SHIFT) {
//            hoverX = hoverY = 0;
//            if (mapMpy > 0) {
//              mapMpy--;
////              new_params = TRUE;
//              if (!mapMpy) {
//                map_cx = bm.bmWidth  / 2;
//                map_cy = bm.bmHeight / 2;
//              }
//            }
//            InvalidateRect(hwnd,NULL,TRUE);
//            UpdateWindow(hwnd);
////            SetFocus(hwndLoc);
//          }
//          focusStationList();
//          return FALSE;
//
//     case WM_LBUTTONUP:
//          closeMapPopup();
//          if (wParam != MK_SHIFT) {
//            hoverX = hoverY = 0;
//            cvt_map_2_lonlat( &lonC, &latC, LOWORD(lParam), HIWORD(lParam));
//            cvt_lonlat_2_map( &map_cx, &map_cy, lonC, latC);
//            if (mapMpy < maxMpy) {
//              mapMpy++;
////              new_params = TRUE;
//            }
//
//            SetCursorPos(MapWinX+(MapWinW/2), MapWinY+(MapWinH/2)); // Center cursor
//            PostMessage(hwndLoc, WM_COMMAND, IDM_MAP_LBDOWN, MAKELONG(DEG2BAMS(latC*2.0)&0xfffe, DEG2BAMS(lonC)));
//            InvalidateRect(hwnd,NULL,TRUE);
//            UpdateWindow(hwnd);
////            SetFocus(hwndLoc);
//          }
//          focusStationList();
//          return FALSE;
//
//
//     case WM_TIMER:
//          sendStationRequest(mouseloc);
//          KillTimer(hwnd, 2);
//          return FALSE;;
//
//
//     case WM_MOUSEMOVE:
////          SetFocus(hwndLoc);
//          if (map_cues)
//          {
//            int X, Y;
//            X = (LOWORD(lParam)) - PixelPerBAMX*2;
//            Y = (HIWORD(lParam)) - PixelPerBAMY*2;
//            if (abs(hoverX-X) > HOVER_SIZE_X ||
//                abs(hoverY-Y) > HOVER_SIZE_Y   ) {
//
//              hoverX = X;
//              hoverY = Y;
//              mouseloc = lParam;
//              closeMapPopup();
//              KillTimer(hwnd, 2);
//              if (map_cues==CUE_HOVER)
//                SetTimer(hwnd,2,(UINT)HOVER_DELAY,NULL); // Delayed station request
//              else
//                sendStationRequest(lParam);      // Immediate station request
//            }
//            return FALSE;
//          }
//          break;
//
//
//     case WM_COMMAND:
//       switch(wParam) {
//         case IDM_MAP_PAINT_DOT:
//            pmap_list = (long int *)lParam;
//            break;
//
//         case IDM_MAP_HAVE_NEAR:
//         {
//            IDX_entry   *pIDX;
//            static char text[256];
//
////            SetFocus(hwndLoc);
//            closeMapPopup();
//            if (lParam) {
//              int staX, staY;
//              KillTimer(hwnd, 2);
//              pIDX = (IDX_entry *)lParam;
//              if (pIDX) cvt_lonlat_pt_2_map(&staX, &staY, MAKELONG((DEG2BAMS(pIDX->IDX_lat*2.0)&0xfffe), DEG2BAMS(pIDX->IDX_lon)));
//              staX += MapWinX+4;
//              staY += MapWinY-4;
//              sprintf(text, "%s (%c)", pIDX->IDX_station_name, pIDX->IDX_type);
//
//              makeMapPopup(hwnd, staX, staY, text);
//              if (map_cues==CUE_HOVER) focusStationList();
//              else SetFocus(hwnd);
//            }
//            return FALSE;;
//         }
//       }
//       InvalidateRect(hwnd,NULL,FALSE);
//       UpdateWindow(hwnd);
//       focusStationList();
//       return FALSE;
//   }
//   return DefWindowProc(hwnd,msg,wParam,lParam);
//}