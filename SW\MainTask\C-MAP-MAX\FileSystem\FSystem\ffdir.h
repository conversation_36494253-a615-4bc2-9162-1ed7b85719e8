/*##################################################################
  FILE    : FFDIR.H

  USE     : Filesystem directory defines.
  PROJECT : C-Map File System.

  AUTHOR  : MLP[24Aug95].  
  UPDATED : SW[17Dec97], SiS[030604].
  ##################################################################


*/



#ifndef __FFDIR__
	#define __FFDIR__


/*****************************************************************************
  #Include section.
 *****************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include <cmaptype.h>
#include <FILESYSP.H>




#ifdef __cplusplus
extern "C"
{
#endif


/*****************************************************************************
  Constants definition section.
 *****************************************************************************/

#define	FSTBLK_NOT_VALID		-1




/*****************************************************************************
  Types & Data Structure definition definition section.
 *****************************************************************************/


typedef struct 
{
	SWord	fltype;
	SWord	fstblk;
	SByte	flname[ FILENAMESIZE ];
	SWord	flsize;
	SWord	ffiller;//filler; //iskang, compile error!
} FS_DIRENTRY;



#ifdef USE_SIZE_OF_AT_COMPILE_TIME
	#if sizeof( FS_DIRENTRY ) != sizeof (VolumeHeader)
		#error "Sizeof FS_DIRENTRY != Sizeof VolumeHeader"
	#endif
#endif



typedef struct 
{
	FS_DIRENTRY			buffer[ MAX_BLOCK / sizeof( FS_DIRENTRY ) ];
	FS_DIRENTRY			*dentry;
	struct DeviceNew	*device;
	SWord				dhfstblk;
	SWord				dhcurblk;
	SWord				dhcurpos;
} FS_DIRHANDLE;



typedef struct 
{
	struct DeviceNew	*device;
	unsigned char		buffer[ MAX_BLOCK ];
	FS_DIRHANDLE		handle ;
	SWord				curblk;
	SWord				curpos;
	SWord				nbytes;
	SWord				blkidx;
	Word				m_wSTTAGSectionId;
	Bool				m_bSTTAGSectionSts;

} FS_FILE ;



/* 
 * File Descriptor (for file direct access).	
 */
typedef struct
{
	SWord Cks;
	SWord Num;
	SWord Type;
	struct DeviceNew *Device;

} FileDescr;





/*****************************************************************************
  Interface Functions prototypes.
 *****************************************************************************/

/*	
 * Makes some initializations to read the directory of the Device dh
 *	and verifies d is properly formatted.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_OpenDir ( struct DeviceNew *d, FS_DIRHANDLE *dh ) ;

/*
 * Reads the next entry of the directory dh.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_ReadDir ( FS_DIRHANDLE *dh ) ;

/*
 * 	Closes the Dir. Dummy function for next CDOS versions. 
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_CloseDir ( FS_DIRHANDLE *dh );

/*
 * Return a pointer to a FS_FILE structure which contains 
 * informations on the file. 
 */
PRE_EXPORT_H extern FS_FILE* IN_EXPORT_H FS_FileInfo ( SWord fh );

/*
 * Return the number of files on the current device.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_FilesPresent ( FS_DIRHANDLE *gdh );


/* File Descriptor routines.	 */
/* #ifdef USE_FILE_DESCR		*/

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FD_GetNameOfFile ( FileDescr *fd, SByte *Name, FS_DIRHANDLE *gDh );

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FD_OpenR_NoIntegrityTest ( FileDescr *pfd );

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FD_OpenR ( FileDescr *pfd );

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FD_CloseR ( SWord ff );

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FD_Delete ( FileDescr *pfd );

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FD_GetFileDescr ( SWord fh, FileDescr *pFd );

/*
 * Get the descriptor of the current file in the dir dh.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FD_GetCurrFileDescr ( FS_DIRHANDLE *dh, FileDescr *pFd );

/* #endif / * #ifdef USE_FILE_DESCR */



#ifdef __cplusplus
}
#endif




/*****************************************************************************
  END of Code.
 *****************************************************************************/

#endif	/* #ifndef __FFDIR__ */


/*------------------------------------------------------------------------
 * $Log: /CplotTools/FileSystem/SourceFiles/H/FFDIR.H $
 * 
 * 6     5-10-00 13:11 Andrea
 * 
 * 5     3-10-00 18:52 Andrea
 * 
 * 4     29-09-00 15:39 Andrea
 * sizeof() is executed only if  USE_SIZE_OF_AT_COMPILE_TIME has been
 * defined  in compila.h module.
 * 
 * 3     31-01-00 12:07 Andrea
 * Added support for ARM7 compiler (ARMCC).
 * 
 * 2     23-09-99 13:19 Andrea
 * Moved the LOG to the end of the file.
 * 
 * 1     22-09-99 18:30 Andrea
 * 
 *-----------------------------------------------------------------------*/
