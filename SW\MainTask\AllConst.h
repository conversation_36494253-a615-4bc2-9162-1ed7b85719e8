/*...........................................................................*/
/*.                  File Name : ALLCONST.H                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.18                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __ALLCONST_H
#define  __ALLCONST_H

//=============================================================================
#ifdef  __PLASTIMO__
#define  LNG_CODE_SIZE               18
#define  LNG_CODE_NUL                -1
#define  LNG_CODE_ENG                 0
#define  LNG_CODE_FRA                 1
#define  LNG_CODE_ITA                 2
#define  LNG_CODE_SPA                 3
#define  LNG_CODE_GER                 4
#define  LNG_CODE_DUT                 5
#define  LNG_CODE_SWE                 6
#define  LNG_CODE_DAN                 7
#define  LNG_CODE_FIN                 8
#define  LNG_CODE_GRE                 9
#define  LNG_CODE_POR                10
#define  LNG_CODE_NOR                11
#define  LNG_CODE_CRO                12
#define  LNG_CODE_RUS                13
#define  LNG_CODE_CHI                14
#define  LNG_CODE_JAP                15
#define  LNG_CODE_POL                16
#define  LNG_CODE_KOR         (LNG_CODE_SIZE - 1)
#else
#define  LNG_CODE_SIZE               29
#define  LNG_CODE_NUL                -1
#define  LNG_CODE_ENG                 0
#define  LNG_CODE_FRA                 1
#define  LNG_CODE_ITA                 2
#define  LNG_CODE_SPA                 3
#define  LNG_CODE_GER                 4
#define  LNG_CODE_DUT                 5
#define  LNG_CODE_SWE                 6
#define  LNG_CODE_DAN                 7
#define  LNG_CODE_FIN                 8
#define  LNG_CODE_GRE                 9
#define  LNG_CODE_POR                10
#define  LNG_CODE_NOR                11
#define  LNG_CODE_CRO                12
#define  LNG_CODE_RUS                13
#define  LNG_CODE_CHI                14
#define  LNG_CODE_JAP                15
#define  LNG_CODE_POL                16
#define  LNG_CODE_TWN                17     // 봺쟢
#define  LNG_CODE_THA                18     // ?뒄
#define  LNG_CODE_MYA                19     // ！뇰쟞
#define  LNG_CODE_MAL                20     // 쟧쒪래?큑
#define  LNG_CODE_IND                21     // 램븸멇?큑
#define  LNG_CODE_TRK                22     // 휼퐈
#define  LNG_CODE_VTM                23     // 쩇?릕
#define  LNG_CODE_ARB                24     // 큑쐓닯
#define  LNG_CODE_PRS                25     // ?웏?큑닯
#define  LNG_CODE_BGL                26     // 쩦늝닯
#define  LNG_CODE_SHL                27     // Sinhala
#define  LNG_CODE_KOR         (LNG_CODE_SIZE - 1)
#endif
//-----------------------------------------------------------------------------
#define  CHI_UNI_CODE_ADD_VAL      0x40
#define  TWN_UNI_CODE_ADD_VAL      0x40
#define  JAP_UNI_CODE_ADD_VAL      0x50
//-----------------------------------------------------------------------------
#define  REDRAW_MODE_MENU             0
#define  REDRAW_MODE_ALL              1
//-----------------------------------------------------------------------------
#define  USING_MAP_CMAP               0
#define  USING_MAP_SAM                1
#define  C_MAP_ZOOM_SCALE            41
#define  NAVS_MAP_ZOOM_SCALE         25
//-----------------------------------------------------------------------------
#define  ENGINE_TOTAL_SIZE            2
#define  ENGINE_PORT                  0
#define  ENGINE_STARBOARD             1
//-----------------------------------------------------------------------------
#define  SLCTD_WIN_NO_EMPTY          -1
#define  SLCTD_WIN_NO_CHART           0
#define  SLCTD_WIN_NO_CHART2          1
#define  SLCTD_WIN_NO_SONAR           2
#define  SLCTD_WIN_NO_RADAR           3
#define  SLCTD_WIN_NO_VIDEO           4
#define  SLCTD_WIN_NO_HIGHWAY         5
#define  SLCTD_WIN_NO_GAUGE           6
#define  SLCTD_WIN_NO_LAST   (SLCTD_WIN_NO_GAUGE)
#define  SLCTD_WIN_WIDTH_NO_CHANGE -999
//-----------------------------------------------------------------------------
#define  ALPHA_BLEND_MIN              0
#define  ALPHA_BLEND_MAX            100
#define  ALPHA_BLEND_DFLT           100
#define  ALPHA_BLEND_STEP            10
//-----------------------------------------------------------------------------
#define  BRIGHT_MIN                  10
#define  BRIGHT_MAX                 100
#define  BRIGHT_DFLT                100
#define  BRIGHT_STEP                 10
//-----------------------------------------------------------------------------
#define  SPLIT_VERT_240_MIN          40
#define  SPLIT_VERT_240_MAX          60
#define  SPLIT_VERT_480_MIN          40
#define  SPLIT_VERT_480_MAX          60
#define  SPLIT_VERT_640_MIN          40
#define  SPLIT_VERT_640_MAX          60
#define  SPLIT_VERT_800_MIN          30
#define  SPLIT_VERT_800_MAX          70
#define  SPLIT_VERT_1024_MIN         30
#define  SPLIT_VERT_1024_MAX         70
#define  SPLIT_VERT_STEP              5

#define  SPLIT_HORI_240_MIN          40
#define  SPLIT_HORI_240_MAX          60
#define  SPLIT_HORI_480_MIN          40
#define  SPLIT_HORI_480_MAX          60
#define  SPLIT_HORI_640_MIN          40
#define  SPLIT_HORI_640_MAX          60
#define  SPLIT_HORI_800_MIN          40
#define  SPLIT_HORI_800_MAX          60
#define  SPLIT_HORI_1024_MIN         40
#define  SPLIT_HORI_1024_MAX         60
#define  SPLIT_HORI_STEP              5
//-----------------------------------------------------------------------------
#define  NAV_MODE_NONE                0
#define  NAV_MODE_WPT                 1
#define  NAV_MODE_ROUTE               2

#define  NAV_START_WPT_NO             0  // 、멤빨/엨? ??? ?퇬목톩 뮐? 떋닳
#define  NAV_CURSOR_WPT_NO            1  // Goto Cursor? 콩п톩 뜬찼 멜툃
//-----------------------------------------------------------------------------
#ifdef  __SAMYUNG__
        #define  WAYPNT_POINT_SIZE 9999
#else
        #define  WAYPNT_POINT_SIZE 5000
#endif
#define  WAYPNT_POINT_END_NO       9999
#define  WAYPNT_TEXT_LEN              8
#define  WAYPNT_DATA_START            2
#define  WAYPNT_ONE_BACK_SIZE        32
#define  WAYPNT_SIM_START_NO   (WAYPNT_POINT_SIZE - 20)
#define  WAYPNT_SIM_NAME_HEAD  "SIM-"
//-----------------------------------------------------------------------------
#define  ROUTE_ALL_SIZE             100
#define  ROUTE_PNT_SIZE_PER_ROUTE   100
#define  ROUTE_NAME_SIZE             12  // 11 + 1NULL
#define  ROUTE_NAV_DIR_FORWARD        0
#define  ROUTE_NAV_DIR_REVERSE        1
#define  ROUTE_SIM_ROUTE_NO  (ROUTE_ALL_SIZE - 1)
//-----------------------------------------------------------------------------
#define  KEY_BEEP_MODE_OFF            0
#define  KEY_BEEP_MODE_LOW            1
#define  KEY_BEEP_MODE_HIGH           2
//-----------------------------------------------------------------------------
#define  MODE_VAL_NULL               -1
#define  MODE_VAL_OFF                 0
#define  MODE_VAL_ON                  1
//-----------------------------------------------------------------------------
#define  UNIT_NULL                   -1
//-----------------------------------------------------------------------------
#define  DIST_UNIT_NM                 0   // 1 NM = 1.852    KM = 1.1507790 MI
#define  DIST_UNIT_MI                 1   // 1 MI = 1.609344 KM
#define  DIST_UNIT_KM                 2   // 1 KM = 0.621371 MI = 0.5399568 NM
//-----------------------------------------------------------------------------
#define  SPD_UNIT_KN                  0
#define  SPD_UNIT_MPH                 1
#define  SPD_UNIT_KPH                 2
//-----------------------------------------------------------------------------
#define  DPT_UNIT_FT                  0   // 1 FT = 0.30480 M
#define  DPT_UNIT_M                   1   // 1 M  = 3.28084 FT = 0.5468 FA
#define  DPT_UNIT_FA                  2   // 1 FA = 1.8288  M

#define  DEPTH_UNIT_MUL_100_FACTOR      100
#define  DEPTH_UNIT_FEET_MUL_100         30
#define  DEPTH_UNIT_METER_MUL_100       100
#define  DEPTH_UNIT_FATHOM_MUL_100      183
//#define  DEPTH_UNIT_FATHOM_I_MUL_100    164
//#define  DEPTH_UNIT_FATHOM_J_MUL_100    151
//#define  DEPTH_UNIT_BRASSN_MUL_100      182
//-----------------------------------------------------------------------------
#define  FUEL_UNIT_LITERS             0   // 1 liters = 0.264187 gallon
#define  FUEL_UNIT_USGAL              1   // 1 gallon = 3.785195 liters
#define  FUEL_UNIT_IMPGAL             2   // 1 Imperial gallon = 4.54609188 liters
//-----------------------------------------------------------------------------
#define  CMPS_UNIT_T                  0
#define  CMPS_UNIT_M                  1
//-----------------------------------------------------------------------------
#define  TMP_UNIT_F                   0   // F = C x 1.8 + 32
#define  TMP_UNIT_C                   1   // C = (F - 32) / 1.8
//-----------------------------------------------------------------------------
#define  WDIR_UNIT_APP                0
#define  WDIR_UNIT_TRUE               1   // $WIMWV,x.x,T/R,x.x,KM/NS,A/V*hh
//-----------------------------------------------------------------------------
#define  WSPD_UNIT_KN                 0
#define  WSPD_UNIT_MPH                1
#define  WSPD_UNIT_KPH                2
#define  WSPD_UNIT_BFT                3
//-----------------------------------------------------------------------------
#define  PRSS_UNIT_KPA                0   // 1kPa = 0.145038 psi
#define  PRSS_UNIT_PSI                1   // 1psi = 6.894757 kPa
//-----------------------------------------------------------------------------
#define  BARO_UNIT_INHG               0   // 1 mB=1/1000 Bar=0.029530 inHg
#define  BARO_UNIT_MMHG               1   // 1 mB=1/1000 Bar=0.750062 mmHg
#define  BARO_UNIT_MB                 2   // 1 mB=1 hPa (1hPa = 100 Pa)
//-----------------------------------------------------------------------------
#define  MENU_BACK_COLOR_BLUE         0
#define  MENU_BACK_COLOR_WHITE        1
//-----------------------------------------------------------------------------
#define  CHART_ROT_MODE_NORTH_UP      0
#define  CHART_ROT_MODE_HEAD_UP       1
#define  CHART_ROT_MODE_COURSE_UP     2
#define  CHART_ROT_MODE_TRUE_MOTION   3
//-----------------------------------------------------------------------------
#define  CHART_PAL_MODE_NORMAL        0
#define  CHART_PAL_MODE_NOAA          1
#define  CHART_PAL_MODE_SUNLIGHT      2
#define  CHART_PAL_MODE_NIGHT         3
//-----------------------------------------------------------------------------
#define  CHART_DATUM_NO_SIZE        216
//-----------------------------------------------------------------------------
#define  CHART_BOUNDARY_OFF           0
#define  CHART_BOUNDARY_ON            1
#define  CHART_BOUNDARY_AUTO          2
//-----------------------------------------------------------------------------
#define  CHART_ICON_SIZE_SMALL        0
#define  CHART_ICON_SIZE_LARGE        1
//-----------------------------------------------------------------------------
#define  CHART_LIGHTS_OFF             0
#define  CHART_LIGHTS_NO_SECTOR       1
#define  CHART_LIGHTS_ON              2
#define  CHART_LIGHTS_ANIMATED        3
//-----------------------------------------------------------------------------
#define  CHART_NAV_AIDS_INT           0
#define  CHART_NAV_AIDS_US            1
//-----------------------------------------------------------------------------
#define  CHART_PRJ_CRS_OFF            0
#define  CHART_PRJ_CRS_02_MIN         1
#define  CHART_PRJ_CRS_10_MIN         2
#define  CHART_PRJ_CRS_30_MIN         3
#define  CHART_PRJ_CRS_1_HOUR         4
#define  CHART_PRJ_CRS_2_HOUR         5
#define  CHART_PRJ_CRS_SHORT          6
#define  CHART_PRJ_CRS_MID            7
#define  CHART_PRJ_CRS_LONG           8
//-----------------------------------------------------------------------------
#define  CHART_CDI_SCALE_00_05        0
#define  CHART_CDI_SCALE_00_10        1
#define  CHART_CDI_SCALE_00_20        2
#define  CHART_CDI_SCALE_00_50        3
#define  CHART_CDI_SCALE_01_00        4
#define  CHART_CDI_SCALE_02_00        5
#define  CHART_CDI_SCALE_04_00        6
#define  CHART_CDI_SCALE_10_00        7
//-----------------------------------------------------------------------------
#define  CHART_BATH_SND_MIN_MIN       0
#define  CHART_BATH_SND_MIN_MAX   20000
#define  CHART_BATH_SND_MAX_MIN       0
#define  CHART_BATH_SND_MAX_MAX   20000

#define  CHART_SECURITY_ZONE_MIN      0
#define  CHART_SECURITY_ZONE_MAX    999
//-----------------------------------------------------------------------------
#define  CHART_MAP_SHIFT_MIN      -1855   // -9999   // -0.9999'
#define  CHART_MAP_SHIFT_MAX      +1855   // +9999   // +0.9999'
#define  CHART_MAP_SHIFT_FACTOR   10000   // / 10000
//-----------------------------------------------------------------------------
#define  SONAR_FREQ_MODE_200          0
#define  SONAR_FREQ_MODE_50           1
#define  SONAR_FREQ_MODE_MIX          2

#define  SONAR_PALT_MODE_BLACK        0
#define  SONAR_PALT_MODE_BLUE         1
#define  SONAR_PALT_MODE_WHITE        2
#define  SONAR_PALT_MODE_8_COLOR      3

#define  SONAR_PALT_MODE_CLR_1        0
#define  SONAR_PALT_MODE_CLR_2        1
#define  SONAR_PALT_MODE_CLR_3        2
#define  SONAR_PALT_MODE_CLR_4        3
#define  SONAR_PALT_MODE_CLR_5        4

#define  SONAR_BACK_MODE_BLACK        0
#define  SONAR_BACK_MODE_BLUE         1
#define  SONAR_BACK_MODE_WHITE        2

#define  SONAR_SCRLL_SPD_VRFAST       0
#define  SONAR_SCRLL_SPD_FAST         1
#define  SONAR_SCRLL_SPD_MEDIUM       2
#define  SONAR_SCRLL_SPD_SLOW         3
#define  SONAR_SCRLL_SPD_PAUSE        4

#define  SONAR_DGT_SIZE_SMALL         0
#define  SONAR_DGT_SIZE_MEDIUM        1
#define  SONAR_DGT_SIZE_LARGE         2

#define  SONAR_FISH_FLTR_SMALL        0
#define  SONAR_FISH_FLTR_MEDIUM       1
#define  SONAR_FISH_FLTR_LARGE        2

#define  SONAR_FISH_SENS_LOW          0
#define  SONAR_FISH_SENS_MEDIUM       1
#define  SONAR_FISH_SENS_HIGH         2

#define  SONAR_ADVN_NOISE_FLTR_OFF    0
#define  SONAR_ADVN_NOISE_FLTR_LOW    1
#define  SONAR_ADVN_NOISE_FLTR_MEDIUM 2
#define  SONAR_ADVN_NOISE_FLTR_HIGH   3

#define  SONAR_PULSE_PWR_TEST_MODE    0

#define  SONAR_ADVN_PULSE_LEN_AUTO    0
#define  SONAR_ADVN_PULSE_LEN_SHORT   1
#define  SONAR_ADVN_PULSE_LEN_MEDIUM  2
#define  SONAR_ADVN_PULSE_LEN_LONG    3
#define  SONAR_ADVN_PULSE_LEN_1       1
#define  SONAR_ADVN_PULSE_LEN_2       2
#define  SONAR_ADVN_PULSE_LEN_3       3
#define  SONAR_ADVN_PULSE_LEN_4       4
#define  SONAR_ADVN_PULSE_LEN_5       5

#define  SONAR_ADVN_PULSE_PWR_AUTO    0
#define  SONAR_ADVN_PULSE_PWR_LOW     1
#define  SONAR_ADVN_PULSE_PWR_MEDIUM  2
#define  SONAR_ADVN_PULSE_PWR_HIGH    3
#define  SONAR_ADVN_PULSE_PWR_1       1
#define  SONAR_ADVN_PULSE_PWR_2       2
#define  SONAR_ADVN_PULSE_PWR_3       3
#define  SONAR_ADVN_PULSE_PWR_4       4
#define  SONAR_ADVN_PULSE_PWR_5       5

#define  SONAR_TRANSDUCER_L_POWER     0       // 300W / 600W
#define  SONAR_TRANSDUCER_H_POWER     1       // 600W / 1 KW

#define  SONAR_LEVEL_SIZE            15       // 닯?뾵쫭 0 -- 15 (족 16 봢뎮)
#define  SONAR_COLOR_OFFSET        0x40
#define  SONAR_DRAW_MUL_FACTOR   (1 << 20)
#define  SONAR_SIZE_MUL_FACTOR   (1 << 18)

#define  SONAR_SPARE_LINE_SIZE       16       // (뜬찼=4,ㆁ뜬=4)
#define  SONAR_SPARE_LINE_POS_P       0       // (뜬찼=0)
#define  SONAR_SPARE_LINE_RAN_P       4       // (ㆁ뜬=4)
#define  SONAR_SPARE_LINE_LAT_P       8       // (LAT =8) by JYD(2015.02.09)
#define  SONAR_SPARE_LINE_LON_P      12       // (LON=12) by JYD(2015.02.09)
//-----------------------------------------------------------------------------
#define  RADAR_COLOR_OFFSET        0x60
#define  OVRLY_COLOR_OFFSET        0x70

#define  RADAR_NORM_TRP_COLOR_NO   0xff
#define  RADAR_OVER_TRP_COLOR_NO   0xff

#define  RADAR_PALT_MODE_BLACK        0
#define  RADAR_PALT_MODE_WHITE        1
#define  RADAR_PALT_MODE_GREEN        2
#define  RADAR_PALT_MODE_YELLOW       3
#define  RADAR_PALT_MODE_MONO         4

#define  RADAR_OVR_PALT_MODE_RED      0
#define  RADAR_OVR_PALT_MODE_ORANGE   1
#define  RADAR_OVR_PALT_MODE_MAGENTA  2
#define  RADAR_OVR_PALT_MODE_GREEN    3
#define  RADAR_OVR_PALT_MODE_BLACK    4

#define  RADAR_DGT_SIZE_SMALL         0
#define  RADAR_DGT_SIZE_MEDIUM        1
#define  RADAR_DGT_SIZE_LARGE         2

#define  RADAR_ROT_MODE_NRT_UP        0
#define  RADAR_ROT_MODE_HDG_UP        1
#define  RADAR_ROT_MODE_CRS_UP        2

#define  RADAR_MOT_MODE_RELATIVE      0
#define  RADAR_MOT_MODE_TRUE          1

#define  RADAR_INTRF_FLTR_OFF         0
#define  RADAR_INTRF_FLTR_LOW         1
#define  RADAR_INTRF_FLTR_MEDIUM      2
#define  RADAR_INTRF_FLTR_HIGH        3

//#define  RADAR_ZERO_BRG_MIN       -1800
//#define  RADAR_ZERO_BRG_MAX       +1800
//#define  RADAR_ZERO_BRG_DFLT          0

#define  RADAR_ZERO_BRG_MIN           0
#define  RADAR_ZERO_BRG_MAX        +359
#define  RADAR_ZERO_BRG_DFLT          0

#define  RADAR_ZERO_RNG_MIN           0
#define  RADAR_ZERO_RNG_MAX         255
#define  RADAR_ZERO_RNG_DFLT          0


#define RADAR_GAIN_LEVEL_MIN          0
#define RADAR_GAIN_LEVEL_MAX        100
#define RADAR_GAIN_LEVEL_DFLT        50

#define RADAR_SEACLUTTER_LEVEL_MIN          0
#define RADAR_SEACLUTTER_LEVEL_MAX        100
#define RADAR_SEACLUTTER_LEVEL_DFLT        50

#define RADAR_RAINCLUTTER_LEVEL_MIN          0
#define RADAR_RAINCLUTTER_LEVEL_MAX        100
#define RADAR_RAINCLUTTER_LEVEL_DFLT        50

#define RADAR_THREASHOLD_LEVEL_MIN          0
#define RADAR_THREASHOLD_LEVEL_MAX        100
#define RADAR_THREASHOLD_LEVEL_DFLT        50

#define RADAR_CTHREASHOLD_LEVEL_MIN         0
#define RADAR_CTHREASHOLD_LEVEL_MAX        100
#define RADAR_CTHREASHOLD_LEVEL_DFLT        50

#define RADAR_VRMEBL_ANGLE_MIN              0
#define RADAR_VRMEBL_ANGLE_MAX            360
#define RADAR_VRMEBL_1_ANGLE_DFLT          30
#define RADAR_VRMEBL_2_ANGLE_DFLT          60

#define RADAR_VRMEBL_DISTANCE_MIN           0
#define RADAR_VRMEBL_DISTANCE_MAX     2400000
#define RADAR_VRMEBL_DISTANCE_1_DFLT   100000
#define RADAR_VRMEBL_DISTANCE_2_DFLT   200000

#define RADAR_GUARDZONE_CIRCLE		0
#define RADAR_GUARDZONE_SECTOR		1

#define RADAR_STATE_OFF			0
#define RADAR_STATE_STANBY			1
#define RADAR_STATE_TRANSMIT		2

#define RADAR_GAIN_MODE_AUTO		0
#define RADAR_GAIN_MODE_MANUAL		1

#define RADAR_SEACLUTTER_HARBOR	0
#define RADAR_SEACLUTTER_OFFSHORE  1
#define RADAR_SEACLUTTER_MANUAL	2

#define RADAR_POSITION_CENTER      0
#define RADAR_POSITION_LOOKAHEAD   1
#define RADAR_POSITION_OFFSET      2

#define RADAR_ORIENTATION_NORTHUP    0
#define RADAR_ORIENTATION_HEADUP     1
#define RADAR_ORIENTATION_COURSEUP   2

#define RADAR_BEARING_RELATIVE     0
#define RADAR_BEARING_TRUE         1

#define RADAR_GUARDZONE_ALARM_ENTER 0
#define RADAR_GUARDZONE_ALARM_EXIT  1
#define RADAR_GUARDZONE_ALARM_BOTH  2
//-----------------------------------------------------------------------------
#define  TRACK_DATA_SIZE             10
#define  TRACK_RECORD_OFF             0
#define  TRACK_RECORD_1               1
#define  TRACK_RECORD_2               2
#define  TRACK_RECORD_3               3
#define  TRACK_RECORD_4               4
#define  TRACK_RECORD_5               5
#define  TRACK_RECORD_6               6
#define  TRACK_RECORD_7               7
#define  TRACK_RECORD_8               8
#define  TRACK_RECORD_9               9
#define  TRACK_RECORD_0              10

#define  TRACK_COLOR_MIN              0
#define  TRACK_COLOR_MAX             15
#define  TRACK_COLOR_DFLT             0

#define  TRACK_THICK_MODE_THIN        0
#define  TRACK_THICK_MODE_THICK       1

#define  TRACK_SAVE_MODE_TIME         0
#define  TRACK_SAVE_MODE_DIST         1

#define  TRACK_SAVE_TIME_01_SEC       0
#define  TRACK_SAVE_TIME_05_SEC       1
#define  TRACK_SAVE_TIME_10_SEC       2
#define  TRACK_SAVE_TIME_30_SEC       3
#define  TRACK_SAVE_TIME_60_SEC       4
#define  TRACK_SAVE_TIME_02_MIN       5
#define  TRACK_SAVE_TIME_05_MIN       6
#define  TRACK_SAVE_TIME_10_MIN       7
#define  TRACK_SAVE_TIME_20_MIN       8
#define  TRACK_SAVE_TIME_30_MIN       9
#define  TRACK_SAVE_TIME_60_MIN      10

#define  TRACK_SAVE_DIST_00_01        0
#define  TRACK_SAVE_DIST_00_05        1
#define  TRACK_SAVE_DIST_00_10        2
#define  TRACK_SAVE_DIST_00_50        3
#define  TRACK_SAVE_DIST_01_00        4
#define  TRACK_SAVE_DIST_02_00        5
#define  TRACK_SAVE_DIST_05_00        6
#define  TRACK_SAVE_DIST_10_00        7

#define  TRACK_TRIP_DIST_MIN          0
#define  TRACK_TRIP_DIST_MAX  1999999999
#define  TRACK_TRIP_DIST_DFLT         0
#define  TRACK_TRIP_DIST_FACT     10000

#define  TRACK_TOTAL_DIST_MIN         0
#define  TRACK_TOTAL_DIST_MAX 1999999999
#define  TRACK_TOTAL_DIST_DFLT        0
//-----------------------------------------------------------------------------
#define  MEMORY_DEVICE_SDCD           0
#define  MEMORY_DEVICE_USB            1

#define  MEMORY_SORT_NAME             0
#define  MEMORY_SORT_TYPE             1
#define  MEMORY_SORT_TIME             2
//-----------------------------------------------------------------------------
#define  AIS_FLT_DST_MIN              1
#define  AIS_FLT_DST_MAX            999
#define  AIS_FLT_DST_DFLT            25

#define  AIS_FLT_SPD_MIN              0
#define  AIS_FLT_SPD_MAX            999
#define  AIS_FLT_SPD_DFLT             0

#define  AIS_OPT_PRJ_CRS_OFF          0
#define  AIS_OPT_PRJ_CRS_02_MIN       1
#define  AIS_OPT_PRJ_CRS_05_MIN       2
#define  AIS_OPT_PRJ_CRS_10_MIN       3
#define  AIS_OPT_PRJ_CRS_20_MIN       4
#define  AIS_OPT_PRJ_CRS_30_MIN       5
#define  AIS_OPT_PRJ_CRS_1_HOUR       6

#define  AIS_OPT_RANGE_RING_0         0
#define  AIS_OPT_RANGE_RING_1         1
#define  AIS_OPT_RANGE_RING_2         2
#define  AIS_OPT_RANGE_RING_3         3
#define  AIS_OPT_RANGE_RING_4         4
#define  AIS_OPT_RANGE_RING_5         5

#define  AIS_SCRN_SAVE_TIME_OFF       0
#define  AIS_SCRN_SAVE_TIME_01        1
#define  AIS_SCRN_SAVE_TIME_03        2
#define  AIS_SCRN_SAVE_TIME_05        3
#define  AIS_SCRN_SAVE_TIME_10        4
#define  AIS_SCRN_SAVE_TIME_15        5
#define  AIS_SCRN_SAVE_TIME_30        6
#define  AIS_SCRN_SAVE_TIME_60        7
//-----------------------------------------------------------------------------
#define  DEEP_ALARM_MIN               0
#define  DEEP_ALARM_MAX             379
#define  DEEP_ALARM_DFLT            100

#define  SHALLOW_ALARM_MIN            0
#define  SHALLOW_ALARM_MAX          379
#define  SHALLOW_ALARM_DFLT          10

#define  TEMP_ALARM_MIN               0
#define  TEMP_ALARM_MAX             999
#define  TEMP_ALARM_DFLT            100

#define  TRATE_ALARM_MIN               0
#define  TRATE_ALARM_MAX             999
#define  TRATE_ALARM_DFLT            100

#define  LBATT_ALARM_MIN             100
#define  LBATT_ALARM_MAX             300
#define  LBATT_ALARM_DFLT            120

#define  ARRV_ALARM_MIN                1
#define  ARRV_ALARM_MAX              999
#define  ARRV_ALARM_DFLT               5

#define  ANCH_ALARM_MIN                0
#define  ANCH_ALARM_MAX              999
#define  ANCH_ALARM_DFLT               2

#define  DNGR_ALARM_MIN                0
#define  DNGR_ALARM_MAX              999
#define  DNGR_ALARM_DFLT             100

#define  TCPA_ALARM_LMT_02_MIN          0
#define  TCPA_ALARM_LMT_05_MIN          1
#define  TCPA_ALARM_LMT_10_MIN          2
#define  TCPA_ALARM_LMT_20_MIN          3
#define  TCPA_ALARM_LMT_30_MIN          4
#define  TCPA_ALARM_LMT_1_HOUR          5

#define  CPA_ALARM_MIN                  1
#define  CPA_ALARM_MAX                 50
#define  CPA_ALARM_DFLT                10

#define  PRDS_ALARM_MIN                 1
#define  PRDS_ALARM_MAX               100
#define  PRDS_ALARM_DFLT               10
//-----------------------------------------------------------------------------
#define  SIMUL_TYPE_NORMAL              0
#define  SIMUL_TYPE_DEMO                1

#define  SIMUL_ROUTE_SIZE               7
#define  SIMUL_ROUTE_LORIENT            0
#define  SIMUL_ROUTE_LAROCHELLE         1
#define  SIMUL_ROUTE_CORSICA            2
#define  SIMUL_ROUTE_UK                 3
#define  SIMUL_ROUTE_GERMANY            4
#define  SIMUL_ROUTE_ITALY              5
#define  SIMUL_ROUTE_GREECE             6

#define  WPT_DSP_MODE_HIDE_ALL          0
#define  WPT_DSP_MODE_SHOW_ALL_I        1         // Icon
#define  WPT_DSP_MODE_SHOW_ALL_I_N      2         // Icon+Name
#define  WPT_DSP_MODE_SELECTED          3

#define  GPS_SRC_INTERNAL               0
#define  GPS_SRC_PORT1                  1
#define  GPS_SRC_PORT2                  2
#define  GPS_SRC_NMEA2000               3

#define  DGPS_SRC_NONE                  0
#define  DGPS_SRC_SBAS                  1

#define  GPS_STATIC_NAV_MIN             0
#define  GPS_STATIC_NAV_MAX           999

#define  DGPS_FREQ_VAL_MIN           2800
#define  DGPS_FREQ_VAL_MAX           3300
#define  DGPS_FREQ_VAL_DFLT          3100

#define  DGPS_BAUD_050_BPS              0
#define  DGPS_BAUD_100_BPS              1
#define  DGPS_BAUD_200_BPS              2

#define  GPS_SPD_FILTER_MIN             0
#define  GPS_SPD_FILTER_MAX            60
#define  GPS_SPD_FILTER_DFLT            5

#define  GPS_CRS_FILTER_MIN             0
#define  GPS_CRS_FILTER_MAX            60

#define  LAT_LON_DPS_2                  0
#define  LAT_LON_DPS_3                  1

#define  MAGNETIC_VAR_MIN            -999
#define  MAGNETIC_VAR_MAX            +999

#define  GPS_SHIP_SPD_MODE_SLOW         0
#define  GPS_SHIP_SPD_MODE_MID          1
#define  GPS_SHIP_SPD_MODE_FAST         2

#define  VIDEO_FORMAT_NTSC              0
#define  VIDEO_FORMAT_PAL               1
#define  VIDEO_FORMAT_SECAM             2

#define  VIDEO_BRIGHT_MIN               0
#define  VIDEO_BRIGHT_MAX              20
#define  VIDEO_BRIGHT_INC               1
#define  VIDEO_BRIGHT_DFT              10

#define  VIDEO_CNTRST_MIN               0
#define  VIDEO_CNTRST_MAX               7
#define  VIDEO_CNTRST_INC               1
#define  VIDEO_CNTRST_DFT               3

#define  VIDEO_SATURA_MIN               0
#define  VIDEO_SATURA_MAX              20
#define  VIDEO_SATURA_INC               1
#define  VIDEO_SATURA_DFT              10

#define  VIDEO_HUEHUE_MIN               0
#define  VIDEO_HUEHUE_MAX              20
#define  VIDEO_HUEHUE_INC               1
#define  VIDEO_HUEHUE_DFT              10

#define  NMEA_SPD_4800                  0
#define  NMEA_SPD_9600                  1
#define  NMEA_SPD_38400                 2

#define  CALI_SPEED_VAL_MIN             0
#define  CALI_SPEED_VAL_MAX          9999
#define  CALI_SPEED_VAL_DFLT            0
#define  CALI_SPEED_VAL_UNIT SPD_UNIT_KPH

#define  CALI_SPEED_FLT_MIN             0
#define  CALI_SPEED_FLT_MAX            30
#define  CALI_SPEED_FLT_DFLT            0

#define  CALI_TEMP_VAL_MIN_C            0
#define  CALI_TEMP_VAL_MAX_C         1000
#define  CALI_TEMP_VAL_MIN_F          320
#define  CALI_TEMP_VAL_MAX_F         2120
#define  CALI_TEMP_VAL_DFLT_C           0
#define  CALI_TEMP_VAL_DFLT_F         320
#define  CALI_TEMP_VAL_UNIT    TMP_UNIT_F

#define  CALI_TEMP_FLT_MIN              0
#define  CALI_TEMP_FLT_MAX            100
#define  CALI_TEMP_FLT_DFLT             0

#define  CALI_KEEL_OFF_MIN           -999
#define  CALI_KEEL_OFF_MAX           +999
#define  CALI_KEEL_OFF_DFLT             0

#define  CALI_SPD_RNG_MIN               5
#define  CALI_SPD_RNG_MAX             200
#define  CALI_SPD_RNG_DFLT            100

#define  TIME_LOC_OFF_MIN           -1300
#define  TIME_LOC_OFF_MAX           +1300
#define  TIME_LOC_OFF_DFLT              0

#define  TIME_TIME_FORM_24              0
#define  TIME_TIME_FORM_12              1

#define  TIME_DATE_FORM_DD_MMM_YY       0
#define  TIME_DATE_FORM_MMM_DD_YY       1
#define  TIME_DATE_FORM_DD_MM_YY        2
#define  TIME_DATE_FORM_MM_DD_YY        3
#define  TIME_DATE_FORM_YYYY_MM_DD      4
//-----------------------------------------------------------------------------
#define  SONAR_SPLIT_MODE_NONE          0
#define  SONAR_SPLIT_MODE_ZOOM          1
#define  SONAR_SPLIT_MODE_FULL_ZOOM     2
#define  SONAR_SPLIT_MODE_BOTTOM        3
#define  SONAR_SPLIT_MODE_50_200        4
#define  SONAR_SPLIT_MODE_A_SCOPE       5
#define  SONAR_SPLIT_MODE_SIZE   (SONAR_SPLIT_MODE_A_SCOPE + 1)

#define  SONAR_SPLIT_RATE_MIN          20
#define  SONAR_SPLIT_RATE_MAX          80
#define  SONAR_SPLIT_RATE_DFLT         50

#define  SONAR_GAIN_MODE_MANUAL         0
#define  SONAR_GAIN_MODE_AUTO           1

#define  SONAR_GAIN_VAL_MIN             0
#define  SONAR_GAIN_VAL_MAX            20
#define  SONAR_GAIN_VAL_DFLT           10

#define  SONAR_TRHD_VAL_MIN             0
#define  SONAR_TRHD_VAL_MAX            75
#define  SONAR_TRHD_VAL_DFLT           20

#define  SONAR_RANGE_MODE_MANUAL        0
#define  SONAR_RANGE_MODE_AUTO          1
//-----------------------------------------------------------------------------
#ifdef _SAMYUNG_THEME_
#define  TITLE_NORMAL_CID       CID_INACTIVE_WND_FRAME
#define  TITLE_NTEXT_CID        CID_INACTIVE_WND_TITLE
#define  TITLE_SELECTED_CID     CID_ACTIVE_WND_FRAME
#define  TITLE_STEXT_CID        CID_ACTIVE_WND_TITLE
#else
#define  TITLE_NORMAL_CID       CID_LIGHTGRAY   // CID_INACTIVE_TITLE
#define  TITLE_NTEXT_CID        CID_DARKGRAY    // CID_LOWLIGHT
#define  TITLE_SELECTED_CID     CID_LIGHTGRAY   // CID_ACTIVE_TITLE
#define  TITLE_STEXT_CID        CID_BLACK       // CID_HIGH_TEXT
#endif

#define  SELECTED_MENU_COLOR_ID       CID_LIGHTGREEN

#ifdef _SAMYUNG_THEME_
#define  SELECTED_WIN_FRAME_COLOR_ID  CID_ACTIVE_WND_FRAME
#else
#define  SELECTED_WIN_FRAME_COLOR_ID  CID_MENU_GRAD_SLCT
#endif

#define  NIGHT_MODE_BACK_COLOR_ID     CID_LIGHTGRAY
#define  NIGHT_MODE_BACK_ALPHA_ID     CID_LIGHTGRAY_ALPHA
#define  DAY_MODE_BACK_COLOR_ID       CID_WHITE
#define  DAY_MODE_BACK_ALPHA_ID       CID_WHITE_ALPHA

#define  MY_MENU_FILL_MONO              0
#define  MY_MENU_FILL_GRADATION         1

#define  MY_MENU_MONO_SLCT_COLOR    (CID_MENU_GRAD_SLCT)
#define  MY_MENU_GRAD_SLCT_COLOR    (CID_MENU_GRAD_SLCT)

#define  DAY_GRADATION_R            0x00    // 0x9f
#define  DAY_GRADATION_G            0x00    // 0x9f
#define  DAY_GRADATION_B            0xff    // 0xff

#define  NGT_GRADATION_R            0x80
#define  NGT_GRADATION_G            0x80
#define  NGT_GRADATION_B            0x80

#define  LAST_GRADATION_PERCENT       50
//-----------------------------------------------------------------------------
#define  ALARM_MARK_ARRIVAL            0
#define  ALARM_MARK_ANCHOR             1
#define  ALARM_MARK_XTE                2
#define  ALARM_MARK_DANGER             3
#define  ALARM_MARK_TOOSHALLOW         4
#define  ALARM_MARK_TOODEEP            5
#define  ALARM_MARK_FISH               6
#define  ALARM_MARK_TEMP               7
#define  ALARM_MARK_TEMPRATE           8
#define  ALARM_MARK_LOWBATT            9
#define  ALARM_MARK_LOWFUEL           10
#define  ALARM_MARK_LOSSDGPS          11
#define  ALARM_MARK_LOSSGPS           12
#define  ALARM_MARK_LOSSAIS           13
#define  ALARM_MARK_DANGERVESSEL      14
//-----------------------------------------------------------------------------
#define  WAYPNT_MARK_NO               48
#define  WAYPNT_MARK_MIN               0
#define  WAYPNT_MARK_MAX  (WAYPNT_MARK_NO - 1)
#define  WAYPNT_MARK_DFLT              0
#define  WAYPNT_MARK_USER_START        0             // 0 -- 11 똞빨 츉錤 у?
#define  WAYPNT_MARK_USER_LAST        11             // 0 -- 11 똞빨 츉錤 у?
#define  WAYPNT_MARK_LINE_START       44             // у땇윞떋 ?퇬
#define  WAYPNT_MARK_LINE_CNNCT       45             // у땇윞떋 킻뎘

#define  WAYPNT_COLOR_MIN              0
#define  WAYPNT_COLOR_MAX             15
#ifdef  __PLASTIMO__
   #define  WAYPNT_COLOR_DFLT          0
#else
   #define  WAYPNT_COLOR_DFLT         12
#endif
#define  WAYPNT_COLOR_GOTO_CURSOR     12

#define  WAYPNT_MARK_DEFAULT        0x00
#define  WAYPNT_MARK_GOTO_CURSOR    0x01
#define  WAYPNT_MARK_DANGER         0x2E
#define  WAYPNT_MARK_MOB            0x2F

#define  WAYPNT_MARK_SHIP_X         0x3A
#define  WAYPNT_MARK_SHIP_Y         0x3B
#define  WAYPNT_MARK_SHIP_Z         0x3C

#define  WAYPNT_MARK_CURSOR_X       0x3D
#define  WAYPNT_MARK_CURSOR_Y       0x3E
#define  WAYPNT_MARK_CURSOR_Z       0x3F

#define  WAYPNT_DSP_MODE_OFF           0
#define  WAYPNT_DSP_MODE_ICON          1
#define  WAYPNT_DSP_MODE_ALL           2

#define  WAYPNT_DATE_UNKNOWN           0
//-----------------------------------------------------------------------------
#define  GPS_FIX_STATUS_LOST           0
#define  GPS_FIX_STATUS_GPS            1
#define  GPS_FIX_STATUS_DGPS           2
#define  GPS_FIX_STATUS_WAAS           3
#define  GPS_FIX_STATUS_EGNOS          4
#define  GPS_FIX_STATUS_MSAS           5
#define  GPS_FIX_STATUS_SBAS           6
#define  GPS_FIX_STATUS_SIM            7
//-----------------------------------------------------------------------------
#define  YEAR_VAL_MIN               2000
#define  YEAR_VAL_MAX               2099
//-----------------------------------------------------------------------------
#define  WIND_REF_APP                  0
#define  WIND_REF_TRUE                 1
#define  WIND_REF_UNKNOWN              2
//-----------------------------------------------------------------------------
#define  SPD_VAL_MIN                   0
#define  SPD_VAL_MAX                9999

#define  CRS_VAL_MIN                   0
#define  CRS_VAL_MAX                3599

#define  COG_VAL_MIN                   0
#define  COG_VAL_MAX                3599

#define  DPT_VAL_MIN                   0
#define  DPT_VAL_MAX               99999

#define  TMP_VAL_MIN                -500
#define  TMP_VAL_MAX               +9999

#define  DST_VAL_MIN            -9999.99
#define  DST_VAL_MAX            +9999.99

#define  PRSS_VAL_MIN            -218000
#define  PRSS_VAL_MAX            +218000

#define  BARO_VAL_MIN           -2180000
#define  BARO_VAL_MAX           +2180000

#define  SYS_VOLT_MIN                100
#define  SYS_VOLT_MAX                360
#define  SYS_VOLT_OFF                 80

#define  WATER_TMP_MIN        TMP_VAL_MIN
#define  WATER_TMP_MAX        TMP_VAL_MAX

#define  AIR_TMP_MIN          TMP_VAL_MIN
#define  AIR_TMP_MAX          TMP_VAL_MAX

#define  ENGINE_TMP_MIN       TMP_VAL_MIN
#define  ENGINE_TMP_MAX       TMP_VAL_MAX

#define  WIND_SPD_MIN         SPD_VAL_MIN
#define  WIND_SPD_MAX         SPD_VAL_MAX

#define  TOTAL_DIST_MIN              0.0
#define  TOTAL_DIST_MAX         999999.9

#define  TRIP_DIST_MIN               0.0
#define  TRIP_DIST_MAX          999999.9

#define  WATER_PRSS_MIN     PRSS_VAL_MIN
#define  WATER_PRSS_MAX     PRSS_VAL_MAX

#define  TRIM_VAL_MIN               -124
#define  TRIM_VAL_MAX               +124

#define  ENGINE_HOUR_MIN               0
#define  ENGINE_HOUR_MAX         9999999
#define  ENGINE_VOLT_MIN           -3300
#define  ENGINE_VOLT_MAX           +3300
#define  ENGINE_TEMP_MIN     TMP_VAL_MIN
#define  ENGINE_TEMP_MAX     TMP_VAL_MAX
#define  ENGINE_RPM_MIN                0
#define  ENGINE_RPM_MAX            20000

#define  FUEL_ECONO_MIN           -32764
#define  FUEL_ECONO_MAX           +32764
#define  FUEL_USED_MIN                 0
//#define  FUEL_USED_MAX             65532
#define  FUEL_USED_MAX         429496000
#define  FUEL_REMAIN_MIN               0
//#define  FUEL_REMAIN_MAX           65532
#define  FUEL_REMAIN_MAX       429496000
#define  FUEL_FLOW_MIN            -32764
#define  FUEL_FLOW_MAX            +32764
#define  FUEL_LEVEL_MIN            -1500
#define  FUEL_LEVEL_MAX            +1500

#define  OIL_LEVEL_MIN             -1500
#define  OIL_LEVEL_MAX             +1500
#define  OIL_TEMP_MIN        TMP_VAL_MIN
#define  OIL_TEMP_MAX        TMP_VAL_MAX
#define  OIL_PRSS_MIN       PRSS_VAL_MIN
#define  OIL_PRSS_MAX       PRSS_VAL_MAX

#define  BRG_NEXT_MIN        COG_VAL_MIN
#define  BRG_NEXT_MAX        COG_VAL_MAX

#define  CRS_TO_NEXT_MIN     COG_VAL_MIN
#define  CRS_TO_NEXT_MAX     COG_VAL_MAX

#define  DST_TO_NEXT_MIN     DST_VAL_MIN
#define  DST_TO_NEXT_MAX     DST_VAL_MAX

#define  DST_TO_DEST_MIN     DST_VAL_MIN
#define  DST_TO_DEST_MAX     DST_VAL_MAX

#define  STEER_VAL_MIN      -COG_VAL_MAX
#define  STEER_VAL_MAX      +COG_VAL_MAX

#define  TIME_TO_NEXT_MIN              0
#define  TIME_TO_NEXT_MAX           9959

#define  TIME_TO_DEST_MIN              0
#define  TIME_TO_DEST_MAX           9959

#define  VMG_TO_NEXT_MIN     -SPD_VAL_MAX
#define  VMG_TO_NEXT_MAX     +SPD_VAL_MAX

#define  XTE_TO_NEXT_MIN     -DST_VAL_MAX
#define  XTE_TO_NEXT_MAX     +DST_VAL_MAX

#define  BARO_VAL_MIN            -2180000
#define  BARO_VAL_MAX            +2180000

#define  WIND_DIR_MIN         COG_VAL_MIN
#define  WIND_DIR_MAX         COG_VAL_MAX

#define  WIND_SPD_MIN         SPD_VAL_MIN
#define  WIND_SPD_MAX         SPD_VAL_MAX

#define  SONAR_COV_MIN        DPT_VAL_MIN
#define  SONAR_COV_MAX        DPT_VAL_MAX

#define  VPW_SPD_MIN         -SPD_VAL_MAX
#define  VPW_SPD_MAX         +SPD_VAL_MAX
//-----------------------------------------------------------------------------
#define  SYS_TIME_UNKNOWN           2014
#define  SYS_VOLT_UNKNOWN              0

#define  GRID_LAT_VAL_UNKNOWN  324000000
#define  GRID_LON_VAL_UNKNOWN 1296000000
#define  SPD_VAL_UNKNOWN           10000  //   0.0 --  999.9
#define  COG_VAL_UNKNOWN            3600  //   0.0 --  359.9
#define  HDG_VAL_UNKNOWN            3600  //   0.0 --  359.9
#define  DPT_VAL_UNKNOWN          100000  //   0.0 -- 9999.9
#define  TMP_VAL_UNKNOWN          +10000  // -50.0 --  999.9
#define  DST_VAL_UNKNOWN        +10000.0
#define  PRSS_VAL_UNKNOWN        +218001

#define  WATER_TMP_UNKNOWN    TMP_VAL_UNKNOWN
#define  AIR_TMP_UNKNOWN      TMP_VAL_UNKNOWN
#define  ENGINE_TMP_UNKNOWN   TMP_VAL_UNKNOWN

#define  WIND_SPD_UNKNOWN     SPD_VAL_UNKNOWN

#define  TOTAL_DIST_UNKNOWN    1000000.0
#define  TRIP_DIST_UNKNOWN     1000000.0

#define  WATER_PRSS_UNKNOWN   PRSS_VAL_UNKNOWN
#define  TRIM_VAL_UNKNOWN           +200

#define  ENGINE_HOUR_UNKNOWN    10000000
#define  ENGINE_VOLT_UNKNOWN       +3333
#define  ENGINE_TEMP_UNKNOWN  TMP_VAL_UNKNOWN
#define  ENGINE_RPM_UNKNOWN        20001

#define  FUEL_ECONO_UNKNOWN       +32767
#define  FUEL_USED_UNKNOWN         65535
#define  FUEL_REMAIN_UNKNOWN       65535
#define  FUEL_FLOW_UNKNOWN        +32767
#define  FUEL_LEVEL_UNKNOWN        +1501
#define  FUEL_CAPACITY_UNKNOWN        -1

#define  OIL_LEVEL_UNKNOWN         +1501
#define  OIL_TEMP_UNKNOWN  TMP_VAL_UNKNOWN
#define  OIL_PRSS_UNKNOWN  PRSS_VAL_UNKNOWN
#define  OIL_CAPACITY_UNKNOWN         -1

#define  BRG_NEXT_UNKNOWN     COG_VAL_UNKNOWN
#define  CTS_TO_NEXT_UNKNOWN  COG_VAL_UNKNOWN
#define  DST_TO_NEXT_UNKNOWN  DST_VAL_UNKNOWN
#define  DST_TO_DEST_UNKNOWN  DST_VAL_UNKNOWN

#define  STEER_VAL_UNKNOWN    COG_VAL_UNKNOWN
#define  TIME_TO_NEXT_UNKNOWN            9999
#define  TIME_TO_DEST_UNKNOWN            9999
#define  VMG_TO_NEXT_UNKNOWN  SPD_VAL_UNKNOWN
#define  XTE_TO_NEXT_UNKNOWN  DST_VAL_UNKNOWN

#define  BARO_VAL_UNKNOWN            +2180001
#define  WIND_DIR_UNKNOWN     COG_VAL_UNKNOWN
#define  WIND_SPD_UNKNOWN     SPD_VAL_UNKNOWN

#define  SONAR_COV_UNKNOWN    DPT_VAL_UNKNOWN

#define  PERCENT_VAL_UNKNOWN         125

#define  VPW_SPD_UNKNOWN      SPD_VAL_UNKNOWN
//-----------------------------------------------------------------------------
#define  DOP_TYPE_PDOP                 0
#define  DOP_TYPE_HDOP                 1
#define  DOP_VALUE_NULL              999
//-----------------------------------------------------------------------------
#define  DRAW_MODE_ERASE               0
#define  DRAW_MODE_DRAW                1
//-----------------------------------------------------------------------------
#define  CHART_CLR_ID_BLACK            0
#define  CHART_CLR_ID_DARK_BLUE        1

#define  CHART_CLR_ID_LIGHT_BLUE       9
#define  CHART_CLR_ID_DARK_GRAY        8
#define  CHART_CLR_ID_LIGHT_GRAY       7
#define  CHART_CLR_ID_DARK_GREEN       2
#define  CHART_CLR_ID_LIGHT_GREEN     10
#define  CHART_CLR_ID_DARK_RED         4
#define  CHART_CLR_ID_LIGHT_RED       12
#define  CHART_CLR_ID_YELLOW          14
#define  CHART_CLR_ID_WHITE           15
//-----------------------------------------------------------------------------
#define  CHART_WPT_LINE_ID      (CHART_CLR_ID_BLACK)
#define  CHART_XTE_LINE_ID      (CHART_CLR_ID_LIGHT_RED)
#define  CHART_ROUTE_LINE_ID    (CHART_CLR_ID_BLACK)
#define  CHART_VIEW_ROUTE_ID    (CHART_CLR_ID_DARK_GRAY)
//-----------------------------------------------------------------------------
#define  SPC_DEG_CHAR             0x0001
#define  SPC_D_C_CHAR             0x0002
#define  SPC_D_F_CHAR             0x0003
#define  SPC_D_T_CHAR             0x0004
#define  SPC_D_R_CHAR             0x0005
#define  SPC_D_M_CHAR             0x0006
#define  SPC_N_R_CHAR             0x0007
#define  SPC_N_L_CHAR             0x0008
#define  SPC_N_U_CHAR             0x0009
#define  SPC_N_D_CHAR             0x000a
#define  SPC_D_S_CHAR             0x000b
#define  SPC_BOX_CHAR             0x000d
#define  SPC_CSR_CHAR             0x000f
#define  SPC_M_R_CHAR             0x0010
#define  SPC_M_L_CHAR             0x0011
//-----------------------------------------------------------------------------
#define  ASC_CHR_CR                 0x0d
#define  ASC_CHR_LF                 0x0a
//-----------------------------------------------------------------------------

//   #define  SCRN_SHOT_MAX_SIZE      16
//   #define  SCRN_SHOT_IMG_SIZE   (1536 * 1024)

#define  SCRN_SHOT_MAX_SIZE       2
#define  SCRN_SHOT_IMG_SIZE   (2560 * 1024)
//-----------------------------------------------------------------------------
#define  CHART_ZOOM_MODE_RATIO          0
#define  CHART_ZOOM_MODE_RANGE          1
#define  CHART_ZOOM_MODE_RUN            0
//-----------------------------------------------------------------------------
#define  NAVTEX_ONE_MSG_SIZE   (16 * 1024)
#define  NAVTEX_MSGS_PER_BLOCK          1        // 1=16/16 , 8=128/16
//-----------------------------------------------------------------------------
#define  DEVICE_SERIAL_X_SIZE          16        // 15+NULL
#define  DEVICE_SERIAL_Y_SIZE          16        // 15+NULL
//=============================================================================
#define  TXT_CURSOR_STR_MSG      "CURSOR"
//=============================================================================
#define  PAGE_WND_EXIT_CODE_ESC         -1
#define  PAGE_WND_EXIT_CODE_DISPLAY     -2
#define  PAGE_WND_EXIT_CODE_TOP_MENU    -3
#define  PAGE_WND_EXIT_CODE_PAGE        -4
#define  PAGE_WND_ESC_KEY_TEST      0xA5A5

#define  PAGE_WND_EXIT_CODE_MOB         -5
//=============================================================================
#define  DATA_SRC_PRIORITY_NULL          0
#define  DATA_SRC_PRIORITY_0183          1
#define  DATA_SRC_PRIORITY_2000          2
#define  DATA_LOST_TIME_SECONDS         10
//=============================================================================
#define  ADC_REF_VALUE_MIN             310
#define  ADC_REF_VALUE_MAX             350
#define  ADC_REF_VALUE_DFLT            330
//=============================================================================
#define  LCD_4_3_INCH_FRQ_DVD_MIN       50
#define  LCD_4_3_INCH_FRQ_DVD_MAX       62
#define  LCD_4_3_INCH_FRQ_DVD_DFLT      57

#define  LCD_5_0_INCH_FRQ_DVD_MIN       10
#define  LCD_5_0_INCH_FRQ_DVD_MAX       17
#define  LCD_5_0_INCH_FRQ_DVD_DFLT      12

#define  LCD_5_6_INCH_FRQ_DVD_MIN       22
#define  LCD_5_6_INCH_FRQ_DVD_MAX       29
#define  LCD_5_6_INCH_FRQ_DVD_DFLT      27
//=============================================================================
#define  ADC_LCD_ADJUST_VALUE       0x55aa
//=============================================================================
#define  SONAR_HWFACTORY_ADJUST_MODE     0
//===========================================================================
#define  SONAR_HW_PCB_VER_OLD            0
#define  SONAR_HW_PCB_VER_NEW            1
//===========================================================================
#define  SONAR_GAIN_COMPILE_PCB_OLD      0
#define  SONAR_GAIN_COMPILE_PCB_NEW      1

//  #define  SONAR_GAIN_COMPILE_PCB_RUN (SONAR_GAIN_COMPILE_PCB_OLD)
#define  SONAR_GAIN_COMPILE_PCB_RUN (SONAR_GAIN_COMPILE_PCB_NEW)
//===========================================================================
#define  SONAR_SPICA_FPGA_USE_MODE_640_DOTS  0
#define  SONAR_SPICA_FPGA_USE_MODE_480_DOTS  1
#define  SONAR_SPICA_FPGA_USE_MODE_RUN    (SONAR_SPICA_FPGA_USE_MODE_640_DOTS)
//#define  SONAR_SPICA_FPGA_USE_MODE_RUN    (SONAR_SPICA_FPGA_USE_MODE_480_DOTS)
//=============================================================================
#define  SONAR_SPICA_GAIN_TABLE_SIZE         2
#define  SONAR_SPICA_GAIN_TABLE_VAL0         0
#define  SONAR_SPICA_GAIN_TABLE_VAL1         1
//=============================================================================
#define  TASK_START_PRIO         30
#define  TASK_1_PRIO        (TASK_START_PRIO + 1)
#define  TASK_2_PRIO        (TASK_START_PRIO + 2)
#define  TASK_3_PRIO        (TASK_START_PRIO + 3)

#define  TASK_START_ID      (TASK_START_PRIO)
#define  TASK_1_ID          (TASK_1_PRIO)
#define  TASK_2_ID          (TASK_2_PRIO)
#define  TASK_3_ID          (TASK_3_PRIO)
//=============================================================================
#define  COLOR_BAR_MENU_FILL_SIZE          4
//=============================================================================
#define  HDG_LINE_DRAW_MODE_TRUE           0
#define  HDG_LINE_DRAW_MODE_MAG            1
#if defined(__SAMYUNG__) || defined(_ICON_GME_)
    #define  HDG_LINE_DRAW_MODE_RUN  (HDG_LINE_DRAW_MODE_TRUE)
#else
    #define  HDG_LINE_DRAW_MODE_RUN  (HDG_LINE_DRAW_MODE_MAG)
#endif

#define  OWN_SHIP_POS_CENTER_ONLY_MODE     0
#define  OWN_SHIP_POS_CENTER_BOTTOM_MODE   1
#define  OWN_SHIP_POS_CENTER_BOTTOM_RUN   (OWN_SHIP_POS_CENTER_BOTTOM_MODE)

#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
    #define  RAW_TIDE_DATA_USE_PROGM_MEMORY    1
#endif
//=============================================================================
#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
    #define  OLD_LAT_MASK          0x07ffffff
    #define  OLD_LON_MASK          0x0fffffff
    #define  OLD_MRK_MASK          0xf8000000
    #define  OLD_COL_MASK          0xf0000000

    #define  SAM_DATA_DIR_NAME           "\\SAMDATA"

    #define  SAM_OLD_DATA_HEAD_X         0xdc
    #define  SAM_OLD_DATA_HEAD_Y         0xfe

    #define  SAM_TRACK_DATA_HEAD_STR     "SAM TRACK V01.00"
    #define  SAM_WAYPT_DATA_HEAD_STR     "SAM WAYPT V01.00"
    #define  SAM_ROUTE_DATA_HEAD_STR     "SAM ROUTE V01.00"
    #define  SAM_ALL_DATA_HEAD_LENTH     16

    #define  SAM_DATA_CNVT_MODE_NONE      0
    #define  SAM_DATA_CNVT_MODE_W84       1      // Korea to W84
    #define  SAM_DATA_CNVT_MODE_KOR       2      // W84   to Korea

    #define  SAM_DATA_FILE_TYPE_OLD       0
    #define  SAM_DATA_FILE_TYPE_NEW       1

    #define   SAM_DATA_FILE_FORMAT_CMAP   0
    #define   SAM_DATA_FILE_FORMAT_SAM    1

  #if !defined(_ASSEMBLER_)
      typedef  struct {
               char   vFileName[128];
               int    nPointCnt;         // the number of points
               int    nFileType;         // SAM_DATA_FILE_TYPE_OLD / SAM_DATA_FILE_TYPE_NEW
               int    nYear;
               int    nMonth;
               int    nDay;
               int    nHour;
               int    nMinute;
               char   vTimeDate[32];
              }SAMDATAFILE;
  #endif

  #if !defined(_ASSEMBLER_)
      typedef  struct {
               char  vPortName[32];
               int   nMercLat;
               int   nMercLon;
               float rDistant;
               float rCourse;
               int   nIdxRecNo;
              }xSAMTIDENAME;
  #endif
#endif
//=============================================================================
#define  AIS_SIZE_SMALL          0
#define  AIS_SIZE_LARGE          1
//=============================================================================
#define  CURSOR_SPD_SLOW         0
#define  CURSOR_SPD_FAST         1
//=============================================================================
#define  ONWA_MARK_USED_EVT      0
#define  ONWA_MARK_USED_WPT      1
#define  ONWA_MARK_USED_RUN      (ONWA_MARK_USED_WPT)
//===========================================================================
#define  NEWPEC_NORM_LINE_OFFSET   0x20
#define  NEWPEC_NORM_POLY_OFFSET   0x50
#define  NEWPEC_NORM_MARK_OFFSET   0x60
#define  NEWPEC_EXPD_POLY_OFFSET   0x70
#define  NEWPEC_EXPD_LINE_OFFSET   0x80
//===========================================================================
#define  AIS_SART_SYMBOL_NO          66
#define  AIS_BASE_SYMBOL_NO          67
#define  AIS_ATON_SYMBOL_NO          68
#define  RDR_ARPA_SYMBOL_NO          70
//===========================================================================
#define  SAM_MAP_TYPE_MODE_OLD        0
#define  SAM_MAP_TYPE_MODE_NPC        1
//===========================================================================
#define  SONAR_SPLIT_MODE_BOTTOM_FULL_DSP
//===========================================================================

//===========================================================================
#define  AIS_TRCK_LIST_SIZE          10
#define  AIS_TRCK_POINT_SIZE       1800      // (6 * 60 * 10) ,  5 hours

#define  AIS_TRCK_TIME_10_SEC         0
#define  AIS_TRCK_TIME_20_SEC         1
#define  AIS_TRCK_TIME_30_SEC         2
#define  AIS_TRCK_TIME_60_SEC         3
#define  AIS_TRCK_TIME_02_MIN         4
#define  AIS_TRCK_TIME_05_MIN         5
#define  AIS_TRCK_TIME_10_MIN         6
#define  AIS_TRCK_TIME_20_MIN         7
#define  AIS_TRCK_TIME_30_MIN         8
#define  AIS_TRCK_TIME_60_MIN         9
//===========================================================================
#define  __N430_OTHERS_ENABLED__
#define  __N430_COMPASS_ENABLED__
//===========================================================================
//#define  __CMAP_ONLY_USE_MODE__
//===========================================================================
//#define  ___GLOBAL___
//===========================================================================

//#define EN_SIM_MSG					    	// VDM Message 12,14(Safety Relation) Simultaion 
//#define EN_SIM_SART_TEST			  	// SART Test 
//#define EN_SCRN_CAPTURE				    // Screen Capture
//#define EN_SIM_PORT_TEST
//#define EN_SIM_EXT_PORT_TEST			// External Port Test
//#define EN_ADD_VIRTUAL_TARGET
//#define EN_HW_MODEM_TEST				// Hardware Modem Test
#define SUPPORT_BAM_ALERT				// IEC62923-2(BAM) applied
#define ENABLE_FACTORY_RESET			// Enable Factory Reset	
#define ENABLE_POST						// Enable Power On Self Test
#define SUPPORT_SHOW_TEST_SART

// Simulation Mode
#define SIM_MODE_NONE				0
#define SIM_MODE_EXT_NOR_LOG		1
#define SIM_MODE_EXT_TIME_LOG		2

#define MAX_ACC_SIM_MODE			512

#define TGT_SHIP_NAME_DSP_MODE_HAN	0
#define TGT_SHIP_NAME_DSP_MODE_ENG  1

#ifdef ENABLE_POST
#define MAX_ST_TRANS_TEST_CNT		30
#define MAX_ST_MKD_ROM_TEST_CNT		3
#define MAX_ST_MKD_RAM_TEST_CNT     3
#define MAX_ST_MKD_COM1_TEST_CNT    3
#define MAX_ST_MKD_COM3_TEST_CNT    3

#define ST_STAT_NONE_STR			"---------------"
#define ST_STAT_TEST_SUCCESS_STR    "OK..."
#define ST_STAT_TEST_FAIL_STR       "FAILED..."
#define ST_STAT_TEST_CANCEL_STR     "CANCELED..."
#define ST_STAT_TESTING_STR         "TESTING"
#define ST_STAT_NONE				" "
#endif

#ifdef EN_SCRN_CAPTURE
#define SCRN_CAP_FMT_PNG				0
#define SCRN_CAP_FMT_BMP				1
#define SCRN_CAP_FMT					SCRN_CAP_FMT_BMP
#endif

#define SCRN_W							800
#define SCRN_H							480

#define TOP_BG_X_POS					0
#define TOP_BG_Y_POS					0
#define TOP_BG_W						SCRN_W
#define TOP_BG_H						44

#define TITLE_BG_X_POS					10
#define TITLE_BG_Y_POS					6
#define TITLE_BG_W						384
#define TITLE_BG_H						37

#define TITLE_TXT_X_POS					57

#define RIGHT_BG_X_POS					600
#define RIGHT_BG_Y_POS					44
#define RIGHT_BG_W						200
#define RIGHT_BG_H						390

#define BTN_START_X_POS					0
#define BTN_W							200
#define BTN_H							46
#define BTN_AREA_W						(BTN_W*4)
#define BTN_Y_POS						(SCRN_H - BTN_H)

#define WND_BACK_X_POS					0
#define WND_BACK_Y_POS					TOP_BG_H
#define WND_BACK_W						(SCRN_W - RIGHT_BG_W)
#define WND_BACK_H						(SCRN_H - (TOP_BG_H+BTN_H))

#define BASE_WND_AREA_X_POS				10
#define BASE_WND_AREA_Y_POS				54
#define BASE_WND_AREA_W					580
#define BASE_WND_AREA_H					370
#define MENU_ITEM_H						37

#define POS_CAP_TXT_X_POS				617
#define POS_CAP_TXT_Y_POS				59
#define LAT_TXT_Y_POS					94
#define LON_TXT_Y_POS					132

#define SOG_CAP_TXT_X_POS				617
#define SOG_CAP_TXT_Y_POS				188
#define SOG_TXT_Y_POS					213

#define HDG_CAP_TXT_X_POS				617
#define HDG_CAP_TXT_Y_POS				275
#define HDG_TXT_Y_POS					300

#define SPK_ICON_X_POS					543
#define SPK_ICON_Y_POS					14

#define SPK_OUTLINE_X_POS				537
#define SPK_OUTLINE_Y_POS				10
#define SPK_OUTLINE_W					40
#define SPK_OUTLINE_H                   25

#define ALM_X_POS						617
#define ALM_Y_POS						364

#define TXT_X_POS						662
#define TXT_Y_POS						364

#define MSG_X_POS						707
#define MSG_Y_POS						364	

#define LRM_X_POS						752
#define LRM_Y_POS						364

#define MSG_ICON_W						32
#define MSG_ICON_H						32
#define MSG_ICON_TXT_Y_POS				404

#define UTC_DATE_X_POS					600
#define UTC_DATE_Y_POS					44

#define UTC_TIME_X_POS					685
#define UTC_TIME_Y_POS					44

#define UTC_SYMBOL_X_POS				700		
#define UTC_SYMBOL_Y_POS				44

#define LOW_PWR_MODE_X_POS				399
#define LOW_PWR_MODE_Y_POS				10
#define LOW_PWR_MODE_W					30
#define LOW_PWR_MODE_H					25

#define GPS_STAT_X_POS					429
#define GPS_STAT_Y_POS					10
#define GPS_STAT_W						63
#define GPS_STAT_H						25

#define GPS_TYPE_X_POS					492					
#define GPS_TYPE_Y_POS					10
#define GPS_TYPE_W						42
#define GPS_TYPE_H						25

#define TRIANGLE_IMG_W					10
#define TRIANGLE_IMG_H					10

#define ALARM_WND_X_POS					40
#define ALARM_WND_Y_POS					84
#define ALARM_WND_W						520
#define ALARM_WND_H						310

#define ALARM_TITLE_X_POS				ALARM_WND_X_POS
#define ALARM_TITLE_Y_POS				ALARM_WND_Y_POS
#define ALARM_TITLE_W					ALARM_WND_W
#define ALARM_TITLE_H					40

#define ALARM_ICON_X_POS				102
#define ALARM_ICON_Y_POS				164	
#define ALARM_ICON_W					104
#define ALARM_ICON_H					104

#define ALARM_BTN_X1_POS				255
#define ALARM_BTN_X2_POS				200
#define ALARM_BTN_X3_POS				320
#define ALARM_BTN_Y_POS					279

#define ALARM_BTN_W						90
#define ALARM_BTN_H						45

#define ALRAM_INFO_BAR_W				ALARM_WND_W
#define ALARM_INFO_BAR_H				40
#define ALARM_INFO_BAR_X_POS			ALARM_WND_X_POS		
#define ALARM_INFO_BAR_Y_POS			(ALARM_WND_Y_POS + ALARM_WND_H - ALARM_INFO_BAR_H)

#define ALARM_MSG_X_POS					230
#define ALARM_MSG_Y_POS					183
#define ALARM_BAM_MSG_Y_POS					160

#define ALAMR_MSG_H						37

#define ALARM_MSG_CONT_X_POS			195
#define ALARM_MSG_CONT_Y_POS			150

#define MNU_BAR_W						WND_BACK_W						
#define MNU_BAR_H						43					
#define MNU_BAR_ARR_X_POS				560
#define MAX_MENU_BAR					9

#define PWR_MODE_HIGH					0
#define PWR_MODE_LOW					1

#define MAX_USER_REGION_NUM				8
#define DEFT_REGION_NUM					1
#define MAX_REGION_NUM					(MAX_USER_REGION_NUM + DEFT_REGION_NUM)

#define MIN_STD_ALARM_NO				1
#define MAX_STD_ALARM_NO				50
#define MAX_IGNR_ALARM_CNT				50

#endif

