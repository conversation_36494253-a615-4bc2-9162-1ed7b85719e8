#include "Wnd.hpp"

#ifndef __PROGRAM_UPLOAD_WND_HPP__
#define __PROGRAM_UPLOAD_WND_HPP__

class CProgramUploadWnd : public CWnd {
protected:
	int m_nSelNum;

public:
	CProgramUploadWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
	
	void OnKeyEvent(int nKey, DWORD nFlags);
	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	
	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	void SetSelNum(int nSelNum) { m_nSelNum = nSelNum; }

	void DrawSubMenu(int nSelNum);
	void TransponderUpload();
	void ProgramUpload();
	void MapUpload();
	void BooterUpload();
	void LoaderUpload();
	void UploadMainWithCCFLBooterAndLoader();
	void UploadMainWithLEDBooterAndLoader();
};

#endif


