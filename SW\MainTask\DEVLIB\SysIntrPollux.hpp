/*...........................................................................*/
/*.                  File Name : SYSINTR.HPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.11                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSINTRPOLLUX_HPP__
#define  __SYSINTRPOLLUX_HPP__

#if defined(__POLLUX__)
//=============================================================================
#define IRQ_PHY_PDISPLAY                  0
#define IRQ_PHY_SDISPLAY                  1
#define IRQ_PHY_DMA                       3
#define IRQ_PHY_SYSTIMER0                 4
#define IRQ_PHY_SYSCTRL                   5
#define IRQ_PHY_UART0                    10
#define IRQ_PHY_SYSTIMER1                11
#define IRQ_PHY_SSPSPI0                  12
#define IRQ_PHY_GPIO                     13
#define IRQ_PHY_SDMMC                    14
#define IRQ_PHY_SYSTIMER2                15
#define IRQ_PHY_UDC                      20
#define IRQ_PHY_SYSTIMER3                21
#define IRQ_PHY_AUDIOIF                  24
#define IRQ_PHY_ADC                      25
#define IRQ_PHY_MCUSTATIC                26
#define IRQ_PHY_GRP3D                    27
#define IRQ_PHY_UHC                      28
#define IRQ_PHY_RTC                      31
#define IRQ_PHY_I2C0                     32
#define IRQ_PHY_I2C1                     33
#define IRQ_PHY_UART1                    34
#define IRQ_PHY_UART2                    35
#define IRQ_PHY_UART3                    36
#define IRQ_PHY_SSPSPI1                  39
#define IRQ_PHY_SSPSPI2                  40
#define IRQ_PHY_CSC                      41
#define IRQ_PHY_SDMMC1                   42
#define IRQ_PHY_SYSTIMER4                43
//----------------------------------------------------------------------
#define INT_MODE_IRQ                      0
#define INT_MODE_FIQ                      1
//=============================================================================

//=============================================================================
#ifdef  __cplusplus
extern "C" {
#endif

void  IsrHandlerIRQ(void) __attribute__ ((interrupt ("IRQ")));
void  IsrHandlerFIQ(void) __attribute__ ((interrupt ("FIQ")));
void  IsrHandlerSWI(void) __attribute__ ((interrupt ("SWI")));
void  IsrHandlerABORT(void) __attribute__ ((interrupt ("ABORT")));
void  IsrHandlerUNDEF(void) __attribute__ ((interrupt ("UNDEF")));

void  ReStartMainBooter(void);

#ifdef  __cplusplus
}
#endif
//=============================================================================
void  IsrHandlerTimer0(void);
void  IsrHandlerUART0(void);
void  IsrHandlerUART1(void);
void  IsrHandlerUART2(void);
void  IsrHandlerUART3(void);
void  IsrHandlerGPIO(void);
//=============================================================================
void  SysSetAllInterrupt(void);
void  SysSetOneInterrupt(int nIntNo,int nIRQ_FIQ);
void  SysSetAllInterruptDisable(void);
//=============================================================================
//==========// POLLUX==========================================================
#endif      // POLLUX

#endif

