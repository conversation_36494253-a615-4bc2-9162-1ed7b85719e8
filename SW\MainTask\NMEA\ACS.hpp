#include "Sentence.hpp"

#ifndef __ACS_HPP__
#define __ACS_HPP__
/******************************************************************************
*
* ACS - Channel management information Source
*
* $--ACS,x,xxxxxxxxx,hhmmss.ss,xx,xx,xxxx*hh<CR><LF>
*        | |         |         |  |  |
*        1 2         3         4  5  6
*
* 1. Sequence number , 0 to 9
* 2. MMSI of originator
* 3. UTC at receipt of regional operating settings
* 4. UTC day, 01 to 31
* 5. UTC month, 01 to 12
* 6. UTC year
*
******************************************************************************/
class CAcs : public CSentence {
protected:
	char m_szTalkID[5];	  // Add talk ID check : HSI 2012.05.07
	int  m_nSeqNumber;    // 0 to 9
	int  m_nMMSIOrig;
	char m_szUTC[10];
	int  m_nUTCHour;
	int  m_nUTCMin;
	int  m_nUTCSec;
	int	 m_nUTCDay;
	int  m_nUTCMonth;
	int  m_nUTCYear;
public:
    CAcs();
    CAcs(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);
	int  MakeSentence(BYTE *pszSentence) { return 0; }
	
	void GetTalkID(char *pszDest) { strcpy(pszDest, m_szTalkID); } // HSI 2014.05.07
	int  GetSeqNumber()   { return m_nSeqNumber;}
	int  GetMMSIOrig()    { return m_nMMSIOrig; }
	void GetUTC(char szUTC[10]) { strcpy(szUTC, m_szUTC); }
	int  GetUTCHour()     { return m_nUTCHour;  }
	int  GetUTCMin()      { return m_nUTCMin;   }
	int  GetUTCSec()      { return m_nUTCSec;   }
	int  GetUTCDay()      { return m_nUTCDay;   }
	int  GetUTCMonth()    { return m_nUTCMonth; }
	int  GetUTCYear()     { return m_nUTCYear;  }
};
		
#endif
