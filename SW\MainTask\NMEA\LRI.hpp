#include "Sentence.hpp"

#ifndef __LRI_HPP__
#define __LRI_HPP__
/******************************************************************************
*
* LRI - Long Range Interrogation
*
* $--LRI,x,a,xxxxxxxxx,xxxxxxxxx,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a*hh<CR><LF>
*        | | |         |         |       | |        | |       | |        |
*        1 2 3         4         5       6 7        8 9      10 11       12
*
* 1.     Sequence Number , 0 to 9
* 2.     Control Flag
* 3.     MMSI of "requestor"
* 4.     MMSI of "destination"
* 5.6.   Latitude  - N/S (north-east co-ordinate)
* 7.8.   Longitude - E/W (north-east co-ordinate)
* 9.10.  Latitude  - N/S (south-west co-ordinate)
* 11.12. Longitude - E/W (south-west co-ordinate)
*
******************************************************************************/
class CLri : public CSentence {
protected:
	
	int   m_nSeqNumber;
	char  m_chControlFlag;
	DWORD m_dwMMSIReq;
	DWORD m_dwMMSIDest;
	int   m_nLatNE;
	int   m_nLonNE;
	int   m_nLatSW;
	int   m_nLonSW;

public:
    CLri();
    CLri(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);
	int  MakeSentence(BYTE *pszSentence);

	void  SetSeqNumber(int nSeqNum) { m_nSeqNumber = nSeqNum; }
	void  SetControlFlag(char chControlFlag) { m_chControlFlag = chControlFlag; }
	void  SetMMSIReq(DWORD dwMMSI)  { m_dwMMSIReq  = dwMMSI; }
	void  SetMMSIDest(DWORD dwMMSI) { m_dwMMSIDest = dwMMSI; }
	
	int   GetSeqNumber()   { return m_nSeqNumber;    }
	char  GetControlFlag() { return m_chControlFlag; }
	DWORD GetMMSIReq()     { return m_dwMMSIReq;      }
	DWORD GetMMSIDest()    { return m_dwMMSIDest;     }
	int   GetLatNE()     { return m_nLatNE;      }
	int   GetLonNE()     { return m_nLonNE;      }
	void  GetNEPos(int *lat, int *lon) {
		*lat = m_nLatNE;
		*lon = m_nLonNE;
	}
	void  GetSWPos(int *lat, int *lon) {
		*lat = m_nLatSW;
		*lon = m_nLonSW;
	}
	double GetLatSW()     { return m_nLatSW;      }
	double GetLonSW()     { return m_nLonSW;      }
};

#endif
