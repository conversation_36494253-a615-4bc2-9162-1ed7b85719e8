#ifndef __FONT_H__
#define __FONT_H__

#include "DataType.h"

typedef struct tagFONT {
	BYTE   uType;            //!< bit-flags defined below    (always = 0x01)
	BYTE   uAscent;          //!< Ascent above baseline      (equal to uHeight)
	BYTE   uDescent;         //!< Descent below baseline     (always = 0x00)
	BYTE   uHeight;          //!< total height of character
	HWORD  wBytesPerLine;    //!< total bytes (width) of one scanline
	HWORD  wFirstChar;       //!< first character present in font (page)
	HWORD  wLastChar;        //!< last character present in font (page)
	HWORD *pOffsets;        //!< bit-offsets for variable-width font
	struct tagFONT *pNext;   //!< NULL unless multi-page Unicode font
	BYTE  *pData;           //!< character bitmap data array
} FONT, *PFONT;


extern FONT MyriadPro14bEng;
extern FONT MyriadPro16bEng;
extern FONT MyriadPro18bEng;
extern FONT MyriadPro21bEng;
extern FONT MyriadPro22bEng;
extern FONT MyriadPro23bEng;
extern FONT MyriadPro24bEng;
extern FONT MyriadPro24bEng_WFix;
extern FONT MyriadPro28bEng;
extern FONT MyriadPro30bEng;
extern FONT MyriadPro32bEng;
extern FONT MyriadPro38bEng;
extern FONT MyriadPro40bEng;
extern FONT MyriadPro72bEng;
extern FONT MyriadPro84bEng;

extern FONT NewGulLim10bCJK;
extern FONT NewGulLim18bCJK;
extern FONT NewGulLim20bCJK;

extern FONT MyriadPro18bRus;
extern FONT MyriadPro21bRus;
extern FONT MyriadPro24bRus;
extern FONT MyriadPro28bRus;
extern FONT MyriadPro30bRus;
extern FONT MyriadPro32bRus;

extern xFONTYY Eng09x06Font;
extern xFONTYY MyriadPro08nEng_Font;
extern xFONTYY MyriadPro10bEng_Font;
extern xFONTYY MyriadPro12bEng_Font;
extern xFONTYY MyriadPro16bEng_Font;
extern xFONTYY MyriadPro18bEng_Font;

extern xFONTYY MyriadPro22bEng_Font;
extern xFONTYY MyriadPro26bEng_Font;
extern xFONTYY MyriadPro44bEng_Font;
extern xFONTYY MyriadPro48bEng_Font;
extern xFONTYY MyriadPro76bEng_Font;

extern xFONTYY NewGulLim30bEng_Font;
extern xFONTYY NewGulLim36bEng_Font;
extern xFONTYY NewGulLim42bEng_Font;
extern xFONTYY NewGulLim48bEng_Font;
extern xFONTYY NewGulLim54bEng_Font;
extern xFONTYY NewGulLim64bEng_Font;
#endif	// __FONT_H__

