/*...........................................................................*/
/*.                  File Name : SYSMLC.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSMLC_H__
#define  __SYSMLC_H__

//=============================================================================
#if defined(__POLLUX__)
    #define  MLC_BACKGROUND_COLOR   0x00000000   //
    #define  MLC_TRANSPARENCY_COLOR 0x00000001   //
    #define  MLC_INVERSION_COLOR    0x00000000   //
#else                           // SPICA
    #define  MLC_BACKGROUND_COLOR   0x00000000   //
    #define  MLC_TRANSPARENCY_COLOR 0x00000001   //
    #define  MLC_INVERSION_COLOR    0x00000000   //
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  MLC_RGBFMT_R5G6B5      0x44320000   // 16bpp { R5, G6, B5 }.
    #define  MLC_RGBFMT_B5G6R5      0xC4320000   // 16bpp { B5, G6, R5 }.

    #define  MLC_RGBFMT_X1R5G5B5    0x43420000   // 16bpp { X1, R5, G5, B5 }.
    #define  MLC_RGBFMT_X1B5G5R5    0xC3420000   // 16bpp { X1, B5, G5, R5 }.
    #define  MLC_RGBFMT_X4R4G4B4    0x42110000   // 16bpp { X4, R4, G4, B4 }.
    #define  MLC_RGBFMT_X4B4G4R4    0xC2110000   // 16bpp { X4, B4, G4, R4 }.
    #define  MLC_RGBFMT_X8R3G3B2    0x41200000   // 16bpp { X8, R3, G3, B2 }.
    #define  MLC_RGBFMT_X8B3G3R2    0xC1200000   // 16bpp { X8, B3, G3, R2 }.

    #define  MLC_RGBFMT_A1R5G5B5    0x33420000   // 16bpp { A1, R5, G5, B5 }.
    #define  MLC_RGBFMT_A1B5G5R5    0xB3420000   // 16bpp { A1, B5, G5, R5 }.
    #define  MLC_RGBFMT_A4R4G4B4    0x22110000   // 16bpp { A4, R4, G4, B4 }.
    #define  MLC_RGBFMT_A4B4G4R4    0xA2110000   // 16bpp { A4, B4, G4, R4 }.
    #define  MLC_RGBFMT_A8R3G3B2    0x11200000   // 16bpp { A8, R3, G3, B2 }.
    #define  MLC_RGBFMT_A8B3G3R2    0x91200000   // 16bpp { A8, B3, G3, R2 }.

    #define  MLC_RGBFMT_R8G8B8      0x46530000   // 24bpp { R8, G8, B8 }.
    #define  MLC_RGBFMT_B8G8R8      0xC6530000   // 24bpp { B8, G8, R8 }.

    #define  MLC_RGBFMT_X8R8G8B8    0x46530000   // 32bpp { X8, R8, G8, B8 }.
    #define  MLC_RGBFMT_X8B8G8R8    0xC6530000   // 32bpp { X8, B8, G8, R8 }.
    #define  MLC_RGBFMT_A8R8G8B8    0x06530000   // 32bpp { A8, R8, G8, B8 }.
    #define  MLC_RGBFMT_A8B8G8R8    0x86530000   // 32bpp { A8, B8, G8, R8 }.
    #define  MLC_RGBFMT_PTR5G6B5    0x443A0000   // Palette Mode
#else                           // SPICA
    #define  MLC_RGBFMT_R5G6B5      0x44320000   // 16bpp { R5, G6, B5 }.
    #define  MLC_RGBFMT_B5G6R5      0xC4320000   // 16bpp { B5, G6, R5 }.

    #define  MLC_RGBFMT_X1R5G5B5    0x43420000   // 16bpp { X1, R5, G5, B5 }.
    #define  MLC_RGBFMT_X1B5G5R5    0xC3420000   // 16bpp { X1, B5, G5, R5 }.
    #define  MLC_RGBFMT_X4R4G4B4    0x42110000   // 16bpp { X4, R4, G4, B4 }.
    #define  MLC_RGBFMT_X4B4G4R4    0xC2110000   // 16bpp { X4, B4, G4, R4 }.
    #define  MLC_RGBFMT_X8R3G3B2    0x41200000   // 16bpp { X8, R3, G3, B2 }.
    #define  MLC_RGBFMT_X8B3G3R2    0xC1200000   // 16bpp { X8, B3, G3, R2 }.

    #define  MLC_RGBFMT_A1R5G5B5    0x33420000   // 16bpp { A1, R5, G5, B5 }.
    #define  MLC_RGBFMT_A1B5G5R5    0xB3420000   // 16bpp { A1, B5, G5, R5 }.
    #define  MLC_RGBFMT_A4R4G4B4    0x22110000   // 16bpp { A4, R4, G4, B4 }.
    #define  MLC_RGBFMT_A4B4G4R4    0xA2110000   // 16bpp { A4, B4, G4, R4 }.
    #define  MLC_RGBFMT_A8R3G3B2    0x11200000   // 16bpp { A8, R3, G3, B2 }.
    #define  MLC_RGBFMT_A8B3G3R2    0x91200000   // 16bpp { A8, B3, G3, R2 }.

    #define  MLC_RGBFMT_R8G8B8      0x46530000   // 24bpp { R8, G8, B8 }.
    #define  MLC_RGBFMT_B8G8R8      0xC6530000   // 24bpp { B8, G8, R8 }.

    #define  MLC_RGBFMT_X8R8G8B8    0x46530000   // 32bpp { X8, R8, G8, B8 }.
    #define  MLC_RGBFMT_X8B8G8R8    0xC6530000   // 32bpp { X8, B8, G8, R8 }.
    #define  MLC_RGBFMT_A8R8G8B8    0x06530000   // 32bpp { A8, R8, G8, B8 }.
    #define  MLC_RGBFMT_A8B8G8R8    0x86530000   // 32bpp { A8, B8, G8, R8 }.
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  MLC_PCLKMODE_DYNAMIC            0    // PCLK is provided only when CPU has access to registers of this module.
    #define  MLC_PCLKMODE_ALWAYS             1    // PCLK is always provided for this module.

    #define  MLC_BCLKMODE_DISABLE            0    // BCLK is disabled.
    #define  MLC_BCLKMODE_DYNAMIC            2    // BCLK is provided only when this module requests it.
    #define  MLC_BCLKMODE_ALWAYS             3    // BCLK is always provided for this module.
#else                           // SPICA
    #define  MLC_PCLKMODE_DYNAMIC            0    // PCLK is provided only when CPU has access to registers of this module.
    #define  MLC_PCLKMODE_ALWAYS             1    // PCLK is always provided for this module.

    #define  MLC_BCLKMODE_DISABLE            0    // BCLK is disabled.
    #define  MLC_BCLKMODE_DYNAMIC            2    // BCLK is provided only when this module requests it.
    #define  MLC_BCLKMODE_ALWAYS             3    // BCLK is always provided for this module.
#endif
//-----------------------------------------------------------------------------
#if defined(__SPICA__)
    #define  MLC_YUVFMT_420           (0 << 16)  // Block Separated YUV420.
    #define  MLC_YUVFMT_422           (1 << 16)  // Block Separated YUV422.
    #define  MLC_YUVFMT_YUYV          (2 << 16)  // Linear YUV422(YUYV).
    #define  MLC_YUVFMT_444           (3 << 16)  // Block Separated YUV444.
    #define  MLC_YUVFMT_FORCEU32    0x7FFFFFFF
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
  #if defined(__SCR_800x600__) || defined(__SCR_640x480__) || defined(__SCR_1024x768__)
      #if defined(__SCR_800x600__)
          #define  RGB_LAYER0_BASE_ADDR   (VIDEO_MEM_START_ADDRESS + 0x00000000)
          #define  RGB_LAYER1_BASE_ADDR   (RGB_LAYER0_BASE_ADDR    + 0x00400000)
          #define  VID_LAYER_Y_BASE_ADDR  (RGB_LAYER0_BASE_ADDR    + 0x00400000)
          #define  VID_LAYER_CB_BASE_ADDR (VID_LAYER_Y_BASE_ADDR   + SCRN_YUV_CR_STRIDE)
          #define  VID_LAYER_CR_BASE_ADDR (VID_LAYER_CB_BASE_ADDR  + SCRN_YUV_Y_STRIDE)
      #endif
      #if defined(__SCR_640x480__)
          #define  RGB_LAYER0_BASE_ADDR   (VIDEO_MEM_START_ADDRESS + 0x00000000)
          #define  RGB_LAYER1_BASE_ADDR   (RGB_LAYER0_BASE_ADDR    + 0x00280000)
          #define  VID_LAYER_Y_BASE_ADDR  (RGB_LAYER0_BASE_ADDR    + 0x00280000)
          #define  VID_LAYER_CB_BASE_ADDR (VID_LAYER_Y_BASE_ADDR   + SCRN_YUV_CR_STRIDE)
          #define  VID_LAYER_CR_BASE_ADDR (VID_LAYER_CB_BASE_ADDR  + SCRN_YUV_Y_STRIDE)
      #endif
      #if defined(__SCR_1024x768__)
          #define  RGB_LAYER0_BASE_ADDR   (VIDEO_MEM_START_ADDRESS + 0x00000000)
          #define  RGB_LAYER1_BASE_ADDR   (RGB_LAYER0_BASE_ADDR    + 0x00600000)
          #define  VID_LAYER_Y_BASE_ADDR  (RGB_LAYER0_BASE_ADDR    + 0x00600000)
          #define  VID_LAYER_CB_BASE_ADDR (VID_LAYER_Y_BASE_ADDR   + SCRN_YUV_CR_STRIDE)
          #define  VID_LAYER_CR_BASE_ADDR (VID_LAYER_CB_BASE_ADDR  + SCRN_YUV_Y_STRIDE)
      #endif
  #else
    #define  RGB_LAYER0_BASE_ADDR   (VIDEO_MEM_START_ADDRESS + 0x00000000)
    #define  RGB_LAYER1_BASE_ADDR   (RGB_LAYER0_BASE_ADDR    + 0x00280000)
    #define  VID_LAYER_Y_BASE_ADDR  (RGB_LAYER0_BASE_ADDR    + 0x00280000)
    #define  VID_LAYER_CB_BASE_ADDR (VID_LAYER_Y_BASE_ADDR   + SCRN_YUV_CR_STRIDE)
    #define  VID_LAYER_CR_BASE_ADDR (VID_LAYER_CB_BASE_ADDR  + SCRN_YUV_Y_STRIDE)
  #endif
#else                           // SPICA
    #define  RGB_LAYER0_BASE_ADDR   (VIDEO_MEM_START_ADDRESS + 0x00000000)
    #define  RGB_LAYER1_BASE_ADDR   (RGB_LAYER0_BASE_ADDR    + 0x00600000)
    #define  RGB_LAYER2_BASE_ADDR   (RGB_LAYER1_BASE_ADDR    + 0x00300000)
    #define  VID_LAYER_Y_BASE_ADDR  (RGB_LAYER2_BASE_ADDR    + 0x00300000)
    #define  VID_LAYER_CB_BASE_ADDR (VID_LAYER_Y_BASE_ADDR   + SCRN_YUV_CR_STRIDE)
    #define  VID_LAYER_CR_BASE_ADDR (VID_LAYER_CB_BASE_ADDR  + SCRN_YUV_Y_STRIDE)
#endif
//-----------------------------------------------------------------------------
#if defined(__POLLUX__)
    #define  MLC_LAYER_PRIORITY              0   // Layer0 > Layer1 > Video

    #define  MLC_RGB_LAYER0                  0
    #define  MLC_RGB_LAYER1                  1
    #define  MLC_VID_LAYER                   2

    #define  MEMU_LAYER_NO      MLC_RGB_LAYER0
    #define  CHART_LAYER_NO     MLC_RGB_LAYER1   // Plotter+Fish
    #define  VIDEO_LAYER_NO     MLC_VID_LAYER    //

    #define  MENU_LAYER_BASE_ADDR   RGB_LAYER0_BASE_ADDR
    #define  CHART_LAYER_BASE_ADDR  RGB_LAYER1_BASE_ADDR
    #define  VIDEO_LAYER_BASE_ADDR  VID_LAYER_Y_BASE_ADDR

#if CLRMENU_SIZEOF_SIZE == 2
    #define  MENU_LAYER_FORMAT   (MLC_RGBFMT_R5G6B5)
#else
    #define  MENU_LAYER_FORMAT   (MLC_RGBFMT_A8R8G8B8)
#endif
#if CLRCHART_SIZEOF_SIZE == 2
    #define  CHART_LAYER_FORMAT  (MLC_RGBFMT_R5G6B5)
#else
    #define  CHART_LAYER_FORMAT  (MLC_RGBFMT_A8R8G8B8)
#endif
#if CLRRADAR_SIZEOF_SIZE == 2
    #define  RADAR_LAYER_FORMAT  (MLC_RGBFMT_R5G6B5)
#else
    #define  RADAR_LAYER_FORMAT  (MLC_RGBFMT_A8R8G8B8)
#endif
    #define  VIDEO_LAYER_FORMAT  (MLC_RGBFMT_R5G6B5)
#else                           // SPICA
    #define  MLC_LAYER_PRIORITY              1   // Layer0 > Video > Layer1 > Layer2

    #define  MLC_RGB_LAYER0                  0
    #define  MLC_RGB_LAYER1                  1
    #define  MLC_RGB_LAYER2                  2
    #define  MLC_VID_LAYER                   3

    #define  MEMU_LAYER_NO      MLC_RGB_LAYER0
    #define  RADAR_LAYER_NO     MLC_RGB_LAYER1   // Radar
    #define  CHART_LAYER_NO     MLC_RGB_LAYER2   // Plotter+Fish
    #define  VIDEO_LAYER_NO     MLC_VID_LAYER    //

    #define  MENU_LAYER_BASE_ADDR   RGB_LAYER0_BASE_ADDR
    #define  RADAR_LAYER_BASE_ADDR  RGB_LAYER1_BASE_ADDR
    #define  CHART_LAYER_BASE_ADDR  RGB_LAYER2_BASE_ADDR
    #define  VIDEO_LAYER_BASE_ADDR  VID_LAYER_Y_BASE_ADDR

#if CLRMENU_SIZEOF_SIZE == 2
    #define  MENU_LAYER_FORMAT   (MLC_RGBFMT_R5G6B5)
#else
    #define  MENU_LAYER_FORMAT   (MLC_RGBFMT_A8R8G8B8)
#endif
#if CLRCHART_SIZEOF_SIZE == 2
    #define  CHART_LAYER_FORMAT  (MLC_RGBFMT_R5G6B5)
#else
    #define  CHART_LAYER_FORMAT  (MLC_RGBFMT_A8R8G8B8)
#endif
#if CLRRADAR_SIZEOF_SIZE == 2
    #define  RADAR_LAYER_FORMAT  (MLC_RGBFMT_R5G6B5)
#else
    #define  RADAR_LAYER_FORMAT  (MLC_RGBFMT_A8R8G8B8)
#endif
    #define  VIDEO_LAYER_FORMAT  (MLC_RGBFMT_R5G6B5)
#endif
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

//=============================================================================
void  SysInitMLC(void);
void  SysSetMLcTopDirtyFlag(void);
int   SysGetMLcTopDirtyFlag(void);
void  SysSetMLcEnableMode(int nDisableEnableMode,int nApplyNow);
int   SysGetMLcEnableMode(void);
void  SysSetMLcFieldEnable(int nDisableEnableMode,int nApplyNow);
int   SysGetMLcFieldEnable(void);
void  SysSetMLcPriority(DWORD dPriority,int nApplyNow);
void  SysSetMLcScreenSize(DWORD dScrWidth,DWORD dScrHeight);
void  SysSetMLcBackground(DWORD dColor);
void  SysSetMLcTopPowerMode(int nDisableEnableMode);
void  SysSetMLcTopSleepMode(int nDisableEnableMode);
void  SysSetMLcClockPClkMode(DWORD dPClkMode);
void  SysSetMLcClockBClkMode(DWORD dBClkMode);
//=============================================================================
void  SysSetMLcLayDirtyFlag(int nLayer);
int   SysGetMLcLayDirtyFlag(int nLayer);
void  SysSetMLcPalettePowerMode(int nLayer,int nDisableEnableMode);
int   SysGetMLcPalettePowerMode(int nLayer);
void  SysSetMLcPaletteSleepMode(int nLayer,int nDisableEnableMode);
int   SysGetMLcPaletteSleepMode(int nLayer);
void  SysSetMLcLayerEnableMode(int nLayer,int nDisableEnableMode);
int   SysGetMLcLayerEnableMode(int nLayer);
void  SysSetMLcLockSize(int nLayer,int nLockSize);
void  SysSetMLc3DEnableMode(int nLayer,int nDisableEnableMode);
void  SysSetMLcAlphaBlending(int nLayer,int nDisableEnableMode,DWORD dAlpha);
void  SysSetMLcTransparency(int nLayer,int nDisableEnableMode,DWORD dColor);
void  SysSetMLcColorInversion(int nLayer,int nDisableEnableMode,DWORD dColor);
DWORD SysGetMLcExtendedColor(DWORD dColor,DWORD dFormat);
void  SysSetMLcRGbFormat(int nLayer,DWORD dFormat);
void 	SysSetMLcYUvFormat(DWORD dFormat);
void  SysSetMLcPosition(int nLayer,DWORD dStartX,DWORD dStartY,DWORD dLastX,DWORD dLastY);
void  SysSetMLcRGbLayerInvalidPosition(int nLayer,int nRegion,DWORD dStartX,DWORD dStartY,DWORD dLastX,DWORD dLastY,DWORD dDisableEnableMode);
void  SysSetMLcRGbLayerStride(int nLayer,DWORD dHoriStride,DWORD dVertStride);
void  SysGetMLcRGbLayerStride(int nLayer,DWORD *pHoriStride,DWORD *pVertStride);
void  SysSetMLcRGbLayerAddress(int nLayer,DWORD dStartAddr);
DWORD SysGetMLcRGbLayerAddress(int nLayer);
void  SysSetMLcRGbLayerPalette(int nLayer,UCHAR bPalIndex,HWORD wPalData);
void  SysSetMLcYUvLayerStride(DWORD dLuStride,DWORD dCbStride,DWORD dCrStride);
void  SysSetMLcYUvLayerAddress(DWORD dLuAddr,DWORD dCbAddr,DWORD dCrAddr);
void  SysSetMLcYUvLayerScaleFactor(DWORD dHoriScale,DWORD dVertScale,DWORD dHoriMode,DWORD dVertMode);
#if defined(__POLLUX__)
void  SysSetMLcYUvLayerScale(DWORD dSourceWidth,DWORD dSourceHeight,DWORD dTargetWidth,DWORD dTargetHeight,DWORD dHoriMode,DWORD dVertMode);
#else                           // SPICA
void  SysSetMLcYUvLayerScale(DWORD dSourceWidth,DWORD dSourceHeight,DWORD dTargetWidth,DWORD dTargetHeight,DWORD dHoriLuma,DWORD dHoriChro,DWORD dVertLuma,DWORD dVertChro);
#endif

void  SysSetMLcYUvLayerLumaEnhance(DWORD dContrast,DWORD dBrightness);

#if defined(__POLLUX__)
void  SysSetMLcYUvLayerChromaEnhance(DWORD dQuadrant,DWORD dCbA,DWORD dCbB,DWORD dCrA,DWORD dCrB);
#else                           // SPICA
void  SysSetMLcYUvLayerChromaEnhance(DWORD dQuadrant,int dCbA,int dCbB,int dCrA,int dCrB);
#endif

#if defined(__SPICA__)
void  SysSetMLcYUvLayerAddressYUYV(DWORD dStartAddr,DWORD dVertStride);
void  SysSetMLcYUvLayerLineBufferPowerMode(int nDisableEnableMode);
void  SysSetMLcYUvLayerLineBufferSleepMode(int nDisableEnableMode);
void  SysSetMLcYUvLayerGamaTablePowerMode(int nY,int nU,int nV);
void  SysSetMLcYUvLayerGamaTableSleepMode(int nY,int nU,int nV);
void  SysSetMLcYUvLayerGammaEnableMode(int nDisableEnableMode);
#endif
//=============================================================================

#ifdef  __cplusplus
}
#endif

#endif

