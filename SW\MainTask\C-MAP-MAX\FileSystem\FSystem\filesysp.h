/*##################################################################
  FILE    : FILESYS.H

  USE     : File system: main definitions.
  PROJECT : C-Map's File System.

  AUTHOR  : CB[17Dec92].
  UPDATED : SiS[030611].
  ##################################################################
*/



#ifndef __FILESYSP__
	#define __FILESYSP__

/*****************************************************************************
  #Include section.
 *****************************************************************************/

/* System include. 	*/
#include <stdlib.h>
#include <string.h>
#include <ctype.h>

/* Local include. 	*/
#include <cmaptype.h>



/*****************************************************************************
  Constants definition section.
 *****************************************************************************/

#define CDOS_VER				1
#define ME						"CDOS"

#define FSYS_ID_OFFSET			1
#define FSYS_ID_LEN				3
#define FSYS_ID_CODE			"DOS"

#define MAX_BLOCK				1024		/* max value used for 'blsize'		 */

#define FILENAMESIZE			8
#define MAXFILETYPE				999

#define dirend 					bamblk




/*****************************************************************************
  Types & Data Structure definition definition section.
 *****************************************************************************/


typedef struct
{
	SByte		cdosid[ 4 ] ;	   /* Should be (C)DOS on formatted devices */
	SByte		vlname[ 8 ] ;
	SWord		numblk ;
	unsigned char dosver, dim16;
} VolumeHeader;




#ifdef USE_SIZE_OF_AT_COMPILE_TIME
	#if ( sizeof(VolumeHeader) != 16 )
		#error "Compiler gives strange size to VolumeHeader"
	#endif
#endif



struct DeviceNewEx 
{
	SLong	SECsize;			/* Sector size								*/
	SWord	SECnum ;			/* Number of Physical Sectors				*/
	SWord	SECperDir;			/* Num. of sectors allocated for the Dir.	*/
	SWord	FATsize;			/* number of blocks used for Dir, Bam 		*/

	SWord	( *open )( struct DeviceNew *d, Bool Mode ) ;
	SWord	( *rd_buff )( struct DeviceNew *d, SLong Address, SWord Count, void *Buffer ) ;
	SWord	( *er_sect )( struct DeviceNew *d, SWord Sector ) ;
	SWord	( *close )( struct DeviceNew *d ) ;
};


struct DeviceNew 
{
	SWord	blsize ;			/* Logical block size in bytes				*/
	SWord	numblk ;			/* NUmber of logical blocks					*/
	SWord	dirblk ;			/* First logical block for the Directory	*/
	SWord	bamblk ;			/* Logical Block for the BAM				*/
	SWord	fstblk ;			/* First logical block used for DATA		*/
	SWord	( *wr_blk )( struct DeviceNew *d, SWord Settore, void *Buffer ) ;
	SWord	( *rd_blk )( struct DeviceNew *d, SWord Settore, void *Buffer ) ;
	SWord	( *format )( struct DeviceNew *d );
	SWord	( *initdv )( struct DeviceNew *d );
	/* 
	 * NULL in case the device does not need to have the support 
	 * for big physical sectors (e.g. 1MB..4MB User C-cards)			
	 */
	struct DeviceNewEx	*DeviceEx;	

	SWord	letter ;
};




/*****************************************************************************
  Interface Functions prototypes.
 *****************************************************************************/

#ifdef __cplusplus
extern "C"
{
#endif

/*
 * Return the number of devices in the current devices table.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_GetDevicesNum ( void );

/* 
 * Return the type of the current device ( DISK, CCARD etc..).
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_GetDeviceType ( void );
 
/*
 * Initializes the file system structures and detects the presence of
 * the Current Device
 *
 * Path: device to be initialized (or empty string).
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Init ( const char *Path );

/*
 * Install the device table into the system. The table must be 
 * NULL-terminated. An error will be returned if the passed pointer 
 * is NULL.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SetDeviceTable ( const struct DeviceNew **DevTabPtr );

/*
 * Destructively Tests and formats a device.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Test ( SByte *VolumeName );

/*
 * Formats a device. Volume name can be in one of the following forms:
 * <DRIVE>:<NAME> just <NAME>. In the second case, the device formatted 
 * is the current one.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Format ( SByte *VolumeName );

/*
 * Opens a file to be read.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_OpenR ( SByte *FileName );

/*
 * Closes a file formerly opened to be read.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_CloseR ( SWord fh );

/*
 * Reads the next char from a file opened to be read.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Getc ( SWord fh );

/*
 * Creates a new file to be written. An error occurs if the file
 * already exists.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_OpenW ( SByte *FileName );

/*
 * Closes a file formerly opened to be written.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_CloseW ( SWord fh );

/*
 * Writes a char to a file opened to be written.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Putc ( SByte c, SWord fh ) ;

/*
 * Writes a string to a file opened to be written.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Puts ( SByte *s, SWord fh ) ;

/*
 * Deletes a file.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Delete ( SByte *FileName ) ;

/*
 * Renames a file.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_Rename ( SByte *Old, SByte *New ) ;

/*
 * Gets the device name as the beginning of the file name.
 */
PRE_EXPORT_H extern struct DeviceNew* IN_EXPORT_H NameToDevice ( SByte *FileName ) ;

/*
 * Change the current device drive.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H DoCd ( SByte *s ) ;

/*
 * 
 */
PRE_EXPORT_H extern void IN_EXPORT_H BuildFileName ( SByte *Buffer, SByte *FileName, SWord FileType ) ;

/*
 * Get the last operation result code. At the end the internal  
 * Errno variable will be reset to NO_ERRORS.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H GetErrno ( void );

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_ReadFile ( SWord fh, SByte *mem, SWord len );

/*
 *	
 */
PRE_EXPORT_H SWord IN_EXPORT_H FS_ReadFileEx ( SWord fh, SByte *mem, Long dwLen );

/*
 *
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_WriteFile ( SWord fh, SByte *mem, SWord len );

/*
 *	
 */
PRE_EXPORT_H SWord IN_EXPORT_H FS_WriteFileEx ( SWord fh, SByte *mem, Long dwLen );

/*
 * Return the total number of blocks of the current device.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_TotalBlocks ( void );

/*
 * Return the number of used blocks of the current device.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_BlocksUsed ( void );

/*
 * Return the number of free and available blocks on the current device.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_BlocksFree ( void );

/*
 * Return the size in KiloBytes of blocks on the current device.
 * The returned value is dependent form the type of the device.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_BlockSize( void );

/*
 * Return the total space size in Kilobytes of the current device.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_DeviceSize ( void );

/*
 * Return the free space size (in bytes) of the current device.
 */
PRE_EXPORT_H extern SLong IN_EXPORT_H FS_SpaceFree ( void );

/*
 *	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_GetCurrDeviceLetter ( void );


#if defined ( DEBUG_SECTORS_HANDLING )
	#if DEBUG_SECTORS_HANDLING

PRE_EXPORT_H extern void IN_EXPORT_H GetSectorInfo ( SWord **ss, SWord *SecNum, SWord *FreeSec, SWord *LastUsed, unsigned char **Buffer );
PRE_EXPORT_H extern SWord IN_EXPORT_H EraseDeletedBlocks ( struct DeviceNew *d );

	#endif
#endif

PRE_EXPORT_H extern Word IN_EXPORT_H EncDecINT ( Word i );
PRE_EXPORT_H extern Long IN_EXPORT_H EncDecDWORD ( Long i );

PRE_EXPORT_H extern void IN_EXPORT_H DecodeFileBuffer ( Byte *L_buffer );
PRE_EXPORT_H extern void IN_EXPORT_H EncodeFileBuffer ( Byte *L_buffer );


#ifdef __cplusplus
}
#endif

/*****************************************************************************
  END of Code.
 *****************************************************************************/

#endif	/* #ifdef __FILESYSP__ */


/*------------------------------------------------------------------------
 * $Log: /CplotTools/FileSystem/SourceFiles/H/FILESYSP.H $
 * 
 * 7     3-10-00 18:53 Andrea
 * 
 * 6     29-09-00 15:40 Andrea
 * sizeof() is executed only if  USE_SIZE_OF_AT_COMPILE_TIME has been
 * defined  in compila.h module. Removed unused header files.
 * 
 * 5     31-01-00 12:07 Andrea
 * Added support for ARM7 compiler (ARMCC).
 * 
 * 4     20-01-00 16:36 Andrea
 * Added prototype of the new function.
 * 
 * 3     23-09-99 13:20 Andrea
 * Moved the LOG to the end of the file.
 * 
 * 1     22-09-99 18:30 Andrea
 * 
 *-----------------------------------------------------------------------*/
