/*...........................................................................*/
/*.                  File Name : SCREEN.CPP                                 .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "comlib.h"
#include "syslib.h"
#include "time.hpp"
#include "screen.hpp"

#include <string.h>
#include <stdlib.h>

cSCREEN::cSCREEN(void)
	: m_nLcdType(LCD_TYPE_COLOR)
{
	m_pLcd = new cLcdSpica();
}

cSCREEN::~cSCREEN(void)
{
}

cLCDCON *cSCREEN::GetLcdController(void)
{
	return(m_pLcd);
}

int cSCREEN::GetLcdType(void)
{
	return(m_nLcdType);
}

void cSCREEN::FillScreen(COLORT Color)
{
	m_pLcd->FillScreen(Color);
}

void cSCREEN::Plot(DWORD X,DWORD Y,COLORT Color)
{
	m_pLcd->Plot(X,Y,Color);
}

/* 2014.05.01. 640x480 Han */
void cSCREEN::SingleLine(int X1,int Y1,int X2,int Y2,COLORT Color)
{
	m_pLcd->SingleLine(X1,Y1,X2,Y2,Color);
}

void cSCREEN::Line(int X1,int Y1,int X2,int Y2,COLORT Color)
{
	m_pLcd->Line(X1,Y1,X2,Y2,Color);
}

void cSCREEN::Rect(int left, int top, int right, int bottom,COLORT Color)
{
	m_pLcd->Rect(left,top,right,bottom,Color);
}

void cSCREEN::FillRect(int left, int top, int right, int bottom,COLORT Color)
{
	m_pLcd->FillRect(left,top,right,bottom,Color);
}

void cSCREEN::PatternRect(int left, int top, int right, int bottom,COLORT Color)
{
	m_pLcd->PatternRect(left,top,right,bottom,Color);
}

void cSCREEN::Triangle(int x, int y, int bUP,COLORT Color)
{
	m_pLcd->Triangle(x,y,bUP,Color);
}

void cSCREEN::Triangle(int x, int y, int nW, int nH, int nDir,COLORT Color)
{
	m_pLcd->Triangle(x,y,nW,nH,nDir,Color);
}

void cSCREEN::DrawIconUTC(int x, int y)
{
	m_pLcd->DrawIconUTC(x,y);
}

void cSCREEN::DrawIconClosedLetter(int x, int y, COLORT Color)
{
	m_pLcd->DrawIconClosedLetter(x,y,Color);
}

void cSCREEN::DrawIconOpendLetter(int x, int y, COLORT Color)
{
	m_pLcd->DrawIconOpendLetter(x,y,Color);
}

void cSCREEN::Circle(int nPointX,int nPointY,int nRadius,COLORT Color)
{
	m_pLcd->Circle(nPointX,nPointY,nRadius,Color);
}

void cSCREEN::FloodFill(int nSeedX,int nSeedY,COLORT Color)
{
	m_pLcd->FloodFill(nSeedX,nSeedY,Color);
}

void cSCREEN::WriteStr(const CHAR *pStr,int X,int Y,int nMode,int nSize,int nDontFillBackColor)
{
	m_pLcd->WriteStr(pStr,X,Y,nMode,nSize,nDontFillBackColor);
}

BYTE *cSCREEN::GetScreenStartAddress(void)
{
	return(m_pLcd->GetScreenStartAddress());
}

DWORD cSCREEN::GetDrawingAddr(void)
{
	return m_pLcd->GetDrawingAddr();
}
void cSCREEN::SaveScreen(UCHAR *pScreen)
{
	m_pLcd->SaveScreen(pScreen);
}

void cSCREEN::SaveScreen(UCHAR *pScreen,int nLeft,int nTop,int nRight,int nBottom)
{
	m_pLcd->SaveScreen(pScreen,nLeft,nTop,nRight,nBottom);
}

void cSCREEN::RestoreScreen(const UCHAR *pScreen)
{
	m_pLcd->RestoreScreen(pScreen);
}

void cSCREEN::RestoreScreen(UCHAR *pScreen,int nLeft,int nTop,int nRight,int nBottom)
{
	m_pLcd->RestoreScreen(pScreen,nLeft,nTop,nRight,nBottom);
}

void cSCREEN::SetScreenBackColor(int nBackColor)
{
	m_pLcd->SetScreenBackColor(nBackColor);
}

void cSCREEN::SetLcdOnOff(int nOnOff)
{
	m_pLcd->SetLcdOnOff(nOnOff);
}

void cSCREEN::SetPaletteData(const DWORD *pPalData)
{
	m_pLcd->LcdSetPalData(pPalData);
}

void cSCREEN::SetLcdContrast(int nPercent, int nOffset)
{
	m_pLcd->SetLcdContrast(nPercent, nOffset);
}

void cSCREEN::SetLcdBright(int nPercent)
{
	m_pLcd->SetLcdBright(nPercent);
}

int cSCREEN::GetScrXSize(void)
{
	return(m_pLcd->GetScrXSize());
}

int cSCREEN::GetScrYSize(void)
{
	return(m_pLcd->GetScrYSize());
}

int cSCREEN::GetScrPitch(void)
{
	return(m_pLcd->GetScrPitch());
}

int cSCREEN::GetScrDotPerByte(void)
{
	return(m_pLcd->GetScrDotPerByte());
}

COLORT cSCREEN::SetForeColor(COLORT Color)
{
	return(m_pLcd->SetForeColor(Color));
}
COLORT cSCREEN::GetForeColor(void)
{
	return(m_pLcd->GetForeColor());
}

COLORT cSCREEN::SetBackColor(COLORT Color)
{
	return(m_pLcd->SetBackColor(Color));
}

COLORT cSCREEN::GetBackColor(void)
{
	return(m_pLcd->GetBackColor());
}

void cSCREEN::SetLanguageMode(int nMode)
{
	m_pLcd->SetLanguageMode(nMode);
}

/* 2014.05.01. 640x480 Han */
void cSCREEN::MapDotThickLine(int X1,int Y1,int X2,int Y2,COLORT Color,int nThick,int nDrawDot,int nSkipDot)
{
	m_pLcd->MapDotThickLine(X1, Y1, X2, Y2, Color, nThick, nDrawDot, nSkipDot);
}

void cSCREEN::DotThickLine(int X1,int Y1,int X2,int Y2,COLORT Color,int nThick,int nDrawDot,int nSkipDot)
{
	m_pLcd->DotThickLine(X1, Y1, X2, Y2, Color, nThick, nDrawDot, nSkipDot);
}

void cSCREEN::FillTriangle(int xi1, int yi1, int xi2, int yi2, int xi3, int yi3, COLORT color)
{
	m_pLcd->FillTriangle(xi1, yi1, xi2, yi2, xi3, yi3, color);
}

void cSCREEN::QuickScan(int x, int y, int size, COLORT color)
{
	m_pLcd->QuickScan(x, y, size, color);
}
/*  2012.02.22 */
void cSCREEN::SetChineseMsg(char langmode)
{
	m_pLcd->SetCnMsgMode(langmode);
}

FONT *cSCREEN::SetFont(FONT *pFont)
{
	FONT *pFnt = NULL;
	pFnt = m_pLcd->SetFont(pFont);
	return pFnt;
}

FONT *cSCREEN::GetFont()
{
	FONT *pFnt = NULL;
	pFnt = m_pLcd->GetFont();
	return pFnt;
}

int cSCREEN::PutChar(int x, int y, const HWORD ch)
{
	return m_pLcd->PutChar(x,y,ch);
}

FONT  *cSCREEN::GetFontStart(FONT *pFont, HWORD ch)
{
	return m_pLcd->GetFontStart(pFont, ch);
}

int cSCREEN::GetFontImage(HWORD ch)
{
	return m_pLcd->GetFontImage(ch);
}

int cSCREEN::GetStringFontWidth(const HWORD *pszText, int nLen)
{
	return m_pLcd->GetStringFontWidth(pszText, nLen);
}

int cSCREEN::GetCharFontWidth(HWORD ch)
{

	return m_pLcd->GetCharFontWidth(ch);
}

int cSCREEN::GetFontHeight()
{
	return m_pLcd->GetFontHeight();
}
	
int cSCREEN::GetFontHeight(HWORD ch)
{
	return m_pLcd->GetFontHeight(ch);
}

void cSCREEN::TextOut(int x, int y, HWORD ch)
{
	m_pLcd->TextOut(x, y, ch);
}

void cSCREEN::TextOut(int x, int y, const HWORD *pszText)
{
	m_pLcd->TextOut(x, y, pszText);
}

void cSCREEN::TextOut(int x, int y, const HWORD *pszText, COLORREF crBorder)
{
	m_pLcd->TextOut(x, y, pszText, crBorder);
}

void cSCREEN::DrawText(int nX, int nY, const HWORD *pwStr, COLORREF txtColor, COLORREF outColor, bool bOutLine)
{
	m_pLcd->DrawText(nX, nY, pwStr, txtColor, outColor, bOutLine);
}

void cSCREEN::DrawText2(int nX, int nY, const HWORD *pwStr, COLORREF txtColor, COLORREF outColor, COLORREF backColor, bool bFillBack, bool bOutLine)
{
	m_pLcd->DrawText2(nX, nY, pwStr, txtColor, outColor, backColor, bFillBack, bOutLine);		
}

void cSCREEN::DrawBitMap(int nScrW,int nScrH,int nXPos,int nYPos,UCHAR * pSrc,COLORREF trans,int nRotAngle)
{
	m_pLcd->DrawBitMap(nScrW,nScrH,nXPos,nYPos,pSrc,trans,nRotAngle);
}

void cSCREEN::GrGetBlendedScrnColor(int nScrnX,int nScrnY,UCHAR *pR,UCHAR *pG,UCHAR *pB)
{
	m_pLcd->GrGetBlendedScrnColor(nScrnX,nScrnY,pR,pG,pB);
}
