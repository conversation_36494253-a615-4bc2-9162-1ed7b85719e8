#include <stdio.h>
#include "MaintenanceMenuWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "uart.hpp"
#include "downprg.hpp"

#include "AllConst.h"
#include "Resource.h"
#include "Font.h"
#include "UpdateTransPrg.hpp"

extern CDocMgr* g_pDocMgr;
extern cKEYBD*  G_pKeyBD;
extern cUART*   G_pUart0;
extern cUART*   G_pUart3;

CMaintenanceMenuWnd::CMaintenanceMenuWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID), m_nSelNum(1)
{
}

void CMaintenanceMenuWnd::DrawWnd(BOOL bRedraw /*TRUE*/)
{
	int nLangMode = g_pDocMgr->GetLangMode();

	CWnd::DrawWnd(bRedraw);

	DrawSubMenu(m_nSelNum);
	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
	DrawButton(1, (BYTE *)FK_EXIT[nLangMode]);

	EraseButton(2);
	EraseButton(3);
}

void CMaintenanceMenuWnd::DrawSubMenu(int nSelNum)
{
	int nLangMode = g_pDocMgr->GetLangMode();
	int i = 0;
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0, nYOffset = 0;
	UCHAR *pArrBmp = NULL;
	HWORD *pUniCodeStr = NULL;
	COLORT clrTxt;
	COLORT clrUpLine;
	COLORT clrDnLine;

	if(m_pScreen != NULL)
	{
		// Draw Menu
		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:
				pFont = &NewGulLim18bCJK;
				nYOffset = 2;
				break;

			case LANG_RUS:
				pFont = &MyriadPro30bRus;
				nYOffset = 2;
				break;
			
			default:
				pFont = &MyriadPro30bEng;
				nYOffset = 5;
				break;
		}
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		for(i = 0; i < MAX_MENU_BAR; i++)
		{
			if(m_nScheme == CS_DAY_BRIGHT)
			{
				clrUpLine = MNU_DAY_UP_LINE;
				clrDnLine = MNU_DAY_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrDayOn;
					clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
				}
				else
				{
					clrTxt = COLORSCHEME[m_nScheme].crFore;
					pArrBmp = G_BmpArrDayOff;
				}
			}
			else
			{
				clrUpLine = MNU_NIGHT_UP_LINE;
				clrDnLine = MNU_NIGHT_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrNightOn;
					clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
				}
				else
				{
					clrTxt = COLORSCHEME[m_nScheme].crFore;
					pArrBmp = G_BmpArrNightOff;
				}
			}
			
			if(nSelNum == (i+1))
			{
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crLetterIcon);	
				
			}
			else
			{
									
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crBack);	
			}

			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							clrUpLine);
			
			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							clrDnLine);

			nXPos = BASE_WND_AREA_X_POS + 5;
			nYPos = WND_BACK_Y_POS + i*MNU_BAR_H + (MENU_ITEM_H - nFontH)/2 + nYOffset;

			switch(i)
			{
				case 0:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MAINTENANCE_VERSION[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_VERSION[nLangMode];
					break;

				case 1:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MAINTENANCE_KEY[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_KEY[nLangMode];
					break;

				case 2:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MAINTENANCE_LCD[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_LCD[nLangMode];
					break;

				case 3:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MAINTENANCE_INPUT[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_INPUT[nLangMode];
					break;

				case 4:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MAINTENANCE_SECURITY[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_SECURITY[nLangMode];
					break;

				case 5:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MAINTENANCE_TP_TEST[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_TP_TEST[nLangMode];
					break;

				case 6:
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_MKD_UPDATE[nLangMode];
					break;

				case 7:
					pUniCodeStr = (HWORD *)MNU_MAINTENANCE_TRANS_UPDATE[nLangMode];
					break;
			}

			switch(i)
			{
				case 0:
				case 1:
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
				case 7:
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);	

					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;
			}
		}

		m_pScreen->SetFont(pOldFont);
	}
}

void CMaintenanceMenuWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
		case KBD_SCAN_CODE_LEFT:
			if( m_nSelNum <= 1 ) m_nSelNum = 8;
			else m_nSelNum--;
			DrawSubMenu(m_nSelNum);
			break;
		case KBD_SCAN_CODE_DOWN:
		case KBD_SCAN_CODE_RIGHT:
			if( m_nSelNum >= 8 ) m_nSelNum = 1;
			else m_nSelNum++;
			DrawSubMenu(m_nSelNum);
			break;
		default:
			break;
	}
}

void CMaintenanceMenuWnd::GlobalPassWord()
{

}

void CMaintenanceMenuWnd::ProgramDownload()
{
	cDPWNPRG *pDownPrg;

	cTIME::SetWatchDogDisable();
	pDownPrg = new cDPWNPRG(m_pScreen,G_pKeyBD,G_pUart0);
	pDownPrg->DownLoadProgram();
	delete pDownPrg;
	cTIME::SetWatchDogEnable();

	DrawWnd();
}

void CMaintenanceMenuWnd::UpdateTransponderPrg()
{
	CUpdateTransPrg *pUpdateTransPrg;
	cTIME::SetWatchDogDisable();

	pUpdateTransPrg = new CUpdateTransPrg(m_pScreen,G_pKeyBD,G_pUart0,G_pUart3);
	pUpdateTransPrg->StartUpdateTransPrg();
	delete pUpdateTransPrg;
	
	cTIME::SetWatchDogEnable();

	DrawWnd();
}

void CMaintenanceMenuWnd::SystemReset()
{
}

int CMaintenanceMenuWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);
	
	if( nResult == AL_YES )
	{
		
	}
	
	return nResult;
}
