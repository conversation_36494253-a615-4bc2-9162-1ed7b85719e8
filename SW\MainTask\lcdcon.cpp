/*...........................................................................*/
/*.                  File Name : LCDCON.CPP                                 .*/
/*.                                                                         .*/
/*.                       Date : 2004.02.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "syslib.h"
#include "comlib.h"
#include "lcdcon.hpp"

#include <string.h>

cLCDCON::cLCDCON(void)
{
      BYTE vIndexTableX[] = { 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10,11,12,13,14,15,16,17,18,19,0,0,0,0,0,0,0,0,0,0,0};
      BYTE vIndexTableY[] = { 0, 0, 0, 1, 2, 3, 4, 5, 0, 0, 6, 7, 8, 9,10,11, 0, 0,12,13,14,15,16,17, 0, 0,18,19,20,21,0,0};
      BYTE vIndexTableZ[] = { 0, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10,11,12,13,14,15,16, 0,17,18,19,20,21,22,23,24,25,26,27,0,0};
      BYTE vTypeTableX[]  = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 3, 3, 1, 2, 4, 4, 4, 2, 1, 3, 0};
      BYTE vTypeTableY[]  = { 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 7, 7, 7, 6, 6, 7, 7, 7, 6, 6, 7, 5};
      BYTE vTypeTableZ[]  = { 0, 0, 2, 0, 2, 1, 2, 1, 2, 3, 0, 2, 1, 3, 3, 1, 2, 1, 3, 3, 1, 1};

      memmove(m_vIndexTableX,vIndexTableX,sizeof(vIndexTableX));
      memmove(m_vIndexTableY,vIndexTableY,sizeof(vIndexTableY));
      memmove(m_vIndexTableZ,vIndexTableZ,sizeof(vIndexTableZ));
      memmove(m_vTypeTableX ,vTypeTableX ,sizeof(vTypeTableX ));
      memmove(m_vTypeTableY ,vTypeTableY ,sizeof(vTypeTableY ));
      memmove(m_vTypeTableZ ,vTypeTableZ ,sizeof(vTypeTableZ ));
}
cLCDCON::~cLCDCON(void)
{
}
void  cLCDCON::LoadOneFont(void **pFontTarget,DWORD dSize,const void *pFontSource)
{
	    DWORD TempX;
	    BYTE *pData;
	    BYTE  Buff[512 * 1024];

      *pFontTarget = new BYTE[dSize + 16];
      UnPackFileData((BYTE *)pFontSource,&pData,&TempX,NULL,Buff,sizeof(Buff));
      memmove(*pFontTarget,pData,dSize);
}
void  cLCDCON::GetHanImage(WORD wHanCode)
{
      WORD wH1,wH2,wH3;//wPos;
      WORD wTypeX,wTypeY,wTypeZ;
      BYTE *pSource;
      int  i;

      wH1 = (wHanCode >> 10) & 0x1f;
      wH2 = (wHanCode >>  5) & 0x1f;
      wH3 = wHanCode & 0x1f;
      wH1 = m_vIndexTableX[wH1];
      wH2 = m_vIndexTableY[wH2];
      wH3 = m_vIndexTableZ[wH3];
      wTypeX = wH3 ? m_vTypeTableY[wH2] : m_vTypeTableX[wH2];
      wTypeY = ((wH1 == 0 || wH1 == 1 || wH1 == 16) ? 0 : 1) + (wH3 ? 2 : 0);
      wTypeZ = m_vTypeTableZ[wH2];
      if (wH1)
         {
          pSource = m_pHan16x16FontX[wTypeX * 20 + wH1];
          memmove(m_pFontBuff,pSource,32);
         }
      else
          memset(m_pFontBuff,0x00,32);
      if (wH2)
         {
          pSource = m_pHan16x16FontY[wTypeY * 22 + wH2];
          for (i = 0;i < 32;i++)
               m_pFontBuff[i] |= *pSource++;
         }
      if (wH3)
         {
          pSource = m_pHan16x16FontZ[wTypeZ * 28 + wH3];
          for (i = 0;i < 32;i++)
               m_pFontBuff[i] |= *pSource++;
         }
}
void  cLCDCON::GetChiImage(WORD wChiCode)
{
      DWORD dChiPos;
      BYTE *pSource;

      dChiPos = (DWORD)((wChiCode >> 8) - 0xb0) * 94;
      dChiPos = dChiPos + (wChiCode & 0xFF) - 0xA1 + 4;
      pSource = m_pChi16x16Font[dChiPos];
      memmove(m_pFontBuff,pSource,32);
}

