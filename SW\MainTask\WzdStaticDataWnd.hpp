#ifndef __WIZARD_STATIC_DATA_WND_HPP__
#define __WIZARD_STATIC_DATA_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"

class CWzdStaticDataWnd : public CWnd {
protected:
	enum {
		FOCUS_MMSI = 0,
		FOCUS_IMO_NO,
		FOCUS_SHIP_NAME,
		FOCUS_CALL_SIGN
	};

	enum{
		ERR_NONE = 0,
		ERR_MMSI,
		ERR_IMO			
	};

	CEditCtrl  *m_pMMSIEdit;
	CEditCtrl  *m_pIMOEdit;
	CEditCtrl  *m_pShipNameEdit;
	CEditCtrl  *m_pCallSignEdit;
	
	int         m_nFocus;
	int			m_nErrType;
	
public:
	CWzdStaticDataWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
	virtual void OnKeyEvent(int nKey, DWORD nFlags);
	virtual void OnCursorEvent(int nState);
	void DrawControls();
	void DrawFuncBtn();
	virtual void DrawWnd(BOOL bRedraw = TRUE);
	virtual void OnActivate();

	void SaveStaticData();
	void InitStaticData();
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

	void SetFocus(int nFocus);
	int  GetFocus() { return m_nFocus; }
};

#endif	// End of __WIZARD_STATIC_DATA_WND_HPP__
