//
// MPEG1 Audio Layer-3 Main Interface
//
// MP3main.h
//

#ifndef __MP3MAIN_H__
#define __MP3MAIN_H__



#include "mp3dec.h"
#include "cmaptype.h"
#include "cm/dbase.h"


//#ifdef	MANAGE_MULTIMEDIA_SECTION
//#ifdef MP3_LIBRARY

typedef enum
{
	NoError,		
	NoFile,			
	NoMP3,			
	NoMemory,		
	UnknownError	
} Error;

// 10-Bands Equalizer
typedef struct
{
	SByte _60;							// 60Hz
	SByte _170;							// 170Hz
	SByte _310;							// 310Hz
	SByte _600;							// 600Hz
	SByte _1k;							// 1kHz
	SByte _3k;							// 3kHz
	SByte _6k;							// 6kHz
	SByte _12k;							// 12kHz
	SByte _14k;							// 14kHz
	SByte _16k;							// 16kHz
} EQ;



#define MAX_MP3_HEADER_DIM 82 // DISP =72 +10 IN MP3INIT

#ifdef __cplusplus
extern "C"
{
#endif


typedef struct
{
	Word FrameSize;				
	Long BufferingNumber;		
} sMp3Data;

typedef struct nsMp3Info
{
	SLong Displacement;
	Long NowPos;
	Long FileMp3Size;
	sMp3Data DecData;
//	sMPEG_DECODE_INFO MP3DIParams;
	sMPEG_DECODE_INFO MP3DI;
	Byte pFrameData[500];
	
}sMp3Info;


SLong CF95_Mp3SetHeaderDisplacement(SByte* pFrameData);
void CF95_Mp3Params(sMp3Info* Sound);
SLong CF95_Equalize(EQ eq);				
SLong CF95_msec2frame(Long msec, SByte* pFrameData);

#ifndef USE_OEM_DECODE_MP3
SLong CF95_Mp3DecodeStep(SByte* pSrcFile, void* Buffer, sMp3Data* DecData, SLong step );
#endif

void CF95_GetMP3Info(sMp3Info* Sound, sMultimediaElement* mmElement);//,sAttrInfoExp* attrinfo); //sBinaryImage* Sound, SLong startCartridgePosition);

PRE_EXPORT_H SLong IN_EXPORT_H cmGetMp3BlockDimension(sMp3Info* Sound);
PRE_EXPORT_H SLong IN_EXPORT_H cmGetNumOfMp3Block(sMp3Info* Sound);
PRE_EXPORT_H SWord IN_EXPORT_H cmGetMp3Channel(sMultimediaElement* mmElement);
PRE_EXPORT_H SLong IN_EXPORT_H cmGetMp3Frequency(sMultimediaElement* mmElement);
PRE_EXPORT_H void IN_EXPORT_H  cmGetBinaryMp3Info(sMp3Info* mp3Info, sMultimediaElement* mmElement,sAttrInfoExp* attrinfo);
PRE_EXPORT_H void IN_EXPORT_H cmMp3GetSoundBlock(sMp3Info* Sound,  sMultimediaElement* mmElement);

#ifndef USE_OEM_DECODE_MP3
PRE_EXPORT_H void IN_EXPORT_H cmMp3Decode(sMp3Info* Sound, Long* Mp3BlockBuffer, Word index);
#endif

//PRE_EXPORT_H void IN_EXPORT_H cmGetFileMp3Size(sMp3Info* mp3Info, sMultimediaElement* mmElement,sAttrInfoExp* attrinfo);


#ifdef __cplusplus
}
#endif
#endif