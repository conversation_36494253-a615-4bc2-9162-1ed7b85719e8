#include <stdio.h>
#include "ACS.hpp"

CAcs::CAcs() : CSentence()
{
}
    
CAcs::CAcs(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CAcs::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_ACS;
}

/******************************************************************************
*
* ACS - Channel management information Source
*
* $--ACS,x,xxxxxxxxx,hhmmss.ss,xx,xx,xxxx*hh<CR><LF>
*        | |         |         |  |  |
*        1 2         3         4  5  6
*
* 1. Sequence number , 0 to 9
* 2. MMSI of originator
* 3. UTC at receipt of regional operating settings
* 4. UTC day, 01 to 31
* 5. UTC month, 01 to 12
* 6. UTC year
*
******************************************************************************/
void CAcs::Parse()
{
	char szTemp[12];

	GetTalkerID(m_szTalkID);		// Add talk ID check : HSI 2012.05.07
	m_nSeqNumber   = GetFieldInteger(1);   // 0 to 9
	m_nMMSIOrig    = GetFieldMMSI(2);
	GetFieldString(3, m_szUTC);

	if( m_szUTC[0] != '\0' && strlen(m_szUTC) >= 6 )
	{
		strncpy(szTemp, m_szUTC, 2);
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCHour);
		m_nUTCHour = atoi(szTemp);

		strncpy(szTemp, m_szUTC+2, 2);
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCMin);
		m_nUTCMin = atoi(szTemp);

		strncpy(szTemp, m_szUTC+4, 2);
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCSec);
		m_nUTCSec = atoi(szTemp);
	}
	else
	{
		m_nUTCHour = 0;
		m_nUTCMin  = 0;
		m_nUTCSec  = 0;
	}

	GetFieldString(4,szTemp);
	if(szTemp[0]!= '\0')
	{
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCDay);
		m_nUTCDay = atoi(szTemp);
	}else
	{
		m_nUTCDay = 0;
	}

	GetFieldString(5,szTemp);
	if(szTemp[0]!= '\0')
	{
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCMonth);
		m_nUTCMonth = atoi(szTemp);
	}else
	{
		m_nUTCMonth = 0;
	}

	GetFieldString(6,szTemp);
	if(szTemp[0]!= '\0')
	{
		szTemp[4] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCYear);
		m_nUTCYear = atoi(szTemp);
	}else
	{
		m_nUTCYear = 0;
	}
}

void CAcs::GetPlainText(char *pszPlainText)
{
	char szTemp[128];

	pszPlainText[0] = '\0';

	if( m_nSeqNumber != NMEA_NULL_INTEGER ) {
		sprintf((char *)szTemp, "Sequence number                              : %d\n", m_nSeqNumber);
		strcat((char *)pszPlainText, (char *)szTemp);
	}

	if( m_nMMSIOrig != NMEA_NULL_INTEGER ) {
		sprintf((char *)szTemp, "MMSI of originator                           : %d\n", m_nMMSIOrig);
		strcat((char *)pszPlainText, (char *)szTemp);
	} else {
		sprintf((char *)szTemp, "MMSI of originator                           : BROADCAST\n");
		strcat((char *)pszPlainText, (char *)szTemp);
	}

	sprintf((char *)szTemp, "UTC at receipt of regional operating settings : %02d:%02d:%02d\n",
		m_nUTCHour, m_nUTCMin, m_nUTCSec);
	strcat(pszPlainText, szTemp);

	if( m_nUTCDay != NMEA_NULL_INTEGER ) {
		sprintf((char *)szTemp, "UTC day, 01 to 31                            : %d\n", m_nUTCDay);
		strcat(pszPlainText, szTemp);
	}

	if( m_nUTCMonth != NMEA_NULL_INTEGER ) {
		sprintf((char *)szTemp, "UTC month, 01 to 12                          : %d\n", m_nUTCMonth);
		strcat(pszPlainText, szTemp);
	}

	if( m_nUTCYear != NMEA_NULL_INTEGER ) {
		sprintf((char *)szTemp, "UTC year                                     : %d\n", m_nUTCYear);
		strcat(pszPlainText, szTemp);
	}
}
