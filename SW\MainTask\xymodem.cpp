#include <stdio.h>
#include <string.h>
#include "keybd.hpp"
#include "Uart.hpp"
#include "Lcdcon.hpp"
#include "UpdateTransPrg.hpp"

//===========================================================================
#define CRC_POLY             	0x1021
//===========================================================================
#define PACKET_SEQNO_INDEX      (1)
#define PACKET_SEQNO_COMP_INDEX (2)

#define PACKET_HEADER			(5)
#define PACKET_TRAILER          (2)
#define PACKET_OVERHEAD         (PACKET_HEADER + PACKET_TRAILER)
#define PACKET_SIZE             (128)
#define PACKET_1K_SIZE          (1024)
#define PACKET_TIMEOUT          (1)

#define FILE_NAME_LENGTH       	(64)
#define FILE_SIZE_LENGTH       	(16)

#define ASC_SOH              	(0x01)
#define ASC_STX              	(0x02)
#define ASC_EOT              	(0x04)
#define ASC_ACK              	(0x06)
#define ASC_NAK              	(0x15)
#define ASC_CAN              	(0x18)
#define ASC_CRC              	(0x43)

#define MAX_ERRORS              (5)
//===========================================================================
#define MODEM_ERROR_NO_ERROR      1
#define MODEM_ERROR_GENERAL       0
//===========================================================================

extern cKEYBD	*G_pKeyBD;
extern cUART    *G_pUart3;
static int   ReadDataFromUART(void *pDataX, int nSize, cUART *pUart)
{
	int   nDataX, nTempX;
	DWORD dTickX;
	UCHAR *pDataY = (UCHAR *)pDataX;

	nTempX = 0;
	dTickX = SysGetSystemTimer();
	while (SysGetDiffTimeMili(dTickX) <= 2000)
	{
		nDataX = pUart->GetComData();
		if (nDataX != UART_NULL_CHAR)
		{
			*pDataY++ = nDataX;
			++nTempX;
		}

		if (nTempX >= nSize)
			break;
	}

	return(nTempX);
}

static int WriteDataToUART(void *pDataX, int nSize, int nWaitMode, cUART *pUart)
{
	DWORD dTickX;

	pUart->WriteComData((BYTE *)pDataX, nSize);

	if (nWaitMode == 0)
	{
		return(nSize);
	}			

	dTickX = SysGetSystemTimer();
	while (SysGetDiffTimeMili(dTickX) <= 2000)
	{
		if (pUart->IsSendingData() == 0)
			break;
	}

	return(nSize);
}

static void  WriteCharToUART(UCHAR bData, cUART *pUart)
{
	WriteDataToUART(&bData, 1, 0,pUart);
}

static int   ReadCharFromUART(int nTimeOut, cUART *pUart)
{
	UCHAR bData;

	if (ReadDataFromUART(&bData, 1,pUart) == 1)
	{
		return(bData);
	}		

	return(-99);
}

static HWORD crc16(const UCHAR *buf, DWORD count)
{
	HWORD crc = 0;
	int   i;

	while (count--)
	{
		crc = crc ^ *buf++ << 8;

		for (i = 0; i < 8; i++)
		{
			if (crc & 0x8000)
			{
				crc = crc << 1 ^ CRC_POLY;
			}				
			else
			{
				crc = crc << 1;
			}				
		}
	}

	return(crc);
}

static const char *u32_to_str(DWORD val)
{
	static char num_str[16];

	sprintf(num_str, "%08d\n", val);

	return(num_str);
}


static void send_packet(UCHAR *data, unsigned short block_no, cUART *pUart)
{
	int   count, crc, packet_size;
	UCHAR vAllData[2048];
	int   nPos;

	// We use a short packet for block 0 - all others are 1K
	if (block_no == 0)
		packet_size = PACKET_SIZE;
	else
		packet_size = PACKET_1K_SIZE;

	crc = crc16(data, packet_size);

	// 128 byte packets use ASC_SOH, 1K use ASC_STX
	vAllData[0] = (block_no == 0) ? ASC_SOH : ASC_STX;
	vAllData[1] = (UCHAR)(block_no & 0x00FF);
	vAllData[2] = ~((UCHAR)(block_no & 0x00FF));
	vAllData[3] = (UCHAR)((block_no  & 0xFF00)>>8);
	vAllData[4] = ~((UCHAR)((block_no  & 0xFF00)>>8));
	nPos = PACKET_HEADER;

	for (count = 0; count < packet_size; count++)
	{
		vAllData[nPos++] = data[count];
	}		

	vAllData[nPos++] = (crc >> 8) & 0xFF;
	vAllData[nPos++] = crc & 0xFF;

	//G_pUart3->OutputDbgMsg("[SendPacket]Bno=0x%04x, [0]0x%02x, [1]0x%02x, [2]0x%02x, [3]0x%02x, [4]0x%02x\r\n",block_no,vAllData[0], vAllData[1],vAllData[2],vAllData[3],vAllData[4]);
	WriteDataToUART(vAllData, nPos, 1, pUart);
}


// Send block 0 (the filename block). filename might be truncated to fit.
static void send_packet0(char* filename, DWORD size, CUpdateTransPrg *pWnd, cLCDCON *pLcdCtrl, cUART *pUart)
{
	int nDrawingPage = 0;
	DWORD count = 0;
	UCHAR block[PACKET_SIZE];
	const char* num;
	char strTemp[128];

	if (filename)
	{
		while (*filename && (count < (PACKET_SIZE    - FILE_SIZE_LENGTH - 2)))
		{
			block[count++] = *filename++;
		}			

		block[count++] = 0;

		num = u32_to_str(size);

		while (*num)
		{
			block[count++] = *num++;
		}			
	}

	while (count < PACKET_SIZE)
	{
		block[count++] = 0;
	}			

	nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
	pLcdCtrl->SetDrawingPage(nDrawingPage);
	sprintf(strTemp,"[Block0]%s size=%d",block,count);
	pWnd->DrawStatusInfo(FALSE,(BYTE *)strTemp);
		
	send_packet(block, 0,pUart);
}

static int  send_data_packets(UCHAR* data, DWORD size, CUpdateTransPrg *pWnd,cLCDCON *pLcdCtrl,cUART *pUart)
{
	unsigned short blockno = 1;
	DWORD send_size;
	int   ch;
	int nDrawingPage = 0;
	DWORD dTmpTick = 0;
	
	char strTemp[128];

	pWnd->SetTotFileSize(size);
	
	while (size > 0)
	{
		if (size > PACKET_1K_SIZE)
			send_size = PACKET_1K_SIZE;
		else
			send_size = size;

		while( pUart->GetComData() != -1)
		{
			pUart->GetComData();
		}

#if 0
		ch = -1;
		do{
			send_packet(data, blockno,pUart);
			ch = ReadCharFromUART(PACKET_TIMEOUT,pUart);
			if (ch == ASC_ACK)
			{
				blockno++;
				data += send_size;
				size -= send_size;
			}
		}while(ch != ASC_ACK);
#else
		send_packet(data, blockno,pUart);

		dTmpTick = SysGetSystemTimer();
		while (SysGetDiffTimeMili(dTmpTick) <= 2000)
		{
			ch = ReadCharFromUART(PACKET_TIMEOUT,pUart);

			if (ch == ASC_ACK)
			{
				blockno++;
				data += send_size;
				size -= send_size;
				break;
			}
			else
			{
			//               if ((ch == ASC_CAN) || (ch == -1))
			//                   return(MODEM_ERROR_GENERAL);
			}
		}
#endif		

		nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
		pLcdCtrl->SetDrawingPage(nDrawingPage);
		sprintf((char *)strTemp,"Send Packet(Send=%d, Remain=%d Bytes)[ch=0x%02x]......",send_size,size,ch);
		pWnd->DrawStatusInfo(FALSE,(BYTE *)strTemp);
		
		pWnd->SetCurUploadFileSize(pWnd->GetTotFileSize() - size);
		pWnd->SetRemainFileSize(size);
		pWnd->DrawProgress(FALSE,pWnd->GetTotFileSize(),pWnd->GetCurUploadFileSize(),pWnd->GetRemainFileSize());
		
		pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
			

		dTmpTick = SysGetSystemTimer();
		while (SysGetDiffTimeMili(dTmpTick) <= 10);
			
		//dTmpTick = SysGetSystemTimer();
		//while (SysGetDiffTimeMili(dTmpTick) <= 200);
		//pUpdateWnd->DspDownLeft(size);
	}

	/*
	do
	{
		WriteCharToUART(ASC_EOT);
		ch = ReadCharFromUART(PACKET_TIMEOUT);
	} while ((ch != ASC_ACK) && (ch != -1));
	*/
	WriteCharToUART(ASC_EOT,pUart);
	WriteCharToUART(ASC_EOT,pUart);

	/*
	// Send last data packet
	if (ch == ASC_ACK)
	{
		ch = ReadCharFromUART(PACKET_TIMEOUT);
		if (ch == ASC_CRC)
		{
			do
			{
				send_packet0(0, 0);
				ch = ReadCharFromUART(PACKET_TIMEOUT);
			} while ((ch != ASC_ACK) && (ch != -1));
		}
	}
	*/

	nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
	pLcdCtrl->SetDrawingPage(nDrawingPage);
	pWnd->DrawStatusInfo(FALSE,(BYTE *)"Send packet done......");
	pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());

	return(MODEM_ERROR_NO_ERROR);
}

int xymodem_send(const char *filename, UCHAR *pFileData, int nFileSize, int wait, CUpdateTransPrg *pWnd, cLCDCON *pLcdCtrl, cUART *pUart, int nUpdateFType)
{
	int	 ch, crc_nak = 1;
	int	 i, nRxSize, nDataX, nFound = 0;
	char	 vTextData[128];
	char     strTmp[128];
	char	 vRecvData[256];
	DWORD  dTickX;
	DWORD  dTmpTick = 0;
	int nDrawingPage = 0;

	nRxSize = 0;
	strcpy(vTextData, "$SYRST,SPA-900T*71\r\n");

	nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
	pLcdCtrl->SetDrawingPage(nDrawingPage);
	pWnd->DrawStatusInfo(FALSE,(BYTE *)"Request YModem connection....");
	pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());	
	
	for (i = 0; i < 5; i++)
	{
		WriteDataToUART(vTextData, strlen(vTextData), 1,pUart);

		dTickX = SysGetSystemTimer();

		nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
		pLcdCtrl->SetDrawingPage(nDrawingPage);
		pWnd->DrawStatusInfo(FALSE,(BYTE *)"Waiting connection response....");
		pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
	
		while (SysGetDiffTimeMili(dTickX) <= 2000)
		{
			nDataX = pUart->GetComData();
			if (nDataX != UART_NULL_CHAR)
			{
				if (nRxSize || (nRxSize == 0 && nDataX == '$'))
				{
					vRecvData[nRxSize++] = nDataX;
				}					
			}
			if (nRxSize == 15)
			{
				vRecvData[nRxSize] = 0x00;
				nRxSize = 0;

				if (strcmp(vRecvData, "$SYRST,SPA-900T") == 0)
				{
					nFound = 1;
					nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
					pLcdCtrl->SetDrawingPage(nDrawingPage);
					pWnd->DrawStatusInfo(FALSE,(BYTE *)"Success to connect....");
					pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
					break;
				}
			}
		}

		if (nFound == 1)
			break;
	}

	if (nFound == 0)
	{
		nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
		pLcdCtrl->SetDrawingPage(nDrawingPage);
		pWnd->DrawStatusInfo(FALSE,(BYTE *)"Connection fail....");
		pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
		
		return(MODEM_ERROR_GENERAL);
	}			

	nRxSize = 0;
	dTickX = SysGetSystemTimer();
	while (SysGetDiffTimeScnd(dTickX) <= 1000)
	{
		nDataX = pUart->GetComData();
		if (nDataX == ASC_CRC)
			++nRxSize;

		if (nRxSize >= 3)
			break;
	}

	while( pUart->GetComData() != -1)
	{
		pUart->GetComData();
	}

	if (nRxSize >= 3)
	{
		nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
		pLcdCtrl->SetDrawingPage(nDrawingPage);
		pWnd->DrawStatusInfo(FALSE,(BYTE *)"Start to send packet0....");
		pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
		do
		{
			send_packet0((char *)filename, nFileSize,pWnd,pLcdCtrl,pUart);
			// When the receiving program receives this block and successfully
			// opened the output file, it shall acknowledge this block with an ACK
			// character and then proceed with a normal XMODEM file transfer
			// beginning with a "C" or ASC_NAK tranmsitted by the receiver.

			if(nUpdateFType == CUpdateTransPrg::UPDATE_FTYPE_MAIN)
			{
				dTmpTick = SysGetSystemTimer();
				while (SysGetDiffTimeMili(dTmpTick) <= 20000)
				{
					nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
					pLcdCtrl->SetDrawingPage(nDrawingPage);
					
					sprintf((char *)strTmp,"Wait until erasing flash....[%ds]",SysGetDiffTimeMili(dTmpTick)/1000);
					pWnd->DrawStatusInfo(FALSE,(BYTE *)strTmp);
					pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
				}
			}
			else if(nUpdateFType == CUpdateTransPrg::UPDATE_FTYPE_SUB)
			{
				dTmpTick = SysGetSystemTimer();
				while (SysGetDiffTimeMili(dTmpTick) <= 10000)
				{
					nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
					pLcdCtrl->SetDrawingPage(nDrawingPage);
					
					sprintf((char *)strTmp,"Wait until erasing flash....[%ds]",SysGetDiffTimeMili(dTmpTick)/1000);
					pWnd->DrawStatusInfo(FALSE,(BYTE *)strTmp);
					pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
				}
			}
			
			dTmpTick = SysGetSystemTimer();
			while (SysGetDiffTimeMili(dTmpTick) <= 6000)
			{
				ch = ReadCharFromUART(PACKET_TIMEOUT,pUart);
				if (ch == ASC_ACK)
				{
					break;
				}					
			}

			dTmpTick = SysGetSystemTimer();
			while (SysGetDiffTimeMili(dTmpTick) <= 6000)
			{
#if 0
				nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
				pLcdCtrl->SetDrawingPage(nDrawingPage);
				
				sprintf(strTmp,"Waiting Ack....[0x%02x]",ch);
				pWnd->DrawStatusInfo(FALSE,strTmp);
				pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
#endif				
				{
					ch = ReadCharFromUART(PACKET_TIMEOUT,pUart);
#if 0
					nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
					pLcdCtrl->SetDrawingPage(nDrawingPage);
					sprintf(strTmp,"Waiting CRC....[0x%02x]",ch);
					pWnd->DrawStatusInfo(FALSE,strTmp);
					pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
#endif					
				
					if (ch == ASC_CRC)
					{
#if 0					
						nDrawingPage = 1 - pLcdCtrl->GetViewingPage();
						pLcdCtrl->SetDrawingPage(nDrawingPage);
						pWnd->DrawStatusInfo(FALSE,"Start to send program....");
						pLcdCtrl->SetViewingPage(pLcdCtrl->GetDrawingPage());
#endif						
			
						return(send_data_packets(pFileData, nFileSize, pWnd,pLcdCtrl,pUart));
					}					
				}
			}
		} while(1);
	}
	
	WriteCharToUART(ASC_CAN,pUart);
	WriteCharToUART(ASC_CAN,pUart);

	return(MODEM_ERROR_GENERAL);
}




