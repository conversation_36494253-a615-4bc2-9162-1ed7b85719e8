#include <stdio.h>

#include "InterrogationWnd.hpp"
#include "DocMgr.hpp"
#include "DblList.h"
#include "ship.hpp"
#include "keybd.hpp"
#include "NMEA/AIR.hpp"

#include "SamMapConst.h"
#include "Comlib.h"
#include "Uart.hpp"

extern cUART *G_pUart3;
extern CDocMgr         *g_pDocMgr;

CInterrogationWnd::CInterrogationWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	m_pMMSI1 = new CComboCtrl(pScreen);
	m_pMMSI2 = new CComboCtrl(pScreen);
	m_pMsg1  = new CComboCtrl(pScreen);
	m_pMsg3  = new CComboCtrl(pScreen);
	m_pMsg2  = new CComboCtrl(pScreen);

//	if( m_pScreen->GetLcdType() == LCD_TYPE_MONO )
	{
		m_pMMSI1->Create(53,  65, 200, 15, 32);
		m_pMsg1->Create( 53,  85, 147, 15, 22);
		m_pMsg2->Create( 53, 105, 147, 15, 22);

		m_pMMSI2->Create(53, 150, 200, 15, 32, 0);
		m_pMsg3->Create( 53, 170, 147, 15, 22, 0);
	}
//	else if( m_pScreen->GetLcdType() == LCD_TYPE_COLOR )
//	{
//		m_pMMSI1->Create(53,  65, 200, 15, 32);
//		m_pMsg1->Create( 53,  85, 147, 15, 22);
//		m_pMsg2->Create( 53, 105, 147, 15, 22);
//
//		m_pMMSI2->Create(53, 150, 200, 15, 32, 0);
//		m_pMsg3->Create( 53, 170, 147, 15, 22, 0);
// 	}

	m_pMsg1->AddItem((BYTE *)"- NONE -");
	m_pMsg1->AddItem((BYTE *)"POSITION");
	m_pMsg1->AddItem((BYTE *)"STATIC AND VOYAGE DATA");

	m_pMsg2->AddItem((BYTE *)"- NONE -");
	m_pMsg2->AddItem((BYTE *)"POSITION");
	m_pMsg2->AddItem((BYTE *)"STATIC AND VOYAGE DATA");

	m_pMsg3->AddItem((BYTE *)"- NONE -");
	m_pMsg3->AddItem((BYTE *)"POSITION");
	m_pMsg3->AddItem((BYTE *)"STATIC AND VOYAGE DATA");
}

void CInterrogationWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
	case KBD_SCAN_CODE_UP:
		if( m_nFocus == FOCUS_MMSI1 && m_pMMSI1->IsExpand() ) {
			m_pMMSI1->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MSG1 && m_pMsg1->IsExpand() ) {
			m_pMsg1->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MSG2 && m_pMsg2->IsExpand() ) {
			m_pMsg2->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MSG3 && m_pMsg3->IsExpand() ) {
			m_pMsg3->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MMSI2 && m_pMMSI2->IsExpand() ) {
			m_pMMSI2->OnKeyEvent(nKey, nFlags);
			break;
		}

		if( m_nFocus > 0 ) {
			m_nFocus--;
			DrawWnd(0);
		}
		break;
	case KBD_SCAN_CODE_DOWN:
		if( m_nFocus == FOCUS_MMSI1 && m_pMMSI1->IsExpand() ) {
			m_pMMSI1->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MSG1 && m_pMsg1->IsExpand() ) {
			m_pMsg1->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MSG2 && m_pMsg2->IsExpand() ) {
			m_pMsg2->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MSG3 && m_pMsg3->IsExpand() ) {
			m_pMsg3->OnKeyEvent(nKey, nFlags);
			break;
		} else if( m_nFocus == FOCUS_MMSI2 && m_pMMSI2->IsExpand() ) {
			m_pMMSI2->OnKeyEvent(nKey, nFlags);
			break;
		}
		
		if( m_nFocus < 4 ) {
			m_nFocus++;
			DrawWnd(0);
		}
		break;
	default:
		switch( m_nFocus )
		{
		case FOCUS_MMSI1:
			m_pMMSI1->OnKeyEvent(nKey, nFlags);
			break;
		case FOCUS_MSG1:
			m_pMsg1->OnKeyEvent(nKey, nFlags);
			break;
		case FOCUS_MSG2:
			m_pMsg2->OnKeyEvent(nKey, nFlags);
			break;
		case FOCUS_MMSI2:
			m_pMMSI2->OnKeyEvent(nKey, nFlags);
			break;
		case FOCUS_MSG3:
			m_pMsg3->OnKeyEvent(nKey, nFlags);
			break;
		}
		break;
	}
}

void CInterrogationWnd::DrawWnd(BOOL bRedraw/*TRUE*/)
{
	int nLangMode = g_pDocMgr->GetLangMode();

	CWnd::DrawWnd(bRedraw);

	if( bRedraw )
	{
		if( m_pScreen->GetLcdType() == LCD_TYPE_MONO )
		{
			m_pScreen->WriteStr(STR_STATION1[nLangMode], 15, 45, TEXT_MODE_INVERSE, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MMSI",LNG_ENG), 15,  65, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG1",LNG_ENG), 15,  85, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG2",LNG_ENG), 15, 105, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);

			m_pScreen->WriteStr(STR_STATION2[nLangMode], 15, 130, TEXT_MODE_INVERSE, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MMSI",LNG_ENG), 15, 150, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG1",LNG_ENG), 15, 170, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
		}
		else if( m_pScreen->GetLcdType() == LCD_TYPE_COLOR )
		{
			COLORT crOldColor = m_pScreen->SetForeColor(COLORSCHEME[m_nScheme].crCaptionShade);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MMSI",LNG_ENG), 16,  66, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG1",LNG_ENG), 16,  86, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG2",LNG_ENG), 16, 106, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);

			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MMSI",LNG_ENG), 16, 151, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG1",LNG_ENG), 16, 171, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD);
			m_pScreen->SetForeColor(crOldColor);

			m_pScreen->WriteStr(STR_STATION1[nLangMode], 15, 45, TEXT_MODE_INVERSE, FONT_ENG_16X08_BOLD);
			m_pScreen->Line(15, 61, 95, 61, COLORSCHEME[m_nScheme].crShadow);
			m_pScreen->Line(95, 45, 95, 61, COLORSCHEME[m_nScheme].crShadow);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MMSI",LNG_ENG), 15,  65, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD, 1);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG1",LNG_ENG), 15,  85, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD, 1);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG2",LNG_ENG), 15, 105, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD, 1);

			m_pScreen->WriteStr(STR_STATION2[nLangMode], 15, 130, TEXT_MODE_INVERSE, FONT_ENG_16X08_BOLD);
			m_pScreen->Line(15, 146, 95, 146, COLORSCHEME[m_nScheme].crShadow);
			m_pScreen->Line(95, 130, 95, 146, COLORSCHEME[m_nScheme].crShadow);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MMSI",LNG_ENG), 15, 150, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD, 1);
			m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)"MSG1",LNG_ENG), 15, 170, TEXT_MODE_NORMAL, FONT_ENG_16X08_BOLD, 1);
		}

		DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
		DrawButton(1, (BYTE *)FK_SEND[nLangMode]);

		EraseButton(2);
		EraseButton(3);
	}

	switch( m_nFocus )
	{
	case FOCUS_MMSI1:
		m_pMMSI1->SetFocus(1);
		m_pMsg1->SetFocus(0);
		m_pMsg2->SetFocus(0);
		m_pMMSI2->SetFocus(0);
		m_pMsg3->SetFocus(0);
		break;
	case FOCUS_MSG1:
		m_pMMSI1->SetFocus(0);
		m_pMsg1->SetFocus(1);
		m_pMsg2->SetFocus(0);
		m_pMMSI2->SetFocus(0);
		m_pMsg3->SetFocus(0);
		break;
	case FOCUS_MSG2:
		m_pMMSI1->SetFocus(0);
		m_pMsg1->SetFocus(0);
		m_pMsg2->SetFocus(1);
		m_pMMSI2->SetFocus(0);
		m_pMsg3->SetFocus(0);
		break;
	case FOCUS_MMSI2:
		m_pMMSI1->SetFocus(0);
		m_pMsg1->SetFocus(0);
		m_pMsg2->SetFocus(0);
		m_pMMSI2->SetFocus(1);
		m_pMsg3->SetFocus(0);
		break;
	case FOCUS_MSG3:
		m_pMMSI1->SetFocus(0);
		m_pMsg1->SetFocus(0);
		m_pMsg2->SetFocus(0);
		m_pMMSI2->SetFocus(0);
		m_pMsg3->SetFocus(1);
		break;
	}

	m_pMMSI1->DrawWnd();
	m_pMsg1->DrawWnd();
	m_pMsg2->DrawWnd();
	m_pMMSI2->DrawWnd();
	m_pMsg3->DrawWnd();

	if( m_pMMSI1->IsExpand() )      m_pMMSI1->DrawWnd();
	else if( m_pMsg1->IsExpand() )  m_pMsg1->DrawWnd();
	else if( m_pMsg2->IsExpand() )  m_pMsg2->DrawWnd();
	else if( m_pMMSI2->IsExpand() ) m_pMMSI2->DrawWnd();
}

void CInterrogationWnd::FillTargetMMSI(DWORD dSelMMSI1, DWORD dSelMMSI2)
{
	BYTE  szMMSI[36];
	BYTE  szShipName[24];
	CShip oShip;
	int   nCurSel1 = 0, nCurSel2 = 0;

	m_pMMSI1->RemoveAllItems();
	m_pMMSI2->RemoveAllItems();

	m_pMMSI1->AddItem((BYTE *)"- NONE -");
	m_pMMSI2->AddItem((BYTE *)"- NONE -");

	g_pDocMgr->SortTargets(SORT_ASC_BY_MMSI);
	for( int i = 0; i < g_pDocMgr->GetTargetCount(); ++i )
	{
		CShip *poShip = g_pDocMgr->GetTargetPtrArray()[i];

		poShip->GetShipName(szShipName);
		sprintf((char *)szMMSI, "%09d  %-20s", poShip->GetMMSI(), (char *)szShipName);
		m_pMMSI1->AddItem(szMMSI);
		m_pMMSI2->AddItem(szMMSI);

		if( poShip->GetMMSI() == dSelMMSI1 )
		{
			nCurSel1 = i + 1;
		}
		
		if( poShip->GetMMSI() == dSelMMSI2 )
		{
			nCurSel2 = i + 1;
		}			
	}
	g_pDocMgr->SortTargets();

	if( dSelMMSI1 == 0 )
		m_pMMSI1->SetCurSel(0);
	else
		m_pMMSI1->SetCurSel(nCurSel1);

	if( dSelMMSI2 == 0)
		m_pMMSI2->SetCurSel(0);
	else
		m_pMMSI2->SetCurSel(nCurSel2);
}

void CInterrogationWnd::SendInterrogation()
{
	CAir  air;
	BYTE  szTemp[MAX_NMEA_LEN];
	int   nMMSI;
	BOOL  bStationReady = 0;

	if( m_pMMSI1->GetCurSel() != 0 ) {
		m_pMMSI1->GetText(szTemp);
		sscanf((char *)szTemp, "%d", &nMMSI);
		air.SetMMSISt1(nMMSI);
		bStationReady = 1;
	}

	if( m_pMMSI2->GetCurSel() != 0 ) {
		m_pMMSI2->GetText(szTemp);
		sscanf((char *)szTemp, "%d", &nMMSI);
		air.SetMMSISt2(nMMSI);
	}

	// First Message of Station 1
	if( m_pMsg1->GetCurSel() == 1 )      // POSITION

		air.SetNumOfFirstMsgSt1(3);
	else if( m_pMsg1->GetCurSel() == 2 ) // STATIC AND VOYAGE DATA
		air.SetNumOfFirstMsgSt1(5);

	// Second Message of Station 1
	if( m_pMsg2->GetCurSel() == 1 )      // POSITION
		air.SetNumOfSecondMsgSt1(3);
	else if( m_pMsg2->GetCurSel() == 2 ) // STATIC AND VOYAGE DATA
		air.SetNumOfSecondMsgSt1(5);

	// First Message of Station 2
	if( m_pMsg3->GetCurSel() == 1 )      // POSITION
		air.SetNumOfMsgSt2(3);
	else if( m_pMsg3->GetCurSel() == 2 ) // STATIC AND VOYAGE DATA
		air.SetNumOfMsgSt2(5);

	if( bStationReady )
	{
		air.MakeSentence(szTemp);
		g_pDocMgr->SendCommand((const BYTE *)szTemp);
	}
}

void CInterrogationWnd::ComboCollapse()
{
	m_pMMSI1->Collapse();
	m_pMsg1->Collapse();
	m_pMsg2->Collapse();
	m_pMMSI2->Collapse();
	m_pMsg3->Collapse();
}
