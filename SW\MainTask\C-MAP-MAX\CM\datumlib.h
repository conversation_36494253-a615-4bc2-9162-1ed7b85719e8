#ifndef __DATUMLIB__
#define __DATUMLIB__

#ifdef DEBUG_INCLUDES
#pragma message( "+++++++++ including datumlib.h +++++++++" )
#endif

#include "cmaptype.h"

#define USE_DATUM_NAME
/* Must be defined in Libconf */
/* #define REDUCED_DATUM_LIST */


/*===========================================================================
									CONSTANTS DEFINITION
============================================================================*/

#define msign(x)  ( (x) > 0 ? 1 : ((x) == 0 ? 0 : (-1)))
#define mabs(x)   ( (x) >= 0 ? (x) : -(x) )
#define mround(x) (SLong)( x+msign(x)*0.5 ) /* values in the range -2,147,483,647..2,147,483,647 */

#define  MTLDEG   180         /* half earth circumpherence in degrees */
#define  MTLDEG_2 360         /* earth circumpherence in degrees */

#define  MTLMT    20038297L   /* lunghezza semicirconferenza equatoriale */
#define  MTLMT_2  40076594L   /* lunghezza circonferenza equatoriale     */

#ifndef  GR
#define  GR       57.295779   /* cost.trasformazione radianti -> gradi   */
#endif

#define  DEG2RAD  (M_PI/180.0)
#define SIN1         4.8481368e-6      /* SIN1 = sin(1 sec.) */

/* -------------------------------------------------------------------------- */

 	#define WGS84ENTRY		cmGetWGS84Entry()
 	#define WGS84_INDEX		cmGetWGS84Index()
 	#define NUMDATUMS		cmGetNumOfDatum()

/* --------------------------------------------------------------------------
STRUCTURE DEFINITION                                                        */

/* DD: degrees( 0..90/0..180), MM: minutes (0..59),
	SS.ss: seconds and hundredth of second (00.00..59.99),
	C: cardinal point (N,S,E,W). */

typedef struct Coords        /* coordinate type in sess. */
	{
	SWord deg;
	SWord minute;
	double sec;
	SByte cardinal;
	} DMSC;


/* DD: degrees( 0..90/0..180), MM.mmmm: minutes and hundredth of (00.0000.59.0000),
	C: cardinal point (N,S,E,W). */

typedef struct CoordM        /* coordinate type in hundredth of minutes */
	{
	SWord deg;
	double minute;
	SByte cardinal;
	} DMC;

/* DATUMLIB allows the user to calculates offsets in mercator's meters from
a specified datum to a another reference datum. The specified datum is usually
the chart local datum, the reference datum is usually WGS84.

C-MAP datum code:  a progressive value starting from 0 which identifies the
					record in the list of C-MAP datums.
Short name:        the short name of the datum for CF85 cartridges use.
Elliposid:         semimajor axis of the ellipsoid describing the datum
Flattening:        1/(ellispoid flattening) in meters
Delta X:           delta X in meters
Delta Y:           delta Y in meters
Delta Z:           delta Z in meters

The specified datum (usually chart local datum) is passed to calculation
functions as parameter and must be a valid code got from C-MAP datum code
the reference datum (usually WGS84) can be changed by modifying the constant
WGS84ENTRY (which contains the C-MAP code of the record where this datum is
described) in DATUMLIB.C.

*/

/*
N.B. La struttura originale utilizzava dati double, facendo alcune prove
si vede pero'che l'errore che si ha utilizzando i float non supera qualche
centesimo di metro mercatore, comunque la struttura costante contenente i
dati ha i valori double, quindi basta sostituire nella definizione di tipo
qui sotto float con double per avere la precisione originaria.
*/


typedef struct datum
	{
#ifdef USE_DATUM_NAME
    String  SName[15];   /*   short datum name for C-MAP cartridges use */
#endif
	double  Ellipsoid;  /*   ellipsoid semimajor axis */
	double  Flattening; /*   1/(ellipsoid flattening) in meters */
	double  Dx;         /*   delta X meters */
	double  Dy;         /*   delta X meters */
	double  Dz;         /*   delta X meters */
	} datum_t;


#ifdef __cplusplus
extern "C"
{
#endif

/*----------------------------------------------------------------------------
calculation procedures definition
----------------------------------------------------------------------------*/
/*Exported if define EXPORT_DATUM_FUNCTION in LibConf.h*/
PRE_EXPORT_H void	IN_EXPORT_H cmSetDatum(SWord code);
PRE_EXPORT_H Word	IN_EXPORT_H cmGetWGS84Entry(void);
PRE_EXPORT_H Word	IN_EXPORT_H cmGetWGS84Index(void);
PRE_EXPORT_H Word	IN_EXPORT_H cmGetNumOfDatum(void);
PRE_EXPORT_H void	IN_EXPORT_H cmGetDatumName(SWord code, String* name, Word MaxLen);
PRE_EXPORT_H SWord	IN_EXPORT_H cmGetDatumCode(Word num);
PRE_EXPORT_H SWord	IN_EXPORT_H cmGetDatumInfo(SWord DatumCode, datum_t *datum);
PRE_EXPORT_H SWord	IN_EXPORT_H cmDatumLocal2WGS84(SWord DatumCode, Double DecLat, Double DecLon,
					Float *LatOfs, Float *LonOfs, Float *HOfs, SWord Mode);
/*******************************************************/
void CF95_SetDatum(SWord code);

void CF95_GetDatumName(SWord code, String* name, Word MaxLen);

Word	CF95_GetNumOfDatum(void);

/* entry where WGS84 is described in the sorted list */
Word	CF95_GetWGS84Index(void);

/* entry where WGS84 is described in the Datum List table */
Word	CF95_GetWGS84Entry(void);


SWord GetDatumCode(Word num);
SWord CF95_GetDatumInfo(SWord DatumCode, datum_t *datum);

SWord CF95_DatumLocal2WGS84(SWord DatumCode, Double DecLat, Double DecLon,
					Float *LatOfs, Float *LonOfs, Float *HOfs, SWord Mode);

short Local2WGS84(SWord DatumCode, DMSC Lat, DMSC Lon,
						float *LatOfs, float *LonOfs, float *HOfs, SWord Mode);
short Auto2WGS(DMSC LatMin, DMSC LonMin, DMSC LatMax, DMSC LonMax,
					SWord DatumCode, float *Xofs, float *Yofs);
void Manual2WGS(DMSC LatMin, DMSC LonMin, DMSC LatMax, DMSC LonMax,
					 float *Xofs, float *Yofs, SWord Mode);
#ifdef __cplusplus
}
#endif

#endif
