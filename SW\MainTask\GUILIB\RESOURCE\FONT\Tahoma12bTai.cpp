/*...........................................................................*/
/*.                  File Name : Tahoma12bTai.cpp                           .*/
/*.                                                                         .*/
/*.                       Date : 2009.06.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY WinInwa14bMiy_Font;

//...........................................................................
ROMDATA PEGUSHORT <PERSON>homa12bAdd_Font_offset_table[257] = {
0x0000,0x000d,0x0017,0x0022,0x002c,0x0037,0x0041,0x004c,0x0056,0x0061,0x0069,0x0075,0x007f,0x008b,0x0095,0x00a1,
0x00ab,0x00b7,0x00c1,0x00cd,0x00d7,0x00e1,0x00eb,0x00f5,0x00ff,0x0109,0x0113,0x011d,0x0127,0x0131,0x013b,0x0144,
0x014a,0x0156,0x0160,0x016c,0x0176,0x0182,0x018c,0x0198,0x01a2,0x01af,0x01ba,0x01c6,0x01d0,0x01d7,0x01de,0x01e5,
0x01eb,0x01f6,0x0200,0x020b,0x0215,0x0220,0x022a,0x0233,0x0238,0x0241,0x0247,0x0250,0x0256,0x025f,0x0264,0x0272,
0x0281,0x028f,0x029e,0x02ac,0x02bb,0x02c7,0x02d1,0x02dd,0x02e7,0x02f3,0x02fd,0x0309,0x0313,0x031f,0x0329,0x0335,
0x033f,0x034b,0x0355,0x0361,0x036b,0x0376,0x0380,0x038b,0x0395,0x03a1,0x03a8,0x03b4,0x03bb,0x03c7,0x03ce,0x03da,
0x03e1,0x03eb,0x03f3,0x03fd,0x0405,0x040f,0x0417,0x0421,0x0429,0x0433,0x043b,0x0446,0x044d,0x0458,0x045f,0x046a,
0x0471,0x047c,0x0483,0x048f,0x0499,0x04a5,0x04af,0x04bb,0x04c5,0x04d1,0x04db,0x04e7,0x04f1,0x04fd,0x0507,0x0513,
0x051d,0x052d,0x053b,0x054b,0x0559,0x0569,0x0577,0x0587,0x0595,0x05a5,0x05b3,0x05bf,0x05c9,0x05d5,0x05df,0x05eb,
0x05f5,0x05ff,0x0608,0x0612,0x061b,0x0625,0x062e,0x0638,0x0640,0x064e,0x0658,0x0662,0x0667,0x0677,0x0687,0x0692,
0x06a2,0x06af,0x06b9,0x06c6,0x06d0,0x06dd,0x06e7,0x06f4,0x06ff,0x070c,0x0716,0x0723,0x072d,0x073a,0x0744,0x0751,
0x075b,0x0768,0x0772,0x077f,0x0789,0x0796,0x07a0,0x07ad,0x07b7,0x07c1,0x07cb,0x07d5,0x07df,0x07e9,0x07f3,0x07fd,
0x0807,0x0812,0x081d,0x0827,0x0831,0x083b,0x0845,0x084f,0x0859,0x0860,0x0865,0x086c,0x0871,0x087d,0x0887,0x0893,
0x089d,0x08a9,0x08b3,0x08bf,0x08ca,0x08d6,0x08e0,0x08ec,0x08f6,0x0902,0x090c,0x0919,0x0923,0x0930,0x093a,0x0947,
0x0951,0x095e,0x0968,0x0975,0x097f,0x098b,0x0995,0x09a1,0x09ab,0x09b9,0x09c5,0x09d3,0x09df,0x09ed,0x09f9,0x0a07,
0x0a13,0x0a21,0x0a2d,0x0a39,0x0a43,0x0a4f,0x0a59,0x0a65,0x0a6f,0x0a7b,0x0a85,0x0a95,0x0aa5,0x0ab5,0x0ac5,0x0ad5,
0x0ae5};



ROMDATA PEGUBYTE Tahoma12bAdd_Font_data_table[6631] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x01, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x03, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xe0, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x20, 0x00, 0x00, 0x40, 
0x00, 0x04, 0xc0, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x16, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x16, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x00, 0x03, 0x01, 0x80, 0x00, 0x00, 0x06, 0x00, 0xc0, 0x00, 
0x00, 0x01, 0x98, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x30, 0xc0, 0xc0, 0x18, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 
0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0c, 0x03, 0x21, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 
0x0c, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x30, 0x38, 0x66, 0x0e, 0x06, 0x00, 0x01, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x07, 0x20, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x06, 0x30, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x0c, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x07, 0x00, 0x38, 0x00, 0x18, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x07, 0x60, 0x00, 0x6e, 0x00, 0x00, 
0x0e, 0x20, 0x00, 0x1c, 0x03, 0x40, 0x38, 0x00, 0x00, 0xcc, 0x00, 0x01, 0x98, 0x00, 0x03, 0x30, 
0x1c, 0x06, 0x60, 0x64, 0x0c, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x72, 0x00, 0x03, 0xb0, 
0x00, 0xdc, 0x00, 0x00, 0xe2, 0x00, 0x0e, 0x03, 0x40, 0xe0, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x00, 0x00, 0x76, 0x00, 0x0d, 0xc0, 0x00, 0x03, 0x88, 0x00, 0x0e, 0x01, 0xa0, 0x38, 
0x00, 0x00, 0x60, 0x00, 0x03, 0x00, 0x00, 0x00, 0x80, 0x00, 0x0e, 0x40, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0xc0, 0x80, 0x00, 0x60, 0x20, 0x00, 0x04, 0x08, 0x00, 0x0e, 
0x42, 0x00, 0x00, 0x00, 0x80, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x1c, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x0c, 0x07, 0xe0, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x03, 0x00, 0x03, 0xf0, 0x00, 0x06, 0x00, 0xc0, 0x00, 
0x00, 0x01, 0x98, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x31, 0x81, 0x80, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 
0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0xa0, 0x18, 0x04, 0xc1, 0x98, 0x3f, 0x03, 0x00, 0xfc, 0x03, 0x03, 0x00, 0x00, 
0x0c, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x60, 0x00, 0x3c, 0x00, 0x06, 0x00, 0x01, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 0x06, 0x07, 0xf8, 0x66, 0x09, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x06, 0x30, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x0c, 0xc0, 0x00, 0x0c, 0x00, 0x01, 0xb0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x08, 0x80, 0x44, 0x04, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x0d, 0x80, 0x00, 0x1b, 0x00, 0x00, 
0x1b, 0x40, 0x1c, 0x36, 0x05, 0x80, 0x6c, 0x00, 0x00, 0x78, 0x01, 0x00, 0xf0, 0x10, 0x01, 0xe0, 
0x04, 0x03, 0xc0, 0x98, 0x07, 0x80, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x9c, 0x00, 0x06, 0xc0, 
0x00, 0x36, 0x00, 0x01, 0xb4, 0x03, 0x9b, 0x05, 0x81, 0xb0, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x08, 0x00, 0x00, 0xd8, 0x00, 0x03, 0x60, 0x00, 0x06, 0xd0, 0x07, 0x1b, 0x02, 0xc0, 0x6c, 
0x00, 0x00, 0xc0, 0x00, 0x01, 0x80, 0x00, 0x01, 0x00, 0x00, 0x13, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x20, 0x00, 0x01, 0x80, 0x80, 0x00, 0x30, 0x20, 0x00, 0x08, 0x08, 0x00, 0x13, 
0x82, 0x00, 0x00, 0x00, 0x80, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x27, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x01, 0x80, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
0x00, 0x00, 0x30, 0x00, 0x00, 0xc0, 0x00, 0x03, 0x00, 0x00, 0x0c, 0x00, 0x06, 0x00, 0x00, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x80, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 
0x30, 0x00, 0x00, 0xc0, 0x00, 0x00, 0xc0, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x18, 0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x18, 0x00, 0x30, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0e, 0x00, 0x31, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb0, 0x00, 0x00, 0x00, 0x66, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x08, 0x80, 0x44, 0x02, 0x1e, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x08, 0x00, 0x00, 
0x08, 0x00, 0x00, 0x00, 0x00, 0x01, 0x98, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x80, 0x00, 0x00, 0x20, 0x00, 0x00, 0x08, 0xe0, 0x00, 
0x02, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x07, 0x00, 0x01, 0xfc, 0x33, 0x0f, 0xe1, 0x80, 0x7f, 0x0c, 0x00, 0x7e, 0x06, 0x7f, 0x00, 0x6d, 
0xfc, 0x00, 0x37, 0xf0, 0x00, 0xdf, 0xc0, 0x03, 0x7f, 0x00, 0x0d, 0xff, 0x00, 0x1f, 0xf0, 0x01, 
0xff, 0x00, 0x1f, 0xf0, 0x01, 0xff, 0x19, 0x9f, 0xe6, 0x07, 0xf0, 0x00, 0xc0, 0x6c, 0x03, 0x01, 
0xb0, 0x0c, 0x06, 0xc0, 0x18, 0x0c, 0xc0, 0x30, 0x1b, 0x00, 0xfc, 0x63, 0xf6, 0x78, 0x33, 0x00, 
0xc1, 0x98, 0x06, 0x0c, 0xc0, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xcc, 0x01, 0x80, 0x30, 
0x60, 0x0c, 0x06, 0x03, 0x00, 0x60, 0x00, 0x18, 0x04, 0x18, 0x60, 0x10, 0x01, 0x80, 0x40, 0x06, 
0x01, 0x00, 0x03, 0xe0, 0x32, 0x0f, 0x80, 0x64, 0x3e, 0x00, 0x00, 0xf8, 0x00, 0x1f, 0xe0, 0x30, 
0xff, 0x01, 0x87, 0xf0, 0x18, 0xfe, 0x00, 0x1f, 0xc0, 0x03, 0xf8, 0x00, 0x1f, 0x83, 0x07, 0xe0, 
0x01, 0xf8, 0x1c, 0x7e, 0x1b, 0x1f, 0x83, 0x1f, 0xf9, 0x87, 0xfe, 0x61, 0xff, 0x98, 0x7f, 0xe6, 
0x18, 0x0c, 0x00, 0x60, 0x30, 0x01, 0x80, 0xc0, 0x06, 0x03, 0x0c, 0x98, 0x0c, 0x00, 0x30, 0x30, 
0xe4, 0xc0, 0xc0, 0x06, 0x08, 0x30, 0x60, 0x18, 0x20, 0xc0, 0x30, 0x60, 0x83, 0x0c, 0x61, 0x82, 
0x0c, 0x06, 0x06, 0x08, 0x30, 0x00, 0x0c, 0x0c, 0x18, 0x30, 0x31, 0x98, 0xc0, 0xc1, 0x87, 0xfc, 
0x38, 0xff, 0x80, 0x1f, 0xf0, 0x03, 0x00, 0x30, 0x08, 0x80, 0x44, 0x02, 0x30, 0x7f, 0xf8, 0x7f, 
0xf8, 0x7c, 0x0f, 0xff, 0x01, 0xc0, 0x00, 0x03, 0x80, 0x10, 0x07, 0x00, 0xec, 0x0e, 0x06, 0xe0, 
0x0e, 0x01, 0xc8, 0x1c, 0x03, 0x80, 0x38, 0x07, 0x00, 0x70, 0x19, 0x80, 0xe0, 0x33, 0x01, 0xc0, 
0x66, 0x03, 0x80, 0xcc, 0x07, 0x01, 0x99, 0xff, 0x00, 0x1f, 0xf0, 0x21, 0xff, 0x1c, 0x9f, 0xf0, 
0xec, 0xff, 0x9b, 0x87, 0xfc, 0x39, 0x7f, 0xc3, 0x87, 0xfc, 0x38, 0x7e, 0x17, 0xe6, 0x0f, 0x80, 
0x00, 0x3e, 0x00, 0x80, 0xf8, 0x0e, 0xc3, 0xe0, 0xdc, 0x07, 0xc0, 0x72, 0x1f, 0x01, 0xc0, 0x7c, 
0x07, 0x01, 0xf0, 0x83, 0x23, 0xe1, 0x30, 0x47, 0xc2, 0x08, 0x8f, 0x84, 0xe5, 0x1f, 0x08, 0x03, 
0x80, 0xc0, 0x06, 0x03, 0x02, 0x18, 0x0f, 0x03, 0x0e, 0x03, 0xc3, 0x03, 0x80, 0xf0, 0x20, 0xe0, 
0x3c, 0x72, 0x38, 0x0f, 0x00, 0x0b, 0x03, 0x0c, 0x0c, 0x0c, 0x00, 0x30, 0x30, 0x10, 0xc0, 0xc3, 
0x91, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe0, 

0x07, 0x00, 0x01, 0x86, 0x33, 0x0c, 0x31, 0x80, 0x61, 0x8c, 0x01, 0xc3, 0x0c, 0x61, 0xc0, 0x6d, 
0x87, 0x00, 0x36, 0x1c, 0x00, 0xd8, 0x70, 0x03, 0x61, 0xc0, 0x0d, 0x80, 0x1f, 0x98, 0x01, 0xf9, 
0x80, 0x00, 0x18, 0x00, 0x01, 0x80, 0x0f, 0x18, 0x06, 0x1c, 0x18, 0xfc, 0xc0, 0x6c, 0x03, 0x01, 
0xb0, 0x0c, 0x06, 0xc0, 0x18, 0x0c, 0xc0, 0x30, 0x1b, 0x00, 0x30, 0x60, 0xc6, 0x78, 0x63, 0x00, 
0xc3, 0x18, 0x06, 0x18, 0xc0, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xce, 0x03, 0x80, 0x60, 
0x70, 0x1c, 0x06, 0x03, 0x80, 0xe0, 0x00, 0x1c, 0x04, 0x18, 0x70, 0x10, 0x01, 0xc0, 0x40, 0x07, 
0x01, 0x00, 0x0e, 0x38, 0x4c, 0x38, 0xe0, 0x98, 0xe3, 0x87, 0xe3, 0x8e, 0x1f, 0x98, 0x30, 0x60, 
0xc1, 0x81, 0x86, 0x18, 0x18, 0xc3, 0x00, 0x18, 0x61, 0xfb, 0x0c, 0x00, 0x30, 0xc3, 0x0c, 0x30, 
0x03, 0x0c, 0x38, 0xc3, 0x0e, 0x30, 0xc3, 0x01, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0c, 0x00, 0x60, 0x30, 0x01, 0x80, 0xc0, 0x06, 0x03, 0x13, 0x18, 0x0c, 0x7e, 0x30, 0x31, 
0x38, 0xc0, 0xc0, 0x06, 0x1c, 0x30, 0x30, 0x18, 0x70, 0xc0, 0x60, 0x61, 0xc3, 0x0c, 0x61, 0x87, 
0x0c, 0x06, 0x06, 0x1c, 0x30, 0x00, 0x0c, 0x0c, 0x18, 0x30, 0x31, 0x98, 0xc0, 0xc1, 0x80, 0x0c, 
0x6c, 0x01, 0x80, 0x00, 0x30, 0x03, 0x00, 0x30, 0x07, 0x00, 0x38, 0x04, 0x30, 0x40, 0x08, 0x40, 
0x08, 0xc6, 0x08, 0x01, 0x01, 0xc0, 0x00, 0x03, 0x80, 0x20, 0x07, 0x01, 0xb0, 0x0e, 0x01, 0xb0, 
0x0e, 0x03, 0x60, 0x1c, 0x06, 0xc0, 0x38, 0x0d, 0x80, 0x70, 0x0f, 0x00, 0xe0, 0x1e, 0x01, 0xc0, 
0x3c, 0x03, 0x80, 0x78, 0x07, 0x00, 0xf1, 0x80, 0x00, 0x18, 0x00, 0x41, 0x80, 0x27, 0x18, 0x01, 
0xb0, 0xc0, 0x06, 0xc6, 0x00, 0x6c, 0x60, 0x06, 0xc6, 0x00, 0x6c, 0x18, 0x21, 0x86, 0x38, 0xe0, 
0x00, 0xe3, 0x81, 0x03, 0x8e, 0x1b, 0x0e, 0x38, 0x36, 0x1c, 0x70, 0xd8, 0x71, 0xc3, 0x61, 0xc7, 
0x0d, 0x87, 0x1c, 0x86, 0x2e, 0x39, 0x18, 0x5c, 0x72, 0x10, 0xb8, 0xe5, 0x39, 0x71, 0xc8, 0x03, 
0x80, 0xc0, 0x06, 0x03, 0x04, 0x18, 0x0c, 0x06, 0x0e, 0x03, 0x01, 0x83, 0x80, 0xc0, 0x40, 0xe0, 
0x30, 0x9c, 0x38, 0x0c, 0x00, 0x0b, 0x03, 0x06, 0x0c, 0x0c, 0x00, 0x30, 0x30, 0x20, 0xc0, 0xc4, 
0xe1, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x0d, 0x80, 0x01, 0x83, 0x30, 0x0c, 0x19, 0x80, 0x60, 0xcc, 0x01, 0x83, 0x00, 0x60, 0x60, 0x0d, 
0x81, 0x80, 0x36, 0x06, 0x00, 0xd8, 0x18, 0x03, 0x60, 0x60, 0x0d, 0x80, 0x00, 0x18, 0x00, 0x01, 
0x80, 0x00, 0x18, 0x00, 0x01, 0x80, 0x00, 0x18, 0x06, 0x18, 0x18, 0x00, 0xc0, 0x6c, 0x03, 0x01, 
0xb0, 0x0c, 0x06, 0xc0, 0x18, 0x0c, 0xc0, 0x30, 0x1b, 0x00, 0x30, 0x00, 0xc0, 0x18, 0xc3, 0x00, 
0xc6, 0x18, 0x06, 0x30, 0xc0, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xce, 0x03, 0x80, 0x00, 
0x70, 0x1c, 0x00, 0x03, 0x80, 0xe0, 0x00, 0x1e, 0x04, 0x00, 0x78, 0x10, 0x01, 0xe0, 0x40, 0x07, 
0x81, 0x00, 0x0c, 0x18, 0x00, 0x30, 0x60, 0x00, 0xc1, 0x80, 0x03, 0x06, 0x00, 0x18, 0x18, 0x00, 
0xc0, 0xc0, 0x06, 0x0c, 0x00, 0xc1, 0x80, 0x18, 0x30, 0x03, 0x06, 0x00, 0x60, 0xc0, 0x18, 0x30, 
0x06, 0x0c, 0x01, 0x83, 0x00, 0x60, 0xc0, 0x01, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0c, 0x00, 0x60, 0x30, 0x01, 0x80, 0xc0, 0x06, 0x03, 0x00, 0x18, 0x0c, 0x00, 0x30, 0x30, 
0x00, 0xc0, 0xc0, 0x06, 0x1c, 0x30, 0x00, 0x18, 0x70, 0xc0, 0x00, 0x61, 0xc3, 0x00, 0x01, 0x87, 
0x0c, 0x00, 0x06, 0x1c, 0x30, 0x00, 0x06, 0x18, 0x00, 0x18, 0x60, 0x00, 0x61, 0x80, 0x00, 0x18, 
0x00, 0x03, 0x00, 0x00, 0x60, 0x03, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x30, 0x40, 0x08, 0x40, 
0x09, 0x83, 0x08, 0x01, 0x03, 0x60, 0x00, 0x06, 0xc0, 0x00, 0x0d, 0x80, 0x00, 0x1b, 0x00, 0x00, 
0x1b, 0x00, 0x00, 0x36, 0x00, 0x00, 0x6c, 0x00, 0x00, 0xd8, 0x00, 0x01, 0xb0, 0x00, 0x03, 0x60, 
0x00, 0x06, 0xc0, 0x00, 0x0d, 0x80, 0x01, 0x80, 0x00, 0x18, 0x00, 0x01, 0x80, 0x00, 0x18, 0x00, 
0x00, 0xc0, 0x00, 0x06, 0x00, 0x00, 0x60, 0x00, 0x06, 0x00, 0x00, 0x18, 0x01, 0x80, 0x30, 0x60, 
0x00, 0xc1, 0x80, 0x03, 0x06, 0x00, 0x0c, 0x18, 0x00, 0x18, 0x30, 0x00, 0x60, 0xc0, 0x01, 0x83, 
0x00, 0x06, 0x0c, 0x80, 0x2c, 0x19, 0x00, 0x58, 0x32, 0x00, 0xb0, 0x64, 0x01, 0x60, 0xc8, 0x03, 
0x80, 0xc0, 0x06, 0x03, 0x00, 0x18, 0x0c, 0x00, 0x0e, 0x03, 0x00, 0x03, 0x80, 0xc0, 0x00, 0xe0, 
0x30, 0x00, 0x38, 0x0c, 0x00, 0x09, 0x86, 0x00, 0x06, 0x18, 0x00, 0x18, 0x60, 0x00, 0x61, 0x80, 
0x01, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x0d, 0x81, 0xf1, 0x83, 0x37, 0x8c, 0x19, 0xbc, 0x60, 0xcd, 0xe3, 0x00, 0x1e, 0x60, 0x61, 0xed, 
0x81, 0x87, 0xb6, 0x06, 0x1e, 0xd8, 0x18, 0x7b, 0x60, 0x61, 0xed, 0x80, 0x1f, 0x18, 0x01, 0xf1, 
0x80, 0x1f, 0x18, 0x01, 0xf1, 0x80, 0x1f, 0x18, 0x0f, 0xb0, 0x00, 0xf6, 0xc0, 0x6d, 0xe3, 0x01, 
0xb7, 0x8c, 0x06, 0xde, 0x18, 0x0c, 0xdf, 0x30, 0x1b, 0x78, 0x30, 0x60, 0xc1, 0x99, 0x83, 0x0c, 
0xcc, 0x18, 0x66, 0x60, 0xc3, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xcb, 0x05, 0xb7, 0x9e, 
0x58, 0x2d, 0xbc, 0xf2, 0xc1, 0x6d, 0xe7, 0x97, 0x05, 0xbc, 0x5c, 0x16, 0xf1, 0x70, 0x5b, 0xc5, 
0xc1, 0x6f, 0x18, 0x0c, 0x7c, 0x60, 0x31, 0xf1, 0x80, 0xc7, 0xc6, 0x03, 0x1f, 0x18, 0x1b, 0x78, 
0xc0, 0xdb, 0xc6, 0x0c, 0x6e, 0xc1, 0x8d, 0xd8, 0x31, 0xbb, 0x06, 0x37, 0x60, 0x0f, 0x98, 0x03, 
0xe6, 0x00, 0xf9, 0x80, 0x3e, 0x60, 0x0f, 0x81, 0x83, 0xf0, 0x60, 0xfc, 0x18, 0x3f, 0x06, 0x0f, 
0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x36, 0x1c, 0x36, 0x10, 0xd8, 0x70, 0xd8, 0x43, 0x61, 0xc3, 0x61, 0x0d, 0x87, 
0x0d, 0x84, 0x36, 0x1c, 0x36, 0x10, 0xc3, 0x31, 0x83, 0x0c, 0xc6, 0x0c, 0x61, 0x8c, 0x30, 0x31, 
0xfe, 0x06, 0x3f, 0xc0, 0xc7, 0xfb, 0x78, 0x7e, 0xc2, 0x19, 0x86, 0x7e, 0x30, 0x40, 0x08, 0x40, 
0x09, 0x83, 0x08, 0x01, 0x03, 0x60, 0x7c, 0x06, 0xc0, 0xf8, 0x0d, 0x81, 0xf0, 0x1b, 0x01, 0xf0, 
0x1b, 0x03, 0xe0, 0x36, 0x07, 0xc0, 0x6c, 0x0f, 0x80, 0xd8, 0x1f, 0x01, 0xb0, 0x3e, 0x03, 0x60, 
0x7c, 0x06, 0xc0, 0xf8, 0x0d, 0x81, 0xf1, 0x80, 0x1f, 0x18, 0x01, 0xf1, 0x80, 0x1f, 0x18, 0x01, 
0xf0, 0xc0, 0x07, 0xc6, 0x00, 0x7c, 0x60, 0x07, 0xc6, 0x00, 0x7c, 0x18, 0x61, 0x86, 0x60, 0x31, 
0xf1, 0x80, 0xc7, 0xc6, 0x03, 0x1f, 0x18, 0x0c, 0x3e, 0x30, 0x18, 0xf8, 0xc0, 0x63, 0xe3, 0x01, 
0x8f, 0x8c, 0x07, 0x1f, 0xd8, 0x0e, 0x3f, 0xb0, 0x1c, 0x7f, 0x60, 0x38, 0xfe, 0xc0, 0x71, 0xfd, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0x60, 0xf6, 0x03, 0x18, 0x3d, 0x80, 0xc6, 0x0f, 0x60, 
0x31, 0x83, 0xd8, 0x0c, 0x60, 0xf1, 0x86, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x8c, 
0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x18, 0xc3, 0x19, 0x86, 0x38, 0xcc, 0x31, 0xc6, 0x61, 0x8e, 0x33, 0x00, 0x31, 0x60, 0x33, 0x1d, 
0x80, 0xcc, 0x76, 0x03, 0x31, 0xd8, 0x0c, 0xc7, 0x60, 0x33, 0x1d, 0x80, 0x31, 0x98, 0x03, 0x19, 
0x80, 0x31, 0x98, 0x03, 0x19, 0x80, 0x31, 0x98, 0x06, 0x30, 0x01, 0x8e, 0xc0, 0x6e, 0x33, 0x01, 
0xb8, 0xcc, 0x06, 0xe3, 0x1f, 0xfc, 0xe1, 0xb0, 0x1b, 0x8c, 0x30, 0x60, 0xc1, 0x9b, 0x03, 0x18, 
0xd8, 0x18, 0xc6, 0xc0, 0xc6, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xcb, 0x05, 0xb8, 0xe3, 
0x58, 0x2d, 0xc7, 0x1a, 0xc1, 0x6e, 0x38, 0xd3, 0x85, 0xc6, 0x4e, 0x17, 0x19, 0x38, 0x5c, 0x64, 
0xe1, 0x71, 0x98, 0x0c, 0xc6, 0x60, 0x33, 0x19, 0x80, 0xcc, 0x66, 0x03, 0x31, 0x98, 0x1b, 0x8c, 
0xc0, 0xdc, 0x66, 0x0c, 0x7e, 0xc1, 0x8f, 0xd8, 0x31, 0xfb, 0x06, 0x3f, 0x70, 0x18, 0x5c, 0x06, 
0x17, 0x01, 0x85, 0xc0, 0x61, 0x70, 0x18, 0x41, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x33, 0x3e, 0x66, 0x38, 0xcc, 0xf9, 0x98, 0xe3, 0x33, 0xe6, 0x63, 0x8c, 0xcf, 
0x99, 0x8e, 0x33, 0x3e, 0x66, 0x38, 0xc1, 0xe0, 0xc6, 0x07, 0x83, 0x18, 0x33, 0x0c, 0x30, 0x30, 
0x06, 0x06, 0x00, 0xc0, 0xc0, 0x1b, 0x8c, 0x30, 0xc7, 0x19, 0x86, 0x43, 0xb0, 0x40, 0x08, 0x40, 
0x09, 0x86, 0x08, 0x01, 0x06, 0x30, 0xc6, 0x0c, 0x61, 0x8c, 0x18, 0xc3, 0x18, 0x31, 0x83, 0x18, 
0x31, 0x86, 0x30, 0x63, 0x0c, 0x60, 0xc6, 0x18, 0xc1, 0x8c, 0x31, 0x83, 0x18, 0x63, 0x06, 0x30, 
0xc6, 0x0c, 0x61, 0x8c, 0x18, 0xc3, 0x19, 0x80, 0x31, 0x98, 0x03, 0x19, 0x80, 0x31, 0x98, 0x03, 
0x18, 0xc0, 0x0c, 0x66, 0x00, 0xc6, 0x60, 0x0c, 0x66, 0x00, 0xc6, 0x18, 0x61, 0x86, 0x60, 0x33, 
0x19, 0x80, 0xcc, 0x66, 0x03, 0x31, 0x98, 0x0c, 0x63, 0x30, 0x19, 0x8c, 0xc0, 0x66, 0x33, 0x01, 
0x98, 0xcc, 0x06, 0x31, 0x98, 0x0c, 0x63, 0x30, 0x18, 0xc6, 0x60, 0x31, 0x8c, 0xc0, 0x63, 0x19, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0x60, 0xc6, 0x03, 0x18, 0x31, 0x80, 0xc6, 0x0c, 0x60, 
0x31, 0x83, 0x18, 0x0c, 0x60, 0xc0, 0xcc, 0x30, 0xc3, 0x30, 0xc3, 0x0c, 0xc3, 0x0c, 0x33, 0x0c, 
0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x18, 0xc0, 0x0d, 0xfe, 0x30, 0x6f, 0xf1, 0x83, 0x7f, 0x8c, 0x1b, 0x00, 0x60, 0x60, 0x36, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0xff, 0x60, 0xdf, 0xf6, 0x0d, 
0xff, 0x60, 0xdf, 0xf6, 0x0d, 0xff, 0x60, 0xdf, 0xe6, 0x30, 0x03, 0x06, 0xff, 0xec, 0x1b, 0xff, 
0xb0, 0x6f, 0xfe, 0xc1, 0x98, 0x0c, 0xc1, 0xbf, 0xfb, 0x06, 0x30, 0x60, 0xc1, 0x9e, 0x03, 0x30, 
0xf0, 0x19, 0x87, 0x80, 0xcc, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xc9, 0x89, 0xb0, 0xc3, 
0x4c, 0x4d, 0x86, 0x1a, 0x62, 0x6c, 0x30, 0xd1, 0xc5, 0x83, 0x47, 0x16, 0x0d, 0x1c, 0x58, 0x34, 
0x71, 0x60, 0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x1b, 0x06, 
0xc0, 0xd8, 0x36, 0x0c, 0x60, 0xc1, 0x8c, 0x18, 0x31, 0x83, 0x06, 0x30, 0x3f, 0x18, 0x0f, 0xc6, 
0x03, 0xf1, 0x80, 0xfc, 0x60, 0x3f, 0x18, 0x01, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x18, 0x63, 
0x0c, 0x61, 0x8c, 0x33, 0x36, 0x63, 0x39, 0x8c, 0xd9, 0x8c, 0xe6, 0x33, 0x66, 0x33, 0x98, 0xcd, 
0x98, 0xce, 0x63, 0x36, 0x63, 0x39, 0x80, 0xc0, 0x6c, 0x03, 0x01, 0xb0, 0x1e, 0x0c, 0x30, 0x60, 
0x0c, 0x0c, 0x01, 0x81, 0x80, 0x33, 0x06, 0x30, 0x67, 0x31, 0x86, 0x01, 0xb0, 0x40, 0x08, 0x40, 
0x09, 0x9e, 0x08, 0x01, 0x06, 0x30, 0x03, 0x0c, 0x60, 0x06, 0x18, 0xc0, 0x0c, 0x31, 0x80, 0x0c, 
0x31, 0x80, 0x18, 0x63, 0x00, 0x30, 0xc6, 0x00, 0x61, 0x8c, 0x00, 0xc3, 0x18, 0x01, 0x86, 0x30, 
0x03, 0x0c, 0x60, 0x06, 0x18, 0xc0, 0x0d, 0xff, 0x60, 0xdf, 0xf6, 0x0d, 0xff, 0x60, 0xdf, 0xf6, 
0x0c, 0xff, 0x98, 0x37, 0xfd, 0x83, 0x7f, 0xd8, 0x37, 0xfd, 0x83, 0x18, 0x61, 0x86, 0x60, 0x36, 
0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x19, 0x83, 0x60, 0x33, 0x06, 0xc0, 0x66, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0x60, 0xc6, 0x03, 0x18, 0x31, 0x80, 0xc6, 0x0c, 0x60, 
0x31, 0x83, 0x18, 0x0c, 0x60, 0xc0, 0x78, 0x30, 0xc1, 0xe0, 0xc3, 0x07, 0x83, 0x0c, 0x1e, 0x0c, 
0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x18, 0xc0, 0xfd, 0x83, 0x30, 0x6c, 0x19, 0x83, 0x60, 0xcc, 0x1b, 0x00, 0x60, 0x60, 0x36, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0x60, 0xd8, 0x06, 0x0d, 
0x80, 0x60, 0xd8, 0x06, 0x0d, 0x80, 0x60, 0xd8, 0x06, 0x30, 0xfb, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0xc1, 0x98, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0x30, 0x60, 0xc1, 0x9b, 0x03, 0x60, 
0xd8, 0x1b, 0x06, 0xc0, 0xd8, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xc9, 0x89, 0xb0, 0xc3, 
0x4c, 0x4d, 0x86, 0x1a, 0x62, 0x6c, 0x30, 0xd0, 0xe5, 0x83, 0x43, 0x96, 0x0d, 0x0e, 0x58, 0x34, 
0x39, 0x60, 0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x33, 0x06, 
0xc1, 0x98, 0x36, 0x18, 0x60, 0xc3, 0x0c, 0x18, 0x61, 0x83, 0x0c, 0x30, 0x1f, 0x9e, 0x07, 0xe7, 
0x81, 0xf9, 0xe0, 0x7e, 0x78, 0x1f, 0x9e, 0x01, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x0c, 0xc1, 
0x98, 0x33, 0x06, 0x63, 0x36, 0x63, 0x6d, 0x8c, 0xd9, 0x8d, 0xb6, 0x33, 0x66, 0x36, 0xd8, 0xcd, 
0x98, 0xdb, 0x63, 0x36, 0x63, 0x6d, 0x80, 0xc0, 0x38, 0x03, 0x00, 0xe0, 0x1e, 0x06, 0x60, 0xc0, 
0x18, 0x18, 0x03, 0x03, 0x00, 0x63, 0x06, 0x30, 0x6d, 0xb0, 0xcc, 0x1f, 0xb0, 0x40, 0x08, 0x40, 
0x09, 0x83, 0x08, 0x01, 0x06, 0x30, 0x3f, 0x0c, 0x60, 0x7e, 0x18, 0xc0, 0xfc, 0x31, 0x80, 0xfc, 
0x31, 0x81, 0xf8, 0x63, 0x03, 0xf0, 0xc6, 0x07, 0xe1, 0x8c, 0x0f, 0xc3, 0x18, 0x1f, 0x86, 0x30, 
0x3f, 0x0c, 0x60, 0x7e, 0x18, 0xc0, 0xfd, 0x80, 0x60, 0xd8, 0x06, 0x0d, 0x80, 0x60, 0xd8, 0x06, 
0x0c, 0xc0, 0x18, 0x36, 0x01, 0x83, 0x60, 0x18, 0x36, 0x01, 0x83, 0x18, 0x61, 0x86, 0x60, 0x36, 
0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x19, 0x83, 0x60, 0x33, 0x06, 0xc0, 0x66, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0x60, 0xc6, 0x03, 0x18, 0x31, 0x80, 0xc6, 0x0c, 0x60, 
0x31, 0x83, 0x18, 0x0c, 0x60, 0xc0, 0x78, 0x19, 0x81, 0xe0, 0x66, 0x07, 0x81, 0x98, 0x1e, 0x06, 
0x61, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x3f, 0xe3, 0x0d, 0x81, 0xb0, 0x6c, 0x0d, 0x83, 0x60, 0x6c, 0x1b, 0x00, 0x60, 0x60, 0x36, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0x7f, 0xd8, 0x07, 0xfd, 
0x80, 0x7f, 0xd8, 0x07, 0xfd, 0x80, 0x7f, 0xd8, 0x06, 0x30, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0xc1, 0x98, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0x30, 0x60, 0xc1, 0x99, 0x83, 0xe0, 
0xcc, 0x1f, 0x06, 0x60, 0xf8, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xc8, 0xd1, 0xb0, 0xc3, 
0x46, 0x8d, 0x86, 0x1a, 0x34, 0x6c, 0x30, 0xd0, 0x75, 0x83, 0x41, 0xd6, 0x0d, 0x07, 0x58, 0x34, 
0x1d, 0x60, 0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xdf, 0xe3, 0x06, 
0xff, 0x18, 0x37, 0xf0, 0x60, 0xfe, 0x0c, 0x1f, 0xc1, 0x83, 0xf8, 0x30, 0x01, 0xcf, 0x80, 0x73, 
0xe0, 0x1c, 0xf8, 0x07, 0x3e, 0x01, 0xcf, 0x81, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x0c, 0xc1, 
0x98, 0x33, 0x06, 0x63, 0x63, 0x63, 0x6d, 0x8d, 0x8d, 0x8d, 0xb6, 0x36, 0x36, 0x36, 0xd8, 0xd8, 
0xd8, 0xdb, 0x63, 0x63, 0x63, 0x6d, 0x81, 0xe0, 0x38, 0x07, 0x80, 0xe0, 0x0c, 0x06, 0x61, 0x80, 
0x30, 0x30, 0x06, 0x06, 0x00, 0xc3, 0x06, 0x30, 0x6d, 0xb0, 0xcc, 0x61, 0xb0, 0x40, 0x08, 0x40, 
0x09, 0x81, 0x88, 0x01, 0x0f, 0xf8, 0xc3, 0x1f, 0xf1, 0x86, 0x3f, 0xe3, 0x0c, 0x7f, 0xc3, 0x0c, 
0x7f, 0xc6, 0x18, 0xff, 0x8c, 0x31, 0xff, 0x18, 0x63, 0xfe, 0x30, 0xc7, 0xfc, 0x61, 0x8f, 0xf8, 
0xc3, 0x1f, 0xf1, 0x86, 0x3f, 0xe3, 0x0d, 0x80, 0x7f, 0xd8, 0x07, 0xfd, 0x80, 0x7f, 0xd8, 0x07, 
0xfc, 0xc0, 0x1f, 0xf6, 0x01, 0xff, 0x60, 0x1f, 0xf6, 0x01, 0xff, 0x18, 0x61, 0x86, 0x60, 0x36, 
0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x19, 0x83, 0x60, 0x33, 0x06, 0xc0, 0x66, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0x60, 0xc6, 0x03, 0x18, 0x31, 0x80, 0xc6, 0x0c, 0x60, 
0x31, 0x83, 0x18, 0x0c, 0x60, 0xc0, 0x30, 0x19, 0x80, 0xc0, 0x66, 0x03, 0x01, 0x98, 0x0c, 0x06, 
0x61, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x30, 0x66, 0x0d, 0x81, 0xb0, 0x6c, 0x0d, 0x83, 0x60, 0x6c, 0x1b, 0x00, 0x60, 0x60, 0x66, 0x0d, 
0x81, 0x98, 0x36, 0x06, 0x60, 0xd8, 0x19, 0x83, 0x60, 0x66, 0x0d, 0x80, 0x60, 0x18, 0x06, 0x01, 
0x80, 0x60, 0x18, 0x06, 0x01, 0x80, 0x60, 0x18, 0x06, 0x30, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0xc1, 0x98, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0x30, 0x60, 0xc1, 0x98, 0xc3, 0x30, 
0xc6, 0x19, 0x86, 0x30, 0xcc, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xc8, 0x61, 0xb0, 0xc3, 
0x43, 0x0d, 0x86, 0x1a, 0x18, 0x6c, 0x30, 0xd0, 0x3d, 0x83, 0x40, 0xf6, 0x0d, 0x03, 0xd8, 0x34, 
0x0f, 0x60, 0xd8, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x03, 0x06, 
0xc0, 0x18, 0x36, 0x18, 0x60, 0xc3, 0x0c, 0x18, 0x61, 0x83, 0x0c, 0x30, 0x00, 0xc3, 0xc0, 0x30, 
0xf0, 0x0c, 0x3c, 0x03, 0x0f, 0x00, 0xc3, 0xc1, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x0c, 0xc1, 
0x98, 0x33, 0x06, 0x63, 0x63, 0x63, 0x6d, 0x8d, 0x8d, 0x8d, 0xb6, 0x36, 0x36, 0x36, 0xd8, 0xd8, 
0xd8, 0xdb, 0x63, 0x63, 0x63, 0x6d, 0x83, 0x30, 0x38, 0x0c, 0xc0, 0xe0, 0x0c, 0x06, 0x61, 0x80, 
0x60, 0x30, 0x0c, 0x06, 0x01, 0x83, 0x06, 0x30, 0x6d, 0xb0, 0xcc, 0xc1, 0xb0, 0x40, 0x08, 0x40, 
0x09, 0x81, 0x88, 0x01, 0x0c, 0x19, 0x83, 0x18, 0x33, 0x06, 0x30, 0x66, 0x0c, 0x60, 0xc6, 0x0c, 
0x60, 0xcc, 0x18, 0xc1, 0x98, 0x31, 0x83, 0x30, 0x63, 0x06, 0x60, 0xc6, 0x0c, 0xc1, 0x8c, 0x19, 
0x83, 0x18, 0x33, 0x06, 0x30, 0x66, 0x0d, 0x80, 0x60, 0x18, 0x06, 0x01, 0x80, 0x60, 0x18, 0x06, 
0x00, 0xc0, 0x18, 0x06, 0x01, 0x80, 0x60, 0x18, 0x06, 0x01, 0x80, 0x18, 0x61, 0x86, 0x60, 0x36, 
0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0x60, 0xd8, 0x0c, 0xc1, 0xb0, 0x19, 0x83, 0x60, 0x33, 0x06, 0xc0, 0x66, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0x60, 0xc6, 0x03, 0x18, 0x31, 0x80, 0xc6, 0x0c, 0x60, 
0x31, 0x83, 0x18, 0x0c, 0x60, 0xc0, 0x30, 0x19, 0x80, 0xc0, 0x66, 0x03, 0x01, 0x98, 0x0c, 0x06, 
0x61, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x60, 0x36, 0x0d, 0x81, 0xb0, 0x6c, 0x0d, 0x83, 0x60, 0x6c, 0x19, 0x83, 0x60, 0x60, 0x66, 0x0d, 
0x81, 0x98, 0x36, 0x06, 0x60, 0xd8, 0x19, 0x83, 0x60, 0x66, 0x0d, 0x80, 0x60, 0x18, 0x06, 0x01, 
0x80, 0x60, 0x18, 0x06, 0x01, 0x80, 0x60, 0x18, 0x06, 0x18, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0xc1, 0x98, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0x30, 0x60, 0xc1, 0x98, 0x63, 0x18, 
0xc3, 0x18, 0xc6, 0x18, 0xc6, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xc8, 0x61, 0xb0, 0xc3, 
0x43, 0x0d, 0x86, 0x1a, 0x18, 0x6c, 0x30, 0xd0, 0x1d, 0x83, 0x40, 0x76, 0x0d, 0x01, 0xd8, 0x34, 
0x07, 0x60, 0xcc, 0x19, 0x83, 0x30, 0x66, 0x0c, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xd8, 0x03, 0x06, 
0xc0, 0x18, 0x36, 0x0c, 0x60, 0xc1, 0x8c, 0x18, 0x31, 0x83, 0x06, 0x30, 0x60, 0xc0, 0xd8, 0x30, 
0x36, 0x0c, 0x0d, 0x83, 0x03, 0x60, 0xc0, 0xc1, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x18, 0x0d, 0x83, 0x60, 0x36, 0x0d, 0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0d, 0x83, 0x07, 0x80, 
0xf0, 0x1e, 0x03, 0xc1, 0xe3, 0xc1, 0xc7, 0x07, 0x8f, 0x07, 0x1c, 0x1e, 0x3c, 0x1c, 0x70, 0x78, 
0xf0, 0x71, 0xc1, 0xe3, 0xc1, 0xc7, 0x06, 0x18, 0x6c, 0x18, 0x61, 0xb0, 0x0c, 0x03, 0xc3, 0x00, 
0xc0, 0x60, 0x18, 0x0c, 0x03, 0x03, 0x06, 0x30, 0x38, 0xe0, 0x78, 0xc1, 0xb0, 0x40, 0x08, 0x40, 
0x09, 0x81, 0x88, 0x01, 0x18, 0x0d, 0x83, 0x30, 0x1b, 0x06, 0x60, 0x36, 0x0c, 0xc0, 0x66, 0x0c, 
0xc0, 0x6c, 0x19, 0x80, 0xd8, 0x33, 0x01, 0xb0, 0x66, 0x03, 0x60, 0xcc, 0x06, 0xc1, 0x98, 0x0d, 
0x83, 0x30, 0x1b, 0x06, 0x60, 0x36, 0x0d, 0x80, 0x60, 0x18, 0x06, 0x01, 0x80, 0x60, 0x18, 0x06, 
0x00, 0xc0, 0x18, 0x06, 0x01, 0x80, 0x60, 0x18, 0x06, 0x01, 0x80, 0x18, 0x61, 0x86, 0x30, 0x66, 
0x0c, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xcc, 0x18, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xcc, 0x19, 0x83, 
0x30, 0x66, 0x0c, 0x60, 0xcc, 0x18, 0xc1, 0x98, 0x31, 0x83, 0x30, 0x63, 0x06, 0x60, 0xc6, 0x0d, 
0x80, 0xd8, 0x36, 0x03, 0x60, 0xd8, 0x0c, 0x60, 0xc6, 0x03, 0x18, 0x31, 0x80, 0xc6, 0x0c, 0x60, 
0x31, 0x83, 0x18, 0x0c, 0x60, 0xc0, 0x30, 0x0f, 0x00, 0xc0, 0x3c, 0x03, 0x00, 0xf0, 0x0c, 0x03, 
0xc1, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x60, 0x36, 0x1d, 0x83, 0x38, 0xcc, 0x19, 0xc6, 0x60, 0xce, 0x31, 0xc3, 0x31, 0x61, 0xc3, 0x1d, 
0x87, 0x0c, 0x76, 0x1c, 0x31, 0xd8, 0x70, 0xc7, 0x61, 0xc3, 0x1d, 0x80, 0x30, 0xd8, 0x03, 0x0d, 
0x80, 0x30, 0xd8, 0x03, 0x0d, 0x80, 0x30, 0x58, 0x06, 0x1c, 0x19, 0x8e, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0xc1, 0x98, 0x0c, 0xc1, 0xb0, 0x1b, 0x06, 0x30, 0x60, 0xc1, 0x98, 0x33, 0x0c, 
0xc1, 0x98, 0x66, 0x0c, 0xc3, 0x30, 0x0c, 0xc0, 0x19, 0x80, 0x33, 0x00, 0xc8, 0x01, 0xb0, 0xc3, 
0x40, 0x0d, 0x86, 0x1a, 0x00, 0x6c, 0x30, 0xd0, 0x0d, 0x83, 0x40, 0x36, 0x0d, 0x00, 0xd8, 0x34, 
0x03, 0x60, 0xce, 0x38, 0xc6, 0x38, 0xe3, 0x18, 0xe3, 0x8c, 0x63, 0x8e, 0x31, 0x98, 0x03, 0x8c, 
0xc0, 0x1c, 0x66, 0x06, 0x60, 0xc0, 0xcc, 0x18, 0x19, 0x83, 0x03, 0x30, 0x61, 0x90, 0xd8, 0x64, 
0x36, 0x19, 0x0d, 0x86, 0x43, 0x61, 0x90, 0xc1, 0x81, 0x80, 0x60, 0x60, 0x18, 0x18, 0x06, 0x06, 
0x0c, 0x18, 0xc7, 0x30, 0x63, 0x1c, 0xc1, 0x8c, 0x73, 0x06, 0x31, 0xcc, 0x18, 0xc7, 0x07, 0x80, 
0xf0, 0x1e, 0x03, 0xc1, 0xc1, 0xc1, 0xc7, 0x07, 0x07, 0x07, 0x1c, 0x1c, 0x1c, 0x1c, 0x70, 0x70, 
0x70, 0x71, 0xc1, 0xc1, 0xc1, 0xc7, 0x0c, 0x0c, 0xc6, 0x30, 0x33, 0x18, 0x0c, 0x03, 0xc6, 0x01, 
0x80, 0xc0, 0x30, 0x18, 0x06, 0x03, 0x06, 0x30, 0x38, 0xe0, 0x78, 0xe3, 0xb0, 0x40, 0x08, 0x40, 
0x09, 0x83, 0x08, 0x01, 0x18, 0x0d, 0x87, 0x30, 0x1b, 0x0e, 0x60, 0x36, 0x1c, 0xc0, 0x66, 0x1c, 
0xc0, 0x6c, 0x39, 0x80, 0xd8, 0x73, 0x01, 0xb0, 0xe6, 0x03, 0x61, 0xcc, 0x06, 0xc3, 0x98, 0x0d, 
0x87, 0x30, 0x1b, 0x0e, 0x60, 0x36, 0x1d, 0x80, 0x30, 0xd8, 0x03, 0x0d, 0x80, 0x30, 0xd8, 0x03, 
0x0c, 0xc0, 0x0c, 0x36, 0x00, 0xc3, 0x60, 0x0c, 0x36, 0x00, 0xc3, 0x18, 0x61, 0x86, 0x38, 0xe3, 
0x18, 0xe3, 0x8c, 0x63, 0x8e, 0x31, 0x8e, 0x38, 0x63, 0x1c, 0x71, 0x8c, 0x71, 0xc6, 0x31, 0xc7, 
0x18, 0xc7, 0x1c, 0x31, 0x8e, 0x38, 0x63, 0x1c, 0x70, 0xc6, 0x38, 0xe1, 0x8c, 0x71, 0xc3, 0x18, 
0xc1, 0x8c, 0x73, 0x06, 0x31, 0xcc, 0x18, 0x31, 0xc3, 0x06, 0x0c, 0x70, 0xc1, 0x83, 0x1c, 0x30, 
0x60, 0xc7, 0x0c, 0x18, 0x31, 0xc0, 0x30, 0x0f, 0x00, 0xc0, 0x3c, 0x03, 0x00, 0xf0, 0x0c, 0x03, 
0xc1, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 

0x60, 0x33, 0xed, 0xfe, 0x37, 0x8f, 0xf1, 0xbc, 0x7f, 0x8d, 0xe0, 0x7e, 0x1e, 0x7f, 0x01, 0xed, 
0xfc, 0x07, 0xb7, 0xf0, 0x1e, 0xdf, 0xc0, 0x7b, 0x7f, 0x01, 0xed, 0xff, 0x1f, 0x9f, 0xf1, 0xf9, 
0xff, 0x1f, 0x9f, 0xf1, 0xf9, 0xff, 0x1f, 0x98, 0x06, 0x07, 0xf0, 0xf6, 0xc0, 0x6c, 0x1b, 0x01, 
0xb0, 0x6c, 0x06, 0xc1, 0x9c, 0x0c, 0xe1, 0xb0, 0x1b, 0x06, 0xfc, 0x63, 0xf1, 0x98, 0x1b, 0x06, 
0xc0, 0xd8, 0x36, 0x06, 0xc1, 0xbf, 0xcc, 0xff, 0x19, 0xfe, 0x33, 0xfc, 0xc8, 0x01, 0xb0, 0xc3, 
0x40, 0x0d, 0x86, 0x1a, 0x00, 0x6c, 0x30, 0xd0, 0x05, 0x83, 0x40, 0x16, 0x0d, 0x00, 0x58, 0x34, 
0x01, 0x60, 0xc3, 0xe0, 0x7c, 0x0f, 0x81, 0xf0, 0x3e, 0x07, 0xc0, 0xf8, 0x1f, 0x18, 0x03, 0x78, 
0xc0, 0x1b, 0xc6, 0x03, 0x60, 0xc0, 0x6c, 0x18, 0x0d, 0x83, 0x01, 0xb0, 0x3f, 0x0f, 0x8f, 0xc3, 
0xe3, 0xf0, 0xf8, 0xfc, 0x3e, 0x3f, 0x0f, 0x81, 0x80, 0xf0, 0x60, 0x3c, 0x18, 0x0f, 0x06, 0x03, 
0xc7, 0xf0, 0x7b, 0x1f, 0xc1, 0xec, 0x7f, 0x07, 0xb1, 0xfc, 0x1e, 0xc7, 0xf0, 0x7b, 0x07, 0x80, 
0x60, 0x1e, 0x01, 0x81, 0xc1, 0xc1, 0x83, 0x07, 0x07, 0x06, 0x0c, 0x1c, 0x1c, 0x18, 0x30, 0x70, 
0x70, 0x60, 0xc1, 0xc1, 0xc1, 0x83, 0x0c, 0x0d, 0x83, 0x30, 0x36, 0x0c, 0x0c, 0x01, 0x87, 0xfd, 
0xfe, 0xff, 0xbf, 0xdf, 0xf7, 0xfb, 0x06, 0x1e, 0x30, 0x60, 0x30, 0x7d, 0xb0, 0x7f, 0xf8, 0x7f, 
0xf9, 0x9e, 0x0f, 0xff, 0x18, 0x0c, 0xfb, 0x30, 0x19, 0xf6, 0x60, 0x33, 0xec, 0xc0, 0x63, 0xec, 
0xc0, 0x67, 0xd9, 0x80, 0xcf, 0xb3, 0x01, 0x9f, 0x66, 0x03, 0x3e, 0xcc, 0x06, 0x7d, 0x98, 0x0c, 
0xfb, 0x30, 0x19, 0xf6, 0x60, 0x33, 0xed, 0xff, 0x1f, 0x9f, 0xf1, 0xf9, 0xff, 0x1f, 0x9f, 0xf1, 
0xf8, 0xff, 0x87, 0xe7, 0xfc, 0x7e, 0x7f, 0xc7, 0xe7, 0xfc, 0x7e, 0x7e, 0x67, 0xe6, 0x0f, 0x81, 
0xf0, 0x3e, 0x07, 0xc0, 0xf8, 0x1f, 0x03, 0xe0, 0x3e, 0x07, 0xc0, 0xf8, 0x1f, 0x03, 0xe0, 0x7c, 
0x0f, 0x81, 0xf0, 0x1f, 0x03, 0xe0, 0x3e, 0x07, 0xc0, 0x7c, 0x0f, 0x80, 0xf8, 0x1f, 0x01, 0xf0, 
0x7f, 0x07, 0xb1, 0xfc, 0x1e, 0xc7, 0xf0, 0x1e, 0xc1, 0xfc, 0x07, 0xb0, 0x7f, 0x01, 0xec, 0x1f, 
0xc0, 0x7b, 0x07, 0xf0, 0x1e, 0xc0, 0x30, 0x06, 0x00, 0xc0, 0x18, 0x03, 0x00, 0x60, 0x0c, 0x01, 
0x81, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe0, 

0x07, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x04, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x20, 0x06, 0x60, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x18, 0x00, 0x00, 0x60, 0x00, 0x01, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x05, 0x00, 0xa0, 0x00, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x08, 0x04, 0x00, 0x00, 0x00, 
0x0c, 0x01, 0x80, 0x00, 0x00, 0x03, 0x00, 0x0c, 0x07, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x0e, 0x07, 0x21, 0xc8, 0x10, 0x04, 0x00, 0x00, 0x00, 0x01, 0x8c, 0x00, 0x00, 0x00, 0x18, 
0x03, 0x00, 0x00, 0x00, 0x04, 0x00, 0x20, 0x06, 0x60, 0xcc, 0x72, 0xe4, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x01, 0x80, 0x00, 0x00, 0x06, 0x0c, 0x18, 0x18, 0x00, 0x00, 0x70, 0xe0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x00, 0x00, 0x03, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x70, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x18, 0x00, 0x00, 0x00, 0x06, 0x03, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x60, 0x30, 0x00, 0x00, 0x07, 0x03, 
0x83, 0x30, 0x66, 0x0e, 0x41, 0xc8, 0x1c, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0c, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 
0x00, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x60, 0x18, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x18, 0x00, 0x01, 0x86, 0x03, 0x00, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 
0x0c, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xc0, 0x06, 0x00, 0x00, 0x06, 0x00, 0xc0, 0x1b, 0x00, 0x00, 0x60, 0x00, 0x01, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x07, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xc0, 0x18, 0x1f, 0x83, 0xf0, 0x70, 0x38, 0x00, 0x00, 0x00, 
0x0c, 0x01, 0x80, 0xfc, 0x1f, 0x86, 0x00, 0x18, 0x0d, 0x81, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x6c, 0x1b, 0x09, 0xc2, 0x70, 0xe0, 0x38, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x18, 
0x03, 0x00, 0x00, 0x00, 0x38, 0x01, 0xc0, 0x03, 0xc0, 0x78, 0x9d, 0x38, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x01, 0x81, 0xf8, 0x3f, 0x06, 0x0c, 0x18, 0x18, 0xfc, 0xfc, 0xd9, 0xb0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x00, 0x00, 0x03, 0x00, 0x60, 0x3f, 0x07, 0xe0, 
0xd8, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x18, 0x00, 0x00, 0x00, 0x06, 0x03, 0x00, 0xc0, 0x60, 0x7e, 0x3f, 0x00, 0x00, 0x03, 0x01, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x60, 0x30, 0x7e, 0x3f, 0x0d, 0x86, 
0xc3, 0x30, 0x66, 0x13, 0x82, 0x70, 0x36, 0x06, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0c, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x0c, 0x06, 0x07, 0xe3, 0xf0, 0xfc, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x60, 0x18, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x18, 0x00, 0x01, 0x86, 0x03, 0x00, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x60, 
0x0c, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xc0, 0x06, 0x00, 0x00, 0x0c, 0x00, 0xc0, 0x33, 0x00, 0x00, 0xc0, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma12bAdd_Font = {0x01, 19, 0, 19, 0, 0, 19, 349, 0x1e00, 0x1eff,
(PEGUSHORT *) Tahoma12bAdd_Font_offset_table, &WinInwa14bMiy_Font,
(PEGUBYTE *) Tahoma12bAdd_Font_data_table};
//...........................................................................


ROMDATA PEGUSHORT Tahoma12bTai_offset_table[92] = {
0x0000,0x000b,0x0016,0x0021,0x002c,0x0037,0x0043,0x004c,0x0056,0x0061,0x006d,0x0079,0x0088,0x0097,0x00a2,0x00ad,
0x00b7,0x00c4,0x00d4,0x00e4,0x00ef,0x00fa,0x0105,0x0111,0x011b,0x0126,0x0131,0x013c,0x0148,0x0154,0x0161,0x016e,
0x0179,0x0184,0x018f,0x0197,0x01a2,0x01ac,0x01b7,0x01c0,0x01cb,0x01d7,0x01e1,0x01ec,0x01f9,0x0203,0x020d,0x0216,
0x021e,0x0226,0x022f,0x023c,0x0245,0x024e,0x0257,0x0260,0x0263,0x0269,0x026b,0x027b,0x028b,0x029b,0x02ab,0x02b6,
0x02bb,0x02c5,0x02cd,0x02d5,0x02dd,0x02e6,0x02ef,0x02f6,0x02f9,0x02ff,0x0305,0x0309,0x030e,0x0312,0x0316,0x0320,
0x032a,0x0335,0x0341,0x034c,0x0358,0x0364,0x036f,0x037e,0x038a,0x0398,0x03a4,0x03b5};



ROMDATA PEGUBYTE Tahoma12bTai_data_table[2261] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x08, 0x14, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x10, 
0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x80, 
0x00, 0x00, 0x00, 0x01, 0x08, 0x00, 0x00, 0x03, 0x81, 0xf0, 0xf4, 0x74, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x03, 0xf3, 0xe4, 0xc0, 0x00, 0x01, 0x02, 0x2b, 
0xca, 0x0c, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x16, 0x80, 
0x00, 0x00, 0x00, 0x02, 0x90, 0x00, 0x80, 0x04, 0x42, 0x31, 0x18, 0x8c, 0x00, 0x07, 0xff, 0x87, 
0xff, 0x87, 0xff, 0x87, 0xff, 0x9f, 0xc0, 0x00, 0x06, 0x05, 0x34, 0xe0, 0x00, 0x01, 0x7a, 0x16, 
0x2f, 0x71, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 
0x00, 0x00, 0x00, 0x01, 0xe0, 0x01, 0x40, 0x0f, 0xe7, 0xf3, 0xf9, 0xfc, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x60, 0x00, 0x07, 0xc2, 0x32, 0xe0, 0x00, 0x00, 0xda, 0x18, 
0x32, 0x42, 0x80, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x06, 0x00, 0x6c, 0x00, 0x00, 0x18, 
0x01, 0x80, 0x06, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x03, 0x00, 0x00, 0x06, 0x00, 
0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x30, 0x00, 0x00, 0x60, 0x33, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x06, 0x08, 0x66, 0x00, 0x00, 0x18, 
0x01, 0x80, 0x06, 0x00, 0x00, 0x00, 0x00, 

0x3f, 0x0f, 0x19, 0xb3, 0x1f, 0x83, 0x70, 0xd8, 0xc0, 0xc3, 0xe0, 0xf8, 0x3c, 0x33, 0x63, 0x1f, 
0x06, 0x3e, 0x0c, 0x3e, 0x07, 0xc1, 0xfc, 0xd8, 0xc3, 0xb8, 0x63, 0xc1, 0x83, 0xf0, 0x6e, 0x0f, 
0xc3, 0x06, 0x3f, 0xcc, 0x19, 0x83, 0x30, 0x66, 0x06, 0x60, 0x66, 0x33, 0x31, 0x98, 0x7c, 0x70, 
0x66, 0x0c, 0xfc, 0x7e, 0x1f, 0x81, 0xf0, 0xf8, 0x3f, 0x8c, 0x18, 0xfe, 0x30, 0x46, 0x26, 0x3f, 
0x0f, 0xe2, 0x19, 0x04, 0x01, 0xf0, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x33, 0x0c, 0x60, 0x60, 0x61, 0x63, 0xe0, 0xd8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0c, 0x14, 0xc2, 0x00, 0x00, 0x18, 
0x03, 0x00, 0x06, 0x43, 0x60, 0x00, 0x00, 

0x61, 0x95, 0x9b, 0xdb, 0x30, 0xc6, 0x99, 0xec, 0xc1, 0x64, 0x31, 0x0c, 0x56, 0x67, 0xb6, 0x31, 
0x86, 0x63, 0x0c, 0x63, 0x0c, 0x63, 0x01, 0xed, 0x66, 0x4c, 0x66, 0x61, 0x86, 0x18, 0xd3, 0x18, 
0x65, 0x8b, 0x60, 0x16, 0x1a, 0xc3, 0x58, 0x6d, 0x06, 0xd0, 0x6b, 0x33, 0x59, 0x98, 0xc6, 0x58, 
0x6d, 0x0d, 0x80, 0xc3, 0x20, 0xc3, 0x19, 0x0c, 0x63, 0x96, 0x19, 0x0f, 0x58, 0xab, 0x26, 0x41, 
0x98, 0x75, 0x3a, 0x98, 0x02, 0x18, 0x10, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x63, 0x0c, 0x60, 0x60, 0x60, 0x64, 0x31, 0xac, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x04, 0x3e, 0x0f, 0xc6, 0x7c, 0x3f, 0xc3, 0xf8, 0x3f, 0x83, 0xf0, 0xff, 0x18, 
0xfe, 0x1e, 0xc4, 0xa7, 0x63, 0xc0, 0x00, 

0xc0, 0xc9, 0x9a, 0x9b, 0x60, 0x6c, 0x0d, 0x4c, 0xc0, 0xe1, 0x98, 0x06, 0x26, 0x35, 0x33, 0x60, 
0xc6, 0xc1, 0x8c, 0xc1, 0x98, 0x33, 0xf1, 0x4d, 0x6c, 0x06, 0x6c, 0x31, 0x8c, 0x0d, 0x81, 0xb0, 
0x33, 0x93, 0x7f, 0x0e, 0x19, 0xc3, 0x38, 0x6e, 0x06, 0xe0, 0x67, 0x33, 0x39, 0x99, 0x83, 0x38, 
0x6e, 0x0d, 0xf9, 0x81, 0x80, 0x66, 0x0c, 0x06, 0xc4, 0xce, 0x18, 0x13, 0x38, 0x47, 0x56, 0x00, 
0xcf, 0xb3, 0xd9, 0xf0, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x9f, 0xe3, 0x0c, 0x60, 0x60, 0x60, 0x60, 0x1a, 0x8c, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x02, 0x63, 0x18, 0x66, 0xc3, 0x66, 0x66, 0x00, 0x60, 0x00, 0x19, 0x99, 0x99, 
0x80, 0x33, 0xac, 0x7b, 0x64, 0x20, 0x00, 

0xe0, 0xc1, 0x99, 0x1b, 0x6c, 0x6d, 0x8c, 0x8c, 0xcc, 0x62, 0xd9, 0x86, 0x06, 0x32, 0x33, 0x70, 
0xc6, 0xe1, 0x8c, 0xf1, 0x9e, 0x30, 0x18, 0x8d, 0x6c, 0x86, 0x6e, 0x31, 0x8c, 0x8d, 0x91, 0xb8, 
0x31, 0x93, 0x31, 0x86, 0x18, 0xc3, 0x18, 0x6c, 0x46, 0xc0, 0x63, 0x4b, 0x1a, 0x59, 0xe3, 0x18, 
0x6c, 0x0c, 0x0d, 0xc1, 0x9c, 0x67, 0x8c, 0x06, 0xd8, 0xc6, 0x18, 0xe3, 0x18, 0xe3, 0x56, 0x30, 
0xcc, 0x30, 0x18, 0x00, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x33, 0x0c, 0x60, 0x60, 0x60, 0x60, 0x19, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x02, 0xc1, 0xb0, 0x36, 0xa3, 0x66, 0x6c, 0x00, 0xc0, 0x00, 0x0d, 0x99, 0x9b, 
0x03, 0x33, 0x18, 0x03, 0x68, 0x00, 0x00, 

0x40, 0xc3, 0x18, 0x33, 0x7a, 0x6f, 0x4c, 0x18, 0xc6, 0x61, 0xfa, 0xc6, 0x0c, 0x30, 0x63, 0x20, 
0xc6, 0x41, 0x8c, 0x61, 0x8c, 0x30, 0xcc, 0x1a, 0x6d, 0x46, 0x64, 0x31, 0x8d, 0x4d, 0xa9, 0x90, 
0x31, 0xa3, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x6c, 0xe6, 0xce, 0x63, 0x4b, 0x1a, 0x58, 0xc3, 0x18, 
0x66, 0x0c, 0x0c, 0x81, 0xb2, 0x63, 0x0c, 0x06, 0xf4, 0xc6, 0x5b, 0x93, 0x19, 0x63, 0x56, 0x68, 
0xda, 0x30, 0x18, 0x00, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x1b, 0x0c, 0x60, 0x60, 0x60, 0x60, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x22, 0xc1, 0xb0, 0x36, 0x43, 0x66, 0x6c, 0x10, 0xc1, 0x00, 0x0d, 0x99, 0x9b, 
0x05, 0xb1, 0x80, 0x03, 0x68, 0x00, 0x00, 

0xc0, 0xc3, 0x18, 0x33, 0x7c, 0x6f, 0x8c, 0x18, 0xc6, 0x60, 0x79, 0xc6, 0x0c, 0x30, 0x63, 0x60, 
0xc6, 0xc1, 0x8c, 0x61, 0x8c, 0x31, 0x6c, 0x1a, 0x6c, 0xc6, 0x6c, 0x31, 0x8c, 0xcd, 0x99, 0xb0, 
0x31, 0xa3, 0x30, 0xc6, 0x18, 0xc3, 0x18, 0x6d, 0xb6, 0xdf, 0x63, 0x87, 0x1c, 0x38, 0xc3, 0x18, 
0x6c, 0x0c, 0x0d, 0x81, 0xb1, 0x63, 0x0c, 0x06, 0xf8, 0xc6, 0xbd, 0x8b, 0x1a, 0x63, 0x8e, 0x70, 
0xdc, 0x30, 0x19, 0x04, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x1b, 0x0c, 0x60, 0x60, 0x60, 0x60, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x02, 0xc1, 0xb2, 0x36, 0x03, 0x60, 0x6c, 0x28, 0xc2, 0x81, 0x0d, 0x81, 0x9b, 
0x33, 0xb1, 0x80, 0x03, 0x68, 0x12, 0x88, 

0xc0, 0xc3, 0x18, 0x33, 0x60, 0x6c, 0x0c, 0x1e, 0xc3, 0x60, 0x38, 0xde, 0x0c, 0x30, 0x63, 0x71, 
0xe6, 0xe1, 0x8c, 0xe1, 0x9c, 0x30, 0xbc, 0x1a, 0x6d, 0x8f, 0x6e, 0x33, 0xcd, 0x8d, 0xb1, 0xb8, 
0x31, 0xc3, 0x30, 0xc6, 0x78, 0xc3, 0x18, 0x6f, 0x1e, 0xfb, 0xe3, 0x87, 0x1c, 0x39, 0xc3, 0x3e, 
0x6c, 0x0c, 0x1d, 0xc1, 0xb8, 0xe7, 0x0c, 0x0e, 0xc0, 0xc6, 0x79, 0xc7, 0x1a, 0x63, 0x8e, 0x60, 
0xd8, 0x30, 0x1a, 0x98, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x1b, 0x8e, 0x70, 0x70, 0x70, 0x70, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x02, 0xc1, 0xb5, 0x36, 0x03, 0x70, 0x6c, 0x30, 0xc3, 0x02, 0x8d, 0xc1, 0x9b, 
0x68, 0xb9, 0x80, 0x03, 0x68, 0x1f, 0x88, 

0xc0, 0xc3, 0x18, 0x33, 0x60, 0x6c, 0x0c, 0x2f, 0xc3, 0x60, 0x18, 0xfd, 0x0c, 0x30, 0x63, 0x6a, 
0xde, 0xd1, 0x8d, 0x61, 0xac, 0x30, 0x1c, 0x1c, 0x6f, 0x16, 0xed, 0x3d, 0xaf, 0x0d, 0xe1, 0xb4, 
0x31, 0xc3, 0x30, 0xc7, 0xf4, 0xc3, 0x18, 0x6e, 0x0e, 0xe0, 0xe3, 0x87, 0x1c, 0x3a, 0xc3, 0x5b, 
0xec, 0x0c, 0x2d, 0xa1, 0xb4, 0x6b, 0x0c, 0x16, 0xc0, 0xc6, 0x19, 0xa3, 0x1c, 0x63, 0x06, 0x60, 
0xd8, 0x30, 0x19, 0xf0, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x84, 
0x00, 0x84, 0x00, 0x84, 0x00, 0x99, 0x33, 0x4d, 0x68, 0x68, 0x68, 0x68, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x04, 0x63, 0x1b, 0x36, 0x03, 0x68, 0x66, 0x30, 0x63, 0x03, 0x19, 0xa1, 0x99, 
0xed, 0xb4, 0xc0, 0x03, 0x64, 0x29, 0x70, 

0xc0, 0xc3, 0xf8, 0x3f, 0x60, 0x6c, 0x0c, 0x19, 0xc1, 0xc0, 0x18, 0xe2, 0x0f, 0xf0, 0x7f, 0x31, 
0x86, 0x61, 0xfc, 0xc1, 0x98, 0x30, 0x0c, 0x1c, 0x6e, 0x0c, 0x26, 0x30, 0xce, 0x0d, 0xc1, 0x98, 
0x31, 0x83, 0x7f, 0x87, 0x88, 0xff, 0x1f, 0xec, 0x06, 0xc0, 0x63, 0x03, 0x18, 0x19, 0x83, 0x70, 
0xef, 0xfc, 0x18, 0xc1, 0x98, 0x66, 0x0c, 0x0c, 0xc0, 0xc7, 0xf8, 0xc3, 0x18, 0x63, 0x06, 0x7f, 
0xdf, 0xf0, 0x18, 0x00, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0x87, 
0xff, 0x87, 0xff, 0x87, 0xff, 0x9f, 0xe1, 0x86, 0x30, 0x30, 0x30, 0x30, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xf8, 0x3e, 0x0e, 0x67, 0xfe, 0x30, 0xc3, 0xfe, 0x3f, 0xe1, 0xf0, 0xc3, 0xf0, 
0xe7, 0x18, 0xc0, 0x03, 0x63, 0xc0, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x84, 0x1b, 0x8d, 0x30, 0x4c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xc8, 0x27, 0x96, 0xb3, 0xd4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd9, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xf0, 0x1b, 0x8d, 0x73, 0xac, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4f, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma12bTai_Font = {0x01, 19, 0, 19, 0, 0, 19, 119, 0x0e01, 0x0e5b,
(PEGUSHORT *) Tahoma12bTai_offset_table, &Tahoma12bAdd_Font,
(PEGUBYTE *) Tahoma12bTai_data_table};


