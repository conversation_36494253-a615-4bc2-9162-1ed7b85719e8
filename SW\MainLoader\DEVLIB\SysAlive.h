/*...........................................................................*/
/*.                  File Name : SYSALIVE.H                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSALIVE_H__
#define  __SYSALIVE_H__

#ifdef  __cplusplus
extern "C" {
#endif

void  SysSetALIVE(void);
void  SysSetALIVExBitData(int nBitNo,int nBitData);
DWORD SysGetALIVExBitData(int nBitNo);
void  SysSetALIVExBitMode(int nBitNo,int nBitInOut);
void  SysSetALIVExBitPullUpMode(int nBitNo,int nMode);
void  SysSetALIVExBitOutEnableMode(int nBitNo,int nMode);

#ifdef  __cplusplus
}
#endif

#endif

