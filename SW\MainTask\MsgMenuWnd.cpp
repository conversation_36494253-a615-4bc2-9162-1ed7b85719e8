#include <stdio.h>
#include "MsgMenuWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "AllConst.h"
#include "Resource.h"
#include "Font.h"

extern CDocMgr* g_pDocMgr;

CMsgMenuWnd::CMsgMenuWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID), m_nSelNum(1)
{
}

void CMsgMenuWnd::DrawWnd(BOOL bRedraw/*TRUE*/)
{
	int nLangMode = g_pDocMgr->GetLangMode();

	CWnd::DrawWnd(bRedraw);

	DrawSubMenu(m_nSelNum);
	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
	DrawButton(1, (BYTE *)FK_EXIT[nLangMode]);

	EraseButton(2);
	EraseButton(3);
}

void CMsgMenuWnd::DrawSubMenu(int nSelNum)
{
	int nLangMode = g_pDocMgr->GetLangMode();
	BOOL   bCoastalMode = g_pDocMgr->IsCoastalMode();
	int i = 0;
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0, nYOffset = 0;
	UCHAR *pArrBmp = NULL;
	HWORD *pUniCodeStr = NULL;
	COLORT clrTxt;
	COLORT clrUpLine;
	COLORT clrDnLine;

	if(m_pScreen != NULL)
	{
		// Draw Menu
		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:
				pFont = &NewGulLim18bCJK;
				nYOffset = 2;
				break;

			case LANG_RUS:
				pFont = &MyriadPro30bRus;
				nYOffset = 2;
				break;
				
			default:
				pFont = &MyriadPro30bEng;
				nYOffset = 6;
				break;
		}
		
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		for(i = 0; i < MAX_MENU_BAR; i++)
		{
			if(m_nScheme == CS_DAY_BRIGHT)
			{
				clrUpLine = MNU_DAY_UP_LINE;
				clrDnLine = MNU_DAY_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrDayOn;

					if( bCoastalMode )
					{
						clrTxt = RGB(0x80, 0x80, 0x80);
					}
					else
					{
						clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
					}
				}
				else
				{
					if( bCoastalMode )
					{
						clrTxt = RGB(0x80, 0x80, 0x80);
					}
					else
					{
						clrTxt = COLORSCHEME[m_nScheme].crFore;
					}
					
					pArrBmp = G_BmpArrDayOff;
				}
			}
			else
			{
				clrUpLine = MNU_NIGHT_UP_LINE;
				clrDnLine = MNU_NIGHT_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrNightOn;
					if( bCoastalMode )
					{
						clrTxt = RGB(0x80, 0x80, 0x80);
					}
					else
					{
						clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
					}
				}
				else
				{
					if( bCoastalMode )
					{
						clrTxt = RGB(0x80, 0x80, 0x80);
					}
					else
					{
						clrTxt = COLORSCHEME[m_nScheme].crFore;
					}
					pArrBmp = G_BmpArrNightOff;
				}
			}
			
			if(nSelNum == (i+1))
			{
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crLetterIcon);	
				
			}
			else
			{
									
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crBack);	
			}

			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							clrUpLine);
			
			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							clrDnLine);

			nXPos = BASE_WND_AREA_X_POS + 5;
			nYPos = WND_BACK_Y_POS + i*MNU_BAR_H + (MENU_ITEM_H - nFontH)/2 + nYOffset;

			switch(i)
			{
				case 0:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_NEW[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_MSG_NEW[nLangMode];
					break;

				case 1:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_FAVOR[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_MSG_FAVOR[nLangMode];
					break;

				case 2:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_LRM[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MSG_LRM[nLangMode];
					break;

				case 3:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_RX_LIST[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MSG_RX_LIST[nLangMode];
					break;

				case 4:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_TX_LIST[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_MSG_TX_LIST[nLangMode];
					break;

				case 5:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_ALARM_LIST[nLangMode],nLangMode);	
					if(g_pDocMgr->GetEnBAMAlert() == TRUE)						
						pUniCodeStr = (HWORD *)MNU_MSG_ALERT_LIST[nLangMode];
					else
						pUniCodeStr = (HWORD *)MNU_MSG_ALARM_LIST[nLangMode];
					break;

				case 6:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_STATUS_LIST[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MSG_STATUS_LIST[nLangMode];
					break;

				case 7:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_MSG_WEATHER_LIST[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_MSG_WEATHER_LIST[nLangMode];
					break;					
			}

			switch(i)
			{
				case 0:
				case 1:
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);	

					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;

				case 7:
					if(nLangMode == LANG_KOR)
					{
						nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
						m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);	

						m_pScreen->DrawBitMap(	m_nScrXSize,
												m_nScrYSize,
												MNU_BAR_ARR_X_POS,
												WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
												pArrBmp,
												CLR_BIT_MAP_TRANS,
												0);
					}
					break;
			}
		}

		m_pScreen->SetFont(pOldFont);
	}
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CMsgMenuWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	int nLangMode = g_pDocMgr->GetLangMode();
	
	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
		case KBD_SCAN_CODE_LEFT:
			if(nLangMode == LANG_KOR)
			{
				if( g_pDocMgr->IsCoastalMode() )
				{
					m_nSelNum = (m_nSelNum == 6) ? 7 : 6;
				}
				else
				{
					if( m_nSelNum <= 1 ) m_nSelNum = 8;
					else m_nSelNum--;
				}
			}
			else
			{
				if( g_pDocMgr->IsCoastalMode() )
				{
					m_nSelNum = (m_nSelNum == 6) ? 7 : 6;
				}
				else
				{
					if( m_nSelNum <= 1 ) m_nSelNum = 7;
					else m_nSelNum--;
				}
			}
			DrawSubMenu(m_nSelNum);
			break;
			
		case KBD_SCAN_CODE_DOWN:
		case KBD_SCAN_CODE_RIGHT:
			if(nLangMode == LANG_KOR)
			{
				if( g_pDocMgr->IsCoastalMode() )
				{
					m_nSelNum = (m_nSelNum == 6) ? 7 : 6;
				}
				else
				{
					if( m_nSelNum >= 8 ) m_nSelNum = 1;
					else m_nSelNum++;
				}
			}
			else
			{
				if( g_pDocMgr->IsCoastalMode() )
				{
					m_nSelNum = (m_nSelNum == 6) ? 7 : 6;
				}
				else
				{
					if( m_nSelNum >= 7 ) m_nSelNum = 1;
					else m_nSelNum++;
				}
			}
			DrawSubMenu(m_nSelNum);
			break;
	}
}
