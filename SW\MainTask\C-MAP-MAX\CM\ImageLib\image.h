#ifndef _IMAGEINFO_H
#define _IMAGEINFO_H

#include "cmaptype.h"
#include "cm/dbase.h"
#include "cm.h"

#define IMG_RESERVED    0
#define IMG_PNG         1
#define IMG_JPG_PALETTE 2
#define IMG_JPG         3
#define IMG_WI          4

#define MAX_X_IMAGE		640        /*Max image width*/
#define MAX_Y_IMAGE		480        /*Max image height*/

typedef enum ImageStatus{
	IMG_FINISH,
	IMG_NOERROR,
	IMG_ERROR
}eImageStatus;

#define SIZEOF_RGBENTRY	3

typedef struct 
{
	Byte red;
	Byte green;
	Byte blue;
}sRGBEntry;


/**
 * Structure used to describe an image
 *
 * This structure contains all the data used to describe the images supported by 
 *  the library.
 *
 * @version 6.0.0.0
 *
 * @since version 7.0.0.2
 *				Added the fields TransparentAvailable and TransparentColor
 * 
 * @see	\c cmGetBinaryImageInfo(); \c cmGetFirstBinaryImageBlock(); \c cmGetNextBinaryImageBlock();
 *
 * \ingroup CM_IMAGE_FUNCTIONS
 */

typedef struct nsImageInfo
{
	Byte type;						///<Is the type of the image: IMG_RESERVED, IMG_PNG, IMG_JPG_PALETTE, IMG_JPG, IMG_RESERVED, IMG_WI
	Byte bpp;						///<Is the number of Byte per Pixel of the image
	sRGBEntry Palette_256[256];		///<This array contains the palette optimized for the iamge (256 colors)
	sRGBEntry Palette_16[16];		///<This array contains the palette optimized for the iamge (16 colors)
	Word width;						///<Image width
	Word height;					///<Image height
	Word BuffWidth;					///<Dimension (in byte) of the buffer to store a row of the image
	Word BuffHeight;				///<Height of buffer used to store the image (until today this value is 1)
	Byte *buffer;					///<Pointer to the buffer used to contains a raw of the image
	Word NumOfUsedColors;			///<Number of color used in the image (used only for the IMG_PNG)
	Bool TransparentAvailable;		///<If TRUE image have transparency FALSE otherwise (since version 7.0.0.2)
	Long TransparentColor;			///<Is the color (RGB) or palette index used as transparent (since version 7.0.0.2)
}sImageInfo;


#ifdef __cplusplus
extern "C"
{
#endif /*__cplusplus*/

PRE_EXPORT_H eImageStatus IN_EXPORT_H cmGetBinaryImageInfo(sImageInfo *,  sMultimediaElement*);
PRE_EXPORT_H eImageStatus IN_EXPORT_H cmGetFirstBinaryImageBlock(sImageInfo *,  sMultimediaElement *);
PRE_EXPORT_H eImageStatus IN_EXPORT_H cmGetNextBinaryImageBlock(sImageInfo *,  sMultimediaElement *);

#ifndef USE_OEM_NEARESTCOLOR
PRE_EXPORT_H void IN_EXPORT_H cmInitNearestColor( void );
PRE_EXPORT_H Word IN_EXPORT_H cmGetNearestColor(sRGBEntry* color);

PRE_EXPORT_H Bool IN_EXPORT_H cmInitErrorDiffusion(Word X_WIDTH);
PRE_EXPORT_H Byte IN_EXPORT_H cmErrorDiffusion(Word x, const sRGBEntry *m,Bool NewLine);

typedef PRE_EXPORT_H Word ( IN_EXPORT_H *GetNearestColorFunc) ( sRGBEntry* color );
PRE_EXPORT_H void IN_EXPORT_H cmSetNearestColorFunc( GetNearestColorFunc NerestFunc );

PRE_EXPORT_C Word IN_EXPORT_C cmOrderedDither(Word x, Word y, sRGBEntry *color);
#endif

#ifdef __cplusplus
}
#endif /*__cplusplus*/

#endif
