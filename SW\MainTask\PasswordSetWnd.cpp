#include <stdio.h>
#include "PasswordSetWnd.hpp"
#include "DocMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Font.h"

#define PWD_SET_WND_X_POS		10
#define PWD_SET_WND_Y_POS		54

#define PWD_SET_WND_W			580
#define PWD_SET_WND_H			370

#define PWD_SET_WND_ROW_H		37

#define PWD_SET_WND_CAP_END_X_POS	(PWD_SET_WND_X_POS + 270)
#define PWD_SET_WND_CTRL_X_POS		(PWD_SET_WND_X_POS + 280)
#define PWD_SET_WND_CTRL_W			130
#define PWD_SET_WND_CTRL_H			30

extern CDocMgr *g_pDocMgr;

/*********************************************************************************************************/
// Name		: CPasswordSetWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CPasswordSetWnd::CPasswordSetWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	m_nFocus = 0;
	
	m_pNewPassword     = new CEditCtrl(pScreen);
	m_pConfirmPassword = new CEditCtrl(pScreen);

	m_pNewPassword->Create(	PWD_SET_WND_CTRL_X_POS, 
							PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*3 + (PWD_SET_WND_ROW_H - PWD_SET_WND_CTRL_H)/2, 
							PWD_SET_WND_CTRL_W, 
							PWD_SET_WND_CTRL_H, 6, 1, 1);
	
	m_pConfirmPassword->Create(	PWD_SET_WND_CTRL_X_POS, 
								PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*5 + (PWD_SET_WND_ROW_H - PWD_SET_WND_CTRL_H)/2, 
								PWD_SET_WND_CTRL_W, 
								PWD_SET_WND_CTRL_H, 6, 1, 1);

}

/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordSetWnd::DrawWnd(BOOL bRedraw)
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 2;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 4;
			break;
			
		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 4;
			break;
	}
	
    CWnd::DrawWnd(bRedraw);
	
	if( bRedraw )
	{
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		pUniCodeStr = (HWORD *)STR_PASSWORD_NEW[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);

		nXPos = (PWD_SET_WND_CAP_END_X_POS - nStrW);
		nYPos = PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*3 + (PWD_SET_WND_ROW_H - pFont->uHeight)/2 + nYOffset;
		
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

		pUniCodeStr = (HWORD *)STR_PASSWORD_CONFIRM[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);

		nXPos = (PWD_SET_WND_CAP_END_X_POS - nStrW);
		nYPos = PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*5 + (PWD_SET_WND_ROW_H - pFont->uHeight)/2 + nYOffset;
		
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
	
		m_pScreen->SetFont(pOldFont);
		
		DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
		DrawButton(1, (BYTE *)FK_SAVE[nLangMode]);
		EraseButton(2);
		EraseButton(3);
	}

	switch( m_nFocus )
	{
		case FOCUS_PASSWORD_NEW:
			m_pNewPassword->SetFocus(1);
			m_pConfirmPassword->SetFocus(0);
			break;
			
		case FOCUS_PASSWORD_CONFIRM:
			m_pNewPassword->SetFocus(0);
			m_pConfirmPassword->SetFocus(1);
			break;
	}
	
	m_pNewPassword->DrawWnd();
	m_pConfirmPassword->DrawWnd();
}

/*********************************************************************************************************/
// Name		: OnCursorEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordSetWnd::OnCursorEvent(int nState)
{
	switch( m_nFocus )
	{
		case FOCUS_PASSWORD_NEW:
			m_pNewPassword->OnCursorEvent(nState);
			break;
		
		case FOCUS_PASSWORD_CONFIRM:
			m_pConfirmPassword->OnCursorEvent(nState);
			break;
	}
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordSetWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	BYTE  szPassword[8];
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	HWORD *pUniCodeStr = NULL;
	int nFontH = 0, nStrW = 0;
	int nXPos = 0, nYPos = 0, nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pFont = &NewGulLim18bCJK;
			nYOffset = 2;
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bRus;
			nYOffset = 2;
			break;
			
		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 2;
			break;
	}

	switch( nKey )
	{
		case KBD_SCAN_CODE_DOWN:
			if( m_nFocus == FOCUS_PASSWORD_NEW )
			{
				if( m_pNewPassword->GetEditMode() )
					break;

				m_pNewPassword->GetText(szPassword);
				
				if( strlen((char *)szPassword) < 6 ) 
				{
					pOldFont = m_pScreen->SetFont(pFont);
					nFontH = pFont->uHeight;
				
					pUniCodeStr = (HWORD *)STR_UNI_SHORT_PWD[nLangMode];
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					
					nXPos = PWD_SET_WND_X_POS + (PWD_SET_WND_W - nStrW)/2;
					nYPos = PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*7 + (PWD_SET_WND_ROW_H - pFont->uHeight)/2;

					m_pScreen->FillRect(PWD_SET_WND_X_POS,
										PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*7,
										PWD_SET_WND_X_POS + PWD_SET_WND_W-1,
										PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);
					
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crWarn);
					m_pScreen->SetFont(pOldFont);

					m_pNewPassword->Reset();
					break;
				}
			}

			if( m_nFocus < FOCUS_PASSWORD_CONFIRM ) 
			{
				m_nFocus++;
				DrawWnd(0);
			}
			break;

		default:
			switch( m_nFocus )
			{
				case FOCUS_PASSWORD_NEW:
					m_pScreen->FillRect(PWD_SET_WND_X_POS,
										PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*7,
										PWD_SET_WND_X_POS + PWD_SET_WND_W-1,
										PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);
					
					m_pNewPassword->OnKeyEvent(nKey, nFlags);
					break;
				
				case FOCUS_PASSWORD_CONFIRM:
					m_pConfirmPassword->OnKeyEvent(nKey, nFlags);
					break;
			}
			break;
	}
}

/*********************************************************************************************************/
// Name		: IsNewConfirmEqual
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
BOOL CPasswordSetWnd::IsNewConfirmEqual()
{
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0, nStrW = 0;
	int nXPos = 0, nYPos= 0;
	HWORD *pUniCodeStr = NULL;
	int nYOffset = 0;
	int nLangMode = g_pDocMgr->GetLangMode();

	if( m_nFocus != FOCUS_PASSWORD_CONFIRM )
		return FALSE;

	BYTE  szNewPassword[8], szConfirmPassword[8];
	DWORD dNewPassword, dConfirmPassword;

	m_pNewPassword->GetText(szNewPassword);
	m_pConfirmPassword->GetText(szConfirmPassword);

	sscanf((char *)szNewPassword, "%d", &dNewPassword);
	sscanf((char *)szConfirmPassword, "%d", &dConfirmPassword);

	if( dNewPassword == dConfirmPassword) 
	{
		return TRUE;
	}		
	else
	{
		m_pNewPassword->Reset();
		m_pConfirmPassword->Reset();
		m_nFocus = FOCUS_PASSWORD_NEW;

		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:
				pFont = &NewGulLim18bCJK;
				nYOffset = 2;
				break;

			case LANG_RUS:
				pFont = &MyriadPro24bRus;
				nYOffset = 2;
				break;
				
			default:
				pFont = &MyriadPro24bEng;
				nYOffset = 2;
				break;
		}
	
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		
		pUniCodeStr = (HWORD *)STR_UNI_WRONG_PWD[nLangMode];
		nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
		
		nXPos = PWD_SET_WND_X_POS + (PWD_SET_WND_W - nStrW)/2;
		nYPos = PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*7 + (PWD_SET_WND_ROW_H - pFont->uHeight)/2;

		m_pScreen->FillRect(PWD_SET_WND_X_POS,
							PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*7,
							PWD_SET_WND_X_POS + PWD_SET_WND_W-1,
							PWD_SET_WND_Y_POS + PWD_SET_WND_ROW_H*8 -1 ,COLORSCHEME[m_nScheme].crBack);
		
		m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crWarn);

		m_pScreen->SetFont(pOldFont);

		DrawWnd(0);
		return FALSE;
	}
}

/*********************************************************************************************************/
// Name		: ResetPasswordBox
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CPasswordSetWnd::ResetPasswordBox()
{
	m_pNewPassword->Reset();
	m_pConfirmPassword->Reset();

	m_pNewPassword->SetEditMode(0);
	m_pConfirmPassword->SetEditMode(0);
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CPasswordSetWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int   nResult = CWnd::CloseAlert(nKey, bMkdAlert);

	BYTE  szNewPassword[8];
	DWORD dPassword;

	m_pNewPassword->GetText(szNewPassword);
	sscanf((char *)szNewPassword, "%d", &dPassword);

	switch( nResult )
	{
		case AL_YES:
			g_pDocMgr->SetUserPassword(dPassword);
			g_pDocMgr->SaveSettingValue();
			break;
		
		case AL_NO:
			break;
	}

	return nResult;
}
