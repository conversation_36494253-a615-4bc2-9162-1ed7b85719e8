/*...........................................................................*/
/*.                  File Name : CalcDstWin.cpp                             .*/
/*.                                                                         .*/
/*.                       Date : 2008.12.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#if 0
#include "DataType.h"
#include "DataType.hpp"
#include "_AllStringY.hpp"
#include "AllVarY.hpp"
#include "AllResource.hpp"
#include "CpuAddr.h"
#include "SysConst.h"
#include "AllConst.h"
#include "KeyConst.h"
#include "SysLib.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "GrLib.h"
#include "datum.hpp"
#include "KeyBD.hpp"
#include "DataBack.hpp"
#include "ChartMainWin.hpp"
#endif

#include "CalcDstWin.hpp"

/*********************************************************************************************************/
// Name		: cCalcDstWin
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
cCalcDstWin::cCalcDstWin(CWnd *pParent)
{
	m_pParent     = pParent;

#if 0	// Temp
	SetWinBlendMode(MODE_VAL_ON);

	this->AddStatus(PSF_VIEWPORT);
	this->AddStatus(PSF_ALWAYS_ON_TOP);
	this->RemoveStatus(PSF_ACCEPTS_FOCUS);
#endif	
}

/*********************************************************************************************************/
// Name		: ~cCalcDstWin
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
cCalcDstWin::~cCalcDstWin(void)
{
}

#if 0	// Temp
int cCalcDstWin::Message(const sMessage &xMsg)
{
	switch (xMsg.Type)
	{
		case  PM_SHOW :
			cMyWin::Message(xMsg);
			break;
		
		case  PM_KEY :
			break;
			
		case  PM_HIDE :
			cMyWin::Message(xMsg);
			break;

		default :
			return(cMyWin::Message(xMsg));
	}
	return(0);
}
#endif

/*********************************************************************************************************/
// Name		: Draw
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void  cCalcDstWin::Draw(BOOL bRedraw)
{
#if 0	// Temp
	CHAR    vTextC[128];
	UNICODE vTextW[128];
	UNICODE vTextX[128];
	PegBrush xBrushX(PegResourceManager::GetColor(CID_BLACK),PegResourceManager::GetColor(mColorId[PCI_NORMAL]), 0);
	PegPoint xPointX;
	int   nFontID;
	int   nBRG;
	int   nLineHeight;
	int   nBtmOffset;
	cChartMainWin *pChartMainWin = (cChartMainWin *)m_pParent;

	RunCheckLangCode();
	RunCheckNightMode();

#if defined(__N430_MODEL__)
	if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
#else
	if (SysGetDeviceType() == DEVICE_TYPE_05_0)
#endif
	{
		nFontID = FID_MyriadPro10bEng;
		if (this->mReal.Top <= m_pParent->mReal.Top)
		{
			nLineHeight = 14;
			nBtmOffset  =  0;
		}
		else
		{
			nLineHeight = 20;
			nBtmOffset  =  2;
		}
	}
	else
	{
		nFontID = FID_MyriadPro12bEng;
		nLineHeight = 20;
		nBtmOffset  =  2;
	}

	pChartMainWin->CalcRunDstBrgLegDST();

	BeginDraw(Invalid);

	cMyWin::Draw(Invalid);

	pChartMainWin->GetRunRouteName((UCHAR *)vTextC);
	CopyCharStrToUniStr(vTextW,vTextC);
	xPointX.Set(this->mClient.Left + 20,mClient.Bottom - nBtmOffset - nLineHeight * 3 - TextHeight(nFontID));
	DrawText(xPointX,vTextW,xBrushX,nFontID);

	nBRG = pChartMainWin->GetCalcRunDstBRG();
	sprintf(vTextC," %03d.%d %s",nBRG / 10,nBRG % 10,GetCompassUnitChrStr());
	CopyCharStrToUniStr(vTextX,vTextC);
	UniStrCpy(vTextW,(UNICODE *)G_pCursorBRGMsgX[m_nLangCode]);
	UniStrCat(vTextW,vTextX);
	xPointX.Set(this->mClient.Left + 1,mClient.Bottom - nBtmOffset - nLineHeight * 2 - TextHeight(nFontID));
	DrawText(xPointX,vTextW,xBrushX,nFontID);

	sprintf(vTextC,"%7.2f %s",pChartMainWin->GetCalcRunDstLEG(),GetDistUnitChrStr());
	CopyCharStrToUniStr(vTextX,vTextC);
	UniStrCpy(vTextW,(UNICODE *)G_pCursorLEGMsgX[m_nLangCode]);
	UniStrCat(vTextW,vTextX);
	xPointX.Set(this->mClient.Left + 1,this->mClient.Bottom - nBtmOffset - nLineHeight * 1 - TextHeight(nFontID));
	DrawText(xPointX,vTextW,xBrushX,nFontID);

	sprintf(vTextC,"%7.2f %s",pChartMainWin->GetCalcRunDstDST(),GetDistUnitChrStr());
	CopyCharStrToUniStr(vTextX,vTextC);
	UniStrCpy(vTextW,(UNICODE *)G_pCursorDSTMsgX[m_nLangCode]);
	UniStrCat(vTextW,vTextX);
	xPointX.Set(this->mClient.Left + 1,this->mClient.Bottom - nBtmOffset - nLineHeight * 0 - TextHeight(nFontID));
	DrawText(xPointX,vTextW,xBrushX,nFontID);

	EndDraw();
#endif	
}

#if 0	// Temp
int cCalcDstWin::RunCheckLangCode(void)
{
	if (!cMyWin::RunCheckLangCode())
		return(0);

	return(1);
}
int cCalcDstWin::RunCheckNightMode(void)
{
	if (!cMyWin::RunCheckNightMode())
		return(0);

	this->SetColor(PCI_NORMAL  ,GetBackGroundColorID(TRUE));

	return(1);
}
#endif

