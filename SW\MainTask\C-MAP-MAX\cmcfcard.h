/*##################################################################
  FILE    : CMCFCARD.H

  USE     :	Cartographic File Technologies Library.
  PROJECT : C-Map Library.

  AUTHOR  : GE[050120].
  UPDATED : 
  ##################################################################

*/

#ifndef	CMC_F_CARD
	#define CMC_F_CARD

#include "cmaptype.h"

/*****************************************************************************
  Constants definition section.
 *****************************************************************************/

/* Identify Drive Information */
#define CYL_NUMBER			1
#define HEAD_NUMBER			3
#define SECTOR_NUMBER_MSB	7
#define SECTOR_NUMBER_LSB	8
#define SERIAL_N_START		10
#define SERIAL_N_END		19
#define F_REV_N_START		23
#define F_REV_N_END			26
#define MODEL_MUN_START		27
#define MODEL_MUN_END		46
#define CAPABILITIES		49

/* String Identify Drive Size + 1 byte for string terminator */
#define MODEL_SIZE			( ( ( MODEL_MUN_END - MODEL_MUN_START + 1 ) * 2 ) + 1 )
#define F_REV_SIZE			( ( ( F_REV_N_END - F_REV_N_START + 1 ) * 2 ) + 1 )
#define SERIAL_SIZE			( ( ( SERIAL_N_END - SERIAL_N_START + 1 ) * 2 ) + 1 )

typedef struct 
{
	Long SectorsNumber;				
	Word CylindersNumber;			/* Not Used for SD card device */
	Word HeadsNumber;				/* Not Used for SD card device */
	Word BytesPerSector;		
	Byte LBA_Capable;				/* Not Used for SD card device */
	Byte SerialNum [SERIAL_SIZE];
	Byte CtrlRevNum[F_REV_SIZE];
	Byte ModelNum  [MODEL_SIZE];
} FDEVInfoType;


typedef enum
{
	FDEV_NOT_PRESENT	= 0,		/* Device not present		*/
	FDEV_SD_PRESENT		= 1,		/* SD Card Device present	*/
	FDEV_MMC_PRESENT	= 2,		/* MMC Card Device present	*/
	FDEV_CF_PRESENT		= 3,		/* CF Card Device present	*/
	FDEV_UNKNOWN		= 0xFF		/* Unknown Device			*/
} FDEV_TYPE;


extern	SLong		F_Open			( char *filename, char *mode );
extern	SLong		F_Get			( SLong Handle, Long Address, Byte *Buffer, SLong Size );
extern	SLong		F_Seek			( SLong Handle, Long Address );
extern	SLong		F_Put			( SLong Handle, Byte *Buffer, SLong Size );
extern	SLong		F_Close			( SLong Handle );
extern	SLong		F_Delete		( char *filename );
extern	SLong		F_FileAttrib	( SLong Handle, SLong *FileSize );
extern	SLong		F_FindFirst		( char *FileName, char *FoundFileName );
extern	SLong		F_FindNext		( SLong FindHandle, char *FoundFileName);
extern	SLong		F_FindClose		( SLong FindHandle );
extern	FDEV_TYPE	F_Detect		( char *DriveLetter, Bool *Changed );
extern	SLong		F_DeviceInfo	( char *DriveLetter, FDEVInfoType *FDevInfo );

#endif	/* #ifndef	CMC_F_CARD */
