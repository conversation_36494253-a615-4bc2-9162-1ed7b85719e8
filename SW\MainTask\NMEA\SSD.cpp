#include <stdio.h>
#include "SSD.hpp"
#include "Uart.hpp"

extern cUART *G_pUart3;

CSsd::CSsd() : CSentence()
{
	strcpy((char *)m_szCallSign, "@@@@@@@");
	strcpy((char *)m_szName, "@@@@@@@@@@@@@@@@@@@@");
	m_nPosA         = 0;
	m_nPosB         = 0;
	m_nPosC         = 0;
	m_nPosD         = 0;
	m_nDTE          = 0;
	m_szSourceID[0] = '\0';
}
    
CSsd::CSsd(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CSsd::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_SSD;
}

/******************************************************************************
 * 
 * SSD - Ship Static Data
 *
 * $--SSD,c--c,c--c,xxx,xxx,xx,xx,c,aa*hh<CR><LF>
 *         |    |    |   |  |  |  | | 
 *         1    2    3   4  5  6  7 8
 *
 * 1. Ship's Call Sign, 1 to 7 characters
 * 2. Ship's Name, 1 to 20 characters
 * 3. Pos. ref. point distance, "A," from bow, 0 to 511 meters
 * 4. Pos. ref., "B," from stern, 0 to 511 meters
 * 5. Pos. ref., "C," from port beam, 0 to 63 meters
 * 6. Pos. ref., "D," from starboard beam, 0 to 63 meters
 * 7. DTE indicator flag
 * 8. Source identifier
 *
 * Add talkerId check : HSI 2012.04.26
 *
 ******************************************************************************/
void CSsd::Parse()
{
	char szTemp[128];
	char *pSzStr = NULL;
	int nStrLen = 0;

	GetTalkerID(m_szTalkID);

	m_szTalkID[9] = '\0';

	// Parse Call Sign
	GetFieldString(1, szTemp);
	pSzStr = ConvertNormalString(szTemp);
	nStrLen = strlen(pSzStr);

	if(strcmp(pSzStr,"@@@@@@@") == 0)
	{
		sprintf((char *)m_szCallSign,"@@@@@@@");
	}
	else
	{
		if(nStrLen > 7)
		{
			strncpy((char *)m_szCallSign, pSzStr,sizeof(char)*7);
			m_szCallSign[7] = '\0';
		}
		else
		{
			strncpy((char *)m_szCallSign, pSzStr,sizeof(char)*nStrLen);
			m_szCallSign[nStrLen] = '\0';
		}			
	}
	//G_pUart3->OutputDbgMsg("[CSsd::Parse]Src Call Sign=%s, Result Call Sign=%s\r\n",pSzStr,m_szCallSign);
	
	GetFieldString(2, szTemp); 
	pSzStr = ConvertNormalString(szTemp);
	nStrLen = strlen(pSzStr);
	if(strcmp(pSzStr,"@@@@@@@@@@@@@@@@@@@@") == 0)
	{
		sprintf((char *)m_szName,"@@@@@@@@@@@@@@@@@@@@");
	}
	else
	{
		if(nStrLen > 20)
		{
			strncpy((char *)m_szName, pSzStr,sizeof(char)*20);
			m_szName[20] = '\0';
		}
		else
		{
			strncpy((char *)m_szName, pSzStr,sizeof(char)*nStrLen);
			m_szName[nStrLen] = '\0';
		}			
	}	
	//G_pUart3->OutputDbgMsg("[CSsd::Parse]Src Ship Name=[[%s]], Result Call Sign=[[%s]]\r\n",pSzStr,m_szName);

	m_nPosA = GetFieldInteger(3);
	m_nPosB = GetFieldInteger(4);
	m_nPosC = GetFieldInteger(5);
	m_nPosD = GetFieldInteger(6);
	m_nDTE  = GetFieldInteger(7);
	GetFieldString(8, m_szSourceID);
	m_szSourceID[4] ='\0';
}

int CSsd::MakeSentence(BYTE *pszSentence)
{
	int  nChecksum;
	BYTE szTemp[MAX_NMEA_LEN];
	BYTE szName[MAX_NMEA_LEN];
	BYTE szCallSign[MAX_NMEA_LEN];

	//G_pUart3->OutputDbgMsg("[Before CSsd::MakeSentence]Name=%s, CallSign=%s\r\n",m_szName,m_szCallSign);
	strcpy((char *)szName,     ConvertNMEAString((char *)m_szName));
	strcpy((char *)szCallSign, ConvertNMEAString((char *)m_szCallSign));
	//G_pUart3->OutputDbgMsg("[After CSsd::MakeSentence]Name=%s, CallSign=%s\r\n",szName,szCallSign);

	sprintf((char *)szTemp, "$AISSD,%s,%s,%03d,%03d,%02d,%02d,%d,%s*",
		szCallSign, szName, m_nPosA, m_nPosB, m_nPosC, m_nPosD, m_nDTE, m_szSourceID);

	nChecksum = CSentence::ComputeChecksum((char *)szTemp);
	sprintf((char *)pszSentence, "%s%02X\x0D\x0A", szTemp, nChecksum);

	//G_pUart3->OutputDbgMsg("[Out CSsd::MakeSentence]%s\r\n",pszSentence);	
	return strlen((char *)pszSentence);
}

void CSsd::GetPlainText(char *pszPlainText)
{
	char szTemp[128];

	pszPlainText[0] = '\0';

	sprintf(szTemp, "Ship's Call Sign                        : %s\n", m_szCallSign);
	strcat(pszPlainText, szTemp);

	sprintf(szTemp, "Ship's Name                             : %s\n", m_szName);
	strcat(pszPlainText, szTemp);

	if( m_nPosA != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Pos. ref. point distance, \"A,\" from bow : %d\n", m_nPosA);
		strcat(pszPlainText, szTemp);
	}

	if( m_nPosB != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Pos. ref., \"B,\" from stern              : %d\n", m_nPosB);
		strcat(pszPlainText, szTemp);
	}

	if( m_nPosC != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Pos. ref., \"C,\" from port beam          : %d\n", m_nPosC);
		strcat(pszPlainText, szTemp);
	}

	if( m_nPosD != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Pos. ref., \"D,\" from starboard beam     : %d\n", m_nPosD);
		strcat(pszPlainText, szTemp);
	}

	if( m_nDTE != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "DTE indicator flag                      : %d\n", m_nDTE);
		strcat(pszPlainText, szTemp);
	}

	sprintf(szTemp, "Source identifier                      : %s\n", m_szSourceID);
	strcat(pszPlainText, szTemp);
}

