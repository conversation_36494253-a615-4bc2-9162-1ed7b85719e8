/*...........................................................................*/
/*.                  File Name : SYSINTR.CPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.31                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include <stdlib.h>
#include <string.h>

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysSDCD.h"
#include "SysIntr.h"

//================================================================
#if defined(__POLLUX__)
static void (*G_pIntHandlerTableX[])(void) = { // POLLUX           
            (void (*)(void))NULL,              // IRQ_PHY_PDISPLAY 
            (void (*)(void))NULL,              // IRQ_PHY_SDISPLAY 
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // IRQ_PHY_DMA      
            (void (*)(void))IsrHandlerTimer0,  // IRQ_PHY_SYSTIMER0
            (void (*)(void))NULL,              // IRQ_PHY_SYSCTRL  
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))IsrHandlerUART0,   // IRQ_PHY_UART0    
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER1
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI0  
            (void (*)(void))NULL,              // IRQ_PHY_GPIO     
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC    
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER2
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // IRQ_PHY_UDC      
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER3
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // IRQ_PHY_AUDIOIF  
            (void (*)(void))NULL,              // IRQ_PHY_ADC      
            (void (*)(void))NULL,              // IRQ_PHY_MCUSTATIC
            (void (*)(void))NULL,              // IRQ_PHY_GRP3D    
            (void (*)(void))NULL,              // IRQ_PHY_UHC      
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // IRQ_PHY_RTC      
            (void (*)(void))NULL,              // IRQ_PHY_I2C0     
            (void (*)(void))NULL,              // IRQ_PHY_I2C1     
            (void (*)(void))IsrHandlerUART1,   // IRQ_PHY_UART1    
            (void (*)(void))IsrHandlerUART2,   // IRQ_PHY_UART2    
            (void (*)(void))IsrHandlerUART3,   // IRQ_PHY_UART3    
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // -----------------
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI1  
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI2  
            (void (*)(void))NULL,              // IRQ_PHY_CSC      
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC1   
            (void (*)(void))NULL};             // IRQ_PHY_SYSTIMER4
#else                           // SPICA
static void (*G_pIntHandlerTableX[])(void) = { // SPICA
            (void (*)(void))NULL,              // IRQ_PHY_PDISPLAY   
            (void (*)(void))NULL,              // IRQ_PHY_SDISPLAY   
            (void (*)(void))NULL,              // IRQ_PHY_VIP        
            (void (*)(void))NULL,              // IRQ_PHY_DMA        
            (void (*)(void))IsrHandlerTimer0,  // IRQ_PHY_SYSTIMER0  
            (void (*)(void))NULL,              // IRQ_PHY_SYSCTRL    
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // IRQ_PHY_MPEGTSI    
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))IsrHandlerUART0,   // IRQ_PHY_UART0      
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER1  
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI     
            (void (*)(void))NULL,              // IRQ_PHY_GPIO       
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC0     
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER2  
            (void (*)(void))NULL,              // IRQ_PHY_H264       
            (void (*)(void))NULL,              // IRQ_PHY_MPEG       
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // IRQ_PHY_VLC        
            (void (*)(void))NULL,              // IRQ_PHY_UDC        
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER3  
            (void (*)(void))NULL,              // IRQ_PHY_DEINTERLACE
            (void (*)(void))NULL,              // IRQ_PHY_PPM        
            (void (*)(void))NULL,              // IRQ_PHY_AUDIOIF    
            (void (*)(void))NULL,              // IRQ_PHY_ADC        
            (void (*)(void))NULL,              // IRQ_PHY_MCUSTATIC  
            (void (*)(void))NULL,              // IRQ_PHY_GRP3D      
            (void (*)(void))NULL,              // IRQ_PHY_UHC        
            (void (*)(void))NULL,              // IRQ_PHY_ROTATOR    
            (void (*)(void))NULL,              // IRQ_PHY_SCALER     
            (void (*)(void))NULL,              // IRQ_PHY_RTC        
            (void (*)(void))NULL,              // IRQ_PHY_I2C0       
            (void (*)(void))NULL,              // IRQ_PHY_I2C1       
            (void (*)(void))IsrHandlerUART1,   // IRQ_PHY_UART1      
            (void (*)(void))IsrHandlerUART2,   // IRQ_PHY_UART2      
            (void (*)(void))IsrHandlerUART3,   // IRQ_PHY_UART3      
            (void (*)(void))NULL,              // IRQ_PHY_UART4      
            (void (*)(void))NULL,              // IRQ_PHY_UART5      
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI1    
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI2    
            (void (*)(void))NULL,              // IRQ_PHY_CSC        
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC1     
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER4  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // IRQ_PHY_I2C2       
            (void (*)(void))NULL,              // IRQ_PHY_I2S        
            (void (*)(void))NULL,              // IRQ_PHY_MPEGTSP_TSI
            (void (*)(void))NULL,              // IRQ_PHY_MPEGTSP_TSP
            (void (*)(void))NULL,              // IRQ_PHY_CDROM      
            (void (*)(void))NULL,              // IRQ_PHY_ALIVE      
            (void (*)(void))NULL,              // IRQ_PHY_EHCI       
            (void (*)(void))NULL,              // IRQ_PHY_OHCI       
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL};             // -----------------  
#endif
//================================================================
static xSYS_INTR *G_pSysIntrCtrl = (xSYS_INTR *)INTC_PHSY_BASE_ADDR;
static xSYS_UART *G_pSysUART0    = (xSYS_UART *)UART0_PHSY_BASE_ADDR;
static xSYS_UART *G_pSysUART1    = (xSYS_UART *)UART1_PHSY_BASE_ADDR;
static xSYS_UART *G_pSysUART2    = (xSYS_UART *)UART2_PHSY_BASE_ADDR;
static xSYS_UART *G_pSysUART3    = (xSYS_UART *)UART3_PHSY_BASE_ADDR;
//================================================================

void  IsrHandlerIRQ(void)
{
      DWORD dIntStatusX;
      DWORD dIntStatusY;
      int   i,nIntNo;
      DWORD dIntMask;

      dIntStatusX = G_pSysIntrCtrl->dINTPEND[0];
      dIntStatusY = G_pSysIntrCtrl->dINTPEND[1];
      dIntMask = 1;
      for (i = 0;i < 32;i++)
          {
           if (dIntStatusX & dIntMask)
              {
               if (G_pIntHandlerTableX[i] != NULL)
                   G_pIntHandlerTableX[i]();
               G_pSysIntrCtrl->dINTPEND[0] = dIntMask;
              }
           if (dIntStatusY & dIntMask)
              {
               nIntNo = i + 32;
               if (G_pIntHandlerTableX[nIntNo] != NULL)
                   G_pIntHandlerTableX[nIntNo]();
               G_pSysIntrCtrl->dINTPEND[1] = dIntMask;
              }
           dIntMask <<= 1;
          }
}
void  IsrHandlerFIQ(void)
{
}
void  IsrHandlerSWI(void)
{
      while (1);
}
void  IsrHandlerABORT(void)
{
      while (1);
}
void  IsrHandlerUNDEF(void)
{
      while (1);
}
void  IsrHandlerTimer0(void)
{
      static xSYS_TIMER *pSysTimer = (xSYS_TIMER *)TIMER0_PHSY_BASE_ADDR;

      SysIncSystemTimer();
      SysCheckSdCardChangStatus();
      pSysTimer->dTMRCONTROL |= (1 << 5);   // Pending Clear
}
void  IsrHandlerUART0(void)
{
      IsrHandlerUARTx(G_pSysUART0);
}
void  IsrHandlerUART1(void)
{
      IsrHandlerUARTx(G_pSysUART1);
}
void  IsrHandlerUART2(void)
{
      IsrHandlerUARTx(G_pSysUART2);
}
void  IsrHandlerUART3(void)
{
      IsrHandlerUARTx(G_pSysUART3);
}
void  IsrHandlerUARTx(xSYS_UART *pSysUART)
{
     HWORD wIntStatus;
     HWORD wTempX;
     int   nCount;
     UCHAR bData;

     wIntStatus = pSysUART->wINTCON;
     if (wIntStatus & (1 << 3))                      // Modem
        {
         wTempX = pSysUART->wINTCON & 0xfff0;
         pSysUART->wINTCON = wTempX | (1 << 3);      // Md Interrupt Flag Clear
        }
     if (wIntStatus & (1 << 2))                      // Error
        {
         wTempX = pSysUART->wINTCON & 0xfff0;
         pSysUART->wINTCON = wTempX | (1 << 2);      // Er Interrupt Flag Clear
         wTempX = pSysUART->wESTATUS;                // Clear Error Status
         wTempX = pSysUART->wFSTATUS;                // Clear FIFO  Status
        }
     if (wIntStatus & (1 << 1))                      // Rx
        {
         nCount = pSysUART->wFSTATUS & 0x000f;
         while (nCount > 0)
               {
                bData = *((volatile UCHAR *)&pSysUART->wRHB);
//              pUartX->vRxBuff[pUartX->nRxHead] = pUartT->DR;
//              ++pUartX->nRxHead;
//              if (pUartX->nRxHead >= SYS_UART_RxBUFF_SIZE)
//                  pUartX->nRxHead -= SYS_UART_RxBUFF_SIZE;
                --nCount;
               }
         wTempX = pSysUART->wINTCON & 0xfff0;
         pSysUART->wINTCON = wTempX | (1 << 1);      // Rx Interrupt Flag Clear
        }
     if (wIntStatus & (1 << 0))                      // Tx
        {
         nCount = 16 - ((pSysUART->wFSTATUS >> 4) & 0x000f);
/*
         while (nCount > 0 && (pUartX->nTxTail != pUartX->nTxHead))
               {
                pUartT->DR = pUartX->vTxBuff[pUartX->nTxTail];
                ++pUartX->nTxTail;
                if (pUartX->nTxTail >= SYS_UART_TxBUFF_SIZE)
                    pUartX->nTxTail -= SYS_UART_TxBUFF_SIZE;
                --nCount;
               }
         if (pUartX->nTxTail == pUartX->nTxHead)
            {
             pUartX->nSending = 0;
             UART_ITConfig(pUartT,UART_IT_Transmit,DISABLE);   // Disable Tx Interrupt
            }
*/
         wTempX = pSysUART->wINTCON & 0xfff0;
         pSysUART->wINTCON = wTempX | (1 << 0);      // Tx Interrupt Flag Clear
        }
}

void  SysSetAllInterrupt(void)
{
      SysSetOneInterrupt(IRQ_PHY_SYSTIMER0,INT_MODE_IRQ);
      SysSetOneInterrupt(IRQ_PHY_UART0    ,INT_MODE_IRQ);
      SysSetOneInterrupt(IRQ_PHY_UART1    ,INT_MODE_IRQ);
      SysSetOneInterrupt(IRQ_PHY_UART2    ,INT_MODE_IRQ);
      SysSetOneInterrupt(IRQ_PHY_UART3    ,INT_MODE_IRQ);
}
void  SysSetOneInterrupt(int nIntNo,int nIRQ_FIQ)
{
      volatile DWORD *pIntMode;
      volatile DWORD *pIntMask;
      int   nShift;
      DWORD dMaskX;

      if (nIntNo >= 32)
         {
          pIntMode = &(G_pSysIntrCtrl->dINTMODE[1]);
          pIntMask = &(G_pSysIntrCtrl->dINTMASK[1]);
          nShift   = nIntNo - 32;
         }
      else
         {
          pIntMode = &(G_pSysIntrCtrl->dINTMODE[0]);
          pIntMask = &(G_pSysIntrCtrl->dINTMASK[0]);
          nShift   = nIntNo;
         }
      dMaskX = 1 << nShift;
      if (nIRQ_FIQ == INT_MODE_IRQ)
          *pIntMode &= ~dMaskX;
      else
          *pIntMode |=  dMaskX;
      *pIntMask &= ~dMaskX;
}
void  SysSetAllInterruptDisable(void)
{
      xSYS_INTR *pSysIntr = (xSYS_INTR *)INTC_PHSY_BASE_ADDR;

      pSysIntr->dINTMASK[0] = 0xffffffff;
      pSysIntr->dINTMASK[1] = 0xffffffff;
}

