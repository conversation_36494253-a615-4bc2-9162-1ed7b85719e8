/*##################################################################
  FILE    : FS_EDATA.H

  USE     : File System Extended User Data Files Header.
  PROJECT : C-Map's File System.

  AUTHOR  : SiS[040908].
  UPDATED :
  ##################################################################


*/

#ifndef __FS_EDATA__
	#define __FS_EDATA__


/************************************************************************
  #Include section.
 ************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include "CMapType.h"



/************************************************************************
  Constants definition section.
 ************************************************************************/

/*
 * Number of chars in Line/Area Name.
 */
#define LA_MAX_NAME_LEN					32

/*
 * Number of data Word in m_ssPattern field of LA_FILL_STYLE type.
 */
#define LA_PATTERN_SIZE					16

/*
 * Resolution for m_slAreaRange	field in LA_HEADER data structure type.
 */
#define	FS_AREA_RANGE_RES				1000




/************************************************************************
  Types & Data Structure definition definition section.
 ************************************************************************/

/*
 * Line/Area Draw style.
 * This structure is equivalent to the DrawStyleType of CMG.
 */
typedef struct
{
	SWord	m_ssForeColor;							// Drawing Color (Foreground).
	SWord	m_ssBackColor;							// Drawing Color (Background). 
	SWord	m_ssForeOper;							// Drawing Operation (Foreground).
	SWord	m_ssBackOper;							// Drawing Operation (Background).
	SWord	m_ssLineWidth;							// Drawing line width.                     
	SWord	m_ssPattern ;							// Drawing Pattern.

} LA_DRAW_STYLE, *lpLA_DRAW_STYLE;


/*
 * Area Fill style.
 * This structure is equivalent to the FillStyleType of CMG.	
 */
typedef struct
{
	SWord	m_ssForeColor;							// Filling Color (Foreground). 
	SWord	m_ssBackColor;							// Filling Color (Background).       
	SWord	m_ssForeOper;							// Filling Operation (Foreground).
	SWord	m_ssBackOper;							// Filling Operation (Background).  
	SWord	m_ssPattern[LA_PATTERN_SIZE];			// Fill Pattern (16x16)                   

} LA_FILL_STYLE, *lpLA_FILL_STYLE;


/*
 * Line/Area Common fields Header. 
 * This structure contains the common fields between 
 * Lines and Areas Header.
 *
 * NOTE: The m_slAlarmRange field represents a distance and it
 * should be considered as a float multiplied by FS_AREA_RANGE_RES
 * (1000) and then casted to SLong. The User must handle the float
 * to SLong and SLong to float conversions when saving and loading 
 * data respectively.
 */
typedef struct  
{
	SByte			m_cName[LA_MAX_NAME_LEN];		// Line/Area name.
	SWord			m_ssNumPoints;					// Number of Points.
	Bool			m_bVisible;						// Visible flag.
	SLong			m_slAlarmRange;					// Alarm Range (Float * FS_AREA_RANGE_RES).
	LA_DRAW_STYLE	m_sDStyle;

} LA_HEADER, *lpLA_HEADER;


/*
 * Lines Header Data Structure type.
 */
typedef struct  
{
	LA_HEADER		m_sHeader;						// Common Fields.

} FS_LINE_HEADER, *lpFS_LINE_HEADER;


/*
 * Areas Header Data Structure type.	
 */
typedef struct  
{
	LA_HEADER		m_sHeader;						// Common Fields.
	LA_FILL_STYLE	m_sFStyle;						// Fill Style.

} FS_AREA_HEADER, *lpFS_AREA_HEADER;


/*
 * Line Data record type definition.
 */
typedef struct  
{
	SLong			m_slLat;						// Point Latitude (Mercator Meters).
	SLong			m_slLon;						// Point Longitude (Mercator Meters).

} FS_LINE_DATA, *lpFS_LINE_DATA;


/*
 * Area Point record type definition.
 */
typedef struct  
{
	SLong			m_slLat;						// Point Latitude (Mercator Meters).
	SLong			m_slLon;						// Point Longitude (Mercator Meters).

} FS_AREA_DATA, *lpFS_AREA_DATA;




/************************************************************************
  Exported Routines prototypes.
 ************************************************************************/


#ifdef __cplusplus
extern "C" 
{
#endif /* __cplusplus */

/*
 * Initialize the passed FS_LINE_HEADER structure with default values.
 */
PRE_EXPORT_H extern void IN_EXPORT_H FS_InitLineHeader ( lpFS_LINE_HEADER lpHeader );

/*
 * Initialize the passed FS_AREA_HEADER structure with default values.
 */
PRE_EXPORT_H extern void IN_EXPORT_H FS_InitAreaHeader ( lpFS_AREA_HEADER lpHeader );

/*
 * Initialize the passed FS_LINE_DATA structure with default values.
 */
PRE_EXPORT_H extern void IN_EXPORT_H FS_InitLineData ( lpFS_LINE_DATA lpData );

/*
 * Initialize the passed FS_AREA_DATA structure with default values.
 */
PRE_EXPORT_H extern void IN_EXPORT_H FS_InitAreaData ( lpFS_AREA_DATA lpData );

/*
 * Save one Line Header on the passed file.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveLineHeader ( SWord ssFp, lpFS_LINE_HEADER lpHeader );

/*
 * Save one Area Header on the passed file.		
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveAreaHeader ( SWord ssFp, lpFS_AREA_HEADER lpHeader );

/*
 * Save one Line Data Record on the passed file.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveLineData ( SWord ssFp, lpFS_LINE_DATA lpData );

/*
 * Save one Line Area Record on the passed file. 
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveAreaData ( SWord ssFp, lpFS_AREA_DATA lpData );

/*
 * Read a Line Header data structure form file.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_LoadLineHeader ( SWord ssFp, lpFS_LINE_HEADER lpHeader );

/*
 * Read an Area Header data structure form file.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_LoadAreaHeader ( SWord ssFp, lpFS_AREA_HEADER lpHeader );

/*
 * Read a Line Point data structure form file.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_LoadLineData ( SWord ssFp, lpFS_LINE_DATA lpData );

/*
 * Read an Area Point data structure form file.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_LoadAreaData ( SWord ssFp, lpFS_AREA_DATA lpData );



#ifdef __cplusplus
}
#endif /* __cplusplus */




/************************************************************************
  END of Code.
 ************************************************************************/

#endif /* #ifndef __FS_EDATA__ */
