/*...........................................................................*/
/*.                  File Name : DOWNPRG.HPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2004.10.20                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "screen.hpp"
#include "keybd.hpp"
#include "uart.hpp"
#include "time.hpp"
#include "flash.hpp"
#include "DataBack.hpp"
#include "wnd.hpp"
#include "osfile.h"

#ifndef  __DOWNPRG_HPP
#define  __DOWNPRG_HPP

//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------

class cDPWNPRG
{
   private:
      cSCREEN *m_pScreen;
      cKEYBD  *m_pKeyBD;
      cUART   *m_pUart;
      cFLASH  *m_pFlash;
   public:

   public:
      cDPWNPRG(cSCREEN *pScreen,cKEYBD *pKeyBD,cUART *pUart);
      virtual ~cDPWNPRG(void);
   public:
      int   DownLoadProgram(void);
      int   SendOneRecord(const BYTE *pCmd,DWORD dFrameNo,WORD wSendSize,const BYTE *pSendData);
      void  SetEndianData(BYTE *pTarget,void *pSource,int nSize);
      void  GetEndianData(void *pTarget,BYTE *pSource,int nSize);

private:
	void ClearScreen(CWnd *pWnd, char *pszFileName=0);
	void EraseAndFuse(BYTE *pDownData, DWORD dDownTotal, DWORD dDownAddr, CWnd *pTempWnd);
	int  ReadLine(FHANDLE *pFh, BYTE *pData, int nBufSize);
	int  MakeUpper(char *pszBuffer);
	int  ParseUpdateDataFile(char *pszBuffer, char *pszFilePath, DWORD *pdwAddr, DWORD *pdwSize, DWORD *pdwCheckSum);
};

#endif

