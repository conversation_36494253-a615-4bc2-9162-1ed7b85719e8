/*...........................................................................*/
/*.                  File Name : DATUM.HPP                                  .*/
/*.                                                                         .*/
/*.                       Date : 2005.10.01                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
//#include "DataType.hpp"

#ifndef  __DATUM_HPP
#define  __DATUM_HPP

//=============================================================================
typedef  struct {
         int    nDatumNo;
         char  *pDatumName;
         REAL   rA;
         REAL   rB;
         REAL   rF;
         REAL   rX;
         REAL   rY;
         REAL   rZ;
         REAL   rE;
         char  *pDatumAbbr;
        }xDATUM;
//-----------------------------------------------------------------------------
#define  WGS84_DATUM_NO          0
#define  WGS72_DATUM_NO          1
#define  PZ_90_DATUM_NO          2
#define  CK_42_DATUM_NO          3
#define  CK_95_DATUM_NO          4
#define  KOREA_DATUM_NO         63
#define  SK_42_DATUM_NO         89
//=============================================================================

class cDATUM
{
   private:
      static int   m_nUsingDatumNo;
      static UNICODE *m_pUniDatumName[CHART_DATUM_NO_SIZE + 1];

   public:
      cDATUM(void);
      virtual ~cDATUM(void);
   public:
      static void  InitAllData(void);
      static int   GetDatumMaxNo(void);
      static void  SetUsingDatumNo(int nDatumNo);
      static int   GetUsingDatumNo(void);
      static CHAR *GetOneDatumName(int nDatumNo);
      static void  GetAllDatumName(CHAR **pDatumName);
      static UNICODE **GetAllDatumName(void);
      static CHAR *GetOneDatumAbbr(int nDatumNo);
      static void  WGS84ToLocalDatum(int nDatumNo,LREAL rW84Lat,LREAL rW84Lon,LREAL *pLocalLat,LREAL *pLocalLon);
      static void  LocalDatumToWGS84(int nDatumNo,LREAL rLocalLat,LREAL rLocalLon,LREAL *pW84Lat,LREAL *pW84Lon);
      static void  LocalDatumToLocal(int nFrDatumNo,int nToDatumNo,LREAL rFrLat,LREAL rFrLon,LREAL *pToLat,LREAL *pToLon);

      static void  ClearAllData(void);
      static int   SaveBackUpData(UCHAR *pBackData);
      static int   RestBackUpData(UCHAR *pBackData);
      static void  TestBackUpData(void);
};

#endif

