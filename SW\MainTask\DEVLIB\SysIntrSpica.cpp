/*...........................................................................*/
/*.                  File Name : SYSINTR.CPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.11                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/



#include "type.hpp"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysSDCD.h"
#include "SysGPIO.h"
#include "SysTimer.h"
#include "Nand64.h"

#include "time.hpp"
#include "uart.hpp"
#include "buzzer.hpp"
#include "screen.hpp"

#include "sysconst.h"
#include "syslib.h"
#include "comlib.h"
#include "keybd.hpp"

#include "Timer.hpp"

#include "SysIntrSpica.hpp"

#include <string.h>

//================================================================
#define  _RESTART_TEST_MEM_ADDR_           0x83a00000
//================================================================

//================================================================
static void (*G_pIntHandlerTableX[])(void) = { // SPICA
            (void (*)(void))NULL,              // IRQ_PHY_PDISPLAY   
            (void (*)(void))NULL,              // IRQ_PHY_SDISPLAY   
            (void (*)(void))NULL,              // IRQ_PHY_VIP        
            (void (*)(void))NULL,              // IRQ_PHY_DMA        
            (void (*)(void))IsrHandlerTimer0,  // IRQ_PHY_SYSTIMER0  
            (void (*)(void))NULL,              // IRQ_PHY_SYSCTRL    
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // IRQ_PHY_MPEGTSI    
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))IsrHandlerUART0,   // IRQ_PHY_UART0      
            (void (*)(void))IsrHandlerTimer1,  // IRQ_PHY_SYSTIMER1  
            (void (*)(void))IsrHandlerSSPSPI0, // IRQ_PHY_SSPSPI     
            (void (*)(void))IsrHandlerGPIO,    // IRQ_PHY_GPIO       
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC0     
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER2  
            (void (*)(void))NULL,              // IRQ_PHY_H264       
            (void (*)(void))NULL,              // IRQ_PHY_MPEG       
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // IRQ_PHY_VLC        
            (void (*)(void))NULL,              // IRQ_PHY_UDC        
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER3  
            (void (*)(void))NULL,              // IRQ_PHY_DEINTERLACE
            (void (*)(void))NULL,              // IRQ_PHY_PPM        
            (void (*)(void))NULL,              // IRQ_PHY_AUDIOIF    
            (void (*)(void))NULL,              // IRQ_PHY_ADC        
            (void (*)(void))NULL,              // IRQ_PHY_MCUSTATIC  
            (void (*)(void))NULL,              // IRQ_PHY_GRP3D      
            (void (*)(void))NULL,              // IRQ_PHY_UHC        
            (void (*)(void))NULL,              // IRQ_PHY_ROTATOR    
            (void (*)(void))NULL,              // IRQ_PHY_SCALER     
            (void (*)(void))NULL,              // IRQ_PHY_RTC        
            (void (*)(void))NULL,              // IRQ_PHY_I2C0       
            (void (*)(void))NULL,              // IRQ_PHY_I2C1       
            (void (*)(void))IsrHandlerUART1,   // IRQ_PHY_UART1      
            (void (*)(void))IsrHandlerUART2,   // IRQ_PHY_UART2      
            (void (*)(void))IsrHandlerUART3,   // IRQ_PHY_UART3      
            (void (*)(void))IsrHandlerUART4,   // IRQ_PHY_UART4      
            (void (*)(void))IsrHandlerUART5,   // IRQ_PHY_UART5      
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI1    
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI2    
            (void (*)(void))NULL,              // IRQ_PHY_CSC        
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC1     
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER4  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // IRQ_PHY_I2C2       
            (void (*)(void))NULL,              // IRQ_PHY_I2S        
            (void (*)(void))NULL,              // IRQ_PHY_MPEGTSP_TSI
            (void (*)(void))NULL,              // IRQ_PHY_MPEGTSP_TSP
            (void (*)(void))NULL,              // IRQ_PHY_CDROM      
            (void (*)(void))NULL,              // IRQ_PHY_ALIVE      
            (void (*)(void))NULL,              // IRQ_PHY_EHCI       
            (void (*)(void))NULL,              // IRQ_PHY_OHCI       
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL,              // -----------------  
            (void (*)(void))NULL};             // -----------------  
//================================================================
static xSYS_INTR *G_pSysIntrCtrl = (xSYS_INTR *)INTC_PHSY_BASE_ADDR;
//----------------------------------------------------------------
static DWORD     G_dWatchDogCounter = 0;
static int       G_nWatchDogRunMode = 0;
static int       G_nReStartRunMode  = 0;
//----------------------------------------------------------------
static DWORD     G_dDrawDogCounter  = 0;
//----------------------------------------------------------------
extern cTimer   *G_pSysTimer0;
//extern cUart    *G_pUartPort0;
//extern cUart    *G_pUartPort1;
//extern cUart    *G_pUartPort2;
//extern cUart    *G_pUartPort3;
//extern cKeyBD   *G_pKeyBD;

extern cKEYBD   *G_pKeyBD;
extern cTIME    *G_pTime;
extern cUART    *G_pUart0;
extern cUART    *G_pUart1;
extern cUART    *G_pUart2;
extern cUART    *G_pUart3;
extern cUART    *G_pUart4;
extern cUART    *G_pUart5;

extern cSCREEN  *G_pScreen;
extern cBUZZER  *G_pBuzzer;
//================================================================

//=============================================================================
#ifdef  __cplusplus
extern "C" {
#endif

void  IsrHandlerIRQ(void)
{
	DWORD dIntStatusX;
	DWORD dIntStatusY;
	int   i,nIntNo;
	DWORD dIntMask;

	dIntStatusX = G_pSysIntrCtrl->dINTPEND[0];
	dIntStatusY = G_pSysIntrCtrl->dINTPEND[1];

	dIntMask = 1;

	for (i = 0;i < 32;i++)
	{
		if (dIntStatusX & dIntMask)
		{
			if (G_pIntHandlerTableX[i] != NULL)
				G_pIntHandlerTableX[i]();
			G_pSysIntrCtrl->dINTPEND[0] = dIntMask;
		}

		if (dIntStatusY & dIntMask)
		{
			nIntNo = i + 32;
			if (G_pIntHandlerTableX[nIntNo] != NULL)
				G_pIntHandlerTableX[nIntNo]();
			G_pSysIntrCtrl->dINTPEND[1] = dIntMask;
		}
		dIntMask <<= 1;
	}
}
void  IsrHandlerFIQ(void)
{
}
void  IsrHandlerSWI(void)
{
      ReStartMainBooter();

      while (1);
}
void  IsrHandlerABORT(void)
{
      ReStartMainBooter();

      while (1);
}
void  IsrHandlerUNDEF(void)
{
      ReStartMainBooter();

      while (1);
}

void ClearWatchDogCounter(void)
{
      G_dWatchDogCounter = 0;
}

void  RunWatchDogCounter(void)
{
	static DWORD dStartTimer = 0;

	++G_dWatchDogCounter;
#if  defined(__NAVIONICS__)
	if (G_dWatchDogCounter >= CALC_SEC_TO_TICK(120))
#else
	if (G_dWatchDogCounter >= CALC_SEC_TO_TICK( 30))
#endif
	{

	#if defined(__N500_MODEL__)
		if (!(((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR)->dPAD & (KBD_GPIO_POWER)))
	#else
		if (!(((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR)->dPAD & (KBD_GPIO_POWER)))
	#endif
		{
			if (dStartTimer == 0)
			{
				dStartTimer = SysGetSystemTimer();
			}				

			if (SysGetDiffTimeMili(dStartTimer) >= 2000)
			{
				//                ReStartMainBooter();
				SysRunSystemPowerOff();
			}
		}
		else
		{
			dStartTimer = 0;
		}			
	}
}

void  SetWatchDogRunMode(int nMode)
{
	G_nWatchDogRunMode = nMode;
}

void  SetDrawDogCounter(DWORD dCounter)
{
      G_dDrawDogCounter = dCounter;
}
void  SetReStartRunMode(int nMode)
{
      G_nReStartRunMode = nMode;
}

void  ReStartMainBooter(void)
{
  #define  MAIN_BOOTER_RAM_START_ADDR            0x80100000
  #define  MAIN_BOOTER_RAM_OFFST_ADDR            0x00001000

  #define  MAIN_LOADER_RAM_START_ADDR            0x82000000
  #define  MAIN_LOADER_RAM_OFFST_ADDR            0x00000010

	  void	(*pMainBooter)(void);

///*
	  if (G_nReStartRunMode)
		 {
		  G_nReStartRunMode = 0;

		  SysCleanAllDCache926();
		  SysInvalidateDCache();
		  SysInvalidateICache();

			*(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x00) = 0x01234567;
			*(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x04) = 0x89abcdef;
			*(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x08) = 0xa5a5aaaa;
			*(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x0c) = 0x5a5a5555;

			G_pBuzzer->SetBuzzerMode(BUZZER_OFF);
			if (((xSYS_GPIO *)GPIOB_PHSY_BASE_ADDR)->dOUT & (1 << BZR_GPIO_BIT_NO))
			{
				((xSYS_GPIO *)GPIOB_PHSY_BASE_ADDR)->dOUT &= ~(1 << BZR_GPIO_BIT_NO);
			}		

			SysDisableIRQ();

			//		  G_pNandFlash->ReadNandFlashByCpuAddr((UCHAR *)(MAIN_LOADER_RAM_START_ADDR - MAIN_LOADER_RAM_OFFST_ADDR),LOAD_PRG_ROM_START_ADDR,512 * 1024);
			ReadNandBlockData((UCHAR *)MAIN_BOOTER_RAM_START_ADDR - MAIN_BOOTER_RAM_OFFST_ADDR, 0);

			SysCleanAllDCache926();
			SysInvalidateDCache();
			SysInvalidateICache();

			//		  pMainBooter = (void (*)(void))(MAIN_LOADER_RAM_START_ADDR);
			pMainBooter = (void (*)(void))(MAIN_BOOTER_RAM_START_ADDR);

			pMainBooter();
		 }
//*/

	  while (1);
}


#ifdef  __cplusplus
}
#endif
//=============================================================================
void  IsrHandlerTimer0(void)
{
	SysIncSystemTimer();
	SysCheckSdCardChangStatus();
	CheckPowerStatus();
	cTIME::RunIntHandler();
	G_pSysTimer0->RunTimerIsrHandler();
	cKEYBD::RunIntHandler();
	cBUZZER::RunIntHandler();
	RunWatchDogCounter();

	if (G_dDrawDogCounter)
	{
		--G_dDrawDogCounter;
		if (G_dDrawDogCounter == 0)
		{
			G_dDrawDogCounter = 0;
			ReStartMainBooter();
		}
	} 

#if defined(__USE_RTOS__)
	extern BOOLEAN  OSRunning;

	if (OSRunning)
		OSTimeTick();
#endif
}
void  IsrHandlerTimer1(void)
{
#if 0	// KPB
      G_pSysTimer1->RunTimerIsrHandler();

      AppendCaliSpeedVal(TIMER1_MATCH_COUNTER);
#endif	  
}
void  IsrHandlerUART0(void)
{
}
void  IsrHandlerUART1(void)
{
	G_pUart0->RunUartIsrHandler();
}
void  IsrHandlerUART2(void)
{
//    G_pUartPort2->RunUartIsrHandler();
}
void  IsrHandlerUART3(void)
{
	G_pUart3->RunUartIsrHandler();
}
void  IsrHandlerUART4(void)
{
#if 0	// KPB
	G_pUart4->RunUartIsrHandler();
#endif
}
void  IsrHandlerUART5(void)
{
#if 0	// KPB
	G_pUart5->RunUartIsrHandler();
#endif
}
void  IsrHandlerGPIO(void)
{
}
void  IsrHandlerSSPSPI0(void)
{
}
//=============================================================================
void  SysSetAllInterrupt(void)
{
	SysSetOneInterrupt(IRQ_PHY_SYSTIMER0,INT_MODE_IRQ);
	SysSetOneInterrupt(IRQ_PHY_UART1    ,INT_MODE_IRQ);
	SysSetOneInterrupt(IRQ_PHY_UART3    ,INT_MODE_IRQ);
}
void  SysSetOneInterrupt(int nIntNo,int nIRQ_FIQ)
{
	volatile DWORD *pIntMode;
	volatile DWORD *pIntMask;
	int   nShift;
	DWORD dMaskX;

	if (nIntNo >= 32)
	{
		pIntMode = &(G_pSysIntrCtrl->dINTMODE[1]);
		pIntMask = &(G_pSysIntrCtrl->dINTMASK[1]);
		nShift   = nIntNo - 32;
	}
	else
	{
		pIntMode = &(G_pSysIntrCtrl->dINTMODE[0]);
		pIntMask = &(G_pSysIntrCtrl->dINTMASK[0]);
		nShift   = nIntNo;
	}

	dMaskX = 1 << nShift;

	if (nIRQ_FIQ == INT_MODE_IRQ)
		*pIntMode &= ~dMaskX;
	else
		*pIntMode |=  dMaskX;

	*pIntMask &= ~dMaskX;
}
void  SysSetAllInterruptDisable(void)
{
	xSYS_INTR *pSysIntr = (xSYS_INTR *)INTC_PHSY_BASE_ADDR;

	pSysIntr->dINTMASK[0] = 0xffffffff;
	pSysIntr->dINTMASK[1] = 0xffffffff;
}
//=============================================================================

