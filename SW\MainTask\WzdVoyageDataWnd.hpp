#ifndef __WIZARD_VOYAGE_DATA_WND_HPP__
#define __WIZARD_VOYAGE_DATA_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "ComboCtrl.hpp"
#include "UniComboCtrl.hpp"

#define MAX_WZD_FAV_SHIP_TYPE_ITEM_CNT	5
#define MAX_WZD_SHIP_TYPE_ITEM_CNT  (MAX_WZD_FAV_SHIP_TYPE_ITEM_CNT + 256)
#define MAX_WZD_SHIP_TYPE_STR  	70

class CWzdVoyageDataWnd : public CWnd {
	protected:
		enum {
			FOCUS_DEST = 0,
			FOCUS_ETA_TIME,
			FOCUS_SHIP_CARGO_TYPE,
			FOCUS_SHIP_STATUS,
			FOCUS_DRAUGHT,
			FOCUS_PERSONS,
			FOCUS_LAST = FOCUS_PERSONS
		};

		CEditCtrl		*m_pDestEdit;
		CEditCtrl		*m_pETATimeEdit;
		CUniComboCtrl	*m_pShipCargoType;
		CEditCtrl		*m_pPersonsEdit;
		CEditCtrl		*m_pDraught;
		CUniComboCtrl	*m_pShipStatusCombo;

		HWORD		*m_pUniStrShipType[MAX_WZD_SHIP_TYPE_ITEM_CNT];

		int         m_nFocus;
		
		BOOL CheckETA(BYTE *pszETA);

	private:
		void InitShipTypeItem();
		void InitNavStatusItem();
		void ReloadShipTypeItem();
		void ReloadNavStatusItem();
		int ConvertShipTypeNumToCmbIdx(int nShipType);
		int ConvertShipTypeCmbIdxToNum(int nIdx);
	
	public:
		CWzdVoyageDataWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void OnCursorEvent(int nState);
		virtual void OnActivate();
		void SetEditMode(int nMode);
		void SetControls(int nMode);
		void DrawControls();
		void DrawFuncBtn();
		void DrawWnd(BOOL bRedraw = TRUE);
		void InitWzdVoyageData();
		void SaveVoyageData();
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		int  GetFocus()           { return m_nFocus; }
		void ComboCollapse() {
			m_pShipStatusCombo->Collapse();
			m_pShipCargoType->Collapse();
		}
};

#endif	// End of __WIZARD_VOYAGE_DATA_WND_HPP__

