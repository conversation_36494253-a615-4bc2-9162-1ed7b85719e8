#include "Sentence.hpp"

#ifndef __BBM_HPP__
#define __BBM_HPP__
/******************************************************************************
*
* BBM - Broadcast Binary Message
*
* $--BBM,x,x,x,x,x.x,s--s,x*hh<CR><LF>
*        | | | | |   |    |
*        1 2 3 4 5   6    7
*
* 1. Total number of sentence needed to transfer the message , 1 to 9
* 2. Sentence number ,1 to 9
* 3. Sequential message identifier , 0 to 9
* 4. AIS channel for broadcast of the radio message
* 5. ITU-R M.1371 message ID , 8 or 14
* 6. Encapsulated data
* 7. Number of fill-bits , 0 to 5
*
******************************************************************************/
class CBbm : public CSentence {
    protected:
		int  m_nTotalNo;         // 1 to 9
		int  m_nSentNo;          // 1 to 9
		int  m_nSeqId;           // 0 to 9
		int  m_nChannel;
			// 0 = no broadcast channel, 
			// 1 = Broadcast on AIS channel A
			// 2 = Broadcast on AIS channel B
			// 3 = Broadcast two copies of the message
			//     (one -> channel A, another channel B)
		int  m_nMsgID;			 // 8 or 14
		char m_szMessage[61];    // first setence up to 58, follwing sentence up to 60
		int  m_nFillBitsNo;      // 0 to 5
    public:
        CBbm();
        CBbm(char *pszSentence);

		void Parse();
		void SetSentence(char *pszSentence);
		int  GetFormat() { return m_nFormat; }
		void GetPlainText(char *pszPlainText);
		int  MakeSentence(BYTE *pszSentence);

		void SetTotalNo(int nTotalNo) { m_nTotalNo = nTotalNo; }
		void SetSentNo(int nSentNo)   { m_nSentNo  = nSentNo;  }
		void SetSeqID(int nSeqID)     { m_nSeqId   = nSeqID;   }
		void SetChannel(int nChannel) { m_nChannel = nChannel; }
		void SetMsgID(int nMsgID)     { m_nMsgID   = nMsgID;   }
		void SetMessage(BYTE *pszMsg) { strcpy((char *)m_szMessage, (char *)pszMsg); }
		void SetFillBitsNo(int nFill) { m_nFillBitsNo = nFill; }

		int  GetTotalNo()    { return m_nTotalNo;    }
		int  GetSentNo()     { return m_nSentNo;     }
		int  GetSeqID()      { return m_nSeqId;      }
		int  GetChannel()    { return m_nChannel;   }
		void GetMessage(char *pszMessage) { strcpy(pszMessage, m_szMessage); }
		int  GetMsgID()      { return m_nMsgID;      }
};
		
#endif
