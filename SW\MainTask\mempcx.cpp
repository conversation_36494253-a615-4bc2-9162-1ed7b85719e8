/*...........................................................................*/
/*.                  File Name : MEMPCX.CPP                                 .*/
/*.                                                                         .*/
/*.                       Date : 2004.10.16                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "DataType.h"
#include "sysconst.h"
#include "mempcx.hpp"
#include "SysLibSpica.h"

#include <string.h>

#include "Uart.hpp"
extern cUART *G_pUart3;
cMEMPCX::cMEMPCX(cSCREEN *pScreen)
{
     m_pScreen = pScreen;
	  m_pvScreen = new COLORT[800*480];
}
cMEMPCX::~cMEMPCX(void)
{
	delete [] m_pvScreen;
}

int cMEMPCX::GetPcxScreen(BYTE *pPcxData)
{
	return GetPcxScreen16Bit800(pPcxData);
}

int   cMEMPCX::GetPcxScreenMono(BYTE *pPcxData)
{
      PCX_FILE PcxFile;
      //BYTE Pal[256 * 3];
      BYTE TempX;
      BYTE TempY;
      BYTE TempZ;
      INT  Before,Counter;
      INT  X,Y;
      BYTE  vLine[2048];
      BYTE *pLine;
      int  nPcxSize;
	    int  nScrXSize;
	    int  nScrYSize;
	    int  nScrPitch;
      BYTE vScreen[320 * 240 * sizeof(COLORT)];

	    nScrXSize = m_pScreen->GetScrXSize();
	    nScrYSize = m_pScreen->GetScrYSize();
	    nScrPitch = m_pScreen->GetScrPitch();
      PcxFile.PcxHeader.Header    = 0x0a;
      PcxFile.PcxHeader.Version   = 0x05;
      PcxFile.PcxHeader.Encode    = 0x01;
      PcxFile.PcxHeader.BitPerPix = 0x01;
      PcxFile.PcxHeader.X1        =    0;
      PcxFile.PcxHeader.Y1        =    0;
      PcxFile.PcxHeader.X2        = nScrXSize - 1;
      PcxFile.PcxHeader.Y2        = nScrYSize - 1;
      PcxFile.PcxHeader.Hres      = nScrXSize;
      PcxFile.PcxHeader.Vres      = nScrYSize;
      memset(PcxFile.Palettes,0x00,16 * 3);
      PcxFile.Palettes[1].Red     = 0xff;
      PcxFile.Palettes[1].Green   = 0xff;
      PcxFile.Palettes[1].Blue    = 0xff;
      PcxFile.PCXInfo.Vmode       = 0x00;
      PcxFile.PCXInfo.NumOfPlanes = 0x01;
      PcxFile.PCXInfo.BytesPerLine= nScrPitch;
      PcxFile.PCXInfo.PalInfo     = 0x01;
      PcxFile.PCXInfo.ScannerX    = 0x0000;
      PcxFile.PCXInfo.ScannerY    = 0x0000;
      memset(&PcxFile.PCXInfo.Unused,0x00,sizeof(PcxFile.PCXInfo.Unused));
      pLine = vLine;
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Header    ,sizeof(PcxFile.PcxHeader.Header   ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Version   ,sizeof(PcxFile.PcxHeader.Version  ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Encode    ,sizeof(PcxFile.PcxHeader.Encode   ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.BitPerPix ,sizeof(PcxFile.PcxHeader.BitPerPix));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X1        ,sizeof(PcxFile.PcxHeader.X1       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y1        ,sizeof(PcxFile.PcxHeader.Y1       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X2        ,sizeof(PcxFile.PcxHeader.X2       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y2        ,sizeof(PcxFile.PcxHeader.Y2       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Hres      ,sizeof(PcxFile.PcxHeader.Hres     ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Vres      ,sizeof(PcxFile.PcxHeader.Vres     ));
      memmove(pLine,PcxFile.Palettes,16 * 3); pLine += (16 * 3);
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.Vmode       ,sizeof(PcxFile.PCXInfo.Vmode       ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.NumOfPlanes ,sizeof(PcxFile.PCXInfo.NumOfPlanes ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.BytesPerLine,sizeof(PcxFile.PCXInfo.BytesPerLine));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.PalInfo     ,sizeof(PcxFile.PCXInfo.PalInfo     ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerX    ,sizeof(PcxFile.PCXInfo.ScannerX    ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerY    ,sizeof(PcxFile.PCXInfo.ScannerY    ));
      memmove(pLine,&PcxFile.PCXInfo.Unused,54); pLine += 54;
      nPcxSize = (int)(pLine - vLine);
      memmove(pPcxData,vLine,nPcxSize);
//////////////////////////////////////////////////////////////////////////////////////////////////////
      m_pScreen->SaveScreen((UCHAR *)vScreen);
      for (Y = 0;Y < nScrYSize;Y++)
          {
           Before = 256;
           Counter= 0;
           for (X = 0;X < nScrPitch;X++)
               {
                TempX = vScreen[Y * nScrPitch + X];
                if (TempX & 0xc0)
                   {
                    if (Counter)
                       {
                        TempY = 0xc0 | Counter;
                        TempZ = Before;
                        pPcxData[nPcxSize++] = TempY;
                        pPcxData[nPcxSize++] = TempZ;
                       }
                    TempY = 0xc1;
                    pPcxData[nPcxSize++] = TempY;
                    pPcxData[nPcxSize++] = TempX;
                    Before = 256;
                    Counter= 0;
                   }
                else
                   {
                    if (Before == TempX)
                       {
                        if (Counter < 62)
                            ++Counter;
                        else
                           {
                            TempY = 0xc0 | Counter;
                            TempZ = Before;
                            pPcxData[nPcxSize++] = TempY;
                            pPcxData[nPcxSize++] = TempZ;
                            Before = TempX;
                            Counter= 1;
                           }
                       }
                    else
                       {
                        if (Counter)
                           {
                            TempY = 0xc0 | Counter;
                            TempZ = Before;
                            pPcxData[nPcxSize++] = TempY;
                            pPcxData[nPcxSize++] = TempZ;
                           }
                        Before = TempX;
                        Counter= 1;
                       }
                   }
               }
           if (Counter)
              {
               TempY = 0xc0 | Counter;
               TempZ = Before;
               pPcxData[nPcxSize++] = TempY;
               pPcxData[nPcxSize++] = TempZ;
              }
          }
/*
      for (X = 0;X < (256 * 3);X++)
           Pal[X] = 0x00 << 2;
      pPcxData[nPcxSize++] = 12;
      memmove(&pPcxData[nPcxSize],Pal,768); nPcxSize += 768;
*/
      return(nPcxSize);
}

int   cMEMPCX::GetPcxScreen65535(BYTE *pPcxData)
{
      PCX_FILE PcxFile;
//      BYTE Pal[256 * 3];
      BYTE TempX;
      BYTE TempY;
      BYTE TempZ;
      INT  Before,Counter;
      INT  X,Y,Z;
	  BYTE  vLine[2048];
      BYTE *pLine;
      int  nPcxSize;
	    int  nScrXSize;
	    int  nScrYSize;
	    int  nScrPitch;
	  WORD vScreen[320 * 240];

	  nScrXSize = m_pScreen->GetScrXSize();
	  nScrYSize = m_pScreen->GetScrYSize();
	  nScrPitch = m_pScreen->GetScrPitch();
		
	  PcxFile.PcxHeader.Header    = 0x0a;
      PcxFile.PcxHeader.Version   = 0x05;
      PcxFile.PcxHeader.Encode    = 0x01;
      PcxFile.PcxHeader.BitPerPix = 0x08;
      PcxFile.PcxHeader.X1        =    0;
      PcxFile.PcxHeader.Y1        =    0;
      PcxFile.PcxHeader.X2        = nScrXSize - 1;
      PcxFile.PcxHeader.Y2        = nScrYSize - 1;
      PcxFile.PcxHeader.Hres      = nScrXSize;
      PcxFile.PcxHeader.Vres      = nScrYSize;
      memset(PcxFile.Palettes,0x00,16 * 3);
      PcxFile.Palettes[1].Red     = 0xff;
      PcxFile.Palettes[1].Green   = 0xff;
      PcxFile.Palettes[1].Blue    = 0xff;
      PcxFile.PCXInfo.Vmode       = 0x00;
      PcxFile.PCXInfo.NumOfPlanes = 0x03;
      PcxFile.PCXInfo.BytesPerLine= nScrPitch;
      PcxFile.PCXInfo.PalInfo     = 0x01;
      PcxFile.PCXInfo.ScannerX    = 0x0000;
      PcxFile.PCXInfo.ScannerY    = 0x0000;
      memset(&PcxFile.PCXInfo.Unused,0x00,sizeof(PcxFile.PCXInfo.Unused));
      pLine = vLine;
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Header    ,sizeof(PcxFile.PcxHeader.Header   ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Version   ,sizeof(PcxFile.PcxHeader.Version  ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Encode    ,sizeof(PcxFile.PcxHeader.Encode   ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.BitPerPix ,sizeof(PcxFile.PcxHeader.BitPerPix));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X1        ,sizeof(PcxFile.PcxHeader.X1       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y1        ,sizeof(PcxFile.PcxHeader.Y1       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X2        ,sizeof(PcxFile.PcxHeader.X2       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y2        ,sizeof(PcxFile.PcxHeader.Y2       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Hres      ,sizeof(PcxFile.PcxHeader.Hres     ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Vres      ,sizeof(PcxFile.PcxHeader.Vres     ));
      memmove(pLine,PcxFile.Palettes,16 * 3); pLine += (16 * 3);
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.Vmode       ,sizeof(PcxFile.PCXInfo.Vmode       ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.NumOfPlanes ,sizeof(PcxFile.PCXInfo.NumOfPlanes ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.BytesPerLine,sizeof(PcxFile.PCXInfo.BytesPerLine));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.PalInfo     ,sizeof(PcxFile.PCXInfo.PalInfo     ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerX    ,sizeof(PcxFile.PCXInfo.ScannerX    ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerY    ,sizeof(PcxFile.PCXInfo.ScannerY    ));
      memmove(pLine,&PcxFile.PCXInfo.Unused,54); pLine += 54;
      nPcxSize = (int)(pLine - vLine);
      memmove(pPcxData,vLine,nPcxSize);
//////////////////////////////////////////////////////////////////////////////////////////////////////
      m_pScreen->SaveScreen((UCHAR *)vScreen);
      for (Y = 0;Y < nScrYSize;Y++)
          {
           for (Z = 0;Z < 3;Z++)
               {
                Before = 256;
                Counter= 0;
                for (X = 0;X < nScrPitch;X++)
                    {
                     if (Z == 0) TempX = ((BYTE)(vScreen[Y * nScrPitch + X] >> 11)) << 3;
                     if (Z == 1) TempX = ((BYTE)(vScreen[Y * nScrPitch + X] >>  5) & 0x3f) << 2;
                     if (Z == 2) TempX = ((BYTE)(vScreen[Y * nScrPitch + X] & 0x1f)) << 3;
                     if (TempX & 0xc0)
                        {
                         if (Counter)
                            {
                             TempY = 0xc0 | Counter;
                             TempZ = Before;
                             pPcxData[nPcxSize++] = TempY;
                             pPcxData[nPcxSize++] = TempZ;
                            }
                         TempY = 0xc1;
                         pPcxData[nPcxSize++] = TempY;
                         pPcxData[nPcxSize++] = TempX;
                         Before = 256;
                         Counter= 0;
                        }
                     else
                        {
                         if (Before == TempX)
                            {
                             if (Counter < 62)
                                 ++Counter;
                             else
                                {
                                 TempY = 0xc0 | Counter;
                                 TempZ = Before;
                                 pPcxData[nPcxSize++] = TempY;
                                 pPcxData[nPcxSize++] = TempZ;
                                 Before = TempX;
                                 Counter= 1;
                                }
                            }
                         else
                            {
                             if (Counter)
                                {
                                 TempY = 0xc0 | Counter;
                                 TempZ = Before;
                                 pPcxData[nPcxSize++] = TempY;
                                 pPcxData[nPcxSize++] = TempZ;
                                }
                             Before = TempX;
                             Counter= 1;
                            }
                        }
                    }
               }
		   if (Counter)
		   {
			   TempY = 0xc0 | Counter;
			   TempZ = Before;
			   pPcxData[nPcxSize++] = TempY;
			   pPcxData[nPcxSize++] = TempZ;
		   }
		   
          }
/*
      for (X = 0;X < (256 * 3);X++)
           Pal[X] = 0x00 << 2;
      pPcxData[nPcxSize++] = 12;
      memmove(&pPcxData[nPcxSize],Pal,768); nPcxSize += 768;
*/
      return(nPcxSize);
}
/* 2014.05.14. 640x480 Han */
int   cMEMPCX::GetPcxScreen65535x640(BYTE *pPcxData)
{
      PCX_FILE PcxFile;
//      BYTE Pal[256 * 3];
      BYTE TempX;
      BYTE TempY;
      BYTE TempZ;
      INT  Before,Counter;
      INT  X,Y,Z;
      BYTE  vLine[4096];
      BYTE *pLine;
      int  nPcxSize;
	    int  nScrXSize;
	    int  nScrYSize;
	    int  nScrPitch;
      WORD vScreen[640 * 480];
	 
	    nScrXSize = 640;//m_pScreen->GetScrXSize();
	    nScrYSize = 480;//m_pScreen->GetScrYSize();
	    nScrPitch = 640;//m_pScreen->GetScrPitch();

		*(DWORD *)0x03b00000 = 0x22222222;
		*(DWORD *)0x03b00004 = nScrXSize;
		*(DWORD *)0x03b00008 = nScrYSize;
		*(DWORD *)0x03b0000c = nScrPitch;
		
	  PcxFile.PcxHeader.Header    = 0x0a;
      PcxFile.PcxHeader.Version   = 0x05;
      PcxFile.PcxHeader.Encode    = 0x01;
      PcxFile.PcxHeader.BitPerPix = 0x08;
      PcxFile.PcxHeader.X1        =    0;
      PcxFile.PcxHeader.Y1        =    0;
      PcxFile.PcxHeader.X2        = nScrXSize - 1;
      PcxFile.PcxHeader.Y2        = nScrYSize - 1;
      PcxFile.PcxHeader.Hres      = nScrXSize;
      PcxFile.PcxHeader.Vres      = nScrYSize;
      memset(PcxFile.Palettes,0x00,16 * 3);
      PcxFile.Palettes[1].Red     = 0xff;
      PcxFile.Palettes[1].Green   = 0xff;
      PcxFile.Palettes[1].Blue    = 0xff;
      PcxFile.PCXInfo.Vmode       = 0x00;
      PcxFile.PCXInfo.NumOfPlanes = 0x03;
      PcxFile.PCXInfo.BytesPerLine= nScrPitch;
      PcxFile.PCXInfo.PalInfo     = 0x01;
      PcxFile.PCXInfo.ScannerX    = 0x0000;
      PcxFile.PCXInfo.ScannerY    = 0x0000;
      memset(&PcxFile.PCXInfo.Unused,0x00,sizeof(PcxFile.PCXInfo.Unused));
      pLine = vLine;
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Header    ,sizeof(PcxFile.PcxHeader.Header   ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Version   ,sizeof(PcxFile.PcxHeader.Version  ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Encode    ,sizeof(PcxFile.PcxHeader.Encode   ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.BitPerPix ,sizeof(PcxFile.PcxHeader.BitPerPix));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X1        ,sizeof(PcxFile.PcxHeader.X1       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y1        ,sizeof(PcxFile.PcxHeader.Y1       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X2        ,sizeof(PcxFile.PcxHeader.X2       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y2        ,sizeof(PcxFile.PcxHeader.Y2       ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Hres      ,sizeof(PcxFile.PcxHeader.Hres     ));
      pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Vres      ,sizeof(PcxFile.PcxHeader.Vres     ));
      memmove(pLine,PcxFile.Palettes,16 * 3); pLine += (16 * 3);
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.Vmode       ,sizeof(PcxFile.PCXInfo.Vmode       ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.NumOfPlanes ,sizeof(PcxFile.PCXInfo.NumOfPlanes ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.BytesPerLine,sizeof(PcxFile.PCXInfo.BytesPerLine));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.PalInfo     ,sizeof(PcxFile.PCXInfo.PalInfo     ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerX    ,sizeof(PcxFile.PCXInfo.ScannerX    ));
      pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerY    ,sizeof(PcxFile.PCXInfo.ScannerY    ));
      memmove(pLine,&PcxFile.PCXInfo.Unused,54); pLine += 54;
      nPcxSize = (int)(pLine - vLine);
      memmove(pPcxData,vLine,nPcxSize);
//////////////////////////////////////////////////////////////////////////////////////////////////////
      m_pScreen->SaveScreen((UCHAR *)vScreen);
      for (Y = 0;Y < nScrYSize;Y++)
          {
           for (Z = 0;Z < 3;Z++)
               {
                Before = 256;
                Counter= 0;
                for (X = 0;X < nScrPitch;X++)
                    {
                     if (Z == 0) TempX = ((BYTE)(vScreen[Y * nScrPitch + X] >> 11)) << 3;
                     if (Z == 1) TempX = ((BYTE)(vScreen[Y * nScrPitch + X] >>  5) & 0x3f) << 2;
                     if (Z == 2) TempX = ((BYTE)(vScreen[Y * nScrPitch + X] & 0x1f)) << 3;
                     if (TempX & 0xc0)
                        {
                         if (Counter)
                            {
                             TempY = 0xc0 | Counter;
                             TempZ = Before;
                             pPcxData[nPcxSize++] = TempY;
                             pPcxData[nPcxSize++] = TempZ;
                            }
                         TempY = 0xc1;
                         pPcxData[nPcxSize++] = TempY;
                         pPcxData[nPcxSize++] = TempX;
                         Before = 256;
                         Counter= 0;
                        }
                     else
                        {
                         if (Before == TempX)
                            {
                             if (Counter < 62)
                                 ++Counter;
                             else
                                {
                                 TempY = 0xc0 | Counter;
                                 TempZ = Before;
                                 pPcxData[nPcxSize++] = TempY;
                                 pPcxData[nPcxSize++] = TempZ;
                                 Before = TempX;
                                 Counter= 1;
                                }
                            }
                         else
                            {
                             if (Counter)
                                {
                                 TempY = 0xc0 | Counter;
                                 TempZ = Before;
                                 pPcxData[nPcxSize++] = TempY;
                                 pPcxData[nPcxSize++] = TempZ;
                                }
                             Before = TempX;
                             Counter= 1;
                            }
                        }
                    }
               }
		   if (Counter)
		   {
			   TempY = 0xc0 | Counter;
			   TempZ = Before;
			   pPcxData[nPcxSize++] = TempY;
			   pPcxData[nPcxSize++] = TempZ;
		   }
		   TempY = 0xc1;
		   pPcxData[nPcxSize++] = TempY;
		   pPcxData[nPcxSize++] = TempX;
		   
          }
/*
      for (X = 0;X < (256 * 3);X++)
           Pal[X] = 0x00 << 2;
      pPcxData[nPcxSize++] = 12;
      memmove(&pPcxData[nPcxSize],Pal,768); nPcxSize += 768;
*/
      return(nPcxSize);
}

int cMEMPCX::GetPcxScreen16Bit800(BYTE *pPcxData)
{
	PCX_FILE PcxFile;
	BYTE TempX = 0;
	BYTE TempY;
	BYTE TempZ;
	INT  Before,Counter;
	INT  X,Y,Z;
	BYTE	vLine[2048];
	BYTE *pLine;
	int  nPcxSize;
	int  nScrXSize;
	int  nScrYSize;
	int  nScrPitch;
	DWORD nPos = 0;
	COLORREF TmpColor;

	nScrXSize = 800;
	nScrYSize = 480;
	nScrPitch = 800;
	
	PcxFile.PcxHeader.Header	  = 0x0a;
	PcxFile.PcxHeader.Version	= 0x05;
	PcxFile.PcxHeader.Encode	  = 0x01;

	PcxFile.PcxHeader.BitPerPix = 0x08;

	PcxFile.PcxHeader.X1		  =    0;
	PcxFile.PcxHeader.Y1		  =    0;
	PcxFile.PcxHeader.X2		  = nScrXSize - 1;
	PcxFile.PcxHeader.Y2		  = nScrYSize - 1;
	PcxFile.PcxHeader.Hres	  = nScrXSize;
	PcxFile.PcxHeader.Vres	  = nScrYSize;
	memset(PcxFile.Palettes,0x00,16 * 3);
	PcxFile.Palettes[1].Red   = 0xff;
	PcxFile.Palettes[1].Green	= 0xff;
	PcxFile.Palettes[1].Blue	  = 0xff;
	PcxFile.PCXInfo.Vmode	  = 0x00;
	
	PcxFile.PCXInfo.NumOfPlanes = 0x03;
	PcxFile.PCXInfo.BytesPerLine= nScrPitch;

	PcxFile.PCXInfo.PalInfo   = 0x01;
	PcxFile.PCXInfo.ScannerX	  = 0x0000;
	PcxFile.PCXInfo.ScannerY	  = 0x0000;
	memset(&PcxFile.PCXInfo.Unused,0x00,sizeof(PcxFile.PCXInfo.Unused));
	pLine = vLine;
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Header	,sizeof(PcxFile.PcxHeader.Header   ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Version ,sizeof(PcxFile.PcxHeader.Version  ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Encode	,sizeof(PcxFile.PcxHeader.Encode   ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.BitPerPix ,sizeof(PcxFile.PcxHeader.BitPerPix));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X1		,sizeof(PcxFile.PcxHeader.X1	   ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y1		,sizeof(PcxFile.PcxHeader.Y1	   ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.X2		,sizeof(PcxFile.PcxHeader.X2	   ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Y2		,sizeof(PcxFile.PcxHeader.Y2	   ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Hres		,sizeof(PcxFile.PcxHeader.Hres	   ));
	pLine = PcxSetData(pLine,&PcxFile.PcxHeader.Vres		,sizeof(PcxFile.PcxHeader.Vres	   ));
	memmove(pLine,PcxFile.Palettes,16 * 3); pLine += (16 * 3);
	pLine = PcxSetData(pLine,&PcxFile.PCXInfo.Vmode 	,sizeof(PcxFile.PCXInfo.Vmode		));
	pLine = PcxSetData(pLine,&PcxFile.PCXInfo.NumOfPlanes ,sizeof(PcxFile.PCXInfo.NumOfPlanes ));
	pLine = PcxSetData(pLine,&PcxFile.PCXInfo.BytesPerLine,sizeof(PcxFile.PCXInfo.BytesPerLine));
	pLine = PcxSetData(pLine,&PcxFile.PCXInfo.PalInfo	,sizeof(PcxFile.PCXInfo.PalInfo 	));
	pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerX	,sizeof(PcxFile.PCXInfo.ScannerX	));
	pLine = PcxSetData(pLine,&PcxFile.PCXInfo.ScannerY	,sizeof(PcxFile.PCXInfo.ScannerY	));
	memmove(pLine,&PcxFile.PCXInfo.Unused,54); pLine += 54;
	nPcxSize = (int)(pLine - vLine);
	memmove(pPcxData,vLine,nPcxSize);
	//////////////////////////////////////////////////////////////////////////////////////////////////////
	m_pScreen->SaveScreen((UCHAR *)m_pvScreen);
	
	for (Y = 0;Y < nScrYSize;Y++)
	{
		for (Z = 0;Z < 4;Z++)
		{
			Before = 256;
			Counter= 0;
			for (X = 0;X < nScrXSize;X++)
			{
				nPos = Y * nScrPitch + X;
				TmpColor = m_pvScreen[nPos];
				if (Z == 0) TempX = ((BYTE)(TmpColor >> 11)) << 3;
				if (Z == 1) TempX = ((BYTE)(TmpColor >> 5) & 0x3f) << 2;
				if (Z == 2) TempX = ((BYTE)(TmpColor & 0x1f)) << 3;
				if (Z == 3) continue;
				
				if (TempX & 0xc0)
				{
					if (Counter)
					{
						TempY = 0xc0 | Counter;
						TempZ = Before;
						pPcxData[nPcxSize++] = TempY;
						pPcxData[nPcxSize++] = TempZ;
					}
					TempY = 0xc1;
					pPcxData[nPcxSize++] = TempY;
					pPcxData[nPcxSize++] = TempX;
					Before = 256;
					Counter= 0;
				}
				else
				{
					if (Before == TempX)
					{
						if (Counter < 62)
							++Counter;
						else
						{
							TempY = 0xc0 | Counter;
							TempZ = Before;
							pPcxData[nPcxSize++] = TempY;
							pPcxData[nPcxSize++] = TempZ;
							Before = TempX;
							Counter= 1;
						}
					}
					else
					{
						if (Counter)
						{
							TempY = 0xc0 | Counter;
							TempZ = Before;
							pPcxData[nPcxSize++] = TempY;
							pPcxData[nPcxSize++] = TempZ;
						}
						Before = TempX;
						Counter= 1;
					}
				}
			}
		}
		
		if (Counter)
		{
			TempY = 0xc0 | Counter;
			TempZ = Before;
			pPcxData[nPcxSize++] = TempY;
			pPcxData[nPcxSize++] = TempZ;
		}
	}
	
	return(nPcxSize);
}

BYTE *cMEMPCX::PcxSetData(BYTE *pTarget,void *pSource,int nSize)
{
      BYTE *pTemp;

      pTemp = static_cast<BYTE *>(pSource);
      #if defined(EB)
	        if (nSize == 1)
	            *pTarget++ = pTemp[0];
	        if (nSize == 2)
	           {
	            *pTarget++ = pTemp[1];
	            *pTarget++ = pTemp[0];
	           }
	        if (nSize == 4)
	           {
	            *pTarget++ = pTemp[3];
	            *pTarget++ = pTemp[2];
	            *pTarget++ = pTemp[1];
	            *pTarget++ = pTemp[0];
	           }
      #else
	        if (nSize == 1)
	            *pTarget++ = pTemp[0];
	        if (nSize == 2)
	           {
	            *pTarget++ = pTemp[0];
	            *pTarget++ = pTemp[1];
	           }
	        if (nSize == 4)
	           {
	            *pTarget++ = pTemp[0];
	            *pTarget++ = pTemp[1];
	            *pTarget++ = pTemp[2];
	            *pTarget++ = pTemp[3];
	           }
      #endif
      return(pTarget);
}

