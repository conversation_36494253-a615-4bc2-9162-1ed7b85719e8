#include <stdio.h>
#include "ACK.hpp"

CAck::CAck() : CSentence()
{
}
    
CAck::CAck(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CAck::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_ACK;
}

/******************************************************************************
*
* ACK- Acknowledge Alarm
*
* $--ACK,xxx*hh<CR><LF>
*        |
*        1
*
* 1.  Unique alram number (identifier) at alarm source
*
******************************************************************************/
void CAck::Parse()
{
	m_nAlramNumber  = GetFieldInteger(1);
}

void CAck::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}
