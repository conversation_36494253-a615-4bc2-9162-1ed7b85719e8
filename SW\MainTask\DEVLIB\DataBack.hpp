/*...........................................................................*/
/*.                  File Name : DataBack.hpp                               .*/
/*.                                                                         .*/
/*.                       Date : 2008.08.11                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "NandFlash.hpp"

#ifndef  __DATABACK_HPP
#define  __DATABACK_HPP

//=============================================================================
#define  BACK_CHK_HEAD_FLAG        0x55aa7799

#define  BACK_SYS_DATA             0xb0040000
#define  BACK_BASIC_DATA           0xb0060000

#define  BACK_A_DATA               0xb0080000
#define  BACK_B_DATA               0xb00A0000
#define  BACK_C_DATA               0xb00C0000
#define  BACK_D_DATA               0xb00E0000
#define  BACK_E_DATA               0xb0100000
#define  BACK_F_DATA               0xb0120000
#define  BACK_G_DATA               0xb0140000
#define  BACK_H_DATA               0xb0160000
#define  BACK_I_DATA               0xb0180000
#define  BACK_J_DATA               0xb01A0000
#define  BACK_K_DATA               0xb01C0000
#define  BACK_L_DATA               0xb01E0000

#define  BACK_M_DATA               0xb04c0000
#define  BACK_N_DATA               0xb04e0000

#define  BACK_TRACK0_DATA          0xb0200000

#define  BACK_TRACK1_DATA          0xb0240000
#define  BACK_TRACK2_DATA          0xb0280000
#define  BACK_TRACK3_DATA          0xb02C0000
#define  BACK_TRACK4_DATA          0xb0300000
#define  BACK_TRACK5_DATA          0xb0340000
#define  BACK_TRACK6_DATA          0xb0380000
#define  BACK_TRACK7_DATA          0xb03C0000
#define  BACK_TRACK8_DATA          0xb0400000
#define  BACK_TRACK9_DATA          0xb0440000

#define  BACK_TRACK0_HEAD   (BACK_TRACK0_DATA + 0x00000000)
#define  BACK_TRACK1_HEAD   (BACK_TRACK1_DATA + 0x00000000)
#define  BACK_TRACK2_HEAD   (BACK_TRACK2_DATA + 0x00000000)
#define  BACK_TRACK3_HEAD   (BACK_TRACK3_DATA + 0x00000000)
#define  BACK_TRACK4_HEAD   (BACK_TRACK4_DATA + 0x00000000)
#define  BACK_TRACK5_HEAD   (BACK_TRACK5_DATA + 0x00000000)
#define  BACK_TRACK6_HEAD   (BACK_TRACK6_DATA + 0x00000000)
#define  BACK_TRACK7_HEAD   (BACK_TRACK7_DATA + 0x00000000)
#define  BACK_TRACK8_HEAD   (BACK_TRACK8_DATA + 0x00000000)
#define  BACK_TRACK9_HEAD   (BACK_TRACK9_DATA + 0x00000000)

#define  BACK_TRACK0_DATA_X (BACK_TRACK0_DATA + 0x00000000)
#define  BACK_TRACK1_DATA_X (BACK_TRACK1_DATA + 0x00000000)
#define  BACK_TRACK2_DATA_X (BACK_TRACK2_DATA + 0x00000000)
#define  BACK_TRACK3_DATA_X (BACK_TRACK3_DATA + 0x00000000)
#define  BACK_TRACK4_DATA_X (BACK_TRACK4_DATA + 0x00000000)
#define  BACK_TRACK5_DATA_X (BACK_TRACK5_DATA + 0x00000000)
#define  BACK_TRACK6_DATA_X (BACK_TRACK6_DATA + 0x00000000)
#define  BACK_TRACK7_DATA_X (BACK_TRACK7_DATA + 0x00000000)
#define  BACK_TRACK8_DATA_X (BACK_TRACK8_DATA + 0x00000000)
#define  BACK_TRACK9_DATA_X (BACK_TRACK9_DATA + 0x00000000)

#define  BACK_TRACK0_DATA_Y (BACK_TRACK0_DATA + 0x00020000)
#define  BACK_TRACK1_DATA_Y (BACK_TRACK1_DATA + 0x00020000)
#define  BACK_TRACK2_DATA_Y (BACK_TRACK2_DATA + 0x00020000)
#define  BACK_TRACK3_DATA_Y (BACK_TRACK3_DATA + 0x00020000)
#define  BACK_TRACK4_DATA_Y (BACK_TRACK4_DATA + 0x00020000)
#define  BACK_TRACK5_DATA_Y (BACK_TRACK5_DATA + 0x00020000)
#define  BACK_TRACK6_DATA_Y (BACK_TRACK6_DATA + 0x00020000)
#define  BACK_TRACK7_DATA_Y (BACK_TRACK7_DATA + 0x00020000)
#define  BACK_TRACK8_DATA_Y (BACK_TRACK8_DATA + 0x00020000)
#define  BACK_TRACK9_DATA_Y (BACK_TRACK9_DATA + 0x00020000)

#define  BACK_TRACK_HEAD_DATA                   0xb0480000

#define  BACK_TRACK_HEAD_SIZE                   (16 * 1024)
//#define  BACK_TRACK_DATA_SIZE                  (112 * 1024)
#define  BACK_TRACK_DATA_SIZE                  (128 * 1024)

#define  BACK_ROUTE_DATA           0xb04a0000
#define  BACK_WAYPNT_DATA_X        0xb0500000

#define  BACK_NAVTEX_DATA          0xb0600000

//=============================================================================
#define  BACK_MODE_SAVE                     0
#define  BACK_MODE_LOAD                     1

#define  BACK_SAVE_MODE_CHECK               0
#define  BACK_SAVE_MODE_START               1
#define  BACK_SAVE_MODE_DELAY               2
//=============================================================================
#define  FLASH_BLOCK_SIZE         (128 * 1024)
#define  FLASH_BACK_SECT_SIZE              12

#define  FLASH_MAX_DOWN_SIZE   (128 * 1024 * 1024)

//=============================================================================

class cDataBack
{
   private:
      int    m_nAllClearMode;
      DWORD  m_dStartBackAddr;
      cNandFlash *m_pNandFlash;

   public:
      cDataBack(cNandFlash *pNandFlash);
      virtual ~cDataBack(void);
   public:
      void  SetAllClearMode(int nMode);
      int   GetAllClearMode(void);
      void  SetStartBackAddr(DWORD dStartBackAddr);
      DWORD GetStartBackAddr(void);
      void  BackDataWrite(DWORD dFlashAddr,void *pData,int nSize);
      void  BackDataRead(void *pData,int nSize,DWORD dFlashAddr);
      int   BackDataSegmentWrite(DWORD dFlashBlock,void *pData,int nStartPos,int nWriteSize);
      int   BackDataSegmentRead(void *pData,int nReadSize,DWORD dFlashBlock);
      int   FindDataSegmentStart(DWORD dFlashBlock,int nSegSize);
      int   FindDataSegmentSize(int nDataSize);
};
//=============================================================================
class cFLASH
{
   private:
      static cNandFlash *m_pNandFlash;
      static cDataBack  *m_pDataBack;

      DWORD m_dBaseAddr;
      DWORD m_dBackAddr;

   public:
      cFLASH(void);
      virtual ~cFLASH(void);
   public:
	  static void SetBusConfig(void);
	  static void SetAllClearMode(int nMode);
      DWORD GetBaseAddr(void);
      void  SetBaseAddr(DWORD dBaseAddr);
      DWORD GetBackAddr(void);
      void  SetBackAddr(DWORD dBackAddr);
      void  WriteCommandToFlash(volatile WORD *pReg,WORD wData);
      WORD  ReadStatusFromFlash(volatile WORD *pReg);
      WORD  WaitFlashReady(volatile WORD *pReg,DWORD dDelay);
      WORD  ReadFlashDeviceCode(void);
      WORD  EraseOneFlashBlockNoWaitStart(DWORD dBlockAddr,int nLockMode);
      WORD  EraseOneFlashBlockNoWaitEnd(DWORD dBlockAddr);
      WORD  EraseOneFlashBlockWaitEnd(DWORD dBlockAddr);
      WORD  EraseFlashBlock(DWORD dBlockAddr,int nBlockNo,int nLockMode);
      WORD  WriteWordToFlash(DWORD dAddr,WORD wData);
      WORD  WriteBlockToFlash(DWORD dAddr,WORD *pData,int nSize);
      void  EraseAndDownToFlash(DWORD dFlashAddr,void *pData,int nBlockSize);
      WORD  LockFlashBlock(DWORD dBlockAddr,int nBlockNo);
      WORD  UnLockFlashBlock(void);
      void  SaveBackSectionData(int nSectionNo,void *pData,DWORD dDataSize);
      void  LoadBackSectionData(int nSectionNo,void *pData,DWORD dDataSize);
};
//=============================================================================

#endif

