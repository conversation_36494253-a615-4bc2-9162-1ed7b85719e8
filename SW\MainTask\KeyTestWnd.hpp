#ifndef __KEY_TEST_WND_HPP__
#define __KEY_TEST_WND_HPP__

#include "Wnd.hpp"

class CKeyTestWnd : public CWnd {

	private:
		enum {
			KEY_TYPE_NUM_0 = 0,
			KEY_TYPE_NUM_1,
			KEY_TYPE_NUM_2,
			KEY_TYPE_NUM_3,
			KEY_TYPE_NUM_4,
			KEY_TYPE_NUM_5,
			KEY_TYPE_NUM_6,
			KEY_TYPE_NUM_7,
			KEY_TYPE_NUM_8,
			KEY_TYPE_NUM_9,
			KEY_TYPE_STAR,
			KEY_TYPE_SHARP,
			KEY_TYPE_UP_ARR,
			KEY_TYPE_DN_ARR,
			KEY_TYPE_LEFT_ARR,
			KEY_TYPE_RIGHT_ARR,
			KEY_TYPE_MENU,
			KEY_TYPE_ENTER,
			KEY_TYPE_PWR,
			KEY_TYPE_F1,
			KEY_TYPE_F2,
			K<PERSON><PERSON>_TYPE_F3,
			<PERSON><PERSON><PERSON>_TYPE_F4,
			MAX_KEY_TYPE
		};
		
	protected:
		BOOL m_bPressNum[10];
		BOOL m_bPressLeft;
		BOOL m_bPressRight;
		BOOL m_bPressUp;
		BOOL m_bPressDown;
		BOOL m_bPressStar;
		BOOL m_bPressSharp;

		BOOL m_bPressEnter;
		BOOL m_bPressMenu;
		BOOL m_bPressPower;
		
		BOOL m_bPressFunc1;
		BOOL m_bPressFunc2;
		BOOL m_bPressFunc3;
		BOOL m_bPressFunc4;

	private:
		void InitVar();
		
	public:
		CKeyTestWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);

		BOOL IsFunc1BtnPressed();
		BOOL IsMenuBtnPressed();
		
		void DrawKeyButton(int nKeyType, BOOL bPressed, int left, int top, int right, int bottom);
		void DrawFuncBtn(BOOL bRedraw);
		void DrawKeyLayout();
		void DrawWnd(BOOL bRedraw = TRUE);
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void ResetKey();
		void SetFocus(int nFocus) { m_nFocus = nFocus; }
};

#endif


