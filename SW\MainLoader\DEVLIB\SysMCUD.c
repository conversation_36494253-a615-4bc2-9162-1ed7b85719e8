/*...........................................................................*/
/*.                  File Name : SYSMCUD.C                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.03                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysMCUD.h"

#include <string.h>

//=============================================================================
#if defined(__POLLUX__)
    static xSYS_MCUD *G_pSysMCUD  = (xSYS_MCUD *)MCUD_PHSY_BASE_ADDR;
#else                           // SPICA
//  static xSYS_MCUD *G_pSysMCUD  = (xSYS_MCUD *)MCUD_PHSY_BASE_ADDR;
#endif
//=============================================================================

//=============================================================================
void  SysInitMCUD(void)
{
      int   nDeviceType;

      nDeviceType = SysGetDeviceType();
#if defined(__POLLUX__)

      // DDR2 SDRAM
      SysSetMCUdDIC(MCUD_DIC_NORMAL);
      SysSetMCUdDLLEnable(1);                    // DISSLL Enable

      SysSetMCUdCasLatency(SysGetCasLatencyByDevice(nDeviceType));    // LAT   = 2-cycle
      SysSetMCUdReadLatency(SysGetReadLatencyByDevice(nDeviceType));  // RDLAT = 2-cycle

      SysSetMCUdMRD(15);
      SysSetMCUdRP (15);
      SysSetMCUdRC (15);
      SysSetMCUdRCD(15);
      SysSetMCUdRAS(15);
      SysSetMCUdWR (15);

//    SysSetMCUdClockDelay(MCUD_DELAY_0_0);
      SysSetMCUdDQSDelay(MCUD_DELAY_3_5,MCUD_DELAY_3_5,MCUD_DELAY_0_0,MCUD_DELAY_0_0);

      SysSetMCUdRefreshPeriod(SysGetRefreshPeriodByDevice(nDeviceType));

      SysSetMCUdDisplayBlockMode(1);             // BLOCKDISP Enable
	
      // BANK A register update
      SysSetMCUdApplyModeSetting();

      do
        {
         SysDelayLoop(0x01FFFFF);
        }	while (SysGetMCUdBusyModeSetting());
#else                           // SPICA
/*
      DWORD dTemp;
//    DWORD dDDRType;
//    DWORD dDDRSize;
//    DWORD dDDRBW;
//    DWORD dResetConfig = *(volatile DWORD *)(CLKPWR_PHSY_BASE_ADDR + 0x007c);  

//    dDDRType = (dResetConfig >> 19) & 0x03; 
//    dDDRSize = (dResetConfig >> 21) & 0x03; 
//    dDDRBW   = (dResetConfig >> 23) & 0x01; 

      G_pSysMCUD->dMEMTIME0 = (0x00000000)  |    //
//                            (0x11U << 24) |    // [31:24] : tRFC
//                            (0x05U << 20) |    // [23:20] : Reserved
//                            (0x09U << 12) |    // [19:12] : tRAS
//                            (0x02U <<  4) |    // [ 7: 4] : tRP
//                            (0x02U <<  0) |    // [ 3: 0] : tRCD
                              (0x2cU << 24) |    // [31:24] : tRFC
                              (0x00U << 20) |    // [23:20] : Reserved
                              (0x0fU << 12) |    // [19:12] : tRAS
                              (0x04U <<  4) |    // [ 7: 4] : tRP
                              (0x04U <<  0) |    // [ 3: 0] : tRCD
                              (0);

      G_pSysMCUD->dMEMTIME1 = (0x00000000)  |    //
//                            (0x05U << 28) |    // [31:28] : tWTR (write to Read time)+ WL
//                            (0x02U << 24) |    // [27:24] : tWR
//                            (0x01U << 20) |    // [23:20] : tRTP (Read to Pre time)
//                            (0x02U << 16) |    // [19:16] : tMRD
//                            (0x0100<<  0) |    // [15: 0] : REFP
                              (0x06U << 28) |    // [31:28] : tWTR (write to Read time)+ WL
                              (0x05U << 24) |    // [27:24] : tWR
                              (0x00U << 20) |    // [23:20] : tRTP (Read to Pre time)
                              (0x01U << 16) |    // [19:16] : tMRD
                              (0x0550<<  0) |    // [15: 0] : REFP
                              (0);
      // DDR2
      G_pSysMCUD->dMEMCFG = (0U << 27)  |  // [27] : nDQSEnb(Only DDR2)
//                          (3U << 24)  |  // [26:24] : Delay Read Latency(DDR2PHY Latency)
                            (2U << 24)  |  // [26:24] : Delay Read Latency(DDR2PHY Latency)
                            (5U << 21)  |  // [23:21] : Cas Latency
                            (0U << 18)  |  // [20:18] : Add Latency
                            (1U << 16)  |  // [17:16] : Rtt Config
                            (0U << 10)  |  // [10] : DIC Config
                            (0U <<  9)  |  // [ 9] : DLL Config
                            (2U <<  7)  |  // [ 8: 7] : Memory Type (00: Reserved, 01: DDR, 10: DDR2, 11: MDDR)
                            (1U <<  5)  |  // [ 6: 5] : Memory Bank BUS Width (1:16-bit)
//                ((dDDRBW + 1) <<  3)  |  // [ 4: 3] : SD-DRAM Data bit width (2:8-bit, 3:16-bit)
//              ((dDDRSize + 2) <<  0)  |  // [ 2: 0] : DDR Size (2:256Mbit, 3:512Mbit, 4:1Gbit, others:Reserved)
                            (2U <<  3)  |  // [ 4: 3] : SD-DRAM Data bit width (2:8-bit, 3:16-bit)
                            (4U <<  0)  |  // [ 2: 0] : DDR Size (2:256Mbit, 3:512Mbit, 4:1Gbit, others:Reserved)
                            (0);

      G_pSysMCUD->dPHYMODE = 0;

      // PHYZQENB

      G_pSysMCUD->dPHYZQCTRL = 1;

      while (1)
            {
             dTemp = (G_pSysMCUD->dPHYZQCTRL >> 2) & 0x03;

             if (dTemp == 1)      // ZQ END Check
                { 
                 break; 
                } 
             else if (dTemp == 2) // ZQ Error Check
                {
                 // Error, do manual 
                 G_pSysMCUD->dPHYZQCTRL   = 0x00;
                 G_pSysMCUD->dPHYZQCTRL   = 0x02;
                 G_pSysMCUD->dPHYDLLFORCE = 0x15;

                 break;
                } 
            } 

      G_pSysMCUD->dPHYUPDATE = 1;

//    if (dDDRType == 3)      // MobileDDR without PHYDLOCK
//       {
//       }
//    else
//       { 
          G_pSysMCUD->dPHYDLLCTRL1 = 3;
//       }

      G_pSysMCUD->dMEMCTRL = 0x00008003;
//*/
#endif
}
void  SysSetMCUdSDRType(HWORD wType)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMCFG  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMCFG,wType, 7, 6);
#else                           // SPICA
#endif
}
void  SysSetMCUdSDRBusWidth(HWORD wBusWidth)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMCFG  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMCFG,wBusWidth, 5, 4);
#else                           // SPICA
#endif
}
void  SysSetMCUdSDRDataWidth(HWORD wDataWidth)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMCFG  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMCFG,wDataWidth, 3, 2);
#else                           // SPICA
#endif
}
void  SysSetMCUdSDRCapacity(HWORD wCapacity)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMCFG  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMCFG,wCapacity, 1, 0);
#else                           // SPICA
#endif
}
void  SysSetMCUdCasLatency(HWORD wCycle)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMTIME1  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMTIME1,wCycle,13,12);
      G_pSysMCUD->wMEMCONTROL&= ~(1 << 2);           // RWD_MASK = Normal
#else                           // SPICA
#endif
}
void  SysSetMCUdReadLatency(HWORD wCycle)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMCONTROL  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMCONTROL,wCycle, 7, 6);
#else                           // SPICA
#endif
}
void  SysSetMCUdMDS(HWORD wMDS)
{
#if defined(__SPICA__)
#endif
}
void  SysSetMCUdPASR(HWORD wPASR)
{
#if defined(__SPICA__)
#endif
}
void  SysSetMCUdDIC(HWORD wDIC)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMCFG  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMCFG,wDIC, 9, 9);
#else                           // SPICA
#endif
}
void  SysSetMCUdDLLEnable(int nDisableEnableMode)
{
#if defined(__POLLUX__)
      if (nDisableEnableMode)
          G_pSysMCUD->wMEMCFG &= ~(1 <<  8);
      else
          G_pSysMCUD->wMEMCFG |= (1 <<  8);
#else                           // SPICA
#endif
}
void  SysSetMCUdApplyModeSetting(void)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMTIME1  |= (1 << 15);
#else                           // SPICA
#endif
}
int   SysGetMCUdBusyModeSetting(void)
{
#if defined(__POLLUX__)
      if (G_pSysMCUD->wMEMTIME1 & (1 << 15))
          return(1);
      return(0);
#else                           // SPICA
      return(0);
#endif
}
void  SysSetMCUdMRD(HWORD wClocks)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMTIME0  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMTIME0,wClocks,15,12);
#else                           // SPICA
#endif
}
void  SysSetMCUdRP(HWORD wClocks)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMTIME0  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMTIME0,wClocks, 7, 4);
#else                           // SPICA
#endif
}
void  SysSetMCUdRCD(HWORD wClocks)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMTIME0  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMTIME0,wClocks, 3, 0);
#else                           // SPICA
#endif
}
void  SysSetMCUdRC(HWORD wClocks)
{
#if defined(__POLLUX__)
      HWORD wModeSet = 1 << 15;
      HWORD wTempX;

      wTempX = G_pSysMCUD->wMEMTIME1 & ~wModeSet;
      G_pSysMCUD->wMEMTIME1  = SysSetMCUdSetValue(wTempX,wClocks - 2,11, 8);
#else                           // SPICA
#endif
}
void  SysSetMCUdRAS(HWORD wClocks)
{
#if defined(__POLLUX__)
      HWORD wModeSet = 1 << 15;
      HWORD wTempX;

      wTempX = G_pSysMCUD->wMEMTIME1 & ~wModeSet;
      G_pSysMCUD->wMEMTIME1  = SysSetMCUdSetValue(wTempX,wClocks - 2, 7, 4);
#else                           // SPICA
#endif
}
void  SysSetMCUdWR(HWORD wClocks)
{
#if defined(__POLLUX__)
      HWORD wModeSet = 1 << 15;
      HWORD wTempX;

      wTempX = G_pSysMCUD->wMEMTIME1 & ~wModeSet;
      G_pSysMCUD->wMEMTIME1  = SysSetMCUdSetValue(wTempX,wClocks - 0, 3, 0);
#else                           // SPICA
#endif
}
void  SysSetMCUdRefreshPeriod(HWORD wPeriod)
{
#if defined(__POLLUX__)
      G_pSysMCUD ->wMEMREFRESH  = wPeriod;
#else                           // SPICA
#endif
}
void  SysSetMCUdDisplayBlockMode(int nDisableEnableMode)
{
#if defined(__POLLUX__)
      if (nDisableEnableMode)
          G_pSysMCUD->wMEMCONTROL |= (1 <<  0);
      else
          G_pSysMCUD->wMEMCONTROL &= ~(1 <<  0);
#else                           // SPICA
#endif
}
void  SysSetMCUdClockDelay(HWORD wDelay)
{
#if defined(__POLLUX__)
      G_pSysMCUD->wMEMCLKDELAY  = SysSetMCUdSetValue(G_pSysMCUD ->wMEMCLKDELAY,wDelay, 2, 0);
#else                           // SPICA
#endif
}
void  SysSetMCUdDQSDelay(HWORD wDQS0OUT,HWORD wDQS1OUT,HWORD wDQS0IN,HWORD wDQS1IN)
{
#if defined(__POLLUX__)
      G_pSysMCUD-> wMEMDQSOUTDELAY  = (wDQS1OUT << 4) | wDQS0OUT;
      G_pSysMCUD-> wMEMDQSINDELAY   = (wDQS1IN  << 4) | wDQS0IN;
#else                           // SPICA
#endif
}
//=============================================================================
HWORD SysSetMCUdSetValue(HWORD wOldValue,HWORD wBitValue,int nMSB,int nLSB)
{
      HWORD wMaskX;

      wMaskX = (((1 << (nMSB - nLSB + 1)) - 1) << nLSB);
      return((wOldValue & ~wMaskX) | (wBitValue << nLSB));		
}
HWORD SysGetMCUdGetValue(HWORD wValue,int nMSB,int nLSB)
{
      HWORD wMaskX;

      wMaskX = (((1 << (nMSB - nLSB + 1)) - 1) << nLSB);
      return((wValue & wMaskX) >> nLSB);
}
//=============================================================================
HWORD SysGetSDRDataWidthByDevice(int nDeviceType)
{
      if (nDeviceType == DEVICE_TYPE_04_3)
          return(MCUD_SDRBW_08BIT);
      return(MCUD_SDRBW_16BIT);
}
HWORD SysGetCasLatencyByDevice(int nDeviceType)
{
      if (nDeviceType == DEVICE_TYPE_04_3)
          return(MCUD_LATENCY_2_0);
      return(MCUD_LATENCY_3_0);
}
HWORD SysGetReadLatencyByDevice(int nDeviceType)
{
#if defined(__POLLUX__)
      if (nDeviceType == DEVICE_TYPE_04_3)
          return(MCUD_RDLAT_2_0);
      return(MCUD_RDLAT_3_0);
#else                           // SPICA
      return(0);
#endif
}
HWORD SysGetRefreshPeriodByDevice(int nDeviceType)
{
#if defined(__POLLUX__)
      if (nDeviceType == DEVICE_TYPE_04_3)
          return(256);
      return(256);
#else                           // SPICA
      return(512);
#endif
}
//=============================================================================

