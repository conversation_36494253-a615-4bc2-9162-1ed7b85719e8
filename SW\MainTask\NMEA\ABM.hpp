#include "Sentence.hpp"

#ifndef __ABM_HPP__
#define __ABM_HPP__
/******************************************************************************
*
* ABM - Addressed Binary and safety related Message
*
* !--ABM,x,x,x,xxxxxxxxx,x,x.x,s--s,x*hh<CR><LF>
*        | | | |         | |   |    |
*        1 2 3 4         5 6   7    8
*
* 1. Total number of sentences needed to transfer the message , 1 to 9
* 2. Sentence number , 1 to 9
* 3. Sequential Message identifier , 0 to 3
* 4. The MMSI of destination AIS unit for the ITU-R M.1371 message
* 5. AIS channel for broadcast of the radio message
* 6. ITU-R M.1371 message ID (6 or 12)
* 7. Encapsulated data
* 8. Number of fill-bits 0 to 5
*
******************************************************************************/
class CAbm : public CSentence {
protected:
	int   m_nTotalNo;		// 1 to 9
	int   m_nSentNo;			// 1 to 9
	int   m_nSeqId;			// 0 to 3
	DWORD m_dMMSI;			
	int   m_nChannel;		
		// 0 = no broadcast channel, 
		// 1 = Broadcast on AIS channel A
		// 2 = Broadcast on AIS channel B
		// 3 = Broadcast two copies of the message
		//     (one -> channel A, another channel B)
	int  m_nMsgID;
	char m_szMessage[61];	// first sentence up to 48, following sentence up to 60
	int  m_nFillBitsNo;		// 0 to 5

public:
    CAbm();
    CAbm(char *pszSentence);

	void  Parse();
	void  SetSentence(char *pszSentence);
	int   GetFormat() { return m_nFormat; }
	void  GetPlainText(char *pszPlainText);
	int   MakeSentence(BYTE *pszSentence);
	
	void  SetTotalNo(int nTotalNo) { m_nTotalNo = nTotalNo; }
	void  SetSentNo(int nSentNo)   { m_nSentNo  = nSentNo;  }
	void  SetSeqID(int nSeqID)     { m_nSeqId   = nSeqID;   }
	void  SetMMSI(DWORD dMMSI)     { m_dMMSI    = dMMSI;    }
	void  SetChannel(int nChannel) { m_nChannel = nChannel; }
	void  SetMsgID(int nMsgID)     { m_nMsgID   = nMsgID;   }
	void  SetMessage(BYTE *pszMsg) { strcpy((char *)m_szMessage,(char *)pszMsg); }
	void  SetFillBitsNo(int nFill) { m_nFillBitsNo = nFill; }

	int   GetTotalNo()    { return m_nTotalNo;    }
	int   GetSentNo()     { return m_nSentNo;     }
	int   GetSeqID()      { return m_nSeqId;      }
	DWORD GetMMSI()       { return m_dMMSI;       }
	int   GetChannel()    { return m_nChannel;   }
	int   GetMsgID()      { return m_nMsgID;      }
	void  GetMessage(char *pszMessage) { strcpy(pszMessage, m_szMessage); }
	int   GetFillBitsNo() { return m_nFillBitsNo; }
};
		
#endif
