#============================================================================
#.                   File Name : makefile.mak
#.
#.                        Date : 2008.05.24
#.
#.                     Version : 1.00
#.
#============================================================================

#============================================================================
CC     = arm-elf-gcc
LD     = arm-elf-ld
OC     = arm-elf-objcopy
OD     = arm-elf-objdump
AR     = arm-elf-ar
RANLIB = arm-elf-ranlib
GCCVER = 4.2.2
#============================================================================

#============================================================================
COMPANY_NAME = __SAMYUNG__
//COMPANY_NAME = __PLASTIMO__
#============================================================================

#============================================================================
//CPU_NAME = __POLLUX__
CPU_NAME = __SPICA__
#============================================================================

#============================================================================
PRODUCT_NAME = __N_5100__
#============================================================================

#============================================================================
FLOAT_MODE = FLOAT_MODE_SOFT
//FLOAT_MODE = FLOAT_MODE_HARD
#============================================================================

#============================================================================
SCR_MODE = __SCR_YYYxZZZ__
#============================================================================

#============================================================================
ENDIAN_MODE = EL
OBJ_FORMAT  = elf32-littlearm
#ENDIAN_MODE = EB
#OBJ_FORMAT  = elf32-bigarm
ifeq ($(ENDIAN_MODE),EB)
  COMP_MODE = big-endian
endif
ifeq ($(ENDIAN_MODE),EL)
  COMP_MODE = little-endian
endif
#============================================================================

#============================================================================
ZIP_LIB     = Zip
FILE_LIB    = File
#============================================================================

#============================================================================
ROOT    = .
SRC_DIR = $(ROOT)
BIN_DIR = $(ROOT)
#============================================================================


#============================================================================
IMAGE_NAME = MainLoader_MKD
IMAGE_BIN  = $(IMAGE_NAME).bin
IMAGE_REC  = $(IMAGE_NAME).rec
IMAGE_ELF  = $(IMAGE_NAME).elf
IMAGE_MAP  = $(IMAGE_NAME).map
IMAGE_DIS  = $(IMAGE_NAME).dis
#============================================================================

#============================================================================
TEXT_BASE  = 0x82000000
#============================================================================

#============================================================================
INCLUDE    = -I ./                                                          \
             -I ./COMLIB/                                                   \
             -I ./DEVLIB/                                                   \
             -I ./SYSLIB/                                                   \
             -I ./FILELIB/                                                  \
             -I ./JPGLIB/                                                   \
             -I /cygdrive/c/GNUARM-4.2.2/Job/F-Project/LIB/FILEX/                 \
             -I /cygdrive/c/GNUARM-4.2.2/Job/F-Project/LIB/FILEX/Include/         \
             -I /cygdrive/c/GNUARM-4.2.2/Job/F-Project/LIB/FILEX/File/            \
             -I /cygdrive/c/GNUARM-4.2.2/arm-elf/include/                         \
             -I /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/$(GCCVER)/include/       \
             -I /cygdrive/c/GNUARM-4.2.2/include/c++/$(GCCVER)/                   \
             -I /cygdrive/c/GNUARM-4.2.2/include/c++/$(GCCVER)/arm-elf/           \
             -I /cygdrive/c/GNUARM-4.2.2/include/c++/$(GCCVER)/bits
#============================================================================

#============================================================================
ifeq ($(FLOAT_MODE),FLOAT_MODE_SOFT)
    LD_LIB_PATH1 = /cygdrive/c/GNUARM-4.2.2/arm-elf/lib
    LD_LIB_PATH2 = /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/$(GCCVER)
    LD_LIB_PATH3 = ./ALLLIB
endif

ifeq ($(FLOAT_MODE),FLOAT_MODE_HARD)
    LD_LIB_PATH1 = /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/fpu
    LD_LIB_PATH2 = /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/$(GCCVER)/fpu
    LD_LIB_PATH3 = ./ALLLIB
endif
#============================================================================

#============================================================================
ifeq ($(FLOAT_MODE),FLOAT_MODE_SOFT)
    C_FLAGS   = -O2 -Wall -nostdinc  -msoft-float -m$(COMP_MODE) -static    \
                -fomit-frame-pointer -fPIC                                  \
                -mcpu=arm1176jzf-s   -mfpu=fpa                              \
                -funsigned-char                                             \
                -D$(COMPANY_NAME)                                           \
                -D$(CPU_NAME)                                               \
                -D$(PRODUCT_NAME)                                           \
                -D$(SCR_MODE)                                               \
                -D$(FLOAT_MODE)                                             \
                -DNAND_BLOCK_SMALL=0                                        \
                -DNAND_BLOCK_LARGE=1                                        \
                -DNAND_BLOCK_TYPE=1                                         \
                -D__N500_MODEL__                                            \
                -D$(ENDIAN_MODE) $(INCLUDE)
endif

ifeq ($(FLOAT_MODE),FLOAT_MODE_HARD)
    C_FLAGS   = -O2 -Wall -nostdinc  -mhard-float -m$(COMP_MODE) -static    \
                -fomit-frame-pointer -fPIC                                  \
                -mcpu=arm1176jzf-s   -mfpu=fpe3                             \
                -funsigned-char                                             \
                -D$(COMPANY_NAME)                                           \
                -D$(CPU_NAME)                                               \
                -D$(PRODUCT_NAME)                                           \
                -D$(SCR_MODE)                                               \
                -D$(FLOAT_MODE)                                             \
                -DNAND_BLOCK_SMALL=0                                        \
                -DNAND_BLOCK_LARGE=1                                        \
                -DNAND_BLOCK_TYPE=1                                         \
                -D__N500_MODEL__                                            \
                -D$(ENDIAN_MODE) $(INCLUDE)
endif

CPP_FLAGS = $(C_FLAGS)
#============================================================================

#============================================================================
LD_SCRIPT = $(ROOT)/link_SPICA.xn
LD_OPTION = -o $(IMAGE_ELF) -Map $(IMAGE_MAP) --oformat $(OBJ_FORMAT)  \
            -T $(LD_SCRIPT)                                            \
            -L $(LD_LIB_PATH1)                                         \
            -L $(LD_LIB_PATH2)                                         \
            -L $(LD_LIB_PATH3)                                         \
            -$(ENDIAN_MODE)  -static                                   \
            --wrap malloc                                              \
            --wrap free                                                \
            --wrap calloc                                              \
            --wrap abort                                               \
            --wrap close                                               \
            --wrap fstat                                               \
            --wrap isatty                                              \
            --wrap lseek                                               \
            --wrap read                                                \
            --wrap sbrk                                                \
            --wrap write                                               \
            --wrap fputc                                               \
            --wrap fputs                                               \
            --wrap printf                                              \
            --wrap _open_r                                             \
            --wrap _close_r                                            \
            --wrap _fstat_r                                            \
            --wrap _lseek_r                                            \
            --wrap _read_r                                             \
            --wrap _sbrk_r                                             \
            --wrap _write_r

ifeq ($(ENDIAN_MODE),EB)
   LD_LIBS = -lstdc++ -lm -lc -lgcc -l$(ZIP_LIB) -l$(FILE_LIB) -lstdc++ -lm -lc -lgcc
endif
ifeq ($(ENDIAN_MODE),EL)
   LD_LIBS = -lstdc++ -lm -lc -lgcc -l$(ZIP_LIB) -l$(FILE_LIB) -lstdc++ -lm -lc -lgcc
endif
#============================================================================

#============================================================================
# Files to be compiled
#============================================================================

OBJS =  start.o            \
        COMLIB/ComLib.o    \
        SYSLIB/SysLib.o    \
        SYSLIB/SysIntr.o   \
        DEVLIB/Nand64.o    \
        DEVLIB/SysMMU.o    \
        DEVLIB/SysPLL.o    \
        DEVLIB/SysMCUD.o   \
        DEVLIB/SysMCUS.o   \
        DEVLIB/SysGPIO.o   \
        DEVLIB/SysAlive.o  \
        DEVLIB/SysTimer.o  \
        DEVLIB/SysUART.o   \
        DEVLIB/SysMLC.o    \
        DEVLIB/SysDPC.o    \
        DEVLIB/SysLCD.o    \
        DEVLIB/SysPWM.o    \
        DEVLIB/SysSDCD.o   \
        DEVLIB/SdCardDRV.o \
        DEVLIB/DataBack.o  \
        FILELIB/osfile.o   \
        JPGLIB/jpegdecr.o  \
        JPGLIB/jpegidct.o  \
        JPGLIB/jpegh2v2.o  \
        main.o


.c.o:
	$(CC) -c $(C_FLAGS) $< -o $@

.cpp.o:
	$(CC) -c $(CPP_FLAGS) $< -o $@

.S.o:
	$(CC) -c $(C_FLAGS) -D_ASSEMBLER_ $< -o $@

BASE_HDR = CpuAddr.h     \
           DataType.h

start.o              : start.S
COMLIB/ComLib.o      : $(BASE_HDR) COMLIB/ComLib.h     COMLIB/ComLib.c
SYSLIB/SysLib.o      : $(BASE_HDR) SYSLIB/SysLib.h     SYSLIB/SysLib.c
SYSLIB/SysIntr.o     : $(BASE_HDR) SYSLIB/SysIntr.h    SYSLIB/SysIntr.c
DEVLIB/Nand64.o      : $(BASE_HDR) DEVLIB/Nand64.h     DEVLIB/Nand64.c
DEVLIB/SysMMU.o      : $(BASE_HDR) DEVLIB/SysMMU.h     DEVLIB/SysMMU.c
DEVLIB/SysPLL.o      : $(BASE_HDR) DEVLIB/SysPLL.h     DEVLIB/SysPLL.c
DEVLIB/SysMCUD.o     : $(BASE_HDR) DEVLIB/SysMCUD.h    DEVLIB/SysMCUD.c
DEVLIB/SysMCUS.o     : $(BASE_HDR) DEVLIB/SysMCUS.h    DEVLIB/SysMCUS.c
DEVLIB/SysGPIO.o     : $(BASE_HDR) DEVLIB/SysGPIO.h    DEVLIB/SysGPIO.c
DEVLIB/SysAlive.o    : $(BASE_HDR) DEVLIB/SysAlive.h   DEVLIB/SysAlive.c
DEVLIB/SysStatic.o   : $(BASE_HDR) DEVLIB/SysStatic.h  DEVLIB/SysStatic.c
DEVLIB/SysTimer.o    : $(BASE_HDR) DEVLIB/SysTimer.h   DEVLIB/SysTimer.c
DEVLIB/SysUART.o     : $(BASE_HDR) DEVLIB/SysUART.h    DEVLIB/SysUART.c
DEVLIB/SysMLC.o      : $(BASE_HDR) DEVLIB/SysMLC.h     DEVLIB/SysMLC.c
DEVLIB/SysDPC.o      : $(BASE_HDR) DEVLIB/SysDPC.h     DEVLIB/SysDPC.c
DEVLIB/SysLCD.o      : $(BASE_HDR) DEVLIB/SysLCD.h     DEVLIB/SysLCD.c
DEVLIB/SysPWM.o      : $(BASE_HDR) DEVLIB/SysPWM.h     DEVLIB/SysPWM.c
DEVLIB/SysSDCD.o     : $(BASE_HDR) DEVLIB/SysSDCD.h    DEVLIB/SysSDCD.c
DEVLIB/SdCardDRV.o   : $(BASE_HDR) DEVLIB/SdCardDRV.h  DEVLIB/SdCardDRV.c
DEVLIB/DataBack.o    : $(BASE_HDR) DEVLIB/DataBack.h   DEVLIB/DataBack.c
FILELIB/osfile.o     : $(BASE_HDR) FILELIB/osfile.h    FILELIB/osfile.c
JPGLIB/jpegdecr.o    : $(BASE_HDR) JPGLIB/jpegdecr.hpp JPGLIB/jpegdecr.cpp
JPGLIB/jpegidct.o    : $(BASE_HDR) JPGLIB/jpegdecr.hpp JPGLIB/jpegidct.cpp
JPGLIB/jpegh2v2.o    : $(BASE_HDR) JPGLIB/jpegdecr.hpp JPGLIB/jpegh2v2.cpp
main.o               : $(BASE_HDR) main.hpp            main.cpp


#C_FILES   = $(wildcard *.c)
#ASM_FILES = $(wildcard *.S)
#CPP_FILES = $(wildcard *.cpp)
#OBJ_FILES = $(ASM_FILES:%.S=%.o) $(C_FILES:%.c=%.o) $(CPP_FILES:%.cpp=%.o)

#============================================================================
# Rules
#============================================================================

.PHONY : all

rebuild: clean all

all: $(OBJS)
	$(LD) $(LD_OPTION) $(LD_LIBS) $(OBJS) $(LD_LIBS)
	$(OC) -O binary $(IMAGE_ELF) $(IMAGE_BIN)
	$(OD) -d $(IMAGE_ELF) >$(IMAGE_DIS)
#	BACK_CRC $(IMAGE_BIN)
#	$(OC) -O srec $(IMAGE_ELF) $(IMAGE_REC)

.PHONY : clean
clean :
	rm -f *.o
	rm -f COMLIB/*.o
	rm -f SYSLIB/*.o
	rm -f DEVLIB/*.o
	rm -f FILELIB/*.o
	rm -f JPGLIB/*.o
	rm -f *.map
	rm -f *.elf
	rm -f *.bin
	rm -f *.rec
	rm -f *.dis

