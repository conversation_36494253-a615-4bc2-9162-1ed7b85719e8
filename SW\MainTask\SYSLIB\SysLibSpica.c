/*...........................................................................*/
/*.                  File Name : SYSLIB.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysTimer.h"
#include "SysMLC.h"
#include "SysALIVE.h"
#include "SysGPIO.h"
#include "SysPWM.h"
#include "SysLibSpica.h"
#include "AllConst.h"

#include <math.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

//========================================================================
static MEMBLOCK G_AllMemBlock;
//========================================================================
static int      G_nCpuType    = CPU_TYPE_POLLUX;
static int      G_nDeviceType = DEVICE_TYPE_04_3;
static int      G_nFpgaType   = FPGA_TYPE_NONE;
static int      G_nNavisModelType = NAVIS_TYPE_800;
static int      G_nSGP330ScrnResMode = SGP330_SCRN_MODE_640x480;
static int      G_nNavis5100AModelMode = 0;
//========================================================================
volatile DWORD  G_dSystemTickCounter = 0;
//========================================================================
static xSYSDATE G_xUTCDate = {SYS_TIME_UNKNOWN, 1, 1};
static xSYSTIME G_xUTCTime = { 0, 0, 0};
static xSYSDATE G_xLOCDate = {SYS_TIME_UNKNOWN, 1, 1};
static xSYSTIME G_xLOCTime = { 0, 0, 0};
//========================================================================
static int      G_nPowerMask  = 0;
//========================================================================
int    G_nSysAlphaBlendPrcnt= 100;
UCHAR  G_cSysAlphaBlendValue= 0xff;
DWORD  G_dSysAlphaBlendValue= 0xff000000;
int    G_nSysBrightPercntVal= 100;        //  10, 20, 30, 40, 50, 60, 70, 80, 90,100
int    G_nSysBrightFactorVal= 256;        //  51, 77, 90,102,115,128,154,179,205,256
UCHAR  G_vSysBrightFactorTbl[256] = {0,};
//========================================================================
UCHAR *G_pSysPaletteDataUCHAR = NULL;
HWORD *G_pSysPaletteDataHWORD = NULL;
int    G_nSysPaletteDataSize  = 0;
UCHAR  G_vMapPaletteDataUCHAR[256 * 3] = {0x00,};
HWORD  G_vMapPaletteDataHWORD[256 * 1] = {0x00,};
CLRRADAR G_vRadarNormColorTable[256]   = {0x00,};
CLRRADAR G_vRadarOverColorTable[256]   = {0x00,};
DWORD  G_dRadarOverTransparecyValue = 0;
//========================================================================
static int   G_nGlobalUsingMap      = USING_MAP_CMAP;
static int   G_nSamMapTypeMode      = SAM_MAP_TYPE_MODE_NPC;
static int   G_nSamMapNewPecFound   = 0;
static int   G_nSamMapKoreaZone     = 0;
//------------------------------------------------------------------------
static int   G_nGlobalLangCode      = LNG_CODE_ENG;
//------------------------------------------------------------------------
static int   G_nGlobalNightMode     = MODE_VAL_OFF;
static int   G_nGlobalChartBodyMode = CHART_PAL_MODE_NORMAL;
static int   G_nGlobalMenuBackColor = MENU_BACK_COLOR_BLUE;
//------------------------------------------------------------------------
static int   G_nSysSonarMode        = MODE_VAL_OFF;   // MODE_VAL_OFF,...,MODE_VAL_ON
static int   G_nSysRadarMode        = MODE_VAL_OFF;   // MODE_VAL_OFF,...,MODE_VAL_ON
static int   G_nSysVideoMode        = MODE_VAL_OFF;   // MODE_VAL_OFF,...,MODE_VAL_ON
static int   G_nSysCanHaveVideoMode = MODE_VAL_OFF;   // MODE_VAL_OFF,...,MODE_VAL_ON
//========================================================================
static int   G_nSdCardLastChangeStatus = 0;
//========================================================================
static int   G_nKeyDataGetEnabledMode  = 1;
//========================================================================
int    FPGA_MUL_FACTOR = 1;
//========================================================================
static int   G_nSonarHwPcbVersion   = SONAR_HW_PCB_VER_OLD;
//========================================================================

//========================================================================
//#if  defined(__NAVIONICS__)
#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
     //#define  __USE_FIXED_MEM__        // may be defined in the make-file.

     #define  __FIXED_MEM_SIZE_0256__          256 
     #define  __FIXED_MEM_SIZE_0512__          512 
     #define  __FIXED_MEM_SIZE_1024__         1024 

     #define  __FIXED_MEM_COUNT_0256__        4096 
     #define  __FIXED_MEM_COUNT_0512__        2048 
     #define  __FIXED_MEM_COUNT_1024__        1024 

     #if defined(__USE_FIXED_MEM__)
         static DWORD     G_dFixedStartAddrX = 0;
         static int       G_nFixedMemoryMode = 0;
         static FIXEDMEM  G_xFixed0256Memory = {0,};
         static FIXEDMEM  G_xFixed0512Memory = {0,};
         static FIXEDMEM  G_xFixed1024Memory = {0,};
     #endif
#endif

//========================================================================

//========================================================================
//#define __MEM_BLOCK_ERROR_CHECK__

#if defined(__MEM_BLOCK_ERROR_CHECK__)
    static int G_nRunDebugCounterX = 0;
#endif
//========================================================================

//========================================================================
void  SysInitFreeMemory(void)
{
#if defined(__USE_FIXED_MEM__)
      static UCHAR  vUsedState0256[__FIXED_MEM_COUNT_0256__];
      static UCHAR  vUsedState0512[__FIXED_MEM_COUNT_0512__];
      static UCHAR  vUsedState1024[__FIXED_MEM_COUNT_1024__];

//    static UCHAR  vFreeMemory0256[__FIXED_MEM_COUNT_0256__ * __FIXED_MEM_SIZE_0256__ + 64];
//    static UCHAR  vFreeMemory0512[__FIXED_MEM_COUNT_0512__ * __FIXED_MEM_SIZE_0512__ + 64];
//    static UCHAR  vFreeMemory1024[__FIXED_MEM_COUNT_1024__ * __FIXED_MEM_SIZE_1024__ + 64];

      static UCHAR  vFreeMemoryAllX[__FIXED_MEM_COUNT_0256__ * __FIXED_MEM_SIZE_0256__ + 64 +
                                    __FIXED_MEM_COUNT_0512__ * __FIXED_MEM_SIZE_0512__ + 64 +
                                    __FIXED_MEM_COUNT_1024__ * __FIXED_MEM_SIZE_1024__ + 64];
#endif

      DWORD dBuffer;
      DWORD dSize;
      MEMLINK *pBuffer;

#if defined(__USE_FIXED_MEM__)
      G_xFixed0256Memory.nBlockSize  = __FIXED_MEM_SIZE_0256__;
      G_xFixed0256Memory.nFullCount  = __FIXED_MEM_COUNT_0256__;
      G_xFixed0256Memory.nFreeCount  = __FIXED_MEM_COUNT_0256__;
      G_xFixed0256Memory.nLastUsedNo = 0;
      G_xFixed0256Memory.nLastFreeNo =-1;
      G_xFixed0256Memory.pUsedState  = &vUsedState0256[0];
//    G_xFixed0256Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemory0256[0] + 0x0f) & 0xfffffff0);
      G_xFixed0256Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemoryAllX[0] + 0x0f) & 0xfffffff0);

      G_dFixedStartAddrX = (DWORD)G_xFixed0256Memory.pFreeMemory;

      G_xFixed0512Memory.nBlockSize  = __FIXED_MEM_SIZE_0512__;
      G_xFixed0512Memory.nFullCount  = __FIXED_MEM_COUNT_0512__;
      G_xFixed0512Memory.nFreeCount  = __FIXED_MEM_COUNT_0512__;
      G_xFixed0512Memory.nLastUsedNo = 0;
      G_xFixed0512Memory.nLastFreeNo =-1;
      G_xFixed0512Memory.pUsedState  = &vUsedState0512[0];
//    G_xFixed0512Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemory0512[0] + 0x0f) & 0xfffffff0);
      G_xFixed0512Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemoryAllX[__FIXED_MEM_COUNT_0256__ * __FIXED_MEM_SIZE_0256__ + 64] + 0x0f) & 0xfffffff0);

      G_xFixed1024Memory.nBlockSize  = __FIXED_MEM_SIZE_1024__;
      G_xFixed1024Memory.nFullCount  = __FIXED_MEM_COUNT_1024__;
      G_xFixed1024Memory.nFreeCount  = __FIXED_MEM_COUNT_1024__;
      G_xFixed1024Memory.nLastUsedNo = 0;
      G_xFixed1024Memory.nLastFreeNo =-1;
      G_xFixed1024Memory.pUsedState  = &vUsedState1024[0];
//    G_xFixed1024Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemory1024[0] + 0x0f) & 0xfffffff0);
      G_xFixed1024Memory.pFreeMemory = (UCHAR *)(((DWORD)&vFreeMemoryAllX[__FIXED_MEM_COUNT_0256__ * __FIXED_MEM_SIZE_0256__ + 64 + __FIXED_MEM_COUNT_0512__ * __FIXED_MEM_SIZE_0512__ + 64] + 0x0f) & 0xfffffff0);

      memset(G_xFixed0256Memory.pUsedState,0x00,__FIXED_MEM_COUNT_0256__ * sizeof(G_xFixed0256Memory.pUsedState[0]));
      memset(G_xFixed0512Memory.pUsedState,0x00,__FIXED_MEM_COUNT_0512__ * sizeof(G_xFixed0512Memory.pUsedState[0]));
      memset(G_xFixed1024Memory.pUsedState,0x00,__FIXED_MEM_COUNT_1024__ * sizeof(G_xFixed1024Memory.pUsedState[0]));
#endif


      dBuffer = SysGetStartOfFreeMemory();
      dSize = RAM_TOTAL_SIZE - 4 * 1024;
      dSize = dSize - (dBuffer - RAM_START_ADDRESS);

      G_AllMemBlock.pStart = (void *)dBuffer;
      G_AllMemBlock.dSize  = dSize;

      if ((dBuffer % MEMORY_ALIGN) != 0)
         {
          dSize -= (MEMORY_ALIGN - dBuffer % MEMORY_ALIGN);
          dBuffer = dBuffer + (MEMORY_ALIGN - dBuffer % MEMORY_ALIGN);
         }

      if ((dSize % MEMORY_ALIGN) != 0)
          dSize -= (dSize % MEMORY_ALIGN);

      pBuffer = (MEMLINK *)dBuffer;

      pBuffer->pPrev = NULL;
      pBuffer->pNext = NULL;
      pBuffer->dSize = dSize - MEMLINK_SIZE;
      pBuffer->wFixed= 1;
      pBuffer->wUsed = 0;

      G_AllMemBlock.pMemLink = pBuffer;
}
#if  defined(__NAVIONICS__)
void  SysRunCTORS(void)
{
      extern DWORD _CTORS_START;
      extern DWORD _CTORS_END;
      DWORD  *pStartAddr;
      int    nSize;
      void (*pRunPtr)(void);

      nSize = (_CTORS_END - _CTORS_START) / 4;

      pStartAddr = (DWORD *)_CTORS_START;

      while (nSize > 0)
            {
             pRunPtr = (void (*)(void))*pStartAddr;
             pRunPtr();

             ++pStartAddr;
             --nSize;
            }
}
#endif
DWORD SysGetAllFreeMemorySize(void)
{
      MEMLINK *pLink;
      DWORD  dTotalMem;

      dTotalMem = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if (!pLink->wUsed)
                 dTotalMem += pLink->dSize;

             pLink = pLink->pNext;
            }

      return(dTotalMem);
}
DWORD SysGetMaxFreeMemorySize(void)
{
      MEMLINK *pLink;
      DWORD  dMaxMem;

      dMaxMem = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if (!pLink->wUsed)
                 if (dMaxMem < pLink->dSize)
                     dMaxMem = pLink->dSize;

             pLink = pLink->pNext;
            }

      return(dMaxMem);
}
int   SysMakeMemoryTree(DWORD *pMemTree)
{
      MEMLINK *pLink;
      int   nNodeSize;

      nNodeSize = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             *pMemTree++ = (DWORD)pLink->pNext;
             *pMemTree++ = (DWORD)pLink->pPrev;
             *pMemTree++ = (DWORD)pLink->dSize;
             *pMemTree++ = (DWORD)pLink->wUsed;

             pLink = pLink->pNext;

             ++nNodeSize;
            }

      return(nNodeSize);
}
int   SysCheckMemoryTree(DWORD *pErrorMem,MEMLINK *pMemLink)
{
      MEMLINK *pLink;
      DWORD dStartMem;
      DWORD dLastMem;
      DWORD dPrev,dNext,dLink;
      int   nError;
#if defined(__USE_RTOS__)
      DWORD dStatusReg;
#endif

#if defined(__USE_RTOS__)
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif

      dStartMem = (DWORD)G_AllMemBlock.pStart;
      dLastMem  = (DWORD)G_AllMemBlock.pStart + G_AllMemBlock.dSize;

      nError = 0;
      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             dPrev = (DWORD)(pLink->pPrev);
             dNext = (DWORD)(pLink->pNext);
             dLink = (DWORD)(pLink);

             if ((dNext != 0 && (dNext < dStartMem || dNext >= dLastMem)) ||
                 (dPrev != 0 && (dPrev < dStartMem || dPrev >= dLastMem)) ||
                 (dLink != 0 && (dLink < dStartMem || dLink >= dLastMem)))
                {
                 *pErrorMem = (DWORD)pLink;
                 *pMemLink  = *pLink;
                 nError = 1;
                 break;
                }

             if ((dPrev & 0x0000000f) ||
                 (dNext & 0x0000000f) ||
                 (dLink & 0x0000000f))
                {
                 *pErrorMem = (DWORD)pLink;
                 *pMemLink  = *pLink;
                 nError = 2;
                 break;
                }

             pLink = pLink->pNext;
            }

#if defined(__USE_RTOS__)
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif

      return(nError);
}
int   SysGetNumberOfFragment(void)
{
      MEMLINK *pLink;
      int   nFragCnt;

      nFragCnt = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if (pLink->wUsed == 0)
                 ++nFragCnt;

             pLink = pLink->pNext;
            }

      return(nFragCnt);
}
DWORD SysGetLastFreeAddress(DWORD *pBlockSize)
{
      MEMLINK *pLink;
      DWORD  dLastAddr = 0;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             dLastAddr   = (DWORD)pLink;
             *pBlockSize = pLink->dSize;

             pLink = pLink->pNext;
            }

      return(dLastAddr);
}
int   SysRemoveMemFragment(void)
{
      return(0);
}
//========================================================================
//#if  defined(__NAVIONICS__)
#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
DWORD SysGetFixedMemorySize(void)
{
      return(__FIXED_MEM_SIZE_0256__ * __FIXED_MEM_COUNT_0256__ +
             __FIXED_MEM_SIZE_0512__ * __FIXED_MEM_COUNT_0512__ +
             __FIXED_MEM_SIZE_1024__ * __FIXED_MEM_COUNT_1024__);
}
DWORD SysGetFixedStartAddrX(void)
{
#if defined(__USE_FIXED_MEM__)
      return(G_dFixedStartAddrX);
#endif
}
int   SysGetFixedMemoryEnable(void)
{
#if defined(__USE_FIXED_MEM__)
      return(G_nFixedMemoryMode);
#endif
}
void  SysSetFixedMemoryEnable(int nMode)
{
#if defined(__USE_FIXED_MEM__)
      G_nFixedMemoryMode = nMode;
#endif
}
void  *MallocFixedMemory(FIXEDMEM *pFixedMemory,int nSize)
{
      int  nTempX;

      if (pFixedMemory->nBlockSize <= nSize)
          return(NULL);

      if (pFixedMemory->nFreeCount > 0)
         {
          if (pFixedMemory->nLastFreeNo != -1)
             {
              nTempX = pFixedMemory->nLastFreeNo;

              if (pFixedMemory->pUsedState[nTempX] == 0)
                 {
                  pFixedMemory->pUsedState[nTempX] = 1;

                  --pFixedMemory->nFreeCount;

                  return(pFixedMemory->pFreeMemory + nTempX * pFixedMemory->nBlockSize);
                 }
             }

          if (pFixedMemory->nLastUsedNo > 0)
             {
              nTempX = pFixedMemory->nLastUsedNo - 1;

              if (pFixedMemory->pUsedState[nTempX] == 0)
                 {
                  pFixedMemory->pUsedState[nTempX] = 1;

                  --pFixedMemory->nLastUsedNo;
                  --pFixedMemory->nFreeCount;

                  return(pFixedMemory->pFreeMemory + nTempX * pFixedMemory->nBlockSize);
                 }
             }

          nTempX = pFixedMemory->nLastUsedNo;

          while (1)
                {
                 if (pFixedMemory->pUsedState[nTempX] == 0)
                    {
                     pFixedMemory->pUsedState[nTempX] = 1;

                     pFixedMemory->nLastUsedNo = nTempX;
                     --pFixedMemory->nFreeCount;

                     return(pFixedMemory->pFreeMemory + nTempX * pFixedMemory->nBlockSize);
                    }

                 ++nTempX;
                 if (nTempX >= pFixedMemory->nFullCount)
                     nTempX = 0;
                }
         }

      return(NULL);
}
int   FreeFixedMemory(FIXEDMEM *pFixedMemory,void *pMemAddr)
{
      DWORD dAddrX;
      int   nTempX;

      dAddrX = (DWORD)pFixedMemory->pFreeMemory + pFixedMemory->nBlockSize * pFixedMemory->nFullCount;

      if ((DWORD)pMemAddr < (DWORD)pFixedMemory->pFreeMemory || (DWORD)pMemAddr >= dAddrX)
          return(-1);

      dAddrX = (DWORD)pMemAddr - (DWORD)pFixedMemory->pFreeMemory;
      nTempX = dAddrX / pFixedMemory->nBlockSize;

      if (nTempX >= 0 && nTempX < pFixedMemory->nFullCount)
         {
          if (pFixedMemory->pUsedState[nTempX] != 0)
             {
              pFixedMemory->pUsedState[nTempX] = 0;

              ++pFixedMemory->nFreeCount;

              pFixedMemory->nLastFreeNo = nTempX;
             }

          return(nTempX);
         }

      return(-1);
}
#endif
//========================================================================
void  *__wrap_malloc(DWORD dSize)
{
      MEMLINK *pLink;
      MEMLINK *pTemp;
#if defined(__USE_RTOS__)
      DWORD dStatusReg;
#endif

#if defined(__USE_RTOS__)
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif

#if defined(__USE_FIXED_MEM__)
    void  *pMemX;

    if (G_nFixedMemoryMode)
       {
        pMemX = MallocFixedMemory(&G_xFixed0256Memory,dSize);
        if (pMemX != NULL)
           {
        #if defined(__USE_RTOS__)
            SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
        #endif

            return(pMemX);
           }

        pMemX = MallocFixedMemory(&G_xFixed0512Memory,dSize);
        if (pMemX != NULL)
           {
        #if defined(__USE_RTOS__)
            SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
        #endif

            return(pMemX);
           }

        pMemX = MallocFixedMemory(&G_xFixed1024Memory,dSize);
        if (pMemX != NULL)
           {
        #if defined(__USE_RTOS__)
            SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
        #endif

            return(pMemX);
           }
       }
#endif

      dSize = (dSize + MEMORY_ALIGN - 1) / MEMORY_ALIGN * MEMORY_ALIGN;
      if (dSize < MEMORY_ALIGN)
          dSize = MEMORY_ALIGN;

      pLink = G_AllMemBlock.pMemLink;

      while (pLink != NULL)
            {
             if ((pLink->dSize >= dSize) && !pLink->wUsed)
                {
                 if ((pLink->dSize - dSize) > MEMLINK_SIZE)
                    {
                     pTemp = (MEMLINK *)((char *)pLink + MEMLINK_SIZE + dSize);
                     pTemp->pNext = pLink->pNext;
                     pTemp->pPrev = pLink;
                     pLink->pNext = pTemp;
                     pTemp->wFixed= 0;
                     pTemp->dSize = pLink->dSize - dSize - MEMLINK_SIZE;
                     pTemp->wUsed = 0;
                     pLink->dSize = dSize;
                    }

                 pLink->wUsed = 1;
                 break;
                }

             pLink = pLink->pNext;
            }

#if defined(__USE_RTOS__)
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif

      if (pLink == NULL)
          return(NULL);

      return((char *)pLink + MEMLINK_SIZE);
}
void  __wrap_free(void *pMemAddr)
{
      MEMLINK *pLink;
      MEMLINK *pTemp;
      MEMLINK *pPrev;
#if defined(__USE_RTOS__)
      DWORD dStatusReg;
#endif

#if defined(__USE_RTOS__)
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif

#if defined(__USE_FIXED_MEM__)
    if (G_nFixedMemoryMode)
       {
        if (FreeFixedMemory(&G_xFixed0256Memory,pMemAddr) >= 0)
           {
        #if defined(__USE_RTOS__)
            SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
        #endif

            return;
           }

        if (FreeFixedMemory(&G_xFixed0512Memory,pMemAddr) >= 0)
           {
        #if defined(__USE_RTOS__)
            SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
        #endif

            return;
           }

        if (FreeFixedMemory(&G_xFixed1024Memory,pMemAddr) >= 0)
           {
        #if defined(__USE_RTOS__)
            SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
        #endif

            return;
           }
       }
#endif

      pMemAddr = (char *)pMemAddr - MEMLINK_SIZE;
      pLink = G_AllMemBlock.pMemLink;
      pPrev = NULL;
      while (pLink != NULL)
            {
             if (pLink == pMemAddr)
                {
                 pLink->wUsed = 0;
                 pTemp = pLink->pNext;
                 if ((pTemp != NULL) && !pTemp->wUsed && !pTemp->wFixed)
                    {
                     pLink->dSize += pTemp->dSize + MEMLINK_SIZE;
                     pLink->pNext = pTemp->pNext;
                    }
                 if (!pLink->wFixed && (pPrev != NULL) && !pPrev->wUsed)
                    {
                     pPrev->dSize += pLink->dSize + MEMLINK_SIZE;
                     pPrev->pNext = pLink->pNext;
                    }
                 break;
                }
             pPrev = pLink;
             pLink = pLink->pNext;
            }
#if defined(__USE_RTOS__)
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif
}
void  *__wrap_calloc(DWORD nItems,DWORD nSize)
{
      void *pMem;

      pMem = malloc(nItems * nSize);
      if (pMem != NULL)
          memset(pMem,0x00,nItems * nSize);
      return(pMem);
}
void  __wrap_abort(void)
{
      while (1);
}
int   __wrap_open(char *Path,int Flags)
{
      return(-1);
}
int   __wrap_close(int file)
{
      return(-1);
}
int   __wrap_fstat(int file,struct stat *st)
{
      st->st_mode = S_IFCHR;
      return(0);
}
int   __wrap_isatty(int file)
{
      return(1);
}
int   __wrap_lseek(int file,int ptr,int dir)
{
      return(0);
}
int   __wrap_read(int file,char *ptr,int len)
{
      return(0);
}
caddr_t __wrap_sbrk(int incr)
{
      static char *heap_end = NULL;
      char *prev_heap_end;
#if defined(__USE_RTOS__)
      DWORD dStatusReg;
#endif

#if defined(__USE_RTOS__)
      dStatusReg = SysSaveStatusRegInCPU();    // disable interrupt
#endif
      incr = incr + 15;
      incr = incr & 0xfffffff0;
      if (heap_end == NULL)
          heap_end = (char *)SysGetStartOfHeapMemory();
      prev_heap_end = heap_end;
      if ((heap_end + incr) >= (char *)SysGetLastOfHeapMemory())
          abort();
      heap_end += incr;
#if defined(__USE_RTOS__)
      SysRestStatusRegInCPU(dStatusReg);       // restore interrupt
#endif
      return((caddr_t)prev_heap_end);
}
int   __wrap_write(int file,char *ptr,int len)
{
      return(len);
}
int   __wrap_fputc(int character,void *stream)
{
      return(0);
}
int   __wrap_fputs(const char *str,void *stream)
{
      return(0);
}
int   __wrap_puts(const char *str)
{
      __wrap_printf(str);
      return(0);
}
int   __wrap_printf(const char *format,...)
{
#if defined(__NAVIONICS_DEBUG_MODE__)
      #define __DEBUG_MEM_BASE_ADDR__     (0x86800000)
      static  UCHAR *pMem = (UCHAR *)(__DEBUG_MEM_BASE_ADDR__ + 0x04);
      char    vTemp[1024];
      va_list xArgPtr;
      int     nRetVal, nStrLen;
      extern  void  WriteStrToDebugPort(char *pDebugStr);

      va_start(xArgPtr, format);
      nRetVal = vsprintf(vTemp, format, xArgPtr);
      va_end(xArgPtr);
      nStrLen = strlen(vTemp);

//    memmove(pMem, vTemp, nStrLen);
//    pMem += nStrLen;
//    *(DWORD *)(__DEBUG_MEM_BASE_ADDR__) = (DWORD)pMem;

      WriteStrToDebugPort(vTemp);

      return(nRetVal);
#endif

    return(0);
}
//========================================================================
int   __wrap__open_r(void *REENT,const char *file,int flags,int mode)
{
      return(0);
}
int   __wrap__close_r(void *REENT,int FD)
{
      return(0);
}
int   __wrap__fstat_r(void *REENT,int FD,struct stat *PSTAT)
{
      PSTAT->st_mode = S_IFCHR;
      return(0);
}
off_t __wrap__lseek_r(void *REENT,int FD,off_t POS,int WHENCE)
{
      return(0);
}
long  __wrap__read_r(void *REENT,int FD,void *BUF,size_t CNT)
{
      return(0);
}
char *__wrap__sbrk_r(void *REENT, size_t INCR)
{
      return(__wrap_sbrk(INCR));
}
long  __wrap__write_r(void *REENT,int FD, const void *BUF, size_t CNT)
{
      return(CNT);
}
//========================================================================
void  SysCheckCpuType(void)
{
      SysSetCpuType(CPU_TYPE_SPICA);
}
void  SysSetCpuType(int nCpuType)
{
      G_nCpuType = nCpuType;
}
int   SysGetCpuType(void)
{
      return(G_nCpuType);
}
void  SysCheckDeviceType(void)
{
//    SysSetDeviceType(DEVICE_TYPE_NAVIS);
      SysSetDeviceType(DEVICE_TYPE_06_5);
}
void  SysSetDeviceType(int nDeviceType)
{
      G_nDeviceType = nDeviceType;
}
int   SysGetDeviceType(void)
{
      return(G_nDeviceType);
}
void  SysCheckNavisModelType(void)
{
      DWORD dMaskX;
      DWORD dTempC;
      DWORD dTempX;
      DWORD dTempY;
      DWORD dTempZ;
      int   nTypeX;
      xSYS_GPIO *pSysGPIO = (xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR;
      xSYS_GPIO *pSysCNFG = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

  #if defined(__N500_MODEL__)
      SysSetNavisModelType(NAVIS_TYPE_700);
      return;
  #endif

      if (pSysCNFG->dPAD & (1 << 17))
         {
          SysSetNavisModelType(NAVIS_TYPE_330);
          return;
         }

      dMaskX = (1 << 10) | (1 <<  9) | (1 <<  8);

      pSysGPIO->dOUTENB &= (~dMaskX);     // input

      SysDelayMiliSec(5);

      dTempC = 0;
      dTempZ = 0;
      dTempY = 0;
      while (dTempC < 10000)
            {
             dTempX = (pSysGPIO->dPAD >> 8) & 0x00000007;

             if (dTempX == dTempY)
                 ++dTempZ;
             else
                {
                 dTempZ = 0;
                 dTempY = dTempX;
                }

             if (dTempZ >= 500)
                 break;

             ++dTempC;
            }

      if (dTempZ < 500)
          dTempX = 6;

      nTypeX = NAVIS_TYPE_700;

      if (dTempX == 1)  nTypeX = NAVIS_TYPE_700;
      if (dTempX == 2)  nTypeX = NAVIS_TYPE_800;
      if (dTempX == 3)
         {
          if (pSysCNFG->dPAD & (1 << 19))
              SysSetNavis5100AModelMode(1);

          nTypeX = NAVIS_TYPE_5100;
         }
      if (dTempX == 4)  nTypeX = NAVIS_TYPE_1200;
      if (dTempX == 5)  nTypeX = NAVIS_TYPE_3800;
//    if (dTempX == 6)  nTypeX = NAVIS_TYPE_330;

      SysSetNavisModelType(nTypeX);

      pSysGPIO->dOUTENB |= dMaskX;     // output
}
void  SysSetNavisModelType(int nNavisType)
{
      G_nNavisModelType = nNavisType;
}
int   SysGetNavisModelType(void)
{
      return(G_nNavisModelType);
}
void  SysCheckSGP330ScrnMode(void)
{
      DWORD *pSysDataMem = (DWORD *)BACK_SYS_DATA_RAM_ADDR;
      xSYS_MLC *pSysMLC  = (xSYS_MLC *)MLCP_PHSY_BASE_ADDR;
      DWORD dHeight;

      if (pSysDataMem[0] == 0x55555555)
         {
          G_nSGP330ScrnResMode = pSysDataMem[2];
          if (G_nSGP330ScrnResMode < SGP330_SCRN_MODE_640x480 || G_nSGP330ScrnResMode > SGP330_SCRN_MODE_LAST)
              G_nSGP330ScrnResMode = SGP330_SCRN_MODE_640x480;
         }
      else
         {
          dHeight = ((pSysMLC->dSCREENSIZE >> 16) & 0x0000ffff) + 1;

          G_nSGP330ScrnResMode = SGP330_SCRN_MODE_640x480;
          if (dHeight == 480)  G_nSGP330ScrnResMode = SGP330_SCRN_MODE_640x480;
          if (dHeight == 600)  G_nSGP330ScrnResMode = SGP330_SCRN_MODE_800x600;
          if (dHeight == 768)  G_nSGP330ScrnResMode = SGP330_SCRN_MODE_1024x768;
         }

  #if defined(__N500_MODEL__)
      G_nSGP330ScrnResMode = SGP330_SCRN_MODE_800_480;
  #endif
}
void  SysSetSGP330ScrnMode(int nScrnMode)
{
      G_nSGP330ScrnResMode = nScrnMode;
}
int   SysGetSGP330ScrnMode(void)
{
      return(G_nSGP330ScrnResMode);
}
void  SysSetNavis5100AModelMode(int nMode)
{
      G_nNavis5100AModelMode = nMode;
}
int   SysGetNavis5100AModelMode(void)
{
      return(G_nNavis5100AModelMode);
}
int   SysIsLedBackLight5100(void)
{
      xSYS_GPIO *pSysCNFG = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

      if (pSysCNFG->dPAD & (1 << 22))
         {
          if (G_nNavisModelType == NAVIS_TYPE_5100 || G_nNavisModelType == NAVIS_TYPE_3800 || G_nNavisModelType == NAVIS_TYPE_800)
              return(1);
         }

      return(0);
}

int   SysCheckLCD3800Mitsubishi(void)
{
  #if !defined(__N500_MODEL__)
      xSYS_GPIO *pSysCNFG = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

      if (G_nNavisModelType == NAVIS_TYPE_3800 && pSysCNFG->dPAD & (1 << 20))
          return(1);
  #endif

      return(0);
}

void  SysCheckFpgaType(void)
{
      SysSetFpgaType(FPGA_TYPE_NONE);
}
void  SysSetFpgaType(int nFpgaType)
{
      G_nFpgaType = nFpgaType;
}
int   SysGetFpgaType(void)
{
      return(G_nFpgaType);
}
int   SysIs240x320Device(void)
{
      return(0);
}
int   SysIs480x640Device(void)
{
      return(0);
}
int   SysIs800x600Device(void)
{
      if (G_nNavisModelType == NAVIS_TYPE_1200)
          return(1);

      if (G_nNavisModelType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
          return(1);

      if (G_nNavisModelType == NAVIS_TYPE_330)
         {
          if (G_nSGP330ScrnResMode == SGP330_SCRN_MODE_800x600)
              return(1);
         }

      return(0);
}
int   SysIs800x600Greater(void)
{
      if (G_nNavisModelType == NAVIS_TYPE_1200 ||
          G_nNavisModelType == NAVIS_TYPE_3800)
          return(1);

      if (G_nNavisModelType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
          return(1);

      if (G_nNavisModelType == NAVIS_TYPE_330)
         {
          if (G_nSGP330ScrnResMode >= SGP330_SCRN_MODE_800x600)
              return(1);
         }

      return(0);
}
int   SysIs640x480Device(void)
{
      if (G_nNavisModelType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
          return(0);

      if (G_nNavisModelType == NAVIS_TYPE_800  ||
          G_nNavisModelType == NAVIS_TYPE_5100 ||
          G_nNavisModelType == NAVIS_TYPE_650)
          return(1);

      if (G_nNavisModelType == NAVIS_TYPE_330)
         {
          if (G_nSGP330ScrnResMode == SGP330_SCRN_MODE_640x480)
              return(1);
         }

      return(0);
}
int   SysIs1024x768Device(void)
{
      if (G_nNavisModelType == NAVIS_TYPE_3800)
          return(1);

      if (G_nNavisModelType == NAVIS_TYPE_330)
         {
          if (G_nSGP330ScrnResMode == SGP330_SCRN_MODE_1024x768)
              return(1);
         }

      return(0);
}
int   SysIs800x480Device(void)
{
      if (G_nNavisModelType == NAVIS_TYPE_700)
          return(1);

      return(0);
}
int   SysIsLandscapeMode(void)
{
      return(1);
}
int   SysIsLargeNANDmode(void)
{
      return(0);
}
void  SysSetSonarMode(int nMode)
{
      G_nSysSonarMode = nMode;
}
int   SysGetSonarMode(void)
{
      return(G_nSysSonarMode);
}
void  SysSetRadarMode(int nMode)
{
      G_nSysRadarMode = nMode;
}
int   SysGetRadarMode(void)
{
      return(G_nSysRadarMode);
}
void  SysSetVideoMode(int nMode)
{
      G_nSysVideoMode = nMode;
}
int   SysGetVideoMode(void)
{
      return(G_nSysVideoMode);
}
int   SysCanSetExtSonarData(void)
{
      if (SysCanHaveSonar() == 0 || SysGetSonarMode() == MODE_VAL_OFF)
          return(1);
      return(0);
}
int   SysCanViewRadarMenu(void)
{
      if (SysGetRadarMode() == MODE_VAL_ON)
          return(1);
      return(0);
}
int   SysCanViewVideoMenu(void)
{
      if (SysGetVideoMode() == MODE_VAL_ON)
          return(1);
      return(0);
}
int   SysCanHaveSonar(void)
{
#ifdef  __SONAR__
      return(1);
#else
      if (SysGetFpgaType() == FPGA_TYPE_NONE)
          return(0);
      return(1);
#endif
}
int   SysCanHaveRadar(void)
{
//    if (G_nNavisModelType == NAVIS_TYPE_5100 ||
//        G_nNavisModelType == NAVIS_TYPE_3800)
//        return(1);

      return(0);
}
int   SysCanHaveVideo(void)
{
//    if (G_nNavisModelType == NAVIS_TYPE_5100 ||
//        G_nNavisModelType == NAVIS_TYPE_3800)
//        return(1);

      return(G_nSysCanHaveVideoMode);
}
void  SysSetCanHaveVideoMode(int nMode)
{
      G_nSysCanHaveVideoMode = nMode;
}
int   SysCanHaveInternalGPS(void)
{
      return(0);
}
int   SysCanHaveNMEA2000(void)
{
  #if defined(__N500_MODEL__)
  #else
      if (G_nNavisModelType == NAVIS_TYPE_700)
          return(1);
  #endif

      return(0);
}
#if !defined(__SONAR__)
int   SysCanHaveDGPS(void)
{
  #if defined(__N500_MODEL__)
  #else
      if (SysGetNavisModelType() != NAVIS_TYPE_700)
          return(1);
  #endif

      return(0);
}
#endif

DWORD SysGetChartScreenAddress(void)
{
      return(CHART_LAYER_BASE_ADDR);
}
DWORD SysGetMenuScreenAddress(void)
{
      return(MENU_LAYER_BASE_ADDR);
}
DWORD SysGetRadarScreenAddress(void)
{
      return(RADAR_LAYER_BASE_ADDR);
}
DWORD SysGetVideoScreenAddress(void)
{
      return(VIDEO_LAYER_BASE_ADDR);
}
DWORD SysGetScreenAddress(void)
{
      return(MENU_LAYER_BASE_ADDR);
}
DWORD SysGetScreenWidth(void)
{
      return(SysGetScreenWidthByDevice(G_nDeviceType));
}
DWORD SysGetScreenHeight(void)
{
      return(SysGetScreenHeightByDevice(G_nDeviceType));
}
DWORD SysGetScreenWidthByDevice(int nDeviceType)
{
      DWORD dTempX = NVS_800_SCRN_WIDTH;

      if (G_nNavisModelType == NAVIS_TYPE_330 )
         {
          dTempX = NVS_3800_SCRN_WIDTH;

          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   dTempX = NVS_5100_SCRN_WIDTH;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   dTempX = NVS_1200_SCRN_WIDTH;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  dTempX = NVS_3800_SCRN_WIDTH;

          return(dTempX);
         }

      if (G_nNavisModelType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
         {
          return(NVS_1200_SCRN_WIDTH);
         }

      if (G_nNavisModelType == NAVIS_TYPE_800 )  dTempX = NVS_800_SCRN_WIDTH ;
      if (G_nNavisModelType == NAVIS_TYPE_5100)  dTempX = NVS_5100_SCRN_WIDTH;
      if (G_nNavisModelType == NAVIS_TYPE_1200)  dTempX = NVS_1200_SCRN_WIDTH;
      if (G_nNavisModelType == NAVIS_TYPE_3800)  dTempX = NVS_3800_SCRN_WIDTH;
      if (G_nNavisModelType == NAVIS_TYPE_330 )  dTempX = NVS_330_SCRN_WIDTH ;
      if (G_nNavisModelType == NAVIS_TYPE_700 )  dTempX = NVS_700_SCRN_WIDTH ;
      if (G_nNavisModelType == NAVIS_TYPE_650 )  dTempX = NVS_650_SCRN_WIDTH ;

      return(dTempX);
}
DWORD SysGetScreenHeightByDevice(int nDeviceType)
{
      DWORD dTempX = NVS_800_SCRN_HEIGHT;

      if (G_nNavisModelType == NAVIS_TYPE_330 )
         {
          dTempX = NVS_3800_SCRN_HEIGHT;

          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   dTempX = NVS_5100_SCRN_HEIGHT;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   dTempX = NVS_1200_SCRN_HEIGHT;
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  dTempX = NVS_3800_SCRN_HEIGHT;

          return(dTempX);
         }

      if (G_nNavisModelType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
         {
          return(NVS_1200_SCRN_HEIGHT);
         }

      if (G_nNavisModelType == NAVIS_TYPE_800 )  dTempX = NVS_800_SCRN_HEIGHT ;
      if (G_nNavisModelType == NAVIS_TYPE_5100)  dTempX = NVS_5100_SCRN_HEIGHT;
      if (G_nNavisModelType == NAVIS_TYPE_1200)  dTempX = NVS_1200_SCRN_HEIGHT;
      if (G_nNavisModelType == NAVIS_TYPE_3800)  dTempX = NVS_3800_SCRN_HEIGHT;
      if (G_nNavisModelType == NAVIS_TYPE_330 )  dTempX = NVS_330_SCRN_HEIGHT ;
      if (G_nNavisModelType == NAVIS_TYPE_700 )  dTempX = NVS_700_SCRN_HEIGHT ;
      if (G_nNavisModelType == NAVIS_TYPE_650 )  dTempX = NVS_650_SCRN_HEIGHT ;

      return(dTempX);
}
DWORD SysGetVirtScreenWidth(void)
{
      return(SysGetScreenWidth());
}
DWORD SysGetVirtScreenHeight(void)
{
      return(SysGetScreenHeight());
}
void  SysGetVirtHoriCenter(int nHoriSize,SHORT *pLeft,SHORT *pRight)
{
      int   nVirtScrnWidth;

      nVirtScrnWidth = SysGetVirtScreenWidth();
      if (nHoriSize >= nVirtScrnWidth || nHoriSize < 1)
         {
          *pLeft = 0;
          *pRight= nVirtScrnWidth - 1;
          return;
         }
      *pLeft = nVirtScrnWidth / 2 - nHoriSize / 2;
      if (*pLeft < 0)
          *pLeft = 0;
      *pRight= nVirtScrnWidth / 2 + nHoriSize / 2;
      if (*pRight >= nVirtScrnWidth)
          *pRight  = nVirtScrnWidth - 1;
}
void  SysGetVirtVertCenter(int nVertSize,SHORT *pTop,SHORT *pBottom)
{
      int   nVirtScrnHeight;

      nVirtScrnHeight = SysGetVirtScreenHeight();
      if (nVertSize >= nVirtScrnHeight || nVertSize < 1)
         {
          *pTop = 0;
          *pBottom = nVirtScrnHeight - 1;
          return;
         }
      *pTop = nVirtScrnHeight / 2 - nVertSize / 2;
      if (*pTop < 0)
          *pTop = 0;
      *pBottom = nVirtScrnHeight / 2 + nVertSize / 2;
      if (*pBottom >= nVirtScrnHeight)
          *pBottom  = nVirtScrnHeight - 1;
}
void  SysGetVirtAllCenter(int nHoriSize,int nVertSize,SHORT *pLeft,SHORT *pTop,SHORT *pRight,SHORT *pBottom)
{
      SysGetVirtHoriCenter(nHoriSize,pLeft,pRight);
      SysGetVirtVertCenter(nVertSize,pTop,pBottom);
}
int   SysGetChartLayerNo(void)
{
      return(CHART_LAYER_NO);
}
int   SysGetMenuLayerNo(void)
{
      return(MEMU_LAYER_NO);
}
void  SysRunChartLayerDirtyFlag(void)
{
      SysSetMLcLayDirtyFlag(CHART_LAYER_NO);
      while (SysGetMLcLayDirtyFlag(CHART_LAYER_NO));
}
void  SysRunMenuLayerDirtyFlag(void)
{
      SysSetMLcLayDirtyFlag(MEMU_LAYER_NO);
      while (SysGetMLcLayDirtyFlag(MEMU_LAYER_NO));
}
//========================================================================
void  SysDelayLoop(volatile DWORD dDelayCnt)
{
      while (dDelayCnt) dDelayCnt--;
}
void  SysDelayMicroSec(DWORD dDelayMicro)
{
      SysTimerDelayMicroSec(dDelayMicro);
}
void  SysDelayMiliSec(DWORD dDelayMili)
{
      SysTimerDelayMiliSec(dDelayMili);
}
DWORD SysGetSystemTimer(void)
{
      return(G_dSystemTickCounter);
}
DWORD SysIncSystemTimer(void)
{
      ++G_dSystemTickCounter;
      return(G_dSystemTickCounter);
}
DWORD SysCalcTickToMili(DWORD dTick)
{
      return(CALC_TICK_TO_MILI(dTick));
}
DWORD SysCalcMiliToTick(DWORD dMili)
{
      return(CALC_MILI_TO_TICK(dMili));
}
DWORD SysCalcTickToScnd(DWORD dTick)
{
      return(CALC_TICK_TO_SEC(dTick));
}
DWORD SysGetDiffTimeTick(DWORD dTime)
{
      DWORD dTemp;

      dTemp = SysGetSystemTimer();
      if (dTemp >= dTime)
          dTemp -= dTime;
      else
          dTemp += (0xffffffff - dTime);
      return(dTemp);
}
DWORD SysGetDiffTimeMili(DWORD dTime)
{
      return(CALC_TICK_TO_MILI(SysGetDiffTimeTick(dTime)));
}
DWORD SysGetDiffTimeScnd(DWORD dTime)
{
      return(SysGetDiffTimeMili(dTime) / 1000);
}
//========================================================================
void  SysSetUTCDate(int nYear,int nMonth,int nDay)
{
      G_xUTCDate.nYear  = nYear;
      G_xUTCDate.nMonth = nMonth;
      G_xUTCDate.nDay   = nDay;
}
void  SysGetUTCDate(int *pYear,int *pMonth,int *pDay)
{
      *pYear  = G_xUTCDate.nYear;
      *pMonth = G_xUTCDate.nMonth;
      *pDay   = G_xUTCDate.nDay;
}
void  SysSetUTCTime(int nHour,int nMinute,int nSecond)
{
      G_xUTCTime.nHour  = nHour;
      G_xUTCTime.nMinute= nMinute;
      G_xUTCTime.nSecond= nSecond;
}
void  SysGetUTCTime(int *pHour,int *pMinute,int *pSecond)
{
      *pHour  = G_xUTCTime.nHour;
      *pMinute= G_xUTCTime.nMinute;
      *pSecond= G_xUTCTime.nSecond;
}
void  SysSetLOCDate(int nYear,int nMonth,int nDay)
{
      G_xLOCDate.nYear  = nYear;
      G_xLOCDate.nMonth = nMonth;
      G_xLOCDate.nDay   = nDay;
}
void  SysGetLOCDate(int *pYear,int *pMonth,int *pDay)
{
      *pYear  = G_xLOCDate.nYear;
      *pMonth = G_xLOCDate.nMonth;
      *pDay   = G_xLOCDate.nDay;
}
void  SysSetLOCTime(int nHour,int nMinute,int nSecond)
{
      G_xLOCTime.nHour  = nHour;
      G_xLOCTime.nMinute= nMinute;
      G_xLOCTime.nSecond= nSecond;
}
void  SysGetLOCTime(int *pHour,int *pMinute,int *pSecond)
{
      *pHour  = G_xLOCTime.nHour;
      *pMinute= G_xLOCTime.nMinute;
      *pSecond= G_xLOCTime.nSecond;
}

void  SetTransponderReset(void)
{
}

void Au1100DelayMiliSec(DWORD DelayMili)
{
	SysDelayMiliSec(DelayMili);
}

//========================================================================
void  SysClearPwrPinMask(void)
{
      SysSetALIVExBitData(1,0);

//    ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dGPIORST |= (1 << 7);    // VDDPWRONRESET=1
//    ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dPWRGATE = 0;            // NPOWERGATING=0 (Disable Writing Data To Alive GPIO)
}
void  SysRunSystemPowerOff(void)
{
      DWORD dPowerCounter;

      SysDisableIRQ();
      SysDisableFIQ();

      dPowerCounter = 0;

      while (1)
            {
             SysTimerDelayMiliSec(1);

         #if defined(__N500_MODEL__)
             if (!(((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR)->dPAD & (1 << PWR_KEY_SN_BIT_NO)))
         #else
             if (!(((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR)->dPAD & (1 << PWR_KEY_SN_BIT_NO)))
         #endif
                {
                 dPowerCounter = 0;
                }
             else
                {
                 ++dPowerCounter;
                }

             if (dPowerCounter >= 100)
                 break;
            }

      *(volatile DWORD *)(CLKPWR_PHSY_BASE_ADDR + 0x0040) = 0x00000000;
      *(volatile DWORD *)(CLKPWR_PHSY_BASE_ADDR + 0x0044) = 0x00000000;
      *(volatile DWORD *)(CLKPWR_PHSY_BASE_ADDR + 0x0048) = 0x00000000;
      *(volatile DWORD *)(CLKPWR_PHSY_BASE_ADDR + 0x004c) = 0x00000000;

      *(volatile DWORD *)(CLKPWR_PHSY_BASE_ADDR + 0x0050) = 0x00000000;
      *(volatile DWORD *)(CLKPWR_PHSY_BASE_ADDR + 0x0054) = 0x00000000;

      ((xSYS_GPIO *)GPIOA_PHSY_BASE_ADDR)->dOUTENB        = 0x00000000;  // input mode
      ((xSYS_GPIO *)GPIOB_PHSY_BASE_ADDR)->dOUTENB        = 0x00000000;  // input mode
      ((xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR)->dOUTENB        = 0x00000000;  // input mode
      ((xSYS_GPIO *)GPIOD_PHSY_BASE_ADDR)->dOUTENB        = 0x00000000;  // input mode
      ((xSYS_GPIO *)GPIOE_PHSY_BASE_ADDR)->dOUTENB        = 0x00000000;  // input mode

      SysDelayLoop(1000);

//    SysSetALIVExBitData(1,0);
      ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dPADOUTSETREG = 0x00;
      ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dPADOUTRSTREG = 0x02;

      ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dPADOUTRSTREG = 0x00;
      ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dPADOUTSETREG = 0x00;
      ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dPWRGATE = 0x00;         // NPOWERGATING=0 (Disable Writing Data To Alive GPIO)

      ((xSYS_CLOCK *)CLKPWR_PHSY_BASE_ADDR)->dPWRMODE = 0x00000002;  // STOP Mode

      ((xSYS_ALIVE *)ALIVE_PHSY_BASE_ADDR)->dVDDCTRLRSTREG = 0x01;   // Core VDD Power off

      while (1);
}
void  SysSetPowerMask(int nMask)
{
      G_nPowerMask = nMask;
}
int   SysGetPowerask(void)
{
      return(G_nPowerMask);
}
int   SysCheckSaveStart(DWORD dStartTick)
{
//    if (SysGetDiffTimeMili(dStartTick) >= 500)
          return(1);
      return(0);
}
//========================================================================
void  SysSetAlphaBlendPrcnt(int nPercent)
{
      nPercent = nPercent / 10 * 10;
      if (nPercent >= 0 && nPercent <= 100)
         {
          G_nSysAlphaBlendPrcnt = nPercent;
          SysSetAlphaBlendValue(255 * G_nSysAlphaBlendPrcnt / 100);
         }
}
int   SysGetAlphaBlendPrcnt(void)
{
      return(G_nSysAlphaBlendPrcnt);
}
void  SysSetAlphaBlendValue(UCHAR bAlpha)
{
      G_cSysAlphaBlendValue = bAlpha;
      G_dSysAlphaBlendValue = ((DWORD)G_cSysAlphaBlendValue) << 24;
}
UCHAR SysGetAlphaBlendValue(void)
{
      return(G_cSysAlphaBlendValue);
}
void  SysSetOriginAlphaBlendValue(void)
{
      SysSetAlphaBlendValue(0xff);
}
void  SysSetBrightValue(int nPercent)
{
	//static int vPwmFactorTable700[] = {  0,280,281,282,283,285,290,300,400,500,1000};
	static int vPwmFactorTable700[] = {  0,247,248,255,270,285,290,300,400,500,1000};
	static int vLcdFactorTable700[] = {  0,256,256,256,256,256,256,256,256,256, 256};
	static int vLedFactorTable700[] = {  0, 10, 30, 50, 70,100,150,300,500,700,1000};  // 1000

	int   nNavisType = SysGetNavisModelType();

	int   *pPwmFactorTable;
	int   *pLcdFactorTable;
	int   *pLedFactorTable;

	int   i,nIndex,nPeriod;


	pPwmFactorTable = vPwmFactorTable700;
	pLcdFactorTable = vLcdFactorTable700;
	pLedFactorTable = vLedFactorTable700;

	nPercent = nPercent / 10 * 10;
	nIndex   = nPercent / 10;
	if (nPercent >= 10 && nPercent <= 100)
	{
		G_nSysBrightPercntVal = nPercent;
		G_nSysBrightFactorVal = pLcdFactorTable[nIndex];
		
		for (i = 0;i < 256;i++)
			G_vSysBrightFactorTbl[i] = i * G_nSysBrightFactorVal / 256;

		SysSetLcdPWM(pPwmFactorTable[nIndex]);

		if (pLedFactorTable[nIndex] >= 0)
		{
			SysSetLedPWMbyPercent(pLedFactorTable[nIndex]);
		}			
		else
		{
			nPeriod = SysGetPWMPeriod(PWM_KEY_CHANNEL);
			SysSetLedPWMbyDutyVal(nPeriod + pLedFactorTable[nIndex]);
		}
	}

#if  defined(__NAVIONICS__)
      SetNavMapBrightValue(nPercent);
#endif
}
int   SysGetBrightValue(void)
{
      return(G_nSysBrightPercntVal);
}
void  SysSetPaletteData(int nSize,DWORD *pColorTable)
{
      int   i;

      G_nSysPaletteDataSize = nSize;

      if (G_pSysPaletteDataUCHAR != NULL) free(G_pSysPaletteDataUCHAR);
      if (G_pSysPaletteDataHWORD != NULL) free(G_pSysPaletteDataHWORD);

      G_pSysPaletteDataUCHAR = (UCHAR *)malloc(G_nSysPaletteDataSize * sizeof(UCHAR) * 3);
      G_pSysPaletteDataHWORD = (HWORD *)malloc(G_nSysPaletteDataSize * sizeof(HWORD) * 1);

      for (i = 0;i < nSize;i++)
          {
           G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 0] = (((pColorTable[i] & 0x00ff0000) >> 16) * G_nSysBrightFactorVal / 256) & 0x000000ff;
           G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 1] = (((pColorTable[i] & 0x0000ff00) >>  8) * G_nSysBrightFactorVal / 256) & 0x000000ff;
           G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 2] = (((pColorTable[i] & 0x000000ff) >>  0) * G_nSysBrightFactorVal / 256) & 0x000000ff;
           G_pSysPaletteDataHWORD[i] = ((G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 0] >> 3) << 11) |
                                       ((G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 1] >> 2) <<  5) |
                                       ((G_pSysPaletteDataUCHAR[i * sizeof(UCHAR) * 3 + 2] >> 3) <<  0);
          }
}
void  SysSetMapLayerPalData(UCHAR *pPalData)
{
      int   i;
      DWORD dR,dG,dB;

      memmove(G_vMapPaletteDataUCHAR,pPalData,sizeof(G_vMapPaletteDataUCHAR));

      for (i = 0;i < (sizeof(G_vMapPaletteDataHWORD) / sizeof(G_vMapPaletteDataHWORD[0]));i++)
          {
           dR = *pPalData++;
           dG = *pPalData++;
           dB = *pPalData++;
           dR = dR * G_nSysBrightFactorVal / 256;
           dG = dG * G_nSysBrightFactorVal / 256;
           dB = dB * G_nSysBrightFactorVal / 256;

           G_vMapPaletteDataHWORD[i] = (HWORD)(((dR >> 1) << 11) | (dG << 5) | (dB >> 1));
          }
}
void  SysGetMapLayerPalData(HWORD wColor,UCHAR *pR,UCHAR *pG,UCHAR *pB)
{
      int   i;

      for (i = 0;i < (sizeof(G_vMapPaletteDataHWORD) / sizeof(G_vMapPaletteDataHWORD[0]));i++)
          {
           if (G_vMapPaletteDataHWORD[i] == wColor)
              {
               *pR = G_vMapPaletteDataUCHAR[i * 3 + 0] << 3;
               *pG = G_vMapPaletteDataUCHAR[i * 3 + 1] << 2;
               *pB = G_vMapPaletteDataUCHAR[i * 3 + 2] << 3;
               return;
              }
          }
}
void  SysSetRadarNormPalData(UCHAR *pPalData)
{
      int   i;
      DWORD dR,dG,dB;

      for (i = 0;i < (sizeof(G_vRadarNormColorTable) / sizeof(G_vRadarNormColorTable[0]));i++)
          {
           dR = *pPalData++;
           dG = *pPalData++;
           dB = *pPalData++;
           dR = dR * G_nSysBrightFactorVal / 256;
           dG = dG * G_nSysBrightFactorVal / 256;
           dB = dB * G_nSysBrightFactorVal / 256;

//         if (dR == 0 && dG == 0 && dB == 0)
//             G_vRadarNormColorTable[i] = MLC_TRANSPARENCY_COLOR;
//         else 
               G_vRadarNormColorTable[i] = 0xff000000 | (dR << 16) | (dG <<  8) | (dB);
          }
}
void  SysSetRadarOverPalData(UCHAR *pPalData)
{
      int   i;
      DWORD dR,dG,dB;
      DWORD dT;

      dT = G_dRadarOverTransparecyValue * 255 / 100;
      dT = 255 - dT;
      dT = dT & 0x000000ff;
      dT = dT << 24;

      for (i = 0;i < (sizeof(G_vRadarOverColorTable) / sizeof(G_vRadarOverColorTable[0]));i++)
          {
           dR = *pPalData++;
           dG = *pPalData++;
           dB = *pPalData++;
           dR = dR * G_nSysBrightFactorVal / 256;
           dG = dG * G_nSysBrightFactorVal / 256;
           dB = dB * G_nSysBrightFactorVal / 256;

           if (dR == 0 && dG == 0 && dB == 0)
               G_vRadarOverColorTable[i] = MLC_TRANSPARENCY_COLOR;
           else 
               G_vRadarOverColorTable[i] = dT | (dR << 16) | (dG <<  8) | (dB);
          }
}
void  SysSetRadarOverTransparencyValue(int nValue)
{
      if (nValue >= 0 && nValue <= 80)
          G_dRadarOverTransparecyValue = nValue;
}
HWORD SysGetMapLayerOriginColor(HWORD wColor)
{
      int   i;
      HWORD R,G,B;

      for (i = 0;i < (sizeof(G_vMapPaletteDataHWORD) / sizeof(G_vMapPaletteDataHWORD[0]));i++)
          {
           if (G_vMapPaletteDataHWORD[i] == wColor)
              {
               R = G_vMapPaletteDataUCHAR[i * 3 + 0] >> 1; R <<= 11;
               G = G_vMapPaletteDataUCHAR[i * 3 + 1] >> 0; G <<=  5;
               B = G_vMapPaletteDataUCHAR[i * 3 + 2] >> 1; B <<=  0;
               wColor = R | G | B;
               break;
              }
          }

      return(wColor);
}
//========================================================================
void  SysSetGlobalUsingMap(int nUsingMap)
{
      G_nGlobalUsingMap = nUsingMap;
}
int   SysGetGlobalUsingMap(void)
{
      return(G_nGlobalUsingMap);
}
int   SysIsUsingMapCMAP(void)
{
      if (G_nGlobalUsingMap == USING_MAP_CMAP)
          return(1);
      return(0);
}
int   SysIsUsingMapSAMYUNG(void)
{
      if (G_nGlobalUsingMap == USING_MAP_SAM)
          return(1);
      return(0);
}
void  SysSetSamMapTypeMode(int nMapType)
{
      G_nSamMapTypeMode = nMapType;
}
int   SysGetSamMapTypeMode(void)
{
      return(G_nSamMapTypeMode);
}
int   SysIsSamMapTypeNewPEC(void)
{
      if (G_nSamMapTypeMode == SAM_MAP_TYPE_MODE_NPC)
          return(1);

      return(0);
}
void  SysSetSamMapNewPecFound(int nFound)
{
      G_nSamMapNewPecFound = nFound;
}
int   SysGetSamMapNewPecFound(void)
{
      return(G_nSamMapNewPecFound);
}
void  SysSetSamMapKoreaZone(int nKoreaZone)
{
      G_nSamMapKoreaZone = nKoreaZone;
}
int   SysGetSamMapKoreaZone(void)
{
      return(G_nSamMapKoreaZone);
}
//========================================================================
void  SysSetGlobalLangCode(int nLangCode)
{
      G_nGlobalLangCode = nLangCode;
}
int   SysGetGlobalLangCode(void)
{
      return(G_nGlobalLangCode);
}
//========================================================================
void  SysSetGlobalNightMode(int nMode)
{
      G_nGlobalNightMode = nMode;
}
int   SysGetGlobalNightMode(void)
{
      return(G_nGlobalNightMode);
}
void  SysSetGlobalChartBodyMode(int nMode)
{
      G_nGlobalChartBodyMode = nMode;
}
int   SysGetGlobalChartBodyMode(void)
{
      return(G_nGlobalChartBodyMode);
}
void  SysSetGlobalMenuBackColor(int nColor)
{
      G_nGlobalMenuBackColor = nColor;
}
int   SysGetGlobalMenuBackColor(void)
{
      return(G_nGlobalMenuBackColor);
}
//========================================================================
int   SysGetUsbDiskMountedStatus(void)
{
      return(0);
}
void  SysAppendDriveName(char *pFileName)
{
	char vDrvName[1024];

	if (pFileName[1] != ':')
	{
		if (SysGetUsbDiskMountedStatus())
		{
			strcpy(vDrvName,OsfGetUsbDiskDriveStr());
		}			
		else
		{
			strcpy(vDrvName,OsfGetSdCardDriveStr());
		}			
		strcat(vDrvName,pFileName);
		strcpy(pFileName,vDrvName);
	}
}
//========================================================================
void  SysSetSdCardLastChangeStatus(int nChanged)
{
      G_nSdCardLastChangeStatus = nChanged;
}
int   SysGetSdCardLastChangeStatus(void)
{
      return(G_nSdCardLastChangeStatus);
}
//========================================================================
void  SysSetKeyDataGetEnabledMode(int nMode)
{
      G_nKeyDataGetEnabledMode = nMode;
}
int   SysGetKeyDataGetEnabledMode(void)
{
      return(G_nKeyDataGetEnabledMode);
}
//========================================================================
void  SysSetFpgaMulFactor(int nFactor)
{
      FPGA_MUL_FACTOR = nFactor;
}
int   SysGetFpgaMulFactor(void)
{
      return(FPGA_MUL_FACTOR);
}
void  SysWriteDataToFPGA(DWORD dAddr,UCHAR bData)
{
      if (FPGA_MUL_FACTOR == 1)
         {
          volatile UCHAR *pFpgaAddr = (volatile UCHAR *)dAddr;

          *pFpgaAddr = bData;
         }
      else
         {
          volatile HWORD *pFpgaAddr = (volatile HWORD *)dAddr;
          HWORD wData = ((HWORD)bData) << 8;

          *pFpgaAddr = wData;
         }
}
UCHAR SysReadDataFromFPGA(DWORD dAddr)
{
      if (FPGA_MUL_FACTOR == 1)
         {
          volatile UCHAR *pFpgaAddr = (volatile UCHAR *)dAddr;

          return(*pFpgaAddr);
         }
      else
         {
          volatile HWORD *pFpgaAddr = (volatile HWORD *)dAddr;
          HWORD wData;

          wData = *pFpgaAddr;
          return(wData >> 8);
         }
}
void  SysReadFpgaVersionData(char *pVerData)
{
      int  i;

      for (i = 0;i < 8;i++)
          {
           pVerData[i] = SysReadDataFromFPGA(FPGA_VER_ADDR + i * FPGA_MUL_FACTOR);
          }
      if (pVerData[0] != 'P' || pVerData[1] != 'S')
          memset(pVerData,'-',8);

      pVerData[8] = 0x00;
}
//========================================================================
void  SysCheckSonarHwPcbVersion(void)
{
  #if defined(__N500_MODEL__)
      SysSetSonarHwPcbVersion(SONAR_HW_PCB_VER_NEW);
  #else
      xSYS_GPIO *pSysCNFG = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

      if (pSysCNFG->dPAD & (1 << 18))
          SysSetSonarHwPcbVersion(SONAR_HW_PCB_VER_NEW);
      else
          SysSetSonarHwPcbVersion(SONAR_HW_PCB_VER_OLD);
  #endif
}
void  SysSetSonarHwPcbVersion(int nHwVer)
{
      G_nSonarHwPcbVersion = nHwVer;
}
int   SysIsSonarHwPcbVersionOld(void)
{
      if (G_nSonarHwPcbVersion == SONAR_HW_PCB_VER_OLD)
          return(1);

      return(0);
}
int   SysIsSonarHwPcbVersionNew(void)
{
      if (G_nSonarHwPcbVersion == SONAR_HW_PCB_VER_NEW)
          return(1);

      return(0);
}
//========================================================================
int   SysIsMyanmareseLanguage(void)
{
#ifdef  __PLASTIMO__
      return(0);
#else
      if (G_nGlobalLangCode == LNG_CODE_MYA)
          return(1);

      return(0);
#endif
}
int   SysIsArabianLanguage(void)
{
#ifdef  __PLASTIMO__
      return(0);
#else
      if (G_nGlobalLangCode == LNG_CODE_ARB)
          return(1);

      return(0);
#endif
}
int   SysIsPersianLanguage(void)
{
#ifdef  __PLASTIMO__
      return(0);
#else
      if (G_nGlobalLangCode == LNG_CODE_PRS)
          return(1);

      return(0);
#endif
}
int   SysIsArabianOrPersianLanguage(void)
{
#ifdef  __PLASTIMO__
      return(0);
#else
      if (G_nGlobalLangCode == LNG_CODE_ARB || G_nGlobalLangCode == LNG_CODE_PRS)
          return(1);

      return(0);
#endif
}
//========================================================================
#if defined(__NAVIONICS__)
///*
void  _exit(int nError)
{
      while (1);
}
int   _getpid_r(void)
{
      return(0);
}
int   _stat_r(const char *file_name,struct stat *buf)
{
      return(0);
}
int   _unlink_r(const char *file_name)
{
      return(0);
}
//*/
#endif

//==============================================================================
static int  G_nPowerStaus = 1;                          // o=off,1=on
static int  G_nCanCheckPowerStatus = 0;
//==============================================================================
void  SetCheckPowerStatus(int nMode)
{
	G_nCanCheckPowerStatus = nMode;
}
void  CheckPowerStatus(void)
{
	static DWORD dSysTimer = 0;
	static DWORD dPwrTimer = 0;
	static int   nPwrChange= 0;
	xSYS_GPIO *pSysGPIO = (xSYS_GPIO *)GPIOC_PHSY_BASE_ADDR;

	dSysTimer++;
	// *(DWORD *)0x03b00000 = 0x11111111;
	// *(DWORD *)0x03b00004 = dSysTimer;
	// *(DWORD *)0x03b00008 = pSysGPIO->dPAD & (1 << PWR_GPIO_BIT_NO);
	// *(DWORD *)0x03b0000c = 0x11111111;

	if (G_nCanCheckPowerStatus && !(pSysGPIO->dPAD & (1 << PWR_GPIO_BIT_NO)))
	{
		if (dPwrTimer == 0)
		{
			dPwrTimer = dSysTimer;
		}
		else
		{
			if (nPwrChange == 0)
			{
				if (dPwrTimer > dSysTimer)
				{
					dPwrTimer = dSysTimer;
				}

				// Power Off �� ����
				if(G_nPowerStaus == 0)
				{
					if ((dSysTimer - dPwrTimer) > 5)     // 50mSec
					{
						G_nPowerStaus = 1 - G_nPowerStaus;
						SetPowerStatus(G_nPowerStaus);
						nPwrChange = 1;
					}
				}
				else
				{
					if ((dSysTimer - dPwrTimer) > 100)     // 1 sec
					{
						G_nPowerStaus = 1 - G_nPowerStaus;
						SetPowerStatus(G_nPowerStaus);
						nPwrChange = 1;
					}
				}
			}
		}
	}
	else
	{
		dPwrTimer = 0;
		nPwrChange= 0;
	}
}

void  SetPowerStatus(int nStatus)
{
#if 1
	G_nPowerStaus = nStatus;

	if(G_nPowerStaus)
	{
		SysSetLcdOn();
	}
	else
	{
		SysSetLcdOff();
	}
#else
	static HWORD wLcdPeriod = 0x0000;
	static HWORD wLcdDuty   = 0x0000;

	static HWORD wKeyPeriod = 0x0000;
	static HWORD wKeyDuty   = 0x0000;

	static HWORD wLedPeriod = 0x0000;
	static HWORD wLedDuty   = 0x0000;

	G_nPowerStaus = nStatus;
	if (G_nPowerStaus)
	{
		SysSetPWMDutyCycle(PWM_LCD_CHANNEL,wLcdDuty);

		SysSetPWMDutyCycle(PWM_KEY_CHANNEL,wKeyDuty);

		SysSetPWMDutyCycle(PWM_BUZ_CHANNEL,wLedDuty);
	}
	else
	{
		wLcdPeriod = SysGetPWMPeriod(PWM_LCD_CHANNEL);
		wLcdDuty   = SysGetPWMDutyCycle(PWM_LCD_CHANNEL);
		SysSetPWMDutyCycle(PWM_LCD_CHANNEL,0);

		wKeyPeriod = SysGetPWMPeriod(PWM_KEY_CHANNEL);
		wKeyDuty   = SysGetPWMDutyCycle(PWM_KEY_CHANNEL);
		SysSetPWMDutyCycle(PWM_KEY_CHANNEL,wKeyPeriod + 1);

		wLedPeriod = SysGetPWMPeriod(PWM_BUZ_CHANNEL);
		wLedDuty   = SysGetPWMDutyCycle(PWM_BUZ_CHANNEL);
		SysSetPWMDutyCycle(PWM_BUZ_CHANNEL,wLedPeriod + 1);
	}
#endif	
}


int GetPowerStatus(void)
{
	return(G_nPowerStaus);
}

int IsLcdLedType(void)
{
	return(1);
}
int SysIs640x480Mode(void)
{
	return 0;
}

int SysIs800x480Mode(void)
{
	return 1;
}

