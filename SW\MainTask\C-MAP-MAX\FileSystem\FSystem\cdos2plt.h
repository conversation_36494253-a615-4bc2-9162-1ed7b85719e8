/*##################################################################
  FILE    : CDOS2PLT.H
 
  USE     : File System High level: Data Reading routines header file.
  PROJECT : C-Map's File System

  AUTHOR  :
  UPDATED :
  ##################################################################

*/




#ifndef __CDOS2PLT__
	#define __CDOS2PLT__

/*****************************************************************************
  #Include section.
 *****************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include <CMapType.h>
#include <FSExtern.h>




/*****************************************************************************
  Constants definition section.
 *****************************************************************************/


/* 
 * Following defines are used to represent the possible errors returned
 * by Do_Load routine. Errors can be combined using OR operator so this
 * is the reason why they don't have consecutive values.
 */

#define ERR_BAD_FILETYPE         	0x0001
#define ERR_OLD_FORMAT           	0x0002
#define ERR_BAD_FILEVERSION      	0x0004
#define ERR_BAD_FORMAT           	0x0008
#define ERR_ROUTE_STRUCTURE_FULL 	0x0010
#define ERR_ROUTE_FULL	  			0x0020
#define ERR_WP_STRUCTURE_FULL    	0x0040
/* #define ERR_EOF                  0x0080 	!! Changed with ERR_EOD 	*/
#define ERR_EOD		            	0x0080				/* END OF DATA 	*/
#define ERR_TRACKS_STRUCTURE_FULL 	0x0100
#define ERR_IS_BREAK                0x0200


/* 
 * ERR_ABORTED_BY_USER is returned when a function has been 
 * interrupted  by the user.															
 */
#define ERR_ABORTED_BY_USER			0x8000


/* 
 * This define specify the size of the name field in MERFileData  
 * structure type. It specify the size in bytes.
 */
#define MAX_USR_WP_NAME_LEN			40


/* 
 * This define is used to specify the maximum size (in bytes) of 
 * the Description filed of the MERFileData structure type.
 */
#define MAX_USR_WP_DESCR_LEN		40


/* 
 * Size of the buffer used read data from the device. The READ_BUFFER_LEN 
 * value must be greater than WP_NAME_LEN+1 and WP_DESCR_LEN+1. 
 */
 
#if ( MAX_USR_WP_NAME_LEN >= MAX_USR_WP_DESCR_LEN )
	#define READ_BUFFER_LEN			( MAX_USR_WP_NAME_LEN + 2 )	
#else
	#define READ_BUFFER_LEN			( MAX_USR_WP_DESCR_LEN + 2 )	
#endif


#define CLASS_MARK					0
#define CLASS_ROUTE			   		1
#define CLASS_TRACK			   		2
#define CLASS_EVENT          		3
#define CLASS_TAG					4
#define CLASS_LINE					5
#define CLASS_AREA					6
#define CLASS_RAW					7


/* 
 * The following defines must be used to set the 'type'  
 * field in the mark-Event-Route file.												
 */
#define USR_MRK_TYPE 				1	/* Mark 		*/
#define USR_WPT_TYPE 				2	/* Waypoint 	*/
#define USR_EVT_TYPE 				3	/* Event. 		*/
#define USR_MOB_TYPE 				4	/* MOB. 		*/
#define USR_RTH_TYPE 				5	/* Route break. */
#define USR_GENERIC_TYPE 			6	/* Raytheon Projects Only	*/
									
#define OUT_FILEVERSION	 			5	/* Latest File Version 		*/


/*======================================================================*/
/*								USER POINTS								*/
/*======================================================================*/

/* 
 * Definition of the size of each field into the User Point file		
 */
#define TYPE_MER_SIZE  			1
#define NUMBER_MER_SIZE			2
#define COLOR_MER_SIZE 			1
#define SYMBOL_MER_SIZE			1
#define RTENUM_MER_SIZE			1
#define POINTDT_MER_SIZE   		4
#define RTELEG_MER_SIZE			1
#define TEMP_MER_SIZE  			2
#define SOURCE_MER_SIZE			1
#define DEPTH_MER_SIZE 			4
#define RTECOL_MER_SIZE			1
#define NTOBJ_MER_SIZE 			2
#define YEAR_MER_SIZE  			2
#define CONFIG_MER_SIZE  		1
#define SPEED_MER_SIZE  		2
#define SECONDS_MER_SIZE  		1

/*
 * Folder name field #defines
 */
#define FOLDER_NAME_LEN			32						// Folder name max size.
#define FOLDER_NAME_BUFF_LEN	( FOLDER_NAME_LEN + 1 ) // 1 extra data byte for terminator.


typedef struct
{
	SByte		Size;				/* Number of fields of this structure 			*/
	SLong		Lat, Lon;
	SByte		Name[MAX_USR_WP_NAME_LEN];
	SByte		Description[MAX_USR_WP_DESCR_LEN];
	SByte		Type;				/* Mark=1 Wpt=2 Event=3	MOB=4 RTH=5				*/
	SWord 		Number;
    SByte    	Color;
	SByte		Symbol;
	SByte		RouteNumber;	
	_fs_time_t	PointDateTime; /* time_t */
	SByte		RouteLeg;
	SWord  		Temp;
	SByte 		Source;
	SLong    	Depth;
    SByte   	RouteColor;
    SWord     	NtObjectLabel;
    SWord     	PointYear;			/* Internal Field */
	/* Descr. Field or EMPTY Field (if Descr could be saved after Name )			*/
	/* Following Bool fields are saved into Configuration Field.					*/
	SByte    	DangerFlag;			/* 1st - 0=Standard Wpt, 1=Danger Wpt			*/
									/* 2nd - for future use							*/
									/* 3rd - for future use							*/
									/* 4th - for future use							*/
	/* End of Configuration Field */

	SWord     	Speed;				/* Vehicle Speed: Knots * USR_TRK_SPEED_FACTOR	*/
	SByte    	PointSeconds;		/* Internal Field */
	SByte		FolderName[FOLDER_NAME_BUFF_LEN];	/* Folder name.		*/

} MERFileData;  			/* Mark Event Route File Data 							*/



/************************************************************
 * Support for CONFIG FIELD:
 *
 *	BIT: 7 6 5 4 3 2 1 0 
 *       | | | | | | | |
 *       | | | | | | | DangerFlag Data
 *       | | | | | | DangerFlag Valid 
 *       | | | | | 2nd Data
 *       | | | | 2nd Valid
 *       | | | 3rd Data
 *       | | 3rd Valid
 *       | 4th Data
 *       4th Valid
 *
 ************************************************************/
#define DANGER_FLAG_VALID		0x02
#define DANGER_FLAG_DATA		0x01

/*======================================================================*/
/*								TRACKING								*/
/*======================================================================*/

/* definition of the size of each field into the Track file				*/
#define PATTERN_TRK_SIZE   			1
#define TYPE_TRK_SIZE				1
#define FCOL_TRK_SIZE				1
#define BCOL_TRK_SIZE				1
#define SPEED_TRK_SIZE				2
#define TIME_TRK_SIZE				4
#define ALT_TRK_SIZE				2
#define TRIP_TRK_SIZE				4
#define COURSE_TRK_SIZE				2
#define WATER_TRK_SIZE				2
#define WSPEED_TRK_SIZE				2
#define WANGLE_TRK_SIZE				2


typedef struct
{
	SByte 	Size;
	SLong  	Lat, Lon;			/* Position 										*/
    SByte   TrackPattern;		/* Pattern 											*/
	SByte	Type;				/* Type: 0/1										*/
    SByte   TrackFColor;   		/* Foreground Color 								*/
    SByte   TrackBColor;		/* Background Color 								*/
	SWord   Speed;				/* Vehicle Speed: Knots * USR_TRK_SPEED_FACTOR		*/
	SLong   DateTime;			/* Seconds since 1/1/1970							*/
	SWord   Altitude;			/* Altitude: Meters + USR_TRK_ALT_OFF				*/
	SLong   TripDeltaDist; 		/* Distance: NM * USR_TRK_TRIP_DIST_FACTOR			*/
	SWord   Course;				/* Vehicle Course (degrees * USR_TRK_COURSE_FACTOR)	*/
	SWord   WaterTemp;			/* Water Temp.: C + USR_TRK_WATER_TEMP_OFF			*/

	SWord	TrueWindSpeed;		/* True Wind Speed: Knots * USR_WND_SPEED_FACTOR	*/
	SWord	TrueWindAngle;		/* True Wind Angle: degrees * USR_WND_ANGLE_FACTOR	*/

} TrackFileData;


/************************************************************
 * Definition of Altitude min/max range and offset values
 *
 * Example for SAVE Altitude field:
 *
 *  	if (Altitude < USR_TRK_ALT_MIN)
 * 			Altitude = USR_TRK_ALT_MIN;
 * 		else if (Altitude > USR_TRK_ALT_MAX)
 * 			Altitude = USR_TRK_ALT_MAX;
 * 		Altitude += USR_TRK_ALT_OFF;
 *
 *
 * Example for LOAD Altitude field:
 *
 * 		if ( Altitude != FSYS_EMPTY_FIELD )
 *		{
 * 			Altitude -= USR_TRK_ALT_OFF;
 *			Store altitude into the Kernel structure
 *		}
 *
 ************************************************************/
#define USR_TRK_ALT_MIN				 -500
#define USR_TRK_ALT_MAX				32267
#define USR_TRK_ALT_OFF				  500
/************************************************************/
						
						
/************************************************************
 * TRIP DELTA DISTANCE FACTOR definition
 * Trip Delta Distance must be stored in NM * 1000 to have a
 * good accuracy.						
 ************************************************************/
#define USR_TRK_TRIP_DIST_FACTOR	1000
/************************************************************/


/************************************************************
 * TRACK & USER POINT SPEED FACTOR definition
 * Speed value (in both Track and User Point files) must be 
 * stored in KTS * 10 
 ************************************************************/
#define USR_TRK_SPEED_FACTOR		10
/************************************************************/


/************************************************************
 * Example for SAVE Course field:
 *  Course is the variable in TrackFileData Structure
 *
 *	if ( CourseOk )
 *	{
 *		Course = (SWord)(CourseF * USR_TRK_COURSE_FACTOR);
 *
 *  	if (Course < USR_TRK_COURSE_MIN)
 * 			Course = USR_TRK_COURSE_MIN;
 * 		else if (Course > USR_TRK_COURSE_MAX)
 * 			Course = USR_TRK_COURSE_MAX;
 *	}
 *
 *
 * Example for LOAD Course field:
 *  Course is the variable in TrackFileData Structure
 *
 * 		if ( Course != FSYS_EMPTY_FIELD )
 *		{
 *			Float CourseF;
 *			CourseF = Course / USR_TRK_COURSE_FACTOR;
 *
 *			Store CourseF into the Kernel structure
 *		}
 *
 *
 * Definition of Course min/max range values:
 *
 ************************************************************/
#define USR_TRK_COURSE_MIN				0
#define USR_TRK_COURSE_MAX				3599	/* 359.9	*/

#define USR_TRK_COURSE_FACTOR			10
/************************************************************/


/************************************************************
 * Example for SAVE WaterTemp field:
 *  WaterTemp is the variable in TrackFileData Structure
 *
 *	if ( WaterTempOk )
 *	{
 *		WaterTemp = (SWord)(WaterTempF * USR_TRK_WATER_TEMP_FACTOR);
 *
 *  	if (WaterTemp < USR_TRK_WATER_TEMP_MIN)
 * 			WaterTemp = USR_TRK_WATER_TEMP_MIN;
 * 		else if (WaterTemp > USR_TRK_WATER_TEMP_MAX)
 * 			WaterTemp = USR_TRK_WATER_TEMP_MAX;
 * 		WaterTemp += USR_TRK_WATER_TEMP_OFF;
 *	}
 *
 *
 * Example for LOAD WaterTemp field:
 *  WaterTemp is the variable in TrackFileData Structure
 *
 * 		if ( WaterTemp != FSYS_EMPTY_FIELD )
 *		{
 *			Float WaterTempF;
 *
 * 			WaterTemp -= USR_TRK_WATER_TEMP_OFF;
 *			WaterTempF = (Float)WaterTemp / USR_TRK_WATER_TEMP_FACTOR;
 *
 *			Store WaterTempF into the Kernel structure
 *		}
 *
 *
 * Definition of Water Temperature min/max range and offset values:
 *
 ************************************************************/
#define USR_TRK_WATER_TEMP_MIN				-2000	/* -200.0 C	*/
#define USR_TRK_WATER_TEMP_MAX				 2000	/*  200.0 C	*/
#define USR_TRK_WATER_TEMP_OFF				 2000	/*	200.0 C	*/

#define USR_TRK_WATER_TEMP_FACTOR			   10

/************************************************************
 * True Wind speed conversion factor.
 ************************************************************/
#define USR_WND_SPEED_FACTOR					10

/************************************************************
 * True Wind angle defines.
 ************************************************************/
#define USR_WND_ANGLE_MIN						0		/* 0.0 deg		*/
#define USR_WND_ANGLE_MAX						3599	/* 359.9 deg	*/
#define USR_WND_ANGLE_FACTOR					10

/************************************************************/

/*
 * Definition of the size of each field into the Track file				
 */
#define CFGDATA_HDR_SIZE   			1
#define UNITID_HDR_SIZE   			2
#define CFGMASK_HDR_SIZE   			1
#define NUMITEM_HDR_SIZE   			4

typedef struct
{
	SByte 	Size;
    SByte   ConfigData;
    SWord   UnitId;				/* For Future Use 	*/
    SByte   ConfigMask;
	SLong	NumOfItems;

} HeaderFileData;


#define NO_UNIT_ID					0x7fff


/*--------------------------------------------------------------------------

					====================================
 					CONFIGURATION DATA Field Description 
					====================================

 	Bit 7 = Lat\Long Position 												
			0 = Meters/4 
			1 = Standard Meters									

 	Bit 6 = WGS84 Coordinates System
 			Not Used in H8\SH2 plotters
 			
	Bit 5 = Waypoint Description Position
			0 = After PointYear field (At the end - Mitsubishi, RC620, RC630)
			1 = After Name field (in the middle - most of H8/SH2 projects)

  --------------------------------------------------------------------------*/


#define FS_CFG_POS_NOT_DIVIDED_BIT		0x80	/* Bit 7 */
#define FS_CFG_WGS84_COORD_SYS_BIT		0x40	/* Bit 6 */
#define FS_CFG_DESCR_AFTER_NAME_BIT		0x20	/* Bit 5 */

#define FS_CFG_STORED_COORD_BIT			0x80	/* Bit 7 */
#define FS_CFG_COORD_SYSTEM_BIT			0x40	/* Bit 6 */
#define FS_CFG_DESCR_POS_BIT			0x20	/* Bit 5 */


/* Default value for Configuartion Data Field */

#define FS_CFG_DEFAULT_DATA  	( FS_CFG_POS_NOT_DIVIDED_BIT | \
								  FS_CFG_WGS84_COORD_SYS_BIT | \
								  FS_CFG_DESCR_AFTER_NAME_BIT )

/* 
 * Default value for Configuration Mask Field: 								
 * This field identify if the relative BIT in the Data Field is 
 * valid (or present) or not. 														
 */ 

#define FS_CFG_DEFAULT_MASK  	( FS_CFG_STORED_COORD_BIT | \
								  FS_CFG_COORD_SYSTEM_BIT | \
								  FS_CFG_DESCR_POS_BIT	 )

/* --------------------------------------------------------------------------- 
	In Raytheon Projects the above configuration values should be as 
	described below:

#define FS_CFG_DEFAULT_DATA  	( FS_CFG_POS_NOT_DIVIDED_BIT | \
								  FS_CFG_WGS84_COORD_SYS_BIT | )

The Description field should be saved at the end of the record so that Old 
Raytheon projects will be able to read files created with the new software.

																		   
#define FS_CFG_DEFAULT_MASK  	( FS_CFG_STORED_COORD_BIT | \
								  FS_CFG_COORD_SYSTEM_BIT | \
								  FS_CFG_DESCR_POS_BIT	 )

It is exactly the same value of all the other projects. 
--------------------------------------------------------------------------- */

/*
 * File Informations structure.
 */
typedef struct
{
	SLong	m_slNumOfItems;			/* Numer of items into the file.	*/
	SWord	m_ssMinutes;			/* File Creation Minute.			*/
	SWord	m_ssHours;				/* File Creation Hour.				*/
	SWord   m_ssYear;				/* File Creation Year.				*/
	SWord   m_ssMonth;				/* File Creation Month.				*/
	SWord   m_ssDay;				/* File Creation Day.				*/

} FS_FILE_INFO, *lpFS_FILE_INFO;




/*****************************************************************************
  External Data definition.
 *****************************************************************************/

														
/* Variables used to store the status */
extern SWord  CmapMeters;			/*	0 = Standard Meters 	  			*/
#define FS_POS_IN_METERS				0
#define FS_POS_IN_METERS_DIV_BY_4		1

extern SWord  WpDescrPosition;		/*  1 = After Wp Name Field   			*/
#define FS_DESCR_POS_AFTER_POINT_YEAR 	0
#define FS_DESCR_POS_AFTER_NAME			1
#define FS_DESCR_POS_NOT_DEFINED		2



									
/*****************************************************************************
  Interface Functions definition section.
 *****************************************************************************/

#ifdef __cplusplus
extern "C"
{
#endif


/*
 * Read one data record form the specified MER file.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H C2P_LoadFileOfMER ( SWord fp, MERFileData *Data );

/*
 * Read one data record from the specified Track file.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H C2P_LoadFileOfTrack ( SWord fp, TrackFileData *Data );

/*
 * Start reading a file already opened for reading operations.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H C2P_Do_Load_Start ( SWord fp, SWord FileType, SWord *Class, SWord *Type );

/*
 * Start reading a file already opened for reading operations 
 * and return info on the file.	
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H C2P_Do_Load_StartEx ( SWord fp, SWord FileType, SWord *Class, SWord *Type, FS_FILE_INFO *lpInfo );

/*
 * Get date and time of file creation.
 */
PRE_EXPORT_H extern SWord IN_EXPORT_H C2P_Get_DateTime ( SWord fp, SWord* Minutes, SWord *Hours, SWord *Year, SWord *Month, SWord *Day ) ;

/*
 *  Initialize one HeaderFileData structure fields.
 */
PRE_EXPORT_H extern void IN_EXPORT_H P2C_InitHeaderFileStructure ( HeaderFileData *Data );

/*
 * Initialize one MERFileData structure fields.	
 */
PRE_EXPORT_H extern void IN_EXPORT_H P2C_InitMERStructure ( MERFileData *Data );

/*
 * Initialize one TrackFileData structure fields.
 */
PRE_EXPORT_H extern void IN_EXPORT_H P2C_InitTrackStructure ( TrackFileData *Data );


#ifdef __cplusplus
}
#endif




/*****************************************************************************
  END of Code.
 *****************************************************************************/

#endif  /* __CDOS2PLT_H__ */

/*------------------------------------------------------------------------
 * $Log: /CplotTools/FileSystem/SourceFiles/H/CDOS2PLT.H $
 * 
 * 10    6-04-01 13:16 Andrea
 * Added support for TRIP DELTA DISTANCE field.
 * 
 * 9     17-10-00 17:50 Andrea
 * The altitude is now saved in (Meters + 500mt). 
 * Added the define to be used to test the Altitude Range:
 * #define USR_TRK_ALT_MIN	   -500
 * #define USR_TRK_ALT_MAX		32267
 * #define USR_TRK_ALT_OFF	    500
 * 
 * 8     5-10-00 13:10 Andrea
 * - Altitude field (in Track Structure) has been changed to Word.
 * - Fixed a spelling error in FS_CFG_DEFUALT_DATA and FS_CFG_DEFUALT_MASK
 * 
 * 7     3-10-00 18:52 Andrea
 * 
 * 6     3-10-00 18:30 Andrea
 * Created two new define to be able to define the size of Name and
 * Description 
 * fields in the High Level:
 * 	MAX_USR_WP_NAME_LEN
 * 	MAX_USR_WP_DESCR_LEN
 * 
 * 5     2-10-00 17:12 Andrea
 * Added new fields into the Track File Structure:
 * 	- Speed (Vehicle Speed: Knots * 10)
 * 	- DateTime (Seconds since 1/1/1970)
 * 	- Altitude(Altitude: Meters * 100)
 * 
 * 4     29-09-00 15:38 Andrea
 * Replaced localdef.h with fflocal.h
 * 
 * 3     22-06-00 17:20 Andrea
 * Created new constant variables to define the size of each field.
 * 
 * 2     23-09-99 13:18 Andrea
 * Moved the LOG to the end of the file.
 * 
 * 1     22-09-99 18:30 Andrea
 * 
 *-----------------------------------------------------------------------*/
