/*...........................................................................*/
/*.                  File Name : WinInwa11nMiy.cpp                          .*/
/*.                                                                         .*/
/*.                       Date : 2010.02.28                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma08bBengali_Font;

ROMDATA PEGUSHORT WinInwa11nMiy_Font_offset_table[225] = {
0x0000,0x0005,0x000b,0x0012,0x0019,0x0028,0x0034,0x003b,0x0042,0x0046,0x004a,0x0051,0x0058,0x0063,0x006a,0x006d,
0x0072,0x0079,0x007f,0x0085,0x008b,0x0091,0x0098,0x009f,0x00a6,0x00ad,0x00b4,0x00bb,0x00be,0x00ca,0x00d1,0x00d8,
0x00db,0x00e7,0x00ee,0x00fa,0x0105,0x010a,0x0111,0x0115,0x011a,0x011e,0x0123,0x0127,0x0129,0x012d,0x0139,0x0140,
0x0147,0x0154,0x015c,0x0163,0x0167,0x016e,0x0171,0x0178,0x0181,0x0188,0x018a,0x0190,0x019b,0x01a7,0x01a9,0x01af,
0x01b9,0x01c0,0x01c6,0x01d1,0x01d8,0x01dd,0x01e4,0x01e8,0x01ed,0x01f1,0x01f8,0x01ff,0x0202,0x0206,0x020c,0x0217,
0x0222,0x0229,0x0234,0x023b,0x023f,0x0249,0x0254,0x025f,0x026a,0x0275,0x027c,0x0283,0x0289,0x0290,0x0292,0x029e,
0x02a6,0x02ae,0x02b6,0x02be,0x02c6,0x02ce,0x02d6,0x02de,0x02e6,0x02ee,0x02f6,0x02fe,0x0306,0x030e,0x0316,0x031e,
0x0326,0x032e,0x0336,0x033e,0x0346,0x034e,0x0356,0x035e,0x0366,0x036e,0x0376,0x037e,0x0386,0x038e,0x0396,0x039e,
0x03a6,0x03ae,0x03b5,0x03bc,0x03c7,0x03cd,0x03d4,0x03dc,0x03e0,0x03e5,0x03ea,0x03f0,0x03f3,0x03fc,0x0403,0x0408,
0x040f,0x0429,0x0435,0x043f,0x0444,0x0449,0x044d,0x0459,0x045d,0x0461,0x0468,0x046e,0x0471,0x0476,0x047d,0x0482,
0x048a,0x0494,0x0499,0x04a4,0x04ae,0x04b4,0x04bc,0x04c1,0x04c8,0x04d1,0x04d6,0x04df,0x04e8,0x04ec,0x04f3,0x04f7,
0x04fb,0x0503,0x0509,0x0514,0x051f,0x052a,0x0535,0x053f,0x0545,0x054c,0x0557,0x0561,0x056c,0x0571,0x057a,0x0583,
0x0587,0x0591,0x059d,0x05a8,0x05b2,0x05b9,0x05c2,0x05c7,0x05ca,0x05d5,0x05da,0x05e7,0x05ef,0x05f3,0x05fb,0x05ff,
0x0603,0x060a,0x0615,0x061e,0x062e,0x0637,0x0640,0x0645,0x064c,0x0653,0x065b,0x0664,0x066c,0x0673,0x067a,0x0687,
0x068e};



ROMDATA PEGUBYTE WinInwa11nMiy_Font_data_table[3360] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf8, 0x01, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 
0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 
0xf9, 0xf9, 0xf9, 0xf9, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x50, 0x00, 0x10, 0x0a, 0x00, 
0x00, 0x02, 0x00, 0x00, 0x14, 0x04, 0xc0, 0x00, 0x00, 0x00, 0x40, 0x00, 0x05, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x04, 
0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x20, 0x32, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x01, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xc1, 0xff, 0x80, 0x3e, 0x00, 0x00, 0x01, 0xff, 0x80, 
0x03, 0x00, 0x60, 0x00, 0x00, 0x03, 0xff, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x71, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x60, 0x0e, 0x60, 0x00, 0x7c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xdf, 0xf9, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x3c, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 
0x31, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x01, 0xfc, 0xfe, 0x20, 0x04, 0x59, 
0x80, 0x07, 0x00, 0x00, 0x1c, 0x03, 0x80, 0x00, 0x06, 0x64, 0x04, 0x00, 0x20, 0x20, 0x20, 0x90, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0x48, 0x21, 0x8a, 
0xac, 0x00, 0x00, 0x40, 0x00, 0x00, 0x50, 0x4c, 0x00, 0x00, 0x01, 0x00, 0x07, 0xc3, 0x82, 0x00, 
0x18, 0x48, 

0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x09, 0x22, 0x00, 0x40, 0x41, 0x00, 0x00, 0x02, 0x00, 0x00, 
0x04, 0x80, 0x40, 0x00, 0x00, 0x04, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x49, 0x00, 0x00, 0x41, 0x00, 0x00, 0x00, 0x90, 0x0a, 0x90, 0x00, 0x82, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x05, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x01, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x49, 0x00, 0x00, 
0x20, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x01, 0x00, 0x80, 0x20, 0x04, 0x52, 
0x40, 0x08, 0x80, 0x00, 0x22, 0x04, 0x40, 0x00, 0x04, 0x94, 0x04, 0x00, 0x20, 0x20, 0x20, 0x90, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 0x02, 0x40, 
0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x08, 0x22, 0x80, 0x00, 
0x24, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x01, 0x02, 0x00, 0x40, 0x41, 0x00, 0x00, 0x02, 0x00, 0x00, 
0x04, 0x80, 0x60, 0x30, 0x10, 0x04, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x41, 0x84, 0x00, 0x41, 0x00, 0x00, 0x00, 0x90, 0x08, 0x10, 0x00, 0x82, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xa0, 0x05, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x7e, 0x38, 0x00, 0x01, 0x00, 0x00, 0x00, 0x30, 0x02, 0x00, 0x00, 0x49, 0x00, 0x00, 
0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x01, 0x00, 0x80, 0x20, 0x04, 0x5a, 
0x40, 0x10, 0x40, 0x00, 0x41, 0x08, 0x20, 0x00, 0x06, 0x94, 0x04, 0x00, 0x20, 0x20, 0x11, 0x1f, 
0x00, 0x6c, 0x20, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x30, 0x42, 0x04, 
0x52, 0xc0, 0x00, 0xe0, 0x00, 0x00, 0x70, 0x38, 0x00, 0x0d, 0x88, 0x40, 0x08, 0x22, 0x08, 0x40, 
0x20, 0x84, 

0x00, 0x00, 0x00, 0x00, 0x04, 0x70, 0x80, 0x00, 0x20, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x01, 0x02, 0x00, 0x00, 0x40, 0x00, 0x00, 0x02, 0x00, 0x00, 
0x03, 0x00, 0x60, 0x30, 0x0c, 0x04, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x20, 0x08, 0x00, 0x40, 0x00, 0x00, 0x00, 0x60, 0x06, 0x10, 0x00, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0xa5, 0x30, 0x00, 0x1f, 0xf0, 0x00, 0x00, 0x30, 0x0e, 0x00, 0x00, 0x31, 0x00, 0x00, 
0x01, 0x03, 0x00, 0x00, 0xc1, 0xc0, 0x00, 0x00, 0x00, 0x40, 0x01, 0x00, 0x80, 0x20, 0x04, 0x59, 
0x80, 0x20, 0x20, 0x00, 0x80, 0x90, 0x10, 0x00, 0x06, 0x64, 0x04, 0x00, 0x20, 0x20, 0x11, 0x10, 
0x80, 0xfe, 0x38, 0x00, 0x98, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x48, 0x41, 0x04, 
0x4c, 0xc0, 0x01, 0x10, 0x00, 0x00, 0x88, 0x44, 0x00, 0x09, 0x88, 0x40, 0x08, 0x01, 0x08, 0x40, 
0x10, 0x84, 

0x00, 0x00, 0x00, 0x3f, 0x4a, 0x49, 0x00, 0x00, 0x20, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x01, 0x02, 0x00, 0x00, 0x40, 0x00, 0x00, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x08, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x03, 
0xc0, 0x00, 0xe6, 0xb8, 0x00, 0x01, 0x00, 0x00, 0x00, 0x30, 0x34, 0x10, 0x00, 0x01, 0x00, 0x00, 
0x02, 0x07, 0x00, 0x01, 0xc1, 0xc0, 0x00, 0x00, 0x00, 0x7e, 0x01, 0xf8, 0xfc, 0x20, 0x04, 0x40, 
0x00, 0x20, 0x20, 0x00, 0x80, 0x90, 0x10, 0x00, 0x00, 0x04, 0x04, 0x00, 0x20, 0x20, 0x0a, 0x10, 
0x40, 0xfe, 0x14, 0x00, 0xd8, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x84, 0x40, 0x84, 
0x40, 0x00, 0x02, 0x08, 0x00, 0x01, 0x04, 0x82, 0x00, 0x0c, 0x08, 0x40, 0x08, 0x00, 0x08, 0x40, 
0x08, 0x84, 

0x03, 0x87, 0x1c, 0x4c, 0xd2, 0x32, 0x07, 0x8f, 0x20, 0x8e, 0x08, 0x4b, 0xc0, 0x03, 0x0e, 0x38, 
0x13, 0x8e, 0x1c, 0x58, 0x70, 0xe1, 0xc1, 0x1a, 0x00, 0x00, 0x40, 0x4e, 0xb8, 0x52, 0x00, 0x38, 
0xe0, 0x1c, 0x00, 0x00, 0x01, 0x54, 0x00, 0x40, 0x38, 0x75, 0xc0, 0x20, 0x40, 0x00, 0x1c, 0x01, 
0x1c, 0x1c, 0x8b, 0x87, 0xc8, 0x10, 0x88, 0x40, 0x71, 0xce, 0x1c, 0x01, 0xc0, 0x10, 0x1c, 0x80, 
0x01, 0xe7, 0x38, 0x73, 0x0e, 0x39, 0xc2, 0x85, 0xee, 0x39, 0xc7, 0xb8, 0xe7, 0x1c, 0xe1, 0x43, 
0x8e, 0x1c, 0x20, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0xe0, 0x07, 0x38, 0xe3, 0x80, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x01, 0x55, 0x22, 0x62, 0x01, 0x00, 0x00, 0x00, 0x10, 0x48, 0x00, 0x3c, 0x79, 0x00, 0xe0, 
0x04, 0x0f, 0x80, 0x23, 0x87, 0xf2, 0xa0, 0x00, 0x00, 0x40, 0x01, 0x00, 0x80, 0x23, 0x84, 0x40, 
0x00, 0xa0, 0x27, 0x38, 0x80, 0x90, 0x10, 0x00, 0xe0, 0x04, 0x04, 0xe0, 0x20, 0x20, 0x04, 0x10, 
0x44, 0x7c, 0x0b, 0x00, 0xf0, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0xfc, 0x4e, 0x44, 
0x40, 0x1c, 0xe2, 0x08, 0xe6, 0x31, 0x04, 0x82, 0x00, 0x80, 0x08, 0x40, 0x08, 0x03, 0x84, 0x9c, 
0xe4, 0x48, 

0x04, 0x48, 0xa0, 0x48, 0xd2, 0x04, 0xc8, 0xc8, 0xa0, 0x91, 0x08, 0x84, 0x20, 0x03, 0x11, 0x44, 
0x14, 0x51, 0x3a, 0x58, 0x89, 0x33, 0x21, 0x1a, 0x00, 0x1f, 0x40, 0x51, 0x44, 0xaa, 0x00, 0x0a, 
0x10, 0x10, 0x00, 0x00, 0x01, 0x54, 0x00, 0x40, 0x44, 0x8a, 0x20, 0x20, 0x40, 0x00, 0x22, 0x01, 
0x22, 0x22, 0x8c, 0xcc, 0xa8, 0x10, 0x50, 0x40, 0xca, 0x73, 0x26, 0x01, 0x00, 0x10, 0x20, 0x80, 
0x01, 0x38, 0x84, 0x88, 0x95, 0x4e, 0x64, 0x45, 0x11, 0x66, 0x68, 0xc5, 0x18, 0xa3, 0x32, 0x25, 
0x59, 0x22, 0x20, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x00, 0x50, 0x0c, 0xc5, 0x14, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0xbd, 0x05, 0xa2, 0x01, 0x00, 0x00, 0x00, 0x11, 0x88, 0x00, 0x0a, 0x01, 0x01, 0x10, 
0x04, 0x0f, 0x80, 0x2f, 0x07, 0xf1, 0xc0, 0x00, 0x00, 0x40, 0x01, 0x00, 0x80, 0x24, 0x44, 0x40, 
0x00, 0xa0, 0x28, 0xc4, 0x80, 0x90, 0x10, 0x00, 0x10, 0x04, 0x05, 0x10, 0x20, 0x20, 0x04, 0x10, 
0x84, 0x78, 0x08, 0xc0, 0x70, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x53, 0x44, 
0x40, 0x22, 0x12, 0x09, 0x11, 0x09, 0x04, 0x82, 0x00, 0x80, 0x08, 0x40, 0x08, 0x00, 0x44, 0xb3, 
0x14, 0x48, 

0x00, 0x4a, 0xa0, 0x4b, 0x4a, 0x05, 0x2c, 0x43, 0x20, 0x91, 0x3e, 0x84, 0x2f, 0x83, 0x11, 0x04, 
0x10, 0x50, 0x2a, 0x44, 0x89, 0xd2, 0x21, 0x02, 0x00, 0x00, 0x40, 0x51, 0x84, 0x8a, 0x00, 0x32, 
0x10, 0x3c, 0x00, 0x00, 0x01, 0x54, 0x00, 0x40, 0x04, 0x8c, 0x20, 0x20, 0x40, 0x00, 0x20, 0x01, 
0x20, 0x2e, 0x88, 0x4b, 0xa8, 0x20, 0x20, 0x40, 0xb8, 0x21, 0x3a, 0x03, 0xe0, 0x10, 0x20, 0x80, 
0x02, 0x10, 0x84, 0x08, 0x95, 0x74, 0x25, 0x44, 0x61, 0x44, 0x28, 0x45, 0x08, 0xa2, 0x12, 0x25, 
0x57, 0x20, 0x20, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x90, 0x08, 0x85, 0x04, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x97, 0x85, 0x2e, 0x01, 0x00, 0x00, 0x00, 0x13, 0xf0, 0x00, 0x3e, 0x01, 0x79, 0x90, 
0x00, 0x07, 0x00, 0x3e, 0x03, 0xf3, 0xf0, 0x00, 0x00, 0x40, 0x01, 0x00, 0x80, 0x20, 0x44, 0x40, 
0x00, 0x90, 0x40, 0x84, 0x41, 0x08, 0x20, 0x00, 0x10, 0x04, 0x04, 0x10, 0x20, 0x20, 0x04, 0x1f, 
0x04, 0x38, 0x07, 0xe0, 0xf0, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x59, 0x44, 
0x40, 0x02, 0x12, 0x08, 0x11, 0x09, 0x04, 0x82, 0x03, 0xe0, 0x08, 0x40, 0x08, 0x00, 0x45, 0x21, 
0x14, 0x50, 

0x00, 0x4d, 0xb0, 0x01, 0x44, 0x09, 0x2a, 0x40, 0xa0, 0x91, 0x08, 0xce, 0x60, 0x33, 0x11, 0x04, 
0x10, 0x50, 0x3a, 0x64, 0x09, 0x52, 0xc1, 0x1a, 0x00, 0x1f, 0x40, 0x51, 0x44, 0x8a, 0x00, 0x23, 
0x30, 0x2a, 0x00, 0x00, 0x01, 0x54, 0x00, 0x40, 0x04, 0x8a, 0x20, 0x20, 0x40, 0x00, 0x20, 0x01, 
0x30, 0x2c, 0x88, 0x4c, 0xa8, 0x20, 0x50, 0x40, 0xc8, 0x21, 0x2a, 0x02, 0xa0, 0x10, 0x20, 0x80, 
0x00, 0x10, 0xcc, 0x98, 0x99, 0x56, 0x66, 0xc4, 0x1f, 0x44, 0x28, 0x45, 0x98, 0xa3, 0x32, 0x26, 
0xd9, 0x30, 0x20, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 
0x09, 0x09, 0x09, 0x09, 0x08, 0x01, 0x10, 0x08, 0x85, 0x04, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x19, 0x45, 0x4f, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x02, 0x2e, 0x01, 0x01, 0x50, 
0x00, 0x03, 0x00, 0x3c, 0x00, 0x41, 0xc0, 0x00, 0x00, 0x40, 0x01, 0x00, 0x80, 0x20, 0xc4, 0x40, 
0x00, 0x88, 0x81, 0x8c, 0x22, 0x04, 0x40, 0x01, 0x10, 0x02, 0x08, 0x30, 0x10, 0x40, 0x04, 0x10, 
0x04, 0x10, 0x07, 0xc1, 0xf8, 0x08, 0x00, 0x00, 0x01, 0x80, 0x00, 0x20, 0x00, 0x44, 0x55, 0x44, 
0x40, 0x03, 0x31, 0x11, 0x31, 0x98, 0x88, 0x44, 0x00, 0x80, 0x04, 0xc0, 0x08, 0x00, 0x43, 0x21, 
0x14, 0x30, 

0x07, 0x87, 0x1e, 0x01, 0x40, 0x10, 0xc6, 0x4f, 0x20, 0x88, 0x08, 0x7b, 0xc0, 0x33, 0x0e, 0x38, 
0x17, 0x8f, 0x02, 0x38, 0x10, 0xe2, 0xc1, 0x1a, 0x00, 0x00, 0x40, 0x4d, 0xd8, 0x72, 0x00, 0x1c, 
0xe0, 0x1e, 0x00, 0x00, 0x01, 0x54, 0x00, 0x40, 0x78, 0x7e, 0xc0, 0x20, 0x40, 0x00, 0x1e, 0x01, 
0x1e, 0x1c, 0x71, 0x87, 0x28, 0x20, 0x88, 0x40, 0x72, 0x62, 0x1c, 0x03, 0xa0, 0x10, 0x1c, 0x80, 
0x00, 0xef, 0x38, 0x77, 0x0e, 0x39, 0xc3, 0x95, 0xfe, 0x24, 0xc6, 0x38, 0xf3, 0x1c, 0xe1, 0xc3, 
0x8e, 0x1e, 0x20, 0x01, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf8, 0x01, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 
0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 0xf9, 
0xf9, 0xf9, 0xf9, 0xf9, 0xf8, 0x00, 0xe0, 0x04, 0x0c, 0xf7, 0xc0, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x00, 0x01, 0x19, 0xc5, 0x72, 0x1f, 0xf0, 0x00, 0x00, 0x00, 0x78, 0x03, 0x1c, 0x01, 0x00, 0xd0, 
0x04, 0x02, 0x00, 0x18, 0x01, 0xe0, 0xa0, 0x00, 0x00, 0x7f, 0x01, 0xfc, 0xfe, 0x27, 0x84, 0x40, 
0x00, 0x87, 0x0f, 0x78, 0x1c, 0x03, 0x80, 0x01, 0xe0, 0x01, 0xf1, 0xe0, 0x0f, 0x80, 0x04, 0x10, 
0x04, 0x10, 0x0f, 0x01, 0x90, 0x3e, 0x00, 0x00, 0x00, 0x80, 0x00, 0x20, 0x00, 0x38, 0x4d, 0x44, 
0x40, 0x3c, 0xe0, 0xe0, 0xee, 0x70, 0x70, 0x38, 0x00, 0x80, 0x03, 0x40, 0x08, 0x07, 0x82, 0x12, 
0x14, 0x20, 

0x04, 0x00, 0x22, 0x01, 0xc0, 0x00, 0x00, 0x40, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x12, 0x02, 0x02, 0x00, 0x20, 0x02, 0x00, 0x02, 0x03, 0x80, 0x46, 0x02, 0x00, 0x02, 0x00, 0x00, 
0x00, 0x01, 0x03, 0x01, 0x41, 0x54, 0x00, 0x40, 0x40, 0x00, 0x00, 0xa6, 0x48, 0x30, 0x22, 0x19, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x01, 0xc4, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x81, 
0x30, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x22, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x40, 0x04, 0x00, 0x1a, 0x40, 0x00, 
0x00, 0x00, 0xd9, 0x82, 0x80, 0x00, 0x00, 0x00, 0x00, 0x30, 0x1c, 0x00, 0x86, 0x01, 0x00, 0x10, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x0c, 0x80, 0x08, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x20, 0x08, 0x00, 0x01, 0x40, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x44, 0x42, 0x00, 
0x14, 0x20, 

0x07, 0x80, 0x3c, 0x00, 0xc0, 0x00, 0x00, 0x40, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x12, 0x02, 0x22, 0x00, 0x20, 0x02, 0x20, 0x02, 0x04, 0x40, 0x49, 0x03, 0xf0, 0x02, 0x00, 0x40, 
0x00, 0x00, 0x04, 0x81, 0x41, 0x54, 0x00, 0xc1, 0x40, 0x00, 0x01, 0xa9, 0x48, 0x4b, 0x3c, 0x25, 
0x3a, 0xc0, 0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x80, 0x06, 0x00, 0x83, 
0x30, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1e, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xb7, 0xf0, 0x13, 0xc7, 0xec, 0xf7, 0x9a, 0x4f, 0xc0, 
0x12, 0x00, 0xae, 0x80, 0x00, 0x00, 0x04, 0x61, 0xef, 0x00, 0x06, 0x03, 0x00, 0x01, 0x00, 0x13, 
0x00, 0x00, 0x0b, 0x00, 0x00, 0x00, 0x07, 0xef, 0x7f, 0x00, 0x78, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x1a, 0x80, 0x08, 0x00, 0x00, 0x00, 0x07, 0xf1, 0x60, 0x00, 0x01, 0x00, 0x00, 0x09, 0x00, 0x00, 
0x04, 0x00, 0x18, 0x00, 0x00, 0x00, 0x3b, 0xbf, 0x3d, 0x00, 0x03, 0x20, 0x18, 0x00, 0x1d, 0x40, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x0f, 0xc8, 0xc3, 0xcc, 0x1f, 
0x14, 0xc0, 

0x00, 0x80, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x40, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x91, 0x04, 0x26, 0x00, 0x20, 0x03, 0x20, 0x02, 0x04, 0x40, 0x49, 0x04, 0xc0, 0x02, 0x00, 0x40, 
0x00, 0x00, 0x04, 0x81, 0x41, 0x54, 0x00, 0xc1, 0x40, 0x00, 0x0a, 0xa9, 0x49, 0xcb, 0x22, 0xe5, 
0x22, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x80, 0x06, 0x00, 0x83, 
0x30, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x32, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x94, 0x90, 0x12, 0xe9, 0x10, 0xf6, 0x9a, 0x52, 0x20, 
0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x91, 0x02, 0x00, 0x02, 0x00, 0x00, 0x01, 0x00, 0x04, 
0x80, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x09, 0x1d, 0x09, 0x00, 0x68, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x1a, 0x80, 0x08, 0x00, 0x00, 0x00, 0x04, 0xc8, 0xe0, 0x00, 0x01, 0x00, 0x00, 0x09, 0x00, 0x00, 
0x04, 0x00, 0x20, 0x00, 0x00, 0x00, 0x34, 0xc8, 0xac, 0x00, 0x07, 0xa0, 0x18, 0x00, 0x10, 0x40, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb0, 0x00, 0x00, 0x12, 0x28, 0xc6, 0x40, 0x21, 
0x14, 0x00, 

0x00, 0x80, 0x03, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xe0, 0x88, 0x1c, 0x00, 0x30, 0x01, 0xc0, 0x01, 0xff, 0x80, 0x3e, 0x04, 0xc0, 0x01, 0xff, 0xc0, 
0x00, 0x00, 0x03, 0x03, 0x61, 0xdb, 0xff, 0xbe, 0x7c, 0x00, 0x06, 0x67, 0xd8, 0x30, 0x1e, 0x3f, 
0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x7d, 
0xb8, 0x0f, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3e, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x70, 0x10, 0x13, 0xc7, 0xe0, 0xf7, 0x3b, 0x4f, 0xc0, 
0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x91, 0xef, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x04, 
0x80, 0x00, 0x07, 0x00, 0x00, 0x00, 0x07, 0x2e, 0x73, 0x00, 0x30, 0x00, 0x00, 0x04, 0x00, 0x00, 
0x0f, 0x80, 0x0f, 0xfc, 0x00, 0x00, 0x07, 0xf8, 0xc0, 0x00, 0x01, 0xff, 0x80, 0x07, 0x00, 0x00, 
0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xb9, 0x18, 0x3f, 0xff, 0x9f, 0xf4, 0x00, 0x1f, 0xc0, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x12, 0xc7, 0x37, 0xc0, 0x3f, 
0xf4, 0x00, 

0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 


};

xFONTYY WinInwa11nMiy_Font = {0x01, 16, 0, 16, 0, 0, 16,210, 0x1020, 0x10ff,
(PEGUSHORT *) WinInwa11nMiy_Font_offset_table, &Tahoma08bBengali_Font,
(PEGUBYTE *) WinInwa11nMiy_Font_data_table};


