/*...........................................................................*/
/*.                  File Name : SYSSDCD.C                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.09                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysGPIO.h"
#include "SdCardDRV.h"
#include "SysSDCD.h"

#include <string.h>
#include <stdlib.h>

//=============================================================================
#if defined(__N500_MODEL__)
      static xSYS_SDCD *G_pSysSDCD = (xSYS_SDCD *)SD1_PHSY_BASE_ADDR;
#else
      static xSYS_SDCD *G_pSysSDCD = (xSYS_SDCD *)SD0_PHSY_BASE_ADDR;
#endif
//=============================================================================
static xSDCARD G_xSdCard = {0,};
static int     G_bHighCapacity = 0;

//=============================================================================
#define SDCD_POLLING_INTERRUPT_MODE             1
//=============================================================================
UCHAR  G_vFullSdCardCidCode[16] = {0x00,};
//=============================================================================

void  SysInitSDCD(void)
{
	int  nNavisType = SysGetNavisModelType();

	if (nNavisType == NAVIS_TYPE_700 || nNavisType == NAVIS_TYPE_800 || nNavisType == NAVIS_TYPE_3800)
		G_pSysSDCD = (xSYS_SDCD *)SD1_PHSY_BASE_ADDR;
	else
		G_pSysSDCD = (xSYS_SDCD *)SD0_PHSY_BASE_ADDR;

	#if defined(__N500_MODEL__)
		G_pSysSDCD = (xSYS_SDCD *)SD1_PHSY_BASE_ADDR;
	#endif
                                          // ============
      SdCardSetSdCardPtr(&G_xSdCard);     // SdCardDRV.c
                                          // ============

      SysSetSdCdClockPClkMode(SDCD_PCLKMODE_ENABLE);
      SysSetSdCdClockSource(SDCD_CLK_SOURCE_SELECT);
      SysSetSdCdClockDivisor(SDCD_CLK_DIVISOR);
      SysSetSdCdClockDivisorEnable(1);
      SysSetSdCdClockDivider(SDCD_SET_CLK_DIVIDER_400KHZ);
      SysSetSdCdClockDividerEnable(1);

      SysReSetSdCdCTRL();                 // Reset the controller.
      while (SysIsSdCdResetCTRL());       // Wait until the controller reset is completed.

      SysReSetSdCdDMA();                  // Reset the DMA interface.
      while (SysIsSdCdResetDMA());        // Wait until the DMA reset is completed.

      SysReSetSdCdFIFO();                 // Reset the FIFO.
      while (SysIsSdCdResetFIFO());       // Wait until the FIFO reset is completed.

      SysSetSdCdDMAMode(SDCD_MODE_POLLING);
      SysSetSdCdDataTimeOut(0x00FFFFFF);

      SysSetSdCdResponseTimeOut(0xff);    // 0x64
      SysSetSdCdDataBusWidth(SDCD_DATA_WIDTH_1BIT);
      SysSetSdCdBlockSize(SDCD_BLOCK_LENGTH);


      SysSetSdCdFIFORxThreshold(8 - 1);   // Issue when RX FIFO Count >= 8 x 4 bytes
      SysSetSdCdFIFOTxThreshold(8 - 0);   // Issue when TX FIFO Count <= 8 x 4 bytes

      SysSetSdCdInterruptEnableAll(0);
      SysClearSdCdInterruptPendingAll();
}
DWORD SysSdCardReadBlockData(DWORD dBlockNo,void *pReadAddr,DWORD dReadSize)
{
      DWORD dSectorNo   = dBlockNo;
      DWORD dNoOfSector = dReadSize / SDCD_BLOCK_LENGTH;
      DWORD *pBuffAddr  = (DWORD *)pReadAddr;
      DWORD *pAlgnBuff  = NULL;
      DWORD dCommand,dArgument,dStatus;
      DWORD dCount,dTempX,dResponse;

      if ((DWORD)pBuffAddr & 0x000003)
         {
          pAlgnBuff = (DWORD *)malloc(dReadSize + SDCD_BLOCK_LENGTH);
          pBuffAddr = pAlgnBuff;
//        memcpy(pBuffAddr,pReadAddr,dReadSize);
         }
      do
        {
         dStatus = SysSendSdCardCommand(SDCD_CMD_SEND_STATUS_13,SysGetSdCardRCA());
         if (dStatus != SDCD_STATUS_NOERROR)
            {
             SysSdCardReadExitError(pAlgnBuff);
             return(SDCD_STATUS_ERROR);
            }
//      } while (!(G_xSdCard.m_vRspRegX[0]  & (1 <<  8)));   // wait until 'Ready for data' is set
        } while (!((G_xSdCard.m_vRspRegX[0] & (1 <<  8)) && (((G_xSdCard.m_vRspRegX[0] >> 9) & 0xF) == 4)));

      dCount = dNoOfSector * SDCD_BLOCK_LENGTH;
      SysSetSdCdByteCount(dCount);

      dCommand = (dNoOfSector > 1) ? SDCD_CMD_READ_MULTI_BLOCK_18 : SDCD_CMD_READ_SINGLE_BLOCK_17;
      dArgument= (G_bHighCapacity) ? dSectorNo : dSectorNo * SDCD_BLOCK_LENGTH;
//    dArgument= dSectorNo * SDCD_BLOCK_LENGTH;

      dStatus = SysSendSdCardCommand(dCommand,dArgument);
      if (dStatus != SDCD_STATUS_NOERROR)
         {
          SysSdCardReadExitError(pAlgnBuff);
          return(SDCD_STATUS_ERROR);
         }

#ifdef SDCD_POLLING_INTERRUPT_MODE
      while (1)
            {
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_RXDR))
                {
                 SysReadSdCdData32(pBuffAddr);
                 pBuffAddr += (32/4);
                 SysClearSdCdInterruptPendingOne(SDCD_INT_RXDR);
                 dCount -= 32;
                 if (dCount == 0)
                     break;
                }
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_DTO))
                {
                 if (dCount >= 64)
                    {
//                   while (1);
                     SysClearSdCdInterruptPendingOne(SDCD_INT_DTO);
                     SysReSetSdCdFIFO();
                     SysSdCardReadExitError(pAlgnBuff);
                     return(SDCD_STATUS_ERROR);
                    }
                 break;
                }
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_FRUN))
                {
                 SysClearSdCdInterruptPendingOne(SDCD_INT_FRUN);
                 SysSdCardReadExitError(pAlgnBuff);
                 return(SDCD_STATUS_ERROR);
                }
            }
#else
#endif	

      // Check Errors
      if (SysGetSdCdInterruptPending32() & ((1 << SDCD_INT_DRTO) |
                                            (1 << SDCD_INT_EBE)  |
                                            (1 << SDCD_INT_SBE)  |
                                            (1 << SDCD_INT_DCRC)))
         {
          SysSdCardReadExitError(pAlgnBuff);
          return(SDCD_STATUS_ERROR);
         }	
      if (SysGetSdCdInterruptPendingOne(SDCD_INT_DTO) == 0)
         {
         }
      if (dCount > 0)
         {
          dTempX = SysGetSdCdFIFOCount() * 4;
          if ((dCount == 32 || dCount == 64) &&
              (dTempX == 32 || dTempX == 64))
             {
              SysReadSdCdData32(pBuffAddr);
              pBuffAddr += (32/4);
              dCount -= 32;
             }
          dTempX = SysGetSdCdFIFOCount() * 4;
          if (dCount == 32 && dTempX == 32)
             {
              SysReadSdCdData32(pBuffAddr);
              pBuffAddr += (32/4);
              dCount -= 32;
             }
         }
#ifdef SDCD_POLLING_INTERRUPT_MODE
      if (dNoOfSector > 1)
         {
          while (SysGetSdCdInterruptPendingOne(SDCD_INT_ACD) == 0);
          dResponse = SysGetSdCdAutoStopResponse();
          if (dResponse & 0xFDF98008)
             {
              SysSdCardReadExitError(pAlgnBuff);
              return(SDCD_STATUS_ERROR);
             }
         }
#else
#endif	

      if (dCount > 0)
         {		
          SysSdCardReadExitError(pAlgnBuff);
          return(SDCD_STATUS_ERROR);
         }

      if (pAlgnBuff != NULL)
         {
          memmove(pReadAddr,pAlgnBuff,dReadSize);
          free(pAlgnBuff);
         }

      return(SDCARD_ERROR_NO_ERROR);
}
void  SysSdCardReadExitError(DWORD *pAlgnBuff)
{
      if (pAlgnBuff != NULL)
          free(pAlgnBuff);
      SysSendSdCardCommand(SDCD_CMD_STOP_TRANSMISSION_12,0x00000000);
      if (SysIsSdCdFIFOEmpty() == 0)
         {
          SysReSetSdCdFIFO();                     // Reest the FIFO.
          while (SysIsSdCdResetFIFO());           // Wait until the FIFO reset is completed.
         }
}
DWORD SysSdCardWriteBlockData(DWORD dBlockNo,void *pWritedAddr,DWORD dWriteSize)
{
      DWORD dSectorNo   = dBlockNo;
      DWORD dNoOfSector = dWriteSize / SDCD_BLOCK_LENGTH;
      DWORD *pBuffAddr  = (DWORD *)pWritedAddr;
      DWORD *pAlgnBuff  = NULL;
      DWORD dCommand,dArgument,dStatus;
      DWORD dCount,dResponse;

      if ((DWORD)pBuffAddr & 0x000003)
         {
          pAlgnBuff = (DWORD *)malloc(dWriteSize + SDCD_BLOCK_LENGTH);
          pBuffAddr = pAlgnBuff;
          memcpy(pBuffAddr,pWritedAddr,dWriteSize);
         }
      do
        {
         dStatus = SysSendSdCardCommand(SDCD_CMD_SEND_STATUS_13,SysGetSdCardRCA());
         if (dStatus != SDCD_STATUS_NOERROR)
            {
             SysSdCardWriteExitError(pAlgnBuff);
             return(SDCD_STATUS_ERROR);
            }
//      } while (!(G_xSdCard.m_vRspRegX[0]  & (1 <<  8)));   // wait until 'Ready for data' is set
        } while (!((G_xSdCard.m_vRspRegX[0] & (1 <<  8)) && (((G_xSdCard.m_vRspRegX[0] >> 9) & 0xF) == 4)));

      dCount = dNoOfSector * SDCD_BLOCK_LENGTH;
      SysSetSdCdByteCount(dCount);

      dCommand = (dNoOfSector > 1) ? SDCD_CMD_WRITE_MULTI_BLOCK_25 : SDCD_CMD_WRITE_SINGLE_BLOCK_24;
      dArgument= (G_bHighCapacity) ? dSectorNo : dSectorNo * SDCD_BLOCK_LENGTH;
//    dArgument= dSectorNo * SDCD_BLOCK_LENGTH;

      dStatus = SysSendSdCardCommand(dCommand,dArgument);
      if (dStatus != SDCD_STATUS_NOERROR)
         {
          SysSdCardWriteExitError(pAlgnBuff);
          return(SDCD_STATUS_ERROR);
         }

#ifdef SDCD_POLLING_INTERRUPT_MODE
      while (1)
            {
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_TXDR))
                {
                 SysWriteSdCdData32(pBuffAddr);
                 pBuffAddr += (32/4);
                 SysClearSdCdInterruptPendingOne(SDCD_INT_TXDR);
                 dCount -= 32;
                 if (dCount == 0)
                     break;
                }
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_DTO))
                {
                 SysClearSdCdInterruptPendingOne(SDCD_INT_DTO);
                 SysReSetSdCdFIFO();
//               while (1);
                 SysSdCardWriteExitError(pAlgnBuff);
                 return(SDCD_STATUS_ERROR);
                }
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_HTO))
                 SysClearSdCdInterruptPendingOne(SDCD_INT_HTO);
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_FRUN))
                {
                 SysClearSdCdInterruptPendingOne(SDCD_INT_FRUN);
//               SysSdCardWriteExitError(pAlgnBuff);
//               return(SDCD_STATUS_ERROR);
                }
            }
#else	
#endif

      while (SysGetSdCdInterruptPendingOne(SDCD_INT_DTO) == 0);

      // Check Errors
      if (SysGetSdCdInterruptPending32() & ((1 << SDCD_INT_DRTO) |
                                            (1 << SDCD_INT_EBE)  |
                                            (1 << SDCD_INT_SBE)  |
                                            (1 << SDCD_INT_DCRC)))
         {
          SysSdCardWriteExitError(pAlgnBuff);
          return(SDCD_STATUS_ERROR);
         }	

#ifdef SDCD_POLLING_INTERRUPT_MODE
      if (dNoOfSector > 1)
         {
          while (SysGetSdCdInterruptPendingOne(SDCD_INT_ACD) == 0);
          dResponse = SysGetSdCdAutoStopResponse();
          if (dResponse & 0xFDF98008)
             {
              SysSdCardWriteExitError(pAlgnBuff);
              return(SDCD_STATUS_ERROR);
             }
         }	
#else
#endif	

      if (dCount > 0)
         {		
          SysSdCardReadExitError(pAlgnBuff);
          return(SDCD_STATUS_ERROR);
         }

      if (pAlgnBuff != NULL)
          free(pAlgnBuff);

      return(SDCD_STATUS_NOERROR);
}
void  SysSdCardWriteExitError(DWORD *pAlgnBuff)
{
      if (pAlgnBuff != NULL)
          free(pAlgnBuff);
      SysSendSdCardCommand(SDCD_CMD_STOP_TRANSMISSION_12,0x00000000);
      if (SysIsSdCdFIFOEmpty() == 0)
         {
          SysReSetSdCdFIFO();                     // Reest the FIFO.
          while (SysIsSdCdResetFIFO());           // Wait until the FIFO reset is completed.
         }
}
DWORD SysSdCardSetUpAndDetect(void)
{
      int   i;
//    DWORD dGetReg;
//    DWORD dCmdArg;
//    DWORD dOldIdx;
      DWORD dHCS;
      DWORD dTimeStart;
      UCHAR vData[8192];

      G_bHighCapacity = 0;

      SysSetSdCdDataBusWidth(SDCD_DATA_WIDTH_1BIT);
      if (SysSetSdCardClock(SDCD_SET_CLK_DIVIDER_400KHZ) != SDCD_STATUS_NOERROR)
          return(SDCD_STATUS_ERROR);
//++  SysSetSdCdClockDivider(SDCD_SET_CLK_DIVIDER_400KHZ);

      SysSetSdCardRCA(0x00000000);

      //--------------------------------------------------------------------------
      // Identify SDIO card
      //--------------------------------------------------------------------------
      //	if( MES_SDCARD_STATUS_NOERROR == SendCommand( IO_SEND_OP_COND, 0 ) )		
      //		return(SDCD_STATUS_ERROR);    // Don't support SDIO

      //--------------------------------------------------------------------------
      // 	Identify SD/MMC card
      //--------------------------------------------------------------------------
      // Go idle state
      SysSendSdCardCommand(SDCD_CMD_GO_IDLE_STATE_00,0x00000000);
      SysSendSdCardCommand(SDCD_CMD_GO_IDLE_STATE_00,0x00000000);
      SysSendSdCardCommand(SDCD_CMD_GO_IDLE_STATE_00,0x00000000);

      // argument = VHS : 2.7~3.6V and Check Pattern(0xAA)
      if (SysSendSdCardCommand(SDCD_CMD_IF_COND_08,(1 << 8) | 0xAA) == SDCD_STATUS_NOERROR)
         {
          // Ver 2.0 or later SD Memory Card
          if (G_xSdCard.m_vRspRegX[0] != ((1 << 8) | 0xAA))
              return(SDCD_STATUS_ERROR);

          dHCS = 1 << 30;
         }
      else
         {
          // voltage mismatch or Ver 1.X SD Memory Card or not SD Memory Card
          dHCS = 0;
         }

       //--------------------------------------------------------------------------
       // voltage validation

      if (dHCS)
         {
          for (i = 0;i < 1000;i++)
              {
               // argument = 3.0 ~ 3.6V
               if (SysSendSdCardAppCommand(SDCD_ACMD_SD_APP_OP_COND_41,(dHCS | 0x00FC0000)) != SDCD_STATUS_NOERROR)
                   return(SDCD_STATUS_ERROR);

               if ((G_xSdCard.m_vRspRegX[0] & (1 << 31)) != 0)
                   break;                 // Wait until card has finished the power up routine
               SysDelayMiliSec(5);
              }
          G_bHighCapacity = (G_xSdCard.m_vRspRegX[0] & (1 << 30)) ? 1 : 0;
         }
      else
         {
          SysSendSdCardCommand(SDCD_CMD_GO_IDLE_STATE_00,0x00000000);
          SysSendSdCardCommand(SDCD_CMD_GO_IDLE_STATE_00,0x00000000);
          SysSendSdCardCommand(SDCD_CMD_GO_IDLE_STATE_00,0x00000000);

          dTimeStart = SysGetSystemTimer();
          while (1)
                {
                 if (SysSendSdCardAppCommand(SDCD_ACMD_SD_APP_OP_COND_41,0x00ff8000) != SDCD_STATUS_NOERROR)
                     return(SDCD_STATUS_ERROR);
                 else
                    {
                     if (G_xSdCard.m_vRspRegX[0] & 0x80000000)    // Card-Power-Up-Status Ready
                         break;
                    }
                 if (SysGetDiffTimeMili(dTimeStart) >= 2000)
                     return(SDCD_STATUS_ERROR);
                 SysDelayMiliSec(10);
                }
          G_bHighCapacity = 0;
         }

      //--------------------------------------------------------------------------
      // Get CID
      if (SysSendSdCardCommand(SDCD_CMD_ALL_SEND_CID_02,0x00000000) != SDCD_STATUS_NOERROR)
         {
//        return(SDCD_STATUS_ERROR);
         }

//    SDMMC_SD_DecodeCID(&(m_CIS.CID));

      // Show some CID information
//    RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("Manufacture ID     : %s\r\n"), m_CIS.CID.PNM));
//    RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("Manufacturing Date : Year %d, Month %d\r\n"),
//                                     2000 + ((m_CIS.CID.dwMDT>>4)&0xFF), m_CIS.CID.dwMDT & 0xF));

      //--------------------------------------------------------------------------
      // Get RCA and change to Stand-by State in data transfer mode
      if (SysSendSdCardCommand(SDCD_CMD_SEND_RELATIVE_ADDR_03,0x00000000) != SDCD_STATUS_NOERROR)
          return(SDCD_STATUS_ERROR);
      SysSetSdCardRCA(G_xSdCard.m_vRspRegX[0]);

      if (G_xSdCard.m_vRspRegX[0] & 0x0000E008)                // Check Status Error
          return(SDCD_STATUS_ERROR);

      //--------------------------------------------------------------------------
      // Get CSD
      if (SysSendSdCardCommand(SDCD_CMD_SEND_CSD_09,SysGetSdCardRCA()) != SDCD_STATUS_NOERROR)
          return(SDCD_STATUS_ERROR);

      // Call DecodeCSD function: decode from long response data
//    SDMMC_SD_DecodeCSD(&(m_CIS.CSD));

//    RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("CSD READ_BL_LEN: %d\r\n"), m_CIS.CSD.byREAD_BL_LEN));
//    RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("CSD C_SIZE     : %d\r\n"), m_CIS.CSD.wC_SIZE));
//    RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("CSD C_SIZE_MULT: %d\r\n"), m_CIS.CSD.byC_SIZE_MULT));


      if (SysSetSdCardClock(SDCD_RUN_CLK_DIVIDER_25MHZ) != SDCD_STATUS_NOERROR)
          return(SDCD_STATUS_ERROR);
//++  SysSetSdCdClockDivider(SDCD_RUN_CLK_DIVIDER_25MHZ);

      if (SdCardSendSelectCard() != SDCD_STATUS_NOERROR)
          return(SDCD_STATUS_ERROR);

      // data transfer mode : Transfer state
      if (SdCardSendPullUpMode(0) != SDCD_STATUS_NOERROR)                   return(SDCD_STATUS_ERROR);
      if (SdCardSendBusWidth(SDCD_DATA_WIDTH_4BIT) != SDCD_STATUS_NOERROR)  return(SDCD_STATUS_ERROR);
      if (SdCardSendBlockLength(SDCD_BLOCK_LENGTH) != SDCD_STATUS_NOERROR)  return(SDCD_STATUS_ERROR);
	
      /////////////////////////////////////////////////////////////////
      ///// Detecting MMC card successfully!
//    m_CIS.CardType = SDMMC_CardSD;


/* High Speed Mode


		DWORD	GetReg = SDMMC_ReadCodecReg16(SDMMC_REG_SDICON);
		GetReg = GetReg |(1<<1);
		SDMMC_WriteCodecReg16(SDMMC_REG_SDICON, GetReg);
		
		SDMMC_WriteCodecReg32(SDMMC_REG_SDIDCON, (1<<19)|(1<<17)|(m_CIS.BusType<<16)|(2<<12)|(1));	
	
		for(i=0; i<SHORT_RETRY; i++)
		{
			// Set High Speed
			// CMD6(SD: SWITCH)
			if(COMMAND(6, 0x80FFFF01 , FLAG_CMD_START | FLAG_SHORT_RESPONSE))
				break;

			Sleep(50);
		}

			Sleep(50);
				Sleep(50);
		RETAILMSG(1, (TEXT("--DetectCardSD CMD6 Retry = %d\r\n"),i));

		Sleep(100);
		BYTE pReadAddr[512];
		DWORD Status,ReadCount = 0 ;
		
		while(1)
		{
			//RETAILMSG(1, (TEXT("--DetectCardSD CMD6 Status = %x\r\n"),Status));
			Status = SDMMC_ReadCodecReg16(SDMMC_REG_SDIFSTA);
			RETAILMSG(1, (TEXT("--DetectCardSD CMD6 Status = %x\r\n"),Status));
			if((Status&_BIT(12))==_BIT(12))	// Is Rx data?
			{
				pReadAddr[ReadCount] = (UCHAR) pSDMMC->rSDIDAT;
				RETAILMSG(1, (TEXT("--DetectCardSD CMD6 pReadAddr[%d] = %x\r\n"),ReadCount,pReadAddr[ReadCount] ));
				ReadCount++;
			}
			if(ReadCount == 64)
				break;
			
		}
		SetPreScaler(PSSD);
*/

     memset(vData,0x00,sizeof(vData));
     SysSdCardReadBlockData(0,vData,SDCD_BLOCK_LENGTH);
     SysParseSdCardMBR(vData);

      return(SDCD_STATUS_NOERROR);
}
//=============================================================================
void  SysSetSdCardRCA(DWORD dRCA)
{
      G_xSdCard.m_dRCA = dRCA & 0xffff0000;
}
DWORD SysGetSdCardRCA(void)
{
      return(G_xSdCard.m_dRCA);
}
void  SysSetSdCardSetStatus(DWORD dStatus)
{
      G_xSdCard.m_dCardStatus = dStatus;
}
DWORD SysGetSdCardSetStatus(void)
{
      return(G_xSdCard.m_dCardStatus);
}

void  SysGetSdCardFull16BytesCID(UCHAR *pFull16BytesCID)
{
      memmove(pFull16BytesCID,G_vFullSdCardCidCode,16);
}

void  SysParseSdCardCID(void)
{
      G_xSdCard.m_xCID.bMID   = SysGetSdCardBitsFromRspData(127 -  0,120 -  0);
      G_xSdCard.m_xCID.wOID   = SysGetSdCardBitsFromRspData(119 -  0,104 -  0);
      G_xSdCard.m_xCID.vPNM[0]= SysGetSdCardBitsFromRspData(103 -  0, 96 -  0);
      G_xSdCard.m_xCID.vPNM[1]= SysGetSdCardBitsFromRspData( 95 -  0, 88 -  0);
      G_xSdCard.m_xCID.vPNM[2]= SysGetSdCardBitsFromRspData( 87 -  0, 80 -  0);
      G_xSdCard.m_xCID.vPNM[3]= SysGetSdCardBitsFromRspData( 79 -  0, 72 -  0);
      G_xSdCard.m_xCID.vPNM[4]= SysGetSdCardBitsFromRspData( 71 -  0, 64 -  0);
      G_xSdCard.m_xCID.bPRV   = SysGetSdCardBitsFromRspData( 63 -  0, 56 -  0);
      G_xSdCard.m_xCID.dPSN   = SysGetSdCardBitsFromRspData( 55 -  0, 24 -  0);
      G_xSdCard.m_xCID.wMDT   = SysGetSdCardBitsFromRspData( 19 -  0,  8 -  0);

      G_vFullSdCardCidCode[ 0]= SysGetSdCardBitsFromRspData(127 -  0,120 -  0);
      G_vFullSdCardCidCode[ 1]= SysGetSdCardBitsFromRspData(119 -  0,112 -  0);
      G_vFullSdCardCidCode[ 2]= SysGetSdCardBitsFromRspData(111 -  0,104 -  0);
      G_vFullSdCardCidCode[ 3]= SysGetSdCardBitsFromRspData(103 -  0, 96 -  0);
      G_vFullSdCardCidCode[ 4]= SysGetSdCardBitsFromRspData( 95 -  0, 88 -  0);
      G_vFullSdCardCidCode[ 5]= SysGetSdCardBitsFromRspData( 87 -  0, 80 -  0);
      G_vFullSdCardCidCode[ 6]= SysGetSdCardBitsFromRspData( 79 -  0, 72 -  0);
      G_vFullSdCardCidCode[ 7]= SysGetSdCardBitsFromRspData( 71 -  0, 64 -  0);
      G_vFullSdCardCidCode[ 8]= SysGetSdCardBitsFromRspData( 63 -  0, 56 -  0);
      G_vFullSdCardCidCode[ 9]= SysGetSdCardBitsFromRspData( 55 -  0, 48 -  0);
      G_vFullSdCardCidCode[10]= SysGetSdCardBitsFromRspData( 47 -  0, 40 -  0);
      G_vFullSdCardCidCode[11]= SysGetSdCardBitsFromRspData( 39 -  0, 32 -  0);
      G_vFullSdCardCidCode[12]= SysGetSdCardBitsFromRspData( 31 -  0, 24 -  0);
      G_vFullSdCardCidCode[13]= SysGetSdCardBitsFromRspData( 23 -  0, 16 -  0);
      G_vFullSdCardCidCode[14]= SysGetSdCardBitsFromRspData( 15 -  0,  8 -  0);
      G_vFullSdCardCidCode[15]= SysGetSdCardBitsFromRspData(  7 -  0,  0 -  0);
}
void  SysParseSdCardCSD(void)
{
      G_xSdCard.m_xCSD.bStructure        = SysGetSdCardBitsFromRspData(127 -  0,126 -  0);
      G_xSdCard.m_xCSD.bTAAC             = SysGetSdCardBitsFromRspData(119 -  0,112 -  0);
      G_xSdCard.m_xCSD.bNSAC             = SysGetSdCardBitsFromRspData(111 -  0,104 -  0);
      G_xSdCard.m_xCSD.bTranSpeed        = SysGetSdCardBitsFromRspData(103 -  0, 96 -  0);
      G_xSdCard.m_xCSD.wCCC              = SysGetSdCardBitsFromRspData( 95 -  0, 84 -  0);
      G_xSdCard.m_xCSD.bReadBlLen        = SysGetSdCardBitsFromRspData( 83 -  0, 80 -  0);
      G_xSdCard.m_xCSD.bReadBlPar        = SysGetSdCardBitsFromRspData( 79 -  0, 79 -  0);
      G_xSdCard.m_xCSD.bWriteBlkMisAlgn  = SysGetSdCardBitsFromRspData( 78 -  0, 78 -  0);
      G_xSdCard.m_xCSD.bReadBlkMisAlgn   = SysGetSdCardBitsFromRspData( 77 -  0, 77 -  0);
      G_xSdCard.m_xCSD.bDsrImp           = SysGetSdCardBitsFromRspData( 76 -  0, 76 -  0);
      G_xSdCard.m_xCSD.wDeviceSize       = SysGetSdCardBitsFromRspData( 73 -  0, 62 -  0);
      G_xSdCard.m_xCSD.bVDDrCurrMin      = SysGetSdCardBitsFromRspData( 61 -  0, 59 -  0);
      G_xSdCard.m_xCSD.bVDDrCurrMax      = SysGetSdCardBitsFromRspData( 58 -  0, 56 -  0);
      G_xSdCard.m_xCSD.bVDDwCurrMin      = SysGetSdCardBitsFromRspData( 55 -  0, 53 -  0);
      G_xSdCard.m_xCSD.bVDDwCurrMax      = SysGetSdCardBitsFromRspData( 52 -  0, 50 -  0);
      G_xSdCard.m_xCSD.bDeviceSizeMul    = SysGetSdCardBitsFromRspData( 49 -  0, 47 -  0);
      G_xSdCard.m_xCSD.bEraseBlkEnable   = SysGetSdCardBitsFromRspData( 46 -  0, 46 -  0);
      G_xSdCard.m_xCSD.bEraseSectSize    = SysGetSdCardBitsFromRspData( 45 -  0, 39 -  0);
      G_xSdCard.m_xCSD.bWpGrpSize        = SysGetSdCardBitsFromRspData( 38 -  0, 32 -  0);
      G_xSdCard.m_xCSD.bWpGrpEnable      = SysGetSdCardBitsFromRspData( 31 -  0, 31 -  0);
      G_xSdCard.m_xCSD.bR2wFactor        = SysGetSdCardBitsFromRspData( 28 -  0, 26 -  0);
      G_xSdCard.m_xCSD.bWriteBlLen       = SysGetSdCardBitsFromRspData( 25 -  0, 22 -  0);
      G_xSdCard.m_xCSD.bWriteBlPar       = SysGetSdCardBitsFromRspData( 21 -  0, 21 -  0);
      G_xSdCard.m_xCSD.bFileFormatGrp    = SysGetSdCardBitsFromRspData( 15 -  0, 15 -  0);
      G_xSdCard.m_xCSD.bCopy             = SysGetSdCardBitsFromRspData( 14 -  0, 14 -  0);
      G_xSdCard.m_xCSD.bPermWriteProtect = SysGetSdCardBitsFromRspData( 13 -  0, 13 -  0);
      G_xSdCard.m_xCSD.bTmpWriteProtect  = SysGetSdCardBitsFromRspData( 12 -  0, 12 -  0);
      G_xSdCard.m_xCSD.bFileFormat       = SysGetSdCardBitsFromRspData( 11 -  0, 10 -  0);
}
void  SysParseSdCardStatus(void)
{
      SysSetSdCardSetStatus(G_xSdCard.m_vRspRegX[0]);
}
void  SysParseSdCardSdStatus(void)
{
}
void  SysParseSdCardMBR(UCHAR *pData)
{
      int  i,nMbrStart;

      memset(G_xSdCard.m_xMBR,0x00,sizeof(G_xSdCard.m_xMBR));
      if (pData[0x1fe] == 0x55 && pData[0x1ff] == 0xaa)
         {
//        SdCardSetCardInStatus(SDCARD_CARD_IN_STATUS_FULL);
          if ((pData[0] == 0xeb && pData[2] == 0x90) || pData[0] == 0xe9)
              return;
          nMbrStart = 0x1be;
          for (i = 0;i < 4;i++)
              {
               G_xSdCard.m_xMBR[i].nDataValid  = 0x01;                   // 0x01=valid
               G_xSdCard.m_xMBR[i].bBootMark   = pData[nMbrStart +  0];  // 0x80=active Partition;  
               G_xSdCard.m_xMBR[i].bStartHead  = pData[nMbrStart +  1];  // Starting Head           
               G_xSdCard.m_xMBR[i].bStartSector= pData[nMbrStart +  2];  // Starting Sector         
               G_xSdCard.m_xMBR[i].bStartCyl   = pData[nMbrStart +  3];  // Starting Cylinder Byte-0
               G_xSdCard.m_xMBR[i].bSystemID   = pData[nMbrStart +  4];  // System ID               
               G_xSdCard.m_xMBR[i].bEndHead    = pData[nMbrStart +  5];  // Ending Head             
               G_xSdCard.m_xMBR[i].bEndSector  = pData[nMbrStart +  6];  // Ending Sector           
               G_xSdCard.m_xMBR[i].bEndCyl     = pData[nMbrStart +  7];  // Ending Cylinder Byte-0  
               G_xSdCard.m_xMBR[i].bRelSector0 = pData[nMbrStart +  8];  // Relative Sector Byte-0  
               G_xSdCard.m_xMBR[i].bRelSector1 = pData[nMbrStart +  9];  // Relative Sector Byte-1  
               G_xSdCard.m_xMBR[i].bRelSector2 = pData[nMbrStart + 10];  // Relative Sector Byte-2  
               G_xSdCard.m_xMBR[i].bRelSector3 = pData[nMbrStart + 11];  // Relative Sector Byte-3  
               G_xSdCard.m_xMBR[i].bTotSector0 = pData[nMbrStart + 12];  // Total    Sector Byte-0  
               G_xSdCard.m_xMBR[i].bTotSector1 = pData[nMbrStart + 13];  // Total    Sector Byte-1  
               G_xSdCard.m_xMBR[i].bTotSector2 = pData[nMbrStart + 14];  // Total    Sector Byte-2  
               G_xSdCard.m_xMBR[i].bTotSector3 = pData[nMbrStart + 15];  // Total    Sector Byte-3
               nMbrStart += 16;
              }
         }
}
DWORD SysGetSdCardBitsFromRspData(int nMSB,int nLSB)
{
      DWORD dData;
      DWORD dMask;

      dData = 0x00000000;
      while (nMSB >= 96 && nMSB >= nLSB)
            {
             dData <<= 1;
             dMask = 1 << (nMSB - 96);
             if (G_xSdCard.m_vRspRegX[3] & dMask)
                 dData |= 0x00000001;
             --nMSB;
            }
      while (nMSB >= 64 && nMSB >= nLSB)
            {
             dData <<= 1;
             dMask = 1 << (nMSB - 64);
             if (G_xSdCard.m_vRspRegX[2] & dMask)
                 dData |= 0x00000001;
             --nMSB;
            }
      while (nMSB >= 32 && nMSB >= nLSB)
            {
             dData <<= 1;
             dMask = 1 << (nMSB - 32);
             if (G_xSdCard.m_vRspRegX[1] & dMask)
                 dData |= 0x00000001;
             --nMSB;
            }
      while (nMSB >=  0 && nMSB >= nLSB)
            {
             dData <<= 1;
             dMask = 1 << (nMSB -  0);
             if (G_xSdCard.m_vRspRegX[0] & dMask)
                 dData |= 0x00000001;
             --nMSB;
            }
      return(dData);
}
void  SysCheckSdCardChangStatus(void)
{
      DWORD dTempX;
      DWORD dExistStatus;

      dTempX = SysGetSdCardRealPortStatus(&dExistStatus);

      if (G_xSdCard.m_dPortStatus != dTempX)
         {
          G_xSdCard.m_dPortStatus = dTempX;
          SdCardSetCardChangStatus(SDCARD_CARD_CHANGE_OCCURED);
         }
      SdCardSetCardInsertStatus(dExistStatus);
}
DWORD SysGetSdCardRealPortStatus(DWORD *pExistStatus)
{
	int   nNavisType = SysGetNavisModelType();
	DWORD dTempX;

	#if defined(__N500_MODEL__)
		dTempX = SysGetGPIOC() & ((1 << SD1_WP_BIT_NO) | (1 << SD1_CD_BIT_NO));

		if (dTempX & (1 << SD1_WP_BIT_NO))
		{
			//    if (dTempX & (1 << SD1_CD_BIT_NO))
			*pExistStatus = 0;
		}			
		else
		{
			*pExistStatus = 1;
		}			
	#else
		if (nNavisType == NAVIS_TYPE_700 || nNavisType == NAVIS_TYPE_800 || nNavisType == NAVIS_TYPE_3800)
		{
			dTempX = SysGetGPIOD() & ((1 << SD1_WP_BIT_NO) | (1 << SD1_CD_BIT_NO));

			if (dTempX & (1 << SD1_WP_BIT_NO))
			{
				//        if (dTempX & (1 << SD1_CD_BIT_NO))
				*pExistStatus = 0;
			}				
			else
			{
				*pExistStatus = 1;
			}				
		}
		else
		{
			dTempX = SysGetGPIOD() & ((1 << SD0_WP_BIT_NO) | (1 << SD0_CD_BIT_NO));

			if (dTempX & (1 << SD0_WP_BIT_NO))
			{
				//        if (dTempX & (1 << SD0_CD_BIT_NO))
				*pExistStatus = 0;
			}				
			else
			{
				*pExistStatus = 1;
			}				
		}
	#endif

      return(dTempX);
}
//=============================================================================
DWORD SysSendSdCardCommand(DWORD dCommand,DWORD dArgument)
{
      int   nRetry;
      DWORD dStatus;

      for (nRetry = 0;nRetry < 1;nRetry++)
          {
           dStatus = SysSendSdCardCommandInternal(dCommand,dArgument);
           if (dStatus == SDCD_STATUS_NOERROR)
               break;
           SysDelayMiliSec(50);
          }
      return(dStatus);
}
DWORD SysSendSdCardAppCommand(DWORD dCommand,DWORD dArgument)
{
      int   nRetry;
      DWORD dStatus;

      for (nRetry = 0;nRetry < 1;nRetry++)
          {
           dStatus = SysSendSdCardCommandInternal(SDCD_CMD_APP_CMD_55,SysGetSdCardRCA());
           if (dStatus == SDCD_STATUS_NOERROR)
              {
               dStatus = SysSendSdCardCommandInternal(dCommand,dArgument);
               if (dStatus == SDCD_STATUS_NOERROR)
                   break;
              }
          }
      return(dStatus);
}
DWORD SysSendSdCardCommandInternal(DWORD dCommand,DWORD dAargument)
{
      DWORD dFlag;
      DWORD dStatus;

      dStatus = SysGetSdCardFlagByCommand(dCommand,&dFlag);

      if (dStatus == SDCD_STATUS_UNKNOWNCMD)
          return(dStatus);

      SysClearSdCdInterruptPendingAll();

#ifdef SDCD_POLLING_INTERRUPT_MODE	
      dStatus = SysSendSdCardCommandInternalByPoll(dCommand,dAargument,dFlag);
#else
      dStatus = SysSendSdCardCommandInternalByDMA(dCommand,dAargument,dFlag);
#endif	
      if (dStatus == SDCD_STATUS_NOERROR)
         {
          if (dCommand == SDCD_CMD_ALL_SEND_CID_02)   SysParseSdCardCID();
          if (dCommand == SDCD_CMD_SEND_CSD_09)       SysParseSdCardCSD();
          if (dCommand == SDCD_CMD_SEND_STATUS_13)    SysParseSdCardStatus();
          if (dCommand == SDCD_ACMD_SD_STATUS_13)     SysParseSdCardSdStatus();
         }
      switch (dCommand)
             {
              case SDCD_CMD_SEND_STATUS_13             :  // R1
              case SDCD_CMD_SET_BLOCK_LEN_16           :  // R1
              case SDCD_CMD_APP_CMD_55                 :  // R1
              case SDCD_ACMD_SET_BUS_WIDTH_06          :  // R1
              case SDCD_ACMD_SD_STATUS_13              :  // R1
              case SDCD_ACMD_SET_CLR_CARD_DETECT_42    :  // R1
              case SDCD_ACMD_SEND_SCR_51               :  // R1
//            case SWITCH_FUNC                         :  // R1
              case SDCD_CMD_READ_SINGLE_BLOCK_17       :  // R1
              case SDCD_CMD_READ_MULTI_BLOCK_18        :  // R1
              case SDCD_CMD_WRITE_SINGLE_BLOCK_24      :  // R1
              case SDCD_CMD_WRITE_MULTI_BLOCK_25       :  // R1
              case SDCD_CMD_STOP_TRANSMISSION_12       :  // R1
              case SDCD_CMD_SEL_DESELECT_CARD_07		:	// R1b
                   if ((dCommand & SDCD_ACMD_UNMASK) != SysGetSdCdResponseIndex())
                      {
//                     RETAILMSG(SDMMC_DEBUG_MSG,(TEXT("Response Index(%02Xh) is different from command index(%02Xh).\n"), MES_SDHC_GetResponseIndex(m_channel), (command & SDCD_ACMD_UNMASK) ));
                      }
                   if (G_xSdCard.m_vRspRegX[0] & 0xFDF98008)
                      {
                       dStatus |= SDCD_STATUS_ERROR;
/*
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<31) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : OUT_OF_RANGE\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<30) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : ADDRESS_ERROR\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<29) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : BLOCK_LEN_ERROR\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<28) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : ERASE_SEQ_ERROR\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<27) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : ERASE_PARAM\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<26) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : WP_VIOLATION\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<24) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : LOCK_UNLOCK_FAILED\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<23) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : COM_CRC_ERROR\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<22) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : ILLEGAL_COMMAND\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<21) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : CARD_ECC_FAILED\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<20) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : Internal Card Controller ERROR\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<19) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : General Error\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<16) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : CID/CSD_OVERWRITE_ERROR\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<<15) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : WP_ERASE_SKIP\n" )));
                       if( G_xSdCard.m_vRspRegX[0] & (1UL<< 3) )	RETAILMSG(SDMMC_DEBUG_MSG, (TEXT("\t\tERROR : AKE_SEQ_ERROR\n" )));
*/
                      }
                   break;
             }

      if (dStatus & SDCD_STATUS_ERROR)
          dStatus = SDCD_STATUS_ERROR;
      return(dStatus);
}
DWORD SysSendSdCardCommandInternalByPoll(DWORD dCommand,DWORD dAargument,DWORD dFlag)
{
      DWORD dTimeStart;
      DWORD dStatus = SDCD_STATUS_NOERROR;

      memset(G_xSdCard.m_vRspRegX,0x00,sizeof(G_xSdCard.m_vRspRegX));

      // Send Command
      dTimeStart = SysGetSystemTimer();
      do
        {
         SysClearSdCdInterruptPendingOne(SDCD_INT_HLE);
         SysSetSdCdArgument(dAargument);
         SysSetSdCdCommand(dCommand & SDCD_ACMD_UNMASK,dFlag);
         while (SysIsSdCdCommandBusy())
               {
                if (SysGetDiffTimeMili(dTimeStart) >= 1000)
                    return(SDCD_STATUS_CMDBUSY);
               }
        } while (SysGetSdCdInterruptPendingOne(SDCD_INT_HLE));

      /*
       // If Data Transfer is busy, you have to clear FIFO to send this command.	
      if (SysIsSdCdDataTransferBusy())
         {
         if (SysIsSdCdFIFOEmpty() == 0)
            {
             SysReSetSdCdFIFO();                    // Reest the FIFO.
             while (SysIsSdCdResetFIFO());          // Wait until the FIFO reset is completed.
            }
         }
       //*/

      // Wait until Command sent to card and got response from card.
      dTimeStart = SysGetSystemTimer();
      while (SysGetSdCdInterruptPendingOne(SDCD_INT_CD) == 0)
            {
             if (SysGetDiffTimeMili(dTimeStart) >= 1000)
                 return(SDCD_STATUS_CMDTOUT);
            }	

      // Check Response Error
      if (SysGetSdCdInterruptPendingOne(SDCD_INT_RCRC))  dStatus |= SDCD_STATUS_RESCRCFAIL;
      if (SysGetSdCdInterruptPendingOne(SDCD_INT_RE))    dStatus |= SDCD_STATUS_RESERROR;
      if (SysGetSdCdInterruptPendingOne(SDCD_INT_RTO))   dStatus |= SDCD_STATUS_RESTOUT;

      if ((dStatus == SDCD_STATUS_NOERROR) && (dFlag & SDCD_CMDFLAG_SHORTRSP))
         {
          if ((dFlag & SDCD_CMDFLAG_LONGRSP) == SDCD_CMDFLAG_LONGRSP)
              SysGetSdCdLongResponse(&G_xSdCard.m_vRspRegX[0]);
          else
              G_xSdCard.m_vRspRegX[0] = SysGetSdCdShortResponse();
         }
      return(dStatus);
}
DWORD SysSendSdCardCommandInternalByDMA(DWORD dCommand,DWORD dAargument,DWORD dFlag)
{
      DWORD dStatus = SDCD_STATUS_NOERROR;
/*
      DWORD dTimeStart;
      int   nTimeOut= 10;

      MES_SDHC_SetInterruptEnableAll(m_channel, CFALSE);
      SysClearSdCdInterruptPendingOneAll(m_channel);
      MES_SDHC_SetInterruptEnable32(m_channel, 1<< MES_SDHC_INT_CD | 1 << SDCD_INT_HLE);
      while (nTimeOut--)
            {
             SysSetSdCdArgument(dAargument);
             SysSetSdCdCommand(dCommand & SDCD_ACMD_UNMASK,dFlag);

             // Wait for interrupt
             if (WAIT_TIMEOUT == WaitForSingleObject(m_SDIIntrEvent, MAXIMUM_WAIT) )
                 return(SDCARD_STATUS_CMDTOUT);
             else
                {
                 if (CTRUE == SysGetSdCdInterruptPendingOne( m_channel, SDCD_INT_HLE ))
                    {
                     MES_SDHC_SetInterruptEnableAll(m_channel, CFALSE);
                     SysClearSdCdInterruptPendingOneAll(m_channel);
                     InterruptDone(m_dwSysintr);		

                     Sleep(10);	
                     continue;
                    }
                 if (CFALSE == SDHC_GetInterruptPending(m_channel,SDHC_INT_CD))
                    {
                     status |= SDCD_STATUS_CMDTOUT;
                     MES_SDHC_SetInterruptEnableAll(m_channel, CFALSE);
                     SysClearSdCdInterruptPendingOneAll(m_channel);
                     InterruptDone(m_dwSysintr);		
                     return(dStatus);
                    }

                 // Check Response Error
                 if (SysGetSdCdInterruptPendingOne(m_channel,SDHC_INT_RCRC))	status |= SDCD_STATUS_RESCRCFAIL;
                 if (SysGetSdCdInterruptPendingOne(m_channel,SDHC_INT_RE))		status |= SDCD_STATUS_RESERROR;
                 if (SysGetSdCdInterruptPendingOne(m_channel,SDHC_INT_RTO))   status |= SDCD_STATUS_RESTOUT;

                 if ((status == SDCD_STATUS_NOERROR) && (flag & SDCD_CMDFLAG_SHORTRSP))
                    {
                     if ((flag & SDCD_CMDFLAG_LONGRSP) == SDCD_CMDFLAG_LONGRSP )
                          MES_SDHC_GetLongResponse( m_channel, &G_xSdCard.m_vRspRegX[0] );
                     else
                          G_xSdCard.m_vRspRegX[0] = MES_SDHC_GetShortResponse(m_channel);
                    }
                 break;
                }
            }

      MES_SDHC_SetInterruptEnableAll(m_channel, CFALSE);
      SysClearSdCdInterruptPendingOneAll(m_channel);
      InterruptDone(m_dwSysintr);		
*/
      return(dStatus);
}
DWORD SysGetSdCardFlagByCommand(DWORD dCommand,DWORD *pFlag)
{
      DWORD dStatus;
      DWORD dFlag;

      dStatus = 0;
      switch (dCommand)
             {
              case SDCD_CMD_GO_IDLE_STATE_00          :  // No Response		
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_SENDINIT | SDCD_CMDFLAG_WAITPRVDAT;
                   break;
              case SDCD_CMD_ALL_SEND_CID_02           :  // R2
              case SDCD_CMD_SEND_CSD_09               :  // R2
              case SDCD_CMD_SEND_CID_10               :  // R2
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_WAITPRVDAT | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_LONGRSP;
                   break;
              case SDCD_CMD_SEND_STATUS_13            :  // R1
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_SHORTRSP;
                   break;
              case SDCD_CMD_SET_BLOCK_LEN_16          :  // R1
              case SDCD_CMD_APP_CMD_55                :  // R1
              case SDCD_ACMD_SET_BUS_WIDTH_06         :  // R1
              case SDCD_ACMD_SD_STATUS_13             :  // R1
              case SDCD_ACMD_SET_CLR_CARD_DETECT_42   :  // R1
              case SDCD_ACMD_SEND_SCR_51              :  // R1
//            case SWITCH_FUNC                        :  // R1
              case SDCD_CMD_SEL_DESELECT_CARD_07      :  // R1b
              case SDCD_CMD_SEND_RELATIVE_ADDR_03     :  // R6
              case SDCD_CMD_IF_COND_08                :  // R7
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_WAITPRVDAT | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_SHORTRSP;
                   break;
              case SDCD_CMD_READ_SINGLE_BLOCK_17      :  // R1
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_WAITPRVDAT | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_SHORTRSP |
                           SDCD_CMDFLAG_BLOCK    | SDCD_CMDFLAG_RXDATA;
                   break;
              case SDCD_CMD_READ_MULTI_BLOCK_18       :  // R1
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_WAITPRVDAT | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_SHORTRSP |
                           SDCD_CMDFLAG_BLOCK    | SDCD_CMDFLAG_RXDATA | SDCD_CMDFLAG_SENDAUTOSTOP;
                   break;
              case SDCD_CMD_WRITE_SINGLE_BLOCK_24     :  // R1
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_WAITPRVDAT | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_SHORTRSP |
                           SDCD_CMDFLAG_BLOCK    | SDCD_CMDFLAG_TXDATA;
                   break;
              case SDCD_CMD_WRITE_MULTI_BLOCK_25      :  // R1
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_WAITPRVDAT | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_SHORTRSP |
                           SDCD_CMDFLAG_BLOCK    | SDCD_CMDFLAG_TXDATA     | SDCD_CMDFLAG_SENDAUTOSTOP;
                   break;
              case SDCD_CMD_STOP_TRANSMISSION_12      :  // R1
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_CHKRSPCRC | SDCD_CMDFLAG_SHORTRSP | SDCD_CMDFLAG_STOPABORT;
                   break;
              case SDCD_ACMD_SD_APP_OP_COND_41        :  // R3
                   dFlag = SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_WAITPRVDAT | SDCD_CMDFLAG_SHORTRSP;
                   break;
              default :
                   dStatus |= SDCD_STATUS_UNKNOWNCMD;
                   dFlag    = 0;
                   break;
             }
      *pFlag = dFlag;
      return(dStatus);
}
DWORD SysSetSdCardClock(DWORD dDivider)
{
      DWORD dTimeOut;

      // 1. Confirm that no card is engaged in any transaction.
      //    If there's a transaction, wait until it finishes.
      //	while( MES_SDHC_IsDataTransferBusy() );
      //	while( MES_SDHC_IsCardDataBusy() );
      if (SysIsSdCdCardDataBusy())          return(SDCD_STATUS_ERROR);
      if (SysIsSdCdDataTransferBusy())      return(SDCD_STATUS_ERROR);

      // 2. Disable the output clock.
      SysSetSdCdClockDividerEnable(0);

      // 3. Program the clock divider as required.
      SysSetSdCdClockDivider(dDivider);

      // 4. Start a command with MES_SDHC_CMDFLAG_UPDATECLKONLY flag.
      while (1)
            {
             SysSetSdCdCommand(0x00000000,SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_UPDATECLKONLY | SDCD_CMDFLAG_WAITPRVDAT);

             // 5. Wait until a update clock command is taken by the SDHC module.
             //    If a HLE is occurred, repeat 4.
             dTimeOut = 0;
             while (SysIsSdCdCommandBusy())
                   {
                    if (++dTimeOut > 0x1000000)
                        return(SDCD_STATUS_ERROR);
                   }
             if (SysGetSdCdInterruptPendingOne(SDCD_INT_HLE))
                 SysClearSdCdInterruptPendingOne(SDCD_INT_HLE);
             else
                 break;
            }

      // 6. Enable the output clock.
      SysSetSdCdClockDividerEnable(1);

      // 7. Start a command with MES_SDHC_CMDFLAG_UPDATECLKONLY flag.
      while (1)
            {
             SysSetSdCdCommand(0x00000000,SDCD_CMDFLAG_STARTCMD | SDCD_CMDFLAG_UPDATECLKONLY | SDCD_CMDFLAG_WAITPRVDAT);

             // 8. Wait until a update clock command is taken by the SDHC module.
             //    If a HLE is occurred, repeat 7.
             dTimeOut = 0;
             while (SysIsSdCdCommandBusy())
                   {
                    if (++dTimeOut > 0x1000000)
                        return(SDCD_STATUS_ERROR);
                   }

             if (SysGetSdCdInterruptPendingOne(SDCD_INT_HLE))
                 SysClearSdCdInterruptPendingOne(SDCD_INT_HLE);
             else
                 break;
            }
      return(SDCD_STATUS_NOERROR);
}
//=============================================================================
void  SysSetSdCdClockPClkMode(DWORD dPclkMode)
{
	  DWORD dTempX;

      dTempX = G_pSysSDCD->dCLKENB;
      dTempX = dTempX & ~(1 <<  3);
      dTempX = dTempX |  (dPclkMode <<  3);
      G_pSysSDCD->dCLKENB = dTempX;
}
void  SysSetSdCdClockSource(DWORD dClkSrc)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dCLKGEN;
      dTempX = dTempX & ~(7 <<  2);
      dTempX = dTempX |  (dClkSrc <<  2);
      G_pSysSDCD->dCLKGEN = dTempX;
}
void  SysSetSdCdClockDivisor(DWORD dDivisor)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dCLKGEN;
      dTempX = dTempX & ~(0x1F <<  5);
      dTempX = dTempX |  ((dDivisor - 1) <<  5);
      G_pSysSDCD->dCLKGEN = dTempX;
}
void  SysSetSdCdClockDivisorEnable(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dCLKENB;
      if (nDisableEnableMode)
          dTempX = dTempX |  (1 <<  2);
      else
          dTempX = dTempX & ~(1 <<  2);
      G_pSysSDCD->dCLKENB = dTempX;
}
void  SysSetSdCdClockDivider(DWORD dDivider)
{
      G_pSysSDCD->dCLKDIV = dDivider / 2;
}
void  SysSetSdCdClockDividerEnable(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dCLKENA;

      dTempX = dTempX | (1 << 16);

      if (nDisableEnableMode)
          dTempX = dTempX |  (1 <<  0);
      else
          dTempX = dTempX & ~(1 <<  0);
      G_pSysSDCD->dCLKENA = dTempX;
}
void  SysSetSdCdByteOrder(void)
{
}
void  SysReSetSdCdDMA(void)
{
      DWORD dDMARST  = 1 << 2;
      DWORD dFIFORST = 1 << 1;
      DWORD dCTRLRST = 1 << 0;
      DWORD dTempX;

      dTempX = G_pSysSDCD->dCTRL;
      dTempX &= ~(dFIFORST | dCTRLRST);
      dTempX |=  dDMARST;
      G_pSysSDCD->dCTRL = dTempX;
}
void  SysReSetSdCdFIFO(void)
{
      DWORD dDMARST  = 1 << 2;
      DWORD dFIFORST = 1 << 1;
      DWORD dCTRLRST = 1 << 0;
      DWORD dTempX;

      dTempX = G_pSysSDCD->dCTRL;
      dTempX &= ~(dDMARST | dCTRLRST);
      dTempX |=  dFIFORST;
      G_pSysSDCD->dCTRL = dTempX;
}
void  SysReSetSdCdCTRL(void)
{
      DWORD dDMARST  = 1 << 2;
      DWORD dFIFORST = 1 << 1;
      DWORD dCTRLRST = 1 << 0;
      DWORD dTempX;

      dTempX = G_pSysSDCD->dCTRL;
      dTempX &= ~(dDMARST | dFIFORST);
      dTempX |=  dCTRLRST;
      G_pSysSDCD->dCTRL = dTempX;
}
int   SysIsSdCdResetDMA(void)
{
      if (G_pSysSDCD->dCTRL & (1 <<  2))
          return(1);

      return(0);
}
int   SysIsSdCdResetFIFO(void)
{
      if (G_pSysSDCD->dCTRL & (1 <<  1))
          return(1);

      return(0);
}
int   SysIsSdCdResetCTRL(void)
{
      if (G_pSysSDCD->dCTRL & (1 <<  0))
          return(1);

      return(0);
}
void  SysSetSdCdInterruptEnableAll(int nDisableEnableMode)
{
      if (nDisableEnableMode)
          G_pSysSDCD->dINTMASK = 0x1FFFE;            // 1 to 16
      else
          G_pSysSDCD->dINTMASK = 0x00000;
}
DWORD SysGetSdCdInterruptPending32(void)
{
      return(G_pSysSDCD->dRINTSTS);
}
DWORD SysGetSdCdInterruptPendingAll(void)
{
      return(G_pSysSDCD->dMINTSTS & 0x1FFFE);
}
void  SysClearSdCdInterruptPendingAll(void)
{
      G_pSysSDCD->dRINTSTS = 0xFFFFFFFF;
}
void  SysClearSdCdInterruptPendingOne(DWORD dIntNo)
{
      G_pSysSDCD->dRINTSTS = (1 << dIntNo);
}
DWORD SysGetSdCdInterruptPendingOne(DWORD dIntNo)
{
      return((G_pSysSDCD->dRINTSTS >> dIntNo) & 0x00000001);
}
void  SysSetSdCdDataTimeOut(DWORD dTimeOut)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dTMOUT;
      dTempX = dTempX & ~(0xffffff <<  8);
      dTempX = dTempX |  (dTimeOut <<  8);
      G_pSysSDCD->dTMOUT = dTempX;
}
void  SysSetSdCdResponseTimeOut(DWORD dTimeOut)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dTMOUT;
      dTempX = dTempX & ~(0xff <<  0);
      dTempX = dTempX |  (dTimeOut <<  0);
      G_pSysSDCD->dTMOUT = dTempX;
}
void  SysSetSdCdDataBusWidth(DWORD dBusWidth)
{
      if (dBusWidth == SDCD_DATA_WIDTH_4BIT)
//        G_pSysSDCD->dCTYPE |=  (1 << 0);        // Bus-Width = 4bit
          G_pSysSDCD->dCTYPE  = 0x00000001;       // Bus-Width = 4bit
      else
//        G_pSysSDCD->dCTYPE |= ~(1 << 0);        // Bus-Width = 1bit
          G_pSysSDCD->dCTYPE  = 0x00000000;       // Bus-Width = 1bit
}
void  SysSetSdCdBlockSize(DWORD dBlockSize)
{
      G_pSysSDCD->dBLKSIZE = dBlockSize;
}
void  SysSetSdCdByteCount(DWORD dSizeInByte)
{
      G_pSysSDCD->dBYTCNT = dSizeInByte;
}
void  SysStartSdCdCommand(void)
{
      G_pSysSDCD->dCMD |= SDCD_CMDFLAG_STARTCMD;
}
void  SysSetSdCdCommand(DWORD dCmd,DWORD dFlag)
{
      G_pSysSDCD->dCMD = dCmd | dFlag;
}
void  SysSetSdCdArgument(DWORD dCmdArg)
{
      G_pSysSDCD->dCMDARG  = dCmdArg;
}
void  SysSetSdCdDMAMode(int nPollDmaMode)
{
      if (nPollDmaMode == SDCD_MODE_DMA)
          G_pSysSDCD->dCTRL |=  (1 <<  5);
      else
          G_pSysSDCD->dCTRL &= ~(1 <<  5);
}
void  SysSetSdCdFIFORxThreshold(DWORD dThreshold)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dFIFOTH;
      dTempX &= ~(0xF << 16);
      dTempX |= (dThreshold << 16);
      G_pSysSDCD->dFIFOTH = dTempX;
}
void  SysSetSdCdFIFOTxThreshold(DWORD dThreshold)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dFIFOTH;
      dTempX &= ~(0xF <<  0);
      dTempX |= (dThreshold <<  0);
      G_pSysSDCD->dFIFOTH = dTempX;
}
int   SysIsSdCdCommandBusy(void)
{
      if (G_pSysSDCD->dCMD & SDCD_CMDFLAG_STARTCMD)
          return(1);

      return(0);
}
DWORD SysGetSdCdAutoStopResponse(void)
{
      return(G_pSysSDCD->dRESP1);
}
UCHAR SysGetSdCdResponseIndex(void)
{
      return((G_pSysSDCD->dSTATUS >> 11) & 0x3f);
}
DWORD SysGetSdCdShortResponse(void)
{
      return(G_pSysSDCD->dRESP0);
}
void  SysGetSdCdLongResponse(DWORD *pResponse)
{
      *pResponse++ = G_pSysSDCD->dRESP0;
      *pResponse++ = G_pSysSDCD->dRESP1;
      *pResponse++ = G_pSysSDCD->dRESP2;
      *pResponse++ = G_pSysSDCD->dRESP3;
}
UCHAR SysGetSdCdCRC(void)
{
      return(0);
}
void  SysReadSdCdData32(DWORD *pTarget)
{
      volatile DWORD *pSource;

      pSource = &(G_pSysSDCD->dDATA);

      *pTarget++ = *pSource;
      *pTarget++ = *pSource;
      *pTarget++ = *pSource;
      *pTarget++ = *pSource;

      *pTarget++ = *pSource;
      *pTarget++ = *pSource;
      *pTarget++ = *pSource;
      *pTarget++ = *pSource;
}
void  SysWriteSdCdData32(DWORD *pSource)
{
      volatile DWORD *pTarget;

      pTarget = &(G_pSysSDCD->dDATA);

      *pTarget = *pSource++;
      *pTarget = *pSource++;
      *pTarget = *pSource++;
      *pTarget = *pSource++;

      *pTarget = *pSource++;
      *pTarget = *pSource++;
      *pTarget = *pSource++;
      *pTarget = *pSource++;
}
DWORD SysGetSdCdFIFOCount(void)
{
      DWORD dTempX;

      dTempX = G_pSysSDCD->dSTATUS;
      dTempX = dTempX & (0x1F << 17);
      return(dTempX >> 17);
}
int   SysIsSdCdDataTransferBusy(void)
{
      if (G_pSysSDCD->dSTATUS & (1 << 10))
          return(1);

      return(0);
}
int   SysIsSdCdCardDataBusy(void)
{
      if (G_pSysSDCD->dSTATUS & (1 <<  9))
          return(1);

      return(0);
}
int   SysIsScCdCardPresent(void)
{
      if (G_pSysSDCD->dSTATUS & (1 <<  8))
          return(1);

      return(0);
}
int   SysIsSdCdTxBusy(void)
{
      return(0);
}
int   SysIsSdCdRxBusy(void)
{
      return(0);
}
int   SysIsSdCdFIFOFull(void)
{
      if (G_pSysSDCD->dSTATUS & (1 <<  3))
          return(1);

      return(0);
}
int   SysIsSdCdFIFOEmpty(void)
{
      if (G_pSysSDCD->dSTATUS & (1 <<  2))
          return(1);

      return(0);
}
int   SysIsSdCdFIFOTxThreshold(void)
{
      if (G_pSysSDCD->dSTATUS & (1 <<  1))
          return(1);

      return(0);
}
int   SysIsSdCdFIFORxThreshold(void)
{
      if (G_pSysSDCD->dSTATUS & (1 <<  0))
          return(1);

      return(0);
}

