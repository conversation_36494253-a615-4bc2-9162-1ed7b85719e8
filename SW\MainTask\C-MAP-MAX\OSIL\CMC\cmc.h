#ifndef __CMC
#define __CMC

#ifdef DEBUG_INCLUDES
#pragma message( "+++++++++ including cmc.h +++++++++" )
#endif

#ifdef __cplusplus
extern "C"
{
#endif

typedef enum
{
	cmc_NO_ERROR = 0,
	cmc_INVALID_HANDLE,
	cmc_NOT_WRITABLE,      /* By SiS[001023] 				*/
	cmc_WRITE_ERROR,
	cmc_LPT_LOCK_ERROR,
	cmc_DEVICE_NOT_RESPONDING
} cmcErr ;


typedef enum        /* by SiS[001023] Type of cartridge	*/
{
	CC_UNKNOWN = 0,	/* For error handling only.			*/
	CC_USER1,		/* User 128k CCards.				*/
	CC_USER2,		/* Type 2 use CCards (1Mb).			*/
	CC_DATA,		/* Nor Data CCards.					*/
	CC_DATA_NAND,	/* Nand Data CCards.				*/
	MEDIA_SD,		/* Secure Digital Data Cards.		*/
	MEDIA_CF,		/* Compact Flash Data Cards.		*/
	MEDIA_NONE		/* No media detected.				*/
} cmcMediaType;


PRE_EXPORT_H Word		IN_EXPORT_H cmcInit					( void );
PRE_EXPORT_H Word		IN_EXPORT_H cmcScanCartridges		( void );
PRE_EXPORT_H Word		IN_EXPORT_H cmcGetNumOfCartridges	( void );

PRE_EXPORT_H void		IN_EXPORT_H cmcClose				( void );
PRE_EXPORT_H cmcErr     IN_EXPORT_H cmcSetCartridge			( Word CdgNum );
PRE_EXPORT_H Word		IN_EXPORT_H cmcGetCartridge			( void );
PRE_EXPORT_H void		IN_EXPORT_H cmcSetPointer			( Long ptr );
PRE_EXPORT_H Long		IN_EXPORT_H cmcGetPointer			( void );
PRE_EXPORT_H void		IN_EXPORT_H cmcSetDirection			( SWord dir );
PRE_EXPORT_H SWord		IN_EXPORT_H cmcGetDirection			( void );
PRE_EXPORT_H Byte		IN_EXPORT_H cmcGetByte				( void );
PRE_EXPORT_H Byte		IN_EXPORT_H cmcFastGetByte			( void );
PRE_EXPORT_H Word		IN_EXPORT_H cmcGetWord				( void );
PRE_EXPORT_H Long		IN_EXPORT_H cmcGetTriple			( void );
PRE_EXPORT_H Long		IN_EXPORT_H cmcGetLong				( void );
PRE_EXPORT_H Word		IN_EXPORT_H cmcGetBuf				( void *ptr, Word size );
PRE_EXPORT_H Bool       IN_EXPORT_H cmcCartridgePresent		( Word CdgNum );

PRE_EXPORT_H void		IN_EXPORT_H cmcGetBlock				(Long address, Long len, Byte *buffer);
PRE_EXPORT_H cmcErr		IN_EXPORT_H cmcPutBlock				(Long addr, Long size, Byte *buf, Bool verify);	

#define		CMC_DISABLED_CDG		0
#define		CMC_ENABLED_CDG			1
#define		CMC_SLOT_EMPTY_CDG		2

PRE_EXPORT_H cmcErr		IN_EXPORT_H cmcSetCdgStatus			( Word i, Word Mode);
PRE_EXPORT_H cmcErr		IN_EXPORT_H cmcGetCdgStatus			( Word i, Word *Mode);


#include <stdio.h>
#include <stdarg.h>


enum DeviceType
{
	cmcDeviceAny = 0,
	cmcDeviceUnknown = 0,
	cmcDeviceDisk,
	cmcDevicePPI,
	cmcDeviceMemory,
	cmcDevicePCCARD,
	cmcDeviceUSB,
	cmcDevicePC104,
	cmcDeviceHSB2,
	cmcDeviceCEMEM,
	cmcDeviceFPCARD,
	cmcDeviceNET,
	cmcDeviceUSBMMR,
	cmcDeviceUSBMMR2
};

enum CacheType
{
	cmcCacheBase = 0,
	cmcCacheFlat,
	cmcCacheDefault
};

/*--------------------------------*/
/* Defines for CMCI_SET_INIT_MODE */
/*--------------------------------*/

#define CMC_DISK_INIT_NONE			0
#define CMC_DISK_INIT_BACKGROUND	0x01
#define	CMC_DISK_INIT_FILES			0x02
#define CMC_DISK_INIT_ALL			(CMC_DISK_INIT_BACKGROUND | CMC_DISK_INIT_FILES)


/*------------------------------------------*/
/* Defines for CMCI_READER_DEVICE_AVAILABLE */
/*------------------------------------------*/

#define CMC_READER_DEVICE_NOT_PRESENT	0
#define CMC_READER_DEVICE_PPI			0x01
#define	CMC_READER_DEVICE_USB			0x02
#define	CMC_READER_DEVICE_FPUSB			0x03
#define	CMC_READER_DEVICE_USBMMR		0x04
#define	CMC_READER_DEVICE_USBDPS		0x05

/*---------------------------------------*/
/* Defines for CMCI_ADD_MARINE_CARTRIDGE */
/*---------------------------------------*/

#define CMC_DEVICE_INFO_SIZE			21
#define	CMC_STORAGE_PATH_SIZE			9

typedef PRE_EXPORT_H void (IN_EXPORT_H * cmGetByteCallBack)(void);


enum CustomFunctionMessages
{
    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*  cmcCustomFunction(CMCI_SET_CARTRIDGE_PATH,TCHAR *path);                               */
    /*  cmcCustomFunction(CMCI_SET_CARTRIDGE_PATH,_T("C:\\Cartridges\\"));	                  */
    /*                                                                                        */
    CMCI_SET_CARTRIDGE_PATH,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*  cmcCustomFunction(CMCI_SET_INIT_MODE,Word Mode);                                      */
    /*  cmcCustomFunction(CMCI_SET_INIT_MODE,CMC_DISK_INIT_BACKGROUND);                       */
    /*                                                                                        */
    CMCI_SET_INIT_MODE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*  cmcCustomFunction(CMCI_GET_NUM_OF_CARTRIDGES,Word *CdgNum);                           */
    /*  cmcCustomFunction(CMCI_GET_NUM_OF_CARTRIDGES,&NOC);                                   */
    /*                                                                                        */
    CMCI_GET_NUM_OF_CARTRIDGES,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*  cmcCustomFunction(CMCI_ADD_CARTRIDGE, TCHAR *CdgPath, TCHAR* FileName);               */
    /*  cmcCustomFunction(CMCI_ADD_CARTRIDGE,_T("C:\\Cartridges"), _T("DMM00000"));           */
    /*                                                                                        */
    CMCI_ADD_CARTRIDGE,

	/*----------------------------------------------------------------------------------------*/
	/*	                                                      	                              */
	/*	cmcCustomFunction(CMCI_ADD_CARTRIDGE_MEM);                                            */
	/*	                                                      	                              */
	CMCI_ADD_CARTRIDGE_MEM,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*  cmcCustomFunction(CMCI_RESET_CARTRIDGES);                                             */
    /*                                                                                        */
    CMCI_RESET_CARTRIDGES,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_OPEN_SLOT, Word Socket, Bool *Res);                             */
    /* cmcCustomFunction(CMCI_OPEN_SLOT, 1, &Res);                                            */
    /*                                                                                        */
    CMCI_OPEN_SLOT,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CLOSE_SLOT, Word Socket, Bool *Res);                            */
    /* cmcCustomFunction(CMCI_CLOSE_SLOT, 1, &Res);                                           */
    /*                                                                                        */
    CMCI_CLOSE_SLOT,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_TEST_SLOT, Word Socket, Bool *Res);                             */
    /* cmcCustomFunction(CMCI_TEST_SLOT, 1, &Res);                                            */
    /*                                                                                        */
    CMCI_TEST_SLOT,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_DEVICE_TYPE,Word CdgNum, enum DeviceType *Type);            */
    /* cmcCustomFunction(CMCI_GET_DEVICE_TYPE,0, &Type);                                      */
    /*                                                                                        */
    CMCI_GET_DEVICE_TYPE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_FILTER_TYPE,Word CdgNum, TCHAR* extension);                 */
    /* cmcCustomFunction(CMCI_GET_FILTER_TYPE,0, extension);                                  */
    /*                                                                                        */
    CMCI_GET_FILTER_TYPE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_FILTER_TYPE_EXT,Word Device, Word CdgNum, TCHAR* extension);*/
    /* cmcCustomFunction(CMCI_GET_FILTER_TYPE_EXT,cmcDeviceAny,0, extension);                 */
    /* when passed a device it returns the related device available filters and the CdgNum    */
    /* field is used as index until an empty string is returned                               */
    /* cmcCustomFunction(CMCI_GET_FILTER_TYPE_EXT,cmcDeviceDisk,i, extension);                */
    /*                                                                                        */
    CMCI_GET_FILTER_TYPE_EXT,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_DEBUG_RESET);                                                   */
    /*                                                                                        */
    CMCI_DEBUG_RESET,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_DEBUG_START);                                                   */
    /*                                                                                        */
    CMCI_DEBUG_START,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_DEBUG_STOP);                                                    */
    /*                                                                                        */
    CMCI_DEBUG_STOP,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_DEBUG_GET_INFO,sCmcDebug *debugData);                           */
    /*                                                                                        */
    CMCI_DEBUG_GET_INFO,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_AVAILABLE, Bool *Res);                               */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_AVAILABLE, &Res);                                    */
    /*                                                                                        */
    CMCI_PPI_DEVICE_AVAILABLE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_SET_CCARD_SLOT,int slot, cmcErr* Res);                          */
    /*                                                                                        */
    CMCI_SET_CCARD_SLOT,
    
    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Usable Only on Plotters with multicartridge cmc library (mcdg2)                        */
    /* Return the number of available information items                                       */
    /* cmcCustomFunction(CMCI_GET_NUM_INFO_ENTRY,Byte* pNumOfEntry,cmcErr* Res);              */
    /*                                                                                        */
    CMCI_GET_NUM_INFO_ENTRY,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Usable Only on Plotters with multicartridge cmc library (mcdg2)                        */
    /* Return information on any physical cartridge present                                   */
    /* cmcCustomFunction(CMCI_GET_INFO,int entry,sCmcInfo* infoData,cmcErr* Res);             */
    /*                                                                                        */
    CMCI_GET_INFO,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_SET_FILE_KEY,TCHAR *FileName,Byte *key,Word KeyLen);            */
    /* cmcCustomFunction(CMCI_SET_FILE_KEY,_T("EMB02013.MCP"),keybuf,24);                     */
    /*                                                                                        */
    CMCI_SET_FILE_KEY,
    
    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Queries the given device status                                                        */
    /* cmcCustomFunction(CMCI_GET_DEVICE_STATUS,sCmcDeviceStatus *DeviceStatus);              */
    /*                                                                                        */
    CMCI_GET_DEVICE_STATUS,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Changes the given device status                                                        */
    /* cmcCustomFunction(CMCI_SET_DEVICE_STATUS,sCmcDeviceStatus *DeviceStatus);              */
    /*                                                                                        */
    CMCI_SET_DEVICE_STATUS,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_LOCK_ENABLE, Bool Enable);                           */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_LOCK_ENABLE, TRUE);                                  */
    CMCI_PPI_DEVICE_LOCK_ENABLE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_SET_LOCK, Bool Lock);                                */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_SET_LOCK, TRUE);                                     */
    CMCI_PPI_DEVICE_SET_LOCK,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_GET_LOCK, Bool *Lock);                               */
    /* cmcCustomFunction(CMCI_PPI_DEVICE_GET_LOCK, &Lock);                                    */
    CMCI_PPI_DEVICE_GET_LOCK,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_ERASE_FLASH_SECTOR, Long Address);                              */
    CMCI_ERASE_FLASH_SECTOR,
    
    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_CDG_SIZE, Long *Size);                                      */
    CMCI_GET_CDG_SIZE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USB_GET_READER_ID, Long *code);                                 */
    CMCI_USB_GET_READER_ID,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USB_SET_READER_ID, Long id);                                    */
    CMCI_USB_SET_READER_ID,
    
	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_READER_DEVICE_AVAILABLE, Word *Type);                           */
    /* cmcCustomFunction(CMCI_READER_DEVICE_AVAILABLE, &Type);                                */
    /*                                                                                        */
    CMCI_READER_DEVICE_AVAILABLE,
    
	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USB_GET_DEVICE_TYPE, Long *USBDeviceType);                      */
    /* cmcCustomFunction(CMCI_USB_GET_DEVICE_TYPE, &USBDeviceType);                           */
    CMCI_USB_GET_DEVICE_TYPE,
    
	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_LOG_START,TCHAR* message);                                      */
    /*                                                                                        */
    CMCI_LOG_START,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_LOG_STOP);                                                      */
    /*                                                                                        */
    CMCI_LOG_STOP,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_SET_WWB_BASE_ADDRESS, Long Address);                            */
    /*                                                                                        */
    CMCI_SET_WWB_BASE_ADDRESS,
    
    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_READ_ICON, const IconType *Icon, Byte *Buffer);                 */
    /*                                                                                        */
    CMCI_READ_ICON,
    
    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_SET_HARDWARE_PARAMS, Long CdgNum, CDG_DESCRIPTOR *Descriptor);  */
    /*                                                                                        */
    CMCI_SET_HARDWARE_PARAMS,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CHANGE_MASTER_BASE_ADDRESS, Long Addres);                       */
    /*                                                                                        */
    CMCI_CHANGE_MASTER_BASE_ADDRESS, 

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_IS_VALID_CCARD, Long CdgNum, Bool *RetVal);                     */
    /*                                                                                        */
    CMCI_IS_VALID_CCARD,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_IS_VALID_CCARD, TCHAR* CMCUProjectName );                       */
    /* cmcCustomFunction(CMCI_IS_VALID_CCARD, buffer );                                       */
    /*                                                                                        */
    CMCI_GET_CMCU_PROJECT_NAME,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
	/* cmcCustomFunction(CMCI_GET_CCARD_TYPE, Long CdgNum, Long Mode, SByte *pRes);			  */
	/* Mode: 0 = Logical Index (cm), 1 = Slot Index											  */ 
	/* pRes possible values are listed below												  */
    /*                                                                                        */
    CMCI_GET_CCARD_TYPE,

    /*--------------------------------------------------------------------------------------------------*/
    /*                                                                                                  */
    /*  cmcCustomFunction(CMCI_ADD_CARTRIDGE_EX, TCHAR *CdgPath, TCHAR* FileName, Word Mode);           */
    /*  cmcCustomFunction(CMCI_ADD_CARTRIDGE_EX, _T("C:\\Cartridges"), _T("DMM00000"),                  */
	/*                                                                 CMC_ADD_CARTRIDGE_FILE);         */
    /*  cmcCustomFunction(CMCI_ADD_CARTRIDGE_EX,"", "NTC1:",CMC_ADD_CARTRIDGE_PCCARD);                  */
    /*                                                                                                  */
    CMCI_ADD_CARTRIDGE_EX,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Queries the cache status                                                               */
    /* cmcCustomFunction(CMCI_GET_DEVICE_STATUS,sCmcDeviceStatus *DeviceStatus);              */
    /*                                                                                        */
    CMCI_GET_CACHE_STATUS,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Changes the chache status                                                              */
    /* cmcCustomFunction(CMCI_SET_DEVICE_STATUS,sCmcDeviceStatus *DeviceStatus);              */
    /*                                                                                        */
    CMCI_SET_CACHE_STATUS,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Sets a callback function that is called for each getbyte                               */
    /* cmcCustomFunction(CMCI_SET_GET_BYTE_CALLBACK,cmGetByteCallBack *funPtr);               */
    /*                                                                                        */
    CMCI_SET_GET_BYTE_CALLBACK,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* Enables the CMC library. Must be called before cmcInit.                                */
    /* cmcCustomFunction(CMCI_ENABLE_CMC,Bool Flag);                                          */
    /*                                                                                        */
    CMCI_ENABLE_CMC,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_ADD_CARTRIDGE_PATH,TCHAR *path);                                */
    /* cmcCustomFunction(CMCI_ADD_CARTRIDGE_PATH,_T("C:\\Charts\\"));	                      */
    /*                                                                                        */
    CMCI_ADD_CARTRIDGE_PATH,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_RESET_CARTRIDGE_PATH);	                                      */
    /*                                                                                        */
    CMCI_RESET_CARTRIDGE_PATH,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_WWB_TYPE, Word *Type);                                      */
    /* cmcCustomFunction(CMCI_GET_WWB_TYPE, &Type);                                           */
    /* Note: This Custom Function is not more used by CP_EMU.                                 */
    CMCI_GET_WWB_TYPE,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_LOCAL_DATA, Long CdgNum, Long	CdgAddress, Long DataSize,    */
	/*                                        Byte* pData, cmcErr* Res);                      */
    /*                                                                                        */
	CMCI_GET_LOCAL_DATA,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_CDG_STATUS_ACCESS,  Bool *CdgAccess );                      */
    /*                                                                                        */
	CMCI_GET_CDG_STATUS_ACCESS,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_OPEN_FILE_DATA, String *pFileName, Long Slot, Long *FileSize,
												cmcErr * RetCode );                           */
    /*                                                                                        */
	CMCI_OPEN_FILE_DATA,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CLOSE_FILE_DATA);                                               */
    /*                                                                                        */
	CMCI_CLOSE_FILE_DATA,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_FILE_DATA, Long Address, Long Length, Byte *pData,
												cmcErr * RetCode );                           */
    /*                                                                                        */
	CMCI_GET_FILE_DATA,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_NUM_LOCAL_DEVICE, Long *NumDevice );                        */
    /*                                                                                        */
	CMCI_GET_NUM_LOCAL_DEVICE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CHANGE_MASTER_CLOCK_MODE, Long ClockMode);                      */
    /*                                                                                        */
    CMCI_SET_MASTER_CLOCK_MODE,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CHECK_FILE_DATA_CKSUM, cmcErr * RetCode );                      */
    /*                                                                                        */
	CMCI_CHECK_FILE_DATA_CKSUM,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CALC_FILE_DATA_CKSUM, Byte *pData, Long Length );               */
    /*                                                                                        */
	CMCI_CALC_FILE_DATA_CKSUM,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_ENABLE_DEMO_CDG, Long Flag );                                   */
    /*                                                                                        */
	CMCI_ENABLE_DEMO_CDG,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_FPCARD_GET_READER_ID, Long *code);                              */
    CMCI_FPCARD_GET_READER_ID,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_FPCARD_SET_READER_ID, Long id);                                 */
    CMCI_FPCARD_SET_READER_ID,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_FPCARD_DEVICE_AVAILABLE, Bool *Res);                            */
    /* cmcCustomFunction(CMCI_FPCARD_DEVICE_AVAILABLE, &Res);                                 */
    /*                                                                                        */
	CMCI_FPCARD_DEVICE_AVAILABLE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_SET_MASTER2_PROT_CLOCK_MODE, Long ClockMode);                   */
    /*                                                                                        */
    CMCI_SET_MASTER2_PROT_CLOCK_MODE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_SET_MASTER2_CHIP_PRESENT, Long Present);                        */
    /*                                                                                        */
    CMCI_SET_MASTER2_CHIP_PRESENT,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USBMMR_GET_READER_ID, Long *code);                              */
    CMCI_USBMMR_GET_READER_ID,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USBMMR_SET_READER_ID, Long id);                                 */
    CMCI_USBMMR_SET_READER_ID,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USBMMR_DEVICE_AVAILABLE, Bool *Res);                            */
	CMCI_USBMMR_DEVICE_AVAILABLE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CMCXP_MAST2_REDUCE_FREQUENCY, Long Flag);                       */
	CMCI_CMCXP_MAST2_REDUCE_FREQUENCY,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_DATA, Long CdgNum, Long Ptr, Long Size,                     */
	/*									Byte* pData, cmcErr *RetCode );                       */
	CMCI_GET_DATA,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_IS_SHAREABLE, Long CdgNum, Long *CkSum, Bool *Shareable );      */
	CMCI_IS_SHAREABLE,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USBMMR_CMD_RESTART);                                            */
    /*                                                                                        */
	CMCI_USBMMR_CMD_RESTART,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_NET_SET_USER_FUN, void* CNETInit,                               */
    /*                      void* CNETSlotNum, void* CNETGetData );                           */
	/*	typedef void (__stdcall *CNET_ClientInit)(void);                                      */
	/*	typedef Byte (__stdcall *CNET_GetSlotNum)(void);                                      */
	/*	typedef Long (__stdcall *CNET_GetCartridgeData)(Word RemoteSlotID,                    */
	/*                                                Long address, Word length, Byte *data); */
    /*                                                                                        */
	CMCI_NET_SET_USER_FUN,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CMCXP_GET_CHARTFILE_INFO, String **pCharNames,                  */
	/*		String **pLicNames,	String **pBrandValid, String **pLicValid, cmcErr *RetCode );  */
    /*                                                                                        */
	CMCI_CMCXP_GET_CHARTFILE_INFO,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CMCXP_DECRYPT_STORAGE_DATA, Byte *pData, Long Length,           */
	/*						Long Slot, cmcErr *RetCode );                                     */
	CMCI_CMCXP_DECRYPT_STORAGE_DATA,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_CMCXP_GET_KEY_DEVICE_INFO, String **pOutKeyDevice,              */
	/*		Long SlotDevice, cmcErr *RetCode );                                               */
    /*                                                                                        */
	CMCI_CMCXP_GET_KEY_DEVICE_INFO,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_ENABLE_CMC, Long wParam, Long lParam);                          */
    /*                                                                                        */
    CMCI_EMPOWER_PRT,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/*  cmcCustomFunction(CMCI_ADD_MARINE_CARTRIDGE, cmcMediaType  MediaType , BYTE *CDGInfo, */
	/*                                               TCHAR *CdgPath, TCHAR* FileName);        */
	/*  cmcCustomFunction(CMCI_ADD_MARINE_CARTRIDGE, MEDIA_SD, &CdGInfo,                      */
	/*                                               "C:\\Charts", "EWM20101");               */
	/*  cmcCustomFunction(CMCI_ADD_MARINE_CARTRIDGE, MEDIA_SD, &CdGInfo,                      */
	/*                                               "CARD0", "EWM20101");                    */
	/*                                                                                        */
	CMCI_ADD_MARINE_CARTRIDGE,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/*  cmcCustomFunction(CMCI_REMOVE_MARINE_CARTRIDGE,                                       */
	/*                                               TCHAR *CdgPath, TCHAR* FileName);        */
	/*  cmcCustomFunction(CMCI_REMOVE_MARINE_CARTRIDGE, "C:\\Charts", "EWM20101");            */
	/*  cmcCustomFunction(CMCI_REMOVE_MARINE_CARTRIDGE, "CARD0", "EWM20101");                 */
	/*                                                                                        */
	CMCI_REMOVE_MARINE_CARTRIDGE,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/*  cmcCustomFunction(CMCI_GET_MARINE_CARTRIDGE_PATH, cmcMediaType  MediaType ,           */
	/*                                      BYTE *CDGInfo, TCHAR *CdgPath, DWORD CdGStrLen);  */
	/*  cmcCustomFunction(CMCI_GET_MARINE_CARTRIDGE_PATH, MEDIA_SD, &CdGInfo,                 */
	/*                                               &CDGPath, sizeof(CDGPath)/sizeof(TCHAR));*/
	/*                                                                                        */
	CMCI_GET_MARINE_CARTRIDGE_PATH,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_GET_CDG_SLOT_REMAP, Word *CDGSlot);                             */
    /*                                                                                        */
    CMCI_GET_CDG_SLOT_REMAP,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USBDPS_DEVICE_AVAILABLE, Bool *Res);                            */
	CMCI_USBDPS_DEVICE_AVAILABLE,

    /*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USBDPS_GET_READER_ID, Long *code);                              */
    CMCI_USBDPS_GET_READER_ID,

	/*----------------------------------------------------------------------------------------*/
    /*                                                                                        */
    /* cmcCustomFunction(CMCI_USBDPS_CMD_RESTART);                                            */
    /*                                                                                        */
	CMCI_USBDPS_CMD_RESTART,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/* cmcCustomFunction(CMCI_REMOVE_CARTRIDGE_PATH, TCHAR *path);                            */
	/* cmcCustomFunction(CMCI_REMOVE_CARTRIDGE_PATH, _T("C:\\Charts\\"));	                  */
	/*                                                                                        */
	CMCI_REMOVE_CARTRIDGE_PATH,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/* cmcCustomFunction(CMCI_CHECK_CARTRIDGE_PATH, TCHAR *path, Bool *CdgDataPresent);       */
	/* cmcCustomFunction(CMCI_CHECK_CARTRIDGE_PATH, _T("C:\\Charts\\"), &CdgDataPresent);	  */
	/*                                                                                        */
	CMCI_CHECK_CARTRIDGE_PATH,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/*  cmcCustomFunction(CMCI_SET_WWB_CARTRIDGE_PATH,TCHAR *pWWBpath);                       */
	/*  cmcCustomFunction(CMCI_SET_WWB_CARTRIDGE_PATH,_T("C:\\Cartridges\\WWB\\"));	          */
	/*                                                                                        */
	CMCI_SET_WWB_CARTRIDGE_PATH,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/* cmcCustomFunction(CMCI_GET_CARTRIDGE_PATH, Long CdgNum, TCHAR *pChartPath,             */
	/*                                            Long PathLen, cmcErr *RetCode );            */
	/* cmcCustomFunction(CMCI_GET_CARTRIDGE_PATH, 1, &ChartPath,                              */
	/*                                            sizeof(ChartPath)/sizeof(TCHAR), &RetCode); */
	/*                                                                                        */
	CMCI_GET_CARTRIDGE_PATH,

	/*----------------------------------------------------------------------------------------*/
	/*                                                                                        */
	/* cmcCustomFunction(CMCI_CHECK_HASH, Long StartMem, Long EndMem, Long ExpHash );         */
	/*                                                                                        */
	CMCI_CHECK_HASH

};

/**
* Get the a real slot position of cartridges.
*
* The \c CMCI_GET_CDG_SLOT_REMAP custom function, return a real physical Slot position of cartridge.\n
* This custom function can be used on library that position of C-CARD swapped.\n
*
* <b> Function prototype:\n
*
* void cmcCustomFunction(CMCI_GET_CDG_SLOT_REMAP, Word *CDGSlot); </b> 
*
* @since		Version 7.05.03.03R.
*
* @param[in,out]	&CDGSlot	CDGSlot parameter is the "logical" Slot of Chart Plotter.\n
*								The value returned is the "REAL" physical Slot.
*
* \note			<b> This cmc Custom function is available only for cmc XP module. </b>
*
* \code
*	
*			for ( i = 0; i < CDG_NUM; i++ )
*			{
*				Word   RealCDGSlot;
*				RealCDGSlot = i;
*
*				// Get the real Physical Slot
*				cmcCustomFunction( CMCI_GET_CDG_SLOT_REMAP, &RealCDGSlot );
*
*				// SlotIndex: Slot to be tested
*				err[RealCDGSlot] += CCARD_ConnectorTest( RealCDGSlot+1 );
*			}
*
* \endcode
* 
* \ingroup		CMC_CUSTOM_FUNCT
*/
#ifdef CMC_DOC
CMCI_GET_CDG_SLOT_REMAP( Word *CDGSlot );
#endif


/**
* Get a valid cartridge path from Marine Cartridge Info.
*
* The \c CMCI_GET_MARINE_CARTRIDGE_PATH custom function, convert the Marine Cartridges Info\n
* to a string ( 8 character ). This custom function can be used to stores Marine cartridges\n
* inside a device storage using the path returned.
*
* <b> Function prototype:\n
*
* void cmcCustomFunction( CMCI_GET_MARINE_CARTRIDGE_PATH, cmcMediaType  MediaType,\n
*								 BYTE *CDGInfo, TCHAR *CdgPath, DWORD CdGStrLen ); </b> 
*
* @since		Version 7.05.03.02B.
*
* @param[in]	MediaType	Identify the type of source marine cartridge.\n
*							MEDIA_SD for SD device, MEDIA_CF for Compact Flash device.
* @param[in]	CDGInfo		This is a Device Information buffer. This buffer contains Serial Number,\n
*							Revision Number and Manufactory ID of source marine cartridge.\n
*							The size of this buffer must be CMC_DEVICE_INFO_SIZE\n
*							( defined in \c cmc.h header file ).\n
*							Ask to C-MAP to know how fill this buffer with this informations.\n
* @param[out]	CdgPath		TCHAR buffer where will be store the path.\n
*							The minimum size of this buffer must be at least CMC_STORAGE_PATH_SIZE\n
*							( defined in \c cmc.h header file ).
* @param[in]	CdGStrLen	Size ( in TCHAR ) of path.
*
* \note			<b> This cmc Custom function is available only for cmc Class module. </b>
*
* @see			\c CMCI_ADD_MARINE_CARTRIDGE, \c CMCI_REMOVE_MARINE_CARTRIDGE.
*
* \code
*		
*			void AddMarineCartridge( void )
*			{
*				BYTE		*pEmulateFirstKey;
*				TCHAR		CdgFirstPath[MAX_PATH]=_T("\0");
*
*				pEmulateFirstKey = (BYTE*) malloc(CMC_DEVICE_INFO_SIZE);
*				memset(pEmulateFirstKey, 0, CMC_DEVICE_INFO_SIZE);
*				pEmulateFirstKey[0]= 0x27;
*				pEmulateFirstKey[1]= 0x50;
*				pEmulateFirstKey[2]= 0x48;
*				pEmulateFirstKey[3]= 0x11;
*				pEmulateFirstKey[4]= 0xB0;
*				pEmulateFirstKey[5]= 0x00;
*				pEmulateFirstKey[6]= 0x0D;
*				pEmulateFirstKey[7]= 0x59;
*
*				// Get Path where the cartridges is stored, using the Device Info.
*				cmcCustomFunction( CMCI_GET_MARINE_CARTRIDGE_PATH, MEDIA_SD,
*									pEmulateFirstKey, CdgFirstPath, sizeof(CdgFirstPath)/sizeof(TCHAR) );
*				//Add cartridges.
*				cmcCustomFunction( CMCI_ADD_MARINE_CARTRIDGE, MEDIA_SD, pEmulateFirstKey,
*									CdgFirstPath, _T("EWM20101") );	
*
*				free(pEmulateFirstKey);
*			}
*
*			void RemoveMarineCartridge( void )
*			{
*				BYTE		*pEmulateFirstKey;
*				TCHAR		CdgFirstPath[MAX_PATH]=_T("\0");
*
*				pEmulateFirstKey = (BYTE*) malloc(CMC_DEVICE_INFO_SIZE);
*				memset(pEmulateFirstKey, 0, CMC_DEVICE_INFO_SIZE);
*				pEmulateFirstKey[0]= 0x27;
*				pEmulateFirstKey[1]= 0x50;
*				pEmulateFirstKey[2]= 0x48;
*				pEmulateFirstKey[3]= 0x11;
*				pEmulateFirstKey[4]= 0xB0;
*				pEmulateFirstKey[5]= 0x00;
*				pEmulateFirstKey[6]= 0x0D;
*				pEmulateFirstKey[7]= 0x59;
*
*				// Get Path where the cartridges is stored, using the Device Info.
*				cmcCustomFunction( CMCI_GET_MARINE_CARTRIDGE_PATH, MEDIA_SD,
*									pEmulateFirstKey, CdgFirstPath, sizeof(CdgFirstPath)/sizeof(TCHAR) );
*				// Remove cartridge.
*				cmcCustomFunction( CMCI_REMOVE_MARINE_CARTRIDGE, CdgFirstPath, _T("EWM20101") );	
*
*				free(pEmulateFirstKey);
*			}
*
* \endcode
* 
* \ingroup		CMC_CUSTOM_FUNCT
*/
#ifdef CMC_DOC
CMCI_GET_MARINE_CARTRIDGE_PATH( cmcMediaType  MediaType, BYTE *CDGInfo, TCHAR *CdgPath, DWORD CdGStrLen );
#endif


/**
* Add a Marine cartridge.
*
* The \c CMCI_ADD_MARINE_CARTRIDGE custom function, remove from CMC module the specified cartridges.
*
* <b> Function prototype:\n
*
* cmcCustomFunction( CMCI_ADD_MARINE_CARTRIDGE, cmcMediaType  MediaType , BYTE *CDGInfo,\n
							TCHAR *CdgPath, TCHAR* FileName ); </b>
*
* @since		Version 7.05.03.02B.
*
* @param[in]	MediaType	Identify the type of source marine cartridge.\n
*							MEDIA_SD for SD device, MEDIA_CF for Compact Flash device.
* @param[in]	CDGInfo		This is a Device Information buffer. This buffer contains Serial Number,\n
*							Revision Number and Manufactory ID of source marine cartridge.\n
*							The size of this buffer must be CMC_DEVICE_INFO_SIZE\n
*							( defined in \c cmc.h header file ).\n
*							Ask to C-MAP to know how fill this buffer with this informations.\n
* @param[in]	CdgPath		Path where the chart is stored.\n
*							Can be the PATH returned by the CMCI_GET_MARINE_CARTRIDGE_PATH cmc custom function.
* @param[in]	FileName	Cartridge name.
*
* \note			<b> This cmc Custom function is available only for cmc Class module. </b>
*
* @see			\c CMCI_GET_MARINE_CARTRIDGE_PATH, \c CMCI_GET_MARINE_CARTRIDGE_PATH.
*
* \code
*		
*			void AddMarineCartridge( void )
*			{
*				BYTE		*pEmulateFirstKey;
*				TCHAR		CdgFirstPath[MAX_PATH]=_T("\0");
*
*				pEmulateFirstKey = (BYTE*) malloc(CMC_DEVICE_INFO_SIZE);
*				memset(pEmulateFirstKey, 0, CMC_DEVICE_INFO_SIZE);
*				pEmulateFirstKey[0]= 0x27;
*				pEmulateFirstKey[1]= 0x50;
*				pEmulateFirstKey[2]= 0x48;
*				pEmulateFirstKey[3]= 0x11;
*				pEmulateFirstKey[4]= 0xB0;
*				pEmulateFirstKey[5]= 0x00;
*				pEmulateFirstKey[6]= 0x0D;
*				pEmulateFirstKey[7]= 0x59;
*
*				// Get Path where the cartridges is stored, using the Device Info.
*				cmcCustomFunction( CMCI_GET_MARINE_CARTRIDGE_PATH, MEDIA_SD,
*									pEmulateFirstKey, CdgFirstPath, sizeof(CdgFirstPath)/sizeof(TCHAR) );
*				//Add cartridges.
*				cmcCustomFunction( CMCI_ADD_MARINE_CARTRIDGE, MEDIA_SD, pEmulateFirstKey,
*									CdgFirstPath, _T("EWM20101") );	
*
*				free(pEmulateFirstKey);
*			}
*
*			void RemoveMarineCartridge( void )
*			{
*				BYTE		*pEmulateFirstKey;
*				TCHAR		CdgFirstPath[MAX_PATH]=_T("\0");
*
*				pEmulateFirstKey = (BYTE*) malloc(CMC_DEVICE_INFO_SIZE);
*				memset(pEmulateFirstKey, 0, CMC_DEVICE_INFO_SIZE);
*				pEmulateFirstKey[0]= 0x27;
*				pEmulateFirstKey[1]= 0x50;
*				pEmulateFirstKey[2]= 0x48;
*				pEmulateFirstKey[3]= 0x11;
*				pEmulateFirstKey[4]= 0xB0;
*				pEmulateFirstKey[5]= 0x00;
*				pEmulateFirstKey[6]= 0x0D;
*				pEmulateFirstKey[7]= 0x59;
*
*				// Get Path where the cartridges is stored, using the Device Info.
*				cmcCustomFunction( CMCI_GET_MARINE_CARTRIDGE_PATH, MEDIA_SD,
*									pEmulateFirstKey, CdgFirstPath, sizeof(CdgFirstPath)/sizeof(TCHAR) );
*				// Remove cartridge.
*				cmcCustomFunction( CMCI_REMOVE_MARINE_CARTRIDGE, CdgFirstPath, _T("EWM20101") );	
*
*				free(pEmulateFirstKey);
*			}
*
* \endcode
* 
* \ingroup		CMC_CUSTOM_FUNCT
*/
#ifdef CMC_DOC
CMCI_ADD_MARINE_CARTRIDGE( cmcMediaType  MediaType , BYTE *CDGInfo, TCHAR *CdgPath, TCHAR* FileName );
#endif


/**
* Remove Marine cartridge.
*
* The \c CMCI_REMOVE_MARINE_CARTRIDGE custom function, remove from CMC module the specified cartridges.
*
* <b> Function prototype:\n
*
* void cmcCustomFunction( CMCI_REMOVE_MARINE_CARTRIDGE, TCHAR *CdgPath,\n
*												TCHAR* FileName ); </b>
*
* @since		Version 7.05.03.02B.
*
* @param[in]	CdgPath		Path where the chart is stored.\n
*							Can be the PATH returned by the CMCI_GET_MARINE_CARTRIDGE_PATH cmc custom function.
* @param[in]	FileName	Cartridges name.
*
* \note			<b> This cmc Custom function is available only for cmc Class module. </b>
*
* @see			\c CMCI_ADD_MARINE_CARTRIDGE, \c CMCI_GET_MARINE_CARTRIDGE_PATH.
*
* \code
*		
*			void AddMarineCartridge( void )
*			{
*				BYTE		*pEmulateFirstKey;
*				TCHAR		CdgFirstPath[MAX_PATH]=_T("\0");
*
*				pEmulateFirstKey = (BYTE*) malloc(CMC_DEVICE_INFO_SIZE);
*				memset(pEmulateFirstKey, 0, CMC_DEVICE_INFO_SIZE);
*				pEmulateFirstKey[0]= 0x27;
*				pEmulateFirstKey[1]= 0x50;
*				pEmulateFirstKey[2]= 0x48;
*				pEmulateFirstKey[3]= 0x11;
*				pEmulateFirstKey[4]= 0xB0;
*				pEmulateFirstKey[5]= 0x00;
*				pEmulateFirstKey[6]= 0x0D;
*				pEmulateFirstKey[7]= 0x59;
*
*				// Get Path where the cartridges is stored, using the Device Info.
*				cmcCustomFunction( CMCI_GET_MARINE_CARTRIDGE_PATH, MEDIA_SD,
*									pEmulateFirstKey, CdgFirstPath, sizeof(CdgFirstPath)/sizeof(TCHAR) );
*				//Add cartridges.
*				cmcCustomFunction( CMCI_ADD_MARINE_CARTRIDGE, MEDIA_SD, pEmulateFirstKey,
*									CdgFirstPath, _T("EWM20101") );	
*
*				free(pEmulateFirstKey);
*			}
*
*			void RemoveMarineCartridge( void )
*			{
*				BYTE		*pEmulateFirstKey;
*				TCHAR		CdgFirstPath[MAX_PATH]=_T("\0");
*
*				pEmulateFirstKey = (BYTE*) malloc(CMC_DEVICE_INFO_SIZE);
*				memset(pEmulateFirstKey, 0, CMC_DEVICE_INFO_SIZE);
*				pEmulateFirstKey[0]= 0x27;
*				pEmulateFirstKey[1]= 0x50;
*				pEmulateFirstKey[2]= 0x48;
*				pEmulateFirstKey[3]= 0x11;
*				pEmulateFirstKey[4]= 0xB0;
*				pEmulateFirstKey[5]= 0x00;
*				pEmulateFirstKey[6]= 0x0D;
*				pEmulateFirstKey[7]= 0x59;
*
*				// Get Path where the cartridges is stored, using the Device Info.
*				cmcCustomFunction( CMCI_GET_MARINE_CARTRIDGE_PATH, MEDIA_SD,
*									pEmulateFirstKey, CdgFirstPath, sizeof(CdgFirstPath)/sizeof(TCHAR) );
*				// Remove cartridge.
*				cmcCustomFunction( CMCI_REMOVE_MARINE_CARTRIDGE, CdgFirstPath, _T("EWM20101") );	
*
*				free(pEmulateFirstKey);
*			}
*
* \endcode
* 
* \ingroup		CMC_CUSTOM_FUNCT
*/
#ifdef CMC_DOC
CMCI_REMOVE_MARINE_CARTRIDGE( TCHAR *CdgPath, TCHAR* FileName );
#endif

/**
* Set the cartridges path.
*
* The \c CMCI_SET_CARTRIDGE_PATH custom function, set the path where the cartridges data is stored.
*
* <b> Function prototype:\n
*
* void cmcCustomFunction( CMCI_SET_CARTRIDGE_PATH, TCHAR *CdgPath ); </b>
*
* @since		Version 1.00.00.00R.
*
* @param[in]	CdgPath		Path where the chart is stored.\n
*
* \note			<b> This cmc Custom function is available for cmcClass and cmcXP module.\n
*				This function must be called before call \c cmcInit() or \c cmInit(). </b>
*
* \code
*		
*			cmErr MAP_Init( Double ScreenRes, _TCHAR  *Chart_Path  )
*			{
*				_TCHAR			SelectorChartPath[MAX_PATH]=_T("\0");
*				_TCHAR			NTPCPath[MAX_PATH]=_T("\0");
*
*				#ifdef EMBEDDED_APPLICATION			// OEM Embedded Application
*					_tcscpy(SelectorChartPath,Chart_Path);
*				#else								// NT/MAX PC Selector Application.
*					KSH_InitKeySafe();
*					KSH_SetOEMAppCode(OEMcode);
*
*					// Get NTPC Selector Path.
*					KSH_GetWebMapAppPath( NTPCPath, MAX_PATH );
*					// Get Chart Path.
*					KSH_GetDestinationPath( SelectorChartPath, MAX_PATH );
*				#endif
*
*				// Set the folder in which the CHART file is stored
*				// (for example Application Path or NT/MAX PC Selector Path).
*				cmcCustomFunction( CMCI_SET_CARTRIDGE_PATH, SelectorChartPath );
*
*				// Enable the enumeration of the cartridge
*				cmcCustomFunction( CMCI_ENABLE_CMC , TRUE );
*
*				// Set the PPM (Pixel per Meter = (Pixel per Inch) *  0.0254) resolution.
*				cmgSetScreenRes( ScreenRes );
*
*				// Initialize the Cartographic Library. In case cmInit() exits with a value different
*				// from cm_NO_ERROR, it means the library has NOT been configured properly and the
*				// application cannot display the C-Map cartography.
*				return cmInit();
*			}
*
* \endcode
* 
* \ingroup		CMC_CUSTOM_FUNCT
*/
#ifdef CMC_DOC
CMCI_SET_CARTRIDGE_PATH( TCHAR *CdgPath );
#endif


/*------------------------------------------*/
/* Return Code for CMCI_GET_WWB_TYPE		*/
/*------------------------------------------*/

/* Type of WWB possible values */
#define	WWM_TYPE_00000		4L
#define	WWB_TYPE_00201		3L
#define	WWB_TYPE_00200		2L
#define	WWB_TYPE_00100		1L


/*------------------------------------------*/
/* Defines for CMCI_GET_CCARD_TYPE			*/
/*------------------------------------------*/

/* Mode possible values */
#define	CMC_LOGICAL_MODE	0L	/* CdgNum: 0=WWB, 1=cdg1, 2=cdg2, 3=cdg3...	*/
#define	CMC_SLOT_MODE		1L	/* CdgNum: 1=Slot 1, 2=Slot 2, ...			*/

/* pRes possible values */
#define	CMC_DATA_CCARD			0	/*	Data C-Card								*/
#define	CMC_UNKNOWN_CCARD		1	/*	unknown (could be User C-Card)			*/
#define	CMC_CM_MODULE			2	/*  Communication Module					*/
#define	CMC_NOT_PRESENT			3	/*  Not Present								*/
#define	CMC_SW_CCARD			4	/*  C-MAP Chart Plotter SW c-card (PROG)	*/
#define	CMC_SOFT_CCARD			5	/*  Generic SW c-card (SOFT)				*/
#define	CMC_USER2_CCARD			6	/*  User Type 2 - accessed with CMU module	*/
#define	CMC_UPS_CCARD			7	/*  UPS C-Card								*/
#define	CMC_UPLOAD1_CCARD		8	/*  Upload C-Card: Geosat					*/
#define	CMC_UPLOAD2_CCARD		9	/*  Upload C-Card: EX20X					*/
#define	CMC_DEMO_CCARD			10	/*  Demo C-Card								*/
#define	CMC_UPLOAD_CMP_CCARD	11	/*  Upload C-Card: Component Upload			*/
#define	CMC_LOAD_CCARD			12	/*  C-MAP Chart Plotter Loader c-card (LOAD)*/


/*------------------------------------------*/
/* Defines for CMCI_ADD_CARTRIDGE_EX modes */
/*------------------------------------------*/

#define CMC_ADD_CARTRIDGE_FILE			0
#define CMC_ADD_CARTRIDGE_PCCARD		0x01



PRE_EXPORT_H void       IN_EXPORT_H cmcCustomFunction(Long FunctionCode, ...);
PRE_EXPORT_H void       IN_EXPORT_H cmcCustomFunctionB(Long FunctionCode, void *P1, void *P2, void *P3, void *P4, void *P5, void *P6, void *P7, void *P8, void *P9, void *P10);



#define cmc_SI_CDG_REMOVABLE   0

PRE_EXPORT_H Long       IN_EXPORT_H cmcGetSpecialInfo(Word Query,Word CdgNum);


/* cmcDebug data structure */
typedef struct
{
	SLong nGetByte;
	SLong nFastGetByte;
	SLong nGetWord;
	SLong nGetTriple;
	SLong nGetLong;
	SLong nGetBuf;

	SLong totalDataRead;

	SLong nSetDirection;
	SLong nSetPointer;
	SLong nSetCartridge;

	/* New statistical fields */

	SLong	nMinBytesXSetPointer;
	SLong	nMaxBytesXSetPointer;
	Double	dMeanBytesXSetPointer;
	SLong	nTempBytesXSetPointer;

	SLong	nMinBytesXSetCartridge;
	SLong	nMaxBytesXSetCartridge;
	Double	dMeanBytesXSetCartridge;
	SLong	nTempBytesXSetCartridge;

	/* Device stat fields */
	SLong	nDevGetBlock;
	SLong	nDevGetBlock2;
	SLong	nDevtotalDataRead;

} sCmcDebug;

/* CMCI_GET_INFO associated structure */
typedef struct
{
	SByte Slot;
	Byte LogIndex;
	Bool bIsPresent;
	Bool bIsUsed;
    Bool bIsCartridge;
	Bool bIsBackGround;
	Word CdgVersion;
} sCmcInfo;


/* Command message to retrieve or set the abilitation status of the device */
#define CMCI_DEVICE_STATUS	0x01

/* Parameters for CMCI_DEVICE_STATUS */
#define CMCI_DEVICE_STATUS_DISABLED		0
#define CMCI_DEVICE_STATUS_ENABLED		1
#define CMCI_DEVICE_STATUS_NOT_PRESENT	2



/* Command Message to retrieve the number of available cartridges */
#define CMCI_DEVICE_CDGS	0x02

typedef struct
{
	Word	dsDevice;

	Word	dsFlags;

	Word	dsStatus;
	Word	dsNumOfCdgs;

	void	*dsDevicePtr;

} sCmcDeviceStatus;

/* Command message to retrieve or set the abilitation status of the cache */
#define CMCI_CACHE_STATUS	0x01

#define CMCI_CACHE_CARTRIDGE_LOCK		0
#define CMCI_CACHE_CARTRIDGE_UNLOCK		1

typedef struct
{
	Word	csCache;

	Word	csMessage;

	Word	csCdgNum;

	void	*csCachePtr;

} sCmcCacheStatus;


/* Cache statistics functions. */
PRE_EXPORT_H void IN_EXPORT_H cmcStatisticInit ( void ); 
PRE_EXPORT_H void IN_EXPORT_H cmcStatisticPrint ( Bool Reset );

#ifdef __cplusplus
}
#endif

#endif
