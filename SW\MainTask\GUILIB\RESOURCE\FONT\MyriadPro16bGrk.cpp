/*...........................................................................*/
/*.                  File Name : MyriadPro16bGrk.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY MyriadPro16bRus_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro16bGrk_offset_table[129] = {
0x0000,0x0004,0x0008,0x000c,0x0010,0x0014,0x0018,0x001c,0x0020,0x0024,0x0028,0x002e,0x0032,0x0036,0x003a,0x003e,
0x0042,0x0048,0x0051,0x005e,0x0064,0x0071,0x0081,0x0089,0x008d,0x009d,0x00a1,0x00b1,0x00c1,0x00c9,0x00d6,0x00e2,
0x00ec,0x00f9,0x0104,0x0110,0x011e,0x012c,0x0132,0x013e,0x014b,0x015c,0x016a,0x0175,0x0183,0x0190,0x019c,0x01a0,
0x01ac,0x01b7,0x01c4,0x01d4,0x01e0,0x01ef,0x01fe,0x0205,0x0212,0x021e,0x0227,0x0233,0x0239,0x0244,0x0250,0x025c,
0x0267,0x0273,0x027c,0x0285,0x0291,0x029c,0x02a2,0x02ad,0x02b7,0x02c3,0x02cd,0x02d6,0x02e2,0x02ee,0x02fa,0x0303,
0x030f,0x0318,0x0323,0x0331,0x033b,0x034a,0x0359,0x0360,0x036b,0x0377,0x0382,0x0391,0x0395,0x0399,0x039d,0x03a1,
0x03a5,0x03a9,0x03ad,0x03b1,0x03b5,0x03b9,0x03bd,0x03c1,0x03c5,0x03c9,0x03cd,0x03d1,0x03d5,0x03d9,0x03dd,0x03e1,
0x03e5,0x03e9,0x03ed,0x03f1,0x03f5,0x03f9,0x03fd,0x0401,0x0405,0x0409,0x040d,0x0411,0x0415,0x0419,0x041d,0x0421,
0x0425};



ROMDATA PEGUBYTE MyriadPro16bGrk_data_table[3325] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0xb8, 0xee, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xee, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xdb, 0x33, 0xc0, 0x06, 0xff, 0x37, 0x07, 
0x37, 0x06, 0x1f, 0x00, 0x76, 0x01, 0x61, 0xf0, 0x6d, 0x87, 0x81, 0xfe, 0x1f, 0xe0, 0xf0, 0x3f, 
0xc3, 0xfe, 0x70, 0x70, 0x1f, 0x07, 0x1c, 0x3c, 0x1e, 0x07, 0x83, 0xc7, 0x83, 0x9f, 0xf8, 0x3e, 
0x0f, 0xfe, 0x7f, 0x00, 0x7f, 0xcf, 0xfe, 0xc0, 0x20, 0x38, 0x0f, 0x0f, 0x73, 0x9c, 0x0f, 0x80, 
0xe3, 0x00, 0x80, 0xc0, 0x1c, 0x0e, 0x07, 0x1b, 0xc0, 0x00, 0x0f, 0x00, 0x00, 0x0f, 0x00, 0x03, 
0xf8, 0x00, 0x07, 0x80, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0x3b, 0x80, 0x70, 0x0e, 
0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xdb, 0x33, 0xc0, 0x06, 0xff, 0x37, 0x07, 
0x37, 0x06, 0x7f, 0xc0, 0x77, 0x07, 0xe7, 0xfc, 0x6d, 0x87, 0x81, 0xff, 0x1f, 0xe0, 0xf0, 0x3f, 
0xc3, 0xfe, 0x70, 0x70, 0x7f, 0xc7, 0x1c, 0x78, 0x1e, 0x07, 0x83, 0xc7, 0xc3, 0x9f, 0xf8, 0xff, 
0x8f, 0xfe, 0x7f, 0xc0, 0x7f, 0xcf, 0xfe, 0xe0, 0xe0, 0x7e, 0x07, 0x1e, 0x73, 0x9c, 0x3f, 0xe0, 
0xe3, 0x83, 0x81, 0x80, 0x38, 0x0c, 0x06, 0x1e, 0xc0, 0x00, 0x3f, 0x80, 0x00, 0x3f, 0x80, 0x03, 
0xf8, 0x00, 0x0f, 0xc0, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0x3b, 0x80, 0xe0, 0x0c, 
0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x18, 0x37, 0xe0, 0x04, 0xe0, 0x27, 0x07, 
0x27, 0x06, 0xf1, 0xe0, 0x63, 0x8f, 0x6f, 0x1e, 0x08, 0x0f, 0xc1, 0xc7, 0x9c, 0x01, 0xf8, 0x38, 
0x00, 0x1c, 0x70, 0x70, 0xf1, 0xe7, 0x1c, 0xf0, 0x3f, 0x06, 0xc6, 0xc7, 0xc3, 0x80, 0x01, 0xe3, 
0xce, 0x0e, 0x71, 0xe0, 0x38, 0x00, 0xe0, 0xf1, 0xf1, 0xff, 0x83, 0x9c, 0x73, 0x9c, 0x78, 0xf0, 
0xe7, 0xc7, 0xc1, 0x80, 0x30, 0x0c, 0x0e, 0x06, 0x00, 0x00, 0x39, 0xc0, 0x00, 0x38, 0x80, 0x00, 
0xf0, 0x00, 0x1c, 0xe0, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x18, 
0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0xe0, 0x07, 0x07, 
0x07, 0x00, 0xe0, 0xe0, 0x01, 0xcc, 0x0e, 0x0e, 0x00, 0x0f, 0xc1, 0xc3, 0x9c, 0x01, 0xf8, 0x38, 
0x00, 0x3c, 0x70, 0x70, 0xe0, 0xe7, 0x1c, 0xe0, 0x3f, 0x06, 0xc6, 0xc7, 0xe3, 0x80, 0x01, 0xc1, 
0xce, 0x0e, 0x70, 0xe0, 0x1c, 0x00, 0xe0, 0x79, 0xc3, 0xbb, 0xc3, 0x9c, 0x73, 0x9c, 0x70, 0x70, 
0xe1, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0xc0, 0x00, 0x3c, 0x00, 0x01, 
0xe0, 0x00, 0x18, 0x60, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x06, 0xe0, 0x00, 0xe0, 0x07, 0x07, 
0x07, 0x01, 0xc0, 0x70, 0x00, 0xdc, 0x1c, 0x07, 0x1c, 0x0d, 0xc1, 0xc3, 0x9c, 0x01, 0x98, 0x38, 
0x00, 0x38, 0x70, 0x71, 0xc0, 0xf7, 0x1d, 0xc0, 0x37, 0x06, 0xc6, 0xc7, 0xe3, 0x80, 0x03, 0x80, 
0xee, 0x0e, 0x70, 0xe0, 0x1e, 0x00, 0xe0, 0x3b, 0x83, 0xb9, 0xc1, 0xf8, 0x73, 0x9c, 0xe0, 0x38, 
0xe0, 0xee, 0x07, 0xb0, 0x7c, 0xce, 0x0e, 0x39, 0xc1, 0xec, 0x71, 0xcf, 0x0e, 0x1e, 0x03, 0xe1, 
0xc3, 0x38, 0x38, 0x77, 0x1c, 0x70, 0xf0, 0xe3, 0x9e, 0x39, 0xe0, 0x3c, 0x3f, 0xf8, 0x3c, 0x03, 
0xe1, 0xff, 0xff, 0x73, 0x81, 0x1c, 0x78, 0xee, 0x77, 0x0e, 0x0e, 0x1c, 0x73, 0x81, 0xe0, 0xe7, 
0x0e, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x06, 0x60, 0x00, 0xe0, 0x07, 0x07, 
0x07, 0x01, 0xc0, 0x70, 0x00, 0xd8, 0x1c, 0x07, 0x1c, 0x1c, 0xc1, 0xc7, 0x1c, 0x03, 0x9c, 0x38, 
0x00, 0x78, 0x70, 0x71, 0xc0, 0x77, 0x1f, 0xc0, 0x73, 0x06, 0xee, 0xc7, 0x73, 0x80, 0x03, 0x80, 
0xee, 0x0e, 0x70, 0xe0, 0x0e, 0x00, 0xe0, 0x1b, 0x07, 0x38, 0xe1, 0xf8, 0x73, 0x9c, 0xe0, 0x38, 
0xe0, 0x6c, 0x0f, 0xf1, 0xfc, 0xff, 0x0e, 0x38, 0xe3, 0xfc, 0x73, 0x87, 0x0e, 0x3f, 0x0f, 0xe3, 
0x83, 0xfc, 0x38, 0x77, 0x1c, 0xe0, 0xf0, 0xe3, 0x8e, 0x38, 0x78, 0xff, 0x3f, 0xf8, 0xff, 0x0f, 
0xe7, 0xff, 0xff, 0x71, 0xc7, 0xbf, 0x38, 0xce, 0x73, 0x8e, 0x0e, 0x1c, 0x71, 0xc7, 0xf8, 0xe3, 
0x8e, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x70, 0xc0, 0xff, 0x07, 0xff, 
0x07, 0x01, 0xc0, 0x70, 0x00, 0x78, 0x1c, 0x07, 0x1c, 0x1c, 0xe1, 0xfe, 0x1c, 0x03, 0x9c, 0x3f, 
0xc0, 0x70, 0x7f, 0xf1, 0xde, 0x77, 0x1f, 0xc0, 0x73, 0x86, 0xec, 0xe7, 0x73, 0x8f, 0xf3, 0x80, 
0xee, 0x0e, 0x71, 0xe0, 0x07, 0x00, 0xe0, 0x1f, 0x07, 0x38, 0xe0, 0xf0, 0x73, 0x9c, 0xe0, 0x38, 
0xe0, 0x7c, 0x0e, 0xf1, 0xc4, 0xe3, 0x8e, 0x38, 0xe3, 0xbc, 0x77, 0x03, 0x8e, 0x7f, 0x8e, 0x23, 
0x03, 0x8e, 0x3f, 0xf7, 0x1d, 0xc0, 0xf0, 0xe3, 0x8e, 0x38, 0xf8, 0xe7, 0x0e, 0x70, 0xe7, 0x0e, 
0x07, 0x38, 0x38, 0x71, 0xc7, 0x77, 0x39, 0xce, 0x73, 0x8c, 0x07, 0x1c, 0x71, 0xc7, 0x38, 0xe3, 
0x9c, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x71, 0xe0, 0xff, 0x07, 0xff, 
0x07, 0x01, 0xc0, 0x70, 0x00, 0x70, 0x1c, 0x07, 0x1c, 0x1c, 0xe1, 0xff, 0x1c, 0x03, 0x1c, 0x3f, 
0xc0, 0xe0, 0x7f, 0xf1, 0xde, 0x77, 0x1f, 0xc0, 0x73, 0x8e, 0x6c, 0xe7, 0x3b, 0x8f, 0xf3, 0x80, 
0xee, 0x0e, 0x7f, 0xc0, 0x07, 0x00, 0xe0, 0x0f, 0x07, 0x38, 0xe0, 0xf0, 0x3b, 0xb8, 0xe0, 0x38, 
0xe0, 0x3c, 0x1c, 0x71, 0xc0, 0xe3, 0x8e, 0x38, 0xe7, 0x1c, 0x77, 0xc3, 0x8c, 0x63, 0x8e, 0x07, 
0x03, 0x8e, 0x3f, 0xf7, 0x1f, 0x81, 0xf8, 0xe3, 0x8e, 0x31, 0xc1, 0xc3, 0x8e, 0x71, 0xc3, 0x9c, 
0x0e, 0x1c, 0x38, 0x71, 0xce, 0x73, 0x9d, 0x8e, 0x73, 0x9c, 0xe7, 0x1c, 0x71, 0xce, 0x1c, 0xe3, 
0x9c, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xf1, 0xe0, 0xe0, 0x07, 0x07, 
0x07, 0x01, 0xc0, 0x70, 0x00, 0x70, 0x1c, 0x07, 0x1c, 0x1f, 0xe1, 0xc7, 0x1c, 0x07, 0x0e, 0x38, 
0x01, 0xe0, 0x70, 0x71, 0xc0, 0x77, 0x1f, 0xe0, 0x63, 0x8e, 0x6c, 0xe7, 0x3b, 0x80, 0x03, 0x80, 
0xee, 0x0e, 0x7f, 0x00, 0x0e, 0x00, 0xe0, 0x0e, 0x07, 0x38, 0xe1, 0xf8, 0x3f, 0xf8, 0xe0, 0x38, 
0xe0, 0x38, 0x1c, 0x70, 0xf8, 0xe3, 0x8e, 0x38, 0xe7, 0x1c, 0x71, 0xc3, 0x9c, 0xe1, 0xc7, 0xc7, 
0x03, 0x8e, 0x38, 0x77, 0x1f, 0x81, 0xf8, 0xe3, 0x87, 0x33, 0x81, 0xc3, 0x8e, 0x71, 0xc3, 0x9c, 
0x0e, 0x1c, 0x38, 0x71, 0xce, 0x73, 0x9f, 0x8e, 0x73, 0x9c, 0xe7, 0x1c, 0x71, 0xce, 0x1c, 0xe3, 
0x9c, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0xc0, 0xe0, 0x07, 0x07, 
0x07, 0x01, 0xc0, 0x70, 0x00, 0x70, 0x0c, 0x06, 0x1c, 0x3f, 0xf1, 0xc3, 0x9c, 0x07, 0x0e, 0x38, 
0x01, 0xc0, 0x70, 0x71, 0xc0, 0xf7, 0x1c, 0xe0, 0xe1, 0xce, 0x7c, 0xe7, 0x1f, 0x80, 0x03, 0x80, 
0xee, 0x0e, 0x70, 0x00, 0x1c, 0x00, 0xe0, 0x0e, 0x07, 0x38, 0xe1, 0xf8, 0x0f, 0xe0, 0x60, 0x30, 
0xe0, 0x38, 0x1c, 0x70, 0xf8, 0xe3, 0x8e, 0x38, 0xe7, 0x1c, 0x70, 0xe1, 0xdc, 0xe1, 0xc7, 0xc7, 
0x03, 0x8e, 0x38, 0x77, 0x1f, 0xc1, 0xb8, 0xe3, 0x87, 0x73, 0x81, 0xc3, 0x8e, 0x71, 0xc3, 0x9c, 
0x0e, 0x1c, 0x38, 0x71, 0xce, 0x73, 0x8f, 0x0e, 0x73, 0x9c, 0xe7, 0x1c, 0x71, 0xce, 0x1c, 0xe3, 
0x9c, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x38, 0x00, 0xe0, 0x07, 0x07, 
0x07, 0x00, 0xe0, 0xe0, 0x00, 0x70, 0x0e, 0x0e, 0x1c, 0x38, 0x71, 0xc3, 0x9c, 0x07, 0x0e, 0x38, 
0x03, 0xc0, 0x70, 0x70, 0xe0, 0xe7, 0x1c, 0x70, 0xe1, 0xce, 0x78, 0xe7, 0x0f, 0x80, 0x01, 0xc1, 
0xce, 0x0e, 0x70, 0x00, 0x1c, 0x00, 0xe0, 0x0e, 0x03, 0xb9, 0xc3, 0x9c, 0x03, 0x80, 0x70, 0x70, 
0xe0, 0x38, 0x1c, 0x71, 0xc0, 0xe3, 0x8e, 0x38, 0xe7, 0x1c, 0x70, 0xe1, 0xf8, 0xe1, 0xce, 0x07, 
0x03, 0x8e, 0x18, 0x67, 0x1d, 0xc3, 0x9c, 0xe3, 0x87, 0x63, 0x81, 0xc3, 0x8e, 0x71, 0xc3, 0x9c, 
0x0e, 0x1c, 0x38, 0x71, 0xce, 0x73, 0x8f, 0x0e, 0x73, 0x1c, 0xe7, 0x1c, 0x71, 0xce, 0x1c, 0xe3, 
0x9c, 0xe7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x38, 0x00, 0xe0, 0x07, 0x07, 
0x07, 0x00, 0xf1, 0xe0, 0x00, 0x70, 0x06, 0x1c, 0x1c, 0x38, 0x71, 0xc7, 0x9c, 0x0e, 0x0f, 0x38, 
0x03, 0x80, 0x70, 0x70, 0xf1, 0xe7, 0x1c, 0x78, 0xe1, 0xce, 0x38, 0xe7, 0x0f, 0x80, 0x01, 0xe3, 
0xce, 0x0e, 0x70, 0x00, 0x38, 0x00, 0xe0, 0x0e, 0x03, 0xff, 0x83, 0x9c, 0x03, 0x80, 0x38, 0xe0, 
0xe0, 0x38, 0x1e, 0xf1, 0xc2, 0xe3, 0x8e, 0x39, 0xc7, 0xbc, 0x71, 0xe1, 0xf8, 0x73, 0x8e, 0x13, 
0xc3, 0x8e, 0x1c, 0xe7, 0x1c, 0xe3, 0x9c, 0xe3, 0x83, 0x61, 0xe0, 0xe7, 0x0c, 0x71, 0xe7, 0x0f, 
0x07, 0x38, 0x38, 0x73, 0x87, 0xf7, 0x0f, 0x07, 0x77, 0x1c, 0xe6, 0x1c, 0x73, 0x87, 0x38, 0xe7, 
0x1c, 0xe6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x3c, 0x00, 0xff, 0x07, 0x07, 
0x07, 0x00, 0x7f, 0xc0, 0x00, 0x70, 0x1f, 0xbf, 0x1e, 0x70, 0x79, 0xff, 0x1c, 0x0f, 0xff, 0x3f, 
0xc7, 0xfe, 0x70, 0x70, 0x7f, 0xc7, 0x1c, 0x39, 0xe1, 0xee, 0x38, 0xe7, 0x07, 0x9f, 0xf8, 0xff, 
0x8e, 0x0e, 0x70, 0x00, 0x7f, 0xe0, 0xe0, 0x0e, 0x00, 0xff, 0x07, 0x0e, 0x03, 0x80, 0xfd, 0xf8, 
0xe0, 0x38, 0x0f, 0xf9, 0xfe, 0xe3, 0x8f, 0x1f, 0xc3, 0xfe, 0x7f, 0xc0, 0xf0, 0x7f, 0x8f, 0xf3, 
0xfb, 0x8e, 0x0f, 0xc7, 0x9c, 0xf3, 0x9c, 0xfd, 0xc3, 0xc1, 0xfc, 0xff, 0x1c, 0x71, 0xff, 0x07, 
0xe7, 0xf8, 0x3c, 0x3f, 0x83, 0xfe, 0x0f, 0x07, 0xfe, 0x0f, 0xfe, 0x1e, 0x3f, 0x87, 0xf8, 0x7f, 
0x0f, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x38, 0x1c, 0x00, 0xff, 0x07, 0x07, 
0x07, 0x00, 0x1f, 0x00, 0x00, 0x70, 0x1f, 0xbf, 0x0e, 0x70, 0x39, 0xfc, 0x1c, 0x0f, 0xff, 0x3f, 
0xc7, 0xfe, 0x70, 0x70, 0x1f, 0x07, 0x1c, 0x3d, 0xc0, 0xee, 0x30, 0xe7, 0x07, 0x9f, 0xf8, 0x3e, 
0x0e, 0x0e, 0x70, 0x00, 0x7f, 0xe0, 0xe0, 0x0e, 0x00, 0x38, 0x0f, 0x0f, 0x03, 0x80, 0xfd, 0xf8, 
0xe0, 0x38, 0x07, 0x38, 0x7c, 0xe3, 0x87, 0x0f, 0x01, 0xce, 0x77, 0x80, 0xe0, 0x1e, 0x03, 0xe0, 
0xfb, 0x8e, 0x07, 0x83, 0x9c, 0x7f, 0x0e, 0xf9, 0xc3, 0xc0, 0x7c, 0x3c, 0x1c, 0x71, 0xdc, 0x03, 
0xe1, 0xe0, 0x1c, 0x1e, 0x01, 0xfc, 0x1f, 0x81, 0xfc, 0x07, 0xbc, 0x0e, 0x1e, 0x01, 0xe0, 0x3c, 
0x07, 0xbc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x70, 0x00, 0xe0, 0x00, 0x00, 0x00, 
0x30, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x01, 0xc0, 0x00, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x70, 0x1b, 0x80, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x70, 0x00, 0xe0, 0x00, 0x00, 0x00, 
0x70, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x18, 0x00, 0x00, 0x01, 0xc0, 0x00, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x70, 0x39, 0xc0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x70, 0x00, 0xe0, 0x00, 0x00, 0x00, 
0x30, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x08, 0x00, 0x00, 0x01, 0xc0, 0x00, 
0x40, 0x00, 0x00, 0x00, 0x00, 0x70, 0x31, 0xc0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x70, 0x00, 0xe0, 0x00, 0x00, 0x00, 
0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x70, 0xe0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY MyriadPro16bGrk_Font = {0x01, 25, 0, 25, 0, 0, 25, 133, 0x0374, 0x03f3,
(PEGUSHORT *) MyriadPro16bGrk_offset_table, &MyriadPro16bRus_Font,
(PEGUBYTE *) MyriadPro16bGrk_data_table};


