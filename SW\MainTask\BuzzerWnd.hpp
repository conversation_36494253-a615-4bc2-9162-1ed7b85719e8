#include "Wnd.hpp"
#include "ComboCtrl.hpp"

#ifndef __BUZZER_WND_HPP__
#define __BUZZER_WND_HPP__

class CBuzzerWnd : public CWnd {
	protected:
		CComboCtrl *m_pKeyBuzzer;
		CComboCtrl *m_pAlarmBuzzer;

		int m_nFocus;

		enum {
			FOCUS_KEY = 0,
			FOCUS_ALARM
		};

	public:
		CBuzzerWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		void InitBuzzerSetting();
};

#endif


