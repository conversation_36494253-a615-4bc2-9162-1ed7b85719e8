/*  Timelib  Time services.
    Original XTide source code date: 1997-08-15
    Last modified 1998-09-07 by <PERSON> for WXTide32

    Copyright (C) 1997  <PERSON>.
    Also starring:  <PERSON>; <PERSON>; <PERSON>;
    <PERSON>.

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

    Changes by <PERSON> for WXTide32:
    Changed do_timestamp to shorten NT's LONG time zone names just the CAPS
    Changed _hpux selector to WIN32 to select HP timezone values.
    Added a whole set of remote TZ handler routines, all starting with "tz".
*/

#include "nvtypes.h"
#include "everythi.h"
//#include <windows.h>
//#include <winbase.h>
#include <wchar.h>

#ifdef  __cplusplus
extern "C" {
#endif
//extern DWORD *pTideDbg;
#ifdef  __cplusplus
}
#endif

/**************************************************************************/

/* Declarations for zoneinfo compatibility */

/* Most of these entries are loaded from the tzdata.h include file. That
   file was generated from tzdata200c.                                  */

char *tz_names[][2] = {
#include "tzdata.h"
//{":Etc/GMT0",                   "GMT0", "GMT0"},
//{":Etc/Greenwich",              ":Greenwich", "GMT0", "GMT0"},
//{":Etc/UCT",                    ":UCT", "GMT0", "GMT0"},
//{":Etc/UTC",                    ":UTC", "GMT0", "GMT0"},
//{":Etc/Universal",              ":Universal", "GMT0", "GMT0"},
//{":Etc/Zulu",                   ":Zulu", "GMT0", "GMT0"},

/* Terminator */
{NULL, NULL}};


/**************************************************************************/

/* Turn a time displacement of the form [-]HH:MM into the number of seconds. */
int
hhmm2seconds (char *hhmm)
{
  int h, m;
  char s;
  if (sscanf (hhmm, "%d:%d", &h, &m) != 2)
    barf (BADHHMM);
  if (sscanf (hhmm, "%c", &s) != 1)
    barf (BADHHMM);
  if (h < 0 || s == '-')
    m = -m;
  return h*HOURSECONDS + m*60;
}

/* The inverse (changed 6/12/2005 to remove zero tens of hours) */
char *
seconds2hhmm (int seconds) {
  char sign;
  static char buf[80];
  if (seconds < 0) {
    sign = '-';
    seconds = -seconds;
  } else
    sign = '+';
  sprintf (buf,
    "%c%01d:%02d", sign, seconds/HOURSECONDS, (seconds%HOURSECONDS)/60);
  return buf;
}

/* This idiotic function is needed by the new tm2gmt. */
#define compare_int(a,b) (((int)(a))-((int)(b)))
static int
compare_tm (struct tm *a, struct tm *b) {
  int temp;
  /* printf ("A is %d:%d:%d:%d:%d:%d   B is %d:%d:%d:%d:%d:%d\n",
    a->tm_year+1900, a->tm_mon+1, a->tm_mday, a->tm_hour,
    a->tm_min, a->tm_sec,
    b->tm_year+1900, b->tm_mon+1, b->tm_mday, b->tm_hour,
    b->tm_min, b->tm_sec); */
  if ((temp = compare_int (a->tm_year, b->tm_year))) return temp;
  if ((temp = compare_int (a->tm_mon,  b->tm_mon)))  return temp;
  if ((temp = compare_int (a->tm_mday, b->tm_mday))) return temp;
  if ((temp = compare_int (a->tm_hour, b->tm_hour))) return temp;
  if ((temp = compare_int (a->tm_min,  b->tm_min)))  return temp;
  return      compare_int (a->tm_sec,  b->tm_sec);
}

/* Convert a struct tm in GMT back to a time_t.  isdst is ignored, since
   it never should have been needed by mktime in the first place.

   Note that switching the global time zone to GMT, using mktime, and
   switching back either screws up or core dumps on certain popular
   platforms.

   I continue to assert that the Posix time API should be taken out and
   shot.  The status quo sucks, and Posix just standardized it without
   fixing it.  As a result, we have to use idiotic kludges and workarounds
   like this one.
*/
time_t
tm2gmt (struct tm *ht)
{
  time_t guess, newguess, thebit;
  int loopcounter, compare;

  /*
      "A thing not worth doing at all is not worth doing well."

        -- Bruce W. Arden and Kenneth N. Astill, Numerical Algorithms:
           Origins and Applications, Addison-Wesley, 1970, Ch. 1.
  */

  guess = 0;
  loopcounter = (sizeof(time_t) * 8)-1;
  thebit = ((time_t)1) << (loopcounter-1);

  /* For simplicity, I'm going to insist that the time_t we want is
     positive.  If time_t is signed, skip the sign bit.
   */
//  if (thebit < (time_t)0) {
//    /* You can't just shift thebit right because it propagates the sign bit. */
//    loopcounter--;
//    thebit = ((time_t)1) << (loopcounter-1);
//    win_assert (thebit > (time_t)0);
//  }

  for (; loopcounter; loopcounter--) {
    newguess = guess | thebit;
    compare = compare_tm (gmtime(&newguess), ht);
    /* printf ("thebit=%lu  guess=%lu  newguess=%lu  compare=%d\n",
      thebit, guess, newguess, compare); */
    if (compare <= 0)
      guess = newguess;
    win_assert (thebit > (time_t)0);
    thebit >>= 1;
  }

  win_assert (!thebit);
  if (compare_tm (gmtime(&guess), ht)) {
    fprintf (stderr, "XTide:  tm2gmt failed to converge on the following input\n");
    fprintf (stderr, "%d:%d:%d:%d:%d:%d\n",
      ht->tm_year+1900, ht->tm_mon+1, ht->tm_mday, ht->tm_hour,
      ht->tm_min, ht->tm_sec);
    barf (BADTIMESTAMP);
  }

  return guess;
}

/*-----------------9/24/2002 4:30PM-----------------
 * An attempt to get Windoz to work with non-US timezones...
 * --------------------------------------------------*/
typedef struct {
  TIME_ZONE_INFORMATION tzi;
  time_t year_beg;
  time_t year_end;
  time_t enter_std;
  time_t enter_dst;
  int    isdst;
} tz_info_entry;

tz_info_entry tz_info_local, tz_info_remote, *tz_info = &tz_info_local;

/*-----------------9/25/2002 7:18AM-----------------
 * Return tz name string from last time computation.
 * --------------------------------------------------*/
char *tz_get_name() {
static char *p, s[80];
 if (tz_info->isdst)
      p = (char *)tz_info->tzi.DaylightName;
 else p = (char *)tz_info->tzi.StandardName;
 if (!strcmp(p, "GMT") && tz_info->tzi.Bias != 0) {
   sprintf(s, "UTC%s", seconds2hhmm(-tz_info->tzi.Bias*60));
   p = strstr(s, ":00");
   if ( p ) *p = '\0';
   p = s;
 }
 return ( p );
}

/*-----------------9/24/2002 8:12AM-----------------
 * Parse time string in the form [-][hh][:mm][:ss] into seconds.
 * Returns updated string pointer and signed seconds.
 * --------------------------------------------------*/
char *tz_time2sec( char *psrc, long *timesec ) {
int neg;
long temp, mpy;
  *timesec = 0;
  mpy      = 3600;
  while (*psrc == ' ') psrc++; /* Skip leading blanks */
  if (*psrc == '+') psrc++;    /* Gobble leading + */
  if (*psrc == '-') {
    neg = TRUE;
    psrc++;
  }
  else neg = FALSE;

  do {
    temp = 0;
    while (isdigit(*psrc))
      temp = temp * 10 + (*(psrc++) - '0');

    *timesec = *timesec + temp * mpy;

    if (*psrc == ':') {
      mpy /= 60;
      psrc++;
    }
  } while ( isdigit(*psrc) );

  if (neg) *timesec = 0 - *timesec;

  return( psrc );
}


/*-----------------9/24/2002 8:16AM-----------------
 * Parse timezone name string.
 * Returns string at psrc, updated psrc, and chars copied.
 * --------------------------------------------------*/
char *tz_parse_name( char *psrc, char *pdst, int maxlen ) {
int nReturn;

  nReturn = 0;
  while (*psrc == ' ') psrc++; /* Skip leading blanks */

  while (isalpha(*psrc) && nReturn < maxlen) {
    *(pdst++) = *(psrc++);
    nReturn++;
  }

  *pdst = 0;
  return( psrc );
}


/*-----------------9/24/2002 8:38AM-----------------
 * Parse tz rule string into SYSTEMTIME structure.
 * --------------------------------------------------*/
char *tz_parse_rule( char *psrc, SYSTEMTIME *st ) {
int mol[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
long temp, mo;
  if    (*psrc == ',') psrc++; /* Gobble leading comma */

  while (*psrc == ' ') psrc++; /* Skip leading blanks */

  st->wYear       = 0;
  st->wMonth      = 0;
  st->wDay        = 0;
  st->wDayOfWeek  = 0;
  st->wHour       = 0;
  st->wMinute     = 0;
  st->wSecond     = 0;
  st->wMilliseconds= 0;

  if (*psrc == 'J') {          /* Julian day (1 <= n <= 365) no leap */
    psrc++; /* Gobble 'J' */
    temp = 0;
    while (isdigit(*psrc))
      temp = temp * 10 + (*(psrc++) - '0');

    if (temp < 1 || temp > 365) return(0);
    temp--;
    for (mo=0; temp >= mol[mo]; mo++) temp -= mol[mo];
    st->wMonth = mo + 1;
    st->wDay   = temp + 1;
    st->wYear  = 1;
  }

  else if (*psrc == 'M') {
    psrc++; /* Gobble 'M' */

    temp = 0;
    while (isdigit(*psrc))
      temp = temp * 10 + (*(psrc++) - '0'); /* Get month */
    if (temp < 1 || temp > 12 || *psrc != '.') return(0);
    st->wMonth = (unsigned short)temp;

    psrc++; /* Gobble '.' */
    temp = 0;
    while (isdigit(*psrc))
      temp = temp * 10 + (*(psrc++) - '0'); /* Get week number */
    if (temp < 1 || temp > 5 || *psrc != '.') return(0);
    st->wDay = (unsigned short)temp;

    psrc++; /* Gobble '.' */
    temp = 0;
    while (isdigit(*psrc))
      temp = temp * 10 + (*(psrc++) - '0'); /* Get day of week number */
    if (temp < 0 || temp > 6) return(0);
    st->wDayOfWeek = (unsigned short)temp;
  }

  if (*psrc == '/') {          /* time is specified */
    psrc++; /* Gobble '/' */
    psrc = tz_time2sec( psrc, &temp );
    if (temp < 0 || temp >= 86400) return(0);
    st->wHour = temp / 3600;
    temp = temp % 3600;
    st->wMinute = temp / 60;
    st->wSecond = temp % 60;
  }
  return( psrc );
}


/*-----------------9/24/2002 3:38PM-----------------
 * Load tz rule into timezone info data block.
 * --------------------------------------------------*/
void tz_load_rule( char *prule, tz_info_entry *tz_info_remote ) {

  prule = tz_parse_name( prule, (char *)tz_info_remote->tzi.StandardName, 30 );
  prule = tz_time2sec( prule, &tz_info_remote->tzi.Bias );
  tz_info_remote->tzi.Bias /= 60;
  tz_info_remote->tzi.StandardBias = 0;

  prule = tz_parse_name( prule, (char *)tz_info_remote->tzi.DaylightName, 30 );
  if ( *(char *)tz_info_remote->tzi.DaylightName != '\0' ) {
    prule = tz_time2sec( prule, &tz_info_remote->tzi.DaylightBias );
    tz_info_remote->tzi.DaylightBias /= 60;
    if ( tz_info_remote->tzi.DaylightBias == 0 )
         tz_info_remote->tzi.DaylightBias = -60;
    else tz_info_remote->tzi.DaylightBias -= tz_info_remote->tzi.Bias;

    if (*prule == ',') {
      prule = tz_parse_rule( prule, &tz_info_remote->tzi.DaylightDate );
      if (prule && *prule == ',')
        tz_parse_rule( prule, &tz_info_remote->tzi.StandardDate );
      else
        tz_parse_rule( "M10.5.0/02:00:00", &tz_info_remote->tzi.StandardDate );
    }
    else {   /* Default is US style tz change */
      tz_parse_rule( "M4.1.0/02:00:00" , &tz_info_remote->tzi.DaylightDate );
      tz_parse_rule( "M10.5.0/02:00:00", &tz_info_remote->tzi.StandardDate );
    }
  }
  else { /* No DST */
    tz_info_remote->tzi.DaylightDate.wMonth = 0;
    tz_info_remote->isdst = 0;
  }
}


/*-----------------9/25/2002 4:58AM-----------------
 * Find change time for TZ for a given year.
 * --------------------------------------------------*/
time_t tz_change_time(int year, SYSTEMTIME *stdate ) {
struct tm ttm;
int week, mol, mols[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

  if (stdate->wMonth     >= 1 && stdate->wMonth     <= 12 &&
      stdate->wDay       >= 1 && stdate->wDay       <=  5 &&
      stdate->wDayOfWeek >= 0 && stdate->wDayOfWeek <=  6) {

    ttm.tm_sec  = ttm.tm_min = ttm.tm_isdst=  0;
    ttm.tm_hour = 12;
    ttm.tm_mday = 1;
    ttm.tm_mon  = stdate->wMonth-1;
    ttm.tm_year = year - 1900;
    mktime( &ttm ); // Have mktime compute day of week of 1st day of month (@noon)

    ttm.tm_mday = stdate->wDayOfWeek - ttm.tm_wday + 1;
    if (ttm.tm_mday <= 0) ttm.tm_mday += 7;

    mol  = mols[stdate->wMonth-1] - 7;
    week = stdate->wDay;

    while ( --week > 0 && ttm.tm_mday <= mol ) ttm.tm_mday += 7;

    ttm.tm_sec  = stdate->wSecond;
    ttm.tm_min  = stdate->wMinute;
    ttm.tm_hour = stdate->wHour;
    ttm.tm_isdst=  0;
    return( tm2gmt( &ttm ));
  }
  else return(0);
}

/*-----------------9/25/2002 4:57AM-----------------
 * For a given gmt time t and TZ rule, compute local time.
 * --------------------------------------------------*/
time_t tz_gm2localtime(time_t t) {
struct tm ttm;

  if (!tz_info->tzi.DaylightDate.wMonth) // No DST, just offset by Bias
     return (t - (time_t)tz_info->tzi.Bias*60);

  if (t < tz_info->year_beg || t > tz_info->year_end) {
// Time is outside of the current year data. Must get values for the new year.
//      ttm = *(gmtime (&t)); /* Get year value for time under consideration */
    ttm.tm_year = gmtime(&t)->tm_year; /* Get year for time under consideration */

    ttm.tm_sec  = ttm.tm_min = ttm.tm_hour = ttm.tm_mon = 0;
    ttm.tm_mday = 1;
    tz_info->year_beg = tm2gmt( &ttm ); // Beginning of year is Jan 1 00:00:00

    ttm.tm_sec  = 59;
    ttm.tm_min  = 59;
    ttm.tm_hour = 23;
    ttm.tm_mon  = 11;
    ttm.tm_mday = 31;
    tz_info->year_end = tm2gmt( &ttm ); // End of year is Dec 31 23:59:59

    if (tz_info->tzi.StandardDate.wYear) { // Absolute date format (J rule format)
      ttm.tm_sec  = tz_info->tzi.StandardDate.wSecond;
      ttm.tm_min  = tz_info->tzi.StandardDate.wMinute;
      ttm.tm_hour = tz_info->tzi.StandardDate.wHour;
      ttm.tm_mday = tz_info->tzi.StandardDate.wDay;
      ttm.tm_mon  = tz_info->tzi.StandardDate.wMonth-1;
      ttm.tm_isdst= 0;
      tz_info->enter_std = tm2gmt( &ttm );

      ttm.tm_sec  = tz_info->tzi.DaylightDate.wSecond;
      ttm.tm_min  = tz_info->tzi.DaylightDate.wMinute;
      ttm.tm_hour = tz_info->tzi.DaylightDate.wHour;
      ttm.tm_mday = tz_info->tzi.DaylightDate.wDay;
      ttm.tm_mon  = tz_info->tzi.DaylightDate.wMonth-1;
      ttm.tm_isdst= 0;
      tz_info->enter_dst = tm2gmt( &ttm );

    } else { // Day of week format (M rule format)
// Find enter_std and enter_dst from rule
      tz_info->enter_std = tz_change_time(ttm.tm_year+1900, &tz_info->tzi.StandardDate)
                              + (time_t)(tz_info->tzi.Bias + tz_info->tzi.DaylightBias)*60;
      tz_info->enter_dst = tz_change_time(ttm.tm_year+1900, &tz_info->tzi.DaylightDate)
                              + (time_t)(tz_info->tzi.Bias + tz_info->tzi.StandardBias)*60;
    }
  }

  if (tz_info->enter_std > tz_info->enter_dst) // Northern hemisphere year starts with std
       tz_info->isdst = (t >= tz_info->enter_dst && t <  tz_info->enter_std);
  else tz_info->isdst = (t <  tz_info->enter_std || t >= tz_info->enter_dst);

  if (tz_info->isdst)
       return( t - (time_t)(tz_info->tzi.Bias + tz_info->tzi.DaylightBias)*60 );
  else return( t - (time_t)(tz_info->tzi.Bias + tz_info->tzi.StandardBias)*60 );
}

/*-----------------9/24/2002 5:26PM-----------------
 * Localtime routine
 * --------------------------------------------------*/
struct tm *tzlocaltime( time_t *t ) {
time_t t_local;
  t_local = tz_gm2localtime(*t);
  return( gmtime( &t_local ) );
}

/* Attempt to load up the local time zone of the location.  Moof! */
void
change_time_zone (char *tz)
{
//  static char env_string[MAXARGLEN+1];
  int index;
int kkk;

  if (*tz == ':') tz++; /* Gobble lead-in char */
  /* Find the translation for the timezone string */
  index = 0;
  while (1) {

    if (tz_names[index][0] == NULL) {
      tz_info = &tz_info_local;
      /* Not found. */
      break;
    }
    if (!stricmp (tz_names[index][0], (tz))) {
      tz_load_rule( tz_names[index][1], &tz_info_remote );
      tz_info = &tz_info_remote;
      /* Force compute next time this data is used */
      tz_info->year_beg = 0;      // Begin date/time is Jan 1, 1970
      tz_info->year_end = 0;      // End date/time is Jan 1, 1970
  //      sprintf (env_string, "TZ=%s", tz_names[index][1]);
      break;
    }
    index++;
  }
}

/* Load up local timezone info */
// SystemTimeToTzSpecificLocalTime( <<<<<<<<<<<<< NT/2k only!!!!!
//void set_local_tz () {
////  TIME_ZONE_INFORMATION   TimeZoneInformation;
//  int a,z;
//  WCHAR *pwtz;
//  char  *ptz;
//
//  GetTimeZoneInformation(&tz_info_local.tzi);
//
///* Copy up to 5 CAP characters for Standard TZ name back into same array */
//  pwtz =        tz_info_local.tzi.StandardName;
//  ptz = (char *)tz_info_local.tzi.StandardName;
//  for (a=z=0; pwtz[a]>=' ' && pwtz[a]<='z' && z<5; a++)
//      if (iswupper(pwtz[a])) ptz[z++]=(char)pwtz[a];
//  ptz[z]='\0';
//
///* Copy up to 5 CAP characters for Daylight TZ name back into same array */
//  pwtz =        tz_info_local.tzi.DaylightName;
//  ptz = (char *)tz_info_local.tzi.DaylightName;
//  for (a=z=0; pwtz[a]>=' ' && pwtz[a]<='z' && z<5; a++)
//      if (iswupper(pwtz[a])) ptz[z++]=(char)pwtz[a];
//  ptz[z]='\0';
//
///* Force compute next time this data is used */
//  tz_info->year_beg = 0;      // Begin date/time is Jan 1, 1970
//  tz_info->year_end = 0;      // End date/time is Jan 1, 1970
//}

/* Calculate time_t of the epoch. */
void
set_epoch (int year, int num_epochs, int first_year)
{
  struct tm ht;
  if (year < first_year || year >= first_year + num_epochs) {
    fprintf (stderr, "Tidelib:  Don't have equilibrium arguments for %d\n",
      year);
    barf (MISSINGYEAR);
  }
  ht.tm_year = year - 1900;
  ht.tm_sec = ht.tm_min = ht.tm_hour = ht.tm_mon = 0;
  ht.tm_mday = 1;
  epoch = tm2gmt (&ht);

//*pTideDbg++ = (DWORD)0x22222222;
//*pTideDbg++ = (DWORD)year;
//*pTideDbg++ = (DWORD)ht.tm_year;
//*pTideDbg++ = (DWORD)ht.tm_sec;
//*pTideDbg++ = (DWORD)epoch;
//*pTideDbg++ = (DWORD)0x22222222;
//*pTideDbg++ = (DWORD)0x22222222;
//*pTideDbg++ = (DWORD)0x22222222;
}

/* Convert YYYY:MM:DD:HH:MM to time_t. */
time_t
parse_time_string (char *time_string)
{
  struct tm ht;
  time_t temp;
  if (sscanf (time_string, "%d:%d:%d:%d:%d",
    &(ht.tm_year), &(ht.tm_mon), &(ht.tm_mday), &(ht.tm_hour),
    &(ht.tm_min)) != 5)
    barf (BADTIMESTAMP);
  ht.tm_sec = 0;
  (ht.tm_mon)--;
  ht.tm_year -= 1900;
  if (uutc) {
    if ((temp = tm2gmt (&ht)) == -1)
      barf (BADTIMESTAMP);
  } else {
    ht.tm_isdst = -1;
    if ((temp = mktime (&ht)) == -1)
      barf (BADTIMESTAMP);
  }
  return temp;
}

/* Return the year for a time_t.  This function gets called a lot and might
   be a performance bottleneck if gmtime is slow. */
int
yearoftimet (time_t t)
{
  return ((gmtime (&t))->tm_year) + 1900;
}

/* Used by do_timestamp. */
static int
is_ascii (char a)
{
  if (a >= ' ' && a <= '~')
    return 1;
  return 0;
}

/* Generate a datestamp in the appropriate style. */
void
do_datestamp (char buf[20], struct tm *t)
{
  char fmt[12];
  fmt[0] = '\0';
  if (weekday)    strcat (fmt, "%a ");
  if (skinny < 1 && !datemdy) strcat (fmt, "%Y-");
  strcat (fmt, "%m-%d");
  if (skinny < 1 && datemdy) strcat (fmt, "-%Y");
  strftime (buf, 20, fmt, t);
}

/* Generate a timestamp in the appropriate style. */
void
do_timestamp (char buf[20], struct tm *t)
{
  int a;
  if (skinny == 2)
    strftime (buf, 20, "%H%M", t);
  else if (tadjust) {
    if (noampm)
         strftime (buf, 20, "%H:%M", t);
    else strftime (buf, 20, "%I:%M %p", t);
  }
  else if (utc) {
    if (skinny < 1)
         strftime (buf, 20, "%H:%M UTC", t);
    else strftime (buf, 20, "%H:%M", t);
  }
  else {
    if (skinny < 1) {
      if (noampm)
           strftime (buf, 20, "%H:%M ", t);
      else strftime (buf, 20, "%I:%M %p ", t);
      strcat( buf, tz_get_name());
    }
    else {
      if (noampm)
           strftime (buf, 20, "%H:%M", t);
      else strftime (buf, 20, "%I:%M %p", t);
    }
  }
  if (skinny < 2 && buf[0] == '0') {
    if (text && !ppm && !gif && !banner)
      buf[0] = ' ';
    else
      memmove (buf, buf+1, 19);
  }

  /* Get rid of any blanks or binary garbage (national characters?) */
  buf[19] = '\0';
  for (a=18;a>=0;a--)
    if (!is_ascii (buf[a]))
      buf[a] = '\0';
  nojunk (buf);
}

/* Do both. */
char *
do_long_timestamp (struct tm *t)
{
  static char longbuf[40];
  char datebuf[20], timebuf[20];
  do_datestamp (datebuf, t);
  do_timestamp (timebuf, t);
  if (timebuf[0] == ' ')
       sprintf (longbuf, "%s %s", datebuf, timebuf+1);
  else sprintf (longbuf, "%s %s", datebuf, timebuf  );
  return longbuf;
}

/* Convert a genuine time_t to a specially mangled struct tm. */
struct tm *
tmtime (time_t t)
{
  if (tadjust) {
    t += tadjust;
    return gmtime (&t);
  }
  else if (utc)
       return gmtime (&t);
  else return tzlocaltime (&t);
}

/* Find the previous hour-transition from a specified time.  If a DST
   change skips over the transition, then we skip it too. */
time_t
prev_hour (time_t t)
{
  struct tm ttm;
  time_t temp, temp2;

  /* Take care of UTC-based time zones. */
  if (utc || tadjust) {
    ttm = *(gmtime (&t));
    ttm.tm_sec = ttm.tm_min = 0;
    temp = tm2gmt (&ttm) - (tadjust % HOURSECONDS);
    if (temp <= t - HOURSECONDS)
      temp += HOURSECONDS;
    if (temp > t)
      temp -= HOURSECONDS;
    win_assert (temp > t - HOURSECONDS && temp <= t);
    return temp;
  }

  /* Deal with local time zones. */
  /* Can't use mktime, because it sucks!  It SUCKS!!! */

  /* The easy case will work unless we are hosed by DST. */
  ttm = *(tzlocaltime (&t));
  temp = t - (ttm.tm_min * 60 + ttm.tm_sec);
  win_assert (temp > t - HOURSECONDS && temp <= t);
  ttm = *(tzlocaltime (&temp));
  if (ttm.tm_sec == 0 && ttm.tm_min == 0)
    return temp;

  /* See if we went back too far. */
  temp2 = temp + HOURSECONDS - (ttm.tm_min * 60 + ttm.tm_sec);
  if (temp2 > t - HOURSECONDS && temp2 <= t) {
    ttm = *(tzlocaltime (&temp2));
    if (ttm.tm_sec == 0 && ttm.tm_min == 0) {
      /* fprintf (stderr, "prev_hour:  Too far\n"); */
      return temp2;
    }
  }

  /* Go back farther. */
  /* fprintf (stderr, "prev_hour:  Not far enough; recursing\n"); */
  return prev_hour (temp);
}

/* Given that we are on an hour transition, find the next one.  If a DST
   change skips over the transition, then we skip it too. */
time_t
increment_hour (time_t t) {
  struct tm ttm;
  time_t temp, temp2;

  /* Take care of UTC-based time zones.  These have no DST. */
  if (utc || tadjust)
    return t + HOURSECONDS;

  /* Deal with local time zones. */
  /* Did I mention how bad mktime SUCKS? */

  /* The easy case will work unless we are hosed by DST. */
  temp = t + HOURSECONDS;
  ttm = *(tzlocaltime (&temp));
  if (ttm.tm_sec == 0 && ttm.tm_min == 0)
    return temp;

  /* See if we went forward too far. */
  temp2 = temp - (ttm.tm_min * 60 + ttm.tm_sec);
  if (temp2 > t && temp2 <= t + HOURSECONDS) {
    ttm = *(tzlocaltime (&temp2));
    if (ttm.tm_sec == 0 && ttm.tm_min == 0) {
      /* fprintf (stderr, "increment_hour:  Too far\n"); */
      return temp2;
    }
  }

  /* Go forward some more. */
  /* fprintf (stderr, "increment_hour:  Not far enough; recursing\n"); */
  return increment_hour (temp);
  /* That will work, even though we are not starting on an hour transition. */
}

/* Find the previous day-transition from a specified time.  Returns a
   reasonable guess even if there is no midnight due to DST adjustment. */
time_t
prev_day (time_t t)
{
  struct tm ttm;
  time_t temp, temp2;
  int today;

  /* Take care of UTC-based time zones. */
  if (utc || tadjust) {
    ttm = *(gmtime (&t));
    ttm.tm_sec = ttm.tm_min = ttm.tm_hour = 0;
    temp = tm2gmt (&ttm) - tadjust;
    if (temp <= t - DAYSECONDS)
      temp += DAYSECONDS;
    if (temp > t)
      temp -= DAYSECONDS;
    win_assert (temp > t - DAYSECONDS && temp <= t);
    return temp;
  }

  /* Deal with local time zones. */
  /* mktime sucks in every country and every time zone! */

  /* The easy case will work unless we are hosed by DST. */
  ttm = *(tzlocaltime (&t));
  today = ttm.tm_mday;
  temp = t - (ttm.tm_hour * HOURSECONDS + ttm.tm_min * 60 + ttm.tm_sec);
  win_assert (temp > t - DAYSECONDS && temp <= t);
  ttm = *(tzlocaltime (&temp));
  if (ttm.tm_hour == 0 && ttm.tm_sec == 0 && ttm.tm_min == 0)
    return temp;

  /* See if we went back too far. */
  if (ttm.tm_mday != today) {
    temp2 = temp + DAYSECONDS - (ttm.tm_hour * HOURSECONDS +
    ttm.tm_min * 60 + ttm.tm_sec);
    /* fprintf (stderr, "prev_day:  Too far\n"); */
    /* This is always our best guess (approach from left) due to DST
       changes being applied exactly at midnight. */
    return temp2;
  }

  /* Go back farther. */
  /* fprintf (stderr, "prev_day:  Not far enough; recursing\n"); */
  return prev_day (temp);
}

/* Given that we are on a day transition, find the next one.  Returns a
   reasonable guess even if there is no midnight due to DST adjustment. */
time_t
increment_day (time_t t)
{
  struct tm ttm;
  time_t temp;
  int today;

  /* Take care of UTC-based time zones. */
  if (utc || tadjust)
    return t + DAYSECONDS;

  /* Deal with local time zones. */
  /* mktime sucks in ways that will get you arrested in the U.S. */

  /* The easy case will work unless we are hosed by DST. */
  ttm   = *(tzlocaltime (&t));
  today = ttm.tm_mday;
  temp  = t + DAYSECONDS;
  ttm   = *(tzlocaltime (&temp));
  if (ttm.tm_hour == 0 && ttm.tm_sec == 0 && ttm.tm_min == 0)
    return temp;

  /* See if we went forward too far. */
  if (ttm.tm_mday != today) {
    /* fprintf (stderr, "increment_day:  Too far; calling prev_day\n"); */
    return prev_day (temp);
  }

  /* Go forward farther. */
  /* fprintf (stderr, "increment_day:  Not far enough; recursing\n"); */
  return increment_day (temp);
  /* That will work, even though we are not starting on a day transition. */
}

/* Find the Sunday on which to start a calendar. */
time_t
sunday_month (time_t t)
{
  struct tm ttm;
  int almost_done = 0;
  ttm = *(tmtime(t));
  if (ttm.tm_mday == 1)
    almost_done = 1;
  while (ttm.tm_wday || !almost_done) {
    t = prev_day (t-1);
    ttm = *(tmtime(t));
    if (ttm.tm_mday == 1)
      almost_done = 1;
  }
  return t;
}

int GetTimeBias(char *tz)
{
	int index;
	tz_info_entry tz_Ret;

	if(*tz == ':')
		tz++; /* Gobble lead-in char */

	/* Find the translation for the timezone string */
	index = 0;
	while(1) 
	{
		if(tz_names[index][0] == NULL) 
			return 0;

		if(!stricmp(tz_names[index][0], (tz))) 
		{
			//tz_load_rule( tz_names[index][1], &tz_Ret);
			char prule[100];
			strcpy(prule, tz_names[index][1]);
			strcpy(prule, tz_parse_name(prule, (char*)tz_Ret.tzi.StandardName, 30));
			tz_time2sec(prule, &tz_Ret.tzi.Bias);
			tz_Ret.tzi.Bias /= 60;
			return tz_Ret.tzi.Bias;
		}
		index++;
	}
	return 0;
}
