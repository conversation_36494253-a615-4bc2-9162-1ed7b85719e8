#include <stdio.h>
#include <stdlib.h>
#include "keybd.hpp"
#include "const.h"
#include "String.hpp"
#include "Font.h"
#include "Uart.hpp"
#include "WndMgr.hpp"
#include "DocMgr.hpp"
#include "WzdSetProductInfoWnd.hpp"

#define WZD_SET_PRDCT_INFO_WND_X_POS		10
#define WZD_SET_PRDCT_INFO_WND_Y_POS		54

#define WZD_SET_PRDCT_INFO_WND_W			580
#define WZD_SET_PRDCT_INFO_WND_H			370

#define WZD_SET_PRDCT_INFO_WND_CAP_X_POS	(WZD_SET_PRDCT_INFO_WND_X_POS + 10)
#define WZD_SET_PRDCT_INFO_WND_CTRL_X_POS	(WZD_SET_PRDCT_INFO_WND_X_POS + 100)

#define WZD_SET_PRDCT_INFO_WND_ROW_H		37
#define WZD_SET_PRDCT_INFO_WND_CTRL_W		180
#define WZD_SET_PRDCT_INFO_WND_CTRL_H		30

extern CWndMgr *g_pWndMgr;
extern CDocMgr *g_pDocMgr;
extern cUART *G_pUart3;

/*********************************************************************************************************/
// Name		: CWzdAntPosWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CWzdSetProductInfoWnd::CWzdSetProductInfoWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	InitWzdSetProductInfoWnd(pScreen);
	SetFocus(FOCUS_SN);
}

/*********************************************************************************************************/
// Name		: InitVariable
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::InitVariable()
{
	memset(m_strSN,0x00, sizeof(BYTE)*MAX_SERIAL_NUM_STR);
}

/*********************************************************************************************************/
// Name		: InitControls
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::InitControls(cSCREEN *pScreen)
{
	m_pEditSN= new CEditCtrl(pScreen);
	m_pEditSN->Create(	WZD_SET_PRDCT_INFO_WND_CTRL_X_POS, 
						WZD_SET_PRDCT_INFO_WND_Y_POS + WZD_SET_PRDCT_INFO_WND_ROW_H*4 + (WZD_SET_PRDCT_INFO_WND_ROW_H - WZD_SET_PRDCT_INFO_WND_CTRL_H)/2, 
						WZD_SET_PRDCT_INFO_WND_CTRL_W,	
						WZD_SET_PRDCT_INFO_WND_CTRL_H, MAX_SERIAL_NUM_STR-1, FALSE);

	m_pEditSN->SetFormat((BYTE *)"________");
	m_pCtrls[FOCUS_SN]		= m_pEditSN;
	m_pEditSN->SetText((BYTE *)"0");
}

/*********************************************************************************************************/
// Name		: InitWzdSetProductInfoWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::InitWzdSetProductInfoWnd(cSCREEN *pScreen)
{
	InitVariable();
	InitControls(pScreen);
}

/*********************************************************************************************************/
// Name		: UpdateSerialNum
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::UpdateSerialNum()
{
	g_pDocMgr->GetSerialNum(m_strSN);
	m_pEditSN->SetText(m_strSN);
	m_pEditSN->SetEditMode(FALSE);
}

/*********************************************************************************************************/
// Name		: SaveSerialNumber
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::SaveSerialNumber()
{
	m_pEditSN->GetText(m_strSN);
	g_pDocMgr->SetSerialNum(m_strSN);

	// Save PCB Version String to Flash
	g_pDocMgr->SavePCBVerBackupData();
}

/*********************************************************************************************************/
// Name		: IsEditMode
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
BOOL CWzdSetProductInfoWnd::IsEditMode()
{
	for( int i = 0; i < FOCUS_COUNT; ++i )
	{
		if( m_pCtrls[i]->GetObjectType() == OBJECT_EDITBOX &&
			((CEditCtrl *)m_pCtrls[i])->GetEditMode() )
		{
			return TRUE;
		}
	}

	return FALSE;
}

/*********************************************************************************************************/
// Name		: DrawFocusOutLine
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::DrawFocusOutLine()
{
#if 0
	int nLeft, nRight, nTop, nBottom;

	nLeft = PCB_VER_1ST_X - 5;
	nRight = 480;

	nTop = PCB_VER_START_Y + PCB_VER_ROW_H*m_nFocus;
	nBottom = PCB_VER_START_Y + PCB_VER_ROW_H*(m_nFocus+1)-2;
	
	m_pScreen->Rect(nLeft, nTop, nRight, nBottom, COLORSCHEME[m_nScheme].crFore);
#endif	
}

/*********************************************************************************************************/
// Name		: DrawControls
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::DrawControls()
{
	for( int i = 0; i < FOCUS_COUNT; ++i )
	{
		m_pCtrls[i]->DrawWnd();
	}
}

/*********************************************************************************************************/
// Name		: DrawCaption
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::DrawCaption()
{
	FONT *pOldFont = NULL;
	FONT *pFont = NULL;
	int nXPos = 0,nYPos = 0, nYOffset = 0;
	int nFontH = 0;
	HWORD *pUniCodeStr = NULL;
	BYTE szTemp[24];
	int nLangMode = g_pDocMgr->GetLangMode();

	switch(nLangMode)
	{
		case LANG_CHI:
		case LANG_KOR:
			pFont = &MyriadPro24bEng;
			nYOffset = 0;	
			break;

		case LANG_RUS:
			pFont = &MyriadPro24bEng;
			nYOffset = 0;	
			break;	

		default:
			pFont = &MyriadPro24bEng;
			nYOffset = 0;
			break;
	}

	pOldFont = m_pScreen->SetFont(pFont);
	nFontH = pFont->uHeight;

	// SERIAL Number
	nXPos = WZD_SET_PRDCT_INFO_WND_CAP_X_POS;
	nYPos = WZD_SET_PRDCT_INFO_WND_Y_POS + WZD_SET_PRDCT_INFO_WND_ROW_H*4 + (WZD_SET_PRDCT_INFO_WND_ROW_H - nFontH)/2;
	sprintf((char *)szTemp, DISP_SERIAL_NUM_STR);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	m_pScreen->SetFont(pOldFont);
}

/*********************************************************************************************************/
// Name		: DrawFuncBtn
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::DrawFuncBtn()
{
	int nLangMode = g_pDocMgr->GetLangMode();
	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
	DrawButton(1, (BYTE *)FK_NEXT[nLangMode]);
	EraseButton(2);
	DrawButton(3, (BYTE *)FK_FINISH[nLangMode]);
}


/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::DrawWnd(BOOL bRedraw)
{
	m_pScreen->FillRect(WND_BACK_X_POS,WND_BACK_Y_POS,WND_BACK_X_POS + WND_BACK_W-1,WND_BACK_Y_POS +WND_BACK_H -1 ,COLORSCHEME[m_nScheme].crBack);
	
	DrawFocusOutLine();
	DrawCaption();
	DrawControls();
	DrawFuncBtn();
}

/*********************************************************************************************************/
// Name		: OnActivate
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::OnActivate()
{
	SetFocus(FOCUS_SN);
	UpdateSerialNum();
}

/*********************************************************************************************************/
// Name		: SetFocus
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::SetFocus(int nFocus)
{
	CWnd::SetFocus(nFocus);

	for( int i = 0; i < FOCUS_COUNT; ++i )
	{
		if( m_pCtrls[i]->GetObjectType() == OBJECT_EDITBOX )
		{
			((CEditCtrl *)m_pCtrls[i])->SetFocus((nFocus == i) ? TRUE : FALSE);
		}			
	}
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
			if( m_nFocus > 0 )
			{
				--m_nFocus;
				SetFocus(m_nFocus);
				DrawWnd(FALSE);
			}
			break;
			
		case KBD_SCAN_CODE_DOWN:
			if( m_nFocus < FOCUS_COUNT )
			{
				++m_nFocus;
				SetFocus(m_nFocus);
				DrawWnd(FALSE);
			}
			break;
			

		case KBD_SCAN_CODE_FUNC2:		// NEXT
			if(!m_pEditSN->GetEditMode())
			{
				SaveSerialNumber();
				g_pWndMgr->SetActiveWnd(WID_WZD_RESULT_WND);
			}
			break;
			
		default:
			switch( m_nFocus )
			{
				case FOCUS_SN:
					m_pEditSN->OnKeyEvent(nKey, nFlags);
					break;
					
				
				default:
					m_pCtrls[m_nFocus]->OnKeyEvent(nKey, nFlags);
			}

			break;
	}
}

/*********************************************************************************************************/
// Name		: OnCursorEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CWzdSetProductInfoWnd::OnCursorEvent(int nState)
{
	CWnd *pFocusCtrl = m_pCtrls[m_nFocus];

	if( pFocusCtrl->GetObjectType() == OBJECT_EDITBOX )
	{
		pFocusCtrl->OnCursorEvent(nState);
	}		
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CWzdSetProductInfoWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);

	if( nResult == AL_YES )
	{
#if 0	
		SetFocus(FOCUS_SN);
		SaveSerialNumber();
#endif		
	}

	return nResult;
}



