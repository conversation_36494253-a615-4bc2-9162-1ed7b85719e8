#ifndef __WIZARD_ETC_WND_HPP__
#define __WIZARD_ETC_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"
//#include "ComboCtrl.hpp"
#include "UniComboCtrl.hpp"
#include "CheckCtrl.hpp"

class CWzdEtcWnd : public CWnd {
	protected:
		enum {
			FOCUS_LANG = 0,
			FOCUS_USE_HAN_SHIP_NAME =1,				
			FOCUS_LAST = FOCUS_USE_HAN_SHIP_NAME
		};

		CUniComboCtrl *m_pCmbLang;
		CCheckCtrl 		*m_pUseHanShipName;

	private:
		void CreateControls(cSCREEN *pScreen);
		void InitWzdEtcWnd();
		
	public:
		CWzdEtcWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void OnCursorEvent(int nState);
		virtual void OnActivate();
		void DrawControls();
		void DrawFuncBtn();
		void DrawWnd(BOOL bRedraw = TRUE);
		void SaveEtcData();
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		int  GetFocus()           { return m_nFocus; }
		void ComboCollapse() 
		{
			m_pCmbLang->Collapse();
		}
};

#endif	// End of __WIZARD_ETC_WND_HPP__

