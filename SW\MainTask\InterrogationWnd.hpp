#include "Wnd.hpp"
#include "ComboCtrl.hpp"

#ifndef __INTERROGATION_WND_HPP__
#define __INTERROGATION_WND_HPP__

class CInterrogationWnd : public CWnd {
	protected:
		CComboCtrl *m_pMMSI1, *m_pMMSI2;
		CComboCtrl *m_pMsg1, *m_pMsg2;
		CComboCtrl *m_pMsg3;

		enum {
			FOCUS_MMSI1 = 0,
			FOCUS_MSG1,
			FOCUS_MSG2,
			FOCUS_MMSI2,
			FOCUS_MSG3
		};

	public:
		CInterrogationWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);

		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		int  GetFocus()           { return m_nFocus; }
		void FillTargetMMSI(DWORD dSelMMSI1=0, DWORD dSelMMSI2=0);
		void ComboCollapse();
		void InitControl() {
			m_pMMSI1->SetCurSel(0);
			m_pMMSI2->SetCurSel(0);
			m_pMsg1->SetCurSel(0);
			m_pMsg2->SetCurSel(0);
			m_pMsg3->SetCurSel(0);
		}

		void SendInterrogation();
};

#endif

