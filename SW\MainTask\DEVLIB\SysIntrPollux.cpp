/*...........................................................................*/
/*.                  File Name : SYSINTR.CPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.11                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#if defined(__POLLUX__)

#include "type.hpp"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysSDCD.h"
#include "SysGPIO.h"
#include "SysTimer.h"
#include "Nand64.h"

#include "time.hpp"
#include "uart.hpp"
#include "buzzer.hpp"
#include "screen.hpp"

#include "sysconst.h"
#include "syslib.h"
#include "comlib.h"
#include "keybd.hpp"

#include "Timer.hpp"

#include "SysIntrPollux.hpp"

#include <string.h>

//================================================================
#define  _RESTART_TEST_MEM_ADDR_               0x03a00000
//================================================================

//================================================================
static void (*G_pIntHandlerTableX[])(void) = { // POLLUX             MMSP2+
            (void (*)(void))NULL,              // IRQ_PHY_PDISPLAY   IRQ_PHY_PDISPLAY   
            (void (*)(void))NULL,              // IRQ_PHY_SDISPLAY   IRQ_PHY_SDISPLAY   
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_VIP        
            (void (*)(void))NULL,              // IRQ_PHY_DMA        IRQ_PHY_DMA        
            (void (*)(void))IsrHandlerTimer0,  // IRQ_PHY_SYSTIMER0  IRQ_PHY_SYSTIMER0  
            (void (*)(void))NULL,              // IRQ_PHY_SYSCTRL    IRQ_PHY_SYSCTRL    
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_DUALCPUIN  
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_MPEGIF     
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_EXTINT0    
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_EXTINT1    
            (void (*)(void))IsrHandlerUART0,   // IRQ_PHY_UART0      IRQ_PHY_UART0      
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER1  IRQ_PHY_SYSTIMER1  
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI0    IRQ_PHY_SSPSPI0    
            (void (*)(void))IsrHandlerGPIO,    // IRQ_PHY_GPIO       IRQ_PHY_GPIO       
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC      IRQ_PHY_SDMMC      
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER2  IRQ_PHY_SYSTIMER2  
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_H264       
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_MPEGCODEC  
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_GRP2D      
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_VLD        
            (void (*)(void))NULL,              // IRQ_PHY_UDC        IRQ_PHY_UDC        
            (void (*)(void))NULL,              // IRQ_PHY_SYSTIMER3  IRQ_PHY_SYSTIMER3  
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_DEINTERLACE
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_PPM        
            (void (*)(void))NULL,              // IRQ_PHY_AUDIOIF    IRQ_PHY_AUDIOIF    
            (void (*)(void))NULL,              // IRQ_PHY_ADC        IRQ_PHY_ADC        
            (void (*)(void))NULL,              // IRQ_PHY_MCUSTATIC  IRQ_PHY_MCUSTATIC  
            (void (*)(void))NULL,              // IRQ_PHY_GRP3D      IRQ_PHY_GRP3D      
            (void (*)(void))NULL,              // IRQ_PHY_UHC        IRQ_PHY_UHC        
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_ROTATOR    
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_SCALER     
            (void (*)(void))NULL,              // IRQ_PHY_RTC        IRQ_PHY_RTC        
            (void (*)(void))NULL,              // IRQ_PHY_I2C0       IRQ_PHY_I2C0       
            (void (*)(void))NULL,              // IRQ_PHY_I2C1       IRQ_PHY_I2C1       
            (void (*)(void))IsrHandlerUART1,   // IRQ_PHY_UART1      IRQ_PHY_UART1      
            (void (*)(void))IsrHandlerUART2,   // IRQ_PHY_UART2      IRQ_PHY_UART2      
            (void (*)(void))IsrHandlerUART3,   // IRQ_PHY_UART3      IRQ_PHY_UART3      
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_UART4      
            (void (*)(void))NULL,              // -----------------  IRQ_PHY_UART5      
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI1    IRQ_PHY_SSPSPI1    
            (void (*)(void))NULL,              // IRQ_PHY_SSPSPI2    IRQ_PHY_SSPSPI2    
            (void (*)(void))NULL,              // IRQ_PHY_CSC        IRQ_PHY_CSC        
            (void (*)(void))NULL,              // IRQ_PHY_SDMMC1     -----------------
            (void (*)(void))NULL};             // IRQ_PHY_SYSTIMER4  -----------------
//================================================================
static xSYS_INTR *G_pSysIntrCtrl = (xSYS_INTR *)INTC_PHSY_BASE_ADDR;
//----------------------------------------------------------------
extern cTimer   *G_pSysTimer0;
//extern cUart    *G_pUartPort0;
//extern cUart    *G_pUartPort1;
//extern cUart    *G_pUartPort2;
//extern cUart    *G_pUartPort3;
//extern cBuzzer  *G_pBuzzer;
//extern cKeyBD   *G_pKeyBD;

extern cKEYBD   *G_pKeyBD;
extern cTIME    *G_pTime;
extern cUART    *G_pUart0;
extern cUART    *G_pUart1;
extern cUART    *G_pUart2;
extern cUART    *G_pUart3;
extern cSCREEN  *G_pScreen;
extern cBUZZER  *G_pBuzzer;
//================================================================

//=============================================================================
#ifdef  __cplusplus
extern "C" {
#endif

void  IsrHandlerIRQ(void)
{
      DWORD dIntStatusX;
      DWORD dIntStatusY;
      int   i,nIntNo;
      DWORD dIntMask;

      dIntStatusX = G_pSysIntrCtrl->dINTPEND[0];
      dIntStatusY = G_pSysIntrCtrl->dINTPEND[1];

      dIntMask = 1;

      for (i = 0;i < 32;i++)
          {
           if (dIntStatusX & dIntMask)
              {
               if (G_pIntHandlerTableX[i] != NULL)
                   G_pIntHandlerTableX[i]();
               G_pSysIntrCtrl->dINTPEND[0] = dIntMask;
              }

           if (dIntStatusY & dIntMask)
              {
               nIntNo = i + 32;
               if (G_pIntHandlerTableX[nIntNo] != NULL)
                   G_pIntHandlerTableX[nIntNo]();
               G_pSysIntrCtrl->dINTPEND[1] = dIntMask;
              }

           dIntMask <<= 1;
          }
}
void  IsrHandlerFIQ(void)
{
}
void  IsrHandlerSWI(void)
{
      ReStartMainBooter();

      while (1);
}
void  IsrHandlerABORT(void)
{
      ReStartMainBooter();

      while (1);
}
void  IsrHandlerUNDEF(void)
{
      ReStartMainBooter();

      while (1);
}

void  ReStartMainBooter(void)
{
#if defined(__POLLUX__)
      #define  MAIN_BOOTER_RAM_START_ADDR            0x00000000
      #define  MAIN_BOOTER_RAM_OFFST_ADDR            0x00000000
#else                           // SPICA
#endif

      void  (*pMainBooter)(void);

#if defined(__POLLUX__)
//    *(DWORD *)0x03b00000 = 0xeeeeeeee;
//    *(DWORD *)0x03b00004 = SysGetDataFaultStatusRegister();
//    *(DWORD *)0x03b00008 = SysGetInstFaultStatusRegister();
//    *(DWORD *)0x03b0000c = SysGetFaultAddressRegister();

      SysDisableIRQ();

      ReadNandBlockData((UCHAR *)MAIN_BOOTER_RAM_START_ADDR - MAIN_BOOTER_RAM_OFFST_ADDR, 0);

      SysCleanAllDCache926();
      SysInvalidateDCache();
      SysInvalidateICache();

      pMainBooter = (void (*)(void))(MAIN_BOOTER_RAM_START_ADDR);

      pMainBooter();


#else                           // SPICA
#endif


/*
      SysCleanAllDCache926();
      SysInvalidateDCache();
      SysInvalidateICache();

#if defined(__POLLUX__)
      *(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x00) = 0x01234567;
      *(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x04) = 0x89abcdef;
      *(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x08) = 0xa5a5aaaa;
      *(DWORD *)(_RESTART_TEST_MEM_ADDR_ + 0x0c) = 0x5a5a5555;

      ReadNandBlockData((UCHAR *)MAIN_BOOTER_RAM_START_ADDR - MAIN_BOOTER_RAM_OFFST_ADDR, 0);

//    pMainBooter = (void (*)(void))(MAIN_BOOTER_RAM_START_ADDR + (MAIN_BOOTER_ROM_START_ADDR - NAND_BOOTER_ROM_START_ADDR));
      pMainBooter = (void (*)(void))(MAIN_BOOTER_RAM_START_ADDR);

      pMainBooter();
#else                           // SPICA
#endif
//*/

      while (1);
}

#ifdef  __cplusplus
}
#endif
//=============================================================================
void  IsrHandlerTimer0(void)
{
//    SysIncSystemTimer();
//    G_pSysTimer0->RunTimerIsrHandler();
//    G_pBuzzer->RunBuzzerIsrHandler();
//    G_pKeyBD->RunKeyIsrHandler();


     ((xSYS_TIMER *)WATCH_DOG_TIMER_PHSY_BASE_ADDR)->dTMRCOUNT = 0x00000000;

      CheckPowerStatus();
      cTIME::RunIntHandler();
      SysIncSystemTimer();
      G_pSysTimer0->RunTimerIsrHandler();
      if (GetPowerStatus())
          cKEYBD::RunIntHandler();
      cBUZZER::RunIntHandler();
}
void  IsrHandlerUART0(void)
{
}
void  IsrHandlerUART1(void)
{
//    G_pUartPort1->RunUartIsrHandler();

      G_pUart0->RunUartIsrHandler();
}
void  IsrHandlerUART2(void)
{
//    G_pUartPort2->RunUartIsrHandler();
}
void  IsrHandlerUART3(void)
{
//    G_pUartPort3->RunUartIsrHandler();
}
void  IsrHandlerGPIO(void)
{
}
//=============================================================================
void  SysSetAllInterrupt(void)
{
      SysSetOneInterrupt(IRQ_PHY_SYSTIMER0,INT_MODE_IRQ);
      SysSetOneInterrupt(IRQ_PHY_UART1    ,INT_MODE_IRQ);
//    SysSetOneInterrupt(IRQ_PHY_UART2    ,INT_MODE_IRQ);
//    SysSetOneInterrupt(IRQ_PHY_UART3    ,INT_MODE_IRQ);
}
void  SysSetOneInterrupt(int nIntNo,int nIRQ_FIQ)
{
      volatile DWORD *pIntMode;
      volatile DWORD *pIntMask;
      int   nShift;
      DWORD dMaskX;

      if (nIntNo >= 32)
         {
          pIntMode = &(G_pSysIntrCtrl->dINTMODE[1]);
          pIntMask = &(G_pSysIntrCtrl->dINTMASK[1]);
          nShift   = nIntNo - 32;
         }
      else
         {
          pIntMode = &(G_pSysIntrCtrl->dINTMODE[0]);
          pIntMask = &(G_pSysIntrCtrl->dINTMASK[0]);
          nShift   = nIntNo;
         }

      dMaskX = 1 << nShift;

      if (nIRQ_FIQ == INT_MODE_IRQ)
          *pIntMode &= ~dMaskX;
      else
          *pIntMode |=  dMaskX;

      *pIntMask &= ~dMaskX;
}
void  SysSetAllInterruptDisable(void)
{
      xSYS_INTR *pSysIntr = (xSYS_INTR *)INTC_PHSY_BASE_ADDR;

      pSysIntr->dINTMASK[0] = 0xffffffff;
      pSysIntr->dINTMASK[1] = 0xffffffff;
}
//=============================================================================
//==========// POLLUX==========================================================
#endif      // POLLUX

