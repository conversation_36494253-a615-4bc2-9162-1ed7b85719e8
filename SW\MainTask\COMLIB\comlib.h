/*...........................................................................*/
/*.                  File Name : COMLIB.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.31                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __COMLIB_HPP
#define  __COMLIB_HPP

#include "type.hpp"
#include "DataType.h"


#define  CODE_SET_UNICODE   0
#define  CODE_SET_JOHAP     1
#define  CODE_SET_GB2312    2

#ifdef  __cplusplus
extern "C" {
#endif

void  LoopDelay(DWORD Delay);

DWORD GetPartialCrc32(const UCHAR *pData, DWORD dSize, int nMode);
DWORD GetCrc32(const UCHAR *pData,DWORD dSize);
int   compress(CHAR *pTargetData,DWORD *nTargetSize,const CHAR *pSourceData,DWORD nSourceSize);
int   uncompress(CHAR *pTargetData,DWORD *nTargetSize,const CHAR *pSourceData,DWORD nSourceSize);
int   UnPackFileData(const BYTE *pSourceData,BYTE **pData,DWORD *pSize,BYTE *pPackAddr,BYTE *pBuffMemP,DWORD dBuffSize);
WORD  HanCodeChrConvertKSToCombi(WORD wCode);
void  HanCodeStrConvert(BYTE *pSource,BYTE *pTarget);

void  Convert32BitColorTo8BitRGB(DWORD dColor,UCHAR *pA,UCHAR *pR,UCHAR *pG,UCHAR *pB);
void  Convert16BitColorTo8BitRGB(HWORD wColor,UCHAR *pR,UCHAR *pG,UCHAR *pB);

void   RangeCheckBack16(BACK16 *pDataValue,BACK16 nLower,BACK16 nUpper,BACK16 nSetValue);
void   RangeCheckBack32(BACK32 *pDataValue,BACK32 nLower,BACK32 nUpper,BACK32 nSetValue);

DWORD  GetCrc32Cont(const UCHAR *pData,DWORD dSize,DWORD dPrevCRC);
DWORD  GetUnAlignedLongData(UCHAR *pData);
UCHAR  UpperChar(UCHAR bData);
DWORD  HexStrToLong(UCHAR *pHexStr,int nSize);
CHAR   *FullAllTrimStr(CHAR *pStr);
CHAR   *RightTrimStr(CHAR *pStr,CHAR bTrimChar);
HWORD  JohabToUniCode(HWORD wJohabCode);
size_t JohabToUniCodeStr(WCHAR *pData, const char *pSrc, size_t nLen);
size_t GB2312ToUniCodeStr(WCHAR *pData, const char *pSrc, size_t nLen);
int	   UniCodeStrLen(const HWORD *pUniCode);
int    IsBasicAsciiCodeStr(const HWORD *pUniCode);
void   CopyCharStrToUniStr(unsigned short *pTarget,char *pSource);
void   CatCharStrToUniStr(unsigned short *pTarget,char *pSource);
HWORD  *UniStrCpy(HWORD *pTarget,HWORD *pSource);
HWORD  *UniStrnCpy(HWORD *pTarget,HWORD *pSource, int n);
HWORD  *UniStrCat(HWORD *pTarget,HWORD *pSource);

HWORD UniCodeToJohab(HWORD wUniCode);
CHAR *UniStrToChohab(const HWORD *pSource,CHAR *pTarget,int nHanMode);
CHAR  *UniStrToChrStr(const HWORD *pSource,CHAR *pTarget);

void  JohabStrToUniStr(UCHAR *pSource,HWORD *pTarget);
WORD GB2312ToUniCode(WORD wCode);
HWORD *ConvertCharStrToUniStr(CHAR *pSource, int nLanCode);
HWORD *ConvertCharStrToUniStr2(CHAR *pSource, int nLanCode);

int MakeSizeOfDoubleWord(int nSize);
void  SwapInt(int *pX,int *pY);
void  SwapLong(long *pX,long *pY);

int SplitTextData(UCHAR *pSource,UCHAR *pOne,UCHAR *pTwo,int nChars);

int IsLinkingVowelCode(HWORD wUniCode);

int CirCularDec(int nValue,int nLast);
int CirCularInc(int nValue,int nLast);
int GetCircularSize(int nHead,int nTail,int nSize);

INT32 PowerOf10(int nPower);
int   StrToInt(CHAR *pStr,int nStart,int nLen);

#ifdef  __cplusplus
}
#endif

#endif

