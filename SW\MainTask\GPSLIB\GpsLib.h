/*...........................................................................*/
/*.                  File Name : GpsLib.h                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.08.05                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __GPSLIB_H__
#define  __GPSLIB_H__

//=============================================================================
#define  M_PI                    3.14159265358979323846
#define  PI_MUL_2                (M_PI * 2.0)
#define  TO_RADIAN               (M_PI / 180.0)
#define  TO_DEGREE               (180.0 / M_PI)
//-----------------------------------------------------------------------------
#define  EARTH_EQUATOR           6378388.0
//#define  EARTH_RADIUS            (6371229.315 / 1852.0)
#define  EARTH_RADIUS            (180.0 * 60.0 / M_PI)
#define  MERCATOR_A              6378388.0
#define  MERCATOR_B              6356911.9
#define  MERCATOR_E              0.0819919778847859
                                 // (sqrt(MERCATOR_A * MERCATOR_A - MERCATOR_B * MERCATOR_B) / MERCATOR_A)
#define  M_ECC2                  (MERCATOR_E * MERCATOR_E)
#define  M_ECC4                  (M_ECC2 * M_ECC2)
#define  M_ECC6                  (M_ECC2 * M_ECC4)
#define  M_ECC2_A                (MERCATOR_A / 1852.0 * M_ECC2)
#define  M_ECC4_A                (MERCATOR_A / 1852.0 * M_ECC4)
#define  M_ECC6_A                (MERCATOR_A / 1852.0 * M_ECC6)
//-----------------------------------------------------------------------------
#define  BESSEL_DX_DIFF          (-147.000000000)
#define  BESSEL_DY_DIFF          (+506.000000000)
#define  BESSEL_DZ_DIFF          (+687.000000000)
#define  SK_42_DX_DIFF           ( +25.0000000000)
#define  SK_42_DY_DIFF           (-141.0000000000)
#define  SK_42_DZ_DIFF           ( -80.0000000000)
//-----------------------------------------------------------------------------
#define  BESSEL_MAJOR            (6377397.155000)
#define  BESSEL_MINOR            (6356078.963250)
#define  BESSEL_FLATTEN          ((BESSEL_MAJOR - BESSEL_MINOR) / BESSEL_MAJOR)
#define  BESSEL_ECCENT2          (2.0 * BESSEL_FLATTEN - BESSEL_FLATTEN * BESSEL_FLATTEN)
#define  WGS84_MAJOR             (6378137.000000)
#define  WGS84_MINOR             (6356752.314200)
#define  WGS84_FLATTEN           ((WGS84_MAJOR - WGS84_MINOR) / WGS84_MAJOR)
#define  WGS84_ECCENT2           (2.0 * WGS84_FLATTEN - WGS84_FLATTEN * WGS84_FLATTEN)
//-----------------------------------------------------------------------------
#define  COOR_BASE_MUL                   3600000
#define  COOR_BASE_MUL_MIN       (COOR_BASE_MUL / 60)
#define  COOR_BASE_MUL_SEC       (COOR_BASE_MUL / 3600)
#define  COOR_BASE_MUL_MIN_1000  (COOR_BASE_MUL / 60000)
#define  ABS_COOR_000                          0
#define  ABS_COOR_085                  306000000
#define  ABS_COOR_089                  320400000
#define  ABS_COOR_090                  324000000
#define  ABS_COOR_180                  648000000
#define  ABS_COOR_360                 1296000000
#define  POS_NULL_VALUE               1296000000
//-----------------------------------------------------------------------------
#define  MERC_LAT_MAX_DEG               89.99990
//-----------------------------------------------------------------------------
#define  MERC_LAT_MAX                   88989448
#define  MERC_LAT_000                          0
#define  MERC_LAT_089                   30242167
#define  MERC_LAT_085                   19972655
#define  MERC_LON_000                          0
#define  MERC_LON_100                   11132387
#define  MERC_LON_120                   13358865
#define  MERC_LON_150                   16698581
#define  MERC_LON_180                   20038297
#define  MERC_LON_360                   40076594
//-----------------------------------------------------------------------------
#define  MERC_LAT_001_MIN                   1855 
#define  MERC_LON_001_MIN                   1855 
//-----------------------------------------------------------------------------
#define  POS_VALUE_TYPE_DEGREE                0
#define  POS_VALUE_TYPE_RADIAN                1
//=============================================================================

#define  AIS_FLOAT_LAT_NULL_VAL			(91.0f)
#define  AIS_FLOAT_LON_NULL_VAL			(181.0f)
#define  AIS_REAL_LAT_NULL_VAL			(91.0)
#define  AIS_REAL_LON_NULL_VAL			(181.0)

#define AIS_DIST_NM_INVALID				100000//-1.0f

#ifdef  __cplusplus
extern "C" {
#endif

LGRID AbsGrid(LGRID nData);
REAL  GridLonToSigndReal(LGRID nLon);
LGRID RealToGrid(LREAL rData);
LREAL GridToReal(LGRID nData);
LMERC RealLatToMerc(LREAL rLat);
LMERC RealLonToMerc(LREAL rLon);
LREAL MercLatToReal(LMERC nLat);
LREAL MercLonToReal(LMERC nLon);
REAL  MercLonToSignedReal(LMERC nLon);
LMERC GridLatToMerc(LGRID nLat);
LMERC GridLonToMerc(LGRID nLon);
LGRID MercLatToGrid(LMERC nLat);
LGRID MercLonToGrid(LMERC nLon);
LREAL DiffLatReal(REAL rLat1,REAL rLat2);
LREAL DiffLonReal(REAL rLon1,REAL rLon2);
LGRID DiffLatGrid(LGRID nLat1,LGRID nLat2);
LGRID DiffLonGrid(LGRID nLon1,LGRID nLon2);
LMERC DiffLatMerc(LMERC nLat1,LMERC nLat2);
LMERC DiffLonMerc(LMERC nLon1,LMERC nLon2);
LREAL OffsetLatReal(LREAL rLat1,LREAL rLat2);
LGRID OffsetLatGrid(LGRID nLat1,LGRID nLat2);
LMERC OffsetLatMerc(LMERC nLat1,LMERC nLat2);
LREAL OffsetLonReal(LREAL rLon1,LREAL rLon2);
LGRID OffsetLonGrid(LGRID nLon1,LGRID nLon2);
LMERC OffsetLonMerc(LMERC nLon1,LMERC nLon2);
LREAL CalcMeridionalLatValue(LREAL rLat,int nValueType);
LREAL CalcMeridionalLonValue(LREAL rLon,int nValueType);
LREAL CalcMeridionalDifference(LREAL rLat1,LREAL rLat2);
LREAL CalcRealLatCenterValue(LREAL rUpLat,LREAL rDnLat);
LREAL CalcRealLonCenterValue(LREAL rLtLon,LREAL rRtLon);
LREAL CheckRealLatRange(LREAL rLat);
LGRID CheckGridLatRange(LGRID nLat);
LREAL CheckRealLonRange(LREAL rLon);
LGRID CheckGridLonRange(LGRID nLon);
REAL  SetRealLonToOrigin(LREAL rLon);
LGRID SetGridLonToOrigin(LGRID nLon);
int   CheckLonInGridLonRange(LGRID nLon,LGRID nLonLeft,LGRID nLonRight);
int   CheckLonInRealLonRange(LREAL rLon,LREAL rLonLeft,LREAL rLonRight);
int   CheckRealLonIntersect(LREAL rMapLt,LREAL rMapRt,LREAL *pL,LREAL *pR,LREAL rScrLt,LREAL rScrRt);
int   CheckGridLonIntersect(LGRID nMapLt,LGRID nMapRt,LGRID *pL,LGRID *pR,LGRID nScrLt,LGRID nScrRt);
int   IsInGridRange(LGRID nData,LGRID nMin,LGRID nMax);
int   IsRealLonZeroCross(LREAL rLonL,LREAL rLonR);
int   IsGridLatRangeError(LGRID nLat);
int   IsGridLonRangeError(LGRID nLon);
int   IsMercLatRangeError(LMERC nMercLat);
int   IsMercLonRangeError(LMERC nMercLon);
REAL  ConvertKNtoBFT(REAL rKn);
REAL  ConvertBFTtoKN(REAL rBft);
REAL  ConvertDstToDst(REAL nDstVal,int nFromUnit,int nToUnit);
int   ConvertDptToDpt(int nDptVal,int nFromUnit,int nToUnit);
int   ConvertSpdToSpd(int nSpdVal,int nFromUnit,int nToUnit);
int   ConvertCrsToCrs(int nCrsVal,int nFromUnit,int nToUnit);
REAL  ConvertRealCrsToRealCrs(REAL rCrsVal,int nFromUnit,int nToUnit);
int   ConvertTmpToTmp(int nTmpVal,int nFromUnit,int nToUnit);
REAL  ConvertFulToFul(REAL rFulVal,int nFromUnit,int nToUnit);
REAL  ConvertPrsToPrs(REAL rPrsVal,int nFromUnit,int nToUnit);
REAL  ConvertBaroToBaro(REAL rBaroVal,int nFromUnit,int nToUnit);
int   CalcHalfDirAngle(int nAngle);

void  SetDistUnitMode(int nMode);
int   GetDistUnitMode(void);
void  SetSpdUnitMode(int nMode);
int   GetSpdUnitMode(void);
void  SetDptUnitMode(int nMode);
int   GetDptUnitMode(void);
void  SetFuelUnitMode(int nMode);
int   GetFuelUnitMode(void);
void  SetCmpssUnitMode(int nMode);
int   GetCmpssUnitMode(void);
void  SetTempUnitMode(int nMode);
int   GetTempUnitMode(void);
void  SetWdirUnitMode(int nMode);
int   GetWdirUnitMode(void);
void  SetWspdUnitMode(int nMode);
int   GetWspdUnitMode(void);
void  SetPrssUnitMode(int nMode);
int   GetPrssUnitMode(void);
void  SetBaroUnitMode(int nMode);
int   GetBaroUnitMode(void);
CHAR  *GetDistUnitChrStr(void);
HWORD *GetDistUnitUniStr(void);
CHAR  *GetSpeedUnitChrStr(void);
HWORD *GetSpeedUnitUniStr(void);
CHAR  *GetCompassUnitChrStr(void);
HWORD *GetCompassUnitUniStr(void);
CHAR  *GetDepthUnitChrStr(void);
HWORD *GetDepthUnitUniStr(void);
CHAR  *GetFuelUnitChrStr(void);
HWORD *GetFuelUnitUniStr(void);
CHAR  *GetWindDirUnitChrStr(void);
HWORD *GetWindDirUnitUniStr(void);
CHAR  *GetWindSpdUnitChrStr(void);
HWORD *GetWindSpdUnitUniStr(void);
CHAR  *GetPrssUnitChrStr(void);
HWORD *GetPrssUnitUniStr(void);
CHAR  *GetBaroUnitChrStr(void);
HWORD *GetBaroUnitUniStr(void);
CHAR  *GetTempUnitChrStr(void);
HWORD *GetTempUnitUniStr(void);

void  SetMagneticVarVal(int nVal);
int   GetMagneticVarVal(void);

int   CheckCompassValue(int nValue);
REAL  CheckRealCompassValue(REAL rValue);

void  SetAisStatusMode(int nMode);
int   GetAisStatusMode(void);

void  SetSystemDateUTC(int nYear,int nMonth,int nDay);
void  GetSystemDateUTC(int *pYear,int *pMonth,int *pDay);
void  SetSystemTimeUTC(int nHour,int nMin,int nSec);
void  GetSystemTimeUTC(int *pHour,int *pMin,int *pSec);
void  SetSystemDateLOC(int nYear,int nMonth,int nDay);
void  GetSystemDateLOC(int *pYear,int *pMonth,int *pDay);
void  SetSystemTimeLOC(int nHour,int nMin,int nSec);
void  GetSystemTimeLOC(int *pHour,int *pMin,int *pSec);

void  SetSysVoltage(int nVolt);
int   GetSysVoltage(void);

void  SetGpsFixStatus(int nStatus,int nSetInstantly);
int   GetGpsFixStatus(void);
int   CheckGpsLostStatus(void);

void  SetShipRcvGridLat(LGRID nLat);
LGRID GetShipRcvGridLat(void);
void  SetShipRcvGridLon(LGRID nLon);
LGRID GetShipRcvGridLon(void);
LMERC GetShipRcvMercLat(void);
LMERC GetShipRcvMercLon(void);

void  SetShipRawGridLat(LGRID nLat);
LGRID GetShipRawGridLat(void);
void  SetShipRawGridLon(LGRID nLon);
LGRID GetShipRawGridLon(void);
void  SetShipLatDatumOffset(LGRID nLatOffset);
LGRID GetShipLatDatumOffset(void);
void  SetShipLonDatumOffset(LGRID nLonOffset);
LGRID GetShipLonDatumOffset(void);
void  SetNmeaDatumOffsetMode(int nMode);
int   GetNmeaDatumOffsetMode(void);

void  SetShipGridLat(LGRID nLat);
void  SetShipGridLon(LGRID nLon);
LGRID GetShipGridLat(void);
LGRID GetShipGridLon(void);
LMERC GetShipMercLat(void);
LMERC GetShipMercLon(void);
LREAL GetShipRealLat(void);
LREAL GetShipRealLon(void);
LGRID *GetShipGridLatPtr(void);
LGRID *GetShipGridLonPtr(void);
void  SetShipSogVal(int nSOG);
int   GetShipSogVal(void);
void  SetShipSowVal(int nSOW);
int   GetShipSowVal(void);
void  SetShipRawSowVal(int nSOW);
int   GetShipRawSowVal(void);
void  SetShipSowSrc(int nSRC);
int   GetShipSowSrc(void);
void  SetShipSowEquippedMode(int nMode);
int   GetShipSowEquippedMode(void);
void  SetShipTmpEquippedMode(int nMode);
int   GetShipTmpEquippedMode(void);
void  SetShipSpeedVal(int nSpeed);
int   GetShipSpeedVal(void);
void  SetShipCogVal(int nCOG);
int   GetShipCogVal(void);
void  SetShipHdgVal(int nHDG);
int   GetShipHdgVal(void);
void  SetShipHdgSrc(int nSRC);
int   GetShipHdgSrc(void);
void  SetShipCourseVal(int nCourse);
int   GetShipCourseVal(void);
void  SetWaterTempVal(int nTmpVal);
int   GetWaterTempVal(void);
void  SetWaterRawTempVal(int nTmpVal);
int   GetWaterRawTempVal(void);
void  SetWaterTempSrc(int nSRC);
int   GetWaterTempSrc(void);
void  SetAirTempVal(int nTmpVal);
int   GetAirTempVal(void);
void  SetAirTempSrc(int nTmpSrc);
int   GetAirTempSrc(void);

void  SetVPWSpdVal(int nVPW);
int   GetVPWSpdVal(void);
void  SetVPWSpdSrc(int nVPWSrc);
int   GetVPWSpdSrc(void);


void  SetTotalDist(REAL rTotalDist);
REAL  GetTotalDist(void);
void  SetTripDist(REAL rTripDist);
REAL  GetTripDist(void);

void  SetDptValue(int nDptValue);
int   GetDptValue(void);
void  SetDptSource(int nSRC);
int   GetDptSource(void);
void  SetDptDspValue(int nDptDspValue);
int   GetDptDspValue(void);
void  SetKeelOffsetValue(int nKeelOffset);
int   GetKeelOffsetValue(void);

void  SetCaliSpeedVal(int nCaliSpeedVal,int nCaliSpeedRaw);
int   GetCaliSpeedVal(void);
void  SetCaliSpeedFlt(int nCaliSpeedFlt);
int   GetCaliSpeedFlt(void);
void  SetCaliTempVal(int nCaliTempVal,int nCaliTempRaw);
int   GetCaliTempVal(void);
void  SetCaliTempFlt(int nCaliTempFlt);
int   GetCaliTempFlt(void);
void  SetCaliSpdRngVal(int nCaliSpdRngVal);
int   GetCaliSpdRngVal(void);

void  AppendCaliSpeedVal(DWORD dTimerCounter);
void  CheckCaliSpeedVal(void);
void  SmoothCaliSpeedVal(int nValInKT);
void  CheckCaliTempVal(void);
int   SmoothRawTempData(int *pRawData,int nFullSize,int nRealSize,int nNoneSize);
void  SmoothCaliTempVal(int nValInC);
void  CheckCaliVoltVal(void);
void  SmoothCaliVoltVal(int nValInV);

void  SetWaterPressure(int nEngineNo,REAL nPrss);
REAL  GetWaterPressure(int nEngineNo);
void  SetTrimValue(int nEngineNo,int nTrim);
int   GetTrimValue(int nEngineNo);

void  SetEngineHour(int nEngineNo,int nHour);
int   GetEngineHour(int nEngineNo);
void  SetEngineVlot(int nEngineNo,int nVolt);
int   GetEngineVlot(int nEngineNo);
void  SetEngineTemp(int nEngineNo,int nTemp);
int   GetEngineTemp(int nEngineNo);
void  SetEngineRpm(int nEngineNo,int nRpm);
int   GetEngineRpm(int nEngineNo);
void  SetEngineRpmSrc(int nSRC);
int   GetEngineRpmSrc(void);

void  SetFuelEcono(int nEngineNo,int nEcono);
int   GetFuelEcono(int nEngineNo);

void  SetFuelInstantEcono(int nEngineNo,int nEcono);
int   GetFuelInstantEcono(int nEngineNo);

void  SetFuelUsed(int nEngineNo,int nUsed);
int   GetFuelUsed(int nEngineNo);
void  SetFuelRemain(int nEngineNo,int nRemain);
int   GetFuelRemain(int nEngineNo);
void  SetFuelFlow(int nEngineNo,REAL nFlow);
REAL  GetFuelFlow(int nEngineNo);
void  SetFuelLevel(int nEngineNo,int nLevel);
int   GetFuelLevel(int nEngineNo);

void  SetFuelCapacity(int nEngineNo,int nLevel);
int   GetFuelCapacity(int nEngineNo);

void  SetOilLevel(int nEngineNo,int nLevel);
int   GetOilLevel(int nEngineNo);
void  SetOilTemp(int nEngineNo,int nTemp);
int   GetOilTemp(int nEngineNo);
void  SetOilPressure(int nEngineNo,REAL nPrss);
REAL  GetOilPressure(int nEngineNo);

void  SetOilCapacity(int nEngineNo,int nCap);
int   GetOilCapacity(int nEngineNo);

//void  SetEngineCoolantPressure(int nEngine,DWORD dValue);
//DWORD GetEngineCoolantPressure(int nEngine);
void  SetEngineFuelPressure(int nEngine,DWORD dValue);
DWORD GetEngineFuelPressure(int nEngine);
void  SetEngineStatus1(int nEngine,DWORD dValue);
DWORD GetEngineStatus1(int nEngine);
void  SetEngineStatus2(int nEngine,DWORD dValue);
DWORD GetEngineStatus2(int nEngine);
void  SetEngineLoad(int nEngine,int nValue);
int   GetEngineLoad(int nEngine);
void  SetEngineTorque(int nEngine,int nValue);
int   GetEngineTorque(int nEngine);

void  SetLibOrgWptNo(int nWptNo);
int   GetLibOrgWptNo(void);
void  SetLibRunWptNo(int nWptNo);
int   GetLibRunWptNo(void);
void  SetBrgFromOrg(int nBrg);
int   GetBrgFromOrg(void);
void  SetBrgToNext(int nBrg);
int   GetBrgToNext(void);
void  SetCtsToNext(int nCrs);
int   GetCtsToNext(void);
void  SetDstToNext(REAL rDst);
REAL  GetDstToNext(void);
void  SetDstToDest(REAL rDst);
REAL  GetDstToDest(void);
void  SetEtaNextUTC(int nYear,int nMonth,int nDay,int nHour,int nMin,int nSec);
void  GetEtaNextUTC(int *pYear,int *pMonth,int *pDay,int *pHour,int *pMin,int *pSec);
void  SetEtaNextLOC(int nYear,int nMonth,int nDay,int nHour,int nMin,int nSec);
void  GetEtaNextLOC(int *pYear,int *pMonth,int *pDay,int *pHour,int *pMin,int *pSec);
void  SetEtaDestUTC(int nYear,int nMonth,int nDay,int nHour,int nMin,int nSec);
void  GetEtaDestUTC(int *pYear,int *pMonth,int *pDay,int *pHour,int *pMin,int *pSec);
void  SetEtaDestLOC(int nYear,int nMonth,int nDay,int nHour,int nMin,int nSec);
void  GetEtaDestLOC(int *pYear,int *pMonth,int *pDay,int *pHour,int *pMin,int *pSec);
void  SetSteerValue(int nSteer);
int   GetSteerValue(void);
void  SetTimeToNext(int nTime);
int   GetTimeToNext(void);
void  SetTimeToDest(int nTime);
int   GetTimeToDest(void);
void  SetVmgToNext(int nVmg);
int   GetVmgToNext(void);
void  SetXteToNext(REAL rXte);
REAL  GetXteToNext(void);
void  SetRealNextLat(LREAL rLat);
LREAL GetRealNextLat(void);
void  SetRealNextLon(LREAL rLon);
LREAL GetRealNextLon(void);
void  SetBrgToAfter(int nBrg);
int   GetBrgToAfter(void);

void  SetBaroValue(REAL nBar);
REAL  GetBaroValue(void);
void  SetWindDir(int nDir);
int   GetWindDir(void);
void  SetWindSpd(int nSpd);
int   GetWindSpd(void);
void  SetWindReference(int nWindReference);
int   GetWindReference(void);
void  SetWindSrc(int nSRC);
int   GetWindSrc(void);

void  SetCdiValue(int nCdi);
int   GetCdiValue(void);
REAL  GetCdiRunVal(int nCdiIndex);
char *GetCdiRunStr(int nCdiIndex);
void  SetDestName(char *pName);
char *GetDestName(void);
void  SetAfterName(char *pName);
char *GetAfterName(void);
void  SetSonarFreqMode(int nMode);
int   GetSonarFreqMode(void);
void  SetSonarCoverage(int nVal);
int   GetSonarCoverage(void);

#if   SONAR_GAIN_COMPILE_PCB_RUN == SONAR_GAIN_COMPILE_PCB_NEW
void  SetSonarLevelDataChanged(int nChanged);
void  SetSonarLevelDataPtr(int nFreq,UCHAR *pLevelPtr);
void  SetSonarGainBaseRunVal(int nFreq,int nVal);
void  SetSonarLevelPerColor(int nVal);
void  SetSonarSplitRunMode(int nMode);
void  SetSonarRawRunData(UCHAR *pRawData);
int   GetSonarLevelStartPos(UCHAR *pData,int nLevel);
#endif

void  SetNavMode(int nNavMode);
int   GetNavMode(void);

void  SetSatInfoAvailable(int nMode);
int   GetSatInfoAvailable(void);
void  SetGSAData(UCHAR *pData);
void  AppendGSVData(UCHAR *pData);
void  ClearGSVData(UCHAR *pData);

void  SetSatOperateMode(UCHAR bMode);
UCHAR GetSatOperateMode(void);
void  SetSatFixMode(UCHAR bMode);
UCHAR GetSatFixMode(void);
void  SetSatPDOP(int nValue);
int   GetSatPDOP(void);
void  SetSatHDOP(int nValue);
int   GetSatHDOP(void);
void  SetSatVDOP(int nValue);
int   GetSatVDOP(void);
void  SetNoOfSatInCalc(int nNo);
int   GetNoOfSatInCalc(void);
void  SetSatInfoCalcID(int nPos,int nID);
int   GetSatInfoCalcID(int nPos);

void  SetSatInfoNoOfSat(int nNo);
int   GetSatInfoNoOfSat(void);
void  SetSatInfoAllData(int nPos,int nPRN,int nElevn,int nAzmth,int nGainX,int nState);
void  GetSatInfoAllData(int nPos,int *pPRN,int *pElevn,int *pAzmth,int *pGainX,int *pState);

void  SetLibAntHeightRcvd(int nHeight);
int   GetLibAntHeightRcvd(void);
void  SetLibMagVarRcvd(int nMagVar);
int   GetLibMagVarRcvd(void);
void  SetLibGeoidalSep(int nGeoidalSep);
int   GetLibGeoidalSep(void);
void  SetLibAgeOfDGPS(int nAge);
int   GetLibAgeOfDGPS(void);
void  SetLibIDofDGPS(char *pID);
char *GetLibIDofDGPS(void);

void  SetTimeLocalOffset(int nOffset);
int   GetTimeLocalOffset(void);
void  SetTimeTimeFormat(int nFormat);
int   GetTimeTimeFormat(void);
void  SetTimeDateFormat(int nFormat);
int   GetTimeDateFormat(void);

void  SetChartCursorMode(int nMode);
int   GetChartCursorMode(void);
void  SetChartCursorTime(DWORD dTime);
DWORD GetChartCursorTime(void);
void  SetChartCursorGridLat(LGRID nLat);
void  SetChartCursorGridLon(LGRID nLon);
LGRID GetChartCursorGridLat(void);
LGRID GetChartCursorGridLon(void);
LMERC GetChartCursorMercLat(void);
LMERC GetChartCursorMercLon(void);
LREAL GetChartCursorRealLat(void);
LREAL GetChartCursorRealLon(void);

void  SetMapShiftMercLatLon(int nFactLat,int nFactLon);
LMERC GetMapShiftMercLat(void);
LMERC GetMapShiftMercLon(void);
void  SetMapShiftRealLatLon(REAL rLat,REAL rLon);
REAL  GetMapShiftRealLat(void);
REAL  GetMapShiftRealLon(void);
void  SetChartDatumOffsetMercLatLon(LGRID nGridLat,LGRID nGridLon);
void  GetAllChartDatumOffsetValue(LMERC *pMercLat,LMERC *pMercLon);

void  SetAllWinReDrawMode(int nMode);
int   GetAllWinReDrawMode(void);
void  SetHaveToDrawSonarData(int nMode);
int   GetHaveToDrawSonarData(void);

CHAR  *GetShipLatChrStr(void);
HWORD *GetShipLatUniStr(void);
CHAR  *GetShipLonChrStr(void);
HWORD *GetShipLonUniStr(void);
CHAR  *GetLatChrStr(LGRID nGridLat);
CHAR  *GetLonChrStr(LGRID nGridLon);
#ifdef _SI_70AM_
CHAR  *GetLatChrStr2(LGRID nGridLat);
CHAR  *GetLonChrStr2(LGRID nGridLon);
#endif
void  GetShipLatLonChrStr(CHAR *pLat,CHAR *pLon);
void  GetShipLatLonUniStr(HWORD *pLat,HWORD *pLon);
CHAR  *GetSpeedChrStr(int nSpeed,int nUnitAppend);
HWORD *GetSpeedUniStr(int nSpeed,int nUnitAppend);
CHAR  *GetCourseChrStr(int nCourse,int nUnitAppend);
HWORD *GetCourseUniStr(int nCourse,int nUnitAppend);

int   GetTimeDiffValueByIndex(int nIndex);
int   GetTimeDiffIndexByValue(int nValue);
void  GetHourMinByTimeDiffValue(int nValue,int *pHour,int *pMin);
int   GetTimeDiffValueByChrStr(char *pStr);
CHAR  *GetTimeDiffChrStr(int nTimeDiff);
HWORD *GetTimeDiffUniStr(int nTimeDiff);
int   GetMagVarValueByChrStr(char *pStr);
CHAR  *GetMagVarSgnStr(int nMagVarVal);
CHAR  *GetMagVarChrStr(int nMagVarVal);
HWORD *GetMagVarUniStr(int nMagVarVal);
UNICODE **GetVgaColorTable(void);
UNICODE **GetVgaDelAllColorTable(UNICODE **pDelAllMsg);
char  *GetAppendedSamDataDirName(const char *pFileName);
int   CheckExistSamDataDirName(int nMakeMode);
int   ReMakeSamDataDirectory(void);
UNICODE **GetSamColorTableX(void);
UNICODE **GetSamColorTableY(UNICODE **pOffMsg);
CHAR  *GetDateChrStr(int nYear,int nMonth,int nDay,int nDateFormat);
CHAR  *GetTimeChrStr(int nHour,int nMinute,int nTimeFormat);
CHAR  *GetFullTimeChrStr(int nHour,int nMinute,int nSecond,int nTimeFormat);
void  SetDayOfWeekUniStr(int nDayOfWeek,UNICODE **pWeekStr);
UNICODE *GetDayOfWeekUniStr(int nDayOfWeek,int nLngCode);
void  GetDistanceAndCourse(LREAL rLat1,LREAL rLon1,LREAL rLat2,LREAL rLon2,REAL *pDist,LREAL *pCourse);
void  GetDistanceAndCourseInGRC(LREAL rLat1,LREAL rLon1,LREAL rLat2,LREAL rLon2,REAL *pDist,REAL *pCourse);
void  GetDistanceAndCourseInMCR(LREAL rLat1,LREAL rLon1,LREAL rLat2,LREAL rLon2,REAL *pDist,REAL *pCourse);
void  GetTargetPointByDistanceAndCourse(LREAL rStartLat,LREAL rStartLon,REAL rDistance,REAL rCourse,LREAL *pTargetPntLat,LREAL *pTargetPntLon);
void  GetTargetPointByDistanceAndCourseInGRC(LREAL rStartLat,LREAL rStartLon,REAL rDistance,REAL rCourse,LREAL *pTargetPntLat,LREAL *pTargetPntLon);
void  GetTargetPointByDistanceAndCourseInMCR(LREAL rStartLat,LREAL rStartLon,REAL rDistance,REAL rCourse,LREAL *pTargetPntLat,LREAL *pTargetPntLon);
void  GetLatLonStringByGrid(LGRID nLat,UCHAR *pLatStr,LGRID nLon,UCHAR *pLonStr,int nSecLen);
UCHAR *GridToString(LGRID nValue,int nLen,UCHAR *pSign,int nSecLen);
#ifdef _SI_70AM_
UCHAR *GridToString2(LGRID nValue,int nLen,UCHAR *pSign,int nSecLen);
#endif
LGRID LatStrToGridVal(CHAR *pLat);
LGRID LonStrToGridVal(CHAR *pLon,int nCheckJob);
int   FindFormatDeciPointLeng(char *pFormatStr);
int   FindStringDeciPointLeng(char *pStringTxt);
int   GetDepthMul100Value(int nDepthUnit);
int   ConvertDepthToCentiMeter(int nDepthUnit,int nMul100Data);

void  DrawOneSonarLine(UCHAR *pSonarData,int nSonarHeight,int nScrnX,int nScrnY,int nScrnHeight,int nCurrentRange,int nMaxDotSize);
void  DrawAscopeScreen(UCHAR *pSonarData,int nSonarHeight,int nScrnX,int nScrnY,int nScrnHeight,int nScrnWidth,int nCurrentRange,int nMaxDotSize);
#if   SONAR_GAIN_COMPILE_PCB_RUN == SONAR_GAIN_COMPILE_PCB_NEW
void  DrawAlevelScreen(UCHAR *pSonarData,int nSonarHeight,int nScrnX,int nScrnY,int nScrnHeight,int nScrnWidth,int nCurrentRange,int nMaxDotSize);
#endif

REAL  CalcScrnLonValByDistDeg(REAL rDstInNM,REAL rDeg,int nUsingMap);
int   CalcScrnHoriDotByDist(REAL rDist,int nScrnMaxWidthDot);
void  CalcScrnRotatedCoordinate(int nScrnOrgX,int nScrnOrgY,int nRadius,int nRotatedDegree,int *pRotatedScrnX,int *pRotatedScrnY);

void  SetGlobalCrsUpDegree(int nDegree);
int   GetGlobalCrsUpDegree(void);

void  SetLibSimulateMode(int nMode);
int   GetLibSimulateMode(void);
void  SetLibGpsSource(int nSource);
int   GetLibGpsSource(void);

int   IsCMapCardInSdSlot(void);
int   IsSdCardWritable(void);
void  SetSpecialKeyToMainWinMode(int nMode);
int   GetSpecialKeyToMainWinMode(void);

void  SetFactoryTestMode(int nMode);
int   GetFactoryTestMode(void);
void  SetSysVoltCaliMode(int nMode);
int   GetSysVoltCaliMode(void);

void  ResetGpsData(void);

//-----------------------------------------------------------------------------
#if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
void  SetNavBaseMapStartAddr(DWORD dStartAddr);
DWORD GetNavBaseMapStartAddr(void);
void  SetNavMapJobWindowNo(int nWinNo);
int   GetNavMapJobWindowNo(void);
void  SetNavMapImageWidth(int nWinNo,int nWidth);
int   GetNavMapImageWidth(int nWinNo);
void  SetNavMapScrnBaseX(int nWinNo,int nScrnX);
int   GetNavMapScrnBaseX(int nWinNo);
void  SetNavMapScrnBaseY(int nWinNo,int nScrnY);
int   GetNavMapScrnBaseY(int nWinNo);
void  SetNavMapScrnHoriDistance(int nWinNo,REAL rHoriDistInNM);
REAL  GetNavMapScrnHoriDistance(int nWinNo,int nDistUnit);
void  SetNavMapScrnVertDistance(int nWinNo,REAL rVertDistInNM);
REAL  GetNavMapScrnVertDistance(int nWinNo,int nDistUnit);
void  SetNavMapScrnLatDifference(int nWinNo,REAL rLatDiff);
REAL  GetNavMapScrnLatDifference(int nWinNo);
void  SetNavMapScrnLonDifference(int nWinNo,REAL rLonDiff);
REAL  GetNavMapScrnLonDifference(int nWinNo);
void  SetNavMapBrightValue(int nPercent);
HWORD *GetNavMapColorPalDataTable(void);

//-----------------------------------------------------------------------------
void  SetNavScrnRectValue(int nWinNo,int nMercY,int nMercX,int nZoomLevel,REAL rLatUp,REAL rLonLt,REAL rLatDn,REAL rLonRt);
int   CalcNvsImgPosByMRC32(MRC32 nLat,MRC32 nLon,int *pImgY,int *pImgX);
int   CalcNvsScrPosByMRC32(MRC32 nLat,MRC32 nLon,int *pImgY,int *pImgX);
//-----------------------------------------------------------------------------
INT32 RealToLatMerc(REAL rLat);
INT32 RealToLonMerc(REAL rLon,int nPositiveMode);

MRC32 RealToLatMRC32(REAL rLat);
MRC32 RealToLonMRC32(REAL rLon);
REAL  LatMRC32ToReal(MRC32 nLat);
REAL  LonMRC32ToReal(MRC32 nLon);
MRC32 DiffLatMRC32(MRC32 nLat1,MRC32 nLat2);
MRC32 DiffLonMRC32(MRC32 nLon1,MRC32 nLon2);
MRC32 LongLatToMRC32(INT32 nLat);
MRC32 LongLonToMRC32(INT32 nLon);
INT32 MRC32ToLongLat(MRC32 nLat);
INT32 MRC32ToLongLon(MRC32 nLon);
MRC32 MercLatToMRC32(INT32 nLat);
MRC32 MercLonToMRC32(INT32 nLon);
//-----------------------------------------------------------------------------
int   RealLatToNavLat(REAL rLat);
int   RealLonToNavLon(REAL rLon);
REAL  NavLatToRealLat(int nLat);
REAL  NavLonToRealLon(int nLon);
//-----------------------------------------------------------------------------
#endif

//-----------------------------------------------------------------------------
void  SetTideHarmonicStartAddr1(DWORD dStartAddr);
DWORD GetTideHarmonicStartAddr1(void);
void  SetTideHarmonicStartAddr3(DWORD dStartAddr);
DWORD GetTideHarmonicStartAddr3(void);
void  SetTideHarmonicStartAddr5(DWORD dStartAddr);
DWORD GetTideHarmonicStartAddr5(void);
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
int   IsPointInPolygon(xPOINT xTestPnt, int nSize, xPOINT *pAllPnts);
//-----------------------------------------------------------------------------

float DiffLatFloat(float fLat1, float fLat2);
float DiffLonFloat(float fLon1, float fLon2);
float GetDistanceByFLOAT(float fLat1, float fLon1, float fLat2, float fLon2, int nDistMode);

#ifdef  __cplusplus
}
#endif

#endif


