/*##################################################################
  FILE    : FS_STTAG.H

  USE     : Structured Tag Files Header file.
  PROJECT : C-Map's File System.

  AUTHOR  : SiS[060517].
  UPDATED :
  ##################################################################

*/

#ifndef __ST_TAGS__
	#define __ST_TAGS__


/************************************************************************
  #Include section.
 ************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include "CMapType.h"



/************************************************************************
  Constants definitions section.
 ************************************************************************/


/**
 * Structured tag file type enum.
 *
 * Values of this enumnerated type are to be used to set the value of the 
 * \c tag_ST_FILE_TYPE tag and their meaning is to identify the content of
 * the structured tag file.
 *
 * \ingroup	STTAGS
 */
typedef enum
{
	_STTAG_RESERVED = 0,		///< Generic structured tag file (Reserved).
	_STTAG_FFINDER,				///< Fish Finder structured tag file.
	_STTAG_LAST					///< Do not use it.

} STTAG_FILE_TYPE;



//////////////////////////////////////////////////////////////////////////



/**
 * Fish Finder presets ID.
 *
 * Elements of this enum are to be used to set the values of the two
 * \c tag_ST_SECTION_START and \c tag_ST_SECTION_END tags and their meaning 
 * is to identify the preset which will follow.
 */
typedef enum
{
	_STTAG_PRESET_GEN = 0,		///< Generic settings.
	_STTAG_PRESET_FISH,			///< FISH preset.
	_STTAG_PRESET_CRUISE,		///< CRUISE preset.
	_STTAG_SECTION_HEADER,		///< File header section.
	_STTAG_PRESET_LAST			///< Do not use it.

} STTAG_SECTID;



/************************************************************************
  Exported Routines prototypes.
 ************************************************************************/


#ifdef __cplusplus
extern "C" 
{
#endif /* __cplusplus */

PRE_EXPORT_H extern SWord IN_EXPORT_H FS_STTAG_SaveHeader ( SWord nFp, STTAG_FILE_TYPE iType );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_STTAG_LoadHeader ( SWord nFp, STTAG_FILE_TYPE iReqType );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_STTAG_ToNextSection ( SWord nFp, STTAG_SECTID *pSectionId );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_STTAG_ReadTag ( SWord nFp, lpTAG_FILE_DATA pData );

PRE_EXPORT_H extern SWord IN_EXPORT_H FS_STTAG_SectionBegin ( SWord nFp, STTAG_SECTID wSectionID );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_STTAG_SectionEnd ( SWord nFp );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_STTAG_WriteTag ( SWord nFp, lpTAG_FILE_DATA pData );


#ifdef __cplusplus
}
#endif /* __cplusplus */




/************************************************************************
  END of Code.
 ************************************************************************/

#endif /* __ST_TAGS__ */
