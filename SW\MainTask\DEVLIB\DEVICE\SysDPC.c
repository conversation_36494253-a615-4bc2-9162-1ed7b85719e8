/*...........................................................................*/
/*.                  File Name : SYSDPC.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysDPC.h"

#include <string.h>

//=============================================================================
static xSYS_DPC *G_pSysDPC = (xSYS_DPC *)(DPCP_PHSY_BASE_ADDR + 0x007c);
//=============================================================================

//=============================================================================
void  SysInitDPC(void)
{
      int   nDeviceType;
      DWORD dClkDivisor;
      DWORD dDpcFormat;
      HWORD wAVWidth,wHSW,wHFP,wHBP,wInvHSync;
      HWORD wAVHeight,wVSW,wVFP,wVBP,wInvVSync;
      HWORD wVSSOffset,wVSEOffset,wEVSSOffset,wEVSEOffset;
      HWORD wDelayRGB,wDelayHS,wDelayVS,wDelayDE,wDelayLP,wDelaySP,wDelayREV;

      nDeviceType = SysGetDeviceType();
      SysSetDPcClockPClkMode(DPC_PCLKMODE_ALWAYS);
      SysSetDPcEnableMode(0);                            // DPC disable
      SysSetDPcClockDivisorEnable(0);

      dClkDivisor = SysGetClockDivisorByDevice(nDeviceType);
      dDpcFormat  = SysGetFormatByDevice(nDeviceType);
      SysGetHSyncByDevice(nDeviceType,&wAVWidth,&wHSW,&wHFP,&wHBP,&wInvHSync);
      SysGetVSyncByDevice(nDeviceType,&wAVHeight,&wVSW,&wVFP,&wVBP,&wInvVSync);
      SysGetVSyncOffsetByDevice(nDeviceType,&wVSSOffset,&wVSEOffset,&wEVSSOffset,&wEVSEOffset);
      SysGetDelayByDevice(nDeviceType,&wDelayRGB,&wDelayHS,&wDelayVS,&wDelayDE,&wDelayLP,&wDelaySP,&wDelayREV);

      // VCLK2 : CLKGEN0
//    SysSetDPcClockSource(0,DPC_VCLK_SOURCE);           // CLKSRCSEL0 = PLL0
      SysSetDPcClockSource(0,SysGetVclkSourceByDevice(nDeviceType));
      SysSetDPcClockDivisor(0,dClkDivisor);              // CLKDIV
      SysSetDPcClockOutDelay(0,DPC_OUTCLKDELAY_0_0NS);   // OUTCLKDELAY
      SysSetDPcClockOutEnb(1);

      // VCLK : CLKGEN1
      SysSetDPcClockSource(1,DPC_VCLK2_SOURCE);          // CLKSRCSEL1 = CLKGEN0's Out
      SysSetDPcClockDivisor(1,DPC_VCLK2_DIVIDER - 1);    // VCLK = VCLK2
      SysSetDPcClockOutDelay(1,DPC_OUTCLKDELAY_0_0NS);   // OUTCLKDELAY

      int  nClkInvert = 0;

      if (SysGetNavisModelType() == NAVIS_TYPE_700)
          nClkInvert = 1;
      nClkInvert = 0;

  #if defined(__N500_MODEL__)
      nClkInvert = 0;
  #endif

      SysSetDpcMode(dDpcFormat,                          // RGB565
                    0,                                   // Progressive
                    0,                                   // Normal
                    1,                                   // RGB Mode
                    0,                                   // RGB
                    0,                                   // Cb Y Cr Y
                    0,                                   // Y/Cb/Cr=0~255
                    0,                                   // Disable Embedded-Sync
                    0,                                   // PAD Output Clock = VCLK
                    nClkInvert);                         // Normal(Falling Edge)

      SysSetDPcHSync(wAVWidth,wHSW,wHFP,wHBP,wInvHSync);
      SysSetDPcVSync(wAVHeight,wVSW,wVFP,wVBP,wInvVSync,1,1,1,1);
      SysSetDPcVSyncOffset(wVSSOffset,wVSEOffset,wEVSSOffset,wEVSEOffset);

      switch (dDpcFormat)
             {
              case DPC_FORMAT_RGB555   :
              case DPC_FORMAT_MRGB555A :
              case DPC_FORMAT_MRGB555B :
                   SysSetDPcDither(DPC_DITHER_5BIT,DPC_DITHER_5BIT,DPC_DITHER_5BIT);
                   break;
              case DPC_FORMAT_RGB565   :
              case DPC_FORMAT_MRGB565  :
                   SysSetDPcDither(DPC_DITHER_5BIT,DPC_DITHER_6BIT,DPC_DITHER_5BIT);
                   break;
              case DPC_FORMAT_RGB666:
              case DPC_FORMAT_MRGB666:
                   SysSetDPcDither(DPC_DITHER_6BIT,DPC_DITHER_6BIT,DPC_DITHER_6BIT);
                   break;
              default:
                   SysSetDPcDither(DPC_DITHER_BYPASS,DPC_DITHER_BYPASS,DPC_DITHER_BYPASS);
                   break;
             }

      SysSetDPcDelay(wDelayRGB,wDelayHS,wDelayVS,wDelayDE,wDelayLP,wDelaySP,wDelayREV);

      SysSetDPcEnableMode(1);                            // DPC enable
      SysSetDPcClockDivisorEnable(1);
}
void  SysSetDPcEnableMode(int nDisableEnableMode)
{
      HWORD wTempX;

      wTempX = G_pSysDPC->wCTRL0;

      wTempX &= ~(1 << 10);                // INTPEND clear
      if (nDisableEnableMode)
          wTempX |= (1 << 15);
      else
          wTempX &= ~(1 << 15);

      G_pSysDPC->wCTRL0 = wTempX;
}
int   SysGetDPcEnableMode(void)
{
      if (G_pSysDPC->wCTRL0 & (1 << 15))
          return(1);

      return(0);
}
void  SysSetDPcDelay(HWORD wDelayRGB,HWORD wDelayHS,HWORD wDelayVS,HWORD wDelayDE,HWORD wDelayLP,HWORD wDelaySP,HWORD wDelayREV)
{
      HWORD wTempX;

      wTempX = G_pSysDPC->wCTRL0;

      wTempX &= ~(1 << 10);                // INTPEND clear
      wTempX &= ~(0xf << 4);               // DELAYRGB
      wTempX = (wTempX | (wDelayRGB << 4));

      G_pSysDPC->wCTRL0 = wTempX;

      G_pSysDPC->wDELAY0 = ((wDelayDE << 8) | (wDelayVS << 4) | (wDelayHS << 0));
}
void  SysSetDPcDither(HWORD wDitherR,HWORD wDitherG,HWORD wDitherB)
{
      HWORD wTempX;

      wTempX = G_pSysDPC->wCTRL1;

      wTempX &= ~(0x3f << 0);
      wTempX = (wTempX | ((wDitherB << 4) | (wDitherG << 2) | (wDitherR << 0)));

      G_pSysDPC->wCTRL1 = wTempX;
}
void  SysSetDpcMode(HWORD wFormat,int nInterlace,int nInvertField,int nRGbMode,int nSwapRB,HWORD wYCorder,int nClipYC,int nEmbeddedSync,HWORD wClock,int nInvertClock)
{
      HWORD wTempX;

      wTempX = G_pSysDPC->wCTRL0;
      wTempX &= ~(1 << 10);                // INTPEND clear

      if (nInterlace)
          wTempX |= (1 <<  9);             // Interlaced  Scan Mode
      else
          wTempX &= ~(1 <<  9);            // Progressive Scan Mode

      if (nInvertField)
          wTempX |= (1 <<  2);             // Inversion(Low is Even-Field)
      else
          wTempX &= ~(1 <<  2);            // Normal   (Low is Odd-Field)

      if (nRGbMode)
          wTempX |= (1 << 12);             // RGB Mode
      else
          wTempX &= ~(1 << 12);            // YCbCr Mode

      if (nEmbeddedSync)
          wTempX |= (1 <<  8);             // Enable Embedded Sync
      else
          wTempX &= ~(1 <<  8);            // Disable Embedded Sync

      G_pSysDPC->wCTRL0 = wTempX;

      wTempX = G_pSysDPC->wCTRL1;
      wTempX &= (0x3f << 0);

      wTempX = (wTempX | (wYCorder <<  6));
      wTempX = (wTempX | (wFormat  <<  8));

      if (!nClipYC)  wTempX |= (1 << 13);
      if (nSwapRB)   wTempX |= (1 << 15);

      G_pSysDPC->wCTRL1 = wTempX;

      wTempX = G_pSysDPC->wCTRL2;
      wTempX &= ~(1 <<  8);                // LCDTYPE
      wTempX &= ~(1 <<  0);                // PADCLKSEL
      wTempX = (wTempX | (wClock << 0));

      G_pSysDPC->wCTRL2 = wTempX;

      // Determines whether invert or not the polarity of the pad clock.
      SysSetDPcClockOutInv(0,nInvertClock);
      SysSetDPcClockOutInv(1,nInvertClock);
}
void  SysSetDPcHSync(HWORD wAVWidth,HWORD wHSW,HWORD wHFP,HWORD wHBP,int nInvHSync)
{
      //	                   <---------------TOTAL----------------->
      // 	                   <-SW->
      //	   Sync  ---------+      +--------------/-/---------------+      +---
      //	                  |      |                                |      |
      //	                  +------+                                +------+
      //	            <-FP->        <-BP-> <--ACTIVE VIDEO-->
      //	  Active --+                    +-------/-/--------+
      //	  Video    |       (BLANK)      |  (ACTIVE DATA)   |      (BLANK)
      //	           +--------------------+                  +-----------------
      //	                   <---ASTART-->
      //	                   <-------------AEND------------->

      HWORD wTempX;

      G_pSysDPC->wHTOTAL  = (wHSW + wHBP + wAVWidth + wHFP - 1);
      G_pSysDPC->wHSWIDTH = (wHSW - 1);
      G_pSysDPC->wHASTART	= (wHSW + wHBP - 1);
      G_pSysDPC->wHAEND   = (wHSW + wHBP + wAVWidth - 1);

      wTempX = G_pSysDPC->wCTRL0;
      wTempX &= ~(1 << 10);                // INTPEND clear

      if (nInvHSync)
          wTempX |= (1 <<  0);             // High Active
      else
          wTempX &= ~(1 <<  0);            // Low  Active

      G_pSysDPC->wCTRL0 = wTempX;
}
void  SysSetDPcVSync(HWORD wAVHeight,HWORD wVSW,HWORD wVFP,HWORD wVBP,int nInvVSync,HWORD wEAVHeight,HWORD wEVSW,HWORD wEVFP,HWORD wEVBP)
{
      //	                   <---------------TOTAL----------------->
      // 	                   <-SW->
      //	   Sync  ---------+      +--------------/-/---------------+      +---
      //	                  |      |                                |      |
      //	                  +------+                                +------+
      //	            <-FP->        <-BP-> <--ACTIVE VIDEO-->
      //	  Active --+                    +-------/-/--------+
      //	  Video    |       (BLANK)      |  (ACTIVE DATA)   |      (BLANK)
      //	           +--------------------+                  +-----------------
      //	                   <---ASTART-->
      //	                   <-------------AEND------------->

      HWORD wTempX;

      G_pSysDPC->wVTOTAL   = (wVSW + wVBP + wAVHeight + wVFP - 1);
      G_pSysDPC->wVSWIDTH  = (wVSW - 1);
      G_pSysDPC->wVASTART  = (wVSW + wVBP - 1);
      G_pSysDPC->wVAEND    = (wVSW + wVBP + wAVHeight - 1);

      G_pSysDPC->wEVTOTAL  = (wEVSW + wEVBP + wEAVHeight + wEVFP - 1);
      G_pSysDPC->wEVSWIDTH = (wEVSW - 1);
      G_pSysDPC->wEVASTART = (wEVSW + wEVBP - 1);
      G_pSysDPC->wEVAEND   = (wEVSW + wEVBP + wEAVHeight - 1);

      wTempX = G_pSysDPC->wCTRL0;

      wTempX &= ~(1 << 10);                // INTPEND clear

      if (nInvVSync)
          wTempX |= (1 <<  1);             // High Active
      else
          wTempX &= ~(1 <<  1);            // Low  Active

      G_pSysDPC->wCTRL0 = wTempX;
}
void  SysSetDPcVSyncOffset(HWORD wVSSOffset,HWORD wVSEOffset,HWORD wEVSSOffset,HWORD wEVSEOffset)
{
      //	            <---HTOTAL--->
      //	  HSYNC  --+ +------------+ +-----/-/----+ +------------+ +-------------
      //	           | |            | |            | |            | |
      //	           +-+            +-+            +-+            +-+
      //
      //	  If VSSOffset == 0 and VSEOffset == 0 then
      //
      //	  VSYNC  -----------------+                             +---------------
      //	              (VFP)       |           (VSW)             |     (VBP)
      //	                          +------------/-/--------------+
      //
      //	  ,else
      //
      //	  VSYNC  -----------+                             +---------------------
      //	                    |<---> tVSSO                  |<---> tVSEO
      //	                    +-------------/-/-------------+
      //	            <------>                      <------>
      //	            VSSOffset                     VSEOffset
      //	         = HTOTAL - tVSSO              = HTOTAL - tVSEO

      G_pSysDPC->wVSEOFFSET  = wVSEOffset;
      G_pSysDPC->wVSSOFFSET  = wVSSOffset;
      G_pSysDPC->wEVSEOFFSET = wEVSEOffset;
      G_pSysDPC->wEVSSOFFSET = wEVSSOffset;
}
//-----------------------------------------------------------------------------
void  SysSetDPcClockPClkMode(DWORD dPclkMode)
{
      DWORD dTempX;
      DWORD dClkmode = 0;

      if (dPclkMode == DPC_PCLKMODE_DYNAMIC) dClkmode = 0;
      if (dPclkMode == DPC_PCLKMODE_ALWAYS)  dClkmode = 1;

      dTempX = G_pSysDPC->dCLKENB;

      dTempX &= ~(1 << 3);
      dTempX |= (dClkmode << 3);

      G_pSysDPC->dCLKENB = dTempX;
}
DWORD SysGetDPcClockPClkMode(void)
{
      if (G_pSysDPC->dCLKENB & (1 << 3))
          return(DPC_PCLKMODE_ALWAYS);

      return(DPC_PCLKMODE_DYNAMIC);
}
void  SysSetDPcClockSource(int nIndex,DWORD dClkSrc)
{
      DWORD dTempX;

      dTempX = G_pSysDPC->dCLKGEN[nIndex][0];

      dTempX &= ~(0x07 << 2);
      dTempX |= (dClkSrc << 2);

      G_pSysDPC->dCLKGEN[nIndex][0] = dTempX;
}
DWORD SysGetDPcClockSource(int nIndex)
{
      DWORD dTempX;

      dTempX = G_pSysDPC->dCLKGEN[nIndex][0];

      dTempX = dTempX >> 2;

      return(dTempX & 0x00000007);
}
void  SysSetDPcClockDivisor(int nIndex,DWORD dDivisor)
{
      DWORD dTempX;

      dDivisor &= 0x000000ff;
      dTempX = G_pSysDPC->dCLKGEN[nIndex][0];

      dTempX &= ~(0xff << 5);
      dTempX |= (dDivisor << 5);

      G_pSysDPC->dCLKGEN[nIndex][0] = dTempX;
}
DWORD SysGetDPcClockDivisor(int nIndex)
{
      DWORD dTempX;

      dTempX = G_pSysDPC->dCLKGEN[nIndex][0];

      dTempX = dTempX >> 5;

      return(dTempX & 0x000000ff);
}
void  SysSetDPcClockOutInv(int nIndex,int nOutClkInv)
{
      DWORD dTempX;

      dTempX = G_pSysDPC->dCLKGEN[nIndex][0];

      if (nOutClkInv)
          dTempX |= (1 << 1);              // OUTCLKINV Invert
      else
          dTempX &= ~(1 << 1);             // OUTCLKINV Normal

      G_pSysDPC->dCLKGEN[nIndex][0] = dTempX;
}
void  SysSetDPcClockOutEnb(int nOutClkEnb)
{
      DWORD dTempX;

      dTempX = G_pSysDPC->dCLKGEN[0][0];

      if (nOutClkEnb)
          dTempX &= ~(1 << 15);             // OUTCLKENB Enable
      else
          dTempX |= (1 << 15);              // OUTCLKENB Disable

      G_pSysDPC->dCLKGEN[0][0] = dTempX;
}
void  SysSetDPcClockDivisorEnable(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysDPC->dCLKENB;

      if (nDisableEnableMode)
          dTempX |= (1 <<  2);              // CLKGENENB Enable
      else
          dTempX &= ~(1 <<  2);             // CLKGENENB Disable

      G_pSysDPC->dCLKENB = dTempX;
}
void  SysSetDPcClockOutDelay(int nIndex,DWORD dDelay)
{
      DWORD dTempX;

      dDelay &= 0x0000001f;
      dTempX = G_pSysDPC->dCLKGEN[nIndex][1];

      dTempX &= ~(0x1f <<  0);
      dTempX |= (dDelay <<  0);

      G_pSysDPC->dCLKGEN[nIndex][1] = dTempX;
}
//=============================================================================
DWORD SysGetClockDivisorByDevice(int nDeviceType)
{
      DWORD dVclkFreq;

      nDeviceType = SysGetNavisModelType();

      dVclkFreq = SysGetVclkFrequency(nDeviceType);

  #if defined(__N500_MODEL__)
      return(dVclkFreq / DPC_N500_VCLK_FREQUENCY - 1);
  #endif

      if (nDeviceType == NAVIS_TYPE_330)
         {
          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   return(dVclkFreq / LCD_5100_VCLK_FREQUENCY - 1);
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   return(dVclkFreq / LCD_12_1_VCLK_FREQUENCY - 1);

          return(dVclkFreq / LCD_15_1_VCLK_FREQUENCY - 1);
         }

      if (nDeviceType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
//        return(dVclkFreq / LCD_12_1_VCLK_FREQUENCY - 1);
          return(dVclkFreq / DPC_12_X_VCLK_FREQUENCY - 1);

      if (nDeviceType == NAVIS_TYPE_800 ) return(dVclkFreq / DPC_800_VCLK_FREQUENCY  - 1);
      if (nDeviceType == NAVIS_TYPE_5100) return(dVclkFreq / DPC_5100_VCLK_FREQUENCY - 1);
      if (nDeviceType == NAVIS_TYPE_1200) return(dVclkFreq / DPC_12_1_VCLK_FREQUENCY - 1);
      if (nDeviceType == NAVIS_TYPE_3800) return(dVclkFreq / DPC_15_1_VCLK_FREQUENCY - 1);
      if (nDeviceType == NAVIS_TYPE_330 ) return(dVclkFreq / DPC_08_4_VCLK_FREQUENCY - 1);
      if (nDeviceType == NAVIS_TYPE_700 ) return(dVclkFreq / DPC_07_0_VCLK_FREQUENCY - 1);
      if (nDeviceType == NAVIS_TYPE_650 ) return(dVclkFreq / DPC_650_VCLK_FREQUENCY  - 1);

      return(dVclkFreq / DPC_650_VCLK_FREQUENCY - 1);
}
DWORD SysGetVclkSourceByDevice(int nDeviceType)
{
      return(DPC_CLKSRCSEL_PLL0);
}
DWORD SysGetVclkFrequency(int nDeviceType)
{
      DWORD dVclkSource;
      DWORD dVclkFrequency;

      dVclkSource = SysGetVclkSourceByDevice(nDeviceType);
      dVclkFrequency = CPU_PLL0_FREQUENCY;

      if (dVclkSource == DPC_CLKSRCSEL_PLL0)  dVclkFrequency = CPU_PLL0_FREQUENCY;
      if (dVclkSource == DPC_CLKSRCSEL_PLL1)  dVclkFrequency = CPU_PLL1_FREQUENCY;

      return(dVclkFrequency);
}
DWORD SysGetFormatByDevice(int nDeviceType)
{
      return(DPC_FORMAT_RGB565);
}
void  SysGetHSyncByDevice(int nDeviceType,HWORD *pAVWidth,HWORD *pHSW,HWORD *pHFP,HWORD *pHBP,HWORD *pInvHSync)
{
      nDeviceType = SysGetNavisModelType();

      *pAVWidth = SysGetScreenWidthByDevice(nDeviceType);

  #if defined(__N500_MODEL__)
     {*pHSW = DPC_N500_HSYNC_SWIDTH;*pHFP = DPC_N500_HSYNC_FRONT_PORCH;*pHBP = DPC_N500_HSYNC_BACK_PORCH;*pInvHSync = DPC_N500_HSYNC_ACTIVE_MODE;};
      return;
  #endif

      if (nDeviceType == NAVIS_TYPE_330)
         {
          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   {*pHSW = LCD_5100_HSYNC_SWIDTH;*pHFP = LCD_5100_HSYNC_FRONT_PORCH;*pHBP = LCD_5100_HSYNC_BACK_PORCH;*pInvHSync = LCD_5100_HSYNC_ACTIVE_MODE;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   {*pHSW = LCD_12_1_HSYNC_SWIDTH;*pHFP = LCD_12_1_HSYNC_FRONT_PORCH;*pHBP = LCD_12_1_HSYNC_BACK_PORCH;*pInvHSync = LCD_12_1_HSYNC_ACTIVE_MODE;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  {*pHSW = LCD_15_1_HSYNC_SWIDTH;*pHFP = LCD_15_1_HSYNC_FRONT_PORCH;*pHBP = LCD_15_1_HSYNC_BACK_PORCH;*pInvHSync = LCD_15_1_HSYNC_ACTIVE_MODE;};

          return;
         }

      if (nDeviceType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
//       {*pHSW = DPC_5100_HSYNC_SWIDTH;*pHFP = DPC_5100_HSYNC_FRONT_PORCH;*pHBP = DPC_5100_HSYNC_BACK_PORCH;*pInvHSync = DPC_5100_HSYNC_ACTIVE_MODE; return;};
         {*pHSW = DPC_12_X_HSYNC_SWIDTH;*pHFP = DPC_12_X_HSYNC_FRONT_PORCH;*pHBP = DPC_12_X_HSYNC_BACK_PORCH;*pInvHSync = DPC_12_X_HSYNC_ACTIVE_MODE; return;};

      if (nDeviceType == NAVIS_TYPE_800 ) {*pHSW = DPC_800_HSYNC_SWIDTH; *pHFP = DPC_800_HSYNC_FRONT_PORCH; *pHBP = DPC_800_HSYNC_BACK_PORCH; *pInvHSync = DPC_800_HSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_5100) {*pHSW = DPC_5100_HSYNC_SWIDTH;*pHFP = DPC_5100_HSYNC_FRONT_PORCH;*pHBP = DPC_5100_HSYNC_BACK_PORCH;*pInvHSync = DPC_5100_HSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_1200) {*pHSW = DPC_12_1_HSYNC_SWIDTH;*pHFP = DPC_12_1_HSYNC_FRONT_PORCH;*pHBP = DPC_12_1_HSYNC_BACK_PORCH;*pInvHSync = DPC_12_1_HSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_3800) {*pHSW = DPC_15_1_HSYNC_SWIDTH;*pHFP = DPC_15_1_HSYNC_FRONT_PORCH;*pHBP = DPC_15_1_HSYNC_BACK_PORCH;*pInvHSync = DPC_15_1_HSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_330)  {*pHSW = DPC_12_1_HSYNC_SWIDTH;*pHFP = DPC_12_1_HSYNC_FRONT_PORCH;*pHBP = DPC_12_1_HSYNC_BACK_PORCH;*pInvHSync = DPC_12_1_HSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_700)  {*pHSW = DPC_07_0_HSYNC_SWIDTH;*pHFP = DPC_07_0_HSYNC_FRONT_PORCH;*pHBP = DPC_07_0_HSYNC_BACK_PORCH;*pInvHSync = DPC_07_0_HSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_650 ) {*pHSW = DPC_650_HSYNC_SWIDTH; *pHFP = DPC_650_HSYNC_FRONT_PORCH; *pHBP = DPC_650_HSYNC_BACK_PORCH; *pInvHSync = DPC_650_HSYNC_ACTIVE_MODE;};
}
void  SysGetVSyncByDevice(int nDeviceType,HWORD *pAVHeight,HWORD *pVSW,HWORD *pVFP,HWORD *pVBP,HWORD *pInvVSync)
{
      nDeviceType = SysGetNavisModelType();

      *pAVHeight = SysGetScreenHeightByDevice(nDeviceType);

  #if defined(__N500_MODEL__)
     {*pVSW = DPC_N500_VSYNC_SWIDTH;*pVFP = DPC_N500_VSYNC_FRONT_PORCH;*pVBP = DPC_N500_VSYNC_BACK_PORCH;*pInvVSync = DPC_N500_VSYNC_ACTIVE_MODE;};
      return;
  #endif

      if (nDeviceType == NAVIS_TYPE_330)
         {
          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   {*pVSW = LCD_5100_VSYNC_SWIDTH;*pVFP = LCD_5100_VSYNC_FRONT_PORCH;*pVBP = LCD_5100_VSYNC_BACK_PORCH;*pInvVSync = LCD_5100_VSYNC_ACTIVE_MODE;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   {*pVSW = LCD_12_1_VSYNC_SWIDTH;*pVFP = LCD_12_1_VSYNC_FRONT_PORCH;*pVBP = LCD_12_1_VSYNC_BACK_PORCH;*pInvVSync = LCD_12_1_VSYNC_ACTIVE_MODE;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  {*pVSW = LCD_15_1_VSYNC_SWIDTH;*pVFP = LCD_15_1_VSYNC_FRONT_PORCH;*pVBP = LCD_15_1_VSYNC_BACK_PORCH;*pInvVSync = LCD_15_1_VSYNC_ACTIVE_MODE;};

          return;
         }

      if (nDeviceType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
//       {*pVSW = DPC_06_5_VSYNC_SWIDTH;*pVFP = DPC_06_5_VSYNC_FRONT_PORCH;*pVBP = DPC_06_5_VSYNC_BACK_PORCH;*pInvVSync = DPC_06_5_VSYNC_ACTIVE_MODE; return;};
         {*pVSW = DPC_12_X_VSYNC_SWIDTH;*pVFP = DPC_12_X_VSYNC_FRONT_PORCH;*pVBP = DPC_12_X_VSYNC_BACK_PORCH;*pInvVSync = DPC_12_X_VSYNC_ACTIVE_MODE; return;};

      if (nDeviceType == NAVIS_TYPE_800 ) {*pVSW = DPC_06_5_VSYNC_SWIDTH;*pVFP = DPC_06_5_VSYNC_FRONT_PORCH;*pVBP = DPC_06_5_VSYNC_BACK_PORCH;*pInvVSync = DPC_06_5_VSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_5100) {*pVSW = DPC_06_5_VSYNC_SWIDTH;*pVFP = DPC_06_5_VSYNC_FRONT_PORCH;*pVBP = DPC_06_5_VSYNC_BACK_PORCH;*pInvVSync = DPC_06_5_VSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_1200) {*pVSW = DPC_12_1_VSYNC_SWIDTH;*pVFP = DPC_12_1_VSYNC_FRONT_PORCH;*pVBP = DPC_12_1_VSYNC_BACK_PORCH;*pInvVSync = DPC_12_1_VSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_3800) {*pVSW = DPC_15_1_VSYNC_SWIDTH;*pVFP = DPC_15_1_VSYNC_FRONT_PORCH;*pVBP = DPC_15_1_VSYNC_BACK_PORCH;*pInvVSync = DPC_15_1_VSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_330)  {*pVSW = DPC_12_1_VSYNC_SWIDTH;*pVFP = DPC_12_1_VSYNC_FRONT_PORCH;*pVBP = DPC_12_1_VSYNC_BACK_PORCH;*pInvVSync = DPC_12_1_VSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_700)  {*pVSW = DPC_07_0_VSYNC_SWIDTH;*pVFP = DPC_07_0_VSYNC_FRONT_PORCH;*pVBP = DPC_07_0_VSYNC_BACK_PORCH;*pInvVSync = DPC_07_0_VSYNC_ACTIVE_MODE;};
      if (nDeviceType == NAVIS_TYPE_650 ) {*pVSW = DPC_06_5_VSYNC_SWIDTH;*pVFP = DPC_06_5_VSYNC_FRONT_PORCH;*pVBP = DPC_06_5_VSYNC_BACK_PORCH;*pInvVSync = DPC_06_5_VSYNC_ACTIVE_MODE;};
}
//=============================================================================
void  SysGetVSyncOffsetByDevice(int nDeviceType,HWORD *pVSSOffset,HWORD *pVSEOffset,HWORD *pEVSSOffset,HWORD *pEVSEOffset)
{
      nDeviceType = SysGetNavisModelType();

  #if defined(__N500_MODEL__)
     {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
      return;
  #endif

      if (nDeviceType == NAVIS_TYPE_330)
         {
          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};

          return;
         }

      if (nDeviceType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
         {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1; return;};

      if (nDeviceType == NAVIS_TYPE_800 ) {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
      if (nDeviceType == NAVIS_TYPE_5100) {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
      if (nDeviceType == NAVIS_TYPE_1200) {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
      if (nDeviceType == NAVIS_TYPE_3800) {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
      if (nDeviceType == NAVIS_TYPE_330)  {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
      if (nDeviceType == NAVIS_TYPE_700)  {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
      if (nDeviceType == NAVIS_TYPE_650 ) {*pVSSOffset = 1;*pVSEOffset = 1;*pEVSSOffset = 1;*pEVSEOffset = 1;};
}
void  SysGetDelayByDevice(int nDeviceType,HWORD *pDelayRGB,HWORD *pDelayHS,HWORD *pDelayVS,HWORD *pDelayDE,HWORD *pDelayLP,HWORD *pDelaySP,HWORD *pDelayREV)
{
      nDeviceType = SysGetNavisModelType();

  #if defined(__N500_MODEL__)
     {*pDelayRGB = DPC_N500_DELAY_RGB;*pDelayHS = DPC_N500_DELAY_HS;*pDelayVS = DPC_N500_DELAY_VS;*pDelayDE = DPC_N500_DELAY_DE;*pDelayLP = DPC_N500_DELAY_LP;*pDelaySP = DPC_N500_DELAY_SP;*pDelayREV = DPC_N500_DELAY_REV;};
      return;
  #endif

      if (nDeviceType == NAVIS_TYPE_330)
         {
          int   nSGP330ScrnMode = SysGetSGP330ScrnMode();

          if (nSGP330ScrnMode == SGP330_SCRN_MODE_640x480)   {*pDelayRGB = LCD_5100_DELAY_RGB;*pDelayHS = LCD_5100_DELAY_HS;*pDelayVS = LCD_5100_DELAY_VS;*pDelayDE = LCD_5100_DELAY_DE;*pDelayLP = LCD_5100_DELAY_LP;*pDelaySP = LCD_5100_DELAY_SP;*pDelayREV = LCD_5100_DELAY_REV;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_800x600)   {*pDelayRGB = LCD_12_1_DELAY_RGB;*pDelayHS = LCD_12_1_DELAY_HS;*pDelayVS = LCD_12_1_DELAY_VS;*pDelayDE = LCD_12_1_DELAY_DE;*pDelayLP = LCD_12_1_DELAY_LP;*pDelaySP = LCD_12_1_DELAY_SP;*pDelayREV = LCD_12_1_DELAY_REV;};
          if (nSGP330ScrnMode == SGP330_SCRN_MODE_1024x768)  {*pDelayRGB = LCD_15_1_DELAY_RGB;*pDelayHS = LCD_15_1_DELAY_HS;*pDelayVS = LCD_15_1_DELAY_VS;*pDelayDE = LCD_15_1_DELAY_DE;*pDelayLP = LCD_15_1_DELAY_LP;*pDelaySP = LCD_15_1_DELAY_SP;*pDelayREV = LCD_15_1_DELAY_REV;};

          return;
         }

      if (nDeviceType == NAVIS_TYPE_5100 && SysGetNavis5100AModelMode())
//       {*pDelayRGB = DPC_06_5_DELAY_RGB;*pDelayHS = DPC_06_5_DELAY_HS;*pDelayVS = DPC_06_5_DELAY_VS;*pDelayDE = DPC_06_5_DELAY_DE;*pDelayLP = DPC_06_5_DELAY_LP;*pDelaySP = DPC_06_5_DELAY_SP;*pDelayREV = DPC_06_5_DELAY_REV; return;};
         {*pDelayRGB = DPC_12_X_DELAY_RGB;*pDelayHS = DPC_12_X_DELAY_HS;*pDelayVS = DPC_12_X_DELAY_VS;*pDelayDE = DPC_12_X_DELAY_DE;*pDelayLP = DPC_12_X_DELAY_LP;*pDelaySP = DPC_12_X_DELAY_SP;*pDelayREV = DPC_12_X_DELAY_REV; return;};

      if (nDeviceType == NAVIS_TYPE_800 ) {*pDelayRGB = DPC_06_5_DELAY_RGB;*pDelayHS = DPC_06_5_DELAY_HS;*pDelayVS = DPC_06_5_DELAY_VS;*pDelayDE = DPC_06_5_DELAY_DE;*pDelayLP = DPC_06_5_DELAY_LP;*pDelaySP = DPC_06_5_DELAY_SP;*pDelayREV = DPC_06_5_DELAY_REV;};
      if (nDeviceType == NAVIS_TYPE_5100) {*pDelayRGB = DPC_06_5_DELAY_RGB;*pDelayHS = DPC_06_5_DELAY_HS;*pDelayVS = DPC_06_5_DELAY_VS;*pDelayDE = DPC_06_5_DELAY_DE;*pDelayLP = DPC_06_5_DELAY_LP;*pDelaySP = DPC_06_5_DELAY_SP;*pDelayREV = DPC_06_5_DELAY_REV;};
      if (nDeviceType == NAVIS_TYPE_1200) {*pDelayRGB = DPC_12_1_DELAY_RGB;*pDelayHS = DPC_12_1_DELAY_HS;*pDelayVS = DPC_12_1_DELAY_VS;*pDelayDE = DPC_12_1_DELAY_DE;*pDelayLP = DPC_12_1_DELAY_LP;*pDelaySP = DPC_12_1_DELAY_SP;*pDelayREV = DPC_12_1_DELAY_REV;};
      if (nDeviceType == NAVIS_TYPE_3800) {*pDelayRGB = DPC_15_1_DELAY_RGB;*pDelayHS = DPC_15_1_DELAY_HS;*pDelayVS = DPC_15_1_DELAY_VS;*pDelayDE = DPC_15_1_DELAY_DE;*pDelayLP = DPC_15_1_DELAY_LP;*pDelaySP = DPC_15_1_DELAY_SP;*pDelayREV = DPC_15_1_DELAY_REV;};
      if (nDeviceType == NAVIS_TYPE_330)  {*pDelayRGB = DPC_12_1_DELAY_RGB;*pDelayHS = DPC_12_1_DELAY_HS;*pDelayVS = DPC_12_1_DELAY_VS;*pDelayDE = DPC_12_1_DELAY_DE;*pDelayLP = DPC_12_1_DELAY_LP;*pDelaySP = DPC_12_1_DELAY_SP;*pDelayREV = DPC_12_1_DELAY_REV;};
      if (nDeviceType == NAVIS_TYPE_700)  {*pDelayRGB = DPC_07_0_DELAY_RGB;*pDelayHS = DPC_07_0_DELAY_HS;*pDelayVS = DPC_07_0_DELAY_VS;*pDelayDE = DPC_07_0_DELAY_DE;*pDelayLP = DPC_07_0_DELAY_LP;*pDelaySP = DPC_07_0_DELAY_SP;*pDelayREV = DPC_07_0_DELAY_REV;};
      if (nDeviceType == NAVIS_TYPE_650 ) {*pDelayRGB = DPC_06_5_DELAY_RGB;*pDelayHS = DPC_06_5_DELAY_HS;*pDelayVS = DPC_06_5_DELAY_VS;*pDelayDE = DPC_06_5_DELAY_DE;*pDelayLP = DPC_06_5_DELAY_LP;*pDelaySP = DPC_06_5_DELAY_SP;*pDelayREV = DPC_06_5_DELAY_REV;};
}

