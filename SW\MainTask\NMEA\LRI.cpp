#include <stdio.h>
#include "LRI.hpp"

CLri::CLri() : CSentence()
{
}
    
CLri::CLri(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CLri::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_LRI;
}

/******************************************************************************
*
* LRI - Long Range Interrogation
*
* $--LRI,x,a,xxxxxxxxx,xxxxxxxxx,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a*hh<CR><LF>
*        | | |         |         |       | |        | |       | |        |
*        1 2 3         4         5       6 7        8 9      10 11       12
*
* 1.     Sequence Number , 0 to 9
* 2.     Control Flag
* 3.     MMSI of "requester"
* 4.     MMSI of "destination"
* 5.6.   Latitude  - N/S (north-east co-ordinate)
* 7.8.   Longitude - E/W (north-east co-ordinate)
* 9.10.  Latitude  - N/S (south-west co-ordinate)
* 11.12. Longitude - E/W (south-west co-ordinate)
*
******************************************************************************/
void CLri::Parse()
{
	m_nSeqNumber     = GetFieldInteger(1);
	m_chControlFlag  = GetFieldChar(2);
	m_dwMMSIReq      = GetFieldMMSI(3);
	m_dwMMSIDest     = GetFieldMMSI(4);
	m_nLatNE         = GetFieldLat(5);
	m_nLonNE         = GetFieldLon(7);
	m_nLatSW         = GetFieldLat(9);
	m_nLonSW         = GetFieldLon(11);
}

void CLri::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}

/* Modify LRI sentence element : HSI 2012.05.01 */
int CLri::MakeSentence(BYTE *pszSentence)
{

	/*
	sprintf(pszSentence, "$AILRI,%d,%c,%09d,%09d,0000.0,N,00000.0,E,0000.0,N,00000.0,E",
		m_nSeqNumber, m_chControlFlag, m_dwMMSIReq, m_dwMMSIDest);

	CSentence::SendMakeNmeaCsData(pszSentence);
	*/

	int checksum;
	int strleng;

	strncpy((char *)pszSentence,"$SY",3);
	checksum = CSentence::ComputeChecksum((char *)pszSentence);

	strleng = strlen((char *)pszSentence) - 2;
	pszSentence[strleng++] = NULL;
	pszSentence[strleng++] = NULL;
	//pszSentence[strleng++] = NULL;
	//pszSentence[strleng++] = NULL;
	sprintf((char *)pszSentence, "%s%02X\x0D\x0A", pszSentence, checksum);

	//CSentence::SendMakeNmeaCsData(pszSentence);
	return strlen((char *)pszSentence);

}
