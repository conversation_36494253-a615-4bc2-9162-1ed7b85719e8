/*...........................................................................*/
/*.                  File Name : SYSMLC.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysMLC.h"

#include <string.h>

//=============================================================================
static xSYS_MLC *G_pSysMLC = (xSYS_MLC *)MLCP_PHSY_BASE_ADDR;
//=============================================================================

//=============================================================================
#define  MLC_TOP_DIRTY          (1 << 3)
#define  MLC_LAY_DIRTY          (1 << 4)
//=============================================================================

//=============================================================================
void  SysInitMLC(void)
{
      DWORD dScrnWidth;
      DWORD dScrnHeight;

      dScrnWidth = SysGetScreenWidth();
      dScrnHeight= SysGetScreenHeight();

      SysSetMLcClockPClkMode(MLC_PCLKMODE_ALWAYS);
      SysSetMLcClockBClkMode(MLC_BCLKMODE_ALWAYS);

      //----------------------------------------------------------------------
      // PRIMARY RGB Layer TOP Field
      //----------------------------------------------------------------------
      // MES_MLC_SetTop3DAddrChangeSync( ModuleIndex, MES_MLC_3DSYNC_PRIMARY );  
      SysSetMLcPriority(MLC_LAYER_PRIORITY,1);
      SysSetMLcTopPowerMode(1);
      SysSetMLcTopSleepMode(0);

      SysSetMLcFieldEnable(0,0);
      SysSetMLcScreenSize(dScrnWidth,dScrnHeight);
      SysSetMLcBackground(MLC_BACKGROUND_COLOR);
      SysSetMLcEnableMode(1,0);
      SysSetMLcTopDirtyFlag();

      //----------------------------------------------------------------------
      // PRIMARY RGB Layer SCREEN Field
      //----------------------------------------------------------------------
      SysSetMLcAlphaBlending(MEMU_LAYER_NO ,1,15);
      SysSetMLcAlphaBlending(CHART_LAYER_NO,0,15);
      SysSetMLcAlphaBlending(RADAR_LAYER_NO,1,15);
      SysSetMLcAlphaBlending(VIDEO_LAYER_NO,0,15);

      SysSetMLcTransparency(MEMU_LAYER_NO ,1,SysGetMLcExtendedColor(MLC_TRANSPARENCY_COLOR,MENU_LAYER_FORMAT));
      SysSetMLcTransparency(CHART_LAYER_NO,0,SysGetMLcExtendedColor(MLC_TRANSPARENCY_COLOR,CHART_LAYER_FORMAT));
      SysSetMLcTransparency(RADAR_LAYER_NO,1,SysGetMLcExtendedColor(MLC_TRANSPARENCY_COLOR,RADAR_LAYER_FORMAT));
      SysSetMLcTransparency(VIDEO_LAYER_NO,0,SysGetMLcExtendedColor(MLC_TRANSPARENCY_COLOR,VIDEO_LAYER_FORMAT));

      SysSetMLcColorInversion(MEMU_LAYER_NO ,0,SysGetMLcExtendedColor(MLC_INVERSION_COLOR,MENU_LAYER_FORMAT));
      SysSetMLcColorInversion(CHART_LAYER_NO,0,SysGetMLcExtendedColor(MLC_INVERSION_COLOR,CHART_LAYER_FORMAT));
      SysSetMLcColorInversion(RADAR_LAYER_NO,0,SysGetMLcExtendedColor(MLC_INVERSION_COLOR,RADAR_LAYER_FORMAT));
      SysSetMLcColorInversion(VIDEO_LAYER_NO,0,SysGetMLcExtendedColor(MLC_INVERSION_COLOR,VIDEO_LAYER_FORMAT));

      SysSetMLcLockSize(MEMU_LAYER_NO ,16);
      SysSetMLcLockSize(CHART_LAYER_NO,16);
      SysSetMLcLockSize(RADAR_LAYER_NO,16);
      SysSetMLcLockSize(VIDEO_LAYER_NO,16);

      SysSetMLc3DEnableMode(MEMU_LAYER_NO ,0);
      SysSetMLc3DEnableMode(CHART_LAYER_NO,0);
      SysSetMLc3DEnableMode(RADAR_LAYER_NO,0);
      SysSetMLc3DEnableMode(VIDEO_LAYER_NO,0);

      SysSetMLcPalettePowerMode(MEMU_LAYER_NO ,1);
      SysSetMLcPalettePowerMode(CHART_LAYER_NO,1);
//    SysSetMLcPalettePowerMode(RADAR_LAYER_NO,1);
//    SysSetMLcPalettePowerMode(VIDEO_LAYER_NO,1);
      SysSetMLcYUvLayerLineBufferPowerMode(1);

      SysSetMLcPaletteSleepMode(MEMU_LAYER_NO ,0);
      SysSetMLcPaletteSleepMode(CHART_LAYER_NO,0);
//    SysSetMLcPaletteSleepMode(RADAR_LAYER_NO,0);
//    SysSetMLcPaletteSleepMode(VIDEO_LAYER_NO,0);
      SysSetMLcYUvLayerLineBufferSleepMode(0);

      SysSetMLcRGbFormat(MEMU_LAYER_NO ,MENU_LAYER_FORMAT);
      SysSetMLcRGbFormat(CHART_LAYER_NO,CHART_LAYER_FORMAT);
      SysSetMLcRGbFormat(RADAR_LAYER_NO,RADAR_LAYER_FORMAT);
      SysSetMLcYUvFormat(MLC_YUVFMT_YUYV);

      SysSetMLcPosition(MEMU_LAYER_NO ,  0,  0,dScrnWidth - 1,dScrnHeight - 1);
      SysSetMLcPosition(CHART_LAYER_NO,  0,  0,dScrnWidth - 1,dScrnHeight - 1);
      SysSetMLcPosition(RADAR_LAYER_NO,  0,  0,dScrnWidth - 1,dScrnHeight - 1);
      SysSetMLcPosition(VIDEO_LAYER_NO,  0,  0,dScrnWidth - 1,dScrnHeight - 1);

      SysSetMLcRGbLayerInvalidPosition(MEMU_LAYER_NO ,0,  0,  0,  0,  0,0);
      SysSetMLcRGbLayerInvalidPosition(MEMU_LAYER_NO ,1,  0,  0,  0,  0,0);
      SysSetMLcRGbLayerInvalidPosition(CHART_LAYER_NO,0,  0,  0,  0,  0,0);
      SysSetMLcRGbLayerInvalidPosition(CHART_LAYER_NO,1,  0,  0,  0,  0,0);
      SysSetMLcRGbLayerInvalidPosition(RADAR_LAYER_NO,0,  0,  0,  0,  0,0);
      SysSetMLcRGbLayerInvalidPosition(RADAR_LAYER_NO,1,  0,  0,  0,  0,0);
      SysSetMLcRGbLayerInvalidPosition(VIDEO_LAYER_NO,0,  0,  0,  0,  0,0);
      SysSetMLcRGbLayerInvalidPosition(VIDEO_LAYER_NO,1,  0,  0,  0,  0,0);

      SysSetMLcRGbLayerStride(MEMU_LAYER_NO ,MENU_SCRN_RGB_HORI_STRIDE ,dScrnWidth * MENU_SCRN_RGB_HORI_STRIDE);
      SysSetMLcRGbLayerStride(CHART_LAYER_NO,CHART_SCRN_RGB_HORI_STRIDE,dScrnWidth * CHART_SCRN_RGB_HORI_STRIDE);
      SysSetMLcRGbLayerStride(RADAR_LAYER_NO,RADAR_SCRN_RGB_HORI_STRIDE,dScrnWidth * RADAR_SCRN_RGB_HORI_STRIDE);
//    SysSetMLcYUvLayerStride(SCRN_YUV_Y_STRIDE,SCRN_YUV_CR_STRIDE,SCRN_YUV_CB_STRIDE);
      SysSetMLcYUvLayerAddressYUYV(VID_LAYER_Y_BASE_ADDR,SCRN_YUV_CB_STRIDE);

      SysSetMLcRGbLayerAddress(MLC_RGB_LAYER0,RGB_LAYER0_BASE_ADDR);
      SysSetMLcRGbLayerAddress(MLC_RGB_LAYER1,RGB_LAYER1_BASE_ADDR);
      SysSetMLcRGbLayerAddress(MLC_RGB_LAYER2,RGB_LAYER2_BASE_ADDR);
//    SysSetMLcYUvLayerAddress(VID_LAYER_Y_BASE_ADDR,VID_LAYER_CB_BASE_ADDR,VID_LAYER_CR_BASE_ADDR);
      SysSetMLcYUvLayerAddressYUYV(VID_LAYER_Y_BASE_ADDR,SCRN_YUV_CB_STRIDE);

      SysSetMLcLayerEnableMode(MEMU_LAYER_NO ,1);
      SysSetMLcLayerEnableMode(CHART_LAYER_NO,1);
      SysSetMLcLayerEnableMode(RADAR_LAYER_NO,1);
      SysSetMLcLayerEnableMode(VIDEO_LAYER_NO,0);

      SysSetMLcLayDirtyFlag(MEMU_LAYER_NO);
      SysSetMLcLayDirtyFlag(CHART_LAYER_NO);
      SysSetMLcLayDirtyFlag(RADAR_LAYER_NO);
      SysSetMLcLayDirtyFlag(VIDEO_LAYER_NO);

      SysSetMLcTopDirtyFlag();
}
void  SysSetMLcTopDirtyFlag(void)
{
      G_pSysMLC->dCONTROLT |= MLC_TOP_DIRTY;
	  while (SysGetMLcTopDirtyFlag());
}
int   SysGetMLcTopDirtyFlag(void)
{
      if (G_pSysMLC->dCONTROLT & MLC_TOP_DIRTY)
          return(1);

      return(0);
}
void	SysSetMLcEnableMode(int nDisableEnableMode,int nApplyNow)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dCONTROLT;

      dTempX = dTempX & ~(1 <<  1);
      dTempX = dTempX & ~MLC_TOP_DIRTY;
      dTempX = dTempX |  (nDisableEnableMode <<  1);

      G_pSysMLC->dCONTROLT = dTempX;

      if (nApplyNow)
          SysSetMLcTopDirtyFlag();
}
int   SysGetMLcEnableMode(void)
{
      if (G_pSysMLC->dCONTROLT & (1 << 1))
          return(1);

      return(0);
}
void  SysSetMLcFieldEnable(int nDisableEnableMode,int nApplyNow)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dCONTROLT;

      dTempX = dTempX & ~(1 <<  0);
      dTempX = dTempX & ~MLC_TOP_DIRTY;
      dTempX = dTempX |  (nDisableEnableMode <<  0);

      G_pSysMLC->dCONTROLT = dTempX;

      if (nApplyNow)
          SysSetMLcTopDirtyFlag();
}
int   SysGetMLcFieldEnable(void)
{
      if (G_pSysMLC->dCONTROLT & (1 << 0))
          return(1);

      return(0);
}
void  SysSetMLcPriority(DWORD dPriority,int nApplyNow)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dCONTROLT;

      dTempX = dTempX & ~(3 <<  8);
      dTempX = dTempX & ~MLC_TOP_DIRTY;
      dTempX = dTempX |  (dPriority <<  8);

      G_pSysMLC->dCONTROLT = dTempX;

      if (nApplyNow)
          SysSetMLcTopDirtyFlag();
}
void	SysSetMLcScreenSize(DWORD dScrWidth,DWORD dScrHeight)
{
      G_pSysMLC->dSCREENSIZE = ((dScrHeight -1) << 16) | (dScrWidth - 1);
}
void	SysSetMLcBackground(DWORD dColor)
{
      G_pSysMLC->dBGCOLOR = dColor;
}
void  SysSetMLcTopPowerMode(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dCONTROLT;

      dTempX = dTempX & ~(1 << 11);
      dTempX = dTempX & ~MLC_TOP_DIRTY;
      if (nDisableEnableMode)
          dTempX = dTempX | (1 << 11);

      G_pSysMLC->dCONTROLT = dTempX;
}
void  SysSetMLcTopSleepMode(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dCONTROLT;

      dTempX = dTempX & ~(1 << 10);
      dTempX = dTempX & ~MLC_TOP_DIRTY;
      if (nDisableEnableMode == 0)
          dTempX = dTempX | (1 << 10);

      G_pSysMLC->dCONTROLT = dTempX;
}
void  SysSetMLcClockPClkMode(DWORD dPClkMode)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dCLKENB;

      dTempX = dTempX & ~MLC_TOP_DIRTY;
      dTempX = dTempX & ~(1 <<  3);
      dTempX = dTempX |  (dPClkMode <<  3);

      G_pSysMLC->dCLKENB = dTempX;
}
void  SysSetMLcClockBClkMode(DWORD dBClkMode)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dCLKENB;

      dTempX = dTempX & ~(0x03 <<  0);
      dTempX = dTempX |  (dBClkMode <<  0);

      G_pSysMLC->dCLKENB = dTempX;
}
//=============================================================================
void 	SysSetMLcLayDirtyFlag(int nLayer)
{
      if (nLayer == MLC_VID_LAYER)
          G_pSysMLC->xVideoLayer.dCONTROL |= MLC_LAY_DIRTY;
      else
          if (nLayer == MLC_RGB_LAYER2)
              G_pSysMLC->xRGB2Layer.dCONTROL |= MLC_LAY_DIRTY;
          else
              G_pSysMLC->xRGB1Layer[nLayer].dCONTROL |= MLC_LAY_DIRTY;

      while (SysGetMLcLayDirtyFlag(nLayer));
}
int   SysGetMLcLayDirtyFlag(int nLayer)
{
      if (nLayer == MLC_VID_LAYER)
         {
          if (G_pSysMLC->xVideoLayer.dCONTROL & MLC_LAY_DIRTY)
              return(1);
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
             {
              if (G_pSysMLC->xRGB2Layer.dCONTROL & MLC_LAY_DIRTY)
                  return(1);
             }
          else
             {
              if (G_pSysMLC->xRGB1Layer[nLayer].dCONTROL & MLC_LAY_DIRTY)
                  return(1);
             }
         }

      return(0);
}
void  SysSetMLcPalettePowerMode(int nLayer,int nDisableEnableMode)
{
}
int   SysGetMLcPalettePowerMode(int nLayer)
{
      return(0);
}
void  SysSetMLcPaletteSleepMode(int nLayer,int nDisableEnableMode)
{
}
int   SysGetMLcPaletteSleepMode(int nLayer)
{
      return(0);
}
void  SysSetMLcLayerEnableMode(int nLayer,int nDisableEnableMode)
{
      if (nDisableEnableMode)
         {
          if (nLayer == MLC_VID_LAYER)
              G_pSysMLC->xVideoLayer.dCONTROL |= (1 << 5);
          else
              if (nLayer == MLC_RGB_LAYER2)
                  G_pSysMLC->xRGB2Layer.dCONTROL |= (1 << 5);
              else
                  G_pSysMLC->xRGB1Layer[nLayer].dCONTROL |= (1 << 5);
         }
      else
         {
          if (nLayer == MLC_VID_LAYER)
              G_pSysMLC->xVideoLayer.dCONTROL &= ~(1 << 5);
          else
              if (nLayer == MLC_RGB_LAYER2)
                  G_pSysMLC->xRGB2Layer.dCONTROL &= ~(1 << 5);
              else
                  G_pSysMLC->xRGB1Layer[nLayer].dCONTROL &= ~(1 << 5);
         }
}
int   SysGetMLcLayerEnableMode(int nLayer)
{
      if (nLayer == MLC_VID_LAYER)
         {
          if (G_pSysMLC->xVideoLayer.dCONTROL & (1 << 5))
              return(1);
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
             {
              if (G_pSysMLC->xRGB2Layer.dCONTROL & (1 << 5))
                  return(1);
             }
          else
             {
              if (G_pSysMLC->xRGB1Layer[nLayer].dCONTROL & (1 << 5))
                  return(1);
             }
         }

      return(0);
}
void  SysSetMLcLockSize(int nLayer,int nLockSize)
{
      DWORD dTempX;
      DWORD dLockSize;

      dLockSize = 0;
      if (nLockSize ==  4)  dLockSize = 0;
      if (nLockSize ==  8)  dLockSize = 1;
      if (nLockSize == 16)  dLockSize = 2;

      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
              dTempX = G_pSysMLC->xRGB2Layer.dCONTROL;
          else
              dTempX = G_pSysMLC->xRGB1Layer[nLayer].dCONTROL;
         }

      dTempX = dTempX & ~MLC_LAY_DIRTY;
      dTempX = dTempX & ~(3 << 12);
      dTempX = dTempX |  (dLockSize << 12);

      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
              G_pSysMLC->xRGB2Layer.dCONTROL = dTempX;
          else
              G_pSysMLC->xRGB1Layer[nLayer].dCONTROL = dTempX;
         }
}
void  SysSetMLc3DEnableMode(int nLayer,int nDisableEnableMode)
{
      DWORD dTempX;

      if (nLayer == MLC_VID_LAYER)
         {
          dTempX = 0;
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
              dTempX = G_pSysMLC->xRGB2Layer.dCONTROL;
          else
              dTempX = G_pSysMLC->xRGB1Layer[nLayer].dCONTROL;
         }

      dTempX = dTempX & ~MLC_LAY_DIRTY;
      if (nDisableEnableMode)
          dTempX |=  (1 <<  8);
      else
          dTempX &= ~(1 <<  8);

      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
              G_pSysMLC->xRGB2Layer.dCONTROL = dTempX;
          else
              G_pSysMLC->xRGB1Layer[nLayer].dCONTROL = dTempX;
         }
}
void  SysSetMLcAlphaBlending(int nLayer,int nDisableEnableMode,DWORD dAlpha)
{
      DWORD dTempX;

      dAlpha = dAlpha & 0x0f;
      dAlpha = dAlpha << 28;

      if (nLayer == MLC_VID_LAYER)
          dTempX = G_pSysMLC->xVideoLayer.dCONTROL;
      else
          if (nLayer == MLC_RGB_LAYER2)
              dTempX = G_pSysMLC->xRGB2Layer.dCONTROL;
          else
              dTempX = G_pSysMLC->xRGB1Layer[nLayer].dCONTROL;

      dTempX = dTempX & ~MLC_LAY_DIRTY;

      if (nDisableEnableMode)
          dTempX |=  (1 <<  2);
      else
          dTempX &= ~(1 <<  2);

      if (nLayer == MLC_VID_LAYER)
          G_pSysMLC->xVideoLayer.dCONTROL = dTempX;
      else
          if (nLayer == MLC_RGB_LAYER2)
              G_pSysMLC->xRGB2Layer.dCONTROL = dTempX;
          else
              G_pSysMLC->xRGB1Layer[nLayer].dCONTROL = dTempX;

      if (nLayer == MLC_VID_LAYER)
         {
          G_pSysMLC->xVideoLayer.dTPCOLOR &= ~(0x0f << 28);
          G_pSysMLC->xVideoLayer.dTPCOLOR |= dAlpha;
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              G_pSysMLC->xRGB2Layer.dTPCOLOR &= ~(0x0f << 28);
              G_pSysMLC->xRGB2Layer.dTPCOLOR |= dAlpha;
             }
          else
             {
              G_pSysMLC->xRGB1Layer[nLayer].dTPCOLOR &= ~(0x0f << 28);
              G_pSysMLC->xRGB1Layer[nLayer].dTPCOLOR |= dAlpha;
             }
}
void  SysSetMLcTransparency(int nLayer,int nDisableEnableMode,DWORD dColor)
{
      DWORD dTempX;

      dColor = dColor & 0x00ffffff;

      if (nLayer == MLC_VID_LAYER)
          dTempX = G_pSysMLC->xVideoLayer.dCONTROL;
      else
          if (nLayer == MLC_RGB_LAYER2)
              dTempX = G_pSysMLC->xRGB2Layer.dCONTROL;
          else
              dTempX = G_pSysMLC->xRGB1Layer[nLayer].dCONTROL;

      dTempX = dTempX & ~MLC_LAY_DIRTY;

      if (nDisableEnableMode)
          dTempX |=  (1 <<  0);
      else
          dTempX &= ~(1 <<  0);

      if (nLayer == MLC_VID_LAYER)
          G_pSysMLC->xVideoLayer.dCONTROL = dTempX;
      else
          if (nLayer == MLC_RGB_LAYER2)
              G_pSysMLC->xRGB2Layer.dCONTROL = dTempX;
          else
              G_pSysMLC->xRGB1Layer[nLayer].dCONTROL = dTempX;

      if (nLayer == MLC_VID_LAYER)
         {
          G_pSysMLC->xVideoLayer.dTPCOLOR &= ~(0x00ffffff <<  0);
          G_pSysMLC->xVideoLayer.dTPCOLOR |= dColor;
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              G_pSysMLC->xRGB2Layer.dTPCOLOR &= ~(0x00ffffff <<  0);
              G_pSysMLC->xRGB2Layer.dTPCOLOR |= dColor;
             }
          else
             {
              G_pSysMLC->xRGB1Layer[nLayer].dTPCOLOR &= ~(0x00ffffff <<  0);
              G_pSysMLC->xRGB1Layer[nLayer].dTPCOLOR |= dColor;
             }
}
void  SysSetMLcColorInversion(int nLayer,int nDisableEnableMode,DWORD dColor)
{
      DWORD dTempX;

      dColor = dColor & 0x00ffffff;

      if (nLayer == MLC_VID_LAYER)
          dTempX = G_pSysMLC->xVideoLayer.dCONTROL;
      else
          if (nLayer == MLC_RGB_LAYER2)
              dTempX = G_pSysMLC->xRGB2Layer.dCONTROL;
          else
              dTempX = G_pSysMLC->xRGB1Layer[nLayer].dCONTROL;

      dTempX = dTempX & ~MLC_LAY_DIRTY;

      if (nDisableEnableMode)
          dTempX |=  (1 <<  1);
      else
          dTempX &= ~(1 <<  1);

      if (nLayer == MLC_VID_LAYER)
          G_pSysMLC->xVideoLayer.dCONTROL = dTempX;
      else
          if (nLayer == MLC_RGB_LAYER2)
              G_pSysMLC->xRGB2Layer.dCONTROL = dTempX;
          else
              G_pSysMLC->xRGB1Layer[nLayer].dCONTROL = dTempX;

      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              G_pSysMLC->xRGB2Layer.dINVCOLOR &= ~(0x00ffffff <<  0);
              G_pSysMLC->xRGB2Layer.dINVCOLOR |= dColor;
             }
          else
             {
              G_pSysMLC->xRGB1Layer[nLayer].dINVCOLOR &= ~(0x00ffffff <<  0);
              G_pSysMLC->xRGB1Layer[nLayer].dINVCOLOR |= dColor;
             }
}
DWORD SysGetMLcExtendedColor(DWORD dColor,DWORD dFormat)
{
      DWORD vRGB[3];
      int   vBW[3],vBP[3];
      int   nBlank,nFill,i;

      switch (dFormat)
             {
              case MLC_RGBFMT_R5G6B5   :      // 16bpp {R5,G6,B5}.
                   vBW[0] =  5;	vBW[1] =  6; vBW[2] =  5;
                   vBP[0] = 11;	vBP[1] =  5; vBP[2] =  0;
                   break;
              case MLC_RGBFMT_B5G6R5   :      // 16bpp {B5,G6,R5}.
                   vBW[0] =  5; vBW[1] =  6; vBW[2] =  5;
                   vBP[0] =  0; vBP[1] =  5; vBP[2] = 11;
                   break;
              case MLC_RGBFMT_X1R5G5B5 :      // 16bpp {X1,R5,G5,B5}.
              case MLC_RGBFMT_A1R5G5B5 :      // 16bpp {A1,R5,G5,B5}.
                   vBW[0] =  5; vBW[1] =  5; vBW[2] =  5;
                   vBP[0] = 10; vBP[1] =  5; vBP[2] =  0;
                   break;
              case MLC_RGBFMT_X1B5G5R5 :      // 16bpp {X1,B5,G5,R5}.
              case MLC_RGBFMT_A1B5G5R5 :      // 16bpp {A1,B5,G5,R5}.
                   vBW[0] =  5; vBW[1] =  5; vBW[2] =  5;
                   vBP[0] =  0; vBP[1] =  5; vBP[2] = 10;
                   break;
              case MLC_RGBFMT_X4R4G4B4 :      // 16bpp {X4,R4,G4,B4}.
              case MLC_RGBFMT_A4R4G4B4 :      // 16bpp {A4,R4,G4,B4}.
                   vBW[0] =  4; vBW[1] =  4; vBW[2] =  4;
                   vBP[0] =  8; vBP[1] =  4; vBP[2] =  0;
                   break;
              case MLC_RGBFMT_X4B4G4R4 :      // 16bpp {X4,B4,G4,R4}.
              case MLC_RGBFMT_A4B4G4R4 :      // 16bpp {A4,B4,G4,R4}.
                   vBW[0] =  4; vBW[1] =  4; vBW[2] =  4;
                   vBP[0] =  0; vBP[1] =  4; vBP[2] =  8;
                   break;
              case MLC_RGBFMT_X8R3G3B2 :      // 16bpp {X8,R3,G3,B2}.
              case MLC_RGBFMT_A8R3G3B2 :      // 16bpp {A8,R3,G3,B2}.
                   vBW[0] =  3; vBW[1] =  3; vBW[2] =  2;
                   vBP[0] =  5; vBP[1] =  2; vBP[2] =  0;
                   break;
              case MLC_RGBFMT_X8B3G3R2 :      // 16bpp {X8,B3,G3,R2}.
              case MLC_RGBFMT_A8B3G3R2 :      // 16bpp {A8,B3,G3,R2}.
                   vBW[0] =  2; vBW[1] =  3; vBW[2] =  3;
                   vBP[0] =  0; vBP[1] =  2; vBP[2] =  5;
                   break;
              case MLC_RGBFMT_R8G8B8   :      // 24bpp {R8,G8,B8}.
//            case MLC_RGBFMT_X8R8G8B8 :      // 32bpp {X8,R8,G8,B8}.
              case MLC_RGBFMT_A8R8G8B8 :      // 32bpp {A8,R8,G8,B8}.
                   vBW[0] =  8; vBW[1] =  8; vBW[2] =  8;
                   vBP[0] = 16; vBP[1] =  8; vBP[2] =  0;
                   break;
              case MLC_RGBFMT_B8G8R8   :      // 24bpp {B8,G8,R8}.
//            case MLC_RGBFMT_X8B8G8R8 :      // 32bpp {X8,B8,G8,R8}.
              case MLC_RGBFMT_A8B8G8R8 :      // 32bpp {A8,B8,G8,R8}.
                   vBW[0] =  8; vBW[1] =  8; vBW[2] =  8;
                   vBP[0] =  0; vBP[1] =  8; vBP[2] = 16;
                   break;
              default :
                   break;
             }

       for (i = 0;i < 3;i++)
           {
            vRGB[i] = (dColor >> vBP[i]) & ((DWORD)(1 << vBW[i]) - 1);

            nFill   = vBW[i];
            nBlank  = 8-nFill;
            vRGB[i] <<= nBlank;
            while (nBlank > 0)
                  {
                   vRGB[i] |= (vRGB[i] >> nFill);
                   nBlank  -= nFill;
                   nFill   += nFill;
                  }
           }

      return((vRGB[0] << 16) | (vRGB[1] << 8) | (vRGB[2] << 0));
}
void  SysSetMLcRGbFormat(int nLayer,DWORD dFormat)
{
      DWORD dTempX;

      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
              dTempX = G_pSysMLC->xRGB2Layer.dCONTROL;
          else
              dTempX = G_pSysMLC->xRGB1Layer[nLayer].dCONTROL;
         }

      dTempX = dTempX & ~(0xffff << 16);
      dTempX = dTempX & ~MLC_LAY_DIRTY;
      dTempX = dTempX | dFormat;

      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
         {
          if (nLayer == MLC_RGB_LAYER2)
              G_pSysMLC->xRGB2Layer.dCONTROL = dTempX;
          else
              G_pSysMLC->xRGB1Layer[nLayer].dCONTROL = dTempX;
         }
}
void 	SysSetMLcYUvFormat(DWORD dFormat)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->xVideoLayer.dCONTROL;

      dTempX = dTempX & ~(0x3 << 16);
      dTempX = dTempX & ~MLC_LAY_DIRTY;
      dTempX = dTempX | dFormat;

      G_pSysMLC->xVideoLayer.dCONTROL = dTempX;
}
void  SysSetMLcPosition(int nLayer,DWORD dStartX,DWORD dStartY,DWORD dLastX,DWORD dLastY)
{
      if (nLayer == MLC_VID_LAYER)
         {
          G_pSysMLC->xVideoLayer.dLEFTRIGHT = ((dStartX & 0x0FFF) << 16) | (dLastX & 0x0FFF);
          G_pSysMLC->xVideoLayer.dTOPBOTTOM = ((dStartY & 0x0FFF) << 16) | (dLastY & 0x0FFF);
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              G_pSysMLC->xRGB2Layer.dLEFTRIGHT = ((dStartX & 0x0FFF) << 16) | (dLastX & 0x0FFF);
              G_pSysMLC->xRGB2Layer.dTOPBOTTOM = ((dStartY & 0x0FFF) << 16) | (dLastY & 0x0FFF);
             }
          else
             {
              G_pSysMLC->xRGB1Layer[nLayer].dLEFTRIGHT = ((dStartX & 0x0FFF) << 16) | (dLastX & 0x0FFF);
              G_pSysMLC->xRGB1Layer[nLayer].dTOPBOTTOM = ((dStartY & 0x0FFF) << 16) | (dLastY & 0x0FFF);
             }
}
void  SysSetMLcRGbLayerInvalidPosition(int nLayer,int nRegion,DWORD dStartX,DWORD dStartY,DWORD dLastX,DWORD dLastY,DWORD dDisableEnableMode)
{
      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              if (nRegion == 0)
                 {
                  G_pSysMLC->xRGB2Layer.dINVALIDLEFTRIGHT0 = ((dDisableEnableMode << 28) | ((dStartX & 0x07FF) << 16) | (dLastX & 0x07FF));
                  G_pSysMLC->xRGB2Layer.dINVALIDTOPBOTTOM0 = (((dStartY & 0x07FF) << 16) | ((dLastY  & 0x07FF)));
                 }
              else
                 {
                  G_pSysMLC->xRGB2Layer.dINVALIDLEFTRIGHT1 = ((dDisableEnableMode << 28) | ((dStartX & 0x07FF) << 16) | (dLastX & 0x07FF));
                  G_pSysMLC->xRGB2Layer.dINVALIDTOPBOTTOM1 = (((dStartY & 0x07FF) << 16) | ((dLastY  & 0x07FF)));
                 }
             }
          else
             {
              if (nRegion == 0)
                 {
                  G_pSysMLC->xRGB1Layer[nLayer].dINVALIDLEFTRIGHT0 = ((dDisableEnableMode << 28) | ((dStartX & 0x07FF) << 16) | (dLastX & 0x07FF));
                  G_pSysMLC->xRGB1Layer[nLayer].dINVALIDTOPBOTTOM0 = (((dStartY & 0x07FF) << 16) | ((dLastY  & 0x07FF)));
                 }
              else
                 {
                  G_pSysMLC->xRGB1Layer[nLayer].dINVALIDLEFTRIGHT1 = ((dDisableEnableMode << 28) | ((dStartX & 0x07FF) << 16) | (dLastX & 0x07FF));
                  G_pSysMLC->xRGB1Layer[nLayer].dINVALIDTOPBOTTOM1 = (((dStartY & 0x07FF) << 16) | ((dLastY  & 0x07FF)));
                 }
             }
}
void  SysSetMLcRGbLayerStride(int nLayer,DWORD dHoriStride,DWORD dVertStride)
{
      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              G_pSysMLC->xRGB2Layer.dHSTRIDE = dHoriStride;
              G_pSysMLC->xRGB2Layer.dVSTRIDE = dVertStride;
             }
          else
             {
              G_pSysMLC->xRGB1Layer[nLayer].dHSTRIDE = dHoriStride;
              G_pSysMLC->xRGB1Layer[nLayer].dVSTRIDE = dVertStride;
             }
}
void  SysGetMLcRGbLayerStride(int nLayer,DWORD *pHoriStride,DWORD *pVertStride)
{
      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              *pHoriStride = G_pSysMLC->xRGB2Layer.dHSTRIDE;
              *pVertStride = G_pSysMLC->xRGB2Layer.dVSTRIDE;
             }
          else
             {
              *pHoriStride = G_pSysMLC->xRGB1Layer[nLayer].dHSTRIDE;
              *pVertStride = G_pSysMLC->xRGB1Layer[nLayer].dVSTRIDE;
             }
}
void  SysSetMLcRGbLayerAddress(int nLayer,DWORD dStartAddr)
{
	if (nLayer == MLC_VID_LAYER)
	{
	}
	else
	{
		if (nLayer == MLC_RGB_LAYER2)
		{
			G_pSysMLC->xRGB2Layer.dADDRESS = dStartAddr;
		}
		else
		{
			G_pSysMLC->xRGB1Layer[nLayer].dADDRESS = dStartAddr;
		}
	}		
}
DWORD SysGetMLcRGbLayerAddress(int nLayer)
{
      DWORD dAddress = 0;

      if (nLayer == MLC_VID_LAYER)
         {
         }
      else
          if (nLayer == MLC_RGB_LAYER2)
             {
              dAddress = G_pSysMLC->xRGB2Layer.dADDRESS;
             }
          else
             {
              dAddress = G_pSysMLC->xRGB1Layer[nLayer].dADDRESS;
             }

      return(dAddress);
}
void  SysSetMLcRGbLayerPalette(int nLayer,UCHAR bPalIndex,HWORD wPalData)
{
}
void  SysSetMLcYUvLayerStride(DWORD dLuStride,DWORD dCbStride,DWORD dCrStride)
{
      G_pSysMLC->xVideoLayer.dVSTRIDE   = dLuStride;
      G_pSysMLC->xVideoLayer.dVSTRIDECB = dCbStride;
      G_pSysMLC->xVideoLayer.dVSTRIDECR = dCrStride;
}
void  SysSetMLcYUvLayerAddress(DWORD dLuAddr,DWORD dCbAddr,DWORD dCrAddr)
{
      G_pSysMLC->xVideoLayer.dADDRESS   = dLuAddr;
      G_pSysMLC->xVideoLayer.dVSTRIDECB = dCbAddr;
      G_pSysMLC->xVideoLayer.dVSTRIDECR = dCrAddr;
}
void  SysSetMLcYUvLayerScaleFactor(DWORD dHoriScale,DWORD dVertScale,DWORD dHoriMode,DWORD dVertMode)
{
}

void  SysSetMLcYUvLayerScale(DWORD dSourceWidth,DWORD dSourceHeight,DWORD dTargetWidth,DWORD dTargetHeight,DWORD dHoriLuma,DWORD dHoriChro,DWORD dVertLuma,DWORD dVertChro)
{
      // If Bilinear Filter is used,
      //    hscale = (source width -1) * (1 << 11) / (destination width-1).
      //    vscale = (source height-1) * (1 << 11) / (destinatin height-1).
      //
      // else
      //    hscale = source width  * (1 << 11) / destination width.
      //    vscale = source height * (1 << 11) / destinatin height.

      DWORD dHoriScale;
      DWORD dVertScale;
      DWORD dTempHeight;

      // HScale
      if ((dHoriLuma || dHoriChro) && (dTargetWidth > dSourceWidth))
         {
          dSourceWidth--;
          dTargetWidth--;
         }

      dHoriScale = (dSourceWidth << 11) / dTargetWidth;

      // VScale
      if ((dVertLuma || dVertChro) && (dTargetHeight > dSourceHeight))
         {
          dSourceHeight--; 
          dTargetHeight--; 

          dVertScale = (dSourceHeight << 11) / dTargetHeight;

          // To remove a noisy vertical line at end of image in case of vertical filter on and scale-up.
          dTempHeight = ((dVertScale * dTargetHeight) >> 11);
          if (dSourceHeight <= dTempHeight)
             {
              dVertScale--;
             }
         }
      else
         {
          dVertScale = (dSourceHeight << 11) / dTargetHeight;
         }

      G_pSysMLC->xVideoLayer.dHSCALE = ((dHoriLuma << 28) | (dHoriChro << 29) | (dHoriScale & 0x007fffff));
      G_pSysMLC->xVideoLayer.dVSCALE = ((dVertLuma << 28) | (dVertChro << 29) | (dVertScale & 0x007fffff));
}

void  SysSetMLcYUvLayerLumaEnhance(DWORD dContrast,DWORD dBrightness)
{
      G_pSysMLC->xVideoLayer.dLUENH = ((dBrightness & 0xFF) << 8) | dContrast;
}

void  SysSetMLcYUvLayerChromaEnhance(DWORD dQuadrant,int dCbA,int dCbB,int dCrA,int dCrB)
{
      // Set factors to control hue and satuation for video layer.

      // dQuadrant : a quadrant to apply hue and saturation, 0 ~ 4.
      //             Set it to 0 to apply hue and saturation on all quadrant.
      //
      // CbA       : cosine value for B-Y axis, -128 ~ +127.
      // CbB       : sine value for R-Y axis, -128 ~ +127.
      // CrA       : sine value for B-Y axis, -128 ~ +127.
      // CrB       : cosine value for R-Y axis, -128 ~ +127.

      // Each quadrant has factors to control hue and satuation.
      // At each coordinate, HUE and saturation is applied by following formula.
      // (B-Y)' = (B-Y)*CbA + (R-Y)*CbB
      // (R-Y)' = (B-Y)*CrA + (R-Y)*CrB
      // where
      // CbA = cos(θ) * 64 * gain, CbB = -sin(θ) * 64 * gain
      // CrA = sin(θ) * 64 * gain, CrB =  cos(θ) * 64 * gain
      //       gain is 0 to 2.

      // The example for this equation is as follows.

      // HUE : phase relationship of the chrominance components, -180 ~ 180 degree.
      // Sat : color intensity, 0 ~ 127.

      // void SetChromaEnhance( mi, int Hue, int Sat )
      // {
      //  S32 sv, cv;
      //
      //  if (Sat < 0)  Sat = 0;
      //  else if (Sat > 127) Sat = 127;
      //
      //  sv = (S32)(sin((3.141592654f * Hue) / 180) * Sat);
      //  cv = (S32)(cos((3.141592654f * Hue) / 180) * Sat);
      //
      //  SysSetMLcYUvLayerChromaEnhance( 0,cv,-sv,sv,cv);
      // };

      DWORD dTemp;

      dTemp = (((DWORD)dCrB & 0xFF) << 24) |
              (((DWORD)dCrA & 0xFF) << 16) |
              (((DWORD)dCbB & 0xFF) <<  8) |
              (((DWORD)dCbA & 0xFF) <<  0);

      if (0 < dQuadrant)
         {
          G_pSysMLC->xVideoLayer.dCHENH[dQuadrant - 1] = dTemp;
         }
      else
         {
          G_pSysMLC->xVideoLayer.dCHENH[0] = dTemp;
          G_pSysMLC->xVideoLayer.dCHENH[1] = dTemp;
          G_pSysMLC->xVideoLayer.dCHENH[2] = dTemp;
          G_pSysMLC->xVideoLayer.dCHENH[3] = dTemp;
         }
}

void  SysSetMLcYUvLayerAddressYUYV(DWORD dStartAddr,DWORD dVertStride)
{
      G_pSysMLC->xVideoLayer.dADDRESS = dStartAddr;
      G_pSysMLC->xVideoLayer.dVSTRIDE = dVertStride;
}
void  SysSetMLcYUvLayerLineBufferPowerMode(int nDisableEnableMode)
{
      // Line buffer can power on/off during Video layer operation.
      // Line buffer only use when Video layer's Scale Up/Down operation with biliner filter On .
      //
      // Line buffer ON sequence
      //   -- Power On
      //   -- Normal Mode ( Sleep Mode disable )
      //
      //   SysSetMLcYUvLayerLineBufferPowerMode(1);
      //   SysSetMLcYUvLayerLineBufferSleepMode(0);
      //   SysSetMLcLayerEnableMode(MLC_VID_LAYER,1);      // Video-Layer Enable
      //   SysSetMLcLayDirtyFlag(MLC_VID_LAYER);           // Apply Now
      //   ...
      //   ...
      //   ...
      // Line buffer OFF sequence
      //   -- Sleep Mode
      //   -- Power Off
      //
      //   SysSetMLcLayerEnableMode(MLC_VID_LAYER,0);      // Video-Layer Disable
      //   SysSetMLcLayDirtyFlag(MLC_VID_LAYER);           // Apply Now
      //   while (SysGetMLcLayDirtyFlag(MLC_VID_LAYER) {}; // Wait Until Video-Layer is disabled
      //   SysSetMLcYUvLayerLineBufferSleepMode(1);
      //   SysSetMLcYUvLayerLineBufferPowerMode(0);

      DWORD dTempX;

      dTempX = G_pSysMLC->xVideoLayer.dCONTROL;

      dTempX = dTempX & ~MLC_LAY_DIRTY;
      if (nDisableEnableMode)
          dTempX |=  (1 << 15);
      else
          dTempX &= ~(1 << 15);

      G_pSysMLC->xVideoLayer.dCONTROL = dTempX;
}
void  SysSetMLcYUvLayerLineBufferSleepMode(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->xVideoLayer.dCONTROL;

      dTempX = dTempX & ~MLC_LAY_DIRTY;
      if (nDisableEnableMode)
          dTempX |=  (1 << 14);
      else
          dTempX &= ~(1 << 14);

      G_pSysMLC->xVideoLayer.dCONTROL = dTempX;
}
void  SysSetMLcYUvLayerGamaTablePowerMode(int nY,int nU,int nV)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dGAMMACONT;

      if (nY) dTempX |=  (1 << 13); else dTempX &= ~(1 << 13);
      if (nU) dTempX |=  (1 << 15); else dTempX &= ~(1 << 15);
      if (nV) dTempX |=  (1 << 17); else dTempX &= ~(1 << 17);

      G_pSysMLC->dGAMMACONT = dTempX;
}
void  SysSetMLcYUvLayerGamaTableSleepMode(int nY,int nU,int nV)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dGAMMACONT;

      if (nY) dTempX |=  (1 << 12); else dTempX &= ~(1 << 12);
      if (nU) dTempX |=  (1 << 14); else dTempX &= ~(1 << 14);
      if (nV) dTempX |=  (1 << 16); else dTempX &= ~(1 << 16);

      G_pSysMLC->dGAMMACONT = dTempX;
}
void  SysSetMLcYUvLayerGammaEnableMode(int nDisableEnableMode)
{
      DWORD dTempX;

      dTempX = G_pSysMLC->dGAMMACONT;

      if (nDisableEnableMode)
          dTempX |=  (1 <<  4);
      else
          dTempX &= ~(1 <<  4);

      G_pSysMLC->dGAMMACONT = dTempX;
}
//=============================================================================


