 #ifndef __MAPSET
#define __MAPSET

#ifdef DEBUG_INCLUDES
#pragma message( "+++++++++ including mapset.h +++++++++" )
#endif


#define	SD_LARGE_AIRPORTS	3
#define SD_MEDIUM_AIRPORTS	2
#define SD_SMALL_AIRPORTS	1
#define SD_GLIDERS_AIRPORTS	0


#define SD_AIRSPACE_ARSA	0
#define SD_AIRSPACE_CTA		1
#define SD_AIRSPACE_TMA		2
#define SD_AIRSPACE_TRSA	3
#define SD_AIRSPACE_TCA		4
#define SD_AIRSPACE_CTR		5
#define SD_AIRSPACE_MODEC	6

/* CATROD RELATED VALUES */
#define SD_ROAD_MOTORWAY		0
#define SD_ROAD_MAJOR_ROAD		1
#define SD_ROAD_MINOR_ROAD		2
#define SD_ROAD_TRACK_PATH		3
#define SD_ROAD_MAJOR_STREET	4
#define SD_ROAD_MINOR_STREET	5

#define SD_ROAD_MR_TYPE_A		50
#define SD_ROAD_MR_TYPE_B		51
#define SD_ROAD_MR_TYPE_C		52
#define SD_ROAD_EXITS			53
#define SD_ROAD_PEDESTRIAN_WAY	54

#ifdef USE_GRAPH_NODE
/* NETCLASS RELATED VALUES */
#define SD_ROAD_NET_CLASS_0		0
#define SD_ROAD_NET_CLASS_1		1
#define SD_ROAD_NET_CLASS_2		2
#define SD_ROAD_NET_CLASS_3		3
#define SD_ROAD_NET_CLASS_4		4
#define SD_ROAD_NET_CLASS_5		5
#define SD_ROAD_NET_CLASS_6		6
#define SD_ROAD_NET_CLASS_7		7
#endif

#define SD_RESAIR_ALERT			0
#define SD_RESAIR_CAUTION		1
#define SD_RESAIR_DANGER		2
#define SD_RESAIR_MOA			3
#define SD_RESAIR_PROHIBITED	4
#define SD_RESAIR_RESTRICTED	5
#define SD_RESAIR_TRAINING		6
#define SD_RESAIR_WARNING		7
#define SD_RESAIR_UNKNOWN		8

/* define the "attribute" parameter for selective display of these */
#define  SD_MAJOR_CITIES          0
#define  SD_LARGE_CITIES          1
#define  SD_MEDIUM_LARGE_CITIES   2
#define  SD_MEDIUM_SMALL_CITIES   3
#define  SD_SMALL_CITIES          4
#define  SD_VERY_SMALL_CITIES     5


#define SD_COPPOI_TAPOI_UNSPECIFIED				15	/* Transparent icon */
/*	Max Cabpoi value	*/
#define SD_TAPOI_COBPOI_MAX_VALUE				1625

/* _tapoi OBJECT catpoi ATTRIBUTE */
#define	SD_AIRLINE_ACCESS			(0 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				  /* Airline access */
#define	SD_AIRPORT					(1 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Airport */
#define	SD_CAR_DEALER				(2 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Car dealer */
#define	SD_CITY_CENTRE				(3 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* City centre */
#define	SD_ENTRY_POINT				(4 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Entry point */
#define	SD_FERRY_TERMINAL			(5 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Ferry terminal */
#define	SD_FRONTIER_CROSSING		(6 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Frontier crossing */
#define	SD_GOVERNMENT_OFFICE		(7 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Government office */
#define	SD_HOSPITAL_POLYCLINIC		(8 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Hospital/polyclinic */
#define	SD_HOTEL_MOTEL				(9 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Hotel/motel */
#define	SD_OPEN_PARKING_AREA		(10 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Parking area area along motorways */
#define	SD_PARKING_GARAGE			(11 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Parking garage */
#define	SD_PETROL_STATION			(12 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Petrol station */
#define	SD_POST_OFFICE				(13 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Post office */
#define	SD_METRO_STATION			(14 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Underground/metro station */
#define	SD_RENT_A_CAR_FACILITY		(15 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Rent-a-car facility */
#define	SD_RENT_A_CAR_PARKING		(16 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Rent-a-car parking */
#define	SD_REST_AREA				(17 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Rest area along motorways */
#define	SD_RESTAURANT				(18 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Restaurant */
#define	SD_SHOPPING_CENTRE			(19 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Shopping centre */
#define	SD_STADIUM					(20 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Stadium */
#define	SD_HIGHWAY_EXIT				(21 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Highway exit */
#define	SD_MINOR_RAILWAY_STATION	(22 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Minor railway station */
#define	SD_MAIN_RAILWAY_STATION		(23 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Main railway station */
#define	SD_PARK_RIDE_FACILITY		(24 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Park/ride facility */

/* Redefined with the specification of TAPOI */
#define	SD_TAPOI_AIRLINE_ACCESS					(0 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				  /* Airline access */
#define	SD_TAPOI_AIRPORT						(1 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Airport */
#define	SD_TAPOI_CAR_DEALER						(2 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Car dealer */
#define	SD_TAPOI_CITY_CENTRE					(3 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* City centre */
#define	SD_TAPOI_ENTRY_POINT					(4 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Entry point */
#define	SD_TAPOI_FERRY_TERMINAL					(5 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Ferry terminal */
#define	SD_TAPOI_FRONTIER_CROSSING				(6 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Frontier crossing */
#define	SD_TAPOI_GOVERNMENT_OFFICE				(7 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Government office */
#define	SD_TAPOI_HOSPITAL_POLYCLINIC			(8 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Hospital/polyclinic */
#define	SD_TAPOI_HOTEL_MOTEL					(9 + SD_TAPOI_COBPOI_MAX_VALUE + 1)               /* Hotel/motel */
#define	SD_TAPOI_OPEN_PARKING_AREA				(10 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Parking area area along motorways */
#define	SD_TAPOI_PARKING_GARAGE					(11 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Parking garage */
#define	SD_TAPOI_PETROL_STATION					(12 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Petrol station */
#define	SD_TAPOI_POST_OFFICE					(13 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Post office */
#define	SD_TAPOI_METRO_STATION					(14 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Underground/metro station */
#define	SD_TAPOI_RENT_A_CAR_FACILITY			(15 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Rent-a-car facility */
#define	SD_TAPOI_RENT_A_CAR_PARKING				(16 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Rent-a-car parking */
#define	SD_TAPOI_REST_AREA						(17 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Rest area along motorways */
#define	SD_TAPOI_RESTAURANT						(18 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Restaurant */
#define	SD_TAPOI_SHOPPING_CENTRE				(19 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Shopping centre */
#define	SD_TAPOI_BEACH							(20 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Beach */
#define	SD_TAPOI_HIGHWAY_EXIT					(21 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Highway exit */
#define	SD_TAPOI_MINOR_RAILWAY_STATION			(22 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Minor railway station */
#define	SD_TAPOI_MAIN_RAILWAY_STATION			(23 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Main railway station */
#define	SD_TAPOI_PARK_RIDE_FACILITY				(24 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Park/ride facility */

#define	SD_TAPOI_METRO_STATION_CLF				(25 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* underground/metro station with car loading facility */
#define	SD_TAPOI_MINOR_RAILWAY_STATION_CLF		(26 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* minor railway station with car loading facility */
#define	SD_TAPOI_MAIN_RAILWAY_STATION_CLF		(27 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* main railway station with car loading facility */
#define	SD_TAPOI_PASSENGER_FREIGHT_AIRPORT		(28 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* passanger/freight, public/private airport */
#define	SD_TAPOI_MILITARY_AIRPORT				(29 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* military airport */
#define	SD_TAPOI_VIEW							(30 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* view */
#define	SD_TAPOI_CONSTRUCTION					(31 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* construction */
#define	SD_TAPOI_BUILDING						(32 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* place of interest: building */
#define	SD_TAPOI_MONUMENT						(33 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* place of interest: monument */
#define	SD_TAPOI_ABBEY							(34 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* abbey */
#define	SD_TAPOI_CHURCH							(35 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* church */
#define	SD_TAPOI_CASTLE							(36 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* castle (to visit) */
#define	SD_TAPOI_CASTLE_NO_VISIT				(37 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* castle (not to visit) */
#define	SD_TAPOI_WINDMILL						(38 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* windmill */
#define	SD_TAPOI_WATERMILL						(39 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* water mill */
#define	SD_TAPOI_FORTRESS						(40 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* fortress */
#define	SD_TAPOI_TERRAIN						(41 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* terrain */
#define	SD_TAPOI_WALKING_TERRAIN				(42 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* walking terrain */
#define	SD_TAPOI_NATURE_RESERVE					(43 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* nature reserve */
#define	SD_TAPOI_MILITARY_CEMETERY				(44 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* military cemetery */
#define	SD_TAPOI_CAMPING_SITE					(45 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* camping site */
#define	SD_TAPOI_HOLIDAY_AREA					(46 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* holiday area */
#define	SD_TAPOI_RECREATIONAL_AREA				(47 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* recreational area */
#define	SD_TAPOI_HIPPODROME						(48 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* hippodrome */
#define	SD_TAPOI_CAR_RACETRACK					(49 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* car racetrack */
#define	SD_TAPOI_GOLF_COURSE					(50 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* golf course */
#define	SD_TAPOI_ROCKS							(51 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* rocks */
#define	SD_TAPOI_AMUSEMENT_PARK					(52 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* amusement park */
#define	SD_TAPOI_ZOO							(53 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* zoo */
#define	SD_TAPOI_STADIUM						(54 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* stadium */
#define	SD_TAPOI_RECREATIONAL_TERRAIN			(55 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* recreational terrain */
#define	SD_TAPOI_PUBLIC_PLACE					(56 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* public place */
#define	SD_TAPOI_CITY_HALL						(57 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* city hall */
#define	SD_TAPOI_POLICE_STATION					(58 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* police station */
#define	SD_TAPOI_STATE_POLICE_STATION			(59 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* state police station */
#define	SD_TAPOI_FIRE_STATION					(60 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* fire station */
#define	SD_TAPOI_COURTHOUSE						(61 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* courthouse */
#define	SD_TAPOI_CULTURAL_PLACE					(62 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* cultural place */
#define	SD_TAPOI_MUSEUM							(63 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* museum */
#define	SD_TAPOI_ARTS_CENTRE					(64 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* arts centre */
#define	SD_TAPOI_LIGHT_HOUSE					(65 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* light house */
#define	SD_TAPOI_MONASTERY						(66 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* monastery */
#define	SD_TAPOI_SPORT_HALL						(67 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* sport hall */
#define	SD_TAPOI_GOVERNMENT_BUILDING			(68 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* government building */
#define	SD_TAPOI_PRISON							(69 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* prison */
#define	SD_TAPOI_LIBRARY						(70 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* library */
#define	SD_TAPOI_UNIVERSITY						(71 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* university */
#define	SD_TAPOI_THEATRE						(72 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* theatre */
#define	SD_TAPOI_FACTORY_GROUND					(73 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* factory ground */
#define	SD_TAPOI_LAND_USE						(74 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* land use */

#define	SD_TAPOI_MOUNTAINT_PASS					(75 + SD_TAPOI_COBPOI_MAX_VALUE + 1)		        /* Mountain pass */
#define	SD_TAPOI_INDUSTRIAL_ACTIVITY            (76 + SD_TAPOI_COBPOI_MAX_VALUE + 1)              /* Industrial activity*/
#define	SD_TAPOI_UNSPECIFIED					(77 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Unspecified	*/
#define	SD_TAPOI_PLACE_OF_WORSHIP				(78 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Place of worship	*/
#define	SD_TAPOI_SPORTS_CENTRE					(79 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Sports centre */
#define	SD_TAPOI_SKATING_RANG					(80 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Skating rang	*/
#define	SD_TAPOI_WATER_SPORTS					(81 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Water Sports	*/
#define	SD_TAPOI_TOURIST_ATTRACTION				(82 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Tourist attraction */
#define	SD_TAPOI_TOURIST_INFORMATION			(83 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Tourist information */
#define	SD_TAPOI_EXHIBITION_CENTRE				(84 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Exhibition centre */
#define	SD_TAPOI_EMBASSY						(85 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Embassy	*/
#define	SD_TAPOI_FREE_PORT						(86 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Free port	*/
#define	SD_TAPOI_CASINO							(87 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Casino	*/
#define	SD_TAPOI_CINEMA							(88 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Cinema	*/
#define	SD_TAPOI_OPERA							(89 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Opera	*/
#define	SD_TAPOI_CONCERT_HALL					(90 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Concert hall	*/
#define	SD_TAPOI_MUSIC_CENTRE					(91 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Music centre	*/
#define	SD_TAPOI_ENTERTEINMENT					(92 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Entertainment	*/
#define	SD_TAPOI_CAFFE_PUB						(93 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Cafe' / Pub	*/
#define	SD_TAPOI_CASH_DISPENSER					(94 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Cash dispenser	*/
#define	SD_TAPOI_DOCTORS						(95 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Doctors	*/
#define	SD_TAPOI_DENTISTS						(96 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Dentists	*/
#define	SD_TAPOI_VETERINARIANS					(97 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Veterinarians */
#define	SD_TAPOI_TRUCK_STOP						(98 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Truck shop	*/
#define	SD_TAPOI_WC								(99 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* WC	*/
#define	SD_TAPOI_KIOSK							(100 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Kiosk	*/
#define	SD_TAPOI_CONVENTION_CENTRE				(101 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Convention centre */
#define	SD_TAPOI_NIGHTLIFE						(102 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Nightlife */
#define	SD_TAPOI_PHARMACY						(103 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Pharmacy	*/
#define	SD_TAPOI_PANORAMICVIEW					(104 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Scenic/Panoramic view */
#define	SD_TAPOI_SWIMMING_POOL					(105 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Swimming pool */
#define	SD_TAPOI_YACHT_BASIN					(106 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Yacht basin	*/
#define	SD_TAPOI_LEISURE_CENTRE					(107 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Leisure centre	*/
#define	SD_TAPOI_MINI_ROUNDABOUT				(108 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Mini roundabout	*/
#define	SD_TAPOI_MOUNTAIN_PEAK					(109 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Mountain peak	*/
#define	SD_TAPOI_BOVAG_GARAGE					(110 + SD_TAPOI_COBPOI_MAX_VALUE + 1)			/* Bovag garage		*/
#define	SD_TAPOI_BRUNNEL						(111 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Brunnel	*/
#define	SD_TAPOI_SHOP							(112 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Shop	*/
#define	SD_TAPOI_ELECTIVE_DISTRICT				(113 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Elective district */
#define	SD_TAPOI_TENNIS_COURT					(114 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Tennis court	*/
#define	SD_TAPOI_WINERY							(115 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Winery	*/
#define	SD_TAPOI_ICE_SKATING_RINK				(116 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Ice Skating rink	*/
#define	SD_TAPOI_RAMP							(117 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Ramp	*/
#define SD_TAPOI_SCHOOL							(118 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* School */
#define SD_TAPOI_VEHICLE_EQUIP_PROVIDER			(119 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Vehicle equip provider */
#define SD_TAPOI_BANK							(120 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Bank */
#define SD_TAPOI_FIRST_AID						(121 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* First AID */
#define SD_TAPOI_CAR_REPAIR						(122 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Car repair */
#define SD_TAPOI_WAL_MART						(123 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Wal-Mart */
#define SD_TAPOI_RV_PARK_CAMPGROUND				(124 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* RV Park & Campground */
#define SD_TAPOI_RV_SERVICE						(125 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* RV Service */
#define SD_TAPOI_RV_PARKING						(126 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* RV Parking */
#define SD_TAPOI_AUTHORIZED_CAR_REPAIR			(127 + SD_TAPOI_COBPOI_MAX_VALUE + 1)				/* Authorized car Repair */

/*	Max Catpoi value	*/
#define SD_TAPOI_CATPOI_MAX_VALUE				127

#define SD_TAPOI_MAX_VALUE						(SD_TAPOI_CATPOI_MAX_VALUE + SD_TAPOI_COBPOI_MAX_VALUE +1)
/*  Special POI  */
#define SD_TAPOI_SMALL_CIRCLE					( SD_TAPOI_MAX_VALUE + 1 )	/* Small Circle */

#define SD_SPECIAL_TAPOI_CATPOI_MAX_VALUE		1
/*  ** ** ** **  */
//#endif

#ifdef __cplusplus
extern "C"
   {
#endif

/*	-------------------------------------------------------------------------
	Initilize Settings
*/

PRE_EXPORT_H void IN_EXPORT_H cmInitMapObjects( void );

/*	-------------------------------------------------------------------------
	Land Settings
*/

PRE_EXPORT_H void IN_EXPORT_H cmSetNaturalFeatures( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetNaturalFeatRIVERS( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetCulturalFeatures( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetLandmarks( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetRoads( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetRailway( Bool OnOff );

/*	-------------------------------------------------------------------------
	Marine Settings
*/

PRE_EXPORT_H void IN_EXPORT_H cmSetTidesCurrents( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetDepths( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetSoundings( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetNatureOfSeabed( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetUnderWaterObj( Bool OnOff );

PRE_EXPORT_H Bool IN_EXPORT_H cmGetDepths( void );
PRE_EXPORT_H Bool IN_EXPORT_H cmGetSoundings( void );
PRE_EXPORT_H Bool IN_EXPORT_H cmGetUnderWaterObj( void );

/*	-------------------------------------------------------------------------
	Naval Aids Settings
*/

PRE_EXPORT_H void IN_EXPORT_H cmSetPorts( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetORA( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetCautionAreas( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetTracksRoutes( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetLights( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetBuoys( Bool OnsOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetSignals( Bool OnOff );

PRE_EXPORT_H Bool IN_EXPORT_H cmGetCautionAreas( void );
PRE_EXPORT_H Bool IN_EXPORT_H cmGetTracksRoutes( void );
PRE_EXPORT_H Bool IN_EXPORT_H cmGetLights( void );
PRE_EXPORT_H Bool IN_EXPORT_H cmGetBuoys( void );
PRE_EXPORT_H Bool IN_EXPORT_H cmGetSignals( void );


/*	-------------------------------------------------------------------------
	Others Settings
*/

PRE_EXPORT_H void IN_EXPORT_H cmSetNames( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetCompassDistance( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetMetaObjects( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetCartographicObjects( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetNewObjects( Bool OnOff );


/*	-------------------------------------------------------------------------
	Aeronautic Settings
*/

PRE_EXPORT_H void IN_EXPORT_H cmSetAirport( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetENRCOM( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetVOR( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetNDB( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetIntersection( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetMORA( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetFIR( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetConAreas( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetResAreas( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetVerticalObstr( Bool OnOff );
PRE_EXPORT_H void IN_EXPORT_H cmSetENRAIR (Bool OnOff );

/*	-------------------------------------------------------------------------
	Selective Display
*/
PRE_EXPORT_H void IN_EXPORT_H cmSetSDObject(Word ObjLabel, Word Type, Long Scale );
PRE_EXPORT_H void IN_EXPORT_H cmEnableSD(Bool OnOff);


#ifdef __cplusplus
	}
#endif

#endif
