/*...........................................................................*/
/*.                  File Name : TIME.CPP                                   .*/
/*.                                                                         .*/
/*.                       Date : 2004.02.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "syslib.h"
#include "comlib.h"
#include "Nand64.h"
#include "time.hpp"

#include <string.h>

/////////////////////////////////////////////////////////////////////////////
DWORD cTIME::m_dTickCount = 0x00000000;
DWORD cTIME::m_dDownTickX = 0x00000000;
DWORD cTIME::m_dDownTickY = 0x00000000;
DWORD cTIME::m_dWatchMode = 0x00000000;
DWORD cTIME::m_dWatchDogC = 0x00000000;
/////////////////////////////////////////////////////////////////////////////

cTIME::cTIME(void)
{
}
cTIME::~cTIME(void)
{
}
DWORD cTIME::GetSysTickCounter(void)
{
      return(m_dTickCount);
}
int   cTIME::Get100thSecond(void)
{
      int  nTemp;

      nTemp = m_dTickCount % ONE_SEC_TICK;
      return(nTemp / (ONE_SEC_TICK / 100));
}
DWORD cTIME::GetDiffMiliTime(DWORD Tick)
{
      DWORD Temp;

      if (Tick > m_dTickCount)
          Temp = m_dTickCount + (0xffffffff - Tick);
      else
          Temp = m_dTickCount - Tick;
      Temp = Temp * (1000 / ONE_SEC_TICK);
      return(Temp);
}
void  cTIME::ClearWatchDogCounter(void)
{
      m_dWatchDogC = 0;
}
void  cTIME::ClearWatchDogCheckMemory(void)
{
 	    volatile DWORD *pCheckMem = (volatile DWORD *)(WATCH_DOG_RESET_CHECK_MEMORY);

      pCheckMem[0] = 0;
      pCheckMem[1] = 0;
      pCheckMem[2] = 0;
      pCheckMem[3] = 0;
}
int   cTIME::IsWatchDogReset(void)
{
 	    volatile DWORD *pCheckMem = (volatile DWORD *)(WATCH_DOG_RESET_CHECK_MEMORY);

      if (pCheckMem[0] == WATCH_DOG_RESET_CHECK_DATA0 &&
          pCheckMem[1] == WATCH_DOG_RESET_CHECK_DATA1 &&
          pCheckMem[2] == WATCH_DOG_RESET_CHECK_DATA2 &&
          pCheckMem[3] == WATCH_DOG_RESET_CHECK_DATA3)
          return(1);
      return(0);
}
DWORD cTIME::SetWatchDogEnable(void)
{
      DWORD dwOldWatchDogMode = m_dWatchMode;

      m_dWatchMode = 1;
      m_dWatchDogC = 0;

      return(dwOldWatchDogMode);
}
DWORD cTIME::SetWatchDogDisable(void)
{
      DWORD dwOldWatchDogMode = m_dWatchMode;

      m_dWatchMode = 0;
      m_dWatchDogC = 0;

      return(dwOldWatchDogMode);
}
void  cTIME::RunIntHandler(void)
{
      ++m_dTickCount;
      if (m_dDownTickX) --m_dDownTickX;
      if (m_dDownTickY) --m_dDownTickY;

#if 0	// KPB
	if (m_dWatchMode)
		++m_dWatchDogC;
	
	if (m_dWatchDogC > (15 * ONE_SEC_TICK))
	{
		void  (*pMainBooter)(void);
		volatile DWORD *pCheckMem = (volatile DWORD *)(WATCH_DOG_RESET_CHECK_MEMORY);

		SysCleanAllDCache926();
		SysInvalidateDCache();
		SysInvalidateICache();

		pCheckMem[0] = WATCH_DOG_RESET_CHECK_DATA0;
		pCheckMem[1] = WATCH_DOG_RESET_CHECK_DATA1;
		pCheckMem[2] = WATCH_DOG_RESET_CHECK_DATA2;
		pCheckMem[3] = WATCH_DOG_RESET_CHECK_DATA3;

		ReadNandBlockData((UCHAR *)0x00000000 - 0x00000000, 0);

		pMainBooter = (void (*)(void))(0x00000000);

		pMainBooter();
         }
#endif	  
}
void  cTIME::ReStartSystem(void)
{
}

