#ifndef __REG_TGT_WND_HPP__
#define __REG_TGT_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "Ship.hpp"

class CRegTgtWnd : public CWnd {
	private:
		int m_nFndMd;
		int m_nCurSelTgt;
		int m_nStartViewIdx;
		int m_nTotTgtCnt;

		CEditCtrl *m_pEditName;
		CEditCtrl *m_pEditMMSI;

	public:
		enum {
			FND_MD_NAME,
			FND_MD_MMSI,				
			MAX_FND_TGT_MD				
		};

	private:
		void InitVar();
		void InitControls(cSCREEN *pScreen);
		void DrawSearchingOpt();
		void DrawResultTgtListItems();
		void DrawResultTargetList();
		void DrawFuncButton();
		void ClearWnd();

		const char *GetShipTypeString(int nType);
		void ClearSearchingResult();
		int SearchTgtByMMSI(char *pStrMMSI, int nSize);
		int SearchTgtByName(char *pStrName, int nSize);

	protected:
		
	public:
		CRegTgtWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
		virtual void OnActivate();
		virtual void OnCursorEvent(int nState);
		virtual void OnKeyEvent(int nKey, DWORD nFlags);	
		void DrawWnd(BOOL bRedraw=1);

		bool SetFindTgtMode(int nMode);
		int GetFindTgtMode();

		int GetTotSearchTgtCnt();
		CShip *GetCurSelTgt();
};

#endif	// End of __FIND_TGT_WND_HPP__

