#include "Sentence.hpp"

#ifndef __AIR_HPP__
#define __AIR_HPP__
/******************************************************************************
*
* AIR - AIS interrogation Request
*
* $--AIR,xxxxxxxxx,x.x,x,x.x,x,xxxxxxxxx,x.x,x*hh<CR><LF>
*        |         |   | |   | |         |   |
*        1         2   3 4   5 6         7   8
*
* 1. MMSI of interrogated station-1
* 2. ITU-R M.1371 message requested from station-1
* 3. message sub-section (Reserved for future use)
* 4. number of second message from station-1
* 5. message sub-section (Reserved for future use)
* 6. MMSI of interrogated station-2
* 7. number of message requested from station-2
* 8. message sub-section (Reserved for future use)
*
******************************************************************************/
class CAir : public CSentence {
    protected:
		int  m_nMMSISt1;
		int  m_nNumOfFirstMsgSt1;
		int  m_nMsgSubSection1;
		int  m_nNumOfSecondMsgSt1;
		int  m_nMsgSubSection2;
		int  m_nMMSISt2;
		int  m_nNumOfMsgSt2;
		int  m_nMsgSubSection3;
		
    public:
        CAir();
        CAir(char *pszSentence);

		void Parse();
		void SetSentence(char *pszSentence);
		int  GetFormat() { return m_nFormat; }
		void GetPlainText(char *pszPlainText);
		int  MakeSentence(BYTE *pszSentence);

		void SetMMSISt1(int nMMSI)              { m_nMMSISt1           = nMMSI ;      }
		void SetNumOfFirstMsgSt1(int nMsg)      { m_nNumOfFirstMsgSt1  = nMsg;        }
		void SetMsgSubSection1(int nSubSection) { m_nMsgSubSection1    = nSubSection; }
		void SetNumOfSecondMsgSt1(int nMsg)     { m_nNumOfSecondMsgSt1 = nMsg;        }
		void SetMsgSubSection2(int nSubSection) { m_nMsgSubSection2    = nSubSection; }
		void SetMMSISt2(int nMMSI)              { m_nMMSISt2           = nMMSI;       }
		void SetNumOfMsgSt2(int nMsg)           { m_nNumOfMsgSt2       = nMsg;        }
		void SetMsgSubSection3(int nSubSection) { m_nMsgSubSection3    = nSubSection; }

		int  GetMMSISt1()           { return m_nMMSISt1; }
		int  GetNumOfFirstMsgSt1()  { return m_nNumOfFirstMsgSt1; }
		int  GetMsgSubSection1()    { return m_nMsgSubSection1; }
		int  GetNumOfSecondMsgSt1() { return m_nNumOfSecondMsgSt1; }
		int  GetMsgSubSection2()    { return m_nMsgSubSection2; }
		int  GetMMSISt2()           { return m_nMMSISt2; }
		int  GetNumOfMsgSt2()       { return m_nNumOfMsgSt2; }
		int  GetMsgSubSection3()    { return m_nMsgSubSection3; }
};
		
#endif
