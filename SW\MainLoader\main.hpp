/*...........................................................................*/
/*.                  File Name : MAIN.H                                     .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.26                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#include <sys\stat.h>

#ifndef  __MAIN_H__
#define  __MAIN_H__

int  main(void);
void LoadLogoJpgFile(void);
void LoadMainPrgInRam(void);
int  LoadEvenOddMainPrgInRam(int nEvenOddX, DWORD dLeftSize, DWORD dBlckSize, UCHAR *pProgData, DWORD dProgSize, DWORD dProgCrcV);
void RunMainProgramFromBySdCard(void);
void RunAllDataWriteToNand(void);
void RunAllDataWriteToNandReal(void);
int  UpdateRealFile(UCHAR *pLineData);
int  ReadOneLineInConfFile(FHANDLE hFile,UCHAR *pLineData);
int  FindCharInLine(UCHAR *pLineData,UCHAR *pPickData,UCHAR uFindChar);
char *GetUpdateFolderName(void);
void RunAllDataWriteExit(void);

int  SerializeSysData(int nSaveLoad);
int  SaveSysVarData(UCHAR *pBackData);
int  RestSysVarData(UCHAR *pBackData);
void TestSysVarData(void);

/////////////////////////////////////////////////////////////////////////////
void *operator new(size_t nSize);
void *operator new [] (size_t nSize);
void  operator delete(void *pMem);
void  operator delete [] (void *pMem);
/////////////////////////////////////////////////////////////////////////////

#endif

