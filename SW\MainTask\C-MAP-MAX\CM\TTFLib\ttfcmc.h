#ifndef TTFCMC_H
#define TTFCMC_H

#include "cmaptype.h"

typedef PRE_EXPORT_H Bool ( IN_EXPORT_H *TTFInitFunc_t) (void* stream);
typedef PRE_EXPORT_H Long ( IN_EXPORT_H *TTFReadFunc_t) (void* stream, Long offset, Byte*  buffer, Long count );
typedef PRE_EXPORT_H Bool ( IN_EXPORT_H *TTFCloseFunc_t)(void* stream);

/*Device definition*/
#define fd_Memory		0
#define fd_Cartridge	1
#define fd_Disk			2

typedef struct SizeTable_t
{
	Word IL_Size;    /*Font size with internal leading*/
	Word NoIL_Size;  /*Font size without internal leading*/
}sSizeTable;

typedef struct  FontSource_t
{
    String			FontName[32];	/*Name of the Font							*/
	Word 			FontID;			/*Reserved -AutoFilled by the library-		*/
	Long			UEM;			/*Reserved -AutoFilled by the library-		*/
	SWord 			PPEM;			/*Reserved -AutoFilled by the library-		*/
	SWord 			Scale;			/*Reserved -AutoFilled by the library-		*/

    Byte			FontDevice;		/*Device identifier							*/
    
	void*			Normal;			/*Stream information for Normal Font		*/
    Long			NormalSize;		/*Number of byte of Normal Font				*/
	sSizeTable*		NSizeTable;
	Word 			NSizeTableNumOfElem;
	
	void*			Bold;			/*Stream information for Bold Font			*/
    Long			BoldSize;		/*Number of byte of Bold Font				*/
	sSizeTable*		BSizeTable;
	Word 			BSizeTableNumOfElem;
    
	void*			Italic;			/*Stream information for Italic Font		*/
    Long			ItalicSize;		/*Number of byte of Italic Font				*/
	sSizeTable*		ISizeTable;
	Word 			ISizeTableNumOfElem;
    
	void*			BoldItalic;		/*Stream information for Bold Italic Font	*/
    Long			BoldItalicSize;	/*Number of byte of Bold Italic Font		*/
	sSizeTable*		BISizeTable;
	Word 			BISizeTableNumOfElem;

	TTFInitFunc_t	TTFInitFunc;	/*Pointer to stream opening function		*/
	TTFReadFunc_t	TTFReadFunc;	/*Pointer to stream reading function		*/
	TTFCloseFunc_t	TTFCloseFunc;	/*Pointer to stream closing function		*/
}sFontSource;


#ifdef __cplusplus
extern "C"
   {
#endif

PRE_EXPORT_H Bool IN_EXPORT_H cmInsertFontSource(const sFontSource *fs);
PRE_EXPORT_H sFontSource* IN_EXPORT_H cmGetFontSource(const String *FontName);
PRE_EXPORT_H sFontSource* IN_EXPORT_H cmGetFirstNextFontSource(Bool FirstFont);
PRE_EXPORT_H Bool IN_EXPORT_H cmDeleteFontSource(const String *FontName);
PRE_EXPORT_H Bool IN_EXPORT_H cmDeleteDeviceSource(Byte FontDevice);

#ifdef __cplusplus
}
#endif


#endif /*TTFCMC_H*/