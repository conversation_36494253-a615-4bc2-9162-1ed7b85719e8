Archive member included because of file (symbol)

/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
                              JPGLIB/jpegdecr.o (__gxx_personality_sj0)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o) (__cxxabiv1::__terminate(void (*)()))
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o) (__cxxabiv1::__terminate_handler)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o) (__cxa_rethrow)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o) (__cxxabiv1::__unexpected_handler)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
                              main.o (__cxa_pure_virtual)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                              main.o (vtable for __cxxabiv1::__class_type_info)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o) (__gnu_cxx::__verbose_terminate_handler())
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o) (__cxa_demangle)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o) (__cxa_allocate_exception)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o) (__cxa_call_terminate)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o) (__cxa_end_catch)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o) (std::exception::~exception())
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o) (__cxa_get_globals_fast)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o) (__cxa_current_exception_type)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
                              COMLIB/ComLib.o (atoi)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o) (fwrite)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o) (_impure_ptr)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
                              main.o (memcmp)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
                              DEVLIB/SysSDCD.o (memcpy)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
                              COMLIB/ComLib.o (memmove)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
                              COMLIB/ComLib.o (memset)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o) (realloc)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o) (_realloc_r)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
                              JPGLIB/jpegdecr.o (setjmp)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
                              COMLIB/ComLib.o (strcat)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
                              main.o (strcmp)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
                              COMLIB/ComLib.o (strcpy)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
                              COMLIB/ComLib.o (strlen)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o) (strncmp)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o) (_strtol_r)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o) (__ctype_ptr)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o) (__sinit)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o) (_free_r)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o) (__sfvwrite_r)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o) (_fwalk)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o) (_malloc_r)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o) (memchr)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o) (__malloc_lock)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o) (__sclose)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o) (__swsetup)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o) (fclose)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o) (fflush)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o) (__smakebuf)
/cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
                              DEVLIB/SysUART.o (__udivsi3)
/cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
                              DEVLIB/DataBack.o (__divsi3)
/cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
                              /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o) (__umodsi3)
/cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
                              /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o) (__div0)
/cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
                              main.o (_Unwind_SjLj_Register)
./ALLLIB/libZip.a(compress.o)
                              COMLIB/ComLib.o (compress)
./ALLLIB/libZip.a(uncompr.o)  COMLIB/ComLib.o (uncompress)
./ALLLIB/libZip.a(deflate.o)  ./ALLLIB/libZip.a(compress.o) (deflateEnd)
./ALLLIB/libZip.a(trees.o)    ./ALLLIB/libZip.a(deflate.o) (_tr_init)
./ALLLIB/libZip.a(zutil.o)    ./ALLLIB/libZip.a(deflate.o) (z_errmsg)
./ALLLIB/libZip.a(inflate.o)  ./ALLLIB/libZip.a(uncompr.o) (inflate)
./ALLLIB/libZip.a(infblock.o)
                              ./ALLLIB/libZip.a(inflate.o) (inflate_blocks_sync_point)
./ALLLIB/libZip.a(inftrees.o)
                              ./ALLLIB/libZip.a(infblock.o) (inflate_trees_bits)
./ALLLIB/libZip.a(infcodes.o)
                              ./ALLLIB/libZip.a(infblock.o) (inflate_codes_new)
./ALLLIB/libZip.a(infutil.o)  ./ALLLIB/libZip.a(infblock.o) (inflate_flush)
./ALLLIB/libZip.a(inffast.o)  ./ALLLIB/libZip.a(infcodes.o) (inflate_fast)
./ALLLIB/libZip.a(adler32.o)  ./ALLLIB/libZip.a(deflate.o) (adler32)
./ALLLIB/libFile.a(Rtfiles.o)
                              FILELIB/osfile.o (RTFClose)
./ALLLIB/libFile.a(Rtfex.o)   ./ALLLIB/libFile.a(Rtfiles.o) (_XHandled)
./ALLLIB/libFile.a(Up437.o)   ./ALLLIB/libFile.a(Rtfiles.o) (_rtfUpper437)
./ALLLIB/libFile.a(Ut437.o)   ./ALLLIB/libFile.a(Rtfiles.o) (_rtfUnicodeMap437)
./ALLLIB/libFile.a(Rtfdata.o)
                              ./ALLLIB/libFile.a(Rtfiles.o) (RTFData)
./ALLLIB/libFile.a(Conf32.o)  ./ALLLIB/libFile.a(Rtfiles.o) (RTFDeviceList)
./ALLLIB/libFile.a(DrvSdCard.o)
                              ./ALLLIB/libFile.a(Conf32.o) (RTFDrvSdCard)
./ALLLIB/libFile.a(SysUcos.o)
                              ./ALLLIB/libFile.a(Rtfiles.o) (RTFSYSGetTLS)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
                              ./ALLLIB/libFile.a(Rtfiles.o) (strchr)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
                              ./ALLLIB/libFile.a(Rtfiles.o) (strpbrk)
/cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)
                              ./ALLLIB/libFile.a(Rtfiles.o) (strrchr)

Discarded input sections

 _ZN19jpeg_decoder_stream6attachEv
                0x00000000        0x0 main.o
 _ZN19jpeg_decoder_stream6detachEv
                0x00000000        0x0 main.o
 _ZN19jpeg_decoder_streamD0Ev
                0x00000000        0x0 main.o
 _ZN19jpeg_decoder_streamD1Ev
                0x00000000        0x0 main.o
 _ZN24jpeg_decoder_file_stream4readEPhiPb
                0x00000000        0x0 main.o
 _ZN24jpeg_decoder_file_streamD0Ev
                0x00000000        0x0 main.o
 _ZN24jpeg_decoder_file_streamD1Ev
                0x00000000        0x0 main.o
 _ZTV24jpeg_decoder_file_stream
                0x00000000        0x0 main.o
 _ZTI24jpeg_decoder_file_stream
                0x00000000        0x0 main.o
 _ZTS24jpeg_decoder_file_stream
                0x00000000        0x0 main.o
 _ZTI19jpeg_decoder_stream
                0x00000000        0x0 main.o
 _ZTS19jpeg_decoder_stream
                0x00000000        0x0 main.o
 _ZTV19jpeg_decoder_stream
                0x00000000        0x0 main.o
 _ZTVN10__cxxabiv121__vmi_class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTVN10__cxxabiv120__si_class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTVN10__cxxabiv117__class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTVSt10bad_typeid
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTVSt8bad_cast
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTVSt9type_info
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTSN10__cxxabiv121__vmi_class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTIN10__cxxabiv121__vmi_class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTSN10__cxxabiv120__si_class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTIN10__cxxabiv120__si_class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTSN10__cxxabiv117__class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTIN10__cxxabiv117__class_type_infoE
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTSSt10bad_typeid
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTISt10bad_typeid
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTSSt8bad_cast
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTISt8bad_cast
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTSSt9type_info
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTISt9type_info
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 _ZTVSt13bad_exception
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 _ZTVSt9exception
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 _ZTSSt13bad_exception
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 _ZTISt13bad_exception
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 _ZTSSt9exception
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 _ZTISt9exception
                0x00000000        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)

Memory Configuration

Name             Origin             Length             Attributes
*default*        0x00000000         0xffffffff

Linker script and memory map

                0x00000000                __DYNAMIC = 0x0
                0x82000000                . = 0x82000000
                0x82000000                __begin_of_text__ = .

startup
 *(.startup)

prog            0x82000000    0x38d08
 *(.text)
 .text          0x82000000      0x550 start.o
                0x820003f8                SysCleanInvalidateDCacheMVA
                0x82000548                SysRestStatusRegInCPU
                0x820004a0                SysSetResetVectorHigh
                0x82000478                SysSetDTLBLockdown
                0x820002ec                SysDisableAlignFault
                0x820004e8                SysDisableIRQ
                0x8200022c                SysEnableICache
                0x820002ac                SysEnableDCache
                0x820004d0                SysEnableIRQ
                0x8200044c                SysInvalidateITLBMVA
                0x820003c0                SysInvalidateICacheMVA
                0x820001dc                SysGetLastOfHeapMemory
                0x82000500                SysEnableFIQ
                0x82000428                SysWaitForInterrupt
                0x820001d4                SysGetStartOfHeapMemory
                0x82000490                SysSetResetVectorLow
                0x820004c0                SysGetInterruptStatusRegister
                0x82000388                SysSetAsyncBusMode
                0x82000334                SysDisableMMU
                0x82000470                SysSetICacheLockdownBase
                0x820000d8                call_main
                0x82000000                _start
                0x8200037c                SysSetFastBusMode
                0x820003b4                SysInvalidateICache
                0x82000480                SysSetITLBLockdown
                0x820003f0                SysCleanDCacheMVA
                0x82000394                SysSetTTBase0
                0x820003dc                SysInvalidateDCacheMVA
                0x820004b8                SysSetMonitorVectorBaseAddr
                0x82000434                SysInvalidateTLB
                0x82000400                SysCleanDCacheIndex
                0x820004c8                SysSetPeriPortReMapRegister
                0x820003d0                SysInvalidateDCache
                0x82000358                SysEnableMMU
                0x820004b0                SysSetNormalVectorBaseAddr
                0x82000310                SysEnableAlignFault
                0x82000408                SysCleanInvalidateDCacheIndex
                0x8200026c                SysDisableDCache
                0x820001e4                SysGetStartOfFreeMemory
                0x82000460                SysInvalidateDTLBMVA
                0x820001ec                SysDisableICache
                0x82000440                SysInvalidateITLB
                0x82000468                SysSetDCacheLockdownBase
                0x820003c8                SysPrefetchICacheMVA
                0x820003e4                SysInvalidateIDCache
                0x82000040                __G_MainBooterMsg
                0x82000488                SysSetProcessID
                0x820003ac                SysSetDomain
                0x82000518                SysDisableFIQ
                0x82000530                SysSaveStatusRegInCPU
                0x82000410                SysCleanAllDCache926
                0x8200041c                SysCleanFlushAllDCache926
                0x820003a4                SysSetTTBaseControl
                0x82000454                SysInvalidateDTLB
                0x8200039c                SysSetTTBase1
 .text          0x82000550      0xe0c COMLIB/ComLib.o
                0x8200079c                UpperString
                0x820008d0                ByteToHexStr
                0x82000584                RangeCheckBack32
                0x82000698                SwapReal
                0x82000a34                HanCodeStrConvert
                0x82000650                PowerOf10
                0x82000fec                LongToHexStr
                0x82000848                GetHexDigit
                0x82001044                StrToInt
                0x820006d0                GetCircularSize
                0x82000e18                SplitTextData
                0x82000920                MemScrollLeft
                0x82000704                IsOneAscii
                0x82000f94                WordToHexStr
                0x82000dcc                CopyCharStrToUniStr
                0x82000edc                IsAllSameCharacters
                0x82000d68                UniCodeStrLen
                0x820010e0                IsAllAscii
                0x820006e4                GetMinInt
                0x82000cc0                UniStrToChohab
                0x82000550                MakeSizeOfDoubleWord
                0x82000e78                RightTrimStr
                0x82000684                SwapLong
                0x82001150                IsAllDigit
                0x82000f30                FullAllTrimStr
                0x82000764                UpperChar
                0x82000670                SwapInt
                0x820006b4                CirCularDec
                0x820005fc                GetCrc32Cont
                0x820006f4                GetMaxInt
                0x82000970                HanCodeChrConvertKSToCombi
                0x82000738                RemoveNullChar
                0x820011c0                UnPackFileData
                0x82000780                LowerChar
                0x82001080                RightAlignStr
                0x8200055c                RangeCheckBack16
                0x820007fc                HexStrToLong
                0x82000d60                UniStrToChrStr
                0x820005a0                GetCrc32
                0x82000bb8                UniCodeToJohab
                0x82000aac                JohabToUniCode
                0x820007cc                LowerString
                0x820006c0                CirCularInc
                0x82000d94                IsBasicAsciiCodeStr
                0x82000870                ByteToBinStr
 .text          0x8200135c      0xd98 SYSLIB/SysLib.o
                0x820019c4                SysCalcMiliToTick
                0x82001768                __wrap_abort
                0x8200192c                SysGetScreenAddress
                0x820017e4                __wrap__close_r
                0x8200179c                __wrap_read
                0x82001934                SysDelayLoop
                0x82001be0                SysGetAlphaBlendValue
                0x8200190c                SysGetRadarScreenAddress
                0x82001968                SysGetSystemTimer
                0x82001c08                SysSetBrightValue
                0x820017a4                __wrap_write
                0x8200177c                __wrap_fstat
                0x82001ce4                SysGetScreenHeightByDevice
                0x8200178c                __wrap_isatty
                0x820017b4                __wrap_fputs
                0x820013f4                SysMakeMemoryTree
                0x82001e0c                SysGetScreenWidth
                0x82001c84                SysGetBrightValue
                0x82001e28                SysCheckLCD3800Mitsubishi
                0x82001564                SysGetLastFreeAddress
                0x82001838                SysGetCpuType
                0x82001804                __wrap__read_r
                0x82001870                SysCheckDeviceType
                0x820017dc                __wrap__open_r
                0x82001a24                SysSetUTCDate
                0x8200176c                __wrap_open
                0x82001814                SysSetCpuType
                0x820018f0                SysGetSGP330ScrnMode
                0x82001a0c                SysGetDiffTimeMili
                0x820013a4                SysGetMaxFreeMemorySize
                0x82001ba4                SysSetAlphaBlendValue
                0x820015b0                __wrap_malloc
                0x82001b6c                SysGetLOCTime
                0x82001fdc                SysDelayMiliSec
                0x8200135c                SysGetAllFreeMemorySize
                0x82001a84                SysSetUTCTime
                0x82001c00                SysSetOriginAlphaBlendValue
                0x820018b0                SysCheckNavisModelType
                0x82001854                SysSetDeviceType
                0x82002058                __wrap_calloc
                0x82001e60                SysRunSystemPowerOn
                0x82001a4c                SysGetUTCDate
                0x820017cc                __wrap_puts
                0x820017ac                __wrap_fputc
                0x820018d4                SysSetSGP330ScrnMode
                0x82001830                SysCheckCpuType
                0x82001ae4                SysSetLOCDate
                0x82001d80                SysGetScreenWidthByDevice
                0x82001cc4                SysGetNavis5100AModelMode
                0x82001988                SysIncSystemTimer
                0x82001774                __wrap_close
                0x8200180c                __wrap__write_r
                0x82001fe4                __wrap_sbrk
                0x82001878                SysGetDeviceType
                0x82001924                SysGetMenuScreenAddress
                0x820017ec                __wrap__fstat_r
                0x82001ca4                SysSetNavis5100AModelMode
                0x820019b4                SysCalcTickToMili
                0x82002050                __wrap__sbrk_r
                0x82001454                SysCheckMemoryTree
                0x82001fe0                SysDelayMicroSec
                0x82001894                SysSetNavisModelType
                0x82001794                __wrap_lseek
                0x820017fc                __wrap__lseek_r
                0x82002080                SysInitFreeMemory
                0x820019ec                SysGetDiffTimeTick
                0x820017bc                __wrap_printf
                0x820018b8                SysGetNavisModelType
                0x82001d64                SysGetScreenHeight
                0x8200166c                __wrap_free
                0x82001b44                SysSetLOCTime
                0x82001aac                SysGetUTCTime
                0x82001e78                SysSetPaletteData
                0x82001e6c                SysRunSystemPowerOff
                0x82001b0c                SysGetLOCDate
                0x82001918                SysGetChartScreenAddress
                0x82001520                SysGetNumberOfFragment
                0x820015a8                SysRemoveMemFragment
 .text          0x820020f4      0x258 SYSLIB/SysIntr.o
                0x82002310                SysSetAllInterruptDisable
                0x82002258                IsrHandlerUART0
                0x820020f4                IsrHandlerIRQ
                0x82002234                IsrHandlerUART3
                0x820022cc                SysSetAllInterrupt
                0x82002240                IsrHandlerUART2
                0x82002188                IsrHandlerSWI
                0x82002264                SysSetOneInterrupt
                0x82002190                IsrHandlerUNDEF
                0x82002328                IsrHandlerTimer0
                0x82002194                IsrHandlerUARTx
                0x82002184                IsrHandlerFIQ
                0x8200224c                IsrHandlerUART1
                0x8200218c                IsrHandlerABORT
 .text          0x8200234c     0x1560 DEVLIB/Nand64.o
                0x82002728                NandCorrectDataX
                0x8200344c                WriteNandBlockData
                0x82003140                ReadNandBlockData
                0x8200358c                SetBlockToBadBlock
                0x820031a0                ReadNandFlashByCpuAddr
                0x82003600                MakeValidBlockInfo
                0x82002e40                DisableWriteProtect
                0x82003224                MakeBlockAddress
                0x82003534                WriteZeroBlock
                0x8200235c                GetBytesPerBlock
                0x8200236c                GetPhyNandAddr
                0x820025fc                NandCalculateEccX
                0x820024dc                ResetFlash
                0x82002458                WaitFlashReady
                0x82002d08                NandCorrectDataY
                0x82002838                NandCalculateEccY
                0x8200237c                GetNandColAddr
                0x82002364                GetTotalBlockNo
                0x820023e8                ConvertVirNandAddrToPhyNand
                0x820025c4                SetMapLoadOrPrgUpdateMode
                0x820025e0                GetMapLoadOrPrgUpdateMode
                0x8200234c                GetBytesPerPage
                0x820023e0                ConvertCpuAddrToVirNand
                0x82003828                SaveNandFlashByCpuAddr
                0x82002e68                EraseOneFlashBlockNoWaitStart
                0x8200327c                WriteNandPageData
                0x82002700                CountAllOneBitsX
                0x82002388                GetNandRowAddr
                0x82002390                IsValidBlock
                0x82002354                GetPagesPerBlock
                0x820034ac                WriteNandFlashByCpuAddr
                0x82002e54                EnableWriteProtect
                0x82002f2c                ReadNandPageData
                0x82002ee4                EraseNandFlashByCpuAddr
                0x82002504                ReadFlashDeviceCode
                0x82002580                CheckFlashID
                0x82002498                WaitBusy
 .text          0x820038ac      0x164 DEVLIB/SysMMU.o
                0x820038ac                SysSetMmuMTT
                0x820038f4                SysMmuInit
 .text          0x82003a10      0x10c DEVLIB/SysPLL.o
                0x82003ad0                SysSetPLL1DIV
                0x82003aec                SysInitPLLs
                0x82003a70                SysSetPLLBCLK
                0x82003ae8                SysSetPLL2DIV
                0x82003a3c                SysSetPLLCPU0
                0x82003ab8                SysSetPLL0DIV
                0x82003a90                SysSetPLL1PowerDownMode
                0x82003a54                SysSetPLLCPU1
                0x82003a6c                SysSetPLLSdRamDIV
                0x82003a28                SysIsPLLStable
                0x82003ab4                SysSetPLL2PowerDownMode
                0x82003a10                SysDoPLLChange
 .text          0x82003b1c       0xec DEVLIB/SysMCUD.o
                0x82003bf4                SysGetReadLatencyByDevice
                0x82003b24                SysSetMCUdSDRDataWidth
                0x82003b34                SysSetMCUdMDS
                0x82003b58                SysSetMCUdRCD
                0x82003b44                SysSetMCUdApplyModeSetting
                0x82003b78                SysSetMCUdSetValue
                0x82003b3c                SysSetMCUdDIC
                0x82003b74                SysSetMCUdDQSDelay
                0x82003b50                SysSetMCUdMRD
                0x82003b6c                SysSetMCUdDisplayBlockMode
                0x82003bd4                SysGetSDRDataWidthByDevice
                0x82003be4                SysGetCasLatencyByDevice
                0x82003b30                SysSetMCUdReadLatency
                0x82003b64                SysSetMCUdWR
                0x82003bac                SysGetMCUdGetValue
                0x82003b38                SysSetMCUdPASR
                0x82003c04                SysInitMCUD
                0x82003b70                SysSetMCUdClockDelay
                0x82003b48                SysGetMCUdBusyModeSetting
                0x82003b5c                SysSetMCUdRC
                0x82003b68                SysSetMCUdRefreshPeriod
                0x82003b1c                SysSetMCUdSDRType
                0x82003bfc                SysGetRefreshPeriodByDevice
                0x82003b2c                SysSetMCUdCasLatency
                0x82003b60                SysSetMCUdRAS
                0x82003b54                SysSetMCUdRP
                0x82003b20                SysSetMCUdSDRBusWidth
                0x82003b28                SysSetMCUdSDRCapacity
                0x82003b40                SysSetMCUdDLLEnable
 .text          0x82003c08      0x318 DEVLIB/SysMCUS.o
                0x82003c08                SysInitNAND
                0x82003cd0                SysSetMCUsCommon
                0x82003c38                SysSetMCUsBit1Data
                0x82003c88                SysSetMCUsBit4Data
                0x82003e7c                SysInitMCUS
                0x82003c64                SysSetMCUsBit2Data
                0x82003cac                SysSetMCUsBit8Data
 .text          0x82003f20      0x574 DEVLIB/SysGPIO.o
                0x820041d4                SysGetGPIOC
                0x82004234                SysGetGPIOxDetectBitData
                0x82004094                SysSetGPIOC
                0x820042fc                SysSetSPI0TXD
                0x8200424c                SysClearGPIOxDetectBitData
                0x8200425c                SysSetSPI0FRM
                0x82004468                SysGetFpgaTDO
                0x820040dc                SysSetGPIOA
                0x82004194                SysGetGPIOA
                0x82004100                SysSetGPIOxBitData
                0x820040b8                SysSetGPIOB
                0x82003f20                SysSetGPIOx
                0x8200434c                SysGetSPI0RXD
                0x8200418c                SysGetGPIOxData
                0x82004148                SysSetGPIOxBitMode
                0x8200404c                SysSetGPIOE
                0x82004214                SysGetGPIOE
                0x820041b4                SysGetGPIOB
                0x82004418                SysSetFpgaTDI
                0x820041f4                SysGetGPIOD
                0x82004070                SysSetGPIOD
                0x820042ac                SysSetSPI0CLK
                0x820043c8                SysSetFpgaTMS
                0x82004378                SysSetFpgaTCK
                0x82004130                SysGetGPIOxBitData
 .text          0x82004494      0x120 DEVLIB/SysAlive.o
                0x820044a0                SysSetALIVExBitPullUpMode
                0x8200449c                SysSetALIVExBitMode
                0x820044d4                SysSetALIVExBitOutEnableMode
                0x82004550                SysSetALIVE
                0x82004508                SysSetALIVExBitData
                0x82004494                SysGetALIVExBitData
 .text          0x820045b4      0x1cc DEVLIB/SysTimer.o
                0x8200467c                SysGetTimerCounter
                0x820046a0                SysTimerDelayMiliSec
                0x8200470c                SysTimerDelayMicroSec
                0x820045b4                SysInitTimer
 .text          0x82004780      0x418 DEVLIB/SysUART.o
                0x82004934                SysSetUartRxFifoTrgLevel
                0x820047e0                SysClearUartIntPendingAll
                0x82004780                SysSetUartBaudRate
                0x820048c0                SysResetUartTxFIFO
                0x820047f0                SysSetUartIntEnableAllMode
                0x8200497c                SysSetUartSyncPendClear
                0x82004898                SysSetUartErrorIntEnable
                0x82004830                SysSetUartTxIntDisable
                0x82004858                SysSetUartRxIntDisable
                0x820048a8                SysSetUartErrorIntDisable
                0x82004820                SysSetUartTxIntEnable
                0x82004970                SysGetUartErrorStatus
                0x82004870                SysSetUartRxTimeOutIntEnable
                0x82004880                SysSetUartRxTimeOutIntDisable
                0x82004848                SysSetUartRxIntEnable
                0x820048dc                SysResetUartRxFIFO
                0x820048f8                SysSetUartTxFifoTrgLevel
                0x82004998                SysSetUART
 .text          0x82004b98     0x15c8 DEVLIB/SysMLC.o
                0x82004e14                SysSetMLcLayerEnableMode
                0x8200591c                SysSetMLcRGbLayerStride
                0x82005bb0                SysSetMLcYUvLayerAddressYUYV
                0x82004d80                SysSetMLcLayDirtyFlag
                0x82004e08                SysSetMLcPaletteSleepMode
                0x82004b98                SysGetMLcTopDirtyFlag
                0x82004c4c                SysSetMLcPriority
                0x82004d00                SysSetMLcClockBClkMode
                0x82004dfc                SysSetMLcPalettePowerMode
                0x82005a48                SysSetMLcYUvLayerAddress
                0x82004c14                SysSetMLcFieldEnable
                0x82004c38                SysGetMLcFieldEnable
                0x82004bd8                SysSetMLcEnableMode
                0x82005bec                SysSetMLcYUvLayerLineBufferSleepMode
                0x82004ce4                SysSetMLcClockPClkMode
                0x82005bc4                SysSetMLcYUvLayerLineBufferPowerMode
                0x820057ac                SysSetMLcRGbLayerInvalidPosition
                0x82004e0c                SysGetMLcPaletteSleepMode
                0x82005cb0                SysInitMLC
                0x82004d1c                SysGetMLcLayDirtyFlag
                0x82004c70                SysSetMLcScreenSize
                0x82004e00                SysGetMLcPalettePowerMode
                0x82005a64                SysSetMLcYUvLayerScale
                0x8200595c                SysGetMLcRGbLayerStride
                0x82005a60                SysSetMLcYUvLayerScaleFactor
                0x82005c50                SysSetMLcYUvLayerGamaTableSleepMode
                0x82005218                SysSetMLcColorInversion
                0x82005c14                SysSetMLcYUvLayerGamaTablePowerMode
                0x82005b64                SysSetMLcYUvLayerChromaEnhance
                0x82004cc0                SysSetMLcTopSleepMode
                0x82005a2c                SysSetMLcRGbLayerPalette
                0x82004c9c                SysSetMLcTopPowerMode
                0x820059b8                SysSetMLcRGbLayerAddress
                0x82004f44                SysSetMLcLockSize
                0x82005c8c                SysSetMLcYUvLayerGammaEnableMode
                0x82005138                SysSetMLcTransparency
                0x820059f0                SysGetMLcRGbLayerAddress
                0x82005058                SysSetMLcAlphaBlending
                0x820056a4                SysSetMLcYUvFormat
                0x82004bb0                SysSetMLcTopDirtyFlag
                0x820056c4                SysSetMLcPosition
                0x82004fbc                SysSetMLc3DEnableMode
                0x82005b4c                SysSetMLcYUvLayerLumaEnhance
                0x82004bfc                SysGetMLcEnableMode
                0x8200563c                SysSetMLcRGbFormat
                0x82004c8c                SysSetMLcBackground
                0x82005a30                SysSetMLcYUvLayerStride
                0x82004ee0                SysGetMLcLayerEnableMode
                0x820052f4                SysGetMLcExtendedColor
 .text          0x82006160      0x890 DEVLIB/SysDPC.o
                0x820065c8                SysSetDPcClockDivisorEnable
                0x82006698                SysGetVSyncOffsetByDevice
                0x82006160                SysSetDPcEnableMode
                0x820061f8                SysSetDPcDither
                0x8200677c                SysInitDPC
                0x82006198                SysGetDPcEnableMode
                0x82006640                SysGetFormatByDevice
                0x820063cc                SysSetDPcClockPClkMode
                0x82006470                SysGetDPcClockDivisor
                0x82006610                SysGetVclkSourceByDevice
                0x820065a4                SysSetDPcClockOutEnb
                0x820064b8                SysSetDpcMode
                0x82006648                SysGetDelayByDevice
                0x820066ec                SysGetVSyncByDevice
                0x820063f4                SysGetDPcClockPClkMode
                0x820062c0                SysSetDPcVSync
                0x8200640c                SysSetDPcClockSource
                0x82006734                SysGetHSyncByDevice
                0x82006448                SysSetDPcClockDivisor
                0x8200642c                SysGetDPcClockSource
                0x82006238                SysSetDPcHSync
                0x820061ac                SysSetDPcDelay
                0x8200648c                SysSetDPcClockOutInv
                0x820065ec                SysSetDPcClockOutDelay
                0x820066c8                SysGetClockDivisorByDevice
                0x820063a0                SysSetDPcVSyncOffset
                0x82006618                SysGetVclkFrequency
 .text          0x820069f0      0x190 DEVLIB/SysLCD.o
                0x82006a04                SysSetLcdCLK
                0x82006a60                SysSetLcdPWM
                0x82006a00                SysSetLcdSDA
                0x82006a0c                SysInitLCD
                0x820069f8                SysWriteDataToLcdI2C
                0x82006ab8                SysSetLcdOff
                0x82006a24                SysSetLedPWMbyPercent
                0x820069f4                SysSetLcdRST
                0x820069f0                SysSetLcdSEN
                0x82006a14                SysSetLcdBackLightMode
                0x82006a18                SysSetLedPWMbyDutyVal
                0x82006b1c                SysSetLcdOn
                0x82006a08                SysSetLcdLvdsPwrDownMode
 .text          0x82006b80      0x358 DEVLIB/SysPWM.o
                0x82006cac                SysSetPWMPeriod
                0x82006d50                SysGetPWMDutyCycle
                0x82006b9c                SysSetPWMClockDivisorEnable
                0x82006ce4                SysGetPWMPeriod
                0x82006b80                SysSetPWMClockPClkMode
                0x82006d84                SysInitPWM
                0x82006bdc                SysSetPWMClockDivisor
                0x82006c50                SysSetPWMPolarity
                0x82006c04                SysSetPWMPreScale
                0x82006d18                SysSetPWMDutyCycle
                0x82006bc0                SysSetPWMClockSource
 .text          0x82006ed8     0x1b8c DEVLIB/SysSDCD.o
                0x82007a0c                SysGetSdCdAutoStopResponse
                0x82008704                SysSdCardSetUpAndDetect
                0x820076bc                SysIsSdCdResetFIFO
                0x82007cfc                SysIsScCdCardPresent
                0x820075b4                SysSetSdCdClockDivider
                0x82007584                SysSetSdCdClockDivisorEnable
                0x82008204                SysCheckSdCardChangStatus
                0x820075d8                SysSetSdCdClockDividerEnable
                0x820078bc                SysSetSdCdByteCount
                0x82007efc                SysSendSdCardCommandInternalByPoll
                0x82007808                SysSetSdCdDataTimeOut
                0x820084ac                SysSdCardReadBlockData
                0x8200789c                SysSetSdCdBlockSize
                0x82007748                SysGetSdCdInterruptPending32
                0x82007834                SysSetSdCdResponseTimeOut
                0x82007708                SysSetSdCdInterruptEnableAll
                0x82007ba8                SysGetSdCdFIFOCount
                0x820070fc                SysParseSdCardCSD
                0x82006f40                SysGetSdCardSetStatus
                0x82007bd0                SysIsSdCdDataTransferBusy
                0x82007d5c                SysIsSdCdFIFOEmpty
                0x820074fc                SysSetSdCdClockPClkMode
                0x820079e8                SysIsSdCdCommandBusy
                0x82007a54                SysGetSdCdShortResponse
                0x820077e0                SysGetSdCdInterruptPendingOne
                0x82006f20                SysSetSdCardSetStatus
                0x82007528                SysSetSdCdClockSource
                0x82007b34                SysWriteSdCdData32
                0x82008184                SysSendSdCardAppCommand
                0x820081b8                SysSendSdCardCommand
                0x82006f00                SysGetSdCardRCA
                0x82007a2c                SysGetSdCdResponseIndex
                0x82007d2c                SysIsSdCdRxBusy
                0x82007768                SysGetSdCdInterruptPendingAll
                0x820077b8                SysClearSdCdInterruptPendingOne
                0x82007ac0                SysReadSdCdData32
                0x82007bf8                SysIsSdCdCardDataBusy
                0x82006ed8                SysSetSdCardRCA
                0x820072dc                SysParseSdCardCID
                0x82007904                SysSetSdCdCommand
                0x82008034                SysSendSdCardCommandInternal
                0x82007610                SysReSetSdCdDMA
                0x82006f84                SysGetSdCardBitsFromRspData
                0x8200763c                SysReSetSdCdFIFO
                0x82007990                SysSetSdCdFIFORxThreshold
                0x82007668                SysReSetSdCdCTRL
                0x820078dc                SysStartSdCdCommand
                0x820079bc                SysSetSdCdFIFOTxThreshold
                0x82006f60                SysParseSdCardStatus
                0x82007ab8                SysGetSdCdCRC
                0x8200760c                SysSetSdCdByteOrder
                0x82007794                SysClearSdCdInterruptPendingAll
                0x820082d0                SysSdCardWriteBlockData
                0x82008258                SysSdCardWriteExitError
                0x82007554                SysSetSdCdClockDivisor
                0x82006f80                SysParseSdCardSdStatus
                0x82007dd0                SysParseSdCardMBR
                0x82007d84                SysIsSdCdFIFOTxThreshold
                0x8200739c                SysSendSdCardCommandInternalByDMA
                0x82007928                SysSetSdCdArgument
                0x8200895c                SysInitSDCD
                0x82007a74                SysGetSdCdLongResponse
                0x820073a4                SysGetSdCardFlagByCommand
                0x82008294                SysSdCardReadExitError
                0x82007d34                SysIsSdCdFIFOFull
                0x82007dac                SysIsSdCdFIFORxThreshold
                0x82007d24                SysIsSdCdTxBusy
                0x82007694                SysIsSdCdResetDMA
                0x82007860                SysSetSdCdDataBusWidth
                0x820081d8                SysGetSdCardRealPortStatus
                0x82007c20                SysSetSdCardClock
                0x820076e4                SysIsSdCdResetCTRL
                0x82007948                SysSetSdCdDMAMode
 .text          0x82008a64      0x400 DEVLIB/SdCardDRV.o
                0x82008e1c                SdCardSetUpAndDetect
                0x82008bd0                SdCardGetRelativeSectors
                0x82008b58                SdCardGetEndingSect
                0x82008d50                SdCardSetCardInsertStatus
                0x82008e34                SdCardWriteBlockData
                0x82008cdc                SdCardGetWriteBlockSizeFromCSD
                0x82008dcc                SdCardSendBusWidth
                0x82008a80                SdCardGetStartingHead
                0x82008ae4                SdCardGetStartingCyli
                0x82008d08                SdCardGetReadBlockSizeFromCSD
                0x82008dbc                SdCardSendPullUpMode
                0x82008c30                SdCardGetTotalSectors
                0x82008b8c                SdCardGetEndingCyli
                0x82008ce4                SdCardGetBlocksPerSectorFromCSD
                0x82008d70                SdCardGetCardInsertStatus
                0x82008e04                SdCardSendSelectCard
                0x82008a64                SdCardSetSdCardPtr
                0x82008d90                SdCardSendBlockLength
                0x82008d10                SdCardSetCardChangStatus
                0x82008ab0                SdCardGetStartingSect
                0x82008e4c                SdCardReadBlockData
                0x82008c90                SdCardGetLBAbyCHS
                0x82008d30                SdCardGetCardChangStatus
                0x82008b28                SdCardGetEndingHead
 .text          0x82008e64      0x194 DEVLIB/DataBack.o
                0x82008e88                BackDataRead
                0x82008f08                FindDataSegmentStart
                0x82008e64                FindDataSegmentSize
                0x82008f7c                BackDataSegmentRead
 .text          0x82008ff8      0x7e8 FILELIB/osfile.o
                0x82009620                OsfWrite
                0x820096d8                OsfClose
                0x820090d4                OsfGetDrvNameStrByMountNo
                0x82009090                OsfGetDriveMountNo
                0x82009480                OsfFindNextEx
                0x820090b8                OsfGetDrvNameCharByMountNo
                0x82009314                OsfCommitAll
                0x82009150                OsfGetFileDateTime
                0x8200971c                OsfOpen
                0x820094dc                OsfFindFirstEx
                0x82009044                OsfGetUsbDiskDriveChar
                0x82009000                OsfGetSdCardDriveStr
                0x820095cc                OsfSeek
                0x820091e4                OsfCloseAll
                0x8200943c                OsfFindClose
                0x8200967c                OsfRead
                0x82009394                OsfFDelete
                0x82009544                OsfRemoveDir
                0x820093e4                OsfRename
                0x820090e4                OsfAppendDriveName
                0x820091e8                OsfRawMediaChanged
                0x8200904c                OsfGetUsbDiskDriveStr
                0x82009768                OsfCheckDir
                0x820091ec                OsfResetDisk
                0x82009588                OsfCreateDir
                0x82009214                OsfMountDisk
                0x82009254                OsfGetDiskFreeSpace
                0x82008ff8                OsfGetSdCardDriveChar
 .text          0x820097e0     0xa76c JPGLIB/jpegdecr.o
                0x82009e1c                jpeg_decoder::terminate(int)
                0x8200f3bc                jpeg_decoder::init_scan()
                0x8200ab34                jpeg_decoder::H2V1Convert()
                0x82009dac                jpeg_decoder::free_all_blocks()
                0x8201047c                jpeg_decoder::decode_init(jpeg_decoder_stream*, bool)
                0x820104dc                jpeg_decoder::jpeg_decoder(jpeg_decoder_stream*, bool)
                0x8200b6fc                jpeg_decoder::decode_scan(void (*)(jpeg_decoder*, int, int, int))
                0x8200cea0                jpeg_decoder::locate_soi_marker()
                0x8200c8e8                jpeg_decoder::init(jpeg_decoder_stream*, bool)
                0x8200a938                jpeg_decoder::H1V2Convert()
                0x82011c90                jpeg_decoder::decode(void**, unsigned int*)
                0x820097e0                jpeg_decoder::word_clear(void*, unsigned short, unsigned int)
                0x82009e18                jpeg_decoder::~jpeg_decoder()
                0x8200f468                jpeg_decoder::init_sequential()
                0x8200f094                jpeg_decoder::process_markers()
                0x8200a1e8                jpeg_decoder::coeff_buf_write(coeff_buf_tag*, int, int, short*)
                0x8200e090                jpeg_decoder::read_sos_marker()
                0x82009e14                jpeg_decoder::~jpeg_decoder()
                0x8200f8a8                jpeg_decoder::begin()
                0x8200a8dc                jpeg_decoder::coeff_buf_open(int, int, int, int)
                0x8200a258                jpeg_decoder::coeff_buf_read(coeff_buf_tag*, int, int, short*)
                0x82010520                jpeg_decoder::decode_next_row()
                0x82010498                jpeg_decoder::jpeg_decoder(jpeg_decoder_stream*, bool)
                0x8200b938                jpeg_decoder::fix_in_buffer()
                0x8200ae40                progressive_block_decoder::decode_block_dc_refine(jpeg_decoder*, int, int, int)
                0x8200f48c                jpeg_decoder::init_progressive()
                0x8200ee10                jpeg_decoder::skip_variable_marker()
                0x82009eb0                jpeg_decoder::coeff_buf_getp(coeff_buf_tag*, int, int)
                0x82009f14                jpeg_decoder::load_next_row()
                0x8200d270                jpeg_decoder::next_marker()
                0x82009ab8                jpeg_decoder::make_huff_table(int, huff_tables_tag*)
                0x8200dae8                jpeg_decoder::read_dqt_marker()
                0x8200be70                progressive_block_decoder::decode_block_dc_first(jpeg_decoder*, int, int, int)
                0x82009e48                jpeg_decoder::check_quant_tables()
                0x8200a760                jpeg_decoder::check_huff_tables()
                0x8200f928                jpeg_decoder::read_sof_marker()
                0x82011e48                progressive_block_decoder::decode_block_ac_refine(jpeg_decoder*, int, int, int)
                0x8200ad2c                jpeg_decoder::H1V1Convert()
                0x82009d4c                jpeg_decoder::transform_row()
                0x820131c0                progressive_block_decoder::decode_block_ac_first(jpeg_decoder*, int, int, int)
                0x820098f0                jpeg_decoder::calc_mcu_block_order()
                0x820103fc                jpeg_decoder::locate_sof_marker()
                0x8200b10c                jpeg_decoder::process_restart()
                0x8200f878                jpeg_decoder::decode_start()
                0x8200a39c                jpeg_decoder::alloc(int)
                0x8200d4d8                jpeg_decoder::read_dht_marker()
                0x8200eba0                jpeg_decoder::read_dri_marker()
                0x82009804                jpeg_decoder::create_look_ups()
                0x82009888                jpeg_decoder::GrayConvert()
                0x8200a2c8                jpeg_decoder::prep_in_buffer()
                0x8200a42c                jpeg_decoder::init_frame()
                0x8200f380                jpeg_decoder::locate_sos_marker()
                0x8200f168                jpeg_decoder::find_eoi()
 .text          0x82013f4c      0x614 JPGLIB/jpegidct.o
                0x82013f4c                idct(short*, unsigned char*)
 .text          0x82014560      0x308 JPGLIB/jpegh2v2.o
                0x82014560                jpeg_decoder::H2V2Convert()
 .text          0x82014868     0x1d8c main.o
                0x82014eb0                SerializeSysData(int)
                0x820148d0                operator new(unsigned long)
                0x82014b0c                RestSysVarData(unsigned char*)
                0x82014868                FindCharInLine(unsigned char*, unsigned char*, unsigned char)
                0x820148c8                operator delete(void*)
                0x8201564c                RunMainProgramFromBySdCard()
                0x8201508c                LoadEvenOddMainPrgInRam(int, unsigned int, unsigned int, unsigned char*, unsigned int, unsigned int)
                0x82014cec                SaveSysVarData(unsigned char*)
                0x820148d4                TestSysVarData()
                0x82015010                ReadOneLineInConfFile(int, unsigned char*)
                0x8201561c                RunAllDataWriteToNand()
                0x820159ac                LoadMainPrgInRam()
                0x8201654c                main
                0x820148c4                operator delete[](void*)
                0x8201511c                UpdateRealFile(unsigned char*)
                0x82015fa8                LoadLogoJpgFile()
                0x820148cc                operator new[](unsigned long)
                0x82014fd4                GetUpdateFolderName()
                0x82014ef8                RunAllDataWriteExit()
                0x820154c0                RunAllDataWriteToNandReal()
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .text          0x820165f4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .text          0x820165f4     0x48d4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
                0x8201adc4                __cxa_demangle
 .text          0x8201aec8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .text          0x8201aec8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .text          0x8201aec8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .text          0x8201aec8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .text          0x8201aec8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .text          0x8201aec8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .text          0x8201aec8       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
                0x8201aec8                _atoi_r
                0x8201aed4                atoi
 .text          0x8201aee0       0xc4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
                0x8201aee0                _fwrite_r
                0x8201af64                fwrite
 .text          0x8201afa4        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .text          0x8201afa4       0x94 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
                0x8201afa4                memcmp
 .text          0x8201b038       0xa8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
                0x8201b038                memcpy
 .text          0x8201b0e0       0xf4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
                0x8201b0e0                memmove
 .text          0x8201b1d4       0x98 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
                0x8201b1d4                memset
 .text          0x8201b26c       0x1c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
                0x8201b26c                realloc
 .text          0x8201b288      0x500 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
                0x8201b288                _realloc_r
 .text          0x8201b788       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
                0x8201b79c                longjmp
                0x8201b788                setjmp
 .text          0x8201b7b4       0xa4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
                0x8201b7b4                strcat
 .text          0x8201b858       0xe8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
                0x8201b858                strcmp
 .text          0x8201b940       0x9c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
                0x8201b940                strcpy
 .text          0x8201b9dc       0x68 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
                0x8201b9dc                strlen
 .text          0x8201ba44      0x144 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
                0x8201ba44                strncmp
 .text          0x8201bb88      0x1f0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
                0x8201bd4c                strtol
                0x8201bb88                _strtol_r
 .text          0x8201bd78        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .text          0x8201bd78      0x26c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
                0x8201bde4                __sinit
                0x8201bebc                __sfmoreglue
                0x8201beac                _cleanup
                0x8201bea0                _cleanup_r
                0x8201be88                __fp_lock_all
                0x8201bdd4                __sfp_lock_acquire
                0x8201bf0c                __sfp
                0x8201bde0                __sinit_lock_release
                0x8201be70                __fp_unlock_all
                0x8201bdd8                __sfp_lock_release
                0x8201bddc                __sinit_lock_acquire
 .text          0x8201bfe4      0x3a4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
                0x8201c0dc                _free_r
                0x8201bfe4                _malloc_trim_r
 .text          0x8201c388      0x44c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
                0x8201c388                __sfvwrite_r
 .text          0x8201c7d4      0x10c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
                0x8201c85c                _fwalk
                0x8201c7d4                _fwalk_reent
 .text          0x8201c8e0      0x798 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
                0x8201c8e0                _malloc_r
 .text          0x8201d078      0x10c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
                0x8201d078                memchr
 .text          0x8201d184        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
                0x8201d188                __malloc_unlock
                0x8201d184                __malloc_lock
 .text          0x8201d18c      0x100 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
                0x8201d1a0                __sseek
                0x8201d248                __sread
                0x8201d18c                __sclose
                0x8201d1e8                __swrite
 .text          0x8201d28c      0x110 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
                0x8201d28c                __swsetup
 .text          0x8201d39c      0x10c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
                0x8201d39c                _fclose_r
                0x8201d494                fclose
 .text          0x8201d4a8      0x1ac /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
                0x8201d4a8                fflush
 .text          0x8201d654      0x178 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
                0x8201d654                __smakebuf
 .text          0x8201d7cc      0x110 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
                0x8201d7cc                __udivsi3
                0x8201d7cc                __aeabi_uidiv
                0x8201d8c4                __aeabi_uidivmod
 .text          0x8201d8dc      0x140 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
                0x8201d8dc                __aeabi_idiv
                0x8201d8dc                __divsi3
                0x8201da04                __aeabi_idivmod
 .text          0x8201da1c       0xcc /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
                0x8201da1c                __umodsi3
 .text          0x8201dae8        0x4 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
                0x8201dae8                __aeabi_ldiv0
                0x8201dae8                __div0
                0x8201dae8                __aeabi_idiv0
 .text          0x8201daec      0x580 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
                0x8201db74                _Unwind_GetIPInfo
                0x8201db24                _Unwind_SjLj_Unregister
                0x8201db64                _Unwind_GetIP
                0x8201dba8                _Unwind_GetRegionStart
                0x8201dd28                _Unwind_Backtrace
                0x8201ded8                _Unwind_SjLj_ForcedUnwind
                0x8201db40                _Unwind_GetCFA
                0x8201daec                _Unwind_SjLj_Register
                0x8201dd04                _Unwind_DeleteException
                0x8201df34                _Unwind_SjLj_RaiseException
                0x8201de78                _Unwind_SjLj_Resume
                0x8201e010                _Unwind_SjLj_Resume_or_Rethrow
                0x8201db8c                _Unwind_SetIP
                0x8201dbc0                _Unwind_GetTextRelBase
                0x8201dbb0                _Unwind_FindEnclosingFunction
                0x8201db9c                _Unwind_GetLanguageSpecificData
                0x8201db50                _Unwind_SetGR
                0x8201db2c                _Unwind_GetGR
                0x8201dbb8                _Unwind_GetDataRelBase
 .text          0x8201e06c       0xd0 ./ALLLIB/libZip.a(compress.o)
                0x8201e06c                compress2
                0x8201e120                compress
 .text          0x8201e13c       0xac ./ALLLIB/libZip.a(uncompr.o)
                0x8201e13c                uncompress
 .text          0x8201e1e8     0x1d18 ./ALLLIB/libZip.a(deflate.o)
                0x8201e7c4                deflate
                0x8201f9cc                deflateSetDictionary
                0x8201fed0                deflateInit_
                0x8201fbfc                deflateInit2_
                0x8201fac8                deflateReset
                0x8201e534                deflateCopy
                0x8201eb04                deflateParams
                0x8201e210                deflateEnd
 .text          0x8201ff00     0x2320 ./ALLLIB/libZip.a(trees.o)
                0x82021b7c                _tr_flush_block
                0x82021a38                _tr_stored_block
                0x8201ffc4                _tr_init
                0x82020974                _tr_tally
                0x820216dc                _tr_align
 .text          0x82022220       0x54 ./ALLLIB/libZip.a(zutil.o)
                0x82022220                zlibVersion
                0x82022260                zcfree
                0x8202223c                zError
                0x82022268                zcalloc
 .text          0x82022274      0x928 ./ALLLIB/libZip.a(inflate.o)
                0x82022334                inflate
                0x82022274                inflateSyncPoint
                0x82022b8c                inflateInit_
                0x82022854                inflateReset
                0x820228ac                inflateSync
                0x820222a0                inflateSetDictionary
                0x820229dc                inflateEnd
                0x82022a40                inflateInit2_
 .text          0x82022b9c      0xfb0 ./ALLLIB/libZip.a(infblock.o)
                0x82023a3c                inflate_blocks_free
                0x82022bd8                inflate_blocks
                0x820239a0                inflate_blocks_reset
                0x82022b9c                inflate_blocks_sync_point
                0x82022bb0                inflate_set_dictionary
                0x82023a88                inflate_blocks_new
 .text          0x82023b4c      0x944 ./ALLLIB/libZip.a(inftrees.o)
                0x82024448                inflate_trees_fixed
                0x82024178                inflate_trees_bits
                0x82024268                inflate_trees_dynamic
 .text          0x82024490      0x910 ./ALLLIB/libZip.a(infcodes.o)
                0x82024490                inflate_codes_new
                0x820244fc                inflate_codes
                0x820244e0                inflate_codes_free
 .text          0x82024da0      0x164 ./ALLLIB/libZip.a(infutil.o)
                0x82024da0                inflate_flush
 .text          0x82024f04      0x514 ./ALLLIB/libZip.a(inffast.o)
                0x82024f04                inflate_fast
 .text          0x82025418      0x188 ./ALLLIB/libZip.a(adler32.o)
                0x82025418                adler32
 .text          0x820255a0     0xbd34 ./ALLLIB/libFile.a(Rtfiles.o)
                0x8202ea94                RTFFindFirst
                0x820265b4                _rtf_Get32
                0x82026508                RTFErrorMessage
                0x8202d308                RTFMakeFileName
                0x82029620                RTFGetFileInfo
                0x8202c908                RTFRead
                0x8202b020                RTFRawMediaChanged
                0x8202afd0                RTFRawDiscardSectors
                0x82026548                RTFSetCriticalErrorHandler
                0x8202c26c                RTFResetDisk
                0x8202ea80                RTFGetDiskInfo
                0x8202e65c                RTFGetDiskInfoEx
                0x8202935c                RTFGetFileInfoEx
                0x82028afc                RTFSetFileTime
                0x8202e0ac                RTFFindNextEx
                0x820287fc                RTFFindClose
                0x82030518                RTFMakeTempFileName
                0x82026564                _rtf_Get16
                0x8202a058                RTFSeek
                0x8202b130                RTFRawMount
                0x8202b284                RTFRawSetMedia
                0x82030d50                RTFOpen
                0x8202c610                RTFGetPartitionInfo
                0x8202e00c                RTFRawRead
                0x820286a0                RTFCloseAll
                0x8202e390                RTFFindFirstEx
                0x8202dbd4                RTFSetAttributes
                0x820256b0                RTFSetCodePage
                0x8202b0f0                RTFRawShutDown
                0x82027514                RTFBufferInfo
                0x820301a4                RTFCreateDir
                0x82029b14                RTFSeekEx
                0x8202ce5c                RTFExpandName
                0x82028230                RTFCommit
                0x8202af80                RTFRawGetDiskGeometry
                0x8202e370                RTFFindNext
                0x8202a128                RTFTruncate
                0x8202dd6c                RTFGetAttributes
                0x820265dc                RTFExtendedFileSize
                0x82028438                RTFClose
                0x8202c4d8                RTFCommitAll
                0x82025d24                RTFSetMessageHandler
                0x8202b060                RTFRawWrite
                0x8202873c                RTFShutDown
                0x8202b17c                RTFRawCallAuxDriverFunction
                0x820272c4                RTFGetFileSize
                0x82031270                RTFSetDefaultOpenFlags
                0x82026638                _rtf_Put32
                0x8202a410                RTFExtend
                0x8202de58                RTFDelete
                0x8202bcd8                RTFGetCurrentDir
                0x820308f0                RTFSetVolumeLabel
                0x8202da80                RTFSetCurrentDir
                0x8202d79c                RTFRemoveDir
                0x82026628                _rtf_Put16
                0x82025918                RTFUnicodeToChar
                0x8202ead4                RTFWrite
                0x820257c0                RTFCharToUnicode
                0x8202af28                RTFRawLowLevelFormat
                0x8202fcfc                RTFRename
                0x82025d40                RTFDiagMessage
                0x82026540                RTFDefaultCriticalErrorHandler
                0x82025c18                DiscardOtherBuffer
 .text          0x820312d4      0x380 ./ALLLIB/libFile.a(Rtfex.o)
                0x820313e4                _XInitBlock
                0x820312d4                _XHandled
                0x82031360                _XReExecute
                0x82031518                _XCloseBlockCanReturn
                0x82031618                _XHandler
                0x820315a4                _XCloseBlock
                0x820312ec                _XCloseBlockNoPropagate
                0x82031448                _XRaiseException
 .text          0x82031654        0x0 ./ALLLIB/libFile.a(Up437.o)
 .text          0x82031654        0x0 ./ALLLIB/libFile.a(Ut437.o)
 .text          0x82031654        0x0 ./ALLLIB/libFile.a(Rtfdata.o)
 .text          0x82031654        0x0 ./ALLLIB/libFile.a(Conf32.o)
 .text          0x82031654      0x2b0 ./ALLLIB/libFile.a(DrvSdCard.o)
                0x82031654                SetSdCardMountedModeInDriver
 .text          0x82031904      0x474 ./ALLLIB/libFile.a(SysUcos.o)
                0x82031a14                RTFSYSGetDateTime
                0x82031d40                CalcRtfTimeToRTOSTick
                0x82031af4                EnsableInterruptsInRTOS
                0x82031998                RTFSYSOwnMutex
                0x82031c28                _rtfLock
                0x820319a0                RTFSYSInstallIntHandler
                0x820319fc                RTFSYSDelay
                0x82031af0                RTFSYSGetTime
                0x82031b54                RTFSYSWaitSemaphore
                0x820319a4                RTFSYSErrorExit
                0x82031b10                DisableInterruptsInRTOS
                0x82031904                GetCurrentThreadIDinRTOS
                0x82031b80                RTFSYSFreeMutex
                0x82031bc4                RTFSYSAllocMutex
                0x82031c4c                RTFSYSSetTLS
                0x82031b34                RTFSYSSignalSemaphore
                0x820319a8                RTFSYSAllocTLS
                0x8203190c                RTFSYSGetTLS
                0x82031ba0                RTFSYSLockMutex
                0x82031c24                RTFSYSAllocSemaphore
                0x820319f0                RTFSYSGetDMABuffer
                0x820319ec                RTFSYSInstallTimerCallback
                0x820319f8                RTFSYSDiagMessage
 .text          0x82031d78      0x130 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
                0x82031d78                strchr
 .text          0x82031ea8       0x50 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
                0x82031ea8                strpbrk
 .text          0x82031ef8       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)
                0x82031ef8                strrchr
 *(.stub)
 *(.text.*)
 .text._ZN19jpeg_decoder_stream6attachEv
                0x82031f38        0x4 main.o
                0x82031f38                jpeg_decoder_stream::attach()
 .text._ZN19jpeg_decoder_stream6detachEv
                0x82031f3c        0x4 main.o
                0x82031f3c                jpeg_decoder_stream::detach()
 .text._ZN19jpeg_decoder_streamD0Ev
                0x82031f40       0x24 main.o
                0x82031f40                jpeg_decoder_stream::~jpeg_decoder_stream()
 .text._ZN19jpeg_decoder_streamD1Ev
                0x82031f64       0x24 main.o
                0x82031f64                jpeg_decoder_stream::~jpeg_decoder_stream()
 .text._ZN24jpeg_decoder_file_stream4readEPhiPb
                0x82031f88       0x64 main.o
                0x82031f88                jpeg_decoder_file_stream::read(unsigned char*, int, bool*)
 .text._ZN24jpeg_decoder_file_streamD0Ev
                0x82031fec       0x44 main.o
                0x82031fec                jpeg_decoder_file_stream::~jpeg_decoder_file_stream()
 .text._ZN24jpeg_decoder_file_streamD1Ev
                0x82032030       0x3c main.o
                0x82032030                jpeg_decoder_file_stream::~jpeg_decoder_file_stream()
 .text._Z12read_uleb128PKhPj
                0x8203206c       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text._Z12read_sleb128PKhPi
                0x82032094       0x48 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text._Z16get_adjusted_ptrPKSt9type_infoS1_PPv
                0x820320dc       0x70 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text._Z28read_encoded_value_with_basehjPKhPj
                0x8203214c      0x164 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text._Z21base_of_encoded_valuehP15_Unwind_Context
                0x820322b0       0x7c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text._Z17parse_lsda_headerP15_Unwind_ContextPKhP16lsda_header_info
                0x8203232c       0xb0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text._Z15get_ttype_entryP16lsda_header_infoj
                0x820323dc       0x7c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text._Z20check_exception_specP16lsda_header_infoPKSt9type_infoPvi
                0x82032458       0x70 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .text.__cxa_call_unexpected
                0x820324c8      0x180 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
                0x820324c8                __cxa_call_unexpected
 .text.__gxx_personality_sj0
                0x82032648      0x4e4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
                0x82032648                __gxx_personality_sj0
 .text._ZSt13set_terminatePFvvE
                0x82032b2c       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                0x82032b2c                std::set_terminate(void (*)())
 .text._ZSt14set_unexpectedPFvvE
                0x82032b44       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                0x82032b44                std::set_unexpected(void (*)())
 .text._ZN10__cxxabiv111__terminateEPFvvE
                0x82032b5c       0xb0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                0x82032b5c                __cxxabiv1::__terminate(void (*)())
 .text._ZSt9terminatev
                0x82032c0c       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                0x82032c0c                std::terminate()
 .text._ZN10__cxxabiv112__unexpectedEPFvvE
                0x82032c1c        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                0x82032c1c                __cxxabiv1::__unexpected(void (*)())
 .text._ZSt10unexpectedv
                0x82032c28       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                0x82032c28                std::unexpected()
 .text.__cxa_rethrow
                0x82032c38       0x78 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
                0x82032c38                __cxa_rethrow
 .text.__cxa_throw
                0x82032cb0       0x68 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
                0x82032cb0                __cxa_throw
 .text._Z23__gxx_exception_cleanup19_Unwind_Reason_CodeP17_Unwind_Exception
                0x82032d18       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .text.__cxa_pure_virtual
                0x82032d58       0x1c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
                0x82032d58                __cxa_pure_virtual
 .text._ZNKSt8bad_cast4whatEv
                0x82032d74        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032d74                std::bad_cast::what() const
 .text._ZNKSt10bad_typeid4whatEv
                0x82032d80        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032d80                std::bad_typeid::what() const
 .text._ZNKSt9type_info14__is_pointer_pEv
                0x82032d8c        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032d8c                std::type_info::__is_pointer_p() const
 .text._ZNKSt9type_info15__is_function_pEv
                0x82032d94        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032d94                std::type_info::__is_function_p() const
 .text._ZNKSt9type_info11__do_upcastEPKN10__cxxabiv117__class_type_infoEPPv
                0x82032d9c        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032d9c                std::type_info::__do_upcast(__cxxabiv1::__class_type_info const*, void**) const
 .text._ZNK10__cxxabiv117__class_type_info10__do_catchEPKSt9type_infoPPvj
                0x82032da4       0x44 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032da4                __cxxabiv1::__class_type_info::__do_catch(std::type_info const*, void**, unsigned int) const
 .text._ZNK10__cxxabiv117__class_type_info11__do_upcastEPKS0_PPv
                0x82032de8       0x5c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032de8                __cxxabiv1::__class_type_info::__do_upcast(__cxxabiv1::__class_type_info const*, void**) const
 .text._ZNK10__cxxabiv117__class_type_info20__do_find_public_srcElPKvPKS0_S2_
                0x82032e44       0x14 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032e44                __cxxabiv1::__class_type_info::__do_find_public_src(long, void const*, __cxxabiv1::__class_type_info const*, void const*) const
 .text._ZNK10__cxxabiv117__class_type_info11__do_upcastEPKS0_PKvRNS0_15__upcast_resultE
                0x82032e58       0x30 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032e58                __cxxabiv1::__class_type_info::__do_upcast(__cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info::__upcast_result&) const
 .text._ZNK10__cxxabiv120__si_class_type_info11__do_upcastEPKNS_17__class_type_infoEPKvRNS1_15__upcast_resultE
                0x82032e88       0x48 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032e88                __cxxabiv1::__si_class_type_info::__do_upcast(__cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info::__upcast_result&) const
 .text._ZNSt9type_infoD0Ev
                0x82032ed0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032ed0                std::type_info::~type_info()
 .text._ZNSt9type_infoD1Ev
                0x82032ee0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032ee0                std::type_info::~type_info()
 .text._ZNSt9type_infoD2Ev
                0x82032ef0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032ef0                std::type_info::~type_info()
 .text._ZN10__cxxabiv117__class_type_infoD0Ev
                0x82032f00       0x24 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032f00                __cxxabiv1::__class_type_info::~__class_type_info()
 .text._ZN10__cxxabiv117__class_type_infoD1Ev
                0x82032f24       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032f24                __cxxabiv1::__class_type_info::~__class_type_info()
 .text._ZN10__cxxabiv117__class_type_infoD2Ev
                0x82032f34       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032f34                __cxxabiv1::__class_type_info::~__class_type_info()
 .text._ZN10__cxxabiv121__vmi_class_type_infoD0Ev
                0x82032f44       0x24 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032f44                __cxxabiv1::__vmi_class_type_info::~__vmi_class_type_info()
 .text._ZN10__cxxabiv121__vmi_class_type_infoD1Ev
                0x82032f68       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032f68                __cxxabiv1::__vmi_class_type_info::~__vmi_class_type_info()
 .text._ZN10__cxxabiv121__vmi_class_type_infoD2Ev
                0x82032f78       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032f78                __cxxabiv1::__vmi_class_type_info::~__vmi_class_type_info()
 .text._ZN10__cxxabiv120__si_class_type_infoD0Ev
                0x82032f88       0x24 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032f88                __cxxabiv1::__si_class_type_info::~__si_class_type_info()
 .text._ZN10__cxxabiv120__si_class_type_infoD1Ev
                0x82032fac       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032fac                __cxxabiv1::__si_class_type_info::~__si_class_type_info()
 .text._ZN10__cxxabiv120__si_class_type_infoD2Ev
                0x82032fbc       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032fbc                __cxxabiv1::__si_class_type_info::~__si_class_type_info()
 .text._ZNSt10bad_typeidD0Ev
                0x82032fcc       0x24 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032fcc                std::bad_typeid::~bad_typeid()
 .text._ZNSt10bad_typeidD1Ev
                0x82032ff0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82032ff0                std::bad_typeid::~bad_typeid()
 .text._ZNSt10bad_typeidD2Ev
                0x82033000       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82033000                std::bad_typeid::~bad_typeid()
 .text._ZNSt8bad_castD0Ev
                0x82033010       0x24 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82033010                std::bad_cast::~bad_cast()
 .text._ZNSt8bad_castD1Ev
                0x82033034       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82033034                std::bad_cast::~bad_cast()
 .text._ZNSt8bad_castD2Ev
                0x82033044       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82033044                std::bad_cast::~bad_cast()
 .text._ZNKSt9type_info10__do_catchEPKS_PPvj
                0x82033054       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82033054                std::type_info::__do_catch(std::type_info const*, void**, unsigned int) const
 .text._ZNK10__cxxabiv117__class_type_info12__do_dyncastElNS0_10__sub_kindEPKS0_PKvS3_S5_RNS0_16__dyncast_resultE
                0x8203306c       0x5c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x8203306c                __cxxabiv1::__class_type_info::__do_dyncast(long, __cxxabiv1::__class_type_info::__sub_kind, __cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info::__dyncast_result&) const
 .text._ZNK10__cxxabiv120__si_class_type_info20__do_find_public_srcElPKvPKNS_17__class_type_infoES2_
                0x820330c8       0x58 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x820330c8                __cxxabiv1::__si_class_type_info::__do_find_public_src(long, void const*, __cxxabiv1::__class_type_info const*, void const*) const
 .text._ZNK10__cxxabiv120__si_class_type_info12__do_dyncastElNS_17__class_type_info10__sub_kindEPKS1_PKvS4_S6_RNS1_16__dyncast_resultE
                0x82033120       0xc4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82033120                __cxxabiv1::__si_class_type_info::__do_dyncast(long, __cxxabiv1::__class_type_info::__sub_kind, __cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info::__dyncast_result&) const
 .text._ZNK10__cxxabiv121__vmi_class_type_info20__do_find_public_srcElPKvPKNS_17__class_type_infoES2_
                0x820331e4       0xc4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x820331e4                __cxxabiv1::__vmi_class_type_info::__do_find_public_src(long, void const*, __cxxabiv1::__class_type_info const*, void const*) const
 .text._ZNK10__cxxabiv121__vmi_class_type_info11__do_upcastEPKNS_17__class_type_infoEPKvRNS1_15__upcast_resultE
                0x820332a8      0x1ec /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x820332a8                __cxxabiv1::__vmi_class_type_info::__do_upcast(__cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info::__upcast_result&) const
 .text.__dynamic_cast
                0x82033494      0x120 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82033494                __dynamic_cast
 .text._ZNK10__cxxabiv121__vmi_class_type_info12__do_dyncastElNS_17__class_type_info10__sub_kindEPKS1_PKvS4_S6_RNS1_16__dyncast_resultE
                0x820335b4      0x4bc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x820335b4                __cxxabiv1::__vmi_class_type_info::__do_dyncast(long, __cxxabiv1::__class_type_info::__sub_kind, __cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info const*, void const*, __cxxabiv1::__class_type_info::__dyncast_result&) const
 .text._ZN9__gnu_cxx27__verbose_terminate_handlerEv
                0x82033a70      0x270 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
                0x82033a70                __gnu_cxx::__verbose_terminate_handler()
 .text._Z41__static_initialization_and_destruction_0ii
                0x82033ce0        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .text._GLOBAL__I__ZN85_GLOBAL__N_.._.._.._.._gcc_4.2.2_libstdc___v3_libsupc___eh_alloc.cc_00000000_1D353EDC15emergency_mutexE
                0x82033ce4       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .text.__cxa_allocate_exception
                0x82033cf4      0x138 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
                0x82033cf4                __cxa_allocate_exception
 .text.__cxa_free_exception
                0x82033e2c       0x50 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
                0x82033e2c                __cxa_free_exception
 .text.__cxa_call_terminate
                0x82033e7c       0x48 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
                0x82033e7c                __cxa_call_terminate
 .text.__cxa_get_exception_ptr
                0x82033ec4        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
                0x82033ec4                __cxa_get_exception_ptr
 .text._ZSt18uncaught_exceptionv
                0x82033ecc       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
                0x82033ecc                std::uncaught_exception()
 .text.__cxa_end_catch
                0x82033ee4       0xa8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
                0x82033ee4                __cxa_end_catch
 .text.__cxa_begin_catch
                0x82033f8c      0x118 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
                0x82033f8c                __cxa_begin_catch
 .text._ZNKSt9exception4whatEv
                0x820340a4        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820340a4                std::exception::what() const
 .text._ZNKSt13bad_exception4whatEv
                0x820340b0        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820340b0                std::bad_exception::what() const
 .text._ZNSt9exceptionD0Ev
                0x820340bc       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820340bc                std::exception::~exception()
 .text._ZNSt9exceptionD1Ev
                0x820340cc       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820340cc                std::exception::~exception()
 .text._ZNSt9exceptionD2Ev
                0x820340dc       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820340dc                std::exception::~exception()
 .text._ZNSt13bad_exceptionD0Ev
                0x820340ec       0x24 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820340ec                std::bad_exception::~bad_exception()
 .text._ZNSt13bad_exceptionD1Ev
                0x82034110       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x82034110                std::bad_exception::~bad_exception()
 .text._ZNSt13bad_exceptionD2Ev
                0x82034120       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x82034120                std::bad_exception::~bad_exception()
 .text.__cxa_get_globals_fast
                0x82034130        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
                0x82034130                __cxa_get_globals_fast
 .text.__cxa_get_globals
                0x8203413c        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
                0x8203413c                __cxa_get_globals
 .text.__cxa_current_exception_type
                0x82034148       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
                0x82034148                __cxa_current_exception_type
 *(.rodata)
 .rodata        0x82034160     0x19c4 COMLIB/ComLib.o
 .rodata        0x82035b24       0x2c SYSLIB/SysLib.o
 .rodata        0x82035b50      0x400 DEVLIB/Nand64.o
 .rodata        0x82035f50       0x40 DEVLIB/SysAlive.o
 .rodata        0x82035f90      0x200 JPGLIB/jpegdecr.o
 .rodata        0x82036190      0x5ec /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .rodata        0x8203677c        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
                0x8203677c                _global_impure_ptr
 .rodata        0x82036780      0x101 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
                0x82036780                _ctype_
 *fill*         0x82036881        0x3 00
 .rodata        0x82036884       0x38 ./ALLLIB/libZip.a(deflate.o)
                0x82036884                deflate_copyright
 .rodata        0x820368bc      0xf28 ./ALLLIB/libZip.a(trees.o)
                0x82036abc                _length_code
                0x820368bc                _dist_code
 .rodata        0x820377e4        0x4 ./ALLLIB/libZip.a(inflate.o)
 .rodata        0x820377e8       0x4c ./ALLLIB/libZip.a(infblock.o)
 .rodata        0x82037834      0x218 ./ALLLIB/libZip.a(inftrees.o)
                0x82037834                inflate_copyright
 .rodata        0x82037a4c       0x70 ./ALLLIB/libFile.a(Rtfiles.o)
 .rodata        0x82037abc       0x84 ./ALLLIB/libFile.a(Up437.o)
                0x82037abc                _rtfUpper437
 .rodata        0x82037b40      0x100 ./ALLLIB/libFile.a(Ut437.o)
                0x82037b40                _rtfUnicodeMap437
 *(.rodata*)
 .rodata.str1.4
                0x82037c40        0x3 FILELIB/osfile.o
                                  0x4 (size before relaxing)
 *fill*         0x82037c43        0x1 00
 .rodata.str1.4
                0x82037c44       0x41 main.o
                                 0x48 (size before relaxing)
 *fill*         0x82037c85        0x3 00
 .rodata._ZTS24jpeg_decoder_file_stream
                0x82037c88       0x1c main.o
                0x82037c88                typeinfo name for jpeg_decoder_file_stream
 .rodata._ZTS19jpeg_decoder_stream
                0x82037ca4       0x18 main.o
                0x82037ca4                typeinfo name for jpeg_decoder_stream
 .rodata.str1.4
                0x82037cbc       0x1c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .rodata.str1.4
                0x82037cd8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .rodata._ZTVN10__cxxabiv121__vmi_class_type_infoE
                0x82037cf8       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037cf8                vtable for __cxxabiv1::__vmi_class_type_info
 .rodata._ZTVN10__cxxabiv120__si_class_type_infoE
                0x82037d24       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037d24                vtable for __cxxabiv1::__si_class_type_info
 .rodata._ZTVN10__cxxabiv117__class_type_infoE
                0x82037d50       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037d50                vtable for __cxxabiv1::__class_type_info
 .rodata._ZTVSt10bad_typeid
                0x82037d7c       0x14 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037d7c                vtable for std::bad_typeid
 .rodata._ZTVSt8bad_cast
                0x82037d90       0x14 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037d90                vtable for std::bad_cast
 .rodata._ZTVSt9type_info
                0x82037da4       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037da4                vtable for std::type_info
 .rodata._ZTSN10__cxxabiv121__vmi_class_type_infoE
                0x82037dc4       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037dc4                typeinfo name for __cxxabiv1::__vmi_class_type_info
 .rodata._ZTIN10__cxxabiv121__vmi_class_type_infoE
                0x82037dec        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037dec                typeinfo for __cxxabiv1::__vmi_class_type_info
 .rodata._ZTSN10__cxxabiv120__si_class_type_infoE
                0x82037df8       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037df8                typeinfo name for __cxxabiv1::__si_class_type_info
 .rodata._ZTIN10__cxxabiv120__si_class_type_infoE
                0x82037e20        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e20                typeinfo for __cxxabiv1::__si_class_type_info
 .rodata._ZTSN10__cxxabiv117__class_type_infoE
                0x82037e2c       0x24 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e2c                typeinfo name for __cxxabiv1::__class_type_info
 .rodata._ZTIN10__cxxabiv117__class_type_infoE
                0x82037e50        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e50                typeinfo for __cxxabiv1::__class_type_info
 .rodata._ZTSSt10bad_typeid
                0x82037e5c       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e5c                typeinfo name for std::bad_typeid
 .rodata._ZTISt10bad_typeid
                0x82037e6c        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e6c                typeinfo for std::bad_typeid
 .rodata._ZTSSt8bad_cast
                0x82037e78        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e78                typeinfo name for std::bad_cast
 .rodata._ZTISt8bad_cast
                0x82037e84        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e84                typeinfo for std::bad_cast
 .rodata._ZTSSt9type_info
                0x82037e90       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037e90                typeinfo name for std::type_info
 .rodata._ZTISt9type_info
                0x82037ea0        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                0x82037ea0                typeinfo for std::type_info
 .rodata.str1.4
                0x82037ea8       0x92 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
                                 0x94 (size before relaxing)
 *fill*         0x82037f3a        0x2 00
 .rodata.str1.4
                0x82037f3c      0x5fc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
                                0x630 (size before relaxing)
 .rodata.str1.4
                0x82038538       0x23 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                                 0x24 (size before relaxing)
 *fill*         0x8203855b        0x1 00
 .rodata._ZTVSt13bad_exception
                0x8203855c       0x14 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x8203855c                vtable for std::bad_exception
 .rodata._ZTVSt9exception
                0x82038570       0x14 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x82038570                vtable for std::exception
 .rodata._ZTSSt13bad_exception
                0x82038584       0x14 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x82038584                typeinfo name for std::bad_exception
 .rodata._ZTISt13bad_exception
                0x82038598        0xc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x82038598                typeinfo for std::bad_exception
 .rodata._ZTSSt9exception
                0x820385a4       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820385a4                typeinfo name for std::exception
 .rodata._ZTISt9exception
                0x820385b4        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                0x820385b4                typeinfo for std::exception
 .rodata.str1.4
                0x820385bc        0x2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
                                  0x4 (size before relaxing)
 *fill*         0x820385be        0x2 00
 .rodata.str1.4
                0x820385c0        0x6 ./ALLLIB/libZip.a(compress.o)
                                  0x8 (size before relaxing)
 .rodata.str1.4
                0x00000000        0x8 ./ALLLIB/libZip.a(uncompr.o)
 .rodata.str1.4
                0x00000000        0x8 ./ALLLIB/libZip.a(deflate.o)
 *fill*         0x820385c6        0x2 00
 .rodata.str1.4
                0x820385c8       0x7d ./ALLLIB/libZip.a(zutil.o)
                                 0x8c (size before relaxing)
 *fill*         0x82038645        0x3 00
 .rodata.str1.4
                0x82038648       0x5d ./ALLLIB/libZip.a(inflate.o)
                                 0x70 (size before relaxing)
 *fill*         0x820386a5        0x3 00
 .rodata.str1.4
                0x820386a8       0x72 ./ALLLIB/libZip.a(infblock.o)
                                 0x74 (size before relaxing)
 *fill*         0x8203871a        0x2 00
 .rodata.str1.4
                0x8203871c       0xed ./ALLLIB/libZip.a(inftrees.o)
                                 0xf0 (size before relaxing)
 *fill*         0x82038809        0x3 00
 .rodata.str1.4
                0x8203880c       0x32 ./ALLLIB/libZip.a(infcodes.o)
                                 0x34 (size before relaxing)
 .rodata.str1.4
                0x00000000       0x34 ./ALLLIB/libZip.a(inffast.o)
 *fill*         0x8203883e        0x2 00
 .rodata.str1.4
                0x82038840      0x419 ./ALLLIB/libFile.a(Rtfiles.o)
                                0x42c (size before relaxing)
 *fill*         0x82038c59        0x3 00
 .rodata.str1.4
                0x82038c5c       0x40 ./ALLLIB/libFile.a(Rtfex.o)
 .rodata.str1.4
                0x82038c9c       0x6c ./ALLLIB/libFile.a(SysUcos.o)
 *(.gnu.warning)
 *(.gnu.linkonce.r*)
 *(.gnu.linkonce.t*)
 *(.glue_7t)
 .glue_7t       0x82038d08        0x0 start.o
 .glue_7t       0x82038d08        0x0 COMLIB/ComLib.o
 .glue_7t       0x82038d08        0x0 SYSLIB/SysLib.o
 .glue_7t       0x82038d08        0x0 SYSLIB/SysIntr.o
 .glue_7t       0x82038d08        0x0 DEVLIB/Nand64.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysMMU.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysPLL.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysMCUD.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysMCUS.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysGPIO.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysAlive.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysTimer.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysUART.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysMLC.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysDPC.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysLCD.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysPWM.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SysSDCD.o
 .glue_7t       0x82038d08        0x0 DEVLIB/SdCardDRV.o
 .glue_7t       0x82038d08        0x0 DEVLIB/DataBack.o
 .glue_7t       0x82038d08        0x0 FILELIB/osfile.o
 .glue_7t       0x82038d08        0x0 JPGLIB/jpegdecr.o
 .glue_7t       0x82038d08        0x0 JPGLIB/jpegidct.o
 .glue_7t       0x82038d08        0x0 JPGLIB/jpegh2v2.o
 .glue_7t       0x82038d08        0x0 main.o
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(compress.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(uncompr.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(deflate.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(trees.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(zutil.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(inflate.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(infblock.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(inftrees.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(infcodes.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(infutil.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(inffast.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libZip.a(adler32.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfiles.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfex.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(Up437.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(Ut437.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfdata.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(Conf32.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(DrvSdCard.o)
 .glue_7t       0x82038d08        0x0 ./ALLLIB/libFile.a(SysUcos.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .glue_7t       0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)
 *(.glue_7)
 .glue_7        0x82038d08        0x0 start.o
 .glue_7        0x82038d08        0x0 COMLIB/ComLib.o
 .glue_7        0x82038d08        0x0 SYSLIB/SysLib.o
 .glue_7        0x82038d08        0x0 SYSLIB/SysIntr.o
 .glue_7        0x82038d08        0x0 DEVLIB/Nand64.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysMMU.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysPLL.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysMCUD.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysMCUS.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysGPIO.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysAlive.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysTimer.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysUART.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysMLC.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysDPC.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysLCD.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysPWM.o
 .glue_7        0x82038d08        0x0 DEVLIB/SysSDCD.o
 .glue_7        0x82038d08        0x0 DEVLIB/SdCardDRV.o
 .glue_7        0x82038d08        0x0 DEVLIB/DataBack.o
 .glue_7        0x82038d08        0x0 FILELIB/osfile.o
 .glue_7        0x82038d08        0x0 JPGLIB/jpegdecr.o
 .glue_7        0x82038d08        0x0 JPGLIB/jpegidct.o
 .glue_7        0x82038d08        0x0 JPGLIB/jpegh2v2.o
 .glue_7        0x82038d08        0x0 main.o
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(compress.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(uncompr.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(deflate.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(trees.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(zutil.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(inflate.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(infblock.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(inftrees.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(infcodes.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(infutil.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(inffast.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libZip.a(adler32.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfiles.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfex.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(Up437.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(Ut437.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfdata.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(Conf32.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(DrvSdCard.o)
 .glue_7        0x82038d08        0x0 ./ALLLIB/libFile.a(SysUcos.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .glue_7        0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.vfp11_veneer   0x82038d08        0x0
 .vfp11_veneer  0x82038d08        0x0 start.o
 .vfp11_veneer  0x82038d08        0x0 COMLIB/ComLib.o
 .vfp11_veneer  0x82038d08        0x0 SYSLIB/SysLib.o
 .vfp11_veneer  0x82038d08        0x0 SYSLIB/SysIntr.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/Nand64.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysMMU.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysPLL.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysMCUD.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysMCUS.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysGPIO.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysAlive.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysTimer.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysUART.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysMLC.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysDPC.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysLCD.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysPWM.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SysSDCD.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/SdCardDRV.o
 .vfp11_veneer  0x82038d08        0x0 DEVLIB/DataBack.o
 .vfp11_veneer  0x82038d08        0x0 FILELIB/osfile.o
 .vfp11_veneer  0x82038d08        0x0 JPGLIB/jpegdecr.o
 .vfp11_veneer  0x82038d08        0x0 JPGLIB/jpegidct.o
 .vfp11_veneer  0x82038d08        0x0 JPGLIB/jpegh2v2.o
 .vfp11_veneer  0x82038d08        0x0 main.o
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(compress.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(uncompr.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(deflate.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(trees.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(zutil.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(inflate.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(infblock.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(inftrees.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(infcodes.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(infutil.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(inffast.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libZip.a(adler32.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfiles.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfex.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(Up437.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(Ut437.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(Rtfdata.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(Conf32.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(DrvSdCard.o)
 .vfp11_veneer  0x82038d08        0x0 ./ALLLIB/libFile.a(SysUcos.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .vfp11_veneer  0x82038d08        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.rel.dyn        0x82038d08        0x0
 *(.rel.init)
 *(.rel.text .rel.text.* .rel.gnu.linkonce.t.*)
 *(.rel.fini)
 *(.rel.rodata .rel.rodata.* .rel.gnu.linkonce.r.*)
 *(.rel.data .rel.data.* .rel.gnu.linkonce.d.*)
 *(.rel.tdata .rel.tdata.* .rel.gnu.linkonce.td.*)
 *(.rel.tbss .rel.tbss.* .rel.gnu.linkonce.tb.*)
 *(.rel.ctors)
 *(.rel.dtors)
 *(.rel.got)
 .rel.got       0x00000000        0x0 COMLIB/ComLib.o
 *(.rel.bss .rel.bss.* .rel.gnu.linkonce.b.*)
                0x00000000                __end_of_text__ = .

.data           0x82038d08     0x23fc
                0x82038d08                __data_beg__ = .
                0x82038d08                __data_beg_src__ = __end_of_text__
 *(.data)
 .data          0x82038d08        0x0 start.o
 .data          0x82038d08        0x0 COMLIB/ComLib.o
 .data          0x82038d08       0x2c SYSLIB/SysLib.o
                0x82038d10                G_nSysBrightPercntVal
                0x82038d14                G_nSysBrightFactorVal
                0x82038d08                G_cSysAlphaBlendValue
                0x82038d0c                G_dSysAlphaBlendValue
 .data          0x82038d34        0x0 SYSLIB/SysIntr.o
 .data          0x82038d34       0x14 DEVLIB/Nand64.o
                0x82038d3c                G_pNandADDR
                0x82038d34                G_pNandDATA
                0x82038d38                G_pNandCMND
 .data          0x82038d48        0x0 DEVLIB/SysMMU.o
 .data          0x82038d48        0x0 DEVLIB/SysPLL.o
 .data          0x82038d48        0x0 DEVLIB/SysMCUD.o
 .data          0x82038d48        0x0 DEVLIB/SysMCUS.o
 .data          0x82038d48      0x514 DEVLIB/SysGPIO.o
                0x82038d48                G_pSysGPIOA
                0x82038d4c                G_pSysGPIOB
                0x82038d58                G_pSysGPIOE
                0x82038d50                G_pSysGPIOC
                0x82038d54                G_pSysGPIOD
 .data          0x8203925c        0x0 DEVLIB/SysAlive.o
 .data          0x8203925c        0x0 DEVLIB/SysTimer.o
 .data          0x8203925c        0x0 DEVLIB/SysUART.o
 .data          0x8203925c        0x0 DEVLIB/SysMLC.o
 .data          0x8203925c        0x0 DEVLIB/SysDPC.o
 .data          0x8203925c        0x0 DEVLIB/SysLCD.o
 .data          0x8203925c        0x0 DEVLIB/SysPWM.o
 .data          0x8203925c        0x4 DEVLIB/SysSDCD.o
 .data          0x82039260        0x0 DEVLIB/SdCardDRV.o
 .data          0x82039260        0x0 DEVLIB/DataBack.o
 .data          0x82039260        0x0 FILELIB/osfile.o
 .data          0x82039260        0x0 JPGLIB/jpegdecr.o
 .data          0x82039260        0x0 JPGLIB/jpegidct.o
 .data          0x82039260        0x0 JPGLIB/jpegh2v2.o
 .data          0x82039260        0xa main.o
                0x82039264                G_wAdcLcdAdjustModeV
                0x82039260                G_wAdcReferenceValue
                0x82039262                G_wLcd50FreqDvdValue
                0x82039266                G_wLcd50fAdjustModeV
                0x82039268                G_wNavisModelType
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .data          0x8203926a        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 *fill*         0x8203926a        0x2 00
 .data          0x8203926c      0x404 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
                0x8203926c                _impure_ptr
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .data          0x82039670        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .data          0x82039670        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
                0x82039670                __ctype_ptr
 .data          0x82039674        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .data          0x82039674        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .data          0x82039674        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .data          0x82039674        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .data          0x82039674      0x410 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
                0x82039a80                __malloc_sbrk_base
                0x82039674                __malloc_av_
                0x82039a7c                __malloc_trim_threshold
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .data          0x82039a84        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .data          0x82039a84        0x0 ./ALLLIB/libZip.a(compress.o)
 .data          0x82039a84        0x0 ./ALLLIB/libZip.a(uncompr.o)
 .data          0x82039a84        0x0 ./ALLLIB/libZip.a(deflate.o)
 .data          0x82039a84        0x0 ./ALLLIB/libZip.a(trees.o)
 .data          0x82039a84        0x0 ./ALLLIB/libZip.a(zutil.o)
 .data          0x82039a84        0x0 ./ALLLIB/libZip.a(inflate.o)
 .data          0x82039a84        0x0 ./ALLLIB/libZip.a(infblock.o)
 .data          0x82039a84     0x1100 ./ALLLIB/libZip.a(inftrees.o)
 .data          0x8203ab84        0x0 ./ALLLIB/libZip.a(infcodes.o)
 .data          0x8203ab84       0x44 ./ALLLIB/libZip.a(infutil.o)
                0x8203ab84                inflate_mask
 .data          0x8203abc8        0x0 ./ALLLIB/libZip.a(inffast.o)
 .data          0x8203abc8        0x0 ./ALLLIB/libZip.a(adler32.o)
 .data          0x8203abc8        0x8 ./ALLLIB/libFile.a(Rtfiles.o)
 .data          0x8203abd0        0x4 ./ALLLIB/libFile.a(Rtfex.o)
                0x8203abd0                _XTlsIndex
 .data          0x8203abd4        0x0 ./ALLLIB/libFile.a(Up437.o)
 .data          0x8203abd4        0x0 ./ALLLIB/libFile.a(Ut437.o)
 .data          0x8203abd4        0x0 ./ALLLIB/libFile.a(Rtfdata.o)
 .data          0x8203abd4        0x0 ./ALLLIB/libFile.a(Conf32.o)
 .data          0x8203abd4        0x4 ./ALLLIB/libFile.a(DrvSdCard.o)
 .data          0x8203abd8        0x0 ./ALLLIB/libFile.a(SysUcos.o)
 .data          0x8203abd8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .data          0x8203abd8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .data          0x8203abd8        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)
 *(.data.rel*)
 .data.rel.ro   0x8203abd8      0x100 SYSLIB/SysIntr.o
 .data.rel.ro._ZTV24jpeg_decoder_file_stream
                0x8203acd8       0x1c main.o
                0x8203acd8                vtable for jpeg_decoder_file_stream
 .data.rel.ro._ZTI24jpeg_decoder_file_stream
                0x8203acf4        0xc main.o
                0x8203acf4                typeinfo for jpeg_decoder_file_stream
 .data.rel.ro._ZTI19jpeg_decoder_stream
                0x8203ad00        0x8 main.o
                0x8203ad00                typeinfo for jpeg_decoder_stream
 .data.rel.ro._ZTV19jpeg_decoder_stream
                0x8203ad08       0x1c main.o
                0x8203ad08                vtable for jpeg_decoder_stream
 .data.rel.ro.local
                0x8203ad24       0x7c ./ALLLIB/libZip.a(deflate.o)
 .data.rel.local
                0x8203ada0       0x3c ./ALLLIB/libZip.a(trees.o)
 .data.rel.local
                0x8203addc       0x28 ./ALLLIB/libZip.a(zutil.o)
                0x8203addc                z_errmsg
 .data.rel      0x8203ae04       0x10 ./ALLLIB/libFile.a(Rtfiles.o)
 .data.rel.ro.local
                0x8203ae14       0xac ./ALLLIB/libFile.a(Rtfiles.o)
 .data.rel.local
                0x8203aec0       0x34 ./ALLLIB/libFile.a(Rtfdata.o)
                0x8203aec0                RTFData
 .data.rel      0x8203aef4       0xa8 ./ALLLIB/libFile.a(Conf32.o)
                0x8203aef4                RTFDeviceList
 .data.rel.local
                0x8203af9c       0x24 ./ALLLIB/libFile.a(DrvSdCard.o)
                0x8203af9c                RTFDrvSdCard
 *(.got*)
 .got           0x8203afc0       0xc8 COMLIB/ComLib.o
 .got.plt       0x8203b088        0xc COMLIB/ComLib.o
                0x8203b088                _GLOBAL_OFFSET_TABLE_
 *(.gcc_except_table*)
 .gcc_except_table
                0x8203b094        0x6 main.o
 *fill*         0x8203b09a        0x2 00
 .gcc_except_table
                0x8203b09c       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .gcc_except_table
                0x8203b0bc       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .gcc_except_table
                0x8203b0cc       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .gcc_except_table
                0x8203b0e4       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .gcc_except_table
                0x8203b0f4       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 *(.gnu.linkonce.d*)
 *(.rel.dyn)
                0x8203b104                __IRQ_HANDLER_VECT = .
                0x8203b104                __data_end__ = .

.data._ZN10__cxxabiv119__terminate_handlerE
                0x8203b104        0x4
 .data._ZN10__cxxabiv119__terminate_handlerE
                0x8203b104        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
                0x8203b104                __cxxabiv1::__terminate_handler

.data._ZN10__cxxabiv120__unexpected_handlerE
                0x8203b108        0x4
 .data._ZN10__cxxabiv120__unexpected_handlerE
                0x8203b108        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
                0x8203b108                __cxxabiv1::__unexpected_handler

.ctors          0x8203b10c        0x4
 .ctors         0x8203b10c        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)

.bss            0x8203b110    0x2efdc
                0x8203b110                __bss_beg__ = .
 *(.bss)
 .bss           0x8203b110        0x0 start.o
 .bss           0x8203b110      0x830 COMLIB/ComLib.o
 .bss           0x8203b940      0x548 SYSLIB/SysLib.o
                0x8203b940                G_dSystemTickCounter
                0x8203b950                G_vMapPaletteDataUCHAR
                0x8203bc50                G_vMapPaletteDataHWORD
                0x8203be50                G_nNavis5100AModelMode
                0x8203b94c                G_nSysPaletteDataSize
                0x8203b948                G_pSysPaletteDataHWORD
                0x8203b944                G_pSysPaletteDataUCHAR
 .bss           0x8203be88        0x0 SYSLIB/SysIntr.o
 .bss           0x8203be88     0x1804 DEVLIB/Nand64.o
 .bss           0x8203d68c        0x0 DEVLIB/SysMMU.o
 .bss           0x8203d68c        0x0 DEVLIB/SysPLL.o
 .bss           0x8203d68c        0x0 DEVLIB/SysMCUD.o
 .bss           0x8203d68c        0x0 DEVLIB/SysMCUS.o
 .bss           0x8203d68c        0x0 DEVLIB/SysGPIO.o
 .bss           0x8203d68c        0x0 DEVLIB/SysAlive.o
 .bss           0x8203d68c        0x0 DEVLIB/SysTimer.o
 .bss           0x8203d68c        0x0 DEVLIB/SysUART.o
 .bss           0x8203d68c        0x0 DEVLIB/SysMLC.o
 .bss           0x8203d68c        0x0 DEVLIB/SysDPC.o
 .bss           0x8203d68c        0x0 DEVLIB/SysLCD.o
 .bss           0x8203d68c        0x0 DEVLIB/SysPWM.o
 .bss           0x8203d68c      0x110 DEVLIB/SysSDCD.o
 .bss           0x8203d79c        0x4 DEVLIB/SdCardDRV.o
 .bss           0x8203d7a0        0x0 DEVLIB/DataBack.o
 .bss           0x8203d7a0        0x8 FILELIB/osfile.o
 .bss           0x8203d7a8        0x0 JPGLIB/jpegdecr.o
 .bss           0x8203d7a8        0x0 JPGLIB/jpegidct.o
 .bss           0x8203d7a8        0x0 JPGLIB/jpegh2v2.o
 .bss           0x8203d7a8       0x70 main.o
                0x8203d7d8                G_vUserPassWord
                0x8203d7b8                G_vDeviceOutput
                0x8203d7ea                G_nBaseGainOff
                0x8203d7ee                G_wSGP330ScrnResMode
                0x8203d7f0                G_wSonarSpicaGainTbl
                0x8203d7c8                G_vDeviceGpsAnt
                0x8203d7a8                G_vDeviceSerial
                0x8203d7e8                G_wSysUsingMap
                0x8203d7f4                G_dBackDataReBuildMode
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .bss           0x8203d818        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .bss           0x8203d818       0x34 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
                0x8203d818                __malloc_top_pad
                0x8203d81c                __malloc_max_sbrked_mem
                0x8203d824                __malloc_current_mallinfo
                0x8203d820                __malloc_max_total_mem
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .bss           0x8203d84c        0x0 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .bss           0x8203d84c        0x4 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(compress.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(uncompr.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(deflate.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(trees.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(zutil.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(inflate.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(infblock.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(inftrees.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(infcodes.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(infutil.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(inffast.o)
 .bss           0x8203d850        0x0 ./ALLLIB/libZip.a(adler32.o)
 .bss           0x8203d850       0x50 ./ALLLIB/libFile.a(Rtfiles.o)
 .bss           0x8203d8a0        0x0 ./ALLLIB/libFile.a(Rtfex.o)
 .bss           0x8203d8a0        0x0 ./ALLLIB/libFile.a(Up437.o)
 .bss           0x8203d8a0        0x0 ./ALLLIB/libFile.a(Ut437.o)
 .bss           0x8203d8a0    0x2c380 ./ALLLIB/libFile.a(Rtfdata.o)
 .bss           0x82069c20       0x1c ./ALLLIB/libFile.a(Conf32.o)
 .bss           0x82069c3c        0x0 ./ALLLIB/libFile.a(DrvSdCard.o)
 .bss           0x82069c3c      0x4b0 ./ALLLIB/libFile.a(SysUcos.o)
 .bss           0x8206a0ec        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .bss           0x8206a0ec        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .bss           0x8206a0ec        0x0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.bss._ZZN9__gnu_cxx27__verbose_terminate_handlerEvE11terminating
                0x8206a0ec        0x1
 .bss._ZZN9__gnu_cxx27__verbose_terminate_handlerEvE11terminating
                0x8206a0ec        0x1 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)

.bss._ZN85_GLOBAL__N_.._.._.._.._gcc_4.2.2_libstdc___v3_libsupc___eh_alloc.cc_00000000_1D353EDC15emergency_mutexE
                0x8206a0f0        0x4
 .bss._ZN85_GLOBAL__N_.._.._.._.._gcc_4.2.2_libstdc___v3_libsupc___eh_alloc.cc_00000000_1D353EDC15emergency_mutexE
                0x8206a0f0        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)

.bss.emergency_buffer
                0x8206a0f4      0x800
 .bss.emergency_buffer
                0x8206a0f4      0x800 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)

.bss.emergency_used
                0x8206a8f4        0x4
 .bss.emergency_used
                0x8206a8f4        0x4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)

.bss.eh_globals
                0x8206a8f8        0x8
 .bss.eh_globals
                0x8206a8f8        0x8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
                0x8206a900                . = ALIGN (0x4)
                0x8206a900                _end = .
                0x8206a900                _bss_end__ = .
                0x8206a900                __bss_end__ = .
                0x8206a900                __end__ = .
                0x8206a900                PROVIDE (end, .)
                0x8206a900                . = ALIGN (0x10)
                0x8206a900                __heap_begin__ = .
                0x820aa900                . = (. + 0x40000)
                0x820aa8f0                __heap_end__ = (. - 0x10)
                0x820aa900                . = ALIGN (0x10)
                0x820ab900                . = (. + 0x1000)
                0x820eb900                . = (. + 0x40000)
                0x820eb8f0                __StackPointer__ = (. - 0x10)
                0x820eb900                . = ALIGN (0x10)
                0x820eb900                __StartOfFreeRam__ = .
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libm.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a
LOAD /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a
LOAD ./ALLLIB/libZip.a
LOAD ./ALLLIB/libFile.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libm.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a
LOAD /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a
LOAD start.o
LOAD COMLIB/ComLib.o
LOAD SYSLIB/SysLib.o
LOAD SYSLIB/SysIntr.o
LOAD DEVLIB/Nand64.o
LOAD DEVLIB/SysMMU.o
LOAD DEVLIB/SysPLL.o
LOAD DEVLIB/SysMCUD.o
LOAD DEVLIB/SysMCUS.o
LOAD DEVLIB/SysGPIO.o
LOAD DEVLIB/SysAlive.o
LOAD DEVLIB/SysTimer.o
LOAD DEVLIB/SysUART.o
LOAD DEVLIB/SysMLC.o
LOAD DEVLIB/SysDPC.o
LOAD DEVLIB/SysLCD.o
LOAD DEVLIB/SysPWM.o
LOAD DEVLIB/SysSDCD.o
LOAD DEVLIB/SdCardDRV.o
LOAD DEVLIB/DataBack.o
LOAD FILELIB/osfile.o
LOAD JPGLIB/jpegdecr.o
LOAD JPGLIB/jpegidct.o
LOAD JPGLIB/jpegh2v2.o
LOAD main.o
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libm.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a
LOAD /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a
LOAD ./ALLLIB/libZip.a
LOAD ./ALLLIB/libFile.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libm.a
LOAD /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a
LOAD /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a
OUTPUT(MainLoader_MKD.elf elf32-littlearm)

.ARM.attributes
                0x00000000       0x10
 .ARM.attributes
                0x00000000       0x10 start.o
 .ARM.attributes
                0x00000010       0x10 COMLIB/ComLib.o
 .ARM.attributes
                0x00000020       0x10 SYSLIB/SysLib.o
 .ARM.attributes
                0x00000030       0x10 SYSLIB/SysIntr.o
 .ARM.attributes
                0x00000040       0x10 DEVLIB/Nand64.o
 .ARM.attributes
                0x00000050       0x10 DEVLIB/SysMMU.o
 .ARM.attributes
                0x00000060       0x10 DEVLIB/SysPLL.o
 .ARM.attributes
                0x00000070       0x10 DEVLIB/SysMCUD.o
 .ARM.attributes
                0x00000080       0x10 DEVLIB/SysMCUS.o
 .ARM.attributes
                0x00000090       0x10 DEVLIB/SysGPIO.o
 .ARM.attributes
                0x000000a0       0x10 DEVLIB/SysAlive.o
 .ARM.attributes
                0x000000b0       0x10 DEVLIB/SysTimer.o
 .ARM.attributes
                0x000000c0       0x10 DEVLIB/SysUART.o
 .ARM.attributes
                0x000000d0       0x10 DEVLIB/SysMLC.o
 .ARM.attributes
                0x000000e0       0x10 DEVLIB/SysDPC.o
 .ARM.attributes
                0x000000f0       0x10 DEVLIB/SysLCD.o
 .ARM.attributes
                0x00000100       0x10 DEVLIB/SysPWM.o
 .ARM.attributes
                0x00000110       0x10 DEVLIB/SysSDCD.o
 .ARM.attributes
                0x00000120       0x10 DEVLIB/SdCardDRV.o
 .ARM.attributes
                0x00000130       0x10 DEVLIB/DataBack.o
 .ARM.attributes
                0x00000140       0x10 FILELIB/osfile.o
 .ARM.attributes
                0x00000150       0x10 JPGLIB/jpegdecr.o
 .ARM.attributes
                0x00000160       0x10 JPGLIB/jpegidct.o
 .ARM.attributes
                0x00000170       0x10 JPGLIB/jpegh2v2.o
 .ARM.attributes
                0x00000180       0x10 main.o
 .ARM.attributes
                0x00000190       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .ARM.attributes
                0x000001a0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .ARM.attributes
                0x000001b0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .ARM.attributes
                0x000001c0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .ARM.attributes
                0x000001d0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .ARM.attributes
                0x000001e0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .ARM.attributes
                0x000001f0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .ARM.attributes
                0x00000200       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .ARM.attributes
                0x00000210       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .ARM.attributes
                0x00000220       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .ARM.attributes
                0x00000230       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .ARM.attributes
                0x00000240       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .ARM.attributes
                0x00000250       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .ARM.attributes
                0x00000260       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .ARM.attributes
                0x00000270       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .ARM.attributes
                0x00000280       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .ARM.attributes
                0x00000290       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .ARM.attributes
                0x000002a0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .ARM.attributes
                0x000002b0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .ARM.attributes
                0x000002c0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .ARM.attributes
                0x000002d0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .ARM.attributes
                0x000002e0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .ARM.attributes
                0x000002f0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .ARM.attributes
                0x00000300       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .ARM.attributes
                0x00000310       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-setjmp.o)
 .ARM.attributes
                0x00000320       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .ARM.attributes
                0x00000330       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .ARM.attributes
                0x00000340       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .ARM.attributes
                0x00000350       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .ARM.attributes
                0x00000360       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .ARM.attributes
                0x00000370       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .ARM.attributes
                0x00000380       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .ARM.attributes
                0x00000390       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .ARM.attributes
                0x000003a0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .ARM.attributes
                0x000003b0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .ARM.attributes
                0x000003c0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .ARM.attributes
                0x000003d0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .ARM.attributes
                0x000003e0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .ARM.attributes
                0x000003f0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .ARM.attributes
                0x00000400       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .ARM.attributes
                0x00000410       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .ARM.attributes
                0x00000420       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .ARM.attributes
                0x00000430       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .ARM.attributes
                0x00000440       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .ARM.attributes
                0x00000450       0x10 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .ARM.attributes
                0x00000460       0x10 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .ARM.attributes
                0x00000470       0x10 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .ARM.attributes
                0x00000480       0x10 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000490       0x10 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .ARM.attributes
                0x000004a0       0x10 ./ALLLIB/libZip.a(compress.o)
 .ARM.attributes
                0x000004b0       0x10 ./ALLLIB/libZip.a(uncompr.o)
 .ARM.attributes
                0x000004c0       0x10 ./ALLLIB/libZip.a(deflate.o)
 .ARM.attributes
                0x000004d0       0x10 ./ALLLIB/libZip.a(trees.o)
 .ARM.attributes
                0x000004e0       0x10 ./ALLLIB/libZip.a(zutil.o)
 .ARM.attributes
                0x000004f0       0x10 ./ALLLIB/libZip.a(inflate.o)
 .ARM.attributes
                0x00000500       0x10 ./ALLLIB/libZip.a(infblock.o)
 .ARM.attributes
                0x00000510       0x10 ./ALLLIB/libZip.a(inftrees.o)
 .ARM.attributes
                0x00000520       0x10 ./ALLLIB/libZip.a(infcodes.o)
 .ARM.attributes
                0x00000530       0x10 ./ALLLIB/libZip.a(infutil.o)
 .ARM.attributes
                0x00000540       0x10 ./ALLLIB/libZip.a(inffast.o)
 .ARM.attributes
                0x00000550       0x10 ./ALLLIB/libZip.a(adler32.o)
 .ARM.attributes
                0x00000560       0x10 ./ALLLIB/libFile.a(Rtfiles.o)
 .ARM.attributes
                0x00000570       0x10 ./ALLLIB/libFile.a(Rtfex.o)
 .ARM.attributes
                0x00000580       0x10 ./ALLLIB/libFile.a(Up437.o)
 .ARM.attributes
                0x00000590       0x10 ./ALLLIB/libFile.a(Ut437.o)
 .ARM.attributes
                0x000005a0       0x10 ./ALLLIB/libFile.a(Rtfdata.o)
 .ARM.attributes
                0x000005b0       0x10 ./ALLLIB/libFile.a(Conf32.o)
 .ARM.attributes
                0x000005c0       0x10 ./ALLLIB/libFile.a(DrvSdCard.o)
 .ARM.attributes
                0x000005d0       0x10 ./ALLLIB/libFile.a(SysUcos.o)
 .ARM.attributes
                0x000005e0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .ARM.attributes
                0x000005f0       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .ARM.attributes
                0x00000600       0x10 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.comment        0x00000000      0x666
 .comment       0x00000000       0x12 COMLIB/ComLib.o
 .comment       0x00000012       0x12 SYSLIB/SysLib.o
 .comment       0x00000024       0x12 SYSLIB/SysIntr.o
 .comment       0x00000036       0x12 DEVLIB/Nand64.o
 .comment       0x00000048       0x12 DEVLIB/SysMMU.o
 .comment       0x0000005a       0x12 DEVLIB/SysPLL.o
 .comment       0x0000006c       0x12 DEVLIB/SysMCUD.o
 .comment       0x0000007e       0x12 DEVLIB/SysMCUS.o
 .comment       0x00000090       0x12 DEVLIB/SysGPIO.o
 .comment       0x000000a2       0x12 DEVLIB/SysAlive.o
 .comment       0x000000b4       0x12 DEVLIB/SysTimer.o
 .comment       0x000000c6       0x12 DEVLIB/SysUART.o
 .comment       0x000000d8       0x12 DEVLIB/SysMLC.o
 .comment       0x000000ea       0x12 DEVLIB/SysDPC.o
 .comment       0x000000fc       0x12 DEVLIB/SysLCD.o
 .comment       0x0000010e       0x12 DEVLIB/SysPWM.o
 .comment       0x00000120       0x12 DEVLIB/SysSDCD.o
 .comment       0x00000132       0x12 DEVLIB/SdCardDRV.o
 .comment       0x00000144       0x12 DEVLIB/DataBack.o
 .comment       0x00000156       0x12 FILELIB/osfile.o
 .comment       0x00000168       0x12 JPGLIB/jpegdecr.o
 .comment       0x0000017a       0x12 JPGLIB/jpegidct.o
 .comment       0x0000018c       0x12 JPGLIB/jpegh2v2.o
 .comment       0x0000019e       0x12 main.o
 .comment       0x000001b0       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .comment       0x000001c2       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .comment       0x000001d4       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .comment       0x000001e6       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .comment       0x000001f8       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .comment       0x0000020a       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .comment       0x0000021c       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .comment       0x0000022e       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .comment       0x00000240       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .comment       0x00000252       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .comment       0x00000264       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .comment       0x00000276       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .comment       0x00000288       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .comment       0x0000029a       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .comment       0x000002ac       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .comment       0x000002be       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .comment       0x000002d0       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .comment       0x000002e2       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .comment       0x000002f4       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .comment       0x00000306       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .comment       0x00000318       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .comment       0x0000032a       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .comment       0x0000033c       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .comment       0x0000034e       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .comment       0x00000360       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .comment       0x00000372       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .comment       0x00000384       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .comment       0x00000396       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .comment       0x000003a8       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .comment       0x000003ba       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .comment       0x000003cc       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .comment       0x000003de       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .comment       0x000003f0       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .comment       0x00000402       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .comment       0x00000414       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .comment       0x00000426       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .comment       0x00000438       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .comment       0x0000044a       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .comment       0x0000045c       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .comment       0x0000046e       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .comment       0x00000480       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .comment       0x00000492       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .comment       0x000004a4       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .comment       0x000004b6       0x12 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .comment       0x000004c8       0x12 ./ALLLIB/libZip.a(compress.o)
 .comment       0x000004da       0x12 ./ALLLIB/libZip.a(uncompr.o)
 .comment       0x000004ec       0x12 ./ALLLIB/libZip.a(deflate.o)
 .comment       0x000004fe       0x12 ./ALLLIB/libZip.a(trees.o)
 .comment       0x00000510       0x12 ./ALLLIB/libZip.a(zutil.o)
 .comment       0x00000522       0x12 ./ALLLIB/libZip.a(inflate.o)
 .comment       0x00000534       0x12 ./ALLLIB/libZip.a(infblock.o)
 .comment       0x00000546       0x12 ./ALLLIB/libZip.a(inftrees.o)
 .comment       0x00000558       0x12 ./ALLLIB/libZip.a(infcodes.o)
 .comment       0x0000056a       0x12 ./ALLLIB/libZip.a(infutil.o)
 .comment       0x0000057c       0x12 ./ALLLIB/libZip.a(inffast.o)
 .comment       0x0000058e       0x12 ./ALLLIB/libZip.a(adler32.o)
 .comment       0x000005a0       0x12 ./ALLLIB/libFile.a(Rtfiles.o)
 .comment       0x000005b2       0x12 ./ALLLIB/libFile.a(Rtfex.o)
 .comment       0x000005c4       0x12 ./ALLLIB/libFile.a(Up437.o)
 .comment       0x000005d6       0x12 ./ALLLIB/libFile.a(Ut437.o)
 .comment       0x000005e8       0x12 ./ALLLIB/libFile.a(Rtfdata.o)
 .comment       0x000005fa       0x12 ./ALLLIB/libFile.a(Conf32.o)
 .comment       0x0000060c       0x12 ./ALLLIB/libFile.a(DrvSdCard.o)
 .comment       0x0000061e       0x12 ./ALLLIB/libFile.a(SysUcos.o)
 .comment       0x00000630       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .comment       0x00000642       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .comment       0x00000654       0x12 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_abbrev   0x00000000     0x4b1b
 .debug_abbrev  0x00000000      0x471 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_abbrev  0x00000471      0x20d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .debug_abbrev  0x0000067e      0x114 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .debug_abbrev  0x00000792      0x1c2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .debug_abbrev  0x00000954      0x114 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .debug_abbrev  0x00000a68       0xad /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .debug_abbrev  0x00000b15      0x5b3 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_abbrev  0x000010c8      0x2de /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_abbrev  0x000013a6      0x2bd /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_abbrev  0x00001663      0x33f /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_abbrev  0x000019a2      0x1d7 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .debug_abbrev  0x00001b79      0x23a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_abbrev  0x00001db3      0x1a2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .debug_abbrev  0x00001f55      0x19b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .debug_abbrev  0x000020f0      0x133 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .debug_abbrev  0x00002223      0x17f /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .debug_abbrev  0x000023a2      0x19c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .debug_abbrev  0x0000253e      0x160 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .debug_abbrev  0x0000269e       0x94 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .debug_abbrev  0x00002732       0x99 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .debug_abbrev  0x000027cb       0xa2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .debug_abbrev  0x0000286d       0xb2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .debug_abbrev  0x0000291f      0x188 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .debug_abbrev  0x00002aa7      0x1fc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_abbrev  0x00002ca3       0x90 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .debug_abbrev  0x00002d33       0x7a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .debug_abbrev  0x00002dad       0x98 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .debug_abbrev  0x00002e45       0x94 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .debug_abbrev  0x00002ed9       0x87 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .debug_abbrev  0x00002f60      0x1c6 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .debug_abbrev  0x00003126       0x5b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .debug_abbrev  0x00003181      0x28a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .debug_abbrev  0x0000340b      0x200 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .debug_abbrev  0x0000360b      0x1bd /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_abbrev  0x000037c8      0x1f7 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_abbrev  0x000039bf      0x22e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_abbrev  0x00003bed       0xbe /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .debug_abbrev  0x00003cab      0x173 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .debug_abbrev  0x00003e1e      0x1bd /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .debug_abbrev  0x00003fdb      0x179 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .debug_abbrev  0x00004154      0x1b0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .debug_abbrev  0x00004304      0x19e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_abbrev  0x000044a2      0x1a4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .debug_abbrev  0x00004646       0x14 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .debug_abbrev  0x0000465a       0x14 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .debug_abbrev  0x0000466e       0x14 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .debug_abbrev  0x00004682       0x14 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .debug_abbrev  0x00004696      0x2df /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .debug_abbrev  0x00004975       0xa7 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .debug_abbrev  0x00004a1c       0x87 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .debug_abbrev  0x00004aa3       0x78 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_info     0x00000000    0x160b0
 .debug_info    0x00000000     0x117a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_info    0x0000117a      0x783 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .debug_info    0x000018fd      0x298 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .debug_info    0x00001b95      0x498 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .debug_info    0x0000202d      0x298 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .debug_info    0x000022c5       0xfe /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .debug_info    0x000023c3     0x1b67 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_info    0x00003f2a     0x1136 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_info    0x00005060     0x21ae /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_info    0x0000720e      0xb11 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_info    0x00007d1f      0x694 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .debug_info    0x000083b3      0x83d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_info    0x00008bf0      0x34e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .debug_info    0x00008f3e      0x6ad /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .debug_info    0x000095eb      0x2ef /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .debug_info    0x000098da      0x941 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .debug_info    0x0000a21b      0xa6a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .debug_info    0x0000ac85      0x91e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .debug_info    0x0000b5a3      0x10a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .debug_info    0x0000b6ad      0x10e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .debug_info    0x0000b7bb      0x13b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .debug_info    0x0000b8f6      0x10a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .debug_info    0x0000ba00      0x936 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .debug_info    0x0000c336      0xb97 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_info    0x0000cecd       0xe2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .debug_info    0x0000cfaf       0xd4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .debug_info    0x0000d083       0xff /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .debug_info    0x0000d182       0xd0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .debug_info    0x0000d252       0xee /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .debug_info    0x0000d340      0xa0b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .debug_info    0x0000dd4b       0x75 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .debug_info    0x0000ddc0      0xb5d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .debug_info    0x0000e91d      0xbab /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .debug_info    0x0000f4c8      0xa75 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_info    0x0000ff3d      0xad1 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_info    0x00010a0e      0xcb2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_info    0x000116c0      0x12d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .debug_info    0x000117ed      0x938 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .debug_info    0x00012125      0xa6c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .debug_info    0x00012b91      0x936 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .debug_info    0x000134c7      0x97c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .debug_info    0x00013e43      0x99a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_info    0x000147dd      0xad9 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .debug_info    0x000152b6       0x6a /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .debug_info    0x00015320       0x6a /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .debug_info    0x0001538a       0x6a /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .debug_info    0x000153f4       0x6a /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .debug_info    0x0001545e      0x9c9 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .debug_info    0x00015e27      0x10b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .debug_info    0x00015f32       0xbd /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .debug_info    0x00015fef       0xc1 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_line     0x00000000     0x4c9d
 .debug_line    0x00000000      0x440 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_line    0x00000440      0x1ca /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .debug_line    0x0000060a      0x11d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .debug_line    0x00000727      0x187 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .debug_line    0x000008ae      0x11d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .debug_line    0x000009cb      0x122 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .debug_line    0x00000aed      0x60a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_line    0x000010f7      0x201 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_line    0x000012f8      0xb83 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_line    0x00001e7b      0x26b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_line    0x000020e6      0x171 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .debug_line    0x00002257      0x208 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_line    0x0000245f      0x19a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .debug_line    0x000025f9      0x189 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .debug_line    0x00002782       0xfa /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .debug_line    0x0000287c       0xf6 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .debug_line    0x00002972      0x155 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .debug_line    0x00002ac7       0xe2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .debug_line    0x00002ba9       0xc4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .debug_line    0x00002c6d       0xce /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .debug_line    0x00002d3b       0xd6 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .debug_line    0x00002e11       0xd0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .debug_line    0x00002ee1       0xf7 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .debug_line    0x00002fd8      0x19d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_line    0x00003175       0x7a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .debug_line    0x000031ef       0x84 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .debug_line    0x00003273       0x79 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .debug_line    0x000032ec       0xa8 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .debug_line    0x00003394       0xdb /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .debug_line    0x0000346f      0x174 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .debug_line    0x000035e3       0x58 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .debug_line    0x0000363b      0x1bc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .debug_line    0x000037f7      0x17f /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .debug_line    0x00003976      0x1f3 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_line    0x00003b69      0x150 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_line    0x00003cb9      0x226 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_line    0x00003edf       0xdc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .debug_line    0x00003fbb       0xf4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .debug_line    0x000040af      0x162 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .debug_line    0x00004211      0x146 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .debug_line    0x00004357      0x149 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .debug_line    0x000044a0      0x185 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_line    0x00004625      0x17c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .debug_line    0x000047a1       0x89 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .debug_line    0x0000482a       0x95 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .debug_line    0x000048bf       0x72 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .debug_line    0x00004931       0x5d /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .debug_line    0x0000498e      0x1a3 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .debug_line    0x00004b31       0x7e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .debug_line    0x00004baf       0x79 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .debug_line    0x00004c28       0x75 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_frame    0x00000000     0x1578
 .debug_frame   0x00000000      0x13c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_frame   0x0000013c       0x80 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .debug_frame   0x000001bc       0x48 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .debug_frame   0x00000204       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .debug_frame   0x0000022c      0x31c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_frame   0x00000548       0x3c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_frame   0x00000584      0x448 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_frame   0x000009cc       0x6c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_frame   0x00000a38       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .debug_frame   0x00000a60       0x7c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_frame   0x00000adc       0x98 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .debug_frame   0x00000b74       0x30 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .debug_frame   0x00000ba4       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .debug_frame   0x00000bcc       0x30 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .debug_frame   0x00000bfc       0x4c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .debug_frame   0x00000c48       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .debug_frame   0x00000c70       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .debug_frame   0x00000c98       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .debug_frame   0x00000cc4       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .debug_frame   0x00000ce4       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .debug_frame   0x00000d04       0x38 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_frame   0x00000d3c       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .debug_frame   0x00000d64       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .debug_frame   0x00000d84       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .debug_frame   0x00000dac       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .debug_frame   0x00000dcc       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .debug_frame   0x00000df4       0x50 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .debug_frame   0x00000e44      0x114 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .debug_frame   0x00000f58       0x50 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .debug_frame   0x00000fa8       0x38 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_frame   0x00000fe0       0x58 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_frame   0x00001038       0x38 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_frame   0x00001070       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .debug_frame   0x00001098       0x30 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .debug_frame   0x000010c8       0x70 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .debug_frame   0x00001138       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .debug_frame   0x00001164       0x3c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .debug_frame   0x000011a0       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_frame   0x000011cc       0x30 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .debug_frame   0x000011fc       0x28 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .debug_frame   0x00001224       0x28 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .debug_frame   0x0000124c       0x28 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .debug_frame   0x00001274      0x290 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .debug_frame   0x00001504       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .debug_frame   0x0000152c       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .debug_frame   0x0000154c       0x2c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_loc      0x00000000     0x720d
 .debug_loc     0x00000000      0xd02 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_loc     0x00000d02       0x83 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .debug_loc     0x00000d85      0x122 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .debug_loc     0x00000ea7       0x1f /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .debug_loc     0x00000ec6      0xdb9 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_loc     0x00001c7f       0xf1 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_loc     0x00001d70     0x22f6 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_loc     0x00004066       0xe0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_loc     0x00004146       0x3d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .debug_loc     0x00004183      0x1bb /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_loc     0x0000433e       0x9c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .debug_loc     0x000043da       0x32 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .debug_loc     0x0000440c       0x39 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .debug_loc     0x00004445      0x167 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .debug_loc     0x000045ac      0x120 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .debug_loc     0x000046cc      0x104 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .debug_loc     0x000047d0      0x17d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .debug_loc     0x0000494d       0xa9 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .debug_loc     0x000049f6       0x3c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .debug_loc     0x00004a32      0x68b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_loc     0x000050bd       0x79 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .debug_loc     0x00005136       0x99 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .debug_loc     0x000051cf       0x3d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .debug_loc     0x0000520c       0x26 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .debug_loc     0x00005232      0x102 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .debug_loc     0x00005334      0x242 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .debug_loc     0x00005576      0x1c2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .debug_loc     0x00005738      0x2ab /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .debug_loc     0x000059e3      0x31a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_loc     0x00005cfd      0x129 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_loc     0x00005e26      0x740 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_loc     0x00006566      0x128 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .debug_loc     0x0000668e      0x1a4 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .debug_loc     0x00006832       0x3d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .debug_loc     0x0000686f       0xa2 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .debug_loc     0x00006911      0x10a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_loc     0x00006a1b       0xd0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .debug_loc     0x00006aeb      0x5d3 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .debug_loc     0x000070be       0xb5 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .debug_loc     0x00007173       0x1e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .debug_loc     0x00007191       0x7c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_pubnames
                0x00000000     0x123c
 .debug_pubnames
                0x00000000       0x46 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_pubnames
                0x00000046       0x75 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .debug_pubnames
                0x000000bb       0x2a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
 .debug_pubnames
                0x000000e5       0x34 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .debug_pubnames
                0x00000119       0x2b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
 .debug_pubnames
                0x00000144       0x29 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .debug_pubnames
                0x0000016d      0x63b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_pubnames
                0x000007a8       0x32 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_pubnames
                0x000007da       0x25 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_pubnames
                0x000007ff       0x48 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_pubnames
                0x00000847       0x2b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .debug_pubnames
                0x00000872       0x6f /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_pubnames
                0x000008e1      0x11a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .debug_pubnames
                0x000009fb       0x43 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .debug_pubnames
                0x00000a3e       0x33 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .debug_pubnames
                0x00000a71       0x27 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .debug_pubnames
                0x00000a98       0x2b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .debug_pubnames
                0x00000ac3       0x39 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
 .debug_pubnames
                0x00000afc       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .debug_pubnames
                0x00000b19       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .debug_pubnames
                0x00000b36       0x1e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .debug_pubnames
                0x00000b54       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .debug_pubnames
                0x00000b71       0x1e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .debug_pubnames
                0x00000b8f       0x21 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_pubnames
                0x00000bb0       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .debug_pubnames
                0x00000bcd       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .debug_pubnames
                0x00000bea       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .debug_pubnames
                0x00000c07       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .debug_pubnames
                0x00000c24       0x1e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .debug_pubnames
                0x00000c42       0x2b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .debug_pubnames
                0x00000c6d       0x2e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
 .debug_pubnames
                0x00000c9b       0xdb /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .debug_pubnames
                0x00000d76       0x31 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .debug_pubnames
                0x00000da7       0x23 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_pubnames
                0x00000dca       0x2e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_pubnames
                0x00000df8       0xce /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_pubnames
                0x00000ec6       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .debug_pubnames
                0x00000ee3       0x38 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .debug_pubnames
                0x00000f1b       0x44 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .debug_pubnames
                0x00000f5f       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .debug_pubnames
                0x00000f7f       0x2b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .debug_pubnames
                0x00000faa       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_pubnames
                0x00000fc7       0x21 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .debug_pubnames
                0x00000fe8      0x1fb /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .debug_pubnames
                0x000011e3       0x1d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .debug_pubnames
                0x00001200       0x1e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .debug_pubnames
                0x0000121e       0x1e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_aranges  0x00000000      0x848
 .debug_aranges
                0x00000000       0x70 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_aranges
                0x00000070       0x50 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
 .debug_aranges
                0x000000c0       0x38 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
 .debug_aranges
                0x000000f8       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
 .debug_aranges
                0x00000120      0x140 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_aranges
                0x00000260       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_aranges
                0x00000288       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_aranges
                0x000002a8       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_aranges
                0x000002e8       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
 .debug_aranges
                0x00000310       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_aranges
                0x00000350       0x60 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
 .debug_aranges
                0x000003b0       0x30 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
 .debug_aranges
                0x000003e0       0x28 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
 .debug_aranges
                0x00000408       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
 .debug_aranges
                0x00000428       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
 .debug_aranges
                0x00000448       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
 .debug_aranges
                0x00000468       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
 .debug_aranges
                0x00000488       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
 .debug_aranges
                0x000004a8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
 .debug_aranges
                0x000004c8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
 .debug_aranges
                0x000004e8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_aranges
                0x00000508       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
 .debug_aranges
                0x00000528       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
 .debug_aranges
                0x00000548       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
 .debug_aranges
                0x00000568       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
 .debug_aranges
                0x00000588       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
 .debug_aranges
                0x000005a8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
 .debug_aranges
                0x000005c8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
 .debug_aranges
                0x000005e8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
 .debug_aranges
                0x00000608       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_aranges
                0x00000628       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_aranges
                0x00000648       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_aranges
                0x00000668       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
 .debug_aranges
                0x00000688       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
 .debug_aranges
                0x000006a8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
 .debug_aranges
                0x000006c8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
 .debug_aranges
                0x000006e8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
 .debug_aranges
                0x00000708       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_aranges
                0x00000728       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
 .debug_aranges
                0x00000748       0x20 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_udivsi3.o)
 .debug_aranges
                0x00000768       0x20 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_divsi3.o)
 .debug_aranges
                0x00000788       0x20 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_umodsi3.o)
 .debug_aranges
                0x000007a8       0x20 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(_dvmd_tls.o)
 .debug_aranges
                0x000007c8       0x20 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
 .debug_aranges
                0x000007e8       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
 .debug_aranges
                0x00000808       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
 .debug_aranges
                0x00000828       0x20 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)

.debug_ranges   0x00000000      0xe68
 .debug_ranges  0x00000000       0x88 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
 .debug_ranges  0x00000088      0x2b0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
 .debug_ranges  0x00000338       0x70 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
 .debug_ranges  0x000003a8      0x920 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
 .debug_ranges  0x00000cc8       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
 .debug_ranges  0x00000ce0       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
 .debug_ranges  0x00000cf8       0x70 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
 .debug_ranges  0x00000d68       0x18 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
 .debug_ranges  0x00000d80       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
 .debug_ranges  0x00000dc0       0x60 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
 .debug_ranges  0x00000e20       0x30 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
 .debug_ranges  0x00000e50       0x18 /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)

.debug_str      0x00000000     0x400e
 .debug_str     0x00000000      0x7bc /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_personality.o)
                                0x8cf (size before relaxing)
 .debug_str     0x000007bc      0x185 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_terminate.o)
                                0x565 (size before relaxing)
 .debug_str     0x00000941       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_term_handler.o)
                                0x372 (size before relaxing)
 .debug_str     0x00000981       0x96 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_throw.o)
                                0x48a (size before relaxing)
 .debug_str     0x00000a17       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_unex_handler.o)
                                0x374 (size before relaxing)
 .debug_str     0x00000a57       0x48 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(pure.o)
                                0x14f (size before relaxing)
 .debug_str     0x00000a9f      0xc31 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(tinfo.o)
                                0xd6a (size before relaxing)
 .debug_str     0x000016d0      0x48e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(vterminate.o)
                                0x69a (size before relaxing)
 .debug_str     0x00001b5e      0xe4a /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(cp-demangle.o)
                                0xfe4 (size before relaxing)
 .debug_str     0x000029a8      0x2a7 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_alloc.o)
                                0x6f9 (size before relaxing)
 .debug_str     0x00002c4f       0x4d /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_call.o)
                                0x452 (size before relaxing)
 .debug_str     0x00002c9c       0xd1 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_catch.o)
                                0x521 (size before relaxing)
 .debug_str     0x00002d6d       0x9c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_exception.o)
                                0x1a3 (size before relaxing)
 .debug_str     0x00002e09       0x64 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_globals.o)
                                0x46e (size before relaxing)
 .debug_str     0x00002e6d       0x55 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libstdc++.a(eh_type.o)
                                0x396 (size before relaxing)
 .debug_str     0x00002ec2       0x7b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-atoi.o)
                                0x493 (size before relaxing)
 .debug_str     0x00002f3d       0xb5 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwrite.o)
                                0x502 (size before relaxing)
 .debug_str     0x00002ff2       0x87 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-impure.o)
                                0x4b1 (size before relaxing)
 .debug_str     0x00003079       0x71 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcmp.o)
                                0x101 (size before relaxing)
 .debug_str     0x000030ea       0x67 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memcpy.o)
                                 0xcb (size before relaxing)
 .debug_str     0x00003151       0x54 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memmove.o)
                                0x134 (size before relaxing)
 .debug_str     0x000031a5       0x46 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memset.o)
                                0x115 (size before relaxing)
 .debug_str     0x000031eb       0x41 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-realloc.o)
                                0x4ab (size before relaxing)
 .debug_str     0x0000322c       0xef /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-reallocr.o)
                                0x566 (size before relaxing)
 .debug_str     0x0000331b       0x4b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcat.o)
                                0x105 (size before relaxing)
 .debug_str     0x00003366       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcmp.o)
                                 0xfa (size before relaxing)
 .debug_str     0x000033a6       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strcpy.o)
                                0x11c (size before relaxing)
 .debug_str     0x000033e6       0x46 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strlen.o)
                                0x114 (size before relaxing)
 .debug_str     0x0000342c       0x42 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strncmp.o)
                                0x103 (size before relaxing)
 .debug_str     0x0000346e       0x69 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strtol.o)
                                0x4d5 (size before relaxing)
 .debug_str     0x000034d7       0x70 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-ctype_.o)
                                 0x8d (size before relaxing)
 .debug_str     0x00003547      0x100 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-findfp.o)
                                0x597 (size before relaxing)
 .debug_str     0x00003647       0xdf /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-freer.o)
                                0x5e6 (size before relaxing)
 .debug_str     0x00003726       0x5c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fvwrite.o)
                                0x512 (size before relaxing)
 .debug_str     0x00003782       0x5c /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fwalk.o)
                                0x4d8 (size before relaxing)
 .debug_str     0x000037de       0xd0 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mallocr.o)
                                0x694 (size before relaxing)
 .debug_str     0x000038ae       0x3e /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-memchr.o)
                                0x122 (size before relaxing)
 .debug_str     0x000038ec       0x6b /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-mlock.o)
                                0x4ba (size before relaxing)
 .debug_str     0x00003957       0x67 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-stdio.o)
                                0x4e0 (size before relaxing)
 .debug_str     0x000039be       0x42 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-wsetup.o)
                                0x4ad (size before relaxing)
 .debug_str     0x00003a00       0x42 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fclose.o)
                                0x4b9 (size before relaxing)
 .debug_str     0x00003a42       0x46 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-fflush.o)
                                0x4cb (size before relaxing)
 .debug_str     0x00003a88      0x117 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-makebuf.o)
                                0x594 (size before relaxing)
 .debug_str     0x00003b9f      0x3ab /cygdrive/c/GNUARM-4.2.2/lib/gcc/arm-elf/4.2.2/libgcc.a(unwind-sjlj.o)
                                0x5f0 (size before relaxing)
 .debug_str     0x00003f4a       0x40 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strchr.o)
                                0x10c (size before relaxing)
 .debug_str     0x00003f8a       0x42 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strpbrk.o)
                                 0xfc (size before relaxing)
 .debug_str     0x00003fcc       0x42 /cygdrive/c/GNUARM-4.2.2/arm-elf/lib/libc.a(lib_a-strrchr.o)
                                0x101 (size before relaxing)
