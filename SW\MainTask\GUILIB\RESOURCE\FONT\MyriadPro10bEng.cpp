/*...........................................................................*/
/*.                  File Name : MyriadPro10bEng.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"


extern xFONTYY MyriadPro10bGrk_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

#if defined(_FONT_PLASTIMO_)
ROMDATA PEGUSHORT MyriadPro10bExt_offset_table[337] = {
0x0000,0x0008,0x000f,0x0017,0x001e,0x0027,0x002e,0x0036,0x003d,0x0045,0x004c,0x0054,0x005a,0x0062,0x0069,0x0072,
0x007b,0x0084,0x008c,0x0093,0x009a,0x00a1,0x00a8,0x00af,0x00b6,0x00bd,0x00c4,0x00cb,0x00d2,0x00db,0x00e3,0x00ec,
0x00f4,0x00fd,0x0105,0x010e,0x0116,0x011f,0x0127,0x0130,0x0138,0x013c,0x0140,0x0144,0x0148,0x014c,0x0150,0x0153,
0x0156,0x0159,0x015c,0x0164,0x016b,0x0171,0x0176,0x017f,0x0186,0x018d,0x0194,0x0198,0x019f,0x01a2,0x01a9,0x01ae,
0x01b5,0x01ba,0x01c1,0x01c5,0x01ce,0x01d6,0x01df,0x01e7,0x01f0,0x01f8,0x0200,0x0209,0x0211,0x021a,0x0222,0x022b,
0x0233,0x023c,0x0244,0x0250,0x025c,0x0263,0x0268,0x026f,0x0274,0x027b,0x0280,0x0287,0x028d,0x0294,0x029a,0x02a1,
0x02a7,0x02ae,0x02b4,0x02bc,0x02c1,0x02c9,0x02cf,0x02d7,0x02dc,0x02e5,0x02ed,0x02f6,0x02fe,0x0307,0x030f,0x0318,
0x0320,0x0329,0x0331,0x033a,0x0342,0x034e,0x0358,0x0360,0x0367,0x036f,0x0377,0x037d,0x0385,0x038b,0x0393,0x0399,
0x039f,0x03a2,0x03a5,0x03a8,0x03ab,0x03ae,0x03b1,0x03b4,0x03b7,0x03ba,0x03bd,0x03c0,0x03c3,0x03c6,0x03c9,0x03cc,
0x03cf,0x03d2,0x03d5,0x03dc,0x03df,0x03e2,0x03e5,0x03e8,0x03eb,0x03ee,0x03f1,0x03f4,0x03f7,0x03fa,0x03fd,0x0400,
0x0403,0x040d,0x0415,0x0418,0x041b,0x041e,0x0421,0x0424,0x0427,0x042a,0x042d,0x0430,0x0433,0x0436,0x0439,0x043c,
0x0446,0x044f,0x0452,0x0455,0x0458,0x045b,0x045e,0x0461,0x0464,0x0467,0x046a,0x046d,0x0470,0x0473,0x0476,0x0479,
0x047c,0x047f,0x0482,0x0485,0x0488,0x048b,0x048e,0x0491,0x0494,0x0497,0x049a,0x049d,0x04a0,0x04a3,0x04a6,0x04a9,
0x04ac,0x04af,0x04b2,0x04b5,0x04b8,0x04bb,0x04be,0x04c1,0x04c4,0x04c7,0x04ca,0x04cd,0x04d0,0x04d3,0x04d6,0x04d9,
0x04dc,0x04df,0x04e2,0x04e5,0x04e8,0x04eb,0x04ee,0x04f1,0x04f4,0x04f7,0x04fa,0x04fd,0x0500,0x0503,0x0506,0x0509,
0x050c,0x050f,0x0512,0x0515,0x0518,0x051b,0x051e,0x0521,0x0524,0x0527,0x052a,0x0532,0x0539,0x0544,0x054f,0x0558,
0x0560,0x0563,0x0566,0x0569,0x056c,0x056f,0x0572,0x0575,0x0578,0x057b,0x057e,0x0581,0x0584,0x0587,0x058a,0x058d,
0x0590,0x0593,0x0596,0x0599,0x059c,0x059f,0x05a2,0x05a5,0x05a8,0x05af,0x05b5,0x05bd,0x05c2,0x05c9,0x05cf,0x05d2,
0x05d5,0x05d8,0x05db,0x05de,0x05e1,0x05e4,0x05e7,0x05ea,0x05ed,0x05f0,0x05f3,0x05f6,0x05f9,0x05fc,0x05ff,0x0602,
0x0605,0x0608,0x060b,0x0613,0x061a,0x061d,0x0620,0x0623,0x0626,0x0629,0x062c,0x062f,0x0632,0x0635,0x0638,0x063b,
0x063e,0x0641,0x0644,0x0647,0x064a,0x064d,0x0650,0x0653,0x0656,0x0659,0x065c,0x065f,0x0662,0x0665,0x0668,0x066b,
0x066e,
};


ROMDATA PEGUBYTE MyriadPro10bExt_data_table[3090] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x3c, 0x00, 0xf8, 0x00, 0xc0, 0x07, 0xc0, 0x1f, 0x00, 0x00, 
0x00, 0x03, 0xc0, 0x0e, 0x00, 0x18, 0x00, 0x00, 0x07, 0xc0, 0x07, 0xc0, 0x03, 0x60, 0x00, 0xc0, 
0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0xc0, 0x00, 0xf0, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0x00, 0x03, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x03, 0xc0, 
0x1c, 0x01, 0xe0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 
0xd8, 0x00, 0x3c, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 
0xc0, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x07, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0xf0, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0xe0, 0x00, 
0x70, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x18, 0x00, 0x78, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x18, 0x00, 0x30, 0x78, 0x30, 0x00, 0x7c, 0x30, 0xf8, 0xc1, 0xf1, 0x87, 0xcf, 0x3f, 0x01, 0xef, 
0xc0, 0x67, 0xe0, 0x1f, 0x9e, 0x7e, 0x31, 0xf8, 0x07, 0xe7, 0x83, 0xe3, 0x01, 0xf3, 0xc1, 0xf8, 
0xc0, 0x7c, 0x31, 0x8c, 0xc0, 0xc6, 0x60, 0x0f, 0x00, 0x6f, 0x6d, 0x86, 0x36, 0xc0, 0x19, 0x8c, 
0xc0, 0x00, 0x06, 0x60, 0xdb, 0x3d, 0x83, 0x18, 0x33, 0x0c, 0x31, 0x86, 0x00, 0xc3, 0x3c, 0x60, 
0x61, 0x80, 0x0f, 0x00, 0x07, 0x86, 0xc3, 0xc1, 0xf1, 0xff, 0x00, 0x07, 0xc7, 0x7c, 0x07, 0xcf, 
0x3e, 0x39, 0xf3, 0x0f, 0x80, 0x7d, 0xff, 0xf0, 0x7f, 0x8f, 0xfe, 0x06, 0x31, 0xe3, 0x18, 0x01, 
0x8c, 0x78, 0xc6, 0x3c, 0x63, 0x1b, 0x31, 0x80, 0x31, 0x8c, 0x30, 0xe7, 0x19, 0x86, 0xfe, 0x33, 
0xf1, 0x8f, 0xef, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 
0x03, 0xd8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x07, 0x07, 0xf0, 0x70, 0x3f, 0x0e, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x07, 0xf8, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1c, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3c, 0x3c, 0x78, 0x30, 0x78, 0x00, 0xc0, 0x61, 0x81, 0xe3, 0x00, 0x0c, 0x06, 0x31, 0x81, 0xec, 
0x61, 0xf6, 0x07, 0x98, 0x0c, 0x60, 0x01, 0x80, 0x06, 0x03, 0x0e, 0x07, 0x87, 0x01, 0x83, 0x00, 
0x01, 0xc0, 0x61, 0x8c, 0xc0, 0xc6, 0xf8, 0x6c, 0x6f, 0x66, 0x61, 0x86, 0x30, 0x03, 0x3d, 0x98, 
0xc0, 0x03, 0x06, 0x60, 0xdb, 0x3d, 0x83, 0x18, 0x33, 0x8c, 0x61, 0xc6, 0x00, 0xe3, 0x18, 0x60, 
0x71, 0x80, 0x19, 0x8f, 0x0c, 0xc3, 0x86, 0x63, 0xe3, 0x30, 0x00, 0x06, 0x66, 0x66, 0x06, 0x66, 
0x60, 0x73, 0x07, 0xd8, 0x00, 0xc0, 0xc1, 0x80, 0x0c, 0x0e, 0x30, 0x06, 0x31, 0xc3, 0x18, 0xf1, 
0x8c, 0x30, 0xc6, 0x18, 0x63, 0x1e, 0x31, 0x80, 0x1b, 0xd8, 0x78, 0x66, 0x7c, 0xcc, 0x0c, 0x60, 
0x30, 0x00, 0xc6, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x30, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x0e, 0x0f, 0x00, 0xc0, 0x66, 0x1c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0xc0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0xc7, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3c, 0x00, 0x78, 0x00, 0x78, 0x01, 0x80, 0x03, 0x00, 0x06, 0x00, 0x18, 0x00, 0x30, 0xc1, 0xec, 
0x30, 0x66, 0x00, 0x18, 0x00, 0x60, 0x01, 0x80, 0x06, 0x00, 0x18, 0x00, 0x0c, 0x00, 0x06, 0x00, 
0x03, 0x00, 0x01, 0x8c, 0xc1, 0xff, 0x60, 0x60, 0x60, 0x60, 0x61, 0x86, 0x30, 0x03, 0x01, 0xb0, 
0xc0, 0x03, 0x06, 0x60, 0xde, 0x31, 0x83, 0x18, 0x33, 0xcc, 0x01, 0xe6, 0x00, 0xf3, 0x00, 0x00, 
0x79, 0x80, 0x30, 0xc0, 0x18, 0x60, 0x0c, 0x30, 0x06, 0x30, 0x00, 0x06, 0x60, 0x66, 0x06, 0x60, 
0x60, 0x03, 0x00, 0x18, 0x00, 0xc0, 0x01, 0x86, 0x0c, 0x30, 0x30, 0xc6, 0x30, 0x03, 0x18, 0x01, 
0x8c, 0x00, 0xc6, 0x00, 0x63, 0x00, 0x31, 0x80, 0x1b, 0xd8, 0x00, 0x3c, 0x00, 0x78, 0x0c, 0x00, 
0x60, 0x00, 0xc0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x30, 0x18, 0x00, 0x00, 0x00, 0x00, 0x06, 0x30, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x0f, 0x00, 0x00, 0xcf, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0xc3, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3c, 0x78, 0x78, 0xf0, 0x78, 0xf1, 0x80, 0x7b, 0x00, 0xf6, 0x01, 0xd8, 0x07, 0xb0, 0xcf, 0x8c, 
0x33, 0xe6, 0x07, 0x98, 0x1e, 0x60, 0x79, 0x81, 0xe6, 0x07, 0x98, 0x07, 0xcc, 0x03, 0xe6, 0x01, 
0xf3, 0x00, 0xf9, 0x8c, 0xf8, 0xc6, 0x7c, 0x66, 0x66, 0x66, 0x6d, 0xb6, 0x36, 0xc3, 0x19, 0xe0, 
0xdd, 0xbb, 0x06, 0x60, 0xd8, 0x31, 0x83, 0xde, 0x3b, 0xcd, 0xf1, 0xe6, 0xf8, 0xf3, 0x7c, 0x7c, 
0x6d, 0xbe, 0x30, 0xcf, 0x18, 0x63, 0x8c, 0x31, 0xc6, 0x30, 0x3f, 0xe6, 0x6f, 0x6e, 0xf6, 0x6f, 
0x30, 0x79, 0x83, 0xcc, 0x1e, 0x60, 0xf1, 0x8f, 0x8c, 0x7c, 0x31, 0xf6, 0x33, 0x33, 0x19, 0x99, 
0x8c, 0xcc, 0xc6, 0x66, 0x63, 0x33, 0x31, 0x99, 0x9b, 0xdb, 0x33, 0x3c, 0xc6, 0x78, 0x19, 0xf8, 
0x67, 0xe1, 0x9f, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x30, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x06, 0x31, 0x9c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x1e, 0x0f, 0x07, 0xbc, 0xcf, 0x3f, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x78, 0xc7, 0xc6, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x8c, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x66, 0x0c, 0xcc, 0x18, 0xcc, 0x19, 0x81, 0xc3, 0x03, 0x86, 0x07, 0x18, 0x1c, 0x30, 0xd9, 0x9f, 
0x36, 0x67, 0xcc, 0xdf, 0x33, 0x7e, 0xcd, 0xfb, 0x37, 0xcc, 0xd9, 0xec, 0xcc, 0xf6, 0x66, 0x7b, 
0x73, 0x3d, 0x99, 0xfc, 0xec, 0xfe, 0x76, 0x66, 0x66, 0x66, 0x6d, 0xb6, 0x36, 0xc3, 0x19, 0xe0, 
0xf1, 0xe3, 0x06, 0x60, 0xd8, 0x31, 0xb3, 0x1c, 0x33, 0x6d, 0xd9, 0xb6, 0xec, 0xdb, 0x76, 0x76, 
0x6d, 0xbb, 0x30, 0xd9, 0x98, 0x6e, 0xcc, 0x37, 0x66, 0x3e, 0x66, 0x37, 0xce, 0x78, 0xe7, 0xce, 
0x1c, 0xc0, 0xe6, 0x07, 0x30, 0x39, 0x81, 0x86, 0x0c, 0x30, 0xfc, 0xc6, 0x33, 0x33, 0x19, 0x99, 
0x8c, 0xcc, 0xc6, 0x66, 0x63, 0x33, 0x31, 0x99, 0x9b, 0xd9, 0xfe, 0x18, 0x6c, 0x30, 0x30, 0x30, 
0xc0, 0xc3, 0x03, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x33, 0x98, 0x00, 0x00, 0x00, 0x00, 0x06, 0x31, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x83, 0x1b, 0xe0, 0xe6, 0xdb, 0x66, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 0xc3, 0x1e, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x0c, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x7e, 0x3c, 0xfc, 0x78, 0xfc, 0x79, 0x81, 0x83, 0x03, 0x06, 0x06, 0x18, 0x18, 0x30, 0xd9, 0x8c, 
0x36, 0x66, 0x0f, 0xd8, 0x3f, 0x60, 0xfd, 0x83, 0xf6, 0x0f, 0xd8, 0x6c, 0xcc, 0x36, 0x66, 0x1b, 
0x33, 0x0d, 0x99, 0x8c, 0xcc, 0xc6, 0x66, 0x66, 0x66, 0x66, 0x6d, 0xb6, 0x36, 0xc3, 0x19, 0xb0, 
0xe1, 0xc3, 0x06, 0x60, 0xd8, 0x31, 0x83, 0x38, 0x73, 0x3d, 0x99, 0xb6, 0xcc, 0xcf, 0x66, 0x66, 
0x67, 0xb3, 0x30, 0xd8, 0xd8, 0x6c, 0x6c, 0x36, 0x36, 0x30, 0x67, 0xf6, 0xcc, 0x6c, 0xc6, 0xcc, 
0x06, 0x60, 0x33, 0x01, 0x98, 0x0c, 0xc1, 0x86, 0x0c, 0x30, 0x31, 0xe6, 0x33, 0x33, 0x19, 0x99, 
0x8c, 0xcc, 0xc6, 0x66, 0x63, 0x33, 0x31, 0x99, 0x8f, 0xf1, 0xfe, 0x18, 0x6c, 0x30, 0x30, 0x61, 
0x81, 0x83, 0x06, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x33, 0x18, 0x00, 0x00, 0x00, 0x00, 0x06, 0x31, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x8f, 0x1f, 0x03, 0xfe, 0xf3, 0x6f, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x60, 0xc3, 0x03, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x66, 0x6c, 0xcc, 0xd8, 0xcc, 0xd9, 0x81, 0x83, 0x03, 0x06, 0x06, 0x18, 0x18, 0x31, 0x99, 0x8c, 
0x36, 0x66, 0x0c, 0x18, 0x30, 0x60, 0xc1, 0x83, 0x06, 0x0c, 0x18, 0x6c, 0xcc, 0x36, 0x66, 0x1b, 
0x33, 0x0d, 0x99, 0x8c, 0xcc, 0xc6, 0x66, 0x66, 0x66, 0x66, 0x6d, 0xb6, 0x36, 0xc3, 0x19, 0x98, 
0xf1, 0xe3, 0x06, 0x60, 0xd8, 0x31, 0x83, 0x18, 0x33, 0x3d, 0x99, 0x9e, 0xcc, 0xcf, 0x66, 0x66, 
0x63, 0xb3, 0x30, 0xd8, 0xd8, 0x6c, 0x6c, 0x36, 0x36, 0x30, 0x66, 0x06, 0x6c, 0x66, 0xc6, 0x6c, 
0x06, 0x38, 0x31, 0xc1, 0x8e, 0x0c, 0x71, 0x86, 0x0c, 0x30, 0x30, 0xc6, 0x33, 0x33, 0x19, 0x99, 
0x8c, 0xcc, 0xc6, 0x66, 0x63, 0x33, 0x31, 0x99, 0x8f, 0xf1, 0xfe, 0x18, 0x3c, 0x30, 0x60, 0x61, 
0x81, 0x86, 0x06, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x0c, 0x33, 0x18, 0x00, 0x00, 0x00, 0x00, 0x06, 0x31, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x9b, 0x33, 0x06, 0xc0, 0xf3, 0x7b, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x38, 0xc3, 0x01, 0x9c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x66, 0x6c, 0xcc, 0xd9, 0x86, 0xd8, 0xc1, 0xc1, 0x83, 0x83, 0x07, 0x0c, 0x1c, 0x33, 0x99, 0x8c, 
0xe6, 0x66, 0x0e, 0x18, 0x38, 0x60, 0xc1, 0x83, 0x06, 0x0e, 0x0c, 0x6c, 0xc6, 0x36, 0x63, 0x1b, 
0x71, 0x8d, 0x99, 0x8c, 0xcc, 0xc6, 0x66, 0x66, 0x66, 0x66, 0x6d, 0xb6, 0x36, 0xc3, 0x19, 0x98, 
0xd9, 0xb3, 0x06, 0x60, 0xd8, 0x31, 0x83, 0x18, 0x33, 0x1d, 0x99, 0x8e, 0xcc, 0xc7, 0x66, 0x66, 
0x63, 0xb3, 0x19, 0x99, 0x8c, 0xcc, 0xe6, 0x66, 0x73, 0x30, 0x6f, 0x06, 0x6c, 0x66, 0xc6, 0x6c, 
0x66, 0x1b, 0x30, 0xd9, 0x86, 0xcc, 0x31, 0x86, 0x0c, 0x30, 0x30, 0xc3, 0x63, 0x31, 0xb1, 0xb8, 
0xd8, 0xdc, 0x66, 0x6e, 0x36, 0x37, 0x1b, 0x1b, 0x8e, 0x71, 0xfe, 0x18, 0x38, 0x30, 0x60, 0xc3, 
0x03, 0x06, 0x0c, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x63, 0x38, 0x00, 0x00, 0x00, 0x00, 0x03, 0x61, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xdb, 0x31, 0x86, 0xe0, 0x66, 0x37, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x18, 0xc3, 0x01, 0xbc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0xc3, 0x3d, 0x86, 0x79, 0x86, 0x78, 0x7c, 0x78, 0xf8, 0xf1, 0xf1, 0xc7, 0xc7, 0xbe, 0x0f, 0x8f, 
0x83, 0xe7, 0xe3, 0xdf, 0x8f, 0x7e, 0x7d, 0xf9, 0xf7, 0xe3, 0xc7, 0xc7, 0xc3, 0xe3, 0xe1, 0xf1, 
0xf0, 0xf8, 0xf9, 0x8c, 0xcc, 0xc6, 0x66, 0x66, 0x66, 0x66, 0x6d, 0xb7, 0xe6, 0xde, 0x19, 0x8e, 
0xcd, 0x9b, 0xf6, 0x7e, 0x1f, 0xb1, 0xfb, 0x1f, 0xb3, 0x0d, 0x99, 0x86, 0xc0, 0xc3, 0x66, 0x66, 
0x61, 0xb3, 0x0f, 0x0f, 0x07, 0x87, 0x83, 0xc3, 0xc1, 0xff, 0x39, 0xf6, 0x6c, 0x66, 0x06, 0x6c, 
0x7c, 0xf3, 0xe7, 0x8f, 0x3c, 0xf9, 0xe0, 0x03, 0x8c, 0x1c, 0x30, 0x71, 0xc1, 0xf0, 0xe0, 0xf8, 
0x70, 0x7c, 0x3c, 0x3e, 0x1c, 0x1f, 0x0e, 0x0f, 0x8c, 0x60, 0xcc, 0x18, 0x18, 0x30, 0xff, 0xff, 
0xf7, 0xef, 0xff, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x03, 0xc1, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xcf, 0x61, 0xf3, 0xbe, 0xfc, 0x7c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0xf0, 0x01, 0xc3, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x0c, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x60, 0x00, 
0x30, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x00, 0x00, 0xc0, 0x18, 0x70, 
0x30, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x38, 0x00, 0x00, 0x00, 
0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x0c, 0x18, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0x80, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x0c, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xc0, 0x00, 0x00, 0x0c, 0xc0, 0x06, 0x60, 0x03, 
0x30, 0x31, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x00, 0x01, 0x80, 0x18, 0x30, 
0x30, 0x00, 0x00, 0x18, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x18, 0x00, 0x00, 0x00, 
0x03, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0x80, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x60, 0xc1, 0x9c, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x60, 0x00, 0x00, 0x07, 0x80, 0x03, 0xc0, 0x01, 
0xe0, 0x60, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x03, 0x00, 0x70, 0x60, 
0x60, 0x00, 0x00, 0x31, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x0c, 0x18, 0x00, 0x03, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x61, 0x83, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
};
#else
ROMDATA PEGUSHORT MyriadPro10bExt_offset_table[337] = {
0x0000,0x000a,0x0012,0x001c,0x0024,0x002e,0x0036,0x003e,0x0045,0x004d,0x0054,0x005c,0x0063,0x006b,0x0072,0x007b,
0x0086,0x0090,0x0098,0x009f,0x00a7,0x00ae,0x00b6,0x00bd,0x00c5,0x00cc,0x00d4,0x00db,0x00e3,0x00eb,0x00f3,0x00fb,
0x0103,0x010b,0x0113,0x011b,0x0123,0x012c,0x0134,0x013f,0x0148,0x014e,0x0152,0x0157,0x015b,0x0160,0x0164,0x0169,
0x016c,0x0171,0x0174,0x0180,0x0188,0x018e,0x0192,0x019a,0x01a1,0x01a8,0x01af,0x01b2,0x01b9,0x01bc,0x01c3,0x01c9,
0x01d0,0x01d6,0x01de,0x01e2,0x01ea,0x01f2,0x01fa,0x0202,0x020a,0x0212,0x021d,0x0225,0x022d,0x0236,0x023e,0x0247,
0x024f,0x0258,0x0260,0x026d,0x027a,0x0283,0x0289,0x0292,0x0298,0x02a1,0x02a7,0x02af,0x02b6,0x02be,0x02c5,0x02cd,
0x02d4,0x02dc,0x02e3,0x02ea,0x02f0,0x02f7,0x02ff,0x0306,0x030c,0x0314,0x031c,0x0324,0x032c,0x0334,0x033c,0x0344,
0x034c,0x0354,0x035c,0x0364,0x036c,0x037a,0x0385,0x038f,0x0398,0x03a2,0x03a9,0x03b0,0x03b7,0x03be,0x03c5,0x03cc,
0x03d0,0x03dd,0x03ea,0x03f7,0x0404,0x0411,0x041e,0x042b,0x0438,0x0445,0x0452,0x045f,0x046c,0x0479,0x0486,0x0493,
0x049c,0x04a9,0x04b6,0x04be,0x04cb,0x04d8,0x04e5,0x04f2,0x04ff,0x050c,0x0519,0x0526,0x0533,0x0540,0x054d,0x055a,
0x0567,0x0571,0x0579,0x0586,0x0593,0x05a0,0x05ad,0x05ba,0x05c7,0x05d4,0x05e1,0x05ee,0x05fb,0x0608,0x0615,0x0622,
0x062b,0x0634,0x0641,0x064e,0x065b,0x0668,0x0675,0x0682,0x068a,0x0697,0x06a4,0x06b1,0x06be,0x06cb,0x06d8,0x06e5,
0x06f2,0x06ff,0x070c,0x0719,0x0726,0x0733,0x0740,0x074d,0x075a,0x0767,0x0774,0x0781,0x078e,0x079b,0x07ab,0x07bb,
0x07cb,0x07db,0x07eb,0x07fb,0x080b,0x081b,0x082b,0x083b,0x084b,0x085b,0x086b,0x087b,0x088b,0x089b,0x08a8,0x08b5,
0x08c2,0x08cf,0x08dc,0x08e9,0x08f6,0x0900,0x0908,0x0910,0x0918,0x0920,0x0927,0x0931,0x0939,0x0946,0x0953,0x095b,
0x0962,0x096f,0x097c,0x0989,0x0996,0x09a3,0x09b3,0x09c0,0x09cd,0x09da,0x09e3,0x09ed,0x09f5,0x0a02,0x0a0e,0x0a17,
0x0a1f,0x0a2c,0x0a39,0x0a46,0x0a53,0x0a60,0x0a6d,0x0a7a,0x0a87,0x0a94,0x0aa1,0x0aae,0x0abb,0x0ac8,0x0ad5,0x0ae2,
0x0aef,0x0afc,0x0b09,0x0b16,0x0b23,0x0b30,0x0b3d,0x0b4a,0x0b57,0x0b5f,0x0b66,0x0b6d,0x0b73,0x0b80,0x0b8d,0x0b9a,
0x0ba7,0x0bb4,0x0bc1,0x0bce,0x0bdb,0x0be8,0x0bf5,0x0c02,0x0c0f,0x0c16,0x0c1e,0x0c2b,0x0c38,0x0c45,0x0c52,0x0c5f,
0x0c6c,0x0c79,0x0c86,0x0c93,0x0ca0,0x0cad,0x0cba,0x0cc7,0x0cd4,0x0ce1,0x0cee,0x0cfb,0x0d08,0x0d15,0x0d22,0x0d2f,
0x0d3c,0x0d49,0x0d56,0x0d63,0x0d70,0x0d7d,0x0d8a,0x0d97,0x0da4,0x0db1,0x0dbe,0x0dcb,0x0dd8,0x0de5,0x0df2,0x0dff,
0x0e0c,
};

ROMDATA PEGUBYTE MyriadPro10bExt_data_table[7200] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x03, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 
0xfc, 0x00, 0x00, 0x01, 0xfc, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x00, 0x30, 0x01, 0xc0, 0x01, 0x80, 0x0d, 0x80, 0x1b, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x00, 0xc0, 0x00, 0x00, 0x06, 0xc0, 0x07, 0x00, 0x08, 0x80, 
0x03, 0x00, 0x00, 0x00, 0x03, 0x86, 0xc0, 0x00, 0x00, 0x64, 0x00, 0x11, 0x00, 0x06, 0x00, 0x00, 
0x00, 0x38, 0x00, 0x00, 0x00, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 
0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x6c, 0x00, 0x18, 0x00, 0xe0, 0x00, 0x00, 0x06, 0xc0, 0x00, 0x00, 0x6c, 0x00, 
0x00, 0x03, 0x20, 0x00, 0x00, 0x04, 0x40, 0x02, 0x40, 0x03, 0x60, 0x00, 0x00, 0x00, 0x38, 0x00, 
0x00, 0x70, 0x00, 0x33, 0x06, 0x00, 0x30, 0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 
0x78, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 
0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x36, 0x00, 0x6c, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x20, 0x18, 
0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x3e, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x60, 0x03, 0x60, 0x01, 0x80, 0x07, 0x00, 0x0e, 0x00, 
0x00, 0x00, 0x00, 0x7c, 0x00, 0x70, 0x00, 0xc0, 0x00, 0x00, 0x03, 0x80, 0x0d, 0x80, 0x07, 0x00, 
0x03, 0x00, 0x00, 0x00, 0x06, 0xc0, 0x00, 0x00, 0x00, 0x98, 0x3c, 0x0e, 0x00, 0x06, 0x00, 0x00, 
0x00, 0x6d, 0x80, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 
0x0e, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x70, 0x00, 0xd8, 0x36, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x00, 0x38, 0x00, 0x30, 0x01, 0xb0, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x38, 0x00, 
0x00, 0x04, 0xc0, 0x07, 0xc0, 0x03, 0x80, 0x02, 0x41, 0x86, 0xc3, 0x60, 0x00, 0x00, 0x6c, 0x00, 
0x00, 0xd8, 0x00, 0x33, 0x0c, 0x00, 0x30, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0xfc, 0x00, 0x30, 0x00, 0xfc, 0x00, 0x78, 0x00, 0xfc, 0x03, 
0x03, 0x00, 0xfc, 0x00, 0xcc, 0x01, 0xfc, 0x00, 0xcc, 0x00, 0x3c, 0x00, 0xcc, 0x00, 0xfc, 0x00, 
0xcc, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1c, 0x00, 0x38, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x40, 0x40, 0x30, 
0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x02, 0x20, 0x00, 0x00, 0x00, 0x60, 0x01, 0xc0, 0x03, 0x00, 0x0d, 0x80, 0x00, 
0xcc, 0x00, 0x06, 0x00, 0x00, 0x01, 0x10, 0x00, 0xc0, 0x00, 0x00, 0x0d, 0x80, 0x07, 0x00, 0x08, 
0x80, 0x03, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x00, 0xc0, 0x03, 0x40, 0x00, 0x90, 0x60, 0x00, 0x00, 
0xc6, 0x01, 0x80, 0x30, 0x00, 0x01, 0x80, 0x60, 0xd9, 0x80, 0xc0, 0x01, 0x80, 0x03, 0x00, 0x00, 
0x00, 0x1b, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x30, 0x00, 0xe0, 0x00, 0x00, 0x06, 0xc0, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x04, 0x41, 0x82, 0x40, 0x03, 0x60, 0x00, 0x00, 0x00, 0x03, 
0x80, 0x00, 0x38, 0x00, 0x00, 0x0c, 0x00, 0x60, 0x03, 0x67, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x80, 0x07, 0xfe, 0x3f, 0xf0, 0x01, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0x00, 0x00, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x00, 0x20, 0x07, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x00, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x80, 0x78, 0x00, 0x30, 0x00, 0x30, 0x00, 0x70, 0x01, 0xce, 0x00, 0x70, 0x03, 
0x03, 0x00, 0x70, 0x03, 0x03, 0x00, 0xcc, 0x03, 0x03, 0x00, 0xcc, 0x03, 0x03, 0x00, 0xcc, 0x03, 
0x03, 0x00, 0xcc, 0x0f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf0, 0x00, 
0x00, 0x00, 0x6c, 0x00, 0xc0, 0x00, 0x00, 0x3f, 0xf1, 0xff, 0x80, 0x0d, 0x9f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x80, 0x60, 0x0f, 0xfc, 0x7f, 0xe3, 0xff, 0x0e, 0x04, 0x40, 0xc0, 0x00, 
0x00, 0xc0, 0x04, 0x30, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x0f, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf8, 0x00, 0x01, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc0, 

0x1c, 0x1f, 0x07, 0x01, 0xc1, 0xc0, 0x01, 0xf0, 0xc3, 0xe3, 0x67, 0xc3, 0x0f, 0x87, 0x3f, 0x00, 
0xcd, 0xf8, 0x1f, 0xfc, 0xf9, 0xf8, 0xe3, 0xf0, 0xc7, 0xe0, 0x0f, 0xc7, 0x0f, 0x8d, 0x8f, 0x87, 
0x0f, 0x83, 0x0f, 0x86, 0x18, 0x6c, 0x06, 0x0d, 0xf0, 0x7a, 0xfd, 0xfe, 0x6f, 0x6f, 0x0f, 0x1e, 
0xc6, 0x7a, 0x71, 0xb0, 0x00, 0xc1, 0xb0, 0x6c, 0xd9, 0xe0, 0xc1, 0x81, 0xb8, 0x86, 0x38, 0x80, 
0x38, 0x8e, 0x18, 0x07, 0x10, 0x03, 0xf1, 0xf1, 0xf8, 0x70, 0xfc, 0x6c, 0x7f, 0xf0, 0x00, 0x3f, 
0x06, 0x7e, 0x00, 0xfc, 0x38, 0xf8, 0x61, 0xf1, 0xb3, 0xe0, 0x07, 0xc3, 0x9f, 0x98, 0xfc, 0xc7, 
0xf9, 0x8c, 0x64, 0xcc, 0x67, 0xcc, 0x63, 0x8c, 0x62, 0x4c, 0x66, 0xcc, 0x60, 0x06, 0x31, 0x86, 
0xc3, 0x0c, 0x6c, 0x61, 0xbf, 0x18, 0xfc, 0x63, 0xf1, 0xcc, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x8f, 0x84, 0x02, 0x20, 0x10, 0x1d, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0xfc, 0x80, 0xa0, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0xc0, 0x14, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x3f, 0x90, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x80, 0x78, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x03, 0x87, 0x00, 0x00, 0x03, 
0x03, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x03, 
0x03, 0x00, 0x00, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x10, 0x7e, 
0x00, 0x7c, 0x38, 0xc6, 0xc0, 0x7c, 0x00, 0x20, 0x11, 0x00, 0x9f, 0xc7, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x80, 0x00, 0x08, 0x04, 0x40, 0x22, 0x01, 0x06, 0x03, 0x81, 0x20, 0xff, 
0x81, 0x81, 0xf8, 0x60, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0xf8, 0x03, 0xf3, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0xf8, 0x01, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0x1c, 0x00, 0x07, 0x00, 0x01, 0xc0, 0x03, 0x08, 0x06, 0x10, 0x0c, 0x20, 0x18, 0x40, 0x31, 0x80, 
0xd9, 0x8c, 0x06, 0xc0, 0x01, 0x80, 0x03, 0x00, 0x06, 0x00, 0x0c, 0x00, 0x18, 0x40, 0x18, 0x40, 
0x18, 0x40, 0x18, 0x40, 0x18, 0x6c, 0x0f, 0xfe, 0xc0, 0x30, 0x18, 0x0c, 0x06, 0x06, 0x06, 0x06, 
0x00, 0x18, 0x33, 0x30, 0x00, 0xc1, 0xb0, 0x6d, 0x9b, 0x60, 0xc1, 0x81, 0xb8, 0x80, 0x38, 0x80, 
0x38, 0x80, 0x30, 0x07, 0x10, 0x06, 0x18, 0x03, 0x0c, 0x01, 0x86, 0x00, 0xc3, 0x00, 0x00, 0x31, 
0x80, 0x63, 0x00, 0xc6, 0x01, 0x84, 0x03, 0x08, 0x06, 0x10, 0x0c, 0x20, 0x06, 0x18, 0x30, 0xcc, 
0x61, 0x8c, 0x60, 0x0c, 0x60, 0x0c, 0x60, 0x0c, 0x61, 0x8c, 0x60, 0x0c, 0x60, 0x06, 0x31, 0x80, 
0x03, 0x0c, 0x00, 0x61, 0x83, 0x00, 0x0c, 0x00, 0x30, 0x0c, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0xc4, 0x02, 0x20, 0x10, 0x31, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x86, 0x80, 0xa0, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0x80, 0x14, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x90, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x80, 0xfc, 0x00, 0xfc, 0x00, 0x30, 0x00, 0x30, 0x03, 0x03, 0x00, 0xfc, 0x03, 
0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x82, 
0x00, 0xc2, 0x00, 0xcc, 0xc0, 0xc6, 0x00, 0x20, 0x11, 0x00, 0x80, 0xc0, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x80, 0xff, 0x08, 0x04, 0x40, 0x22, 0x01, 0x00, 0x03, 0x80, 0xc1, 0x98, 
0x00, 0x03, 0x1c, 0x04, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x84, 0x00, 0xc3, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0x80, 0x01, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0x36, 0x0f, 0x0d, 0x83, 0xc3, 0x60, 0xf3, 0x09, 0xe6, 0x13, 0xcc, 0x27, 0x98, 0x4f, 0x30, 0xcf, 
0xd9, 0x86, 0x7e, 0xc0, 0xf9, 0x81, 0xf3, 0x03, 0xe6, 0x07, 0xcc, 0x0f, 0x98, 0x4f, 0xd8, 0x4f, 
0xd8, 0x4f, 0xd8, 0x4f, 0xd8, 0x6d, 0xc6, 0x0c, 0xdc, 0x31, 0x98, 0xcc, 0x66, 0x66, 0x66, 0x06, 
0xce, 0x1b, 0xb6, 0x33, 0x66, 0xc1, 0xb0, 0x6d, 0x9b, 0x60, 0xc1, 0xa1, 0xac, 0xb7, 0x2c, 0xb7, 
0x2c, 0xb7, 0x36, 0xe5, 0x96, 0xe6, 0x19, 0xf3, 0x0c, 0xf9, 0x86, 0x7c, 0xc3, 0x03, 0xef, 0x31, 
0x9b, 0x63, 0x36, 0xc6, 0x6d, 0x84, 0xf3, 0x09, 0xe6, 0x13, 0xcc, 0x27, 0x86, 0x3e, 0x31, 0xfc, 
0x63, 0xec, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x66, 0x31, 0xb3, 
0x31, 0x98, 0xc6, 0x33, 0x03, 0x7e, 0x0d, 0xf8, 0x37, 0xec, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x80, 0x64, 0x02, 0x20, 0x10, 0x61, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x87, 0x3f, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0x98, 0xf4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x03, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x80, 0xcc, 0x01, 0xce, 0x00, 0x30, 0x00, 0x30, 0x07, 0x03, 0x81, 0xce, 0x03, 
0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x13, 0x00, 
0x7e, 0xc2, 0x7e, 0xd8, 0xcd, 0x83, 0x3e, 0x20, 0x11, 0x00, 0x81, 0x9f, 0x90, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x81, 0xcc, 0x08, 0x04, 0x40, 0x22, 0x01, 0x1f, 0x86, 0xc1, 0xe1, 0x98, 
0x0e, 0xf3, 0x2c, 0xf8, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x84, 0xf0, 0xc7, 0xc8, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0x81, 0xf1, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0x36, 0x11, 0x8d, 0x84, 0x63, 0x61, 0x1b, 0x03, 0x16, 0x06, 0x2c, 0x0c, 0x58, 0x18, 0xb0, 0xd8, 
0xc1, 0x86, 0xc6, 0xc1, 0x8d, 0x83, 0x1b, 0x06, 0x36, 0x0c, 0x6c, 0x18, 0xd8, 0x18, 0xd8, 0x18, 
0xd8, 0x18, 0xd8, 0x18, 0xd8, 0x6e, 0x66, 0x0c, 0xe6, 0x31, 0x98, 0xcc, 0x66, 0x66, 0x66, 0x06, 
0xc6, 0x19, 0xbc, 0x36, 0x6c, 0xc1, 0xb0, 0x6c, 0x18, 0x66, 0xd9, 0xc1, 0xec, 0xb9, 0xac, 0xb9, 
0xac, 0xb9, 0x87, 0x35, 0x97, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0x06, 0x31, 0xb1, 
0x9f, 0x63, 0x3e, 0xc6, 0x7d, 0x81, 0x8b, 0x03, 0x16, 0x06, 0x2c, 0x0c, 0x46, 0x18, 0x30, 0xc0, 
0x61, 0x8c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x66, 0x79, 0xb3, 
0x31, 0x98, 0xc6, 0x33, 0x06, 0x06, 0x18, 0x18, 0x60, 0x6c, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x9f, 0xe4, 0x02, 0x20, 0x10, 0x61, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x86, 0x63, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0x98, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x06, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x81, 0xcc, 0x00, 0x0e, 0x00, 0x30, 0x00, 0x30, 0x07, 0x03, 0x81, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x13, 0x00, 
0xc6, 0xc0, 0xc6, 0xf0, 0xd9, 0x83, 0x63, 0x20, 0x11, 0x00, 0x83, 0x03, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x81, 0xcc, 0x08, 0x04, 0x40, 0x22, 0x01, 0x1d, 0x86, 0xc2, 0x31, 0x98, 
0x13, 0x1b, 0x2d, 0x9c, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x81, 0x88, 0xc3, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0x83, 0x19, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0x63, 0x01, 0x98, 0xc0, 0x66, 0x30, 0x1b, 0x03, 0x06, 0x06, 0x0c, 0x0c, 0x18, 0x18, 0x30, 0xd8, 
0xc3, 0xe6, 0xc6, 0xf9, 0x8d, 0xf3, 0x1b, 0xe6, 0x37, 0xcc, 0x6f, 0x98, 0xd9, 0xd8, 0xd9, 0xd8, 
0xd9, 0xd8, 0xd9, 0xd8, 0xdf, 0xec, 0x67, 0xfc, 0xc6, 0x31, 0x98, 0xcc, 0x66, 0x66, 0x66, 0x06, 
0xc6, 0x19, 0xb8, 0x3c, 0x78, 0xc1, 0xb0, 0x6c, 0x18, 0x66, 0xd9, 0x83, 0xa6, 0xb1, 0xa6, 0xb1, 
0xa6, 0xb1, 0x86, 0x34, 0xd6, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0xf6, 0x31, 0xbf, 
0x18, 0x7e, 0x30, 0xfc, 0x60, 0xf9, 0xc1, 0xf3, 0x83, 0xe7, 0x07, 0xce, 0x06, 0x18, 0x30, 0xc0, 
0xf1, 0x8c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x63, 0x7b, 0x1b, 
0x60, 0xf0, 0xc6, 0x1e, 0x0c, 0x0c, 0x30, 0x30, 0xc0, 0xcc, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x98, 0x64, 0x02, 0x20, 0x10, 0xf9, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x86, 0x63, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0x98, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x0f, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x81, 0xfe, 0x00, 0x7e, 0x00, 0x30, 0x00, 0x30, 0x07, 0x03, 0x81, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x13, 0x3e, 
0xc6, 0xce, 0xc6, 0xe0, 0xf1, 0x83, 0x63, 0x20, 0x11, 0x00, 0x87, 0x86, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x81, 0xdc, 0x08, 0x04, 0x40, 0x22, 0x01, 0x19, 0x8c, 0x60, 0x33, 0x1f, 
0x83, 0x1b, 0x4d, 0x9c, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0xf9, 0xc0, 0xc3, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0xfb, 0x19, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0x7f, 0x1f, 0x9f, 0xc7, 0xe7, 0xf1, 0xfb, 0x03, 0x06, 0x06, 0x0c, 0x0c, 0x18, 0x18, 0x30, 0xd8, 
0xc1, 0x86, 0xc6, 0xc1, 0xfd, 0x83, 0xfb, 0x07, 0xf6, 0x0f, 0xec, 0x1f, 0xd8, 0xd8, 0xd8, 0xd8, 
0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0x6c, 0x66, 0x0c, 0xc6, 0x31, 0x98, 0xcc, 0x66, 0x66, 0x66, 0x06, 
0xc6, 0x19, 0xbc, 0x38, 0x70, 0xc1, 0xb0, 0x6c, 0x18, 0x60, 0xc3, 0x81, 0xa6, 0xb1, 0xa6, 0xb1, 
0xa6, 0xb1, 0x86, 0x34, 0xd6, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0x06, 0x3f, 0xb6, 
0x18, 0x6c, 0x30, 0xd8, 0x60, 0x0c, 0xf0, 0x19, 0xe0, 0x33, 0xc0, 0x67, 0x86, 0x18, 0x30, 0xc0, 
0x63, 0xcc, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x63, 0xcf, 0x1b, 
0x60, 0x60, 0x6c, 0x0c, 0x18, 0x18, 0x60, 0x61, 0x81, 0x8c, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x98, 0x64, 0x02, 0x20, 0x10, 0x61, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x86, 0x63, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0x98, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x03, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x81, 0x86, 0x01, 0xc6, 0x00, 0x30, 0x00, 0x30, 0x03, 0x03, 0x01, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 0x03, 0x01, 0x86, 0x03, 
0x03, 0x01, 0x86, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x13, 0x06, 
0xc6, 0xc6, 0xc6, 0xf0, 0xe1, 0x83, 0x63, 0x20, 0x11, 0x00, 0x81, 0x87, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x80, 0xf8, 0x08, 0x04, 0x40, 0x22, 0x01, 0x19, 0x8f, 0xe3, 0xf3, 0xf8, 
0x1f, 0xfb, 0x4d, 0xac, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x0c, 0xf0, 0xc3, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0x83, 0xf9, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0x63, 0x31, 0x98, 0xcc, 0x66, 0x33, 0x1b, 0x0b, 0x06, 0x16, 0x0c, 0x2c, 0x18, 0x58, 0x30, 0xd8, 
0xc1, 0x86, 0xc6, 0xc1, 0x81, 0x83, 0x03, 0x06, 0x06, 0x0c, 0x0c, 0x18, 0x18, 0xd8, 0xd8, 0xd8, 
0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0x6c, 0x66, 0x0c, 0xc6, 0x31, 0x98, 0xcc, 0x66, 0x66, 0x66, 0x06, 
0xc6, 0x19, 0xb6, 0x3c, 0x78, 0xc1, 0xb0, 0x6c, 0x18, 0x60, 0xc1, 0x81, 0xa3, 0xb1, 0xa3, 0xb1, 
0xa3, 0xb1, 0x86, 0x34, 0x76, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0x06, 0x30, 0x33, 
0x18, 0x66, 0x30, 0xcc, 0x61, 0x0c, 0x3a, 0x18, 0x74, 0x30, 0xe8, 0x61, 0xc6, 0x18, 0x30, 0xc0, 
0x61, 0x8c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x63, 0xcf, 0x1c, 
0xe0, 0x60, 0x6c, 0x0c, 0x30, 0x30, 0xc0, 0xc3, 0x03, 0x0c, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x98, 0x64, 0x02, 0x20, 0x10, 0x61, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x86, 0x63, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0x98, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x90, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x83, 0x87, 0x01, 0x86, 0x00, 0x30, 0x00, 0x30, 0x03, 0x87, 0x01, 0x86, 0x01, 
0x87, 0x01, 0x86, 0x01, 0x87, 0x01, 0x86, 0x01, 0x87, 0x01, 0x86, 0x01, 0x87, 0x01, 0x86, 0x01, 
0x87, 0x01, 0x86, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x13, 0x8f, 
0xc6, 0xc6, 0xc6, 0xd8, 0xf1, 0x83, 0x63, 0x20, 0x11, 0x00, 0x80, 0xc3, 0x90, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x81, 0x80, 0x08, 0x04, 0x40, 0x22, 0x01, 0x19, 0x8c, 0x66, 0x33, 0x18, 
0x33, 0x03, 0x4d, 0xcc, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x0c, 0x38, 0xc3, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0x83, 0x01, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0xc1, 0xb1, 0xb0, 0x6c, 0x6c, 0x1b, 0x1b, 0x0b, 0x16, 0x16, 0x2c, 0x2c, 0x58, 0x58, 0xb1, 0x99, 
0xc1, 0x8c, 0xce, 0xc1, 0x85, 0x83, 0x0b, 0x06, 0x16, 0x0c, 0x2c, 0x18, 0x58, 0xd9, 0xd8, 0xd9, 
0xd8, 0xd9, 0xd8, 0xd9, 0xd8, 0x6c, 0x66, 0x0c, 0xc6, 0x31, 0x98, 0xcc, 0x66, 0x66, 0x66, 0x06, 
0xc6, 0x19, 0xb3, 0x36, 0x6c, 0xc1, 0xb0, 0x6c, 0x18, 0x60, 0xc1, 0x81, 0xa3, 0xb1, 0xa3, 0xb1, 
0xa3, 0xb1, 0x86, 0x34, 0x76, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0x06, 0x30, 0xb1, 
0x98, 0x63, 0x30, 0xc6, 0x61, 0x0d, 0x1a, 0x1a, 0x34, 0x34, 0x68, 0x68, 0xc6, 0x18, 0x30, 0xc0, 
0x61, 0x8c, 0x6c, 0xec, 0x6c, 0xec, 0x6c, 0xec, 0x6c, 0xec, 0x6c, 0xec, 0x6c, 0xe1, 0x86, 0x0c, 
0xc0, 0x60, 0x38, 0x0c, 0x30, 0x60, 0xc1, 0x83, 0x06, 0x0c, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 
0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x8c, 0xc4, 0x02, 0x20, 0x10, 0x61, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x86, 0x63, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x31, 0x99, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x01, 0x90, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 
0x20, 0x11, 0x00, 0x83, 0x03, 0x01, 0xce, 0x00, 0x30, 0x00, 0x30, 0x01, 0xce, 0x01, 0xce, 0x01, 
0xce, 0x01, 0xde, 0x01, 0xce, 0x01, 0xde, 0x01, 0xce, 0x01, 0xde, 0x01, 0xce, 0x01, 0xde, 0x01, 
0xce, 0x01, 0xde, 0x08, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0xc6, 
0x7e, 0xc6, 0xce, 0xcc, 0xd8, 0xc6, 0x63, 0x20, 0x11, 0x00, 0x80, 0xc1, 0x90, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x81, 0xfe, 0x08, 0x04, 0x40, 0x22, 0x01, 0x19, 0x98, 0x36, 0x36, 0x18, 
0x33, 0x0b, 0x8d, 0xcc, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 
0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 
0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x09, 0x0d, 0x18, 0xc3, 0x08, 0x04, 
0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 
0x10, 0x09, 0x83, 0x09, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 
0x80, 0x40, 

0xc1, 0x9f, 0xb0, 0x67, 0xec, 0x19, 0xf9, 0xf1, 0xe3, 0xe3, 0xc7, 0xc7, 0x8f, 0x8f, 0x3f, 0x0e, 
0xc1, 0xf8, 0x76, 0xfc, 0xf9, 0xf9, 0xf3, 0xf3, 0xe7, 0xe7, 0xcf, 0xcf, 0x8f, 0xce, 0xcf, 0xce, 
0xcf, 0xce, 0xcf, 0xce, 0xd8, 0x6c, 0x66, 0x0c, 0xc6, 0x79, 0xbc, 0xde, 0x6f, 0x6f, 0x6f, 0x7c, 
0xc6, 0xf1, 0xb1, 0xb3, 0x66, 0xfd, 0xbf, 0x6f, 0xd8, 0x7e, 0xc1, 0xf9, 0xa1, 0xb1, 0xa1, 0xb1, 
0xa1, 0xb1, 0x86, 0x34, 0x36, 0x33, 0xf1, 0xf1, 0xf8, 0xf8, 0xfc, 0x7c, 0x7f, 0xf3, 0xef, 0x30, 
0xd8, 0x61, 0xb0, 0xc3, 0x60, 0xf8, 0xf1, 0xf1, 0xe3, 0xe3, 0xc7, 0xc7, 0x86, 0x0e, 0x30, 0x70, 
0x60, 0xe7, 0xc7, 0x67, 0xc7, 0x67, 0xc7, 0x67, 0xc7, 0x67, 0xc7, 0x67, 0xc7, 0x61, 0x86, 0x0c, 
0xc0, 0x60, 0x38, 0x0c, 0x3f, 0x7e, 0xfd, 0xfb, 0xf7, 0xec, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 
0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x87, 0x87, 0xfe, 0x3f, 0xf0, 0xc1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xfc, 0x3e, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0x0e, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x01, 0x9f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 
0x3f, 0xf1, 0xff, 0x87, 0x03, 0x80, 0xfe, 0x00, 0x30, 0x00, 0x30, 0x00, 0x78, 0x00, 0x78, 0x00, 
0xfc, 0x00, 0xfe, 0x00, 0xfc, 0x00, 0xfe, 0x00, 0xfc, 0x00, 0xfe, 0x00, 0xfc, 0x00, 0xfe, 0x00, 
0xfc, 0x00, 0xfe, 0x0f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf0, 0xfc, 
0x06, 0x7e, 0x76, 0xc6, 0xcc, 0x7c, 0x3e, 0x3f, 0xf1, 0xff, 0x80, 0xc1, 0x9f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x81, 0x86, 0x0f, 0xfc, 0x7f, 0xe3, 0xff, 0x3f, 0xd8, 0x33, 0xf6, 0x1f, 
0x9c, 0xf1, 0xf8, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 
0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 
0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xf8, 0xf0, 0xc1, 0xcf, 0xfc, 
0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 
0x1f, 0xf9, 0xf9, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 
0xff, 0xc0, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0xc0, 0x00, 0xc1, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x40, 0x00, 0x00, 
0x06, 0x01, 0x84, 0x04, 0x00, 0x00, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x10, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x01, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xff, 0x00, 0x06, 0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0xc1, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x10, 0xc0, 0x10, 
0xc0, 0x10, 0xc1, 0x10, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x40, 0x00, 0x00, 
0x06, 0x01, 0x84, 0x04, 0x00, 0x00, 0x04, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x10, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x01, 0x02, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x40, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x86, 0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x11, 0x91, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x30, 0xc1, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x60, 0x00, 0x00, 0x0f, 0x80, 0x0f, 
0x80, 0x0f, 0x86, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb0, 0x00, 0x00, 
0x1c, 0x03, 0x18, 0x18, 0x00, 0x00, 0x18, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 
0x00, 0x00, 0x00, 0x00, 0xe0, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x81, 0x80, 0x00, 0x06, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x7c, 0x00, 0x7c, 0x00, 0x00, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x1f, 0x1f, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x61, 0x83, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc1, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 
};
#endif

#if defined(_FONT_PLASTIMO_)
xFONTYY MyriadPro10bExt_Font = {0x01, 13, 3, 15, 1, 0, 16, 206, 0x0100, 0x024f,
(PEGUSHORT *) MyriadPro10bExt_offset_table,&MyriadPro10bGrk_Font,
(PEGUBYTE *) MyriadPro10bExt_data_table};
#else
xFONTYY MyriadPro10bExt_Font = {0x01, 13, 3, 16, 0, 0, 16, 450, 0x0100, 0x024f,
(PEGUSHORT *) MyriadPro10bExt_offset_table,&MyriadPro10bGrk_Font,
(PEGUBYTE *) MyriadPro10bExt_data_table};
#endif


#if defined(_FONT_PLASTIMO_)
ROMDATA PEGUSHORT MyriadPro10bEng_offset_table[257] = {
0x0000,0x0009,0x0011,0x0019,0x0021,0x0029,0x0031,0x0039,0x0041,0x0049,0x0051,0x0059,0x0061,0x006a,0x0073,0x007c,
0x008c,0x0094,0x009c,0x00a5,0x00ae,0x00b7,0x00c0,0x00c9,0x00d2,0x00db,0x00e4,0x00ed,0x00f6,0x00ff,0x0108,0x0111,
0x011a,0x0122,0x0125,0x012a,0x0131,0x0138,0x0143,0x014c,0x014f,0x0154,0x0158,0x015e,0x0166,0x0169,0x016d,0x0170,
0x0176,0x017e,0x0186,0x018e,0x0196,0x019e,0x01a6,0x01ae,0x01b6,0x01be,0x01c6,0x01c9,0x01cc,0x01d5,0x01dd,0x01e5,
0x01eb,0x01f5,0x01fd,0x0204,0x020c,0x0215,0x021d,0x0224,0x022d,0x0236,0x023a,0x0241,0x0249,0x0250,0x025b,0x0264,
0x026d,0x0274,0x027d,0x0284,0x028c,0x0294,0x029d,0x02a5,0x02af,0x02b7,0x02bf,0x02c7,0x02cb,0x02d0,0x02d4,0x02dc,
0x02e4,0x02e8,0x02ef,0x02f7,0x02fe,0x0306,0x030d,0x0312,0x031a,0x0322,0x0326,0x032b,0x0332,0x0335,0x0340,0x0348,
0x0350,0x0358,0x0360,0x0365,0x036b,0x0370,0x0378,0x037f,0x0389,0x0390,0x0397,0x039d,0x03a1,0x03a4,0x03a8,0x03b0,
0x03b9,0x03bc,0x03bf,0x03c2,0x03c5,0x03c8,0x03cb,0x03ce,0x03d1,0x03d4,0x03d7,0x03da,0x03dd,0x03e0,0x03e3,0x03e6,
0x03e9,0x03ec,0x03ef,0x03f2,0x03f5,0x03f8,0x03fb,0x03fe,0x0401,0x0404,0x0407,0x040a,0x040d,0x0410,0x0413,0x0416,
0x0419,0x041c,0x041f,0x0426,0x042d,0x0434,0x043c,0x043f,0x0446,0x044b,0x0455,0x045a,0x0460,0x0468,0x046c,0x0473,
0x0477,0x047c,0x0484,0x0489,0x048e,0x0493,0x049b,0x04a2,0x04a5,0x04a9,0x04ad,0x04b2,0x04b8,0x04c3,0x04cd,0x04d8,
0x04de,0x04e6,0x04ee,0x04f6,0x04ff,0x0508,0x0510,0x051b,0x0524,0x052b,0x0532,0x0539,0x0540,0x0543,0x0547,0x054b,
0x054f,0x0558,0x0561,0x056a,0x0573,0x057c,0x0585,0x058e,0x0596,0x059f,0x05a8,0x05b1,0x05ba,0x05c3,0x05cb,0x05d2,
0x05da,0x05e1,0x05e8,0x05ef,0x05f6,0x05fd,0x0604,0x060f,0x0615,0x061c,0x0623,0x062a,0x0631,0x0634,0x0638,0x063c,
0x0640,0x0647,0x064f,0x0657,0x065f,0x0667,0x066f,0x0677,0x067f,0x0687,0x068f,0x0697,0x069f,0x06a7,0x06ae,0x06b6,
0x06bd,
};


ROMDATA PEGUBYTE MyriadPro10bEng_data_table[3456] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xfe, 
0xff, 0x7f, 0xbf, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x30, 0x00, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x0c, 0x28, 0x28, 0x28, 0x28, 0x28, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x06, 0x00, 0x33, 0xc1, 0xb0, 0xf8, 0x30, 0x00, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x07, 0x70, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 
0x6c, 0x1c, 0x00, 0x00, 0x07, 0x83, 0x8f, 0x1e, 0x00, 0x1e, 0x00, 0x18, 0x0e, 0x03, 0x83, 0xc1, 
0xe0, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x12, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x04, 0x04, 0x28, 0x1e, 0x00, 0x00, 0xe0, 
0x38, 0x07, 0x00, 0x77, 0xe1, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x0f, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x98, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x30, 0xf0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xef, 0xe0, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x38, 0x1c, 0x0e, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 0x0e, 0x04, 0x10, 0x33, 0x00, 0x07, 0xe0, 
0x38, 0x07, 0x80, 0xf1, 0x81, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x1f, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1b, 0xcf, 0x0c, 0x71, 0x87, 0x86, 0x7e, 0x78, 0x00, 0x00, 0x0c, 0x60, 
0x70, 0xf1, 0xe0, 0x18, 0xf8, 0x71, 0xf8, 0xf0, 0xe0, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0xc3, 
0xe1, 0xf7, 0xe3, 0xf3, 0xf0, 0xfb, 0x19, 0x83, 0x33, 0xb0, 0x30, 0xcc, 0x31, 0xe3, 0xe1, 0xe3, 
0xe3, 0xef, 0xf6, 0x36, 0x1a, 0x23, 0xcf, 0xce, 0xfe, 0xf8, 0x70, 0xc0, 0x0c, 0x00, 0xc0, 0x00, 
0x18, 0x01, 0xc0, 0x18, 0x18, 0xcc, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0xb6, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x78, 0x06, 0x76, 0x7b, 0x67, 0xe0, 0x00, 0x00, 0x07, 0xc0, 0xe0, 
0x01, 0xb8, 0x60, 0x03, 0xc0, 0x30, 0x00, 0x71, 0x9c, 0x63, 0x8c, 0x00, 0x60, 0x60, 0x60, 0x70, 
0x38, 0x18, 0x0f, 0xe1, 0xe7, 0xef, 0xdf, 0xbf, 0x00, 0x0c, 0xfc, 0x61, 0x8f, 0x07, 0x83, 0xc1, 
0xe0, 0xf0, 0x00, 0x7e, 0xc6, 0x63, 0x31, 0x98, 0xdc, 0xec, 0x0f, 0x0c, 0x0e, 0x18, 0x79, 0xb1, 
0xe0, 0x00, 0x01, 0x80, 0xc3, 0x0f, 0x63, 0x6f, 0x3c, 0x78, 0x70, 0x1c, 0x30, 0x78, 0x78, 0x00, 
0x00, 0x60, 0x18, 0x30, 0x78, 0x38, 0x00, 0xf0, 

0x00, 0x0c, 0x07, 0x0f, 0x9f, 0x8f, 0x00, 0x06, 0x0c, 0x1f, 0x04, 0x0f, 0x33, 0x00, 0x06, 0xe0, 
0x38, 0x07, 0xc1, 0xf1, 0x81, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1b, 0xcf, 0x1f, 0xdb, 0x0c, 0xc6, 0x66, 0x70, 0x00, 0x00, 0x18, 0xf0, 
0xf1, 0x9b, 0x30, 0x38, 0xc0, 0xc0, 0x19, 0x99, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x63, 0xe1, 0xe3, 
0x33, 0x06, 0x33, 0x03, 0x03, 0x83, 0x19, 0x83, 0x36, 0x30, 0x39, 0xce, 0x33, 0x33, 0x33, 0x33, 
0x36, 0x01, 0x86, 0x33, 0x33, 0x76, 0xcc, 0xcc, 0x0c, 0xd8, 0x31, 0xc0, 0x06, 0x00, 0xc0, 0x00, 
0x18, 0x03, 0x00, 0x18, 0x00, 0x0c, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0x33, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x38, 0xc0, 0x06, 0x66, 0xc0, 0x0c, 0x37, 0x80, 0x00, 0x0f, 0xff, 0xb0, 
0x03, 0x0c, 0xc0, 0x0f, 0xc0, 0x33, 0x80, 0x33, 0x0c, 0xc0, 0xd8, 0x00, 0xf0, 0xf0, 0xf0, 0x70, 
0x78, 0x1c, 0x1e, 0x07, 0x06, 0x0c, 0x18, 0x30, 0x6c, 0xcc, 0xc6, 0x71, 0x99, 0x8c, 0xc6, 0x63, 
0x31, 0x98, 0x00, 0xcc, 0xc6, 0x63, 0x31, 0x98, 0xcc, 0xcc, 0x19, 0x8e, 0x0c, 0x38, 0xc0, 0x00, 
0xc0, 0x00, 0x00, 0xc1, 0x87, 0x80, 0x36, 0xf0, 0x18, 0x70, 0x38, 0x38, 0x7c, 0x60, 0x00, 0x00, 
0x00, 0x30, 0x30, 0x78, 0x00, 0x71, 0x80, 0x00, 

0x00, 0x00, 0x0f, 0x8f, 0x9f, 0x9f, 0x99, 0x87, 0x1c, 0x3f, 0x84, 0x19, 0x9e, 0x00, 0x07, 0xe3, 
0xff, 0x87, 0xe3, 0xf1, 0x81, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x06, 0x03, 0x00, 0xc0, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1b, 0xcf, 0x30, 0xdb, 0x0c, 0xc6, 0xc3, 0xfc, 0x60, 0x00, 0x19, 0x98, 
0x30, 0x18, 0x30, 0x78, 0xc1, 0x80, 0x31, 0x99, 0x98, 0x00, 0x18, 0x06, 0x00, 0x66, 0x31, 0xe3, 
0x36, 0x06, 0x1b, 0x03, 0x06, 0x03, 0x19, 0x83, 0x36, 0x30, 0x79, 0xef, 0x36, 0x1b, 0x36, 0x1b, 
0x36, 0x01, 0x86, 0x33, 0x33, 0x76, 0x78, 0x78, 0x0c, 0xcc, 0x33, 0x60, 0x00, 0x00, 0xc0, 0x00, 
0x18, 0x03, 0x00, 0x18, 0x00, 0x0c, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0x33, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x60, 0xc3, 0xf3, 0xc6, 0xe0, 0x1b, 0xf0, 0xc0, 0x00, 0x0f, 0x61, 0xb1, 
0x87, 0xf8, 0x00, 0x0f, 0xc0, 0x36, 0xc0, 0x33, 0x0c, 0xc0, 0xd8, 0x00, 0xf0, 0xf0, 0xf0, 0xf0, 
0x6c, 0x1e, 0x1e, 0x0c, 0x06, 0x0c, 0x18, 0x30, 0x6c, 0xcc, 0xc3, 0x79, 0xb0, 0xd8, 0x6c, 0x36, 
0x1b, 0x0c, 0x01, 0x9e, 0xc6, 0x63, 0x31, 0x98, 0xc7, 0x8f, 0x99, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 

0x00, 0x00, 0x18, 0x8c, 0x06, 0x19, 0x99, 0xbf, 0xbf, 0x84, 0x04, 0x10, 0x0c, 0x00, 0x06, 0x63, 
0xff, 0x87, 0xf7, 0xf1, 0x81, 0xb1, 0xf9, 0xfe, 0xff, 0x7c, 0x06, 0x03, 0xe0, 0x61, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x3f, 0xb0, 0xde, 0x07, 0x80, 0xc3, 0x78, 0x60, 0x00, 0x19, 0x98, 
0x30, 0x18, 0x30, 0x78, 0xc1, 0xf0, 0x30, 0xf1, 0x99, 0xb0, 0x70, 0x03, 0x80, 0xcd, 0xd9, 0xe3, 
0x36, 0x06, 0x1b, 0x03, 0x06, 0x03, 0x19, 0x83, 0x3c, 0x30, 0x79, 0xef, 0x36, 0x1b, 0x36, 0x1b, 
0x33, 0x01, 0x86, 0x33, 0x33, 0x76, 0x78, 0x78, 0x18, 0xcc, 0x33, 0x60, 0x00, 0x78, 0xfc, 0x3c, 
0xf8, 0xf7, 0x8f, 0x9f, 0x18, 0xcd, 0xdb, 0xfe, 0x7c, 0x1c, 0x7e, 0x3e, 0x79, 0xff, 0x66, 0xc7, 
0x99, 0xf7, 0xc7, 0xfb, 0x36, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0xc0, 0xc3, 0x33, 0xc6, 0xf8, 0x1e, 0x1b, 0xdb, 0x00, 0x0e, 0xe0, 0xe1, 
0x80, 0x00, 0x0c, 0xcf, 0xc0, 0x06, 0xf6, 0x36, 0x0d, 0x81, 0xb0, 0x18, 0xf0, 0xf0, 0xf0, 0xd8, 
0x6c, 0x1e, 0x1e, 0x0c, 0x06, 0x0c, 0x18, 0x30, 0x6c, 0xcc, 0xc3, 0x6d, 0xb0, 0xd8, 0x6c, 0x36, 
0x1b, 0x0f, 0x9d, 0x9e, 0xc6, 0x63, 0x31, 0x98, 0xc7, 0x8c, 0xdb, 0x1e, 0x3c, 0x79, 0xf1, 0xe3, 
0xc7, 0xbc, 0x79, 0xe3, 0xc7, 0x8f, 0x36, 0x66, 0x3c, 0xf8, 0x38, 0x38, 0x38, 0x78, 0x38, 0x30, 
0x7e, 0xcc, 0xcc, 0xcc, 0xcd, 0x8d, 0xf9, 0x98, 

0x00, 0x00, 0x18, 0x0c, 0x06, 0x19, 0x9f, 0x87, 0x1c, 0x04, 0x04, 0x18, 0x3f, 0x00, 0x06, 0x63, 
0xff, 0x87, 0xe3, 0xf1, 0x81, 0xb0, 0xf8, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x07, 0xf3, 0xf8, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x1b, 0x1c, 0xdd, 0xcf, 0x30, 0xc3, 0x78, 0x60, 0x00, 0x31, 0x98, 
0x30, 0x30, 0xe0, 0xd9, 0xf1, 0xd8, 0x60, 0xf1, 0x99, 0xb1, 0xc7, 0xf8, 0xe1, 0x9b, 0x7b, 0x33, 
0xe6, 0x06, 0x1b, 0xe3, 0xe6, 0x7b, 0xf9, 0x83, 0x3c, 0x30, 0x6f, 0x6d, 0xb6, 0x1b, 0x76, 0x1b, 
0xe1, 0xc1, 0x86, 0x33, 0x63, 0x76, 0x30, 0x30, 0x30, 0xcc, 0x33, 0x60, 0x00, 0x0c, 0xe6, 0xe1, 
0x99, 0x9b, 0x19, 0x9d, 0x98, 0xcf, 0x1b, 0xb3, 0x76, 0x76, 0x73, 0x66, 0x73, 0x0c, 0x66, 0x6c, 
0xff, 0x36, 0x6c, 0x33, 0x36, 0x3b, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0xc1, 0xf3, 0x37, 0xe6, 0xcc, 0x1e, 0x1e, 0xde, 0xff, 0x03, 0x80, 0x0f, 
0xf0, 0x00, 0x0c, 0xcf, 0xc0, 0x06, 0xde, 0x3c, 0xcd, 0xf0, 0xf6, 0x19, 0x99, 0x99, 0x98, 0xd8, 
0x6c, 0x36, 0x37, 0xcc, 0x07, 0xcf, 0x9f, 0x3e, 0x6c, 0xcd, 0xf3, 0x6d, 0xb0, 0xd8, 0x6c, 0x36, 
0x1b, 0x0c, 0xf1, 0xb6, 0xc6, 0x63, 0x31, 0x98, 0xc3, 0x0c, 0xdb, 0x03, 0x06, 0x0c, 0x18, 0x30, 
0x60, 0xe6, 0xc3, 0x36, 0x6c, 0xd9, 0xb6, 0x66, 0x66, 0xec, 0xec, 0xec, 0xec, 0xcc, 0xec, 0x00, 
0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xd9, 0xcd, 0xb0, 

0x00, 0x00, 0x18, 0x0f, 0x86, 0x1f, 0x16, 0x86, 0x0c, 0x04, 0x3f, 0x8f, 0x0c, 0x00, 0x06, 0x60, 
0x38, 0x07, 0xc1, 0xf1, 0x81, 0xb0, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x61, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x1b, 0x06, 0x7f, 0x79, 0xb0, 0xc3, 0x03, 0xfc, 0x78, 0x31, 0x98, 
0x30, 0x30, 0x39, 0x98, 0x19, 0x98, 0x61, 0x98, 0xf8, 0x07, 0x00, 0x00, 0x39, 0x9e, 0x7b, 0xf3, 
0x36, 0x06, 0x1b, 0x03, 0x06, 0x1b, 0x19, 0x83, 0x36, 0x30, 0x6f, 0x6c, 0xf6, 0x1b, 0xc6, 0x1b, 
0x60, 0x61, 0x86, 0x31, 0xe1, 0xfc, 0x78, 0x30, 0x30, 0xc6, 0x36, 0x30, 0x00, 0x3c, 0xc6, 0xc1, 
0x99, 0xfb, 0x19, 0x99, 0x98, 0xce, 0x1b, 0x33, 0x66, 0x63, 0x63, 0x66, 0x61, 0x8c, 0x66, 0x6c, 
0xff, 0x1c, 0x6c, 0x66, 0x33, 0x6e, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xc0, 0xc3, 0x31, 0x80, 0x6c, 0x1e, 0x1b, 0xf6, 0x03, 0xf0, 0x00, 0x01, 
0x80, 0x00, 0x0c, 0xc7, 0xd8, 0x03, 0x9b, 0x0d, 0xcf, 0x1f, 0xee, 0x01, 0xf9, 0xf9, 0xf8, 0xf8, 
0x7c, 0x3e, 0x3e, 0x0c, 0x06, 0x0c, 0x18, 0x30, 0x6c, 0xcc, 0xc3, 0x67, 0xb0, 0xd8, 0x6c, 0x36, 
0x1b, 0x0c, 0x61, 0xe6, 0xc6, 0x63, 0x31, 0x98, 0xc3, 0x0c, 0xd9, 0x8f, 0x1e, 0x3c, 0x78, 0xf1, 
0xe3, 0xfe, 0xc3, 0xf7, 0xef, 0xdf, 0xb6, 0x66, 0x66, 0xcc, 0xc6, 0xc6, 0xc6, 0xc6, 0xc7, 0xfe, 
0xde, 0xcc, 0xcc, 0xcc, 0xcc, 0xd9, 0x8c, 0xf0, 

0x00, 0x00, 0x18, 0x0c, 0x06, 0x1e, 0x16, 0x84, 0x04, 0x04, 0x1f, 0x01, 0x8c, 0x00, 0x06, 0xe0, 
0x38, 0x07, 0x80, 0xf1, 0x80, 0x00, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0xc0, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x83, 0x0f, 0x78, 0xe0, 0xc3, 0x00, 0x60, 0x00, 0x61, 0x98, 
0x30, 0x60, 0x19, 0xfc, 0x19, 0x98, 0x61, 0x98, 0x18, 0x03, 0x87, 0xf8, 0x70, 0x1e, 0xfb, 0x33, 
0x36, 0x06, 0x33, 0x03, 0x06, 0x1b, 0x19, 0x83, 0x36, 0x30, 0x6f, 0x6c, 0xf6, 0x1b, 0x06, 0x1b, 
0x30, 0x61, 0x86, 0x31, 0xe1, 0xfc, 0x78, 0x30, 0x60, 0xc6, 0x30, 0x00, 0x00, 0x6c, 0xc6, 0xc1, 
0x99, 0x83, 0x19, 0x99, 0x98, 0xcf, 0x1b, 0x33, 0x66, 0x63, 0x63, 0x66, 0x60, 0xec, 0x66, 0x6c, 
0xff, 0x1c, 0x3c, 0x63, 0x36, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0xc0, 0xc3, 0xf7, 0xe0, 0x38, 0x0f, 0xb0, 0x36, 0x03, 0x00, 0x00, 0x01, 
0x80, 0x00, 0x0c, 0xc3, 0xd8, 0x00, 0x1b, 0x1b, 0xc3, 0x18, 0x7e, 0x19, 0x99, 0x99, 0x99, 0x8c, 
0xc6, 0x36, 0x66, 0x0c, 0x06, 0x0c, 0x18, 0x30, 0x6c, 0xcc, 0xc3, 0x67, 0xb0, 0xd8, 0x6c, 0x36, 
0x1b, 0x0c, 0xf1, 0xe6, 0xc6, 0x63, 0x31, 0x98, 0xc3, 0x0f, 0x98, 0xdb, 0x36, 0x6c, 0xd9, 0xb3, 
0x66, 0xc0, 0xc3, 0x06, 0x0c, 0x18, 0x36, 0x66, 0x66, 0xcc, 0xc6, 0xc6, 0xc6, 0xc6, 0xc6, 0x00, 
0xf6, 0xcc, 0xcc, 0xcc, 0xcc, 0x79, 0x8c, 0xf0, 

0x00, 0x00, 0x18, 0x8c, 0x06, 0x1b, 0x16, 0x80, 0x00, 0x04, 0x0e, 0x00, 0x8c, 0x00, 0x0e, 0xe0, 
0x38, 0x07, 0x00, 0x77, 0xe0, 0x00, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x1e, 0x03, 0x1b, 0x78, 0xe0, 0xc3, 0x00, 0x60, 0x03, 0x60, 0xf0, 
0x30, 0xc0, 0x18, 0x18, 0x18, 0xd8, 0xc1, 0x98, 0x31, 0x80, 0xe0, 0x01, 0xc1, 0x9b, 0xb3, 0x33, 
0x33, 0x06, 0x73, 0x03, 0x03, 0x1b, 0x19, 0x83, 0x33, 0x30, 0x6e, 0x6c, 0x73, 0x33, 0x03, 0x33, 
0x36, 0x61, 0x83, 0x61, 0xe1, 0xdc, 0xcc, 0x30, 0x60, 0xc6, 0x30, 0x00, 0x00, 0x6c, 0xe6, 0xe1, 
0x99, 0xc3, 0x19, 0x99, 0x98, 0xcd, 0x9b, 0x33, 0x66, 0x67, 0x73, 0x66, 0x60, 0x6c, 0x6e, 0x38, 
0xff, 0x36, 0x38, 0xc3, 0x36, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x79, 0x80, 0x01, 0x86, 0x0c, 0x06, 0x60, 0x1b, 0x03, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0xc3, 0xc0, 0x00, 0x36, 0x1f, 0xe6, 0x30, 0xdf, 0x19, 0x99, 0x99, 0x99, 0x8c, 
0xc6, 0x63, 0x63, 0x06, 0x06, 0x0c, 0x18, 0x30, 0x6c, 0xcc, 0xce, 0x63, 0x99, 0x8c, 0xc6, 0x63, 
0x31, 0x99, 0x98, 0xcc, 0x6c, 0x36, 0x1b, 0x0d, 0x83, 0x0c, 0x18, 0xdb, 0x36, 0x6c, 0xd9, 0xb3, 
0x66, 0xe0, 0xc3, 0x87, 0x0e, 0x1c, 0x36, 0x66, 0x66, 0xcc, 0xce, 0xce, 0xce, 0xce, 0xce, 0x30, 
0x6e, 0xdc, 0xdc, 0xdc, 0xdc, 0x71, 0xcc, 0xe0, 

0x00, 0x00, 0x0f, 0x8c, 0x06, 0x19, 0x96, 0x80, 0x00, 0x04, 0x04, 0x19, 0x8c, 0x00, 0x0e, 0x00, 
0x38, 0x06, 0x00, 0x33, 0xc1, 0xb0, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x1e, 0x3e, 0x31, 0xcf, 0xb0, 0xc3, 0x00, 0x61, 0x83, 0x60, 0x60, 
0x31, 0xf9, 0xf0, 0x19, 0xf0, 0x71, 0xc0, 0xf0, 0xe1, 0xb0, 0x30, 0x03, 0x01, 0x8c, 0x06, 0x1b, 
0xe1, 0xf7, 0xc3, 0xf3, 0x01, 0xf3, 0x19, 0x9e, 0x31, 0xbf, 0x66, 0x6c, 0x31, 0xe3, 0x01, 0xe3, 
0x37, 0xc1, 0x81, 0xc0, 0xc1, 0x8d, 0xce, 0x30, 0xfe, 0xc3, 0x30, 0x00, 0x00, 0x3c, 0xfc, 0x3c, 
0xf8, 0x7b, 0x0f, 0x99, 0x98, 0xcc, 0xdb, 0x33, 0x66, 0x3c, 0x7e, 0x3e, 0x63, 0xc7, 0x3e, 0x30, 
0x66, 0x77, 0x19, 0xfb, 0x33, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x31, 0xf8, 0x01, 0x86, 0x0c, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x0f, 
0xf0, 0x00, 0x0f, 0xe3, 0xc0, 0x00, 0x00, 0x30, 0xcc, 0xf8, 0xc6, 0x33, 0x0f, 0x0f, 0x0f, 0x07, 
0x87, 0x63, 0xc3, 0xe3, 0xf7, 0xef, 0xdf, 0xbf, 0x6c, 0xcc, 0xf8, 0x61, 0x8f, 0x07, 0x83, 0xc1, 
0xe0, 0xf3, 0x0d, 0xf8, 0x38, 0x1c, 0x0e, 0x07, 0x03, 0x0c, 0x1f, 0x8f, 0x1e, 0x3c, 0x78, 0xf1, 
0xe3, 0xbe, 0x78, 0xf1, 0xe3, 0xc7, 0xb6, 0x66, 0x3c, 0xcc, 0x78, 0x78, 0x78, 0x78, 0x78, 0x00, 
0xf8, 0x7c, 0x7c, 0x7c, 0x7c, 0x31, 0xf8, 0x60, 

0x00, 0x00, 0x07, 0x0c, 0x06, 0x19, 0x90, 0x80, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x81, 0xb0, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x66, 0x00, 0x03, 0x00, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x0e, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x03, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x06, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x03, 0xc3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x60, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x03, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x70, 0x0f, 0xf0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x19, 0x80, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xe0, 0x01, 0xb6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xc1, 0x80, 0xc0, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0x81, 0x80, 
};
#else
ROMDATA PEGUSHORT MyriadPro10bEng_offset_table[257] = {
0x0000,0x0009,0x0011,0x0019,0x0021,0x0029,0x0031,0x0039,0x0041,0x0049,0x0051,0x0059,0x0061,0x006a,0x0073,0x007c,
0x008c,0x0094,0x009c,0x00a5,0x00ae,0x00b7,0x00c0,0x00c9,0x00d2,0x00db,0x00e4,0x00ed,0x00f6,0x00ff,0x0108,0x0111,
0x011a,0x0122,0x0127,0x012d,0x0136,0x013e,0x014c,0x0157,0x015b,0x0161,0x0167,0x016f,0x017a,0x017e,0x0184,0x0188,
0x0190,0x0198,0x01a0,0x01a8,0x01b0,0x01b8,0x01c0,0x01c8,0x01d0,0x01d8,0x01e0,0x01e5,0x01ea,0x01f5,0x0200,0x020b,
0x0212,0x021c,0x0226,0x022e,0x0236,0x023f,0x0247,0x024d,0x0255,0x025e,0x0263,0x0269,0x0271,0x0278,0x0283,0x028b,
0x0294,0x029c,0x02a5,0x02ae,0x02b6,0x02bd,0x02c5,0x02cf,0x02d9,0x02e3,0x02ed,0x02f4,0x02fa,0x0309,0x030f,0x031a,
0x0322,0x0329,0x0331,0x0339,0x0340,0x0348,0x0350,0x0355,0x035d,0x0365,0x0368,0x036c,0x0373,0x0376,0x0381,0x0389,
0x0391,0x0399,0x03a1,0x03a7,0x03ae,0x03b4,0x03bc,0x03c5,0x03d0,0x03d8,0x03e1,0x03e8,0x03ef,0x03f6,0x03fd,0x0408,
0x0411,0x041e,0x042b,0x0438,0x0445,0x0452,0x045f,0x046c,0x0479,0x0486,0x0493,0x04a0,0x04ad,0x04ba,0x04c7,0x04d4,
0x04e1,0x04ee,0x04fb,0x0508,0x0515,0x0522,0x052f,0x053c,0x0549,0x0556,0x0563,0x0570,0x057d,0x058a,0x0597,0x05a4,
0x05b1,0x05b5,0x05ba,0x05c2,0x05ca,0x05d2,0x05da,0x05e1,0x05e9,0x05f0,0x05fc,0x0603,0x060c,0x0617,0x061d,0x0629,
0x0631,0x0638,0x0643,0x064a,0x0651,0x0658,0x0660,0x0668,0x066d,0x0674,0x067b,0x0682,0x068b,0x069a,0x06a9,0x06b8,
0x06bf,0x06c9,0x06d3,0x06dd,0x06e7,0x06f1,0x06fb,0x0708,0x0710,0x0717,0x071e,0x0725,0x072c,0x0731,0x0736,0x073b,
0x0741,0x074b,0x0753,0x075c,0x0765,0x076e,0x0777,0x0780,0x078b,0x0794,0x079c,0x07a4,0x07ac,0x07b4,0x07be,0x07c7,
0x07cf,0x07d7,0x07df,0x07e7,0x07ef,0x07f7,0x07ff,0x080b,0x0812,0x081a,0x0822,0x082a,0x0832,0x0836,0x0839,0x083d,
0x0843,0x084b,0x0853,0x085b,0x0863,0x086b,0x0873,0x087b,0x0886,0x088e,0x0896,0x089e,0x08a6,0x08ae,0x08b7,0x08bf,
0x08c8,
};

ROMDATA PEGUBYTE MyriadPro10bEng_data_table[4496] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xfe, 
0xff, 0x7f, 0xbf, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xfe, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x30, 0x00, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x03, 0x03, 0x80, 0xc8, 0x6c, 0x0e, 0x00, 
0x00, 0x00, 0x60, 0x30, 0xe3, 0x6c, 0x19, 0xd9, 0x80, 0x06, 0x46, 0x00, 0xc0, 0xe0, 0xc8, 0xcc, 
0x00, 0x00, 0x03, 0x01, 0x83, 0x86, 0xc0, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x0c, 0x28, 0x28, 0x28, 0x28, 0x28, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x06, 0x00, 0x33, 0xc1, 0xb0, 0xf8, 0x30, 0x00, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x7f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x06, 0x06, 0xc1, 0x30, 0x6c, 0x11, 0x00, 
0x00, 0x00, 0x30, 0x61, 0xb3, 0x66, 0x33, 0x79, 0x80, 0x09, 0x83, 0x01, 0x81, 0xb1, 0x30, 0xcc, 
0x00, 0x00, 0x01, 0x83, 0x06, 0xc6, 0xc0, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x12, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x04, 0x04, 0x10, 0x1e, 0x00, 0x00, 0xe0, 
0x3c, 0x07, 0x00, 0x77, 0xe1, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x0f, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xb0, 0x00, 0x40, 0x00, 0x00, 0x00, 0xc3, 0x30, 0x30, 0x00, 0x00, 
0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x80, 0x00, 0x00, 0x07, 0x8c, 
0x63, 0x3c, 0x00, 0x00, 0x0c, 0x00, 0x60, 0x00, 0x06, 0x00, 0x38, 0x06, 0x06, 0x6c, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x63, 0x80, 
0x00, 0x63, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 0x00, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x36, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x60, 0x18, 0x70, 0x64, 0xd8, 0x48, 
0x00, 0x00, 0x18, 0x03, 0x0e, 0x1b, 0x31, 0xb6, 0x66, 0x86, 0x4c, 0x01, 0x87, 0x06, 0x4d, 0x80, 
0x00, 0x01, 0x80, 0x30, 0xe1, 0xb0, 0x19, 0x80, 0x6c, 

0x00, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 0x0e, 0x04, 0x0f, 0x33, 0x00, 0x07, 0xe0, 
0x3c, 0x07, 0x80, 0xf1, 0x81, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x1f, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x19, 0xb0, 0x91, 0xf1, 0xe0, 0x03, 0xc0, 0xc6, 0x18, 0xb4, 0x00, 0x00, 
0x00, 0x04, 0x7c, 0x18, 0x7c, 0x7c, 0x0c, 0x7e, 0x3c, 0xfe, 0x7c, 0x7c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0f, 0x0f, 0x81, 0xc3, 0xf1, 0xf3, 0xf0, 0xfd, 0xfb, 0xe6, 0x1b, 0xcf, 0x63, 0x60, 0xe1, 
0xdc, 0x4f, 0xcf, 0xc7, 0xe7, 0xe1, 0xf3, 0xf6, 0x33, 0x0c, 0xdd, 0xb0, 0xcc, 0x37, 0xe6, 0x0c, 
0x63, 0x0c, 0x18, 0x00, 0x06, 0x00, 0x60, 0x00, 0x06, 0x00, 0x60, 0x06, 0x06, 0x6c, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x60, 0xc0, 
0x00, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x03, 0x02, 0x07, 0x00, 0x19, 0x8c, 0x3e, 0x36, 0x1f, 0x07, 
0x80, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x1c, 0x00, 0x07, 0x0e, 0x18, 0x00, 0x7e, 0x00, 0x01, 0x8f, 
0x00, 0x06, 0x08, 0x0c, 0x10, 0x1c, 0x10, 0x30, 0x38, 0x0e, 0x03, 0x80, 0xe0, 0x38, 0x0e, 0x03, 
0xfe, 0x7c, 0xfd, 0xfb, 0xf7, 0xef, 0x7b, 0xcf, 0x3f, 0x1c, 0x4f, 0xc7, 0xe3, 0xf1, 0xf8, 0xfc, 
0x00, 0x0f, 0xcc, 0x6c, 0x6c, 0x6c, 0x66, 0x1b, 0x01, 0x98, 0x30, 0x30, 0xd8, 0x98, 0xd8, 0x48, 
0x00, 0x00, 0x0c, 0x06, 0x1b, 0x1b, 0x1b, 0x4e, 0x63, 0x09, 0x86, 0x03, 0x0d, 0x89, 0x8d, 0x80, 
0x00, 0x00, 0xc0, 0x61, 0xb1, 0xb0, 0x31, 0x80, 0x6c, 

0x00, 0x0c, 0x07, 0x0f, 0x9f, 0x8f, 0x00, 0x06, 0x0c, 0x1f, 0x04, 0x19, 0xb3, 0x00, 0x06, 0xe0, 
0x3c, 0x07, 0xc1, 0xf1, 0x81, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x19, 0xb0, 0x93, 0x4b, 0x31, 0x06, 0x60, 0xc6, 0x18, 0x78, 0x08, 0x00, 
0x00, 0x08, 0xc6, 0x78, 0x86, 0x86, 0x1c, 0x60, 0x60, 0x06, 0xc6, 0xc6, 0x00, 0x00, 0x60, 0x00, 
0x60, 0x11, 0x90, 0x41, 0xc3, 0x1b, 0x0b, 0x18, 0xc1, 0x86, 0x16, 0x19, 0x83, 0x66, 0x60, 0xe1, 
0xdc, 0x58, 0x6c, 0x6c, 0x36, 0x33, 0x08, 0xc6, 0x33, 0x0c, 0xdd, 0xb0, 0xcc, 0x30, 0x66, 0x0c, 
0xf3, 0x0c, 0x24, 0x00, 0x00, 0x00, 0x60, 0x00, 0x06, 0x00, 0x60, 0x06, 0x00, 0x0c, 0x18, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x60, 0xc0, 
0x00, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x03, 0x02, 0x0c, 0xa0, 0x99, 0x8c, 0x61, 0x00, 0x20, 0x80, 
0xc0, 0x00, 0x00, 0x01, 0x04, 0x00, 0x22, 0x04, 0x09, 0x93, 0x00, 0x00, 0xfa, 0x00, 0x03, 0x99, 
0x80, 0x0e, 0x10, 0x1c, 0x20, 0x26, 0x20, 0x30, 0x38, 0x0e, 0x03, 0x80, 0xe0, 0x38, 0x0e, 0x06, 
0x60, 0xc2, 0xc1, 0x83, 0x06, 0x06, 0x31, 0x86, 0x31, 0x9c, 0x58, 0x6c, 0x36, 0x1b, 0x0d, 0x86, 
0x00, 0x18, 0xec, 0x6c, 0x6c, 0x6c, 0x66, 0x1b, 0xf9, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x80, 0x08, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 

0x00, 0x00, 0x0f, 0x8c, 0x1f, 0x9f, 0x99, 0x87, 0x1c, 0x3f, 0x84, 0x10, 0x1e, 0x00, 0x07, 0xe1, 
0xff, 0x87, 0xe3, 0xf1, 0x81, 0xb1, 0xf8, 0x30, 0x00, 0x0c, 0x06, 0x03, 0x00, 0xc0, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x19, 0xb3, 0xfb, 0x43, 0x32, 0x06, 0x60, 0xcc, 0x0c, 0xb4, 0x08, 0x00, 
0x00, 0x08, 0xc6, 0x18, 0x86, 0x06, 0x2c, 0x60, 0xc0, 0x0c, 0xc6, 0xc6, 0x63, 0x01, 0x83, 0xfc, 
0x18, 0x01, 0xa7, 0xa3, 0x63, 0x1b, 0x0b, 0x0c, 0xc1, 0x86, 0x16, 0x19, 0x83, 0x6c, 0x60, 0xb2, 
0xd6, 0x58, 0x6c, 0x6c, 0x36, 0x33, 0x08, 0xc6, 0x33, 0x0c, 0xdd, 0x99, 0x86, 0x60, 0x66, 0x1f, 
0xff, 0x8c, 0x42, 0x00, 0x00, 0x1e, 0x6e, 0x3c, 0x7e, 0x7c, 0xf3, 0xf6, 0xe6, 0xec, 0xdb, 0xee, 
0x6e, 0x3e, 0x6e, 0x3f, 0x6c, 0xf3, 0xec, 0x66, 0x36, 0x66, 0x66, 0x63, 0x7e, 0x30, 0x60, 0xc0, 
0x00, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x00, 0x0f, 0x0c, 0x1f, 0x0f, 0x0c, 0x60, 0x00, 0x40, 0x47, 
0xc2, 0x20, 0x00, 0x02, 0x02, 0x00, 0x22, 0x04, 0x01, 0x86, 0x00, 0xc6, 0xfa, 0x60, 0x01, 0x99, 
0x91, 0x06, 0x20, 0x0c, 0x40, 0x0c, 0x40, 0x00, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 0x6c, 0x1b, 0x06, 
0x60, 0xc2, 0xc1, 0x83, 0x06, 0x06, 0x31, 0x86, 0x30, 0xd6, 0x58, 0x6c, 0x36, 0x1b, 0x0d, 0x86, 
0x11, 0x19, 0x6c, 0x6c, 0x6c, 0x6c, 0x63, 0x33, 0x0d, 0x98, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 
0x77, 0x8f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1b, 0x31, 0x80, 0xdb, 0x8f, 0x8f, 0x8f, 0x8f, 0x8f, 0x81, 
0x81, 0xf3, 0x1b, 0x1b, 0x1b, 0x19, 0x8d, 0xb8, 0xc6, 

0x00, 0x00, 0x18, 0x8c, 0x06, 0x19, 0x99, 0xbf, 0xbf, 0x84, 0x04, 0x18, 0x0c, 0x00, 0x06, 0x61, 
0xff, 0x87, 0xf7, 0xf1, 0x81, 0xb1, 0xf9, 0xfe, 0xff, 0x7c, 0x06, 0x03, 0xe0, 0x61, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x03, 0xfb, 0x43, 0x34, 0x06, 0x60, 0x0c, 0x0c, 0x30, 0x08, 0x00, 
0x00, 0x10, 0xc6, 0x18, 0x06, 0x06, 0x4c, 0x7c, 0xfc, 0x0c, 0xc6, 0xc6, 0x63, 0x06, 0x00, 0x00, 
0x06, 0x03, 0x2d, 0xa3, 0x63, 0x1b, 0x03, 0x0c, 0xc1, 0x86, 0x06, 0x19, 0x83, 0x78, 0x60, 0xb2, 
0xd6, 0x58, 0x6c, 0x6c, 0x36, 0x33, 0x00, 0xc6, 0x31, 0x98, 0xfd, 0x8f, 0x06, 0x60, 0xc6, 0x06, 
0xf6, 0x0c, 0x81, 0x00, 0x00, 0x23, 0x73, 0x62, 0xc6, 0xc6, 0x66, 0x37, 0x36, 0x6d, 0x9b, 0x33, 
0x73, 0x63, 0x73, 0x63, 0x7d, 0x89, 0x8c, 0x66, 0x36, 0x66, 0x66, 0x63, 0x06, 0x30, 0x60, 0xc3, 
0xc2, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x03, 0x1a, 0x8c, 0x11, 0x0f, 0x0c, 0x3e, 0x00, 0x8f, 0x2c, 
0xc6, 0x60, 0x00, 0x04, 0xf1, 0x00, 0x22, 0x04, 0x03, 0x03, 0x00, 0xc6, 0xfa, 0x60, 0x01, 0x99, 
0x99, 0x86, 0x23, 0x0c, 0x4e, 0x06, 0x4c, 0x30, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 0x6c, 0x1b, 0x06, 
0x60, 0xc0, 0xc1, 0x83, 0x06, 0x06, 0x31, 0x86, 0x30, 0xd6, 0x58, 0x6c, 0x36, 0x1b, 0x0d, 0x86, 
0x0a, 0x19, 0x6c, 0x6c, 0x6c, 0x6c, 0x63, 0x33, 0x0d, 0xb8, 0x8c, 0x8c, 0x8c, 0x8c, 0x8c, 0x8c, 
0x98, 0xd8, 0xb1, 0xb1, 0xb1, 0xb1, 0x9b, 0x31, 0x8f, 0xdc, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc0, 
0x03, 0x3b, 0x1b, 0x1b, 0x1b, 0x19, 0x8d, 0xcc, 0xc6, 

0x00, 0x00, 0x18, 0x0f, 0x86, 0x19, 0x9f, 0x87, 0x1c, 0x04, 0x04, 0x0f, 0x3f, 0x00, 0x06, 0x61, 
0xff, 0x87, 0xe3, 0xf1, 0x81, 0xb0, 0xf8, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x07, 0xf3, 0xf8, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x01, 0x21, 0xf1, 0xeb, 0xc3, 0xcc, 0x0c, 0x0c, 0x00, 0x7f, 0x03, 
0xe0, 0x10, 0xc6, 0x18, 0x0c, 0x3c, 0x8c, 0x06, 0xc6, 0x18, 0x7c, 0xc6, 0x00, 0x18, 0x00, 0x00, 
0x01, 0x86, 0x2d, 0xa6, 0x33, 0xf3, 0x03, 0x0c, 0xf9, 0xf6, 0x77, 0xf9, 0x83, 0x70, 0x60, 0x9c, 
0xd3, 0x58, 0x6c, 0x6c, 0x37, 0xe1, 0xf0, 0xc6, 0x31, 0x98, 0x7d, 0x86, 0x03, 0xc1, 0x86, 0x07, 
0x9e, 0x0c, 0x00, 0x00, 0x00, 0x03, 0x63, 0x60, 0xc6, 0xc6, 0x66, 0x36, 0x36, 0x6f, 0x1b, 0x33, 
0x63, 0x63, 0x63, 0x63, 0x61, 0xc1, 0x8c, 0x66, 0x33, 0x6c, 0x3c, 0x63, 0x0c, 0xe0, 0x60, 0x77, 
0xe2, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x03, 0x1a, 0x1f, 0x11, 0x06, 0x00, 0x63, 0x00, 0x98, 0x2c, 
0xcc, 0xc7, 0xf9, 0xf4, 0xd9, 0x00, 0x1c, 0x3f, 0x86, 0x13, 0x00, 0xc6, 0xfa, 0x00, 0x01, 0x99, 
0x8c, 0xc6, 0x47, 0x0c, 0x93, 0x26, 0x9c, 0x30, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x31, 0x8c, 
0x7e, 0xc0, 0xf9, 0xf3, 0xe7, 0xc6, 0x31, 0x86, 0x7c, 0xd3, 0x58, 0x6c, 0x36, 0x1b, 0x0d, 0x86, 
0x04, 0x1a, 0x6c, 0x6c, 0x6c, 0x6c, 0x61, 0xe3, 0x0d, 0x8c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 
0x18, 0xd8, 0x31, 0xb1, 0xb1, 0xb1, 0x9b, 0x31, 0x98, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xcf, 
0xf3, 0x3b, 0x1b, 0x1b, 0x1b, 0x19, 0x8d, 0x8c, 0xc6, 

0x00, 0x00, 0x18, 0x0c, 0x06, 0x1f, 0x16, 0x86, 0x0c, 0x04, 0x3f, 0x81, 0x8c, 0x00, 0x06, 0x61, 
0xff, 0x87, 0xc1, 0xf1, 0x81, 0xb0, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x61, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x07, 0xf0, 0x58, 0x16, 0x66, 0x6c, 0x0c, 0x0c, 0x00, 0x08, 0x00, 
0x00, 0x20, 0xc6, 0x18, 0x18, 0x06, 0xfe, 0x06, 0xc6, 0x18, 0xc6, 0x7e, 0x00, 0x18, 0x03, 0xfc, 
0x01, 0x86, 0x2d, 0xa7, 0xf3, 0x1b, 0x03, 0x0c, 0xc1, 0x86, 0x36, 0x19, 0x83, 0x78, 0x60, 0x9c, 
0xd3, 0x58, 0x6f, 0xcc, 0x36, 0xc0, 0x18, 0xc6, 0x30, 0xf0, 0x7f, 0x8f, 0x01, 0x83, 0x06, 0x07, 
0x9e, 0x0c, 0x00, 0x00, 0x00, 0x3f, 0x63, 0x60, 0xc6, 0xfe, 0x66, 0x36, 0x36, 0x6e, 0x1b, 0x33, 
0x63, 0x63, 0x63, 0x63, 0x60, 0xf1, 0x8c, 0x63, 0x63, 0x6c, 0x18, 0x36, 0x18, 0x30, 0x60, 0xc4, 
0x7e, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x03, 0x1a, 0x0c, 0x11, 0x1f, 0x80, 0x63, 0x00, 0x98, 0x27, 
0xcc, 0xc0, 0x08, 0x04, 0xf1, 0x00, 0x00, 0x04, 0x0f, 0x8e, 0x00, 0xc6, 0x7a, 0x00, 0x03, 0xcf, 
0x0c, 0xc6, 0x8b, 0x0d, 0x03, 0x1d, 0x2c, 0x60, 0xfe, 0x3f, 0x8f, 0xe3, 0xf8, 0xfe, 0x3f, 0x8f, 
0xe0, 0xc0, 0xc1, 0x83, 0x06, 0x06, 0x31, 0x86, 0x30, 0xd3, 0x58, 0x6c, 0x36, 0x1b, 0x0d, 0x86, 
0x0a, 0x1a, 0x6c, 0x6c, 0x6c, 0x6c, 0x60, 0xc3, 0x0d, 0x8c, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 
0xff, 0xd8, 0x3f, 0xbf, 0xbf, 0xbf, 0x9b, 0x31, 0x98, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc0, 
0x03, 0x5b, 0x1b, 0x1b, 0x1b, 0x18, 0xd9, 0x8c, 0x6c, 

0x00, 0x00, 0x18, 0x0c, 0x06, 0x1e, 0x16, 0x84, 0x04, 0x04, 0x1f, 0x00, 0x8c, 0x00, 0x06, 0xe0, 
0x3c, 0x07, 0x80, 0xf1, 0x80, 0x00, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0xc0, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xf0, 0x58, 0x26, 0x66, 0x38, 0x0c, 0x0c, 0x00, 0x08, 0x00, 
0x00, 0x20, 0xc6, 0x18, 0x30, 0x06, 0x0c, 0x06, 0xc6, 0x30, 0xc6, 0x06, 0x00, 0x06, 0x00, 0x00, 
0x06, 0x00, 0x2d, 0xa6, 0x33, 0x1b, 0x0b, 0x0c, 0xc1, 0x86, 0x36, 0x19, 0x83, 0x6c, 0x60, 0x88, 
0xd1, 0xd8, 0x6c, 0x0c, 0x36, 0x62, 0x18, 0xc6, 0x30, 0xf0, 0x77, 0x99, 0x81, 0x86, 0x06, 0x03, 
0x9c, 0x0c, 0x00, 0x00, 0x00, 0x63, 0x63, 0x60, 0xc6, 0xc0, 0x66, 0x36, 0x36, 0x6f, 0x1b, 0x33, 
0x63, 0x63, 0x63, 0x63, 0x60, 0x39, 0x8c, 0x63, 0x63, 0x9c, 0x3c, 0x36, 0x30, 0x30, 0x60, 0xc4, 
0x3c, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x03, 0x1a, 0x0c, 0x1f, 0x06, 0x0c, 0x63, 0x00, 0x98, 0x20, 
0x06, 0x60, 0x08, 0x04, 0xd9, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0xc6, 0x0a, 0x00, 0x00, 0x00, 
0x19, 0x80, 0x93, 0x01, 0x06, 0x01, 0x4c, 0xc0, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x31, 0x8c, 
0x60, 0xc2, 0xc1, 0x83, 0x06, 0x06, 0x31, 0x86, 0x30, 0xd1, 0xd8, 0x6c, 0x36, 0x1b, 0x0d, 0x86, 
0x11, 0x1a, 0x6c, 0x6c, 0x6c, 0x6c, 0x60, 0xc3, 0xf9, 0x8d, 0x8d, 0x8d, 0x8d, 0x8d, 0x8d, 0x8d, 
0x98, 0x18, 0x30, 0x30, 0x30, 0x30, 0x1b, 0x31, 0x98, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc1, 
0x83, 0x9b, 0x1b, 0x1b, 0x1b, 0x18, 0xd9, 0x8c, 0x6c, 

0x00, 0x00, 0x18, 0x8c, 0x06, 0x1b, 0x16, 0x80, 0x00, 0x04, 0x0e, 0x19, 0x8c, 0x00, 0x0e, 0xe0, 
0x3c, 0x07, 0x00, 0x77, 0xe0, 0x00, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x02, 0x42, 0x58, 0x46, 0x66, 0x30, 0x0c, 0x0c, 0x00, 0x08, 0x18, 
0x06, 0x40, 0xc6, 0x18, 0x60, 0x86, 0x0c, 0x86, 0xc6, 0x30, 0xc6, 0x0c, 0x63, 0x01, 0x80, 0x00, 
0x18, 0x06, 0x26, 0xcc, 0x1b, 0x1b, 0x0b, 0x18, 0xc1, 0x86, 0x36, 0x19, 0x83, 0x66, 0x60, 0x88, 
0xd1, 0xd8, 0x6c, 0x0c, 0x36, 0x32, 0x18, 0xc6, 0x30, 0x60, 0x63, 0x30, 0xc1, 0x86, 0x06, 0x03, 
0x0c, 0x0c, 0x00, 0x00, 0x00, 0x63, 0x63, 0x62, 0xce, 0xc2, 0x66, 0x76, 0x36, 0x6d, 0x9b, 0x33, 
0x63, 0x63, 0x63, 0x67, 0x61, 0x19, 0x8c, 0xe1, 0xc1, 0x98, 0x66, 0x1c, 0x60, 0x30, 0x60, 0xc0, 
0x00, 0x63, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 0x00, 0x80, 0x44, 0x02, 0x20, 
0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 
0x04, 0x40, 0x22, 0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 
0x01, 0x10, 0x08, 0x80, 0x44, 0x02, 0x03, 0x1a, 0x98, 0x20, 0x86, 0x0c, 0x3e, 0x00, 0x8f, 0x20, 
0x02, 0x20, 0x08, 0x04, 0xcd, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0xce, 0x0a, 0x00, 0x00, 0x00, 
0x11, 0x01, 0x1f, 0x82, 0x0c, 0x02, 0x7e, 0xc5, 0x83, 0x60, 0xd8, 0x36, 0x0d, 0x83, 0x60, 0xd8, 
0x60, 0xc2, 0xc1, 0x83, 0x06, 0x06, 0x31, 0x86, 0x31, 0x91, 0xd8, 0x6c, 0x36, 0x1b, 0x0d, 0x86, 
0x00, 0x1c, 0x6c, 0x6c, 0x6c, 0x6c, 0x60, 0xc3, 0x01, 0x8d, 0x8d, 0x8d, 0x8d, 0x8d, 0x8d, 0x8d, 
0x98, 0x58, 0xb0, 0xb0, 0xb0, 0xb0, 0x9b, 0x31, 0x98, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc1, 
0x83, 0x9b, 0x3b, 0x3b, 0x3b, 0x38, 0x71, 0x8c, 0x38, 

0x00, 0x00, 0x0f, 0x8c, 0x06, 0x19, 0x96, 0x80, 0x00, 0x04, 0x04, 0x0f, 0x0c, 0x00, 0x0e, 0x00, 
0x3c, 0x06, 0x00, 0x33, 0xc1, 0xb0, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x02, 0x41, 0xf0, 0x03, 0xc3, 0xdc, 0x0c, 0x0c, 0x00, 0x00, 0x18, 
0x06, 0x40, 0x7c, 0x7e, 0xfe, 0x7c, 0x0c, 0x7c, 0x7c, 0x30, 0x7c, 0x78, 0x63, 0x00, 0x60, 0x00, 
0x60, 0x06, 0x10, 0x0c, 0x1b, 0xf1, 0xf3, 0xf0, 0xfd, 0x83, 0xf6, 0x1b, 0xde, 0x63, 0x7e, 0x80, 
0xd0, 0xcf, 0xcc, 0x07, 0xe6, 0x19, 0xf0, 0xc3, 0xe0, 0x60, 0x63, 0x30, 0xc1, 0x87, 0xe6, 0x03, 
0x0c, 0x0c, 0x00, 0x00, 0x00, 0x3f, 0x7e, 0x3c, 0x76, 0x7c, 0x63, 0xb6, 0x36, 0x6c, 0xdb, 0x33, 
0x63, 0x3e, 0x7e, 0x3b, 0x60, 0xf0, 0xe7, 0x61, 0xc1, 0x98, 0x66, 0x1c, 0x7e, 0x30, 0x60, 0xc0, 
0x00, 0x7f, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 0x00, 0xff, 0xc7, 0xfe, 0x3f, 
0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 
0xfc, 0x7f, 0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 
0xff, 0x1f, 0xf8, 0xff, 0xc7, 0xfe, 0x03, 0x0f, 0x1f, 0x80, 0x06, 0x0c, 0x03, 0x00, 0x40, 0x40, 
0x00, 0x00, 0x08, 0x02, 0x02, 0x00, 0x00, 0x3f, 0x80, 0x00, 0x00, 0xf6, 0x0a, 0x00, 0x00, 0x00, 
0x00, 0x02, 0x03, 0x04, 0x1f, 0x04, 0x0c, 0x79, 0x83, 0x60, 0xd8, 0x36, 0x0d, 0x83, 0x60, 0xd8, 
0x7e, 0x7c, 0xfd, 0xfb, 0xf7, 0xef, 0x7b, 0xcf, 0x3f, 0x10, 0xcf, 0xc7, 0xe3, 0xf1, 0xf8, 0xfc, 
0x00, 0x0f, 0xc7, 0xc7, 0xc7, 0xc7, 0xc0, 0xc3, 0x01, 0xb8, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 0xfc, 
0xe7, 0x8f, 0x1f, 0x1f, 0x1f, 0x1f, 0x1b, 0x31, 0x8f, 0x98, 0xcf, 0x8f, 0x8f, 0x8f, 0x8f, 0x80, 
0x01, 0xf1, 0xd9, 0xd9, 0xd9, 0xd8, 0x71, 0xf8, 0x38, 

0x00, 0x00, 0x07, 0x0c, 0x06, 0x19, 0x96, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x81, 0xb0, 0x78, 0x00, 0x18, 0x0c, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x06, 0x18, 0x00, 0x00, 0x30, 
0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 
0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x30, 0x60, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x0c, 0x43, 0x00, 0x20, 0x80, 
0x00, 0x00, 0x00, 0x01, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x0a, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x80, 0x18, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x06, 0x18, 0x00, 0x00, 0x30, 
0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 
0x00, 0x3c, 0x00, 0x3f, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x30, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x1c, 0x63, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x0c, 0x3e, 0x00, 0x1f, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x0a, 0x00, 0x40, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x30, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x01, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x30, 
};
#endif


#if defined(_FONT_PLASTIMO_)
xFONTYY MyriadPro10bEng_Font = {0x01, 13, 3, 16, 0, 0, 16, 216, 0x0000, 0x00ff,
(PEGUSHORT *) MyriadPro10bEng_offset_table,&MyriadPro10bExt_Font,
(PEGUBYTE *) MyriadPro10bEng_data_table};
#else
xFONTYY MyriadPro10bEng_Font = {0x01, 13, 3, 16, 0, 0, 16, 281, 0x0000, 0x00ff,
(PEGUSHORT *) MyriadPro10bEng_offset_table,&MyriadPro10bExt_Font,
(PEGUBYTE *) MyriadPro10bEng_data_table};
#endif


/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

