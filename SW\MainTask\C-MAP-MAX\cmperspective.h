#ifndef _CMPERSPECTIVE_H_03032006
#define _CMPERSPECTIVE_H_03032006

#ifdef __cplusplus
extern "C"
{
#endif

PRE_EXPORT_H void	IN_EXPORT_H cmSetPerspectiveDistance(SLong Distance);
PRE_EXPORT_H Float	IN_EXPORT_H cmGetPerspectiveDistanceFactor(void);
PRE_EXPORT_H void	IN_EXPORT_H cmSetPerspectiveDistanceFactor(Float Factor);
PRE_EXPORT_H SWord	IN_EXPORT_H cmGetPerspectiveAngle(void);
PRE_EXPORT_H SLong	IN_EXPORT_H cmGetPerspectiveDistance(void);
PRE_EXPORT_H void	IN_EXPORT_H cmSetPerspectiveAngle(SWord angle);
PRE_EXPORT_H Pixel	IN_EXPORT_H cmGetPerspectiveSkyHeight(void);
PRE_EXPORT_H Bool	IN_EXPORT_H cmIsPerspectiveEnabled(void);
PRE_EXPORT_H Bool	IN_EXPORT_H  cmGetPerspectiveStatus(void);
PRE_EXPORT_H Bool	IN_EXPORT_H cmSetPerspectiveView(Bool Enable);
PRE_EXPORT_H void	IN_EXPORT_H cmForcePerspectiveClipRegion(void);
PRE_EXPORT_H void	IN_EXPORT_H cmRemovePerspectiveClipRegion(void);
PRE_EXPORT_H void	IN_EXPORT_H cmSetPerspDrawSkyCallBack(void (*CallBackFun)(void));

#ifdef __cplusplus
}
#endif

#endif /*_CMPERSPECTIVE_H_03032006*/
