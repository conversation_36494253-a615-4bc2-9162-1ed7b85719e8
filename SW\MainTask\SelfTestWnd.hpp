#ifndef __SELF_TEST_WND_HPP__
#define __SELF_TEST_WND_HPP__

#include "Wnd.hpp"

class CSelfTestWnd : public CWnd {
public:
	enum {
		ST_NONE = 0,
		ST_START,
		ST_CANCEL,
		ST_DONE,
		MAX_ST	
	};

	enum {
		ST_RES_NONE,
		ST_RES_CANCEL,
		ST_RES_SUCCESS,
		ST_RES_FAIL,
		ST_RES_TESTING,
		MAX_ST_RES
	};

	enum{
		ST_ITEM_TRANS_IN_VOL = 0,
		ST_ITEM_TRANS_ROM,
		ST_ITEM_TRANS_RAM,
		ST_ITEM_TRANS_GPS,
		ST_ITEM_TRANS_EEPROM,
		ST_ITEM_TRANS_COM_TO_SUB,
		ST_ITEM_TRANS_LOOPBACK_CH1,
		ST_ITEM_TRANS_LOOPBACK_CH2,
		ST_ITEM_MKD_ROM,
		ST_ITEM_MKD_RAM,
		ST_ITEM_MKD_COM1,
		ST_ITEM_MKD_COM3,
		MAX_ST_ITEM
	};

private:
	int m_nTestCnt;
	int m_nCurTestItem;
	
	int m_nPrevTstStat;
	int m_nCurTstStat;
	int m_nPrevTstItemResStat[MAX_ST_ITEM];
	int m_nCurTstItemResStat[MAX_ST_ITEM];

private:
	void InitVar();
	void InitCtrls();
	
public:
	CSelfTestWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
	~CSelfTestWnd();

	void ProcesssSTStart();
	void ProcessSTCancel();
	void ProcessSTDone();
	
	virtual void OnKeyEvent(int nKey, DWORD nFlags);
	virtual void OnCursorEvent(int nState);
	virtual void OnActivate();
	virtual void OnTimer();

	void InitTestStat();
	void InitTstItemResStat();
	
	void DrawFuncBtn();
	void DrawPCBVersionTBL();
	void DrawPCBVersion();
	void DrawProgramVersionTBL();
	void DrawProgramVersion();
	void DrawVersion();
	void DrawSelfTestStatBackground();
	void DrawSelfTestStat();
	void DrawWnd(BOOL bRedraw = TRUE);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
	void SystemReset();


	BOOL SetCurSelfTestStat(int nStat);
	int  GetPrevSelfTestStat();
	int  GetCurSelfTestStat();

	int  GetPrevSelfTestItemResStat(int nItem);
	int  GetCurSelfTestItemResStat(int nItem);
	BOOL GetCurSelfTestItemResString(int nItem, BYTE *pStr);
	BOOL SetCurSelfTestItemResStat(int nItem, int nResStatus);
};

#endif	// End of __SELF_TEST_WND_HPP__



