/*...........................................................................*/
/*.                  File Name : SYSUART.H                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"

#ifndef  __SYSUART_H__
#define  __SYSUART_H__

//=============================================================================
#define  UART_PARITY_NONE                              0
#define  UART_PARITY_ODD                               4
#define  UART_PARITY_EVEN                              5
//-----------------------------------------------------------------------------
#define  UART_DATA_BIT_5                               0
#define  UART_DATA_BIT_6                               1
#define  UART_DATA_BIT_7                               2
#define  UART_DATA_BIT_8                               3
//-----------------------------------------------------------------------------
#define  UART_STOP_BIT_1                               0
#define  UART_STOP_BIT_2                               1
//-----------------------------------------------------------------------------
#define  UART_TX_INT_DISABLE                           0
#define  UART_TX_INT_ENABLE                            1
//-----------------------------------------------------------------------------
#define  UART_RX_INT_DISABLE                           0
#define  UART_RX_INT_ENABLE                            1
//-----------------------------------------------------------------------------
#define  UART_TX_FIFO_TRG_LEVEL_0                      0
#define  UART_TX_FIFO_TRG_LEVEL_4                      1
#define  UART_TX_FIFO_TRG_LEVEL_8                      2
#define  UART_TX_FIFO_TRG_LEVEL_12                     3
//-----------------------------------------------------------------------------
#define  UART_RX_FIFO_TRG_LEVEL_1                      0
#define  UART_RX_FIFO_TRG_LEVEL_4                      1
#define  UART_RX_FIFO_TRG_LEVEL_8                      2
#define  UART_RX_FIFO_TRG_LEVEL_12                     3
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

void  SysSetUART(xSYS_UART *pSysUART,int nSpeed,int nParity,int nDataBit,int nStopBit,int nTxInt,int nRxInt);
void  SysSetUartBaudRate(xSYS_UART *pSysUART,int nSpeed);
void  SysClearUartIntPendingAll(xSYS_UART *pSysUART);
void  SysSetUartIntEnableAllMode(xSYS_UART *pSysUART,int nIntAllMode);
void  SysSetUartTxIntEnable(xSYS_UART *pSysUART);
void  SysSetUartTxIntDisable(xSYS_UART *pSysUART);
void  SysSetUartRxIntEnable(xSYS_UART *pSysUART);
void  SysSetUartRxIntDisable(xSYS_UART *pSysUART);
void  SysSetUartRxTimeOutIntEnable(xSYS_UART *pSysUART);
void  SysSetUartRxTimeOutIntDisable(xSYS_UART *pSysUART);
void  SysSetUartErrorIntEnable(xSYS_UART *pSysUART);
void  SysSetUartErrorIntDisable(xSYS_UART *pSysUART);
void	SysResetUartTxFIFO(xSYS_UART *pSysUART);
void	SysResetUartRxFIFO(xSYS_UART *pSysUART);
void  SysSetUartTxFifoTrgLevel(xSYS_UART *pSysUART,int nTrgLevel);
void  SysSetUartRxFifoTrgLevel(xSYS_UART *pSysUART,int nTrgLevel);
DWORD SysGetUartErrorStatus(xSYS_UART *pSysUART);
void  SysSetUartSyncPendClear(xSYS_UART *pSysUART);

#ifdef  __cplusplus
}
#endif

#endif

