/*...........................................................................*/
/*.                  File Name : DATATYPE.H                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __DATATYPE_H__
#define  __DATATYPE_H__

//-----------------------------------------------------------------------------
#define  __PACK__  __attribute__ ((packed))
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
typedef             char          CHAR;
typedef    signed   char         SCHAR;
typedef  unsigned   char          BYTE;
typedef  unsigned   char         UCHAR;
typedef  short      int          SHORT;
typedef  unsigned   short         WORD;
typedef  unsigned   short        HWORD;
typedef  unsigned   int          DWORD;
typedef  unsigned   int           UINT;
typedef             long          LONG;
typedef  unsigned   long         ULONG;
typedef  short      int          INT16;
typedef             int          INT32;
typedef  float                  REAL32;
typedef  double                 REAL64;
typedef  double                   REAL;
//-----------------------------------------------------------------------------
typedef  DWORD                    TIME;
typedef  DWORD                  TIME_T;
typedef  DWORD                    TICK;
typedef  DWORD                 SECTIME;
typedef  unsigned   char        BACK08;
typedef  short      int         BACK16;
typedef             int         BACK32;
typedef  unsigned   short       BACKWW;
typedef  unsigned   int         BACKDD;
//-----------------------------------------------------------------------------
typedef  long long  int          INT64;
typedef  unsigned long long int  QWORD;
//-----------------------------------------------------------------------------
#define    COLORT_SIZEOF_SIZE        2
#define    CLRMENU_SIZEOF_SIZE       2
#define    CLRCHART_SIZEOF_SIZE      2
#define    CLRRADAR_SIZEOF_SIZE      2

typedef    HWORD                COLORT;
typedef    HWORD               CLRMENU;
typedef    HWORD              CLRCHART;
#if CLRRADAR_SIZEOF_SIZE == 2
    typedef    HWORD          CLRRADAR;
#else
    typedef    DWORD          CLRRADAR;
#endif
//-----------------------------------------------------------------------------
typedef  union HangulCode {
         HWORD wCodeX;
         struct {
                 HWORD wLast : 5;
                 HWORD wMid  : 5;
                 HWORD wFirst: 5;
                 HWORD wFlag : 1;
                } CodeY;
         struct {
                 UCHAR bLow;
                 UCHAR bHigh;
                } CodeZ;
        }HANCODE;
//-----------------------------------------------------------------------------
typedef  union {
         DWORD dOutCode;
         struct
              {
               DWORD dCode0 : 1;
               DWORD dCode1 : 1;
               DWORD dCode2 : 1;
               DWORD dCode3 : 1;
              }OCS;
        }CLIP;
//-----------------------------------------------------------------------------
typedef  struct {
         int    nYear;          // 0 -- 9999
         int    nMonth;         // 1 -- 12
         int    nDay;           // 1 -- 31
        }xSYSDATE;
//-----------------------------------------------------------------------------
typedef  struct {
         int    nHour;          // 0 -- 23
         int    nMinute;        // 0 -- 59
         int    nSecond;        // 0 -- 59
        }xSYSTIME;
//-----------------------------------------------------------------------------
typedef  struct {
         xSYSDATE xDate;
         xSYSTIME xTime;
        }xDTIME;
//-----------------------------------------------------------------------------
typedef  struct {
         INT32  nX;
         INT32  nY;
        }COORX;
//-----------------------------------------------------------------------------
typedef  struct {
         REAL   rLat;
         REAL   rLon;
        }FPOS;
//-----------------------------------------------------------------------------
typedef  struct {
         INT32  nLat;
         INT32  nLon;
        }LPOS;
//-----------------------------------------------------------------------------
typedef  struct {
         REAL   rLat;
         REAL   rLon;
         INT32  nLat;
         INT32  nLon;
         INT32  nMcY;
         INT32  nMcX;
        }XPOS;
//-----------------------------------------------------------------------------
typedef  struct {
         INT32  nWidth;
         INT32  nHeight;
         INT32  nLastX;
         INT32  nLastY;
         REAL   rLastX;          // RDXReal
         REAL   rLastY;          // RDYReal
         INT32  nOrigX;
         INT32  nOrigY;
         REAL   rOrigX;
         REAL   rOrigY;
         INT32  nClipX0;         // Screen clipping left (0)
         INT32  nClipX1;         // Screen clipping left (m_nScrLastX,GpsRightX)
         INT32  nClipY0;         // Screen clipping up   (0)
         INT32  nClipY1;         // Screen clipping up   (m_nScrLastY)
        }XSCRN;
//-----------------------------------------------------------------------------
typedef  struct {
         INT32  nStartX;
         INT32  nStartY;
         INT32  nLastX;
         INT32  nLastY;
         INT32  nDX;
         INT32  nDY;
        }POLY;
//-----------------------------------------------------------------------------
typedef  struct _xFont {
         UCHAR  uType;            // bit-flags defined below
         UCHAR  uAscent;          // Ascent above baseline
         UCHAR  uDescent;         // Descent below baseline
         UCHAR  uHeight;          // total height of character
         HWORD  wBytesPerLine;    // total bytes (width) of one scanline
         HWORD  wFirstChar;       // first character present in font (page)
         HWORD  wLastChar;        // last character present in font (page)
         HWORD  *pOffsets;        // bit-offsets for variable-width font
         struct _xFont *pNext;    // NULL unless multi-page Unicode font
         UCHAR  *pData;           // character bitmap data array
        }xFONT;
//-----------------------------------------------------------------------------

#endif
