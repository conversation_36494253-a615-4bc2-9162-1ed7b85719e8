/*...........................................................................*/
/*.                  File Name : CPUADDR.H                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef __CPUADDR_H__
#define __CPUADDR_H__

//=============================================================================
#if defined(__POLLUX__)
    #define CPU_MCUA_PHSY_BASE_ADDR             0x00000000
    #define CPU_MCUB_PHSY_BASE_ADDR             0x40000000
    #define CPU_MCUS_PHSY_BASE_ADDR             0x80000000
        #define MCUS_STATIC0_BASE_ADDR          0x80000000
        #define MCUS_STATIC1_BASE_ADDR          0x84000000
        #define MCUS_STATIC2_BASE_ADDR          0x88000000
        #define MCUS_STATIC3_BASE_ADDR          0x8c000000
    #define CPU_IORW_PHSY_BASE_ADDR             0xC0000000
//-----------------------------------------------------------------------------
    #define CPU_MCUA_VIRT_BASE_ADDR             0x00000000
    #define CPU_MCUB_VIRT_BASE_ADDR             0x40000000
    #define CPU_MCUS_VIRT_BASE_ADDR             0x80000000
    #define CPU_IORW_VIRT_BASE_ADDR             0xC0000000
#else                           // SPICA
    #define CPU_MCUA_PHSY_BASE_ADDR             0x80000000
    #define CPU_MCUS_PHSY_BASE_ADDR             0x00000000
        #define MCUS_STATIC0_BASE_ADDR          0x00000000
        #define MCUS_STATIC1_BASE_ADDR          0x04000000
        #define MCUS_STATIC2_BASE_ADDR          0x08000000
        #define MCUS_STATIC3_BASE_ADDR          0x0c000000
    #define CPU_IORW_PHSY_BASE_ADDR             0xC0000000
//-----------------------------------------------------------------------------
    #define CPU_MCUA_VIRT_BASE_ADDR             0x80000000
    #define CPU_MCUS_VIRT_BASE_ADDR             0x00000000
    #define CPU_IORW_VIRT_BASE_ADDR             0xC0000000
#endif
//=============================================================================
#if defined(__POLLUX__)
    #define INTC_PHSY_BASE_ADDR                 0xC0000800

    #define MCUS_PHSY_BASE_ADDR                 0xC0015800
    #define MCUD_PHSY_BASE_ADDR                 0xC0014800

    #define TIMER0_PHSY_BASE_ADDR               0xC0001800
    #define TIMER1_PHSY_BASE_ADDR               0xC0001880
    #define TIMER2_PHSY_BASE_ADDR               0xC0001900
    #define TIMER3_PHSY_BASE_ADDR               0xC0001980
    #define TIMER4_PHSY_BASE_ADDR               0xC0001a00

    #define CLKPWR_PHSY_BASE_ADDR               0xC000F000
        #define CLKPWR_CLKMODEREG                   0x0000
        #define CLKPWR_PLLSETREG0                   0x0004
        #define CLKPWR_PLLSETREG1                   0x0008

    #define NAND_CTRL_PHSY_BASE_ADDR            0xC0015874
        #define NAND_NFCONTROL                      0x0000
        #define NAND_NFECCL                         0x0004
        #define NAND_NFECCH                         0x0008
        #define NAND_NFORGECCL                      0x000c
        #define NAND_NFORGECCH                      0x0010
        #define NAND_NFCNT                          0x0014
        #define NAND_NFECCSTATUS                    0x0018
        #define NAND_NFSYNDRONE31                   0x001c
        #define NAND_NFSYNDRONE75                   0x0020

    #define NAND_DATA_PHSY_BASE_ADDR            0xAC000000
        #define NAND_NFDATA                         0x0000
        #define NAND_NFCMD                          0x0010
        #define NAND_NFADDR                         0x0018

    #define GPIOA_PHSY_BASE_ADDR                0xC000A000
    #define GPIOB_PHSY_BASE_ADDR                0xC000A040
    #define GPIOC_PHSY_BASE_ADDR                0xC000A080

    #define ALIVE_PHSY_BASE_ADDR                0xC0019000

    #define I2C_PHSY_BASE_ADDR                  0xC000E000

    #define PWM_PHSY_BASE_ADDR                  0xC000C000

    #define RTC_PHSY_BASE_ADDR                  0xC000F080

    #define UART0_PHSY_BASE_ADDR                0xC0016000
    #define UART1_PHSY_BASE_ADDR                0xC0016080
    #define UART2_PHSY_BASE_ADDR                0xC0016800
    #define UART3_PHSY_BASE_ADDR                0xC0016880

    #define DMA_PHSY_BASE_ADDR                  0xC0000000

    #define AUDIO_PHSY_BASE_ADDR                0xC000D800

    #define SD0_PHSY_BASE_ADDR                  0xC0009800
    #define SD1_PHSY_BASE_ADDR                  0xC000C800

    #define UHC_PHSY_BASE_ADDR                  0xC000D000

    #define UDC_PHSY_BASE_ADDR                  0xC0018000

    #define SSPSPI_PHSY_BASE_ADDR               0xC0007800
    #define SPI0_PHSY_BASE_ADDR                 0xC0007800
    #define SPI1_PHSY_BASE_ADDR                 0xC0008000
    #define SPI2_PHSY_BASE_ADDR                 0xC0008800

    #define GRP3D_PHSY_BASE_ADDR                0xC001A000

    #define ADC_PHSY_BASE_ADDR                  0xC0005000

    #define MLCP_PHSY_BASE_ADDR                 0xC0004000
    #define MLCS_PHSY_BASE_ADDR                 0xC0004400

    #define DPCP_PHSY_BASE_ADDR                 0xC0003000
    #define DPCS_PHSY_BASE_ADDR                 0xC0003400
#else                           // SPICA
    #define INTC_PHSY_BASE_ADDR                 0xC0000800
        #define INTC_START_OFFSET                   0x0000

    #define MCUS_PHSY_BASE_ADDR                 0xC0015800
        #define MCUS_START_OFFSET                   0x0000

    #define MCUD_PHSY_BASE_ADDR                 0xC0014800
        #define MCUD_START_OFFSET                   0x0000

    #define TIMER0_PHSY_BASE_ADDR               0xC0001800
    #define TIMER1_PHSY_BASE_ADDR               0xC0001880
    #define TIMER2_PHSY_BASE_ADDR               0xC0001900
    #define TIMER3_PHSY_BASE_ADDR               0xC0001980
    #define TIMER4_PHSY_BASE_ADDR               0xC0001a00
        #define TIMER_START_OFFSET                  0x0000

    #define CLKPWR_PHSY_BASE_ADDR               0xC000F000
        #define CLKPWR_CLKMODEREG0                  0x0000
        #define CLKPWR_CLKMODEREG1                  0x0004
        #define CLKPWR_PLLSETREG0                   0x0008
        #define CLKPWR_PLLSETREG1                   0x000c

    #define NAND_CTRL_PHSY_BASE_ADDR            0xC001587c
        #define NAND_NFCONTROL                      0x0000
        #define NAND_NFECC0                         0x0004
        #define NAND_NFECC1                         0x0008
        #define NAND_NFECC2                         0x000c
        #define NAND_NFECC3                         0x0010
        #define NAND_NFECC4                         0x0014
        #define NAND_NFECC5                         0x0018
        #define NAND_NFECC6                         0x001c
        #define NAND_NFORGECC0                      0x0020
        #define NAND_NFORGECC1                      0x0024
        #define NAND_NFORGECC2                      0x0028
        #define NAND_NFORGECC3                      0x002c
        #define NAND_NFORGECC4                      0x0030
        #define NAND_NFORGECC5                      0x0034
        #define NAND_NFORGECC6                      0x0038
        #define NAND_NFCNT                          0x003c
        #define NAND_NFECCSTATUS                    0x0040
        #define NAND_NFSYNDRONE0                    0x0044
        #define NAND_NFSYNDRONE1                    0x0048
        #define NAND_NFSYNDRONE2                    0x004c
        #define NAND_NFSYNDRONE3                    0x0050
        #define NAND_NFSYNDRONE4                    0x0054
        #define NAND_NFSYNDRONE5                    0x0058
        #define NAND_NFSYNDRONE6                    0x005c
        #define NAND_NFSYNDRONE7                    0x0060

    #define NAND_DATA_PHSY_BASE_ADDR            0x2C000000
        #define NAND_NFDATA                         0x0000
        #define NAND_NFCMD                          0x0010
        #define NAND_NFADDR                         0x0018

    #define IDE_DATA_PHSY_BASE_ADDR             0x28000000

    #define GPIOA_PHSY_BASE_ADDR                0xC000A000
    #define GPIOB_PHSY_BASE_ADDR                0xC000A040
    #define GPIOC_PHSY_BASE_ADDR                0xC000A080
    #define GPIOD_PHSY_BASE_ADDR                0xC000A0C0
    #define GPIOE_PHSY_BASE_ADDR                0xC000A100

    #define ALIVE_PHSY_BASE_ADDR                0xC0019000

    #define I2C_PHSY_BASE_ADDR                  0xC000E000

    #define PWM_PHSY_BASE_ADDR                  0xC000C000

    #define RTC_PHSY_BASE_ADDR                  0xC000F080

    #define UART0_PHSY_BASE_ADDR                0xC0016000
    #define UART1_PHSY_BASE_ADDR                0xC0016080
    #define UART2_PHSY_BASE_ADDR                0xC0016800
    #define UART3_PHSY_BASE_ADDR                0xC0016880
    #define UART4_PHSY_BASE_ADDR                0xC0017000
    #define UART5_PHSY_BASE_ADDR                0xC0017080

    #define DMA_PHSY_BASE_ADDR                  0xC0000000

    #define AUDIO_PHSY_BASE_ADDR                0xC000D800

    #define SD0_PHSY_BASE_ADDR                  0xC0009800
    #define SD1_PHSY_BASE_ADDR                  0xC000C800

    #define UHC_PHSY_BASE_ADDR                  0xC000D000

    #define UDC_PHSY_BASE_ADDR                  0xC0018000

    #define SSPSPI_PHSY_BASE_ADDR               0xC0007800
    #define SPI0_PHSY_BASE_ADDR                 0xC0007800
    #define SPI1_PHSY_BASE_ADDR                 0xC0008000
    #define SPI2_PHSY_BASE_ADDR                 0xC0008800

    #define GRP3D_PHSY_BASE_ADDR                0xC001A000

    #define ADC_PHSY_BASE_ADDR                  0xC0005000

    #define MLCP_PHSY_BASE_ADDR                 0xC0004000
    #define MLCS_PHSY_BASE_ADDR                 0xC0004400

    #define DPCP_PHSY_BASE_ADDR                 0xC0003000
    #define DPCS_PHSY_BASE_ADDR                 0xC0003400

    #define CSC_PHSY_BASE_ADDR                  0xC0009000

    #define ECID_PHSY_BASE_ADDR                 0xC001F800

    #define PPM_PHSY_BASE_ADDR                  0xC000A800

    #define ROTATOR_PHSY_BASE_ADDR              0xC0004800

    #define SCALER_PHSY_BASE_ADDR               0xC0003800

    #define VIP_PHSY_BASE_ADDR                  0xC0002800
#endif
//=============================================================================

//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD	dNFCONTROL;                           // 0x5874
    DWORD	dNFECCL;                              // 0x5878
    DWORD	dNFECCH;                              // 0x587c
    DWORD	dNFORGECCL;                           // 0x5880
    DWORD	dNFORGECCH;                           // 0x5884
    DWORD	dNFCNT;                               // 0x5888
    DWORD	dNFECCSTATUS;                         // 0x588c
    DWORD	dNFSYNDRONE31;                        // 0x5890
    DWORD	dNFSYNDRONE75;                        // 0x5894
#else                           // SPICA
    DWORD dNFCONTROL;                           // 0x007C
    DWORD dNFECC[7];                            // 0x0080 ~ 0x0098
    DWORD dNFORGECC[7];                         // 0x009C ~ 0x00B4
    DWORD dNFCNT;                               // 0x00B8
    DWORD dNFECCSTATUS;                         // 0x00BC
    DWORD dNFSYNDROME[8];                       // 0x00C0 ~ 0x00DC
#endif
} xSYS_NAND_CTRL;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD	dNFDATA;                              // 0x0000
    DWORD	dDummyX[3];
    UCHAR	bNFCMD;                               // 0x0010
    UCHAR bDummyY[3];
    DWORD	dDummyZ;
    UCHAR	bNFADDR;                              // 0x0018
#else                           // SPICA
    DWORD	dNFDATA;                              // 0x0000
    DWORD	dDummyX[3];
    UCHAR	bNFCMD;                               // 0x0010
    UCHAR bDummyY[3];
    DWORD	dDummyZ;
    UCHAR	bNFADDR;                              // 0x0018
#endif
} xSYS_NAND_DATA;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
    DWORD dOUT;                                 // 0x0000 Output Register
    DWORD dOUTENB;                              // 0x0004 Output Enable Register
    DWORD dDETMODE[2];                          // 0x0008 Event Detect Mode Register 
    DWORD dINTENB;                              // 0x0010 Interrupt Enable Register
    DWORD dDET;                                 // 0x0014 Event Detect Register
    DWORD dPAD;                                 // 0x0018 PAD Status Register
    DWORD dPUENB;                               // 0x001C Pull Up Enable Register
    DWORD dALTFN[2];                            // 0x0020 Alternate Function Select Register
} xSYS_GPIO;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD dPWRGATE;                             // 0x0000 Alive Power Gating Register.
    DWORD dGPIORST;                             // 0x0004 Alive GPIO Reset Register.
    DWORD dGPIOSET;                             // 0x0008 Alive GPIO Set Register.
    DWORD dGPIOREAD;                            // 0x000C Alive GPIO Read Register.
    DWORD dSCRATCHRST;                          // 0x0010 Alive Scratch Reset Register.
    DWORD dSCRATCHSET;                          // 0x0014 Alive Scratch Set Register.
    DWORD dSCRATCHREAD;                         // 0x0018 Alive Scratch Read Register.
#else                           // SPICA
    DWORD dPWRGATE;                             // 0x0000 Alive Power Gating Register
    DWORD dASYNCDETECTMODERSTREG0;              // 0x0004 Alive GPIO ASync Detect Mode Reset Register0
    DWORD dASYNCDETECTMODESETREG0;              // 0x0008 Alive GPIO ASync Detect Mode Set Register0
    DWORD dLOWASYNCDETECTMODEREADREG;           // 0x000C Alive GPIO Low ASync Detect Mode Read Register

    DWORD dASYNCDETECTMODERSTREG1;              // 0x0010 Alive GPIO ASync Detect Mode Reset Register1
    DWORD dASYNCDETECTMODESETREG1;              // 0x0014 Alive GPIO ASync Detect Mode Set Register1
    DWORD dHIGHASYNCDETECTMODEREADREG;          // 0x0018 Alive GPIO High ASync Detect Mode Read Register

    DWORD dDETECTMODERSTREG0;                   // 0x001C Alive GPIO Detect Mode Reset Register0
    DWORD dDETECTMODESETREG0;                   // 0x0020 Alive GPIO Detect Mode Reset Register0
    DWORD dFALLDETECTMODEREADREG;               // 0x0024 Alive GPIO Falling Edge Detect Mode Read Register

    DWORD dDETECTMODERSTREG1;                   // 0x0028 Alive GPIO Detect Mode Reset Register1
    DWORD dDETECTMODESETREG1;                   // 0x002C Alive GPIO Detect Mode Reset Register1
    DWORD dRISEDETECTMODEREADREG;               // 0x0030 Alive GPIO Rising Edge Detect Mode Read Register

    DWORD dDETECTMODERSTREG2;                   // 0x0034 Alive GPIO Detect Mode Reset Register2
    DWORD dDETECTMODESETREG2;                   // 0x0038 Alive GPIO Detect Mode Reset Register2
    DWORD dLOWDETECTMODEREADREG;                // 0x003C Alive GPIO Low Level Detect Mode Read Register

    DWORD dDETECTMODERSTREG3;                   // 0x0040 Alive GPIO Detect Mode Reset Register3
    DWORD dDETECTMODESETREG3;                   // 0x0044 Alive GPIO Detect Mode Reset Register3
    DWORD dHIGHDETECTMODEREADREG;               // 0x0048 Alive GPIO High Level Detect Mode Read Register

    DWORD dDETECTENBRSTREG;                     // 0x004C Alive GPIO Detect Enable Reset Register
    DWORD dDETECTENBSETREG;                     // 0x0050 Alive GPIO Detect Enable Set Register
    DWORD dDETECTENBREADREG;                    // 0x0054 Alive GPIO Detect Enable Read Register

    DWORD dINTENBRSTREG;                        // 0x0058 Alive GPIO Interrupt Enable Reset Register
    DWORD dINTENBSETREG;                        // 0x005C Alive GPIO Interrupt Enable Set Register
    DWORD dINTENBREADREG;                       // 0x0060 Alive GPIO Interrupt Enable Read Register

    DWORD dDETECTPENDREG;                       // 0x0064 Alive GPIO Detect Pending Register

    DWORD dSCRATCHRSTREG;                       // 0x0068 Alive Scratch Reset Register
    DWORD dSCRATCHSETREG;                       // 0x006C Alive Scratch Set Register
    DWORD dSCRATCHREADREG;                      // 0x0070 Alive Scratch Read Register

    DWORD dPADOUTENBRSTREG;                     // 0x0074 Alive GPIO PAD Out Enable Reset Register
    DWORD dPADOUTENBSETREG;                     // 0x0078 Alive GPIO PAD Out Enable Set Register
    DWORD dPADOUTENBREADREG;                    // 0x007C Alive GPIO PAD Out Enable Read Register

    DWORD dPADPULLUPRSTREG;                     // 0x0080 Alive GPIO PAD Pullup Reset Register
    DWORD dPADPULLUPSETREG;                     // 0x0084 Alive GPIO PAD Pullup Set Register
    DWORD dPADPULLUPREADREG;                    // 0x0088 Alive GPIO PAD Pullup Read Register

    DWORD dPADOUTRSTREG;                        // 0x008C Alive GPIO PAD Out Reset Register
    DWORD dPADOUTSETREG;                        // 0x0090 Alive GPIO PAD Out Set Register
    DWORD dPADOUTREADREG;                       // 0x0094 Alive GPIO PAD Out Read Register

    DWORD dVDDCTRLRSTREG;                       // 0x0098 VDD Control Reset Register
    DWORD dVDDCTRLSETREG;                       // 0x009C VDD Control Set Register
    DWORD dVDDCTRLREADREG;                      // 0x00A0 VDD Control Read Register
#endif
} xSYS_ALIVE;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD dCLKMODEREG;                          // 0x0000 Clock Mode Register
    DWORD dPLLSETREG[2];                        // 0x0004 PLL Setting Register
    DWORD dReservedW[13];                       // 0x0010 ~ 0x03F
    DWORD dGPIOWAKEUPENB;                       // 0x0040 GPIO Wakeup Enable Register
    DWORD dRTCWAKEUPENB;                        // 0x0044 RTC Wakeup Enable Register
    DWORD dGPIOWAKEUPRISEENB;                   // 0x0048 GPIO Rising Edge Detect Enable Register
    DWORD dGPIOWAKEUPFALLENB;                   // 0x004C GPIO Falling Edge Detect Enable Register
    DWORD dGPIOPEND;                            // 0x0050 GPIO Wakeup Detect Pending Register
    DWORD dReservedX;                           // 0x0054
    DWORD dINTPENDSPAD;                         // 0x0058 Interrupt Pending & Scratch PAD Register
    DWORD dPWRRSTSTATUS;                        // 0x005C Power Reset Status Register
    DWORD dINTENB;                              // 0x0060 Interrupt Enable Register
    DWORD dReservedY[6];                        // 0x064 ~ 0x07B
    DWORD dPWRMODE;                             // 0x07C  Power Mode Control Register
    DWORD dReservedZ[20];                       // 0x80 ~ 0xFC
    DWORD dPADSTRENGTHGPIO[3][2];               // 0x100,0x104 GPIOA Pad Strength Register
                                                // 0x108,0x10C GPIOB Pad Strength Register
                                                // 0x110,0x114 GPIOC Pad Strength Register
    DWORD dPADSTRENGTHBUS;                      // 0x118       Bus Pad Strength Register
#else                           // SPICA
    DWORD dCLKMODEREG0;                         // 0x0000 Clock Mode Register 0
    DWORD dCLKMODEREG1;                         // 0x0004 Clock Mode Register 1
    DWORD dPLLSETREG[2];                        // 0x0008 ~ 0x000C PLL Setting Register
    UCHAR vReservedW[0x40 - 0x10];              // 0x0010 ~ 0x003C Reserved Region
    DWORD dGPIOWAKEUPRISEENB;                   // 0x0040 GPIO Rising Edge Detect Enable Register
    DWORD dGPIOWAKEUPFALLENB;                   // 0x0044 GPIO Falling Edge Detect Enable Register
    DWORD dGPIORSTENB;                          // 0x0048 GPIO Reset Enable Register
    DWORD dGPIOWAKEUPENB;                       // 0x004C GPIO Wakeup Source Enable
    DWORD dGPIOINTENB;                          // 0x0050 Interrupt Enable Register
    DWORD dGPIOINTPEND;                         // 0x0054 Interrupt Pend Register
    DWORD dRESETSTATUS;                         // 0x0058 Reset Status Register
    DWORD dINTENABLE;                           // 0x005C Interrupt Enable Register
    DWORD dINTPEND;                             // 0x0060 Interrupt Pend Register
    DWORD dPWRCONT;                             // 0x0064 Power Control Register
    DWORD dPWRMODE;                             // 0x0068 Power Mode Register
    DWORD dReservedX;                           // 0x006C Reserved Region
    DWORD dSCRATCH[3];                          // 0x0070 ~ 0x0078 Scratch Register
    DWORD dSYSRSTCONFIG;                        // 0x007C System Reset Configuration Register.
    UCHAR vReservedY[0x100 - 0x80];             // 0x0080 ~ 0x00FC Reserved Region
    DWORD dPADSTRENGTHGPIO[5][2];               // 0x0100 , 0x104 GPIOA Pad Strength Register
                                                // 0x0108 , 0x10C GPIOB Pad Strength Register
                                                // 0x0110 , 0x114 GPIOC Pad Strength Register
                                                // 0x0118 , 0x11C GPIOD Pad Strength Register
                                                // 0x0120 , 0x124 GPIOE Pad Strength Register
    DWORD dReservedZ[2];                        // 0x0128 ~ 0x12C Reserved Region
    DWORD dPADSTRENGTHBUS;                      // 0x0130 Bus Pad Strength Register
#endif
} xSYS_CLOCK;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    HWORD wMEMCFG;                              // 0x0000 [RW] Memory Configuration Register
    HWORD wMEMTIME0;                            // 0x0002 [RW] Memory Timing #0 Register
    HWORD wMEMTIME1;                            // 0x0004 [RW] Memory Timing #1 Register
    HWORD wMEMACTPWD;                           // 0x0006 [RW] Memory Active Power Down Control Register
    HWORD wMEMREFRESH;                          // 0x0008 [RW] Memory Refresh Control Register
    HWORD wMEMCONTROL;                          // 0x000A [RW] Memory Control Register
    HWORD wMEMCLKDELAY;                         // 0x000C [RW] Memory Clock Output Delay Register
    HWORD wMEMDQSOUTDELAY;                      // 0x000E [RW] Memory DQS Output Delay Register
    HWORD wMEMDQSINDELAY;                       // 0x0010 [RW] Memory DQS Input Delay Register
#else                           // SPICA
    DWORD dMEMCFG;                              // 0x0000 Memory Configuration Register
    DWORD dMEMTIME0;                            // 0x0004 Memory Timing #0 Register
    DWORD dMEMCTRL;                             // 0x0008 Memory Control Register
    DWORD dMEMACTPWD;                           // 0x000C Memory Active Power Down Control Register
    DWORD dMEMTIME1;                            // 0x0010 Memory Timing #1 Register
    DWORD dReservedX[3];                        // 0x0014 ~ 0x001C Reserved
    DWORD dFASTCH[3];                           // 0x0020 , 0x0024 , 0x0028 Memory Fast Arbiter Channel Registers
    DWORD dSLOWCH[3];                           // 0x002C , 0x0030 , 0x0034 Memory Slow Arbiter Channel Registers
    DWORD dINITPERFCNT;                         // 0x0038 Memory Performance Count Init Value Register
    DWORD dVALIDCNT[22];                        // 0x003C ~ 0x0090 Memory Counter Registers
    DWORD dPHYDELAYCTRL;                        // 0x0094 PHY Delay Control Register
    DWORD dPHYDLLCTRL0;                         // 0x0098 PHY DLL Control #0 Register
    DWORD dPHYMODE;                             // 0x009C PHY Mode Register
    DWORD dPHYDLLCTRL1;                         // 0x00A0 PHY DLL Control #1 Register
    DWORD dPHYDLLFORCE;                         // 0x00A4 PHY DLL Force Register
    DWORD dReservedY[1];                        // 0x00A8 Reserved
    DWORD dPHYZQCTRL;                           // 0x00AC PHY ZQ Control Register
    DWORD dPHYZQFORCE;                          // 0x00B0 PHY ZQ Force Register
    DWORD dPHYTERMCTRL;                         // 0x00B4 PHY Termination Control Register
    DWORD dPHYUPDATE;                           // 0x00B8 PHY Update Register
#endif
} xSYS_MCUD;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD dMEMBW;	                              // 0x0000 Memory Bus Width Register
    DWORD dMEMTACS;                             // 0x0004 Memory Timing for tACS Register
    DWORD dMEMTCOS;                             // 0x008h Memory Timing for tCOS Register
    DWORD dMEMTACCL;                            // 0x000C Memory Timing for tACC Register
    DWORD dMEMTACCH;                            // 0x0010 Memory Timing for tACC Register
    DWORD dMEMTSACCL;                           // 0x0014 Memory Timing for tSACC Register
    DWORD dMEMTSACCH;                           // 0x0018 Memory Timing for tSACC Register
    DWORD dReservedX[2];                        // 0x001C ~ 0x0020
    DWORD dMEMTCOH;                             // 0x0024 Memory Timing for tCOH Register
    DWORD dMEMTCAH;                             // 0x0028 Memory Timing for tCAH Register
    DWORD dMEMBURSTL;                           // 0x002C Memory Burst Control Register
    DWORD dMEMBURSTH;                           // 0x0030 Memory Burst Control Register
    DWORD dMEMWAIT;                             // 0x0034 Memory Wait Control Register
    DWORD dReservedY[15];                       // 0x0038 ~ 0x0070 Reserved for future use.
    DWORD dNFCONTROL;                           // 0x0074 Nand Flash Control Register
    DWORD dNFECCL;                              // 0x0078 Nand Flash ECC Low Register
    DWORD dNFECCH;                              // 0x007C Nand Flash ECC High Register
    DWORD dNFORGECCL;                           // 0x0080 Nand Flash Origin ECC Low Register
    DWORD dNFORGECCH;                           // 0x0084 Nand Flash Origin ECC High Register
    DWORD dNFCNT;                               // 0x0088 Nand Flash Data Count Register
    DWORD dNFECCSTATUS;                         // 0x008C Nand Flash ECC Status Register
    DWORD dNFSYNDRONE31;                        // 0x0090 Nand Flash ECC Syndrome Value31 Register
    DWORD dNFSYNDRONE75;                        // 0x0094 Nand Flash ECC Syndrome Value75 Register
#else                           // SPICA
    DWORD dMEMBW;                               // 0x0000 Memory Bus Width Register
    DWORD dMEMTACS[2];                          // 0x0004 , 0x0008 Memory Timing for tACS Register
    DWORD dMEMTCOS[2];                          // 0x000C , 0x0010 Memory Timing for tCOS Register
    DWORD dMEMTACC[3];                          // 0x0014 , 0x0018 , 0x001C Memory Timing for tACC Register
    DWORD dMEMTSACC[3];                         // 0x0020 , 0x0024 , 0x0028 Memory Timing for tSACC Register
    DWORD dMEMTWACC[3];                         // 0x002C , 0x0030 , 0x0034 Memory Timing for tWACC Register
    DWORD dMEMTCOH[2];                          // 0x0038 , 0x003C Memory Timing for tCOH Register
    DWORD dMEMTCAH[2];                          // 0x0040 , 0x0044 Memory Timing for tCAH Register
    DWORD dMEMBURST;                            // 0x0048 Memory Burst Control Register
    DWORD dReservedX[1];                        // 0x004C	Reserved for future use
    DWORD dMEMWAIT;                             // 0x0050 Memory Wait Control Register
    DWORD dIDEDMATIMEOUT;                       // 0x0054	DMA Time-out Register
    DWORD dIDEDMACTRL;                          // 0x0058	DMA Control Register
    DWORD dIDEDMAPOL;                           // 0x005C	DMA Polarity Register
    DWORD dIDEDMATIME0;                         // 0x0060	DMA Timing 0 Register
    DWORD dIDEDMATIME1;                         // 0x0064	DMA Timing 1 Register
    DWORD dIDEDMATIME2;                         // 0x0068	DMA Timing 2 Register
    DWORD dIDEDMATIME3;                         // 0x006C	DMA Timing 3 Register
    DWORD dIDEDMARST;                           // 0x0070	DMA Reset Register
    DWORD dIDEDMATIME4;                         // 0x0074	DMA Timing 4 Register
    DWORD dReservedY[1];                        // 0x0078 Reserved for future use.
    DWORD dNFCONTROL;                           // 0x007C Nand Flash Control Register
    DWORD dNFECC[7];                            // 0x0080 ~ 0x0098 Nand Flash ECC 0 ~ 6 Register
    DWORD dNFORGECC[7];                         // 0x009C ~ 0x00B4 Nand Flash Origin ECC 0 ~ 6 Register
    DWORD dNFCNT;                               // 0x00B8 Nand Flash Data Count Register
    DWORD dNFECCSTATUS;                         // 0x00BC Nand Flash ECC Status Register
    DWORD dNFSYNDROME[8];                       // 0x00C0 ~ 0x00DC Nand Flash ECC Syndrome Value 0 ~ 7 Register
#endif
} xSYS_MCUS;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
    DWORD dReservedX[2];                        // 0x0000 ~ 0x0007
    DWORD dINTMODE[2];                          // 0x0008 ~ 0x000C Interrupt Mode Register
    DWORD dINTMASK[2];                          // 0x0010 ~ 0x0014 Interrupt Mask Register
    DWORD dPRIORDER;                            // 0x0018 Priority Order Register
    DWORD dReservedY;                           // 0x001C
    DWORD dINTPEND[2];                          // 0x0020 ~ 0x0024 Interrupt Pending Register
    DWORD dReservedZ;                           // 0x0028
} xSYS_INTR;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
    DWORD dTMRCOUNT;                            // 0x0000 Timer counter register
    DWORD dTMRMATCH;                            // 0x0004 Timer match register
    DWORD dTMRCONTROL;                          // 0x0008 Timer control register
    DWORD dReservedX[13];                       // 0x000c ~ 0x003C
    DWORD dTMRCLKENB;                           // 0x0040
    DWORD dTMRCLKGEN;                           // 0x0044
} xSYS_TIMER;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    HWORD wLCON;                                // 0x0000 Line Control Register
    HWORD	wUCON;                                // 0x0002 Control Register
    HWORD	wFCON;                                // 0x0004 FIFO Control Register
    HWORD	wMCON;                                // 0x0006 Modem Control Register
    HWORD	wTRSTATUS;                            // 0x0008 Tx/Rx Status Register
    HWORD	wESTATUS;                             // 0x000a Error Status Register
    HWORD	wFSTATUS;                             // 0x000c FIFO Status Register
    HWORD	wMSTATUS;                             // 0x000e Modem Status Register
    HWORD	wTHB;                                 // 0x0010 Transmit Buffer Register
    HWORD	wRHB;                                 // 0x0012 Receive Buffer Register
    HWORD	wBRD;                                 // 0x0014 Baud Rate Divisor Register
    HWORD	wTIMEOUT;                             // 0x0016 Receive TimeOut Register
    HWORD	wINTCON;                              // 0x0018 Interrupt Control Register
    HWORD wReservedX[0x13];                     // 0x001A ~ 0x03E
    DWORD dCLKENB;                              // 0x0040 Clock Enable Register
    DWORD dCLKGEN;                              // 0x0044 Clock Generate Register
#else                           // SPICA
    HWORD wLCON;                                // 0x0000 Line Control Register
    HWORD	wUCON;                                // 0x0002 Control Register
    HWORD	wFCON;                                // 0x0004 FIFO Control Register
    HWORD	wMCON;                                // 0x0006 Modem Control Register
    HWORD	wTRSTATUS;                            // 0x0008 Tx/Rx Status Register
    HWORD	wESTATUS;                             // 0x000a Error Status Register
    HWORD	wFSTATUS;                             // 0x000c FIFO Status Register
    HWORD	wMSTATUS;                             // 0x000e Modem Status Register
    HWORD	wTHB;                                 // 0x0010 Transmit Buffer Register
    HWORD	wRHB;                                 // 0x0012 Receive Buffer Register
    HWORD	wBRD;                                 // 0x0014 Baud Rate Divisor Register
    HWORD	wTIMEOUT;                             // 0x0016 Receive TimeOut Register
    HWORD	wINTCON;                              // 0x0018 Interrupt Control Register
    HWORD wReservedX[0x13];                     // 0x001A ~ 0x03E
    DWORD dCLKENB;                              // 0x0040 Clock Enable Register
    DWORD dCLKGEN;                              // 0x0044 Clock Generate Register
#endif
} xSYS_UART;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD dLEFTRIGHT;                           // 0x000C MLC RGB Layer Left Right Register 0/1
    DWORD dTOPBOTTOM;                           // 0x0010 MLC RGB Layer Top Bottom Register 0/1
    DWORD dINVALIDLEFTRIGHT0;                   // 0x0014 MLC RGB Invalid Area0 Left, Right Register 0/1
    DWORD dINVALIDTOPBOTTOM0;                   // 0x0018 MLC RGB Invalid Area0 Top, Bottom Register 0/1
    DWORD dINVALIDLEFTRIGHT1;                   // 0x001C MLC RGB Invalid Area1 Left, Right Register 0/1
    DWORD dINVALIDTOPBOTTOM1;                   // 0x0020 MLC RGB Invalid Area1 Top, Bottom Register 0/1
    DWORD dCONTROL;                             // 0x0024 MLC RGB Layer Control Register 0/1
    DWORD dHSTRIDE;                             // 0x0028 MLC RGB Layer Horizontal Stride Register 0/1
    DWORD dVSTRIDE;                             // 0x002C MLC RGB Layer Vertical Stride Register 0/1
    DWORD dTPCOLOR;                             // 0x0030 MLC RGB Layer Transparency Color Register 0/1
    DWORD dINVCOLOR;                            // 0x0034 MLC RGB Layer Inversion Color Register 0/1
    DWORD dADDRESS;                             // 0x0038 MLC RGB Layer Base Address Register 0/1
    DWORD dPALETTE;                             // 0x003C MLC RGB Layer Palette Register 0/1
#else                           // SPICA
#endif
} xMLC_LAYER;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
#else                           // SPICA
    DWORD dLEFTRIGHT;                           // 0x000C MLC RGB Layer Left Right Register 0/1
    DWORD dTOPBOTTOM;                           // 0x0010 MLC RGB Layer Top Bottom Register 0/1
    DWORD dINVALIDLEFTRIGHT0;                   // 0x0014 MLC RGB Invalid Area0 Left, Right Register 0/1
    DWORD dINVALIDTOPBOTTOM0;                   // 0x0018 MLC RGB Invalid Area0 Top, Bottom Register 0/1
    DWORD dINVALIDLEFTRIGHT1;                   // 0x001C MLC RGB Invalid Area1 Left, Right Register 0/1
    DWORD dINVALIDTOPBOTTOM1;                   // 0x0020 MLC RGB Invalid Area1 Top, Bottom Register 0/1
    DWORD dCONTROL;                             // 0x0024 MLC RGB Layer Control Register 0/1
    DWORD dHSTRIDE;                             // 0x0028 MLC RGB Layer Horizontal Stride Register 0/1
    DWORD dVSTRIDE;                             // 0x002C MLC RGB Layer Vertical Stride Register 0/1
    DWORD dTPCOLOR;                             // 0x0030 MLC RGB Layer Transparency Color Register 0/1
    DWORD dINVCOLOR;                            // 0x0034 MLC RGB Layer Inversion Color Register 0/1
    DWORD dADDRESS;                             // 0x0038 MLC RGB Layer Base Address Register 0/1
    DWORD dReservedX;                           // 0x003C Reserved
#endif
} xRGB1_LAYER;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
#else                           // SPICA
    DWORD dLEFTRIGHT;                           // 0x000C MLC RGB Layer Left Right Register 0/1
    DWORD dTOPBOTTOM;                           // 0x0010 MLC RGB Layer Top Bottom Register 0/1
    DWORD dINVALIDLEFTRIGHT0;                   // 0x0014 MLC RGB Invalid Area0 Left, Right Register 0/1
    DWORD dINVALIDTOPBOTTOM0;                   // 0x0018 MLC RGB Invalid Area0 Top, Bottom Register 0/1
    DWORD dINVALIDLEFTRIGHT1;                   // 0x001C MLC RGB Invalid Area1 Left, Right Register 0/1
    DWORD dINVALIDTOPBOTTOM1;                   // 0x0020 MLC RGB Invalid Area1 Top, Bottom Register 0/1
    DWORD dCONTROL;                             // 0x0024 MLC RGB Layer Control Register 0/1
    DWORD dHSTRIDE;                             // 0x0028 MLC RGB Layer Horizontal Stride Register 0/1
    DWORD dVSTRIDE;                             // 0x002C MLC RGB Layer Vertical Stride Register 0/1
    DWORD dTPCOLOR;                             // 0x0030 MLC RGB Layer Transparency Color Register 0/1
    DWORD dINVCOLOR;                            // 0x0034 MLC RGB Layer Inversion Color Register 0/1
    DWORD dADDRESS;                             // 0x0038 MLC RGB Layer Base Address Register 0/1
#endif
} xRGB2_LAYER;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
#else                           // SPICA
    DWORD dLEFTRIGHT;                           // 0x0074 MLC Video Layer Left Right Register
    DWORD dTOPBOTTOM;                           // 0x0078 MLC Video Layer Top Bottom Register
    DWORD dCONTROL;                             // 0x007C MLC Video Layer Control Register
    DWORD dVSTRIDE;                             // 0x0080 MLC Video Layer Y Vertical Stride Register
    DWORD dTPCOLOR;                             // 0x0084 MLC Video Layer Transparency Color Register
    DWORD dReservedX[1];                        // 0x0088 Reserved Region
    DWORD dADDRESS;                             // 0x008C MLC Video Layer Y Base Address Register
    DWORD dADDRESSCB;                           // 0x0090 MLC Video Layer Cb Base Address Register
    DWORD dADDRESSCR;                           // 0x0094 MLC Video Layer Cr Base Address Register
    DWORD dVSTRIDECB;                           // 0x0098 MLC Video Layer Cb Vertical Stride Register
    DWORD dVSTRIDECR;                           // 0x009C MLC Video Layer Cr Vertical Stride Register
    DWORD dHSCALE;                              // 0x00A0 MLC Video Layer Horizontal Scale Register
    DWORD dVSCALE;                              // 0x00A4 MLC Video Layer Vertical Scale Register
    DWORD dLUENH;                               // 0x00A8 MLC Video Layer Luminance Enhancement Control Register
    DWORD dCHENH[4];                            // 0x00AC, 0xB0, 0xB4, 0xB8 MLC Video Layer Chrominance Enhancement Control Register 0/1/2/3
#endif
} xVIDEO_LAYER;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD dCONTROLT;                            // 0x0000 MLC Top Control Register
    DWORD dSCREENSIZE;                          // 0x0004 MLC Screen Size Register
    DWORD dBGCOLOR;                             // 0x0008 MLC Background Color Register
    xMLC_LAYER xLayer[2];
    DWORD dLEFTRIGHT2;                          // 0x0074 MLC Video Layer Left Right Register
    DWORD dTOPBOTTOM2;                          // 0x0078 MLC Video Layer Top Bottom Register
    DWORD dCONTROL2;                            // 0x007C MLC Video Layer Control Register
    DWORD dVSTRIDE2;                            // 0x0080 MLC Video Layer Y Vertical Stride Register
    DWORD dTPCOLOR2;                            // 0x0084 MLC Video Layer Transparency Color Register
    DWORD dReservedX;                           // 0x0088 Reserved Region
    DWORD dADDRESS2;                            // 0x008C MLC Video Layer Y Base Address Register
    DWORD dADDRESSCB;                           // 0x0090 MLC Video Layer Cb Base Address Register
    DWORD dADDRESSCR;                           // 0x0094 MLC Video Layer Cr Base Address Register
    DWORD dVSTRIDECB;                           // 0x0098 MLC Video Layer Cb Vertical Stride Register
    DWORD dVSTRIDECR;                           // 0x009C MLC Video Layer Cr Vertical Stride Register
    DWORD dHSCALE;                              // 0x00A0 MLC Video Layer Horizontal Scale Register
    DWORD dVSCALE;                              // 0x00A4 MLC Video Layer Vertical Scale Register
    DWORD dLUENH;                               // 0x00A8 MLC Video Layer Luminance Enhancement Control Register
    DWORD dCHENH[4];                            // 0x00AC ~ 0x00B8 MLC Video Layer Chrominance Enhancement Control Register 0/1/2/3
    DWORD dReserved[0xC1];                      // 0x00BC ~ 0x03BC
    DWORD dCLKENB;                              // 0x03C0 MLC Clock Enable Register
#else                           // SPICA
    DWORD dCONTROLT;                            // 0x0000 MLC Top Control Register
    DWORD dSCREENSIZE;                          // 0x0004 MLC Screen Size Register
    DWORD dBGCOLOR;                             // 0x0008 MLC Background Color Register

    xRGB1_LAYER  xRGB1Layer[2];                 // 0x000c ~ 0x0073
    xVIDEO_LAYER xVideoLayer;                   // 0x0074 ~ 0x00BB
    xRGB2_LAYER  xRGB2Layer;                    // 0x00BC ~ 0x00EB

    DWORD dGAMMACONT;                           // 0x00EC MLC Gama Control Register
    DWORD dRGAMMATABLEWRITE;                    // 0x00F0 MLC Red Gamma Table Write Register
    DWORD dGGAMMATABLEWRITE;                    // 0x00F4 MLC Green Gamma Table Write Register
    DWORD dBGAMMATABLEWRITE;                    // 0x00F8 MLC Blue Gamma Table Write Register
    DWORD dReservedX[(0x3C0 - 0xFC) / 4];       // 0x00FC ~ 0x03BC : Reserved Region
    DWORD dCLKENB;                              // 0x03C0 MLC Clock Enable Register
#endif
} xSYS_MLC;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    HWORD wReservedW;                           // 0x0000
    HWORD wVENCCTRLA;                           // 0x0002 Video Encoder Control A Register
    HWORD wVENCCTRLB;                           // 0x0004 Video Encoder Control B Register
    HWORD wReservedX;                           // 0x0006
    HWORD wVENCSCH;                             // 0x0008 Video Encoder SCH Phase Control Register
    HWORD wVENCHUE;                             // 0x000A Video Encoder HUE Phase Control Register
    HWORD wVENCSAT;                             // 0x000C Video Encoder Chroma Saturation Control Register
    HWORD wVENCCRT;                             // 0x000E Video Encoder Contrast Control Register
    HWORD wVENCBRT;                             // 0x0010 Video Encoder Bright Control Register
    HWORD wVENCFSCADJH;                         // 0x0012 Video Encoder Color Burst Frequency Adjustment High Register
    HWORD wVENCFSCADJL;                         // 0x0014 Video Encoder Color Burst Frequency Adjustment Low Register
    HWORD wReservedY[5];                        // 0x0016 ~ 0x001F
    HWORD wVENCDACSEL;                          // 0x0020 Video Encoder DAC Output Select Register
    HWORD wReservedZ[15];                       // 0x0022 ~ 0x003F
    HWORD wVENCICNTL;                           // 0x0040 Video Encoder Timing Configuration Register
    HWORD wReservedA[3];                        // 0x0042 ~ 0x0047
    HWORD wVENCHSVSO;                           // 0x0048 Video Encoder Horizontal & Vertical Sync Register
    HWORD wVENCHSOS;                            // 0x004A Video Encoder Horizontal Sync Start Register
    HWORD wVENCHSOE;                            // 0x004C Video Encoder Horizontal Sync End Register
    HWORD wVENCVSOS;                            // 0x004E Video Encoder Vertical Sync Start Register
    HWORD wVENCVSOE;                            // 0x0050 Video Encoder Vertical Sync End Register
    HWORD wReservedB[21];                       // 0x0052 ~ 0x007B

    HWORD wHTOTAL;                              // 0x007C DPC Horizontal Total Length Register
    HWORD wHSWIDTH;                             // 0x007E DPC Horizontal Sync Width Register
    HWORD wHASTART;                             // 0x0080 DPC Horizontal Active Video Start Register
    HWORD wHAEND;                               // 0x0082 DPC Horizontal Active Video End Register
    HWORD wVTOTAL;                              // 0x0084 DPC Vertical Total Length Register
    HWORD wVSWIDTH;                             // 0x0086 DPC Vertical Sync Width Register
    HWORD wVASTART;                             // 0x0088 DPC Vertical Active Video Start Register
    HWORD wVAEND;                               // 0x008A DPC Vertical Active Video End Register
    HWORD wCTRL0;                               // 0x008C DPC Control 0 Register
    HWORD wCTRL1;                               // 0x008E DPC Control 1 Register
    HWORD wEVTOTAL;                             // 0x0090 DPC Even Field Vertical Total Length Register
    HWORD wEVSWIDTH;                            // 0x0092 DPC Even Field Vertical Sync Width Register
    HWORD wEVASTART;                            // 0x0094 DPC Even Field Vertical Active Video Start Register
    HWORD wEVAEND;                              // 0x0096 DPC Even Field Vertical Active Video End Register
    HWORD wCTRL2;                               // 0x0098 DPC Control 2 Register
    HWORD wVSEOFFSET;                           // 0x009A DPC Vertical Sync End Offset Register
    HWORD wVSSOFFSET;                           // 0x009C DPC Vertical Sync Start Offset Register
    HWORD wEVSEOFFSET;                          // 0x009E DPC Even Field Vertical Sync End Offset Register
    HWORD wEVSSOFFSET;                          // 0x00A0 DPC Even Field Vertical Sync Start Offset Register
    HWORD wDELAY0;                              // 0x00A2 DPC Delay 0 Register
    HWORD wUPSCALECON0;                         // 0x00A4 DPC Sync Upscale Control Register 0
    HWORD wUPSCALECON1;                         // 0x00A6 DPC Sync Upscale Control Register 1
    HWORD wUPSCALECON2;                         // 0x00A8 DPC Sync Upscale Control Register 2
    HWORD wRNUMGENCON0;                         // 0x00AA DPC Sync Random Number Generator Control Register 0
    HWORD wRNUMGENCON1;                         // 0x00AC DPC Sync Random Number Generator Control Register 1
    HWORD wRNUMGENCON2;                         // 0x00AE DPC Sync Random Number Generator Control Register 2
    HWORD wRNUMGENFORMULASEL0;                  // 0x00B0 DPC Sync Random Number Generator Formula Select Low Register
    HWORD wRNUMGENFORMULASEL1;                  // 0x00B2 DPC Sync Random Number Generator Formula Select High Register
    HWORD wFDTADDR;                             // 0x00B4 DPc Sync Frame Dither Table Address Register
    HWORD wFRDITHERVALUE;                       // 0x00B6 DPC Sync Frame Red Dither Table Register
    HWORD wFGDITHERVALUE;                       // 0x00B8 DPC Sync Frame Green Dither Table Register
    HWORD wFBDITHERVALUE;                       // 0x00BA DPC Sync Frame Blue Dither Table Register
    HWORD wReservedC[0x82];                     // 0x00BC ~ 0x01BE
    DWORD dCLKENB;                              // 0x01C0 DPC Clock Generation Enable Register
    DWORD dCLKGEN[2];                           // 0x01C4 ~ 0x01C8 DPC Clock Generation Control 0/1 Register
#else                           // SPICA
    HWORD wHTOTAL;                              // 0x007C DPC Horizontal Total Length Register
    HWORD wHSWIDTH;                             // 0x007E DPC Horizontal Sync Width Register
    HWORD wHASTART;                             // 0x0080 DPC Horizontal Active Video Start Register
    HWORD wHAEND;                               // 0x0082 DPC Horizontal Active Video End Register
    HWORD wVTOTAL;                              // 0x0084 DPC Vertical Total Length Register
    HWORD wVSWIDTH;                             // 0x0086 DPC Vertical Sync Width Register
    HWORD wVASTART;                             // 0x0088 DPC Vertical Active Video Start Register
    HWORD wVAEND;                               // 0x008A DPC Vertical Active Video End Register
    HWORD wCTRL0;                               // 0x008C DPC Control 0 Register
    HWORD wCTRL1;                               // 0x008E DPC Control 1 Register
    HWORD wEVTOTAL;                             // 0x0090 DPC Even Field Vertical Total Length Register
    HWORD wEVSWIDTH;                            // 0x0092 DPC Even Field Vertical Sync Width Register
    HWORD wEVASTART;                            // 0x0094 DPC Even Field Vertical Active Video Start Register
    HWORD wEVAEND;                              // 0x0096 DPC Even Field Vertical Active Video End Register
    HWORD wCTRL2;                               // 0x0098 DPC Control 2 Register
    HWORD wVSEOFFSET;                           // 0x009A DPC Vertical Sync End Offset Register
    HWORD wVSSOFFSET;                           // 0x009C DPC Vertical Sync Start Offset Register
    HWORD wEVSEOFFSET;                          // 0x009E DPC Even Field Vertical Sync End Offset Register
    HWORD wEVSSOFFSET;                          // 0x00A0 DPC Even Field Vertical Sync Start Offset Register
    HWORD wDELAY0;                              // 0x00A2 DPC Delay 0 Register
    HWORD wUPSCALECON0;                         // 0x00A4 DPC Sync Upscale Control Register 0
    HWORD wUPSCALECON1;                         // 0x00A6 DPC Sync Upscale Control Register 1
    HWORD wUPSCALECON2;                         // 0x00A8 DPC Sync Upscale Control Register 2
    HWORD wRNUMGENCON0;                         // 0x00AA DPC Sync Random Number Generator Control Register 0
    HWORD wRNUMGENCON1;                         // 0x00AC DPC Sync Random Number Generator Control Register 1
    HWORD wRNUMGENCON2;                         // 0x00AE DPC Sync Random Number Generator Control Register 2
    HWORD wRNUMGENFORMULASEL0;                  // 0x00B0 DPC Sync Random Number Generator Formula Select Low Register
    HWORD wRNUMGENFORMULASEL1;                  // 0x00B2 DPC Sync Random Number Generator Formula Select High Register
    HWORD wFDTADDR;                             // 0x00B4 DPc Sync Frame Dither Table Address Register
    HWORD wFRDITHERVALUE;                       // 0x00B6 DPC Sync Frame Red Dither Table Register
    HWORD wFGDITHERVALUE;                       // 0x00B8 DPC Sync Frame Green Dither Table Register
    HWORD wFBDITHERVALUE;                       // 0x00BA DPC Sync Frame Blue Dither Table Register
    HWORD wReservedX[0x82];                     // 0x00BC ~ 0x1BE
    DWORD dCLKENB;                              // 0x01C0 DPC Clock Generation Enable Register
    DWORD dCLKGEN[2][2];                        // 0x01C4 DPC Clock Generation Control 0 Low Register
                                                // 0x01C8 DPC Clock Generation Control 0 High Register
                                                // 0x01CC DPC Clock Generation Control 1 Low Register
                                                // 0x01D0 DPC Clock Generation Control 1 High Register
#endif
} xSYS_DPC;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    HWORD wPREPOL;                              // 0x0000 Prescaler Register  
    HWORD wDUTY[2];                             // 0x0002 ~ 0x0004 Duty Cycle Register
    HWORD wPERIOD[2];                           // 0x0006 ~ 0x0008 Period Cycle Register
    HWORD wReservedX[3];                        // 0x000A ~ 0x000E Reserved Region
#else                           // SPICA
    HWORD wPREPOL;                              // 0x0000 Prescaler Register  
    HWORD wDUTY[2];                             // 0x0002 ~ 0x0004 Duty Cycle Register
    HWORD wPERIOD[2];                           // 0x0006 ~ 0x0008 Period Cycle Register
    HWORD wReservedX[3];                        // 0x000A ~ 0x000E Reserved Region
#endif
} xPWM_LAYER;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    xPWM_LAYER xLayer[2];
    DWORD dReservedX[0x08];                     // 0x0020 ~ 0x003C
    DWORD dCLKENB;                              // 0x0040 Clock Enable Register
    DWORD dCLKGEN;                              // 0x0044 Clock Generater Register
#else                           // SPICA
    xPWM_LAYER xLayer[2];
    DWORD dReservedX[0x08];                     // 0x0020 ~ 0x003C
    DWORD dCLKENB;                              // 0x0040 Clock Enable Register
    DWORD dCLKGEN;                              // 0x0044 Clock Generater Register
#endif
} xSYS_PWM;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    DWORD	dCTRL;                                // 0x0000 Control
    DWORD	dReservedX;                           // 0x0004 Reserved
    DWORD	dCLKDIV;                              // 0x0008 Clock Divider 
    DWORD	dReservedY;                           // 0x000C Reserved
    DWORD	dCLKENA;                              // 0x0010 Clock Enable
    DWORD	dTMOUT;                               // 0x0014 Time-Out
    DWORD	dCTYPE;                               // 0x0018 Card Type
    DWORD	dBLKSIZE;                             // 0x001C Block Size
    DWORD	dBYTCNT;                              // 0x0020 Byte Count
    DWORD	dINTMASK;                             // 0x0024 Interrupt Mask
    DWORD	dCMDARG;                              // 0x0028 Command Argument
    DWORD	dCMD;                                 // 0x002C Command
    DWORD	dRESP0;                               // 0x0030 Response 0
    DWORD	dRESP1;                               // 0x0034 Response 1
    DWORD	dRESP2;                               // 0x0038 Response 2
    DWORD	dRESP3;                               // 0x003C Response 3
    DWORD	dMINTSTS;                             // 0x0040 Masked Interrupt
    DWORD	dRINTSTS;                             // 0x0044 Raw Interrupt
    DWORD	dSTATUS;                              // 0x0048 Status
    DWORD	dFIFOTH;                              // 0x004C FIFO Threshold Watermark
    UCHAR bReservedX[0x05C-0x050];              // 0x0050 ~ 0x005B
    DWORD	dTCBCNT;                              // 0x005C Transferred Card Byte Count
    DWORD	dTBBCNT;                              // 0x0060 Transferred Host Byte Count
    UCHAR bReservedY[0x100-0x064];              // 0x0064 ~ 0x0FF Reserved
    DWORD	dDATA;                                // 0x0100 Data
    UCHAR bReservedZ[0x7C0-0x104];              // 0x0104 ~ 0x07BF : Reserved
    DWORD	dCLKENB;                              // 0x07C0 Clock Enable for CLKGEN module
    DWORD	dCLKGEN;                              // 0x07C4 Clock Generator for CLKGEN module
#else                           // SPICA
    DWORD	dCTRL;                                // 0x0000 Control
    DWORD	dReservedX;                           // 0x0004 Reserved
    DWORD	dCLKDIV;                              // 0x0008 Clock Divider 
    DWORD	dReservedY;                           // 0x000C Reserved
    DWORD	dCLKENA;                              // 0x0010 Clock Enable
    DWORD	dTMOUT;                               // 0x0014 Time-Out
    DWORD	dCTYPE;                               // 0x0018 Card Type
    DWORD	dBLKSIZE;                             // 0x001C Block Size
    DWORD	dBYTCNT;                              // 0x0020 Byte Count
    DWORD	dINTMASK;                             // 0x0024 Interrupt Mask
    DWORD	dCMDARG;                              // 0x0028 Command Argument
    DWORD	dCMD;                                 // 0x002C Command
    DWORD	dRESP0;                               // 0x0030 Response 0
    DWORD	dRESP1;                               // 0x0034 Response 1
    DWORD	dRESP2;                               // 0x0038 Response 2
    DWORD	dRESP3;                               // 0x003C Response 3
    DWORD	dMINTSTS;                             // 0x0040 Masked Interrupt
    DWORD	dRINTSTS;                             // 0x0044 Raw Interrupt
    DWORD	dSTATUS;                              // 0x0048 Status
    DWORD	dFIFOTH;                              // 0x004C FIFO Threshold Watermark
    UCHAR bReservedX[0x05C-0x050];              // 0x0050 ~ 0x005B
    DWORD	dTCBCNT;                              // 0x005C Transferred Card Byte Count
    DWORD	dTBBCNT;                              // 0x0060 Transferred Host Byte Count
    UCHAR bReservedY[0x100-0x064];              // 0x0064 ~ 0x0FF Reserved
    DWORD	dDATA;                                // 0x0100 Data
    UCHAR bReservedZ[0x7C0-0x104];              // 0x0104 ~ 0x07BF : Reserved
    DWORD	dCLKENB;                              // 0x07C0 Clock Enable for CLKGEN module
    DWORD	dCLKGEN;                              // 0x07C4 Clock Generator for CLKGEN module
#endif
} xSYS_SDCD;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    HWORD wCONT0;                               // 0x0000 : SSP/SPI Control Register 0
    HWORD wCONT1;                               // 0x0002 : SSP/SPI Control Register 1
    HWORD wDATA;                                // 0x0004 : SSP/SPI Data Register
    HWORD wSTATE;                               // 0x0006 : SSP/SPI Status Register
    HWORD wRXBUSTSIZE;                          // 0x0008 : SSP/SPI Burst Receive Size Register
    HWORD wReserved[0x1B];                      // 0x000A ~ 0x003E
    DWORD dCLKENB;                              // 0x0040 : SSP/SPI Clock Enable Register
    DWORD dCLKGEN;                              // 0x0044 : SSP/SPI Clock Generator Register
#else                           // SPICA
    HWORD wCONT0;                               // 0x0000 : SSP/SPI Control Register 0
    HWORD wCONT1;                               // 0x0002 : SSP/SPI Control Register 1
    HWORD wDATA;                                // 0x0004 : SSP/SPI Data Register
    HWORD wSTATE;                               // 0x0006 : SSP/SPI Status Register
    HWORD wRXBUSTSIZE;                          // 0x0008 : SSP/SPI Burst Receive Size Register
    HWORD wReserved[0x1B];                      // 0x000A ~ 0x003E
    DWORD dCLKENB;                              // 0x0040 : SSP/SPI Clock Enable Register
    DWORD dCLKGEN;                              // 0x0044 : SSP/SPI Clock Generator Register
#endif
} xSYS_SSPSPI;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
    HWORD wADCCON;                              // 0x0000 : ADC Control Register
    HWORD wReservedW;                           // 0x0002 : RESERVED_0
    HWORD wADCDAT;                              // 0x0004 : ADC Output Data Register
    HWORD wReservedX;                           // 0x0006 : RESERVED_1
    HWORD wADCINTENB;                           // 0x0008 : ADC Interrupt Enable Register
    HWORD wReservedY;                           // 0x000A : RESERVED_2
    HWORD wADCINTCLR;                           // 0x000C : ADC Interrutp Pending and Clear Register
    HWORD wReservedZ[0x19];                     // 0x000E ~ 0x003E
    DWORD dCLKENB;                              // 0x0040 : ADC Clock Enable Register
#else                           // SPICA
    HWORD wADCCON;                              // 0x0000 : ADC Control Register
    HWORD wReservedW;                           // 0x0002 : RESERVED_0
    HWORD wADCDAT;                              // 0x0004 : ADC Output Data Register
    HWORD wReservedX;                           // 0x0006 : RESERVED_1
    HWORD wADCINTENB;                           // 0x0008 : ADC Interrupt Enable Register
    HWORD wReservedY;                           // 0x000A : RESERVED_2
    HWORD wADCINTCLR;                           // 0x000C : ADC Interrutp Pending and Clear Register
    HWORD wReservedZ[0x19];                     // 0x000E ~ 0x003E
    DWORD dCLKENB;                              // 0x0040 : ADC Clock Enable Register
#endif
} xSYS_ADC;
#endif
//=============================================================================
#if !defined(_ASSEMBLER_)
typedef volatile struct
{
#if defined(__POLLUX__)
#else                           // SPICA
    HWORD wCTRL;                                // 0x0000 : Control Register
    HWORD wReservedX;                           // 0x0002 : Reserved Regopm
    HWORD wSTAT;                                // 0x0004 : Status Register
    HWORD wLOWPERIOD;                           // 0x0006 : Low Period Register
    HWORD wHIGHPERIOD;                          // 0x0008 : High Period Register
    UCHAR bReservedY[0x40 - 0x0A];              // 0x000A ~ 0x3C : Reserved Region
    DWORD dCLKENB;                              // 0x0040 : Clock Enable Register
    DWORD dCLKGEN;                              // 0x0044 : Clock Generator Register
    DWORD dCLKGENHIGH;                          // 0x0048 : Clock Generator High Register
#endif
} xSYS_PPM;
#endif
//=============================================================================

#endif

