/*...........................................................................*/
/*.                  File Name : SYSSDCD.H                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.09                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef _LIB_FILE_MAKE_
    #include "DataType.h"
#endif

#ifndef  __SYSSDCD_H__
#define  __SYSSDCD_H__

//=============================================================================
#define  SDCD_MODE_POLLING                   0    // Polling Mode
#define  SDCD_MODE_DMA                       1    // DMA Mode
#define  SDCD_DATA_WIDTH_1BIT                0
#define  SDCD_DATA_WIDTH_4BIT                1
#define  SDCD_BLOCK_LENGTH                 512
//=============================================================================
#define  SDCD_PCLKMODE_DISABLE           0    // PCLK is Disable
#define  SDCD_PCLKMODE_ENABLE            1    // PCLK is Enable

#define  SDCD_CLKSRCSEL_PCLK             0
#define  SDCD_CLKSRCSEL_PLL0             1
#define  SDCD_CLKSRCSEL_PLL1             2

#define  SDCD_CLK_SOURCE_SELECT  (SDCD_CLKSRCSEL_PLL1)

#if  SDCD_CLK_SOURCE_SELECT == SDCD_CLKSRCSEL_PCLK
		#define  SDCD_CLK_DIVISOR                3    // 22MHZ = PCLK/3 = 66 MHz / 3
		#define  SDCD_SET_CLK_DIVIDER_400KHZ    56    // 392 KHz
		#define  SDCD_RUN_CLK_DIVIDER_25MHZ      1    //  22 MHz
#endif
#if  SDCD_CLK_SOURCE_SELECT == SDCD_CLKSRCSEL_PLL1
		#define  SDCD_CLK_DIVISOR                6    // 22MHZ = PCLK/3 = 66 MHz / 3
		#define  SDCD_SET_CLK_DIVIDER_400KHZ    56    // 392 KHz
		#define  SDCD_RUN_CLK_DIVIDER_25MHZ      1    //  22 MHz
#endif
//=============================================================================
#define  SDCD_CMDFLAG_STARTCMD		   (1 << 31)
#define  SDCD_CMDFLAG_UPDATECLKONLY	(1 << 21)
#define  SDCD_CMDFLAG_SENDINIT 		(1 << 15)
#define  SDCD_CMDFLAG_STOPABORT		(1 << 14)
#define  SDCD_CMDFLAG_WAITPRVDAT		(1 << 13)
#define  SDCD_CMDFLAG_SENDAUTOSTOP 	(1 << 12)
#define  SDCD_CMDFLAG_BLOCK			(0 << 11)
#define  SDCD_CMDFLAG_STREAM			(1 << 11)
#define  SDCD_CMDFLAG_TXDATA			(3 <<  9)
#define  SDCD_CMDFLAG_RXDATA			(1 <<  9)
#define  SDCD_CMDFLAG_CHKRSPCRC		(1 <<  8)
#define  SDCD_CMDFLAG_SHORTRSP 		(1 <<  6)
#define  SDCD_CMDFLAG_LONGRSP			(3 <<  6)
//=============================================================================
#define  SDCD_INT_SDIO                        16
#define  SDCD_INT_EBE                         15
#define  SDCD_INT_ACD                         14
#define  SDCD_INT_SBE                         13
#define  SDCD_INT_HLE                         12
#define  SDCD_INT_FRUN                        11
#define  SDCD_INT_HTO                         10
#define  SDCD_INT_DRTO                         9
#define  SDCD_INT_RTO                          8
#define  SDCD_INT_DCRC                         7
#define  SDCD_INT_RCRC                         6
#define  SDCD_INT_RXDR                         5
#define  SDCD_INT_TXDR                         4
#define  SDCD_INT_DTO                          3
#define  SDCD_INT_CD                           2
#define  SDCD_INT_RE                           1
//=============================================================================
#define  SDCD_CMD_GO_IDLE_STATE_00              0
#define  SDCD_CMD_ALL_SEND_CID_02               2
#define  SDCD_CMD_SEND_RELATIVE_ADDR_03         3
#define  SDCD_CMD_SEL_DESELECT_CARD_07          7
#define  SDCD_CMD_IF_COND_08                    8
#define  SDCD_CMD_SEND_CSD_09                   9
#define  SDCD_CMD_SEND_CID_10                  10
#define  SDCD_CMD_READ_DAT_UNTIL_STOP_11       11
#define  SDCD_CMD_STOP_TRANSMISSION_12         12
#define  SDCD_CMD_SEND_STATUS_13               13
#define  SDCD_CMD_GO_INACTIVE_STATE_15         15
#define  SDCD_CMD_SET_BLOCK_LEN_16             16
#define  SDCD_CMD_READ_SINGLE_BLOCK_17         17
#define  SDCD_CMD_READ_MULTI_BLOCK_18          18
#define  SDCD_CMD_WRITE_SINGLE_BLOCK_24        24
#define  SDCD_CMD_WRITE_MULTI_BLOCK_25         25
#define  SDCD_CMD_PROGRAM_CSD_27               27
#define  SDCD_CMD_SET_WRITE_PROT_28            28
#define  SDCD_CMD_CLR_WRITE_PROT_29            29
#define  SDCD_CMD_SEND_WRITE_PROT_30           30
#define  SDCD_CMD_ERASE_WR_BLOCK_START_32      32
#define  SDCD_CMD_ERASE_WR_BLOCK_END_33        33
#define  SDCD_CMD_ERASE_BLOCK_RUN_38           38
#define  SDCD_CMD_APP_CMD_55                   55
#define  SDCD_CMD_GEN_CMD_56                   56
#define  SDCD_ACMD_UNMASK                    0xff
#define  SDCD_ACMD_SET_BUS_WIDTH_06           ((SDCD_CMD_APP_CMD_55 << 8) +  6)
#define  SDCD_ACMD_SD_STATUS_13               ((SDCD_CMD_APP_CMD_55 << 8) + 13)
#define  SDCD_ACMD_SEND_NUM_WR_BLOCKS_22      ((SDCD_CMD_APP_CMD_55 << 8) + 22)
#define  SDCD_ACMD_SET_WR_BLK_ERASE_COUNT_23  ((SDCD_CMD_APP_CMD_55 << 8) + 23)
#define  SDCD_ACMD_SD_APP_OP_COND_41          ((SDCD_CMD_APP_CMD_55 << 8) + 41)
#define  SDCD_ACMD_SET_CLR_CARD_DETECT_42     ((SDCD_CMD_APP_CMD_55 << 8) + 42)
#define  SDCD_ACMD_SEND_SCR_51                ((SDCD_CMD_APP_CMD_55 << 8) + 51)
//----------------------------------------------------------------------------
#define  SDCD_STATUS_NOERROR                      0
#define  SDCD_STATUS_ERROR                 (1 << 31)
#define  SDCD_STATUS_CMDBUSY      (SDCD_STATUS_ERROR | (1 << 0))
#define  SDCD_STATUS_CMDTOUT      (SDCD_STATUS_ERROR | (1 << 1))
#define  SDCD_STATUS_RESCRCFAIL   (SDCD_STATUS_ERROR | (1 << 2))
#define  SDCD_STATUS_RESERROR     (SDCD_STATUS_ERROR | (1 << 3))
#define  SDCD_STATUS_RESTOUT      (SDCD_STATUS_ERROR | (1 << 4))
#define  SDCD_STATUS_UNKNOWNCMD   (SDCD_STATUS_ERROR | (1 << 5))
//=============================================================================

//============================================================================
typedef  struct {
       	 UCHAR bMID;
       	 HWORD wOID;
       	 UCHAR vPNM[5];
       	 UCHAR bPRV;
       	 DWORD dPSN;
       	 HWORD wMDT;
        }xCID;
typedef  struct {
       	 UCHAR bStructure;
       	 UCHAR bTAAC;
       	 UCHAR bNSAC;
       	 UCHAR bTranSpeed;
       	 HWORD wCCC;
       	 UCHAR bReadBlLen;
       	 UCHAR bReadBlPar;
       	 UCHAR bWriteBlkMisAlgn;
       	 UCHAR bReadBlkMisAlgn;
       	 UCHAR bDsrImp;
       	 HWORD wDeviceSize;
       	 UCHAR bVDDrCurrMin;
       	 UCHAR bVDDrCurrMax;
       	 UCHAR bVDDwCurrMin;
       	 UCHAR bVDDwCurrMax;
       	 UCHAR bDeviceSizeMul;
       	 UCHAR bEraseBlkEnable;
       	 UCHAR bEraseSectSize;
       	 UCHAR bWpGrpSize;
       	 UCHAR bWpGrpEnable;
       	 UCHAR bR2wFactor;
       	 UCHAR bWriteBlLen;
       	 UCHAR bWriteBlPar;
       	 UCHAR bFileFormatGrp;
       	 UCHAR bCopy;
       	 UCHAR bPermWriteProtect;
       	 UCHAR bTmpWriteProtect;
       	 UCHAR bFileFormat;
        }xCSD;
typedef  struct {
       	 UCHAR bStructure;
       	 UCHAR bSdSpec;
       	 UCHAR bStatusAftErase;
       	 UCHAR bSdSecurity;
       	 UCHAR bSdBusWidth;
       	 HWORD wOID;
       	 UCHAR vPNM[5];
       	 UCHAR bPRV;
       	 DWORD dPSN;
       	 HWORD wMDT;
        }xSCR;
typedef  struct {
       	 UCHAR bDataBusWidth;
       	 UCHAR bSecureMode;
       	 HWORD wSdCardType;
       	 DWORD dSizeOfProctedArea;
        }xSdSTATUS;
typedef  struct {
         int    nDataValid;        // 0=invalid,1=valid
         UCHAR  bBootMark;         // 0x80=active Partition;
         UCHAR  bStartHead;        // Starting Head
         UCHAR  bStartSector;      // Starting Sector
         UCHAR  bStartCyl;         // Starting Cylinder
         UCHAR  bSystemID;         // System ID
         UCHAR  bEndHead;          // Ending Head
         UCHAR  bEndSector;        // Ending Sector
         UCHAR  bEndCyl;           // Ending Cylinder
         UCHAR  bRelSector0;       // Relative Sector Byte-0
         UCHAR  bRelSector1;       // Relative Sector Byte-1
         UCHAR  bRelSector2;       // Relative Sector Byte-2
         UCHAR  bRelSector3;       // Relative Sector Byte-3
         UCHAR  bTotSector0;       // Total    Sector Byte-0
         UCHAR  bTotSector1;       // Total    Sector Byte-1
         UCHAR  bTotSector2;       // Total    Sector Byte-2
         UCHAR  bTotSector3;       // Total    Sector Byte-3
        }xMBR;
typedef  struct {
         int     m_nExistMode;     // 0=not-exist,1=exist
         DWORD   m_dBaseAddr;
         DWORD   m_dPortStatus;
         int     m_nCardInserted;  // SDCARD_CARD_IN_STATUS_FULL : SD-CARD �a slot�A ���q
         int     m_nCardChanged;   // SDCARD_CARD_CHANGE_OCCURED : SD-CARD �a �a��
         DWORD   m_dRCA;
         DWORD   m_dSdState;
         DWORD   m_dCommand;
         DWORD   m_dRspType;
         DWORD   m_dArgument;
         int     m_nBlockCnt;
         int     m_nBlockLen;
         int     m_nSizeToRead;
         volatile int     m_nCmdRunStatus;
         DWORD   m_vRspRegX[4];
         volatile DWORD   m_nErrorCode;
         volatile DWORD   m_dCmdSendTick;   // 0=�����a�w,interrupt�A�� 0�a�� �q
         UCHAR  *m_pTxDataBuff;
         volatile int     m_nTxDataSize;
         volatile int     m_nTxDataTail;
         UCHAR  *m_pRxDataBuff;
         volatile int     m_nRxDataSize;
         xCID    m_xCID;
         xCSD    m_xCSD;
         xSCR    m_xSCR;
         DWORD   m_dCardStatus;
         xSdSTATUS m_xSdStatus;
         xMBR    m_xMBR[4];
        }xSDCARD;
//============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

//=============================================================================
void  SysInitSDCD(void);
DWORD SysSdCardReadBlockData(DWORD dBlockNo,void *pReadAddr,DWORD dReadSize);
void  SysSdCardReadExitError(DWORD *pAlgnBuff);
DWORD SysSdCardWriteBlockData(DWORD dBlockNo,void *pWritedAddr,DWORD dWriteSize);
void  SysSdCardWriteExitError(DWORD *pAlgnBuff);
DWORD SysSdCardSetUpAndDetect(void);
//=============================================================================
void  SysSetSdCardRCA(DWORD dRCA);
DWORD SysGetSdCardRCA(void);
void  SysSetSdCardSetStatus(DWORD dStatus);
DWORD SysGetSdCardSetStatus(void);
void  SysGetSdCardFull16BytesCID(UCHAR *pFull16BytesCID);

void  SysParseSdCardCID(void);
void  SysParseSdCardCSD(void);
void  SysParseSdCardStatus(void);
void  SysParseSdCardSdStatus(void);
void  SysParseSdCardMBR(UCHAR *pData);
DWORD SysGetSdCardBitsFromRspData(int nMSB,int nLSB);
void  SysCheckSdCardChangStatus(void);
DWORD SysGetSdCardRealPortStatus(DWORD *pExistStatus);
//=============================================================================
DWORD SysSendSdCardCommand(DWORD dCommand,DWORD dArgument);
DWORD SysSendSdCardAppCommand(DWORD dCommand,DWORD dArgument);
DWORD SysSendSdCardCommandInternal(DWORD dCommand,DWORD dAargument);
DWORD SysSendSdCardCommandInternalByPoll(DWORD dCommand,DWORD dAargument,DWORD dFlag);
DWORD SysSendSdCardCommandInternalByDMA(DWORD dCommand,DWORD dAargument,DWORD dFlag);
DWORD SysGetSdCardFlagByCommand(DWORD dCommand,DWORD *pFlag);
DWORD SysSetSdCardClock(DWORD dDivider);
//=============================================================================
void  SysSetSdCdClockPClkMode(DWORD dPclkMode);
void  SysSetSdCdClockSource(DWORD dClkSrc);
void  SysSetSdCdClockDivisor(DWORD dDivisor);
void  SysSetSdCdClockDivisorEnable(int nDisableEnableMode);
void  SysSetSdCdClockDivider(DWORD dDivider);
void  SysSetSdCdClockDividerEnable(int nDisableEnableMode);
void  SysSetSdCdByteOrder(void);
void  SysReSetSdCdDMA(void);
void  SysReSetSdCdFIFO(void);
void  SysReSetSdCdCTRL(void);
int   SysIsSdCdResetDMA(void);
int   SysIsSdCdResetFIFO(void);
int   SysIsSdCdResetCTRL(void);
void  SysSetSdCdInterruptEnableAll(int nDisableEnableMode);
DWORD SysGetSdCdInterruptPending32(void);
DWORD SysGetSdCdInterruptPendingAll(void);
void  SysClearSdCdInterruptPendingAll(void);
void  SysClearSdCdInterruptPendingOne(DWORD dIntNo);
DWORD SysGetSdCdInterruptPendingOne(DWORD dIntNo);
void  SysSetSdCdDataTimeOut(DWORD dTimeOut);
void  SysSetSdCdResponseTimeOut(DWORD dTimeOut);
void  SysSetSdCdDataBusWidth(DWORD dBusWidth);
void  SysSetSdCdBlockSize(DWORD dBlockSize);
void  SysSetSdCdByteCount(DWORD dSizeInByte);
void  SysStartSdCdCommand(void);
void  SysSetSdCdCommand(DWORD dCmd,DWORD dFlag);
void  SysSetSdCdArgument(DWORD dCmdArg);
void  SysSetSdCdDMAMode(int nPollDmaMode);
void  SysSetSdCdFIFORxThreshold(DWORD dThreshold);
void  SysSetSdCdFIFOTxThreshold(DWORD dThreshold);
int   SysIsSdCdCommandBusy(void);
DWORD SysGetSdCdAutoStopResponse(void);
UCHAR SysGetSdCdResponseIndex(void);
DWORD SysGetSdCdShortResponse(void);
void  SysGetSdCdLongResponse(DWORD *pResponse);
UCHAR SysGetSdCdCRC(void);
void  SysReadSdCdData32(DWORD *pTarget);
void  SysWriteSdCdData32(DWORD *pSource);
DWORD SysGetSdCdFIFOCount(void);
int   SysIsSdCdDataTransferBusy(void);
int   SysIsSdCdCardDataBusy(void);
int   SysIsScCdCardPresent(void);
int   SysIsSdCdTxBusy(void);
int   SysIsSdCdRxBusy(void);
int   SysIsSdCdFIFOFull(void);
int   SysIsSdCdFIFOEmpty(void);
int   SysIsSdCdFIFOTxThreshold(void);
int   SysIsSdCdFIFORxThreshold(void);
//=============================================================================

#ifdef  __cplusplus
}
#endif

#endif

