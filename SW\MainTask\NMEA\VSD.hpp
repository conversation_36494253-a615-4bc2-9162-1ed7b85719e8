#include "Sentence.hpp"

#ifndef __VSD_HPP__
#define __VSD_HPP__

/******************************************************************************
 * 
 * VSD - Voyage Static Data
 *
 * $--VSD,x.x,x.x,x.x,c--c,hhmmss.ss,xx,xx,x.x,x.x*hh<CR><LF>
 *         |   |   |   |     |       |  |   |   |
 *         1   2   3   4     5       6  7   8   9
 *
 * 1. Type of ship and cargo category, 0 to 255
 * 2. Maximum present static draught, 0 to 25.5
 * 3. Persons on-board, 0 to 8191
 * 4. Destination, 1-20 characters
 * 5. Est. UTC of destination arrival
 * 6. Est. day of arrival at destination, 00 to 31(UTC)
 * 7. Est. month of arrival at destination, 00 to 12(UTC)
 * 8. Navigational status, 0 to 15
 * 9. Regional application flags, 0 to 15
 *
 * Add talkerId check : HSI 2012.04.26
 *
 ******************************************************************************/
class CVsd : public CSentence {
	protected:
		int    m_nShipCargoType;
		double m_dblDraught;
		int    m_nPersons;
		char   m_szTalkID[10];		  // 2 + 1(NULL)s
		char   m_szDestination[60+1];   // 60(For Special Charcter conversion) + 1(NULL)
		char   m_szEstUTC[20];        // 6 + 1(NULL)
		int    m_nEstUTCHour;
		int    m_nEstUTCMin;
		int    m_nEstDay;
		int    m_nEstMonth;
		int    m_nNavStatus;
		int    m_nRegionalAppFlags;
        
    public:
        CVsd();
        CVsd(char *pszSentence);

		void Parse();
		void SetSentence(char *pszSentence);
		int  GetFormat() { return m_nFormat; }
		void GetPlainText(char *pszPlainText);
		int  MakeSentence(BYTE *pszSentence);
		
		void SetShipCargoType(int nShipCargoType)       { m_nShipCargoType = nShipCargoType; }
		void SetDraught(double dblDraught)              { m_dblDraught = dblDraught; }
		void SetPersons(int nPersons)                   { m_nPersons = nPersons; }
		void SetDestination(const char *pszDest);
		void SetUTC(const char *pszUTC)                       { strcpy(m_szEstUTC, pszUTC); }
		void SetUTCDay(int nUTCDay)                     { m_nEstDay = nUTCDay; }
		void SetUTCMonth(int nUTCMonth)                 { m_nEstMonth = nUTCMonth; }
		void SetNavStatus(int nNavStatus)               { m_nNavStatus = nNavStatus; }
		void SetRegionalAppFlags(int nRegionalAppFlags) { m_nRegionalAppFlags = nRegionalAppFlags; }

		int    GetShipCargoType()    { return m_nShipCargoType; }
		double GetDraught()          { return m_dblDraught; }
		int    GetPersons()          { return m_nPersons; }
		void   GetTalkID(char *pszDest) { strcpy(pszDest, m_szTalkID); } // HSI 2014.04.26
		void   GetDestination(char *pszDest) { strcpy(pszDest, m_szDestination); }
		void   GetEstUTC(char *pszEstUTC)    { strcpy(pszEstUTC, m_szEstUTC);    }
		int    GetEstUTCHour()       { return m_nEstUTCHour; }
		int    GetEstUTCMin()        { return m_nEstUTCMin;  }
		int    GetEstDay()           { return m_nEstDay;     }
		int    GetEstMonth()         { return m_nEstMonth;   }
		int    GetNavStatus()        { return m_nNavStatus;  }
		int    GetRegionalAppFlags() { return m_nRegionalAppFlags; }
};

#endif

