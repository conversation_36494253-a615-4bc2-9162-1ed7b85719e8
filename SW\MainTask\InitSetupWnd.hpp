#include "Wnd.hpp"

#ifndef __INIT_SETUP_WND_HPP__
#define __INIT_SETUP_WND_HPP__

class CInitSetupWnd : public CWnd {
private:
	int m_nSelNum;

public:
	CInitSetupWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	void OnKeyEvent(int nKey, DWORD nFlags);
	void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
	void DrawSubMenu(int nSelNum);
	void SetFocus(int nFocus)   { m_nFocus = nFocus; }
	void SetSelNum(int nSelNum) { m_nSelNum = nSelNum; }
	int  GetSelNum()            { return m_nSelNum;    }

	int CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
};

#endif

