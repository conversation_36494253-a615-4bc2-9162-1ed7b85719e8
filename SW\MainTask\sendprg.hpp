/*...........................................................................*/
/*.                  File Name : SENDPRG.HPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2005.04.23                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "SamMapConst.h"
#include "SamMapAll.hpp"

#include "type.hpp"
#include "sysconst.h"
#include "screen.hpp"
#include "keybd.hpp"
#include "uart.hpp"
#include "time.hpp"
#include "flash.hpp"
#include "DataBack.hpp"
#include "wnd.hpp"

#ifndef  __SENDPRG_HPP
#define  __SENDPRG_HPP

//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------

class cSENDPRG
{
	private:
		cSCREEN *m_pScreen;
		cKEYBD  *m_pKeyBD;
		cUART   *m_pUart;
		cFLASH  *m_pFlash;
		int	  m_nBright;
	
	public:

	public:
		cSENDPRG(cSCREEN *pScreen,cKEYBD *pKeyBD,cUART *pUart, int nBr);
		virtual ~cSENDPRG(void);
		
	public:
		int   SendProgram(int nType, cSamMapAll *pMapData);		
		int   SendOneRecord(const BYTE *pCmd,DWORD dFrameNo,WORD wSendSize,const BYTE *pSendData);
		void  DspSendStatus(DWORD dDownSize,DWORD dDownTotal,CWnd *pWnd);
		void  SetEndianData(BYTE *pTarget,void *pSource,int nSize);
		void  GetEndianData(void *pTarget,BYTE *pSource,int nSize);
};

#endif

