#include "Wnd.hpp"

#ifndef __CHECK_CTRL_H__
#define __CHECK_CTRL_H__

class CCheckCtrl : public CWnd {
	protected:
		BYTE **m_pszCaption;
		RECT  m_rectSize;
		int   m_nFontSize;
		int   m_nTextLen;
		int   m_nMaxLen;
		int   m_nFontWidth, m_nFontHeight;
		BOOL  m_bFocus;
		BOOL  m_bChecked;
		BOOL   m_bEnable;
		FONT  *m_pFont;
		void SetFontSize(int nFontSize);

	public:
		CCheckCtrl(cSCREEN *pScreen, const BYTE **pCaption=0, DWORD dWndID=0);
		~CCheckCtrl();

		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		void OnKeyEvent(int nKey, DWORD nFlags=0);
		void OnCursorEvent(int nState);
		void SetFocus(BOOL bFocus=1) { m_bFocus = bFocus; }
		void Create(int x, int y, const BYTE **pszCaption, int nFontSize=FONT_ENG_12X06);
		void Create(POINT pos, const BYTE **pszCaption, int nFontSize=FONT_ENG_12X06);
		void SetCheck() { m_bChecked = !m_bChecked; }
		void SetCheck(BOOL bChecked) { m_bChecked = bChecked; }
		BOOL GetCheck() { return m_bChecked; }
		void Enable(BOOL bEnable=1) { m_bEnable = bEnable; }
};

#endif

