#include <stdio.h>
#include "RSSListWnd.hpp"
#include "DocMgr.hpp"
#include "Alarm.hpp"
#include "keybd.hpp"
#include "const.h"
#include "DblList.h"
#include "NMEA/Sentence.hpp"
#include "SamMapConst.h"
#include "Comlib.h"

extern CDocMgr          *g_pDocMgr;

CRSSListWnd::CRSSListWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
: CWnd(pScreen, pCaption, dWndID)
{
}

void CRSSListWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
	switch( nKey )
	{
	case KBD_SCAN_CODE_UP:
	case KBD_SCAN_CODE_DOWN:
	case KBD_SCAN_CODE_LEFT:
	case KBD_SCAN_CODE_RIGHT:
		break;
	default:
		break;
	}
}

void CRSSListWnd::DrawWnd(BOOL bRedraw/*TRUE*/)
{
	CWnd::DrawWnd(bRedraw);

	int nLangMode = g_pDocMgr->GetLangMode();

	if( bRedraw )
	{
		DrawButton(3, (BYTE *)FK_PREV[nLangMode]);
	}

	DrawRSSLogList();
}

void CRSSListWnd::ResetRSSLogList()
{
	DrawWnd();
}

void CRSSListWnd::DrawRSSLogList()
{
	
	int    nPosY = 0;
	int    i;

	if( m_pScreen->GetLcdType() == LCD_TYPE_MONO )       nPosY = 27;//47;
	else if( m_pScreen->GetLcdType() == LCD_TYPE_COLOR ) nPosY = 24;//44;

	int list_end = g_pDocMgr->GetRSSListSize();

	for( int i=0; i<list_end; i++ )
	{
		m_pScreen->WriteStr((const CHAR *)ConvertCharStrToUniStr((CHAR *)g_pDocMgr->GetRSSList(i),LNG_ENG), 5, nPosY, TEXT_MODE_NORMAL,  FONT_ENG_12X06);
		nPosY += 17;
	}
}
