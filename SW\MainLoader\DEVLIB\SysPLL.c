/*...........................................................................*/
/*.                  File Name : SYSPLL.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysPLL.h"

#include <string.h>

//=============================================================================
static xSYS_CLOCK *G_pSysCLKP = (xSYS_CLOCK *)CLKPWR_PHSY_BASE_ADDR;
//=============================================================================

void  SysInitPLLs(void)
{
#if defined(__POLLUX__)
      SysSetPLLCPU0();
      SysSetPLLCPU1();
      SysSetPLLSdRamDIV();
      SysSetPLLBCLK();
      SysSetPLL1PowerDownMode(0);       // PLL1 power on
      SysSetPLL2PowerDownMode(0);       // PLL2 power on
      SysSetPLL0DIV(CPU_PLL0_PDIV,CPU_PLL0_MDIV,CPU_PLL0_SDIV);
      SysSetPLL1DIV(CPU_PLL1_PDIV,CPU_PLL1_MDIV,CPU_PLL1_SDIV);

      SysDoPLLChange();
      while (SysIsPLLStable());

      SysDelayLoop(0x02FFFFF);
#else                           // SPICA
  #if (ARM_FCLK != 132000000)
      SysSetPLLBCLK();
      SysSetPLLCPU0();
      SysSetPLLCPU1();

      SysDoPLLChange();
      while (SysIsPLLStable());

      SysDelayLoop(0x02FFFFF);
  #endif
#endif
}
void  SysDoPLLChange(void)
{
#if defined(__POLLUX__)
      G_pSysCLKP->dPWRMODE |= (1 << 15);          // CHGPLL=1
#else                           // SPICA
      G_pSysCLKP->dPWRMODE |= (1 << 15);          // CHGPLL=1
#endif
}
DWORD SysIsPLLStable(void)
{
#if defined(__POLLUX__)
      return(G_pSysCLKP->dPWRMODE & (1 << 15));
#else                           // SPICA
      return(G_pSysCLKP->dPWRMODE & (1 << 15));
#endif
}
void  SysSetPLLCPU0(void)
{
#if defined(__POLLUX__)
      DWORD dTempX;

      dTempX = G_pSysCLKP->dCLKMODEREG & ~(0x3ff);
      dTempX = dTempX | ((4 - 1) <<  6)  |      // CLKDIV2CPU0=ARM926=PLL0/4 (132 MHz)
                        (     0  <<  4)  |      // CLKSELCPU0=PLL0  (0=PLL0,1=PLL1,2=PLL2,3=None)
                        ((1 - 1) <<  0);        // CLKDIVCPU0=ARM926=PLL0/1  (528 MHz)

      G_pSysCLKP->dCLKMODEREG = dTempX;
#else                           // SPICA
      DWORD dTempX;

      dTempX = ((CPU_PLL0_PDIV) << 18) |       // PDIV
               ((CPU_PLL0_MDIV) <<  8) |       // MDIV
               ((CPU_PLL0_SDIV) <<  0);        // SDIV

      G_pSysCLKP->dPLLSETREG[0] = dTempX;
#endif
}
void  SysSetPLLCPU1(void)
{
#if defined(__SPICA__)
      DWORD dTempX;

      dTempX = ((CPU_PLL1_PDIV) << 18) |       // PDIV
               ((CPU_PLL1_MDIV) <<  8) |       // MDIV
               ((CPU_PLL1_SDIV) <<  0);        // SDIV

      G_pSysCLKP->dPLLSETREG[1] = dTempX;
#endif
}
void  SysSetPLLSdRamDIV(void)
{
#if defined(__SPICA__)
#endif
}
void  SysSetPLLBCLK(void)
{
      DWORD dTempX;

#if defined(__POLLUX__)
      dTempX = G_pSysCLKP->dCLKMODEREG;
      dTempX = dTempX & ~(0x3f << 20);
      dTempX = dTempX | (BCLK_SRC<< 24)  |      // CLKSELBCLK=PLL0
                        ((BCLK_DIV - 1) << 20); // CLKDIV1BCLK=PLL0/4        (132 MHz)

      G_pSysCLKP->dCLKMODEREG = dTempX;
#else                           // SPICA
      dTempX = ((HCLK_FACT - 1)       <<  8) |       // CLKDIV2CPU0
               ((PLL_CLKSELCPU0_PLL0) <<  4) |       // PLL0
               ((FCLK_FACT - 1)       <<  0);        // CLKDIV1CPU0

      G_pSysCLKP->dCLKMODEREG0 = dTempX;

      dTempX = ((PCLK_FACT - 1)       << 12) |       // CLKDIVPCLK
               ((BCLK_FACT - 1)       <<  8) |       // CLKDIVBCLK
               ((PLL_CLKSELMCLK_PLL0) <<  4) |       // PLL0
//             ((PLL_CLKSELMCLK_FCLK) <<  4) |       // FCLK
               ((MCLK_FACT - 1)       <<  0);        // CLKDIVMCLK

      G_pSysCLKP->dCLKMODEREG1 = dTempX;
#endif
}
void  SysSetPLL1PowerDownMode(int nDisableEnableMode)
{
      DWORD dTempX;

#if defined(__POLLUX__)
      dTempX = G_pSysCLKP->dCLKMODEREG;

      if (nDisableEnableMode)
          dTempX = dTempX |  (1 << 30);
      else
          dTempX = dTempX & ~(1 << 30);

      G_pSysCLKP->dCLKMODEREG = dTempX;
#else                           // SPICA
      dTempX = G_pSysCLKP->dCLKMODEREG0;

      if (nDisableEnableMode)
          dTempX = dTempX |  (1 << 30);
      else
          dTempX = dTempX & ~(1 << 30);

      G_pSysCLKP->dCLKMODEREG0 = dTempX;
#endif
}
void  SysSetPLL2PowerDownMode(int nDisableEnableMode)
{
#if defined(__SPICA__)
#endif
}
void  SysSetPLL0DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV)
{
#if defined(__POLLUX__)
      G_pSysCLKP->dPLLSETREG[0] = (dPDIV << 18) | (dMDIV << 8) | (dSDIV);
#else                           // SPICA
      G_pSysCLKP->dPLLSETREG[0] = (dPDIV << 18) | (dMDIV << 8) | (dSDIV);
#endif
}
void  SysSetPLL1DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV)
{
#if defined(__POLLUX__)
      G_pSysCLKP->dPLLSETREG[1] = (dPDIV << 18) | (dMDIV << 8) | (dSDIV);
#else                           // SPICA
      G_pSysCLKP->dPLLSETREG[1] = (dPDIV << 18) | (dMDIV << 8) | (dSDIV);
#endif
}
void  SysSetPLL2DIV(DWORD dPDIV,DWORD dMDIV,DWORD dSDIV)
{
#if defined(__SPICA__)
#endif
}

