/*##################################################################
  FILE    : TAGLIST.H

  USE     : Tag Values definition file.
  PROJECT : C-Map File System.

  AUTHOR  : SiS[030826].
  UPDATED :
  ##################################################################

  This header file contains all the available Tags with the following 
  info:

	  - ID
	  - Type
	  - Maximum value.
	  - Minimum value.
	  - Resolution (only for floating point type tags).

  ID:	
	It is a 2 bytes positive value that (range 0 - 0xFFFF) which 
	identify the tag. Tag IDs must be consecutive and starting 
	from 0. ID = 0 is reserved.
  
  Type: 
	It's the Tag data type, allowable types are the following:
	  - TAG_TYPE_UINT32: unsigned long int.
	  - TAG_TYPE_UINT16: unsigned short int.
	  - TAG_TYPE_UINT8:  unsigned char.	
	  - TAG_TYPE_SINT32: signed long int.		
	  - TAG_TYPE_SINT16: signed short int.			
	  - TAG_TYPE_SINT8:  signed char.
	  - TAG_TYPE_FLOAT:  float.

  Maximum value:
	The maxim valid value for the tag (which is not necessary the
	maximum value for the tag type. 

  Minimum vale:
	As above for the minimum.

  Resolution:
	This info is necessary only for float types tags. Due float 
	values ares stored as long int, the resolution represent the 
	value that the tag data must be divided by to get the original 
	float number.



  TAG DEFINITION EXAMPLE:
 
	#define tag_<TAG_NAME>		->	Tag ID.
	#define tag_<TAG_NAME>_TYPE	->	Tag type.		
	#define tag_<TAG_NAME>_MIN	->	Tag minimum value. 
	#define tag_<TAG_NAME>_MAX	->	Tag maximum value.
	#define tag_<TAG_NAME>_RES	->  Tag resolution (for float type tags).

*/


#ifndef __TAG_LIST__
	#define __TAG_LIST__


/************************************************************************
  #Include section.
 ************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include <CMapType.h>
#include <FS_Tags.h>




/************************************************************************
  Constants definition section.
 ************************************************************************/


/*
 * NULL tag, (reserved).
 */
#define tag_NULL						0x0000			
#define tag_NULL_TYPE					TAG_TYPE_UINT8
#define tag_NULL_MIN					0				
#define tag_NULL_MAX		 			100				
#define tag_NULL_RES					1				

/*
 * Engine Units Setting (0=Metric, 1=Imperial).
 */
#define tag_ENGINE_UNITS				0x0001					
#define tag_ENGINE_UNITS_TYPE			TAG_TYPE_UINT8			
#define tag_ENGINE_UNITS_MIN			0						
#define tag_ENGINE_UNITS_MAX			1						
#define tag_ENGINE_UNITS_RES			1						

/*
 * Engine Display Setting (0=Digital, 1=Bar Graph).
 */
#define tag_ENGINE_DISP					0x0002					
#define tag_ENGINE_DISP_TYPE			TAG_TYPE_UINT8			
#define tag_ENGINE_DISP_MIN				0						
#define tag_ENGINE_DISP_MAX		 		1						
#define tag_ENGINE_DISP_RES				1						


/*
 * Twin Engine Setting (0=No, 1=Yes).
 */
#define tag_TWIN_ENGINE					0x0003					
#define tag_TWIN_ENGINE_TYPE			TAG_TYPE_UINT8			
#define tag_TWIN_ENGINE_MIN				0						
#define tag_TWIN_ENGINE_MAX		 		1						
#define tag_TWIN_ENGINE_RES				1						

/*
 * Twin Engine Setting (0=Port Engine, 1=Starboard Engine).
 * 
 * This tag is used to specify that all the successive 
 * engine parameters related tags are relative to the 
 * engine specified into the data field of the tag_CURR_ENGINE
 * tag.
 */
#define tag_CURR_ENGINE					0x0004					
#define tag_CURR_ENGINE_TYPE			TAG_TYPE_UINT8			
#define tag_CURR_ENGINE_MIN				0						
#define tag_CURR_ENGINE_MAX		 		1						
#define tag_CURR_ENGINE_RES				1						

/*
 * Engine Speed Display Setting (0=Off, 1=On).
 */
#define tag_ENGINE_SPD					0x0005					
#define tag_ENGINE_SPD_TYPE				TAG_TYPE_UINT8			
#define tag_ENGINE_SPD_MIN				0						
#define tag_ENGINE_SPD_MAX		 		1						
#define tag_ENGINE_SPD_RES				1						

/*
 * Percent Load Display Setting (0=Off, 1=On).
 */
#define tag_PERC_LOAD					0x0006					
#define tag_PERC_LOAD_TYPE				TAG_TYPE_UINT8			
#define tag_PERC_LOAD_MIN				0						
#define tag_PERC_LOAD_MAX		 		1						
#define tag_PERC_LOAD_RES				1						

/*
 * Total Engine Hours Display Setting (0=Off, 1=On).
 */
#define tag_ENG_HOURS					0x0007					
#define tag_ENG_HOURS_TYPE				TAG_TYPE_UINT8			
#define tag_ENG_HOURS_MIN				0						
#define tag_ENG_HOURS_MAX		 		1						
#define tag_ENG_HOURS_RES				1						

/*
 * Engine Coolant Temperature Display Setting (0=Off, 1=On).
 */
#define tag_COOLANT_TEMP				0x0008					
#define tag_COOLANT_TEMP_TYPE			TAG_TYPE_UINT8			
#define tag_COOLANT_TEMP_MIN			0						
#define tag_COOLANT_TEMP_MAX			1						
#define tag_COOLANT_TEMP_RES			1						

/*
 * Fuel Temperature Display Setting (0=Off, 1=On).
 */
#define tag_FUEL_TEMP					0x0009					
#define tag_FUEL_TEMP_TYPE				TAG_TYPE_UINT8			
#define tag_FUEL_TEMP_MIN				0						
#define tag_FUEL_TEMP_MAX				1						
#define tag_FUEL_TEMP_RES				1						

/*
 * Engine Oil Temperature Display Setting (0=Off, 1=On).
 */
#define tag_OIL_TEMP					0x000A					
#define tag_OIL_TEMP_TYPE				TAG_TYPE_UINT8			
#define tag_OIL_TEMP_MIN				0						
#define tag_OIL_TEMP_MAX				1						
#define tag_OIL_TEMP_RES				1						

/*
 * Fuel Delivery Pressure Display Setting (0=Off, 1=On).
 */
#define tag_FUEL_PRESS					0x000B					
#define tag_FUEL_PRESS_TYPE				TAG_TYPE_UINT8			
#define tag_FUEL_PRESS_MIN				0						
#define tag_FUEL_PRESS_MAX				1						
#define tag_FUEL_PRESS_RES				1			

/*
 * Engine Oil Pressure Display Setting (0=Off, 1=On).
 */
#define tag_OIL_PRESS					0x000C					
#define tag_OIL_PRESS_TYPE				TAG_TYPE_UINT8			
#define tag_OIL_PRESS_MIN				0						
#define tag_OIL_PRESS_MAX				1						
#define tag_OIL_PRESS_RES				1			

/*
 * Fuel Rate Display Setting (0=Off, 1=On).
 */
#define tag_FUEL_RATE					0x000D					
#define tag_FUEL_RATE_TYPE				TAG_TYPE_UINT8			
#define tag_FUEL_RATE_MIN				0						
#define tag_FUEL_RATE_MAX				1						
#define tag_FUEL_RATE_RES				1			

/*
 * Boost Pressure Display Setting (0=Off, 1=On).
 */
#define tag_BOOST_PRESS					0x000E					
#define tag_BOOST_PRESS_TYPE			TAG_TYPE_UINT8			
#define tag_BOOST_PRESS_MIN				0						
#define tag_BOOST_PRESS_MAX				1						
#define tag_BOOST_PRESS_RES				1			

/*
 * Intake Manifold Temperature Display Setting (0=Off, 1=On).
 */
#define tag_MANIF_TEMP					0x000F					
#define tag_MANIF_TEMP_TYPE				TAG_TYPE_UINT8			
#define tag_MANIF_TEMP_MIN				0						
#define tag_MANIF_TEMP_MAX				1						
#define tag_MANIF_TEMP_RES				1			

/*
 * Exhaust Gas Temperature Display Setting (0=Off, 1=On).
 */
#define tag_GAS_TEMP					0x0010					
#define tag_GAS_TEMP_TYPE				TAG_TYPE_UINT8			
#define tag_GAS_TEMP_MIN				0						
#define tag_GAS_TEMP_MAX				1						
#define tag_GAS_TEMP_RES				1			

/*
 * Transmission Oil Pressure Display Setting (0=Off, 1=On).
 */
#define tag_TRANS_OIL_PRESS				0x0011					
#define tag_TRANS_OIL_PRESS_TYPE		TAG_TYPE_UINT8			
#define tag_TRANS_OIL_PRESS_MIN			0						
#define tag_TRANS_OIL_PRESS_MAX			1						
#define tag_TRANS_OIL_PRESS_RES			1			

/*
 * Transmission Oil Temperature Display Setting (0=Off, 1=On).
 */
#define tag_TRANS_OIL_TEMP				0x0012					
#define tag_TRANS_OIL_TEMP_TYPE			TAG_TYPE_UINT8			
#define tag_TRANS_OIL_TEMP_MIN			0						
#define tag_TRANS_OIL_TEMP_MAX			1						
#define tag_TRANS_OIL_TEMP_RES			1			

/*
 * Fuel Level Display Setting (0=Off, 1=On).
 */
#define tag_FUEL_LEVEL					0x0013					
#define tag_FUEL_LEVEL_TYPE				TAG_TYPE_UINT8			
#define tag_FUEL_LEVEL_MIN				0						
#define tag_FUEL_LEVEL_MAX				1						
#define tag_FUEL_LEVEL_RES				1			
	
/*
 * Turbocharger 1 Turbine Inlet Temperature Display Setting (0=Off, 1=On).
 */
#define tag_TURBINE1_TEMP				0x0014					
#define tag_TURBINE1_TEMP_TYPE			TAG_TYPE_UINT8			
#define tag_TURBINE1_TEMP_MIN			0						
#define tag_TURBINE1_TEMP_MAX			1						
#define tag_TURBINE1_TEMP_RES			1			

/*
 * Turbocharger 2 Turbine Inlet Temperature Display Setting (0=Off, 1=On).
 */
#define tag_TURBINE2_TEMP				0x0015					
#define tag_TURBINE2_TEMP_TYPE			TAG_TYPE_UINT8			
#define tag_TURBINE2_TEMP_MIN			0						
#define tag_TURBINE2_TEMP_MAX			1						
#define tag_TURBINE2_TEMP_RES			1		

/*
 * Electrical Potential (Voltage) Display Setting (0=Off, 1=On).
 */
#define tag_ELECTR_POT					0x0016					
#define tag_ELECTR_POT_TYPE				TAG_TYPE_UINT8			
#define tag_ELECTR_POT_MIN				0						
#define tag_ELECTR_POT_MAX				1						
#define tag_ELECTR_POT_RES				1		

/*
 * Battery Potential (Voltage), Switched Display Setting (0=Off, 1=On).
 */
#define tag_BATTERY_POT					0x0017					
#define tag_BATTERY_POT_TYPE			TAG_TYPE_UINT8			
#define tag_BATTERY_POT_MIN				0						
#define tag_BATTERY_POT_MAX				1						
#define tag_BATTERY_POT_RES				1		

/*
 * Battery 2 Potential (Voltage) Display Setting (0=Off, 1=On).
 */
#define tag_BATTERY2_POT				0x0018					
#define tag_BATTERY2_POT_TYPE			TAG_TYPE_UINT8			
#define tag_BATTERY2_POT_MIN			0						
#define tag_BATTERY2_POT_MAX			1						
#define tag_BATTERY2_POT_RES			1		

/*
 * Tachometer Alarm level Setting (0 to 65535 rpm).
 */
#define tag_TACHOMETER_ALEVL			0x0019					
#define tag_TACHOMETER_ALEVL_TYPE		TAG_TYPE_UINT16			
#define tag_TACHOMETER_ALEVL_MIN		0						
#define tag_TACHOMETER_ALEVL_MAX		0xffff						
#define tag_TACHOMETER_ALEVL_RES		1		

/*
 * Coolant Temperature Alarm level Setting (0 to 256 C).
 */
#define tag_COOLANT_TEMP_ALEVL			0x001A					
#define tag_COOLANT_TEMP_ALEVL_TYPE		TAG_TYPE_UINT8			
#define tag_COOLANT_TEMP_ALEVL_MIN		0						
#define tag_COOLANT_TEMP_ALEVL_MAX		0xff						
#define tag_COOLANT_TEMP_ALEVL_RES		1		

/*
 * Engine Oil Temperature Alarm level Setting (0 to 256 C).
 */
#define tag_ENG_OIL_TEMP_ALEVL			0x001B					
#define tag_ENG_OIL_TEMP_ALEVL_TYPE		TAG_TYPE_UINT8			
#define tag_ENG_OIL_TEMP_ALEVL_MIN		0						
#define tag_ENG_OIL_TEMP_ALEVL_MAX		0xff						
#define tag_ENG_OIL_TEMP_ALEVL_RES		1		

/*
 * Engine Oil Pressure Alarm level Setting (0.0 to 100.0 bar).
 */
#define tag_ENG_OIL_ALEVL				0x001C					
#define tag_ENG_OIL_ALEVL_TYPE			TAG_TYPE_FLOAT			
#define tag_ENG_OIL_ALEVL_MIN			0						
#define tag_ENG_OIL_ALEVL_MAX			1000						
#define tag_ENG_OIL_ALEVL_RES			10		

/*
 * Exhaust Gas Temperature Alarm level Setting (0 to 1000 C).
 */
#define tag_GAST_TEMP_ALEVL				0x001D					
#define tag_GAST_TEMP_ALEVL_TYPE		TAG_TYPE_UINT16			
#define tag_GAST_TEMP_ALEVL_MIN			0						
#define tag_GAST_TEMP_ALEVL_MAX			1000						
#define tag_GAST_TEMP_ALEVL_RES			1		

/*
 * Boost Pressure Alarm level Setting (0.0 to 100.0 bar).
 */
#define tag_BOOST_PRESS_ALEVL			0x001E					
#define tag_BOOST_PRESS_ALEVL_TYPE		TAG_TYPE_FLOAT			
#define tag_BOOST_PRESS_ALEVL_MIN		0						
#define tag_BOOST_PRESS_ALEVL_MAX		100						
#define tag_BOOST_PRESS_ALEVL_RES		10		

/*
 * Transmission Oil Temperature Alarm level Setting (0 to 256 C).
 */
#define tag_TRANS_OIL_TEMP_ALEVL		0x001F					
#define tag_TRANS_OIL_TEMP_ALEVL_TYPE	TAG_TYPE_UINT8			
#define tag_TRANS_OIL_TEMP_ALEVL_MIN	0						
#define tag_TRANS_OIL_TEMP_ALEVL_MAX	0xFF						
#define tag_TRANS_OIL_TEMP_ALEVL_RES	1		

/*
 * Transmission Oil Pressure Alarm level Setting (0.0 to 50.0 bar).
 */
#define tag_TRANS_OIL_PRESS_ALEVL		0x0020					
#define tag_TRANS_OIL_PRESS_ALEVL_TYPE	TAG_TYPE_FLOAT			
#define tag_TRANS_OIL_PRESS_ALEVL_MIN	0						
#define tag_TRANS_OIL_PRESS_ALEVL_MAX	500						
#define tag_TRANS_OIL_PRESS_ALEVL_RES	10		

/*
 * Fuel Tank Alarm level Setting (0% to 100%).
 */
#define tag_FUEL_TANK_ALEVL				0x0021					
#define tag_FUEL_TANK_ALEVL_TYPE		TAG_TYPE_UINT8			
#define tag_FUEL_TANK_ALEVL_MIN			0						
#define tag_FUEL_TANK_ALEVL_MAX			100						
#define tag_FUEL_TANK_ALEVL_RES			1		

/*
 * Voltmeter High Alarm level Setting (0 to 100V).
 */
#define tag_VOLT_HI_ALEVL				0x0022					
#define tag_VOLT_HI_ALEVL_TYPE			TAG_TYPE_UINT8			
#define tag_VOLT_HI_ALEVL_MIN			0						
#define tag_VOLT_HI_ALEVL_MAX			100						
#define tag_VOLT_HI_ALEVL_RES			1		

/*
 * Voltmeter Low Alarm level Setting (0 to 100V).
 */
#define tag_VOLT_LO_ALEVL				0x0023					
#define tag_VOLT_LO_ALEVL_TYPE			TAG_TYPE_UINT8			
#define tag_VOLT_LO_ALEVL_MIN			0						
#define tag_VOLT_LO_ALEVL_MAX			100						
#define tag_VOLT_LO_ALEVL_RES			1		

/*
 * Turbo Inlet Temperature Alarm level Setting (0 to 1000C).
 */
#define tag_TURBO_TEMP_ALEVL			0x0024					
#define tag_TURBO_TEMP_ALEVL_TYPE		TAG_TYPE_UINT16			
#define tag_TURBO_TEMP_ALEVL_MIN		0						
#define tag_TURBO_TEMP_ALEVL_MAX		1000						
#define tag_TURBO_TEMP_ALEVL_RES		1		

/*
 * Digital Engine Page Setting (On/Off). 	
 */
#define tag_DIG_ENG_PAGE				0x0025					
#define tag_DIG_ENG_PAGE_TYPE			TAG_TYPE_UINT8			
#define tag_DIG_ENG_PAGE_MIN			0						
#define tag_DIG_ENG_PAGE_MAX			1						
#define tag_DIG_ENG_PAGE_RES			1		

/*
 * Radar heading line.
 */
#define tag_RAD_HEADING_LINE			0x0026
#define tag_RAD_HEADING_LINE_TYPE		TAG_TYPE_SINT32
#define tag_RAD_HEADING_LINE_MIN		0x80000000L
#define tag_RAD_HEADING_LINE_MAX		0x7FFFFFFFL
#define tag_RAD_HEADING_LINE_RES		1

/*
 * Radar Antenna parking Position.	
 */
#define tag_RAD_PARKING_POS				0x0027
#define tag_RAD_PARKING_POS_TYPE		TAG_TYPE_SINT32
#define tag_RAD_PARKING_POS_MIN			0x80000000L
#define tag_RAD_PARKING_POS_MAX			0x7FFFFFFFL
#define tag_RAD_PARKING_POS_RES			1

/*
 * Radar Transmission off Start angle.	
 */
#define tag_RAD_TXOFF_START_ANGLE		0x0028
#define tag_RAD_TXOFF_START_ANGLE_TYPE	TAG_TYPE_SINT32
#define tag_RAD_TXOFF_START_ANGLE_MIN	0x80000000L
#define tag_RAD_TXOFF_START_ANGLE_MAX	0x7FFFFFFFL
#define tag_RAD_TXOFF_START_ANGLE_RES	1

/*
 * Radar Transmission off End angle.	
 */
#define tag_RAD_TXOFF_END_ANGLE			0x0029
#define tag_RAD_TXOFF_END_ANGLE_TYPE	TAG_TYPE_SINT32
#define tag_RAD_TXOFF_END_ANGLE_MIN		0x80000000L
#define tag_RAD_TXOFF_END_ANGLE_MAX		0x7FFFFFFFL
#define tag_RAD_TXOFF_END_ANGLE_RES		1

/*
 * Radar Transmission Trigger Delay.	
 */
#define tag_RAD_TX_TRIGGER_DELAY		0x002A
#define tag_RAD_TX_TRIGGER_DELAY_TYPE	TAG_TYPE_SINT32
#define tag_RAD_TX_TRIGGER_DELAY_MIN	0x80000000L
#define tag_RAD_TX_TRIGGER_DELAY_MAX	0x7FFFFFFFL
#define tag_RAD_TX_TRIGGER_DELAY_RES	1

/*
 * Radar Coarse Tune.
 */
#define tag_RAD_COARSE_TUNE				0x002B
#define tag_RAD_COARSE_TUNE_TYPE		TAG_TYPE_SINT32
#define tag_RAD_COARSE_TUNE_MIN			0x80000000L
#define tag_RAD_COARSE_TUNE_MAX			0x7FFFFFFFL
#define tag_RAD_COARSE_TUNE_RES			1

/*
 * Radar Fine Tune.
 */
#define tag_RAD_FINE_TUNE				0x002C
#define tag_RAD_FINE_TUNE_TYPE			TAG_TYPE_SINT32
#define tag_RAD_FINE_TUNE_MIN			0x80000000L
#define tag_RAD_FINE_TUNE_MAX			0x7FFFFFFFL
#define tag_RAD_FINE_TUNE_RES			1

/*
 * Fish Finder Chart Speed.
 */
#define tag_FF_CHART_SPD				0x002D
#define tag_FF_CHART_SPD_TYPE			TAG_TYPE_UINT16
#define tag_FF_CHART_SPD_MIN			0
#define tag_FF_CHART_SPD_MAX			0xFFFF
#define tag_FF_CHART_SPD_RES			1

/*
 * Fish Finder Palette
 */
#define tag_FF_PALETTE					0x002E
#define tag_FF_PALETTE_TYPE				TAG_TYPE_UINT8
#define tag_FF_PALETTE_MIN				0
#define tag_FF_PALETTE_MAX				0xFF
#define tag_FF_PALETTE_RES				1

/*
 * Fish Targets identification ON/OFF.
 */
#define tag_FF_TARGETS					0x002F
#define tag_FF_TARGETS_TYPE				TAG_TYPE_UINT8
#define tag_FF_TARGETS_MIN				0
#define tag_FF_TARGETS_MAX				1
#define tag_FF_TARGETS_RES				1

/*
 * Fish Targets simbols display ON/OFF.
 */
#define tag_FF_TGT_SYMBOLS				0x0030
#define tag_FF_TGT_SYMBOLS_TYPE			TAG_TYPE_UINT8
#define tag_FF_TGT_SYMBOLS_MIN			0
#define tag_FF_TGT_SYMBOLS_MAX			1
#define tag_FF_TGT_SYMBOLS_RES			1

/*
 * Fish Targets depth display ON/OFF.
 */
#define tag_FF_TGT_DEPTH				0x0031
#define tag_FF_TGT_DEPTH_TYPE			TAG_TYPE_UINT8
#define tag_FF_TGT_DEPTH_MIN			0
#define tag_FF_TGT_DEPTH_MAX			1
#define tag_FF_TGT_DEPTH_RES			1

/*
 * Fish Targets depth decimal digits display ON/OFF.
 */
#define tag_FF_TGT_DEPTH_DEC			0x0032
#define tag_FF_TGT_DEPTH_DEC_TYPE		TAG_TYPE_UINT8
#define tag_FF_TGT_DEPTH_DEC_MIN		0
#define tag_FF_TGT_DEPTH_DEC_MAX		1
#define tag_FF_TGT_DEPTH_DEC_RES		1

/*
 * Fish Targets echo arches display ON/OFF.
 */
#define tag_FF_TGT_ECHO					0x0033
#define tag_FF_TGT_ECHO_TYPE			TAG_TYPE_UINT8
#define tag_FF_TGT_ECHO_MIN				0
#define tag_FF_TGT_ECHO_MAX				1
#define tag_FF_TGT_ECHO_RES				1

/*
 * Fish Finder preset mode (FISH, CRUISE...)
 */
#define tag_FF_PRESET_MODE				0x0034
#define tag_FF_PRESET_MODE_TYPE			TAG_TYPE_UINT8
#define tag_FF_PRESET_MODE_MIN			0
#define tag_FF_PRESET_MODE_MAX			0xFF
#define tag_FF_PRESET_MODE_RES			1

/*
 * Fish Finder Range Mode (AUTO, MANUAL...)
 */
#define tag_FF_RANGE_MODE				0x0035
#define tag_FF_RANGE_MODE_TYPE			TAG_TYPE_UINT8
#define tag_FF_RANGE_MODE_MIN			0
#define tag_FF_RANGE_MODE_MAX			0xFF
#define tag_FF_RANGE_MODE_RES			1

/*
 * Fish Finder Frequency.
 */
#define tag_FF_FREQUENCY				0x0036
#define tag_FF_FREQUENCY_TYPE			TAG_TYPE_UINT8
#define tag_FF_FREQUENCY_MIN			0
#define tag_FF_FREQUENCY_MAX			0xFF
#define tag_FF_FREQUENCY_RES			1

/*
 * Fish Finder Zoom ratio.
 */
#define tag_FF_ZOOM						0x0037
#define tag_FF_ZOOM_TYPE				TAG_TYPE_UINT8
#define tag_FF_ZOOM_MIN					0
#define tag_FF_ZOOM_MAX					0xFF
#define tag_FF_ZOOM_RES					1

/*
 * Fish Finder STC level.
 */
#define tag_FF_STC						0x0038
#define tag_FF_STC_TYPE					TAG_TYPE_UINT8
#define tag_FF_STC_MIN					0
#define tag_FF_STC_MAX					0xFF
#define tag_FF_STC_RES					1

/*
 * Fish Finder surface declutter level.
 */
#define tag_FF_SD						0x0039
#define tag_FF_SD_TYPE					TAG_TYPE_UINT8
#define tag_FF_SD_MIN					0
#define tag_FF_SD_MAX					0xFF
#define tag_FF_SD_RES					1

/*
 * Fish Finder Interference rejection ON/OFF.
 */
#define tag_FF_INT_REJECT				0x003A
#define tag_FF_INT_REJECT_TYPE			TAG_TYPE_UINT8
#define tag_FF_INT_REJECT_MIN			0
#define tag_FF_INT_REJECT_MAX			0xFF
#define tag_FF_INT_REJECT_RES			1

/*
 * Fish Finder working mode
 */
#define tag_FF_MODE						0x003B
#define tag_FF_MODE_TYPE				TAG_TYPE_UINT8
#define tag_FF_MODE_MIN					0
#define tag_FF_MODE_MAX					0xFF
#define tag_FF_MODE_RES					1

/*
 * Fish Finder depth range (in tenth of depth base unit).
 */
#define tag_FF_RANGE					0x003C
#define tag_FF_RANGE_TYPE				TAG_TYPE_UINT32
#define tag_FF_RANGE_MIN				0
#define tag_FF_RANGE_MAX				0xFFFFFFFF
#define tag_FF_RANGE_RES				1

/*
 * Fish Finder shift from surface (in tenth of depth base unit).
 */
#define tag_FF_SHIFT					0x003D
#define tag_FF_SHIFT_TYPE				TAG_TYPE_UINT32
#define tag_FF_SHIFT_MIN				0
#define tag_FF_SHIFT_MAX				0xFFFFFFFF
#define tag_FF_SHIFT_RES				1

/*
 * Fish Finder gain (0-100%).
 */
#define tag_FF_GAIN						0x003E
#define tag_FF_GAIN_TYPE				TAG_TYPE_UINT8
#define tag_FF_GAIN_MIN					0
#define tag_FF_GAIN_MAX					0xFF
#define tag_FF_GAIN_RES					1

/*
 * Fish Finder gain mode.
 */
#define tag_FF_GAIN_MODE				0x003F
#define tag_FF_GAIN_MODE_TYPE			TAG_TYPE_UINT8
#define tag_FF_GAIN_MODE_MIN			0
#define tag_FF_GAIN_MODE_MAX			0xFF
#define tag_FF_GAIN_MODE_RES			1

/*
 * Fish Finder gain mode.
 */
#define tag_FF_GAIN_OFFS				0x0040
#define tag_FF_GAIN_OFFS_TYPE			TAG_TYPE_SINT16
#define tag_FF_GAIN_OFFS_MIN			-100
#define tag_FF_GAIN_OFFS_MAX			100
#define tag_FF_GAIN_OFFS_RES			1

/*
 * Keel offset (draft, in tenth of depth base unit).
 */
#define tag_FF_KEEL_OFFS				0x0041
#define tag_FF_KEEL_OFFS_TYPE			TAG_TYPE_SINT16
#define tag_FF_KEEL_OFFS_MIN			-400
#define tag_FF_KEEL_OFFS_MAX			400
#define tag_FF_KEEL_OFFS_RES			1

/*
 * Sound Speed calibration ([90-110%] * 10).
 */
#define tag_FF_SSC						0x0042
#define tag_FF_SSC_TYPE					TAG_TYPE_UINT16
#define tag_FF_SSC_MIN					9000
#define tag_FF_SSC_MAX					11000
#define tag_FF_SSC_RES					1

/*
 * Water Speed calibration.
 */
#define tag_FF_WSC						0x0043
#define tag_FF_WSC_TYPE					TAG_TYPE_UINT8
#define tag_FF_WSC_MIN					1
#define tag_FF_WSC_MAX					255
#define tag_FF_WSC_RES					1

/*
 * Water Temperature calibration ([-10,+10] Celsius * 100).
 */
#define tag_FF_WTC						0x0044
#define tag_FF_WTC_TYPE					TAG_TYPE_SINT16
#define tag_FF_WTC_MIN					-1000
#define tag_FF_WTC_MAX					1000
#define tag_FF_WTC_RES					1

/*
 * Auxiliary Water Temperature calibration ([-10,+10] Celsius * 100).
 */
#define tag_FF_AWTC						0x0045
#define tag_FF_AWTC_TYPE				TAG_TYPE_SINT16
#define tag_FF_AWTC_MIN					-1000
#define tag_FF_AWTC_MAX					1000
#define tag_FF_AWTC_RES					1

/*
 * White line mode (ON/OFF).
 */
#define tag_FF_WHITE_LINE				0x0046
#define tag_FF_WHITE_LINE_TYPE			TAG_TYPE_UINT8
#define tag_FF_WHITE_LINE_MIN			0
#define tag_FF_WHITE_LINE_MAX			1
#define tag_FF_WHITE_LINE_RES			1

/*
 * Fish Finder auto shift range (in tenth of depth base unit).
 */
#define tag_FF_AUTO_SHIFT				0x0047
#define tag_FF_AUTO_SHIFT_TYPE			TAG_TYPE_UINT32
#define tag_FF_AUTO_SHIFT_MIN			0
#define tag_FF_AUTO_SHIFT_MAX			0xFFFFFFFF
#define tag_FF_AUTO_SHIFT_RES			1

/*
 * Fish Finder depth range for 50kHz (in tenth of depth base unit).
 */
#define tag_FF_RANGE_50K				0x0048
#define tag_FF_RANGE_50K_TYPE			TAG_TYPE_UINT32
#define tag_FF_RANGE_50K_MIN			0
#define tag_FF_RANGE_50K_MAX			0xFFFFFFFF
#define tag_FF_RANGE_50K_RES			1

/*
 * Fish Finder shift from surface for 50kHz (in tenth of depth base unit).
 */
#define tag_FF_SHIFT_50K				0x0049
#define tag_FF_SHIFT_50K_TYPE			TAG_TYPE_UINT32
#define tag_FF_SHIFT_50K_MIN			0
#define tag_FF_SHIFT_50K_MAX			0xFFFFFFFF
#define tag_FF_SHIFT_50K_RES			1

/*
 * Fish Finder gain for 50kHz (0-100%).
 */
#define tag_FF_GAIN_50K					0x004A
#define tag_FF_GAIN_50K_TYPE			TAG_TYPE_UINT8
#define tag_FF_GAIN_50K_MIN				0
#define tag_FF_GAIN_50K_MAX				0xFF
#define tag_FF_GAIN_50K_RES				1

/*
 * Fish Finder gain offset for 50kHz.
 */
#define tag_FF_GAIN_OFFS_50K			0x004B
#define tag_FF_GAIN_OFFS_50K_TYPE		TAG_TYPE_SINT16
#define tag_FF_GAIN_OFFS_50K_MIN		-100
#define tag_FF_GAIN_OFFS_50K_MAX		100
#define tag_FF_GAIN_OFFS_50K_RES		1

/*
 * Fish Finder water temperature source (Internal/Auxiliary).
 */
#define tag_FF_WATER_TEMP_SRC			0x004C
#define tag_FF_WATER_TEMP_SRC_TYPE		TAG_TYPE_UINT8
#define tag_FF_WATER_TEMP_SRC_MIN		0
#define tag_FF_WATER_TEMP_SRC_MAX		0xFF
#define tag_FF_WATER_TEMP_SRC_RES		1

/*
 * Fish Finder STC level for 50kHz.
 */
#define tag_FF_STC_50K					0x004D
#define tag_FF_STC_50K_TYPE				TAG_TYPE_UINT8
#define tag_FF_STC_50K_MIN				0
#define tag_FF_STC_50K_MAX				0xFF
#define tag_FF_STC_50K_RES				1

/*
 * Fish Finder surface declutter level for 50kHz.
 */
#define tag_FF_SD_50K					0x004E
#define tag_FF_SD_50K_TYPE				TAG_TYPE_UINT8
#define tag_FF_SD_50K_MIN				0
#define tag_FF_SD_50K_MAX				0xFF
#define tag_FF_SD_50K_RES				1

/*
 * Fish Finder Interference rejection ON/OFF (for 50kHz).
 */
#define tag_FF_INT_REJECT_50K			0x004F
#define tag_FF_INT_REJECT_50K_TYPE		TAG_TYPE_UINT8
#define tag_FF_INT_REJECT_50K_MIN		0
#define tag_FF_INT_REJECT_50K_MAX		0xFF
#define tag_FF_INT_REJECT_50K_RES		1

/*
 * Keel offset for 50kHz (draft, in tenth of depth base unit).
 */
#define tag_FF_KEEL_OFFS_50K			0x0050
#define tag_FF_KEEL_OFFS_50K_TYPE		TAG_TYPE_SINT16
#define tag_FF_KEEL_OFFS_50K_MIN		-400
#define tag_FF_KEEL_OFFS_50K_MAX		400
#define tag_FF_KEEL_OFFS_50K_RES		1

/*
 * Sound Speed calibration for 50kHz ([90-110%] * 10).
 */
#define tag_FF_SSC_50K					0x0051
#define tag_FF_SSC_50K_TYPE				TAG_TYPE_UINT16
#define tag_FF_SSC_50K_MIN				9000
#define tag_FF_SSC_50K_MAX				11000
#define tag_FF_SSC_50K_RES				1

/*
 * Fish Finder auto shift range (in tenth of depth base unit).
 */
#define tag_FF_AUTO_SHIFT_50K			0x0052
#define tag_FF_AUTO_SHIFT_50K_TYPE		TAG_TYPE_UINT32
#define tag_FF_AUTO_SHIFT_50K_MIN		0
#define tag_FF_AUTO_SHIFT_50K_MAX		0xFFFFFFFF
#define tag_FF_AUTO_SHIFT_50K_RES		1

/*
 * Fish Targets alarm mode (ON/OFF)
 */
#define tag_FF_TGT_ALARM				0x0053
#define tag_FF_TGT_ALARM_TYPE			TAG_TYPE_UINT8
#define tag_FF_TGT_ALARM_MIN			0
#define tag_FF_TGT_ALARM_MAX			0xFF
#define tag_FF_TGT_ALARM_RES			1

/*
 * Fish Targets alarm treshold level (0-5).
 */
#define tag_FF_TGT_ALARM_LEVEL			0x0054
#define tag_FF_TGT_ALARM_LEVEL_TYPE		TAG_TYPE_UINT8
#define tag_FF_TGT_ALARM_LEVEL_MIN		0
#define tag_FF_TGT_ALARM_LEVEL_MAX		0xFF
#define tag_FF_TGT_ALARM_LEVEL_RES		1

/*
 * Custom STC Length.
 */
#define tag_FF_STC_LEN					0x0055
#define tag_FF_STC_LEN_TYPE				TAG_TYPE_UINT16
#define tag_FF_STC_LEN_MIN				1
#define tag_FF_STC_LEN_MAX				0xFFFF
#define tag_FF_STC_LEN_RES				1

/*
 * Custom STC Length 50k.
 */
#define tag_FF_STC_LEN_50K				0x0056
#define tag_FF_STC_LEN_50K_TYPE			TAG_TYPE_UINT16
#define tag_FF_STC_LEN_50K_MIN			1
#define tag_FF_STC_LEN_50K_MAX			0xFFFF
#define tag_FF_STC_LEN_50K_RES			1

/*
 * Custom STC Strength (0-100%).
 */
#define tag_FF_STC_STR					0x0057
#define tag_FF_STC_STR_TYPE				TAG_TYPE_UINT8
#define tag_FF_STC_STR_MIN				0
#define tag_FF_STC_STR_MAX				100
#define tag_FF_STC_STR_RES				1

/*
 * Custom STC Strength 50k (0-100%).
 */
#define tag_FF_STC_STR_50K				0x0058
#define tag_FF_STC_STR_50K_TYPE			TAG_TYPE_UINT8
#define tag_FF_STC_STR_50K_MIN			0
#define tag_FF_STC_STR_50K_MAX			100
#define tag_FF_STC_STR_50K_RES			1

/*
 * Deep Water alarm (deepth in tenth of base unit).
 */
#define tag_FF_ALARM_DEEP				0x0059
#define tag_FF_ALARM_DEEP_TYPE			TAG_TYPE_SINT32
#define tag_FF_ALARM_DEEP_MIN			0
#define tag_FF_ALARM_DEEP_MAX			1000000
#define tag_FF_ALARM_DEEP_RES			10

/*
 * Deep Water alarm status (On/Off).
 */
#define tag_FF_ALARM_DEEP_EN			0x005A
#define tag_FF_ALARM_DEEP_EN_TYPE		TAG_TYPE_UINT8
#define tag_FF_ALARM_DEEP_EN_MIN		0
#define tag_FF_ALARM_DEEP_EN_MAX		1
#define tag_FF_ALARM_DEEP_EN_RES		1

/*
 * Shallow Water alarm (deepth in tenth of base unit).
 */
#define tag_FF_ALARM_SHALLOW			0x005B
#define tag_FF_ALARM_SHALLOW_TYPE		TAG_TYPE_SINT32
#define tag_FF_ALARM_SHALLOW_MIN		0
#define tag_FF_ALARM_SHALLOW_MAX		1000000
#define tag_FF_ALARM_SHALLOW_RES		10

/*
 * Shallow Water alarm status (On/Off).
 */
#define tag_FF_ALARM_SHALLOW_EN			0x005C
#define tag_FF_ALARM_SHALLOW_EN_TYPE	TAG_TYPE_UINT8
#define tag_FF_ALARM_SHALLOW_EN_MIN		0
#define tag_FF_ALARM_SHALLOW_EN_MAX		1
#define tag_FF_ALARM_SHALLOW_EN_RES		1

/*
 * High Water temperature alarm (celsius degrees / 100).
 */
#define tag_FF_ALARM_TEMP_HIGH			0x005D
#define tag_FF_ALARM_TEMP_HIGH_TYPE		TAG_TYPE_SINT32
#define tag_FF_ALARM_TEMP_HIGH_MIN		-10000
#define tag_FF_ALARM_TEMP_HIGH_MAX		10000
#define tag_FF_ALARM_TEMP_HIGH_RES		100

/*
 * High Water temperature alarm status (On/Off).
 */
#define tag_FF_ALARM_TEMP_HIGH_EN		0x005E
#define tag_FF_ALARM_TEMP_HIGH_EN_TYPE	TAG_TYPE_UINT8
#define tag_FF_ALARM_TEMP_HIGH_EN_MIN	0
#define tag_FF_ALARM_TEMP_HIGH_EN_MAX	1
#define tag_FF_ALARM_TEMP_HIGH_EN_RES	1

/*
 * Low Water temperature alarm (celsius degrees / 100).
 */
#define tag_FF_ALARM_TEMP_LOW			0x005F
#define tag_FF_ALARM_TEMP_LOW_TYPE		TAG_TYPE_SINT32
#define tag_FF_ALARM_TEMP_LOW_MIN		-10000
#define tag_FF_ALARM_TEMP_LOW_MAX		10000
#define tag_FF_ALARM_TEMP_LOW_RES		100

/*
 * Low Water temperature alarm status (On/Off).
 */
#define tag_FF_ALARM_TEMP_LOW_EN		0x0060
#define tag_FF_ALARM_TEMP_LOW_EN_TYPE	TAG_TYPE_UINT8
#define tag_FF_ALARM_TEMP_LOW_EN_MIN	0
#define tag_FF_ALARM_TEMP_LOW_EN_MAX	1
#define tag_FF_ALARM_TEMP_LOW_EN_RES	1

/*
 * Water temperature rate alarm (celsius degrees / 100).
 */
#define tag_FF_ALARM_TEMP_RATE			0x0061
#define tag_FF_ALARM_TEMP_RATE_TYPE		TAG_TYPE_SINT32
#define tag_FF_ALARM_TEMP_RATE_MIN		-10000
#define tag_FF_ALARM_TEMP_RATE_MAX		10000
#define tag_FF_ALARM_TEMP_RATE_RES		100

/*
 * Water temperature rate alarm status (On/Off).
 */
#define tag_FF_ALARM_TEMP_RATE_EN		0x0062
#define tag_FF_ALARM_TEMP_RATE_EN_TYPE	TAG_TYPE_UINT8
#define tag_FF_ALARM_TEMP_RATE_EN_MIN	0
#define tag_FF_ALARM_TEMP_RATE_EN_MAX	1
#define tag_FF_ALARM_TEMP_RATE_EN_RES	1


//////////////////////////////////////////////////////////////////////////

/*
 * Structured Tags: File type.
 */
#define tag_ST_FILE_TYPE				0x0063
#define tag_ST_FILE_TYPE_TYPE			TAG_TYPE_UINT16
#define tag_ST_FILE_TYPE_MIN			0
#define tag_ST_FILE_TYPE_MAX			0xFFFF
#define tag_ST_FILE_TYPE_RES			1

/*
 * Structured Tags: File structure version
 */
#define tag_ST_FILE_VER					0x0064
#define tag_ST_FILE_VER_TYPE			TAG_TYPE_UINT16
#define tag_ST_FILE_VER_MIN				0
#define tag_ST_FILE_VER_MAX				0xFFFF
#define tag_ST_FILE_VER_RES				1

/*
 * Structured Tags: Preset Section start delimiter tag.
 */
#define tag_ST_SECTION_START			0x0065
#define tag_ST_SECTION_START_TYPE		TAG_TYPE_UINT16
#define tag_ST_SECTION_START_MIN		0
#define tag_ST_SECTION_START_MAX		0xFFFF
#define tag_ST_SECTION_START_RES		1

/*
 * Structured Tags: Preset Section end delimiter tag.
 */
#define tag_ST_SECTION_END				0x0066
#define tag_ST_SECTION_END_TYPE			TAG_TYPE_UINT16
#define tag_ST_SECTION_END_MIN			0
#define tag_ST_SECTION_END_MAX			0xFFFF
#define tag_ST_SECTION_END_RES			1


//////////////////////////////////////////////////////////////////////////


/*
 * Radar Fine Tune.
 */
#define tag_RAD_MBS						0x0067
#define tag_RAD_MBS_TYPE				TAG_TYPE_UINT8
#define tag_RAD_MBS_MIN					0x00
#define tag_RAD_MBS_MAX					0xFF
#define tag_RAD_MBS_RES					1


/*
 * Number of tags.
 */
#define NUM_OF_TAGS						( tag_RAD_MBS + 1 )




/************************************************************************
  END of Code.
 ************************************************************************/

#endif /* #ifndef __TAG_LIST__ */
