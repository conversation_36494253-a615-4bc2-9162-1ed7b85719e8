/*...........................................................................*/
/*.                  File Name : WayPntData.cpp                             .*/
/*.                                                                         .*/
/*.                       Date : 2008.09.23                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "datatype.h"
#if 0	// Temp
#include "datatype.hpp"
#endif
#include "CpuAddr.h"
#include "SysConst.h"
#include "KeyConst.h"
#include "AllConst.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "GrLib.h"
#include "SysLib.h"
#include "SysGPIO.h"

#include "KeyBD.hpp"
#include "DataBack.hpp"
#if 0	// Temp
#include "AlarmData.hpp"
#include "MainWin.hpp"
#endif

#ifdef  __SAMYUNG__
  #include "SamMapLib.h"
#endif

#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
  #include "SdCardDRV.h"
#endif

#include "WayPntData.hpp"

//===========================================================================
#ifdef  __cplusplus
extern "C" {
#endif
void  DrawFishDataInDraw(void);
#ifdef  __cplusplus
}
#endif
//===========================================================================

//===========================================================================
extern cKEYBD      *G_pKeyBD;
extern cDataBack   *G_pDataBack;
#if 0	// Temp
extern cMainWin    *G_pMainWin;
#endif
//===========================================================================

//===========================================================================
BACK16   cWayPntData::m_nWayPntMarkType  = WAYPNT_MARK_MIN;
BACK16   cWayPntData::m_nWayPntMarkColor = WAYPNT_COLOR_DFLT;
//---------------------------------------------------------------------------
BACK16   cWayPntData::m_nWayPntDspMode   = WPT_DSP_MODE_SHOW_ALL_I_N;

#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
     BACK16 cWayPntData::m_nPrevSaveSlotNo = 0;
#endif
//---------------------------------------------------------------------------
xWPTPOINT *cWayPntData::m_pWayPntData    = NULL;
//===========================================================================

cWayPntData::cWayPntData(void)
{
}
cWayPntData::~cWayPntData(void)
{
}
void  cWayPntData::GetMemory(void)
{
      m_pWayPntData = new xWPTPOINT[WAYPNT_POINT_SIZE];
      ClearAllData();
}

void  cWayPntData::MakeWayPntDataByVal(xWPTPOINT *pWptData,INT32 nGridLat,INT32 nGridLon,int nWptNo,UCHAR bMark,UCHAR bColor,UCHAR bDangerMode,UCHAR bDspMode)
{
      UCHAR vText[WAYPNT_TEXT_LEN * 2];

      strcpy((char *)vText,cWayPntData::FindUnUsedWayPntName(nWptNo,NULL));

      cWayPntData::MakeWayPntDataByVal(pWptData,nGridLat,nGridLon,vText,bMark,bColor,bDangerMode,bDspMode);
}
void  cWayPntData::MakeWayPntDataByVal(xWPTPOINT *pWptData,INT32 nGridLat,INT32 nGridLon,UCHAR *pText,UCHAR bMark,UCHAR bColor,UCHAR bDangerMode,UCHAR bDspMode)
{
#if 0	// Temp
      MakeWayPntDataNull(pWptData);

      pWptData->nGridLat    = nGridLat;
      pWptData->nGridLon    = nGridLon;
      memmove(pWptData->vText,pText,WAYPNT_TEXT_LEN);
      pWptData->bMark       = bMark;
      pWptData->bColor      = bColor;
      pWptData->bDangerMode = bDangerMode;
      pWptData->bDspMode    = bDspMode;
      pWptData->nMercLat    = GridLatToMerc(nGridLat);
      pWptData->nMercLon    = GridLonToMerc(nGridLon);
      pWptData->nAlarmStatus= ALARM_STATUS_NONE;

  #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
      pWptData->nMc32Y      = LongLatToMRC32(nGridLat);
      pWptData->nMc32X      = LongLonToMRC32(nGridLon);
  #endif
#endif  
}
void  cWayPntData::MakeWayPntDataByVal(xWPTPOINT *pWptData,INT32 nGridLat,INT32 nGridLon,UCHAR *pText)
{
#if 0	// Temp
      MakeWayPntDataNull(pWptData);

      pWptData->nGridLat    = nGridLat;
      pWptData->nGridLon    = nGridLon;
      memmove(pWptData->vText,pText,WAYPNT_TEXT_LEN);
      pWptData->bMark       = m_nWayPntMarkType;
      pWptData->bColor      = m_nWayPntMarkColor;
      pWptData->bDangerMode = MODE_VAL_OFF;
      pWptData->bDspMode    = WAYPNT_DSP_MODE_ALL;
      pWptData->nMercLat    = GridLatToMerc(nGridLat);
      pWptData->nMercLon    = GridLonToMerc(nGridLon);
      pWptData->nAlarmStatus= ALARM_STATUS_NONE;

  #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
      pWptData->nMc32Y      = LongLatToMRC32(nGridLat);
      pWptData->nMc32X      = LongLonToMRC32(nGridLon);
  #endif
#endif  
}

void  cWayPntData::MakeWayPntDataNull(xWPTPOINT *pWptData)
{
      pWptData->nGridLat    = GRID_LAT_VAL_UNKNOWN;
      pWptData->nGridLon    = GRID_LON_VAL_UNKNOWN;
      memset(pWptData->vText,' ',WAYPNT_TEXT_LEN);
      pWptData->bMark       = WAYPNT_MARK_DFLT;
      pWptData->bColor      = WAYPNT_COLOR_DFLT;
      pWptData->bDangerMode = MODE_VAL_OFF;
      pWptData->bDspMode    = WAYPNT_DSP_MODE_OFF;
      pWptData->wDepth      = DPT_VAL_UNKNOWN / 10;
      pWptData->wTemper     = TMP_VAL_UNKNOWN;
      pWptData->dDate       = WAYPNT_DATE_UNKNOWN;
      pWptData->dTime       = WAYPNT_DATE_UNKNOWN;
      pWptData->nMercLat    = 0;
      pWptData->nMercLon    = 0;

  #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
      pWptData->nMc32Y      = 0;
      pWptData->nMc32X      = 0;
  #endif
}
void  cWayPntData::MakeWayPntDataNull(int nIndex)
{
      MakeWayPntDataNull(m_pWayPntData + nIndex);
}
char *cWayPntData::MakeNormalWayPntName(int nWptNo,char *pHead)
{
      static char vName[WAYPNT_TEXT_LEN * 2];
      char  vText[32];

      memset(vName,' ',sizeof(vName) - 1);

      if (pHead == NULL)
      #if  defined(__SAMYUNG__) && !defined(_ICON_GME_)
         {
          if (SysGetGlobalLangCode() == LNG_CODE_KOR)
              sprintf(vText,"P%d      ",nWptNo);
          else
              sprintf(vText,"WPT%04d ",nWptNo);
         }
      #else
          sprintf(vText,"WPT%04d ",nWptNo);
      #endif
      else
          sprintf(vText,"%s%04d ",pHead,nWptNo);

      memmove(vName,vText,strlen(vText));
      vName[WAYPNT_TEXT_LEN - 0] = 0x00;

      return(vName);
}
char *cWayPntData::MakeEmptyWayPntName(void)
{
      static char vName[WAYPNT_TEXT_LEN + 2];
      char  vText[32];

      memset(vName,' ',sizeof(vName) - 1);

      strcpy(vText,"WPT---- ");

      memmove(vName,vText,strlen(vText));
      vName[WAYPNT_TEXT_LEN - 0] = 0x00;

      return(vName);
}
char *cWayPntData::GetWayPntName(int nWptNo)
{
      static char vName[WAYPNT_TEXT_LEN + 2];

      if (nWptNo == NAV_START_WPT_NO || nWptNo == NAV_CURSOR_WPT_NO)
         {
          if (nWptNo == NAV_CURSOR_WPT_NO)
              strcpy(vName,TXT_CURSOR_STR_MSG);
          else
              strcpy(vName,cWayPntData::MakeNormalWayPntName(nWptNo));
         }
      else
          memmove(vName,m_pWayPntData[nWptNo].vText,WAYPNT_TEXT_LEN);

      vName[WAYPNT_TEXT_LEN - 0] = 0x00;

      return(vName);
}
char *cWayPntData::FindUnUsedWayPntName(int nWptNo,char *pHead)
{
      static char vName[WAYPNT_TEXT_LEN + 2];
      int   nTmpNo;

      nTmpNo = nWptNo;
      while (1)
            {
             strcpy((char *)vName,cWayPntData::MakeNormalWayPntName(nTmpNo,pHead));
             if (cWayPntData::CheckSameNameExist((char *)vName) == 0)
                 break;

             ++nTmpNo;
             if (nTmpNo > WAYPNT_POINT_END_NO)
                 nTmpNo = WAYPNT_DATA_START;
            }

      return(vName);
}

void  cWayPntData::SetWayPntMarkType(int nMarkType)
{
      m_nWayPntMarkType = nMarkType;
}
int   cWayPntData::GetWayPntMarkType(void)
{
      return(m_nWayPntMarkType);
}
void  cWayPntData::SetWayPntMarkColor(int nMarkColor)
{
      m_nWayPntMarkColor = nMarkColor;
}
int   cWayPntData::GetWayPntMarkColor(void)
{
      return(m_nWayPntMarkColor);
}

#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
void  cWayPntData::SetPrevSaveSlotNo(int nSlotNo)
{
      m_nPrevSaveSlotNo = nSlotNo;
}
int   cWayPntData::GetPrevSaveSlotNo(void)
{
      return(m_nPrevSaveSlotNo);
}
int   cWayPntData::AppendConnectedLine(int nWptNo, xWPTPOINT *pWptData, int nSaveMode)
{
      int  nTempX;
      xWPTPOINT xWptDataP;

      if (cWayPntData::CheckSamePositionExist(pWptData->nGridLat, pWptData->nGridLon))
          return(0);

      if ((pWptData->bMark == WAYPNT_MARK_LINE_START || pWptData->bMark == WAYPNT_MARK_LINE_CNNCT))
         {
          if (pWptData->bMark == WAYPNT_MARK_LINE_START)
             {
              pWptData->dDate = WAYPNT_DATE_UNKNOWN;  // L-Link
              pWptData->dTime = WAYPNT_DATE_UNKNOWN;  // R-Link
             }
          else
             {
              nTempX = cWayPntData::GetPrevSaveSlotNo();

              if (cWayPntData::IsWptDataEmpty(nTempX) == 0)
                 {
                  pWptData->dDate = nTempX;               // L-Link
                  pWptData->dTime = WAYPNT_DATE_UNKNOWN;  // R-Link

                  cWayPntData::GetWayPntDataOne(nTempX, &xWptDataP);

                  if (xWptDataP.bMark == WAYPNT_MARK_LINE_START || xWptDataP.bMark == WAYPNT_MARK_LINE_CNNCT)
                     {
                      xWptDataP.dTime = nWptNo;        // R-Link

                      cWayPntData::SetWayPntDataOne(nTempX, &xWptDataP, 0);
                     }
                 }
              else
                 {
                  pWptData->bMark = WAYPNT_MARK_LINE_START;
                  pWptData->dDate = WAYPNT_DATE_UNKNOWN;  // L-Link
                  pWptData->dTime = WAYPNT_DATE_UNKNOWN;  // R-Link
                 }
             }

          cWayPntData::SetPrevSaveSlotNo(nWptNo);
          cWayPntData::SetWayPntMarkType(WAYPNT_MARK_LINE_CNNCT);

          if (nSaveMode)
              cWayPntData::SetWayPntDataOne(nWptNo, pWptData, 0);
         }

      return(1);
}
#endif

void  cWayPntData::SetWayPntDataOne(int nIndex,xWPTPOINT *pWptData,int nMustConvert)
{
      if (nIndex >= 0 && nIndex < WAYPNT_POINT_SIZE)
         {
          m_pWayPntData[nIndex] = *pWptData;
          if (nMustConvert)
              ConvertGridToMercOne(nIndex);
         }
}
int   cWayPntData::GetWayPntDataOne(int nIndex,xWPTPOINT *pWptData)
{
      if (nIndex >= 0 && nIndex < WAYPNT_POINT_SIZE)
         {
          *pWptData = m_pWayPntData[nIndex];
         }

      return(0);
}
int   cWayPntData::ConvertGridToMercOne(int nIndex)
{
      if (nIndex >= 0 && nIndex < WAYPNT_POINT_SIZE)
         {
          if (m_pWayPntData[nIndex].nGridLat <= -ABS_COOR_090 ||
              m_pWayPntData[nIndex].nGridLat >= +ABS_COOR_090)
              m_pWayPntData[nIndex].nMercLat = 0;
          else
              m_pWayPntData[nIndex].nMercLat = GridLatToMerc(m_pWayPntData[nIndex].nGridLat);

          m_pWayPntData[nIndex].nMercLon = GridLonToMerc(m_pWayPntData[nIndex].nGridLon);

      #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
          if (m_pWayPntData[nIndex].nGridLat <= -ABS_COOR_090 ||
              m_pWayPntData[nIndex].nGridLat >= +ABS_COOR_090)
              m_pWayPntData[nIndex].nMc32Y = 0;
          else
              m_pWayPntData[nIndex].nMc32Y = LongLatToMRC32(m_pWayPntData[nIndex].nGridLat);

          m_pWayPntData[nIndex].nMc32X = LongLonToMRC32(m_pWayPntData[nIndex].nGridLon);
      #endif
         }

      return(0);
}
LGRID cWayPntData::GetWayPntGridLat(int nIndex)
{
      return(m_pWayPntData[nIndex].nGridLat);
}
LGRID cWayPntData::GetWayPntGridLon(int nIndex)
{
      return(m_pWayPntData[nIndex].nGridLon);
}
LMERC cWayPntData::GetWayPntMercLat(int nIndex)
{
      return(m_pWayPntData[nIndex].nMercLat);
}
LMERC cWayPntData::GetWayPntMercLon(int nIndex)
{
      return(m_pWayPntData[nIndex].nMercLon);
}
void  cWayPntData::SetWayPntMercLat(int nIndex,LMERC nLat)
{
      m_pWayPntData[nIndex].nMercLat = nLat;
      m_pWayPntData[nIndex].nGridLat = MercLatToGrid(nLat);

  #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
      m_pWayPntData[nIndex].nMc32Y = MercLatToMRC32(nLat);
  #endif
}
void  cWayPntData::SetWayPntMercLon(int nIndex,LMERC nLon)
{
      m_pWayPntData[nIndex].nMercLon = nLon;
      m_pWayPntData[nIndex].nGridLon = MercLonToGrid(nLon);

  #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
      m_pWayPntData[nIndex].nMc32X = MercLonToMRC32(nLon);
  #endif
}
void  cWayPntData::SetWayPntMercOne(int nIndex,LMERC nLat,LMERC nLon)
{
      SetWayPntMercLat(nIndex,nLat);
      SetWayPntMercLon(nIndex,nLon);
}

#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
void  cWayPntData::SetWayPntGridLat(int nIndex,LGRID nLat)
{
      m_pWayPntData[nIndex].nGridLat = nLat;
      m_pWayPntData[nIndex].nMercLat = RealLatToMerc(GridToReal(nLat));

  #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
      m_pWayPntData[nIndex].nMc32Y = LongLatToMRC32(nLat);
  #endif
}
void  cWayPntData::SetWayPntGridLon(int nIndex,LGRID nLon)
{
      m_pWayPntData[nIndex].nGridLon = nLon;
      m_pWayPntData[nIndex].nMercLon = RealLonToMerc(GridToReal(nLon));;

  #if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
      m_pWayPntData[nIndex].nMc32X = LongLonToMRC32(nLon);
  #endif
}
void  cWayPntData::SetWayPntGridOne(int nIndex,LGRID nLat,LGRID nLon)
{
      SetWayPntGridLat(nIndex,nLat);
      SetWayPntGridLon(nIndex,nLon);
}
#endif


int   cWayPntData::CheckSameNameExist(int nIndex,xWPTPOINT *pWptData)
{
      int  i;
      char vTextX[WAYPNT_TEXT_LEN * 2];
      char vTextY[WAYPNT_TEXT_LEN * 2];

      vTextX[WAYPNT_TEXT_LEN + 0] = 0x00;
      vTextY[WAYPNT_TEXT_LEN + 0] = 0x00;

      memmove(vTextY,pWptData->vText,WAYPNT_TEXT_LEN);

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (IsWptDataEmpty(i) == 0)
              {
               memmove(vTextX,m_pWayPntData[i].vText,WAYPNT_TEXT_LEN);
               if (i != nIndex && strcmp(vTextX,vTextY) == 0)
                   return(1);
              }
          }

      return(0);
}
int   cWayPntData::CheckSameNameExist(char *pWptName)
{
      int  i;
      char vTextX[WAYPNT_TEXT_LEN * 2];
      char vTextY[WAYPNT_TEXT_LEN * 2];

      vTextX[WAYPNT_TEXT_LEN + 0] = 0x00;
      vTextY[WAYPNT_TEXT_LEN + 0] = 0x00;

      memmove(vTextY,pWptName,WAYPNT_TEXT_LEN);

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (IsWptDataEmpty(i) == 0)
              {
               memmove(vTextX,m_pWayPntData[i].vText,WAYPNT_TEXT_LEN);
               if (strcmp(vTextX,vTextY) == 0)
                   return(1);
              }
          }

      return(0);
}
int   cWayPntData::CheckSamePositionExist(LGRID nLat,LGRID nLon)
{
      int  i;

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (IsWptDataEmpty(i) == 0)
              {
               if (m_pWayPntData[i].nGridLat == nLat &&
                   m_pWayPntData[i].nGridLon == nLon)
                   return(1);
              }
          }

      return(0);
}

int   cWayPntData::IsWptDataEmpty(int nIndex)
{
      if (m_pWayPntData[nIndex].nGridLat == GRID_LAT_VAL_UNKNOWN ||
          m_pWayPntData[nIndex].nGridLon == GRID_LON_VAL_UNKNOWN)
          return(1);

      return(0);
}
int   cWayPntData::FindFirstEmptySlot(int nMustFind)
{
      int  i;

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (m_pWayPntData[i].nGridLat == GRID_LAT_VAL_UNKNOWN)
               return(i);
          }

      if (nMustFind)
          return(WAYPNT_POINT_SIZE - 1);

      return(WPT_NOT_FOUND);
}
int   cWayPntData::FindWptByScrnPos(int nScrnX,int nScrnY,int nWinLeft,int nWinBottom)
{
      int   i;
      INT32 nScrnX0,nScrnY0;
      INT32 nLimitUp,nLimitDn,nLimitLt,nLimitRt;

      nLimitUp = nScrnY - 7;
      nLimitDn = nScrnY + 7;
      nLimitLt = nScrnX - 7;
      nLimitRt = nScrnX + 7;

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (m_pWayPntData[i].nGridLat != GRID_LAT_VAL_UNKNOWN)
              {
               if (SysIsUsingMapCMAP())
                  {
               #if defined(__NAVIONICS__)
                   CalcNvsScrPosByMRC32(m_pWayPntData[i].nMc32Y,m_pWayPntData[i].nMc32X,(int *)&nScrnY0,(int *)&nScrnX0);
               #else
                   cmMerc2Screen(m_pWayPntData[i].nMercLon,m_pWayPntData[i].nMercLat,(SLong *)&nScrnX0,(SLong *)&nScrnY0);
                   nScrnX0 = cmgWinGetRealScrX(nWinLeft,nScrnX0);
                   nScrnY0 = cmgWinGetRealScrY(nWinBottom,nScrnY0);
               #endif
                  }
           #ifdef  __SAMYUNG__
               else
                  {
               #if defined(__NEW_MERCATOR__)
                   CalcWndwCoorByScrnOrigLong(m_pWayPntData[i].nMc32Y  ,m_pWayPntData[i].nMc32X  ,&nScrnY0,&nScrnX0,1);
               #else
                   CalcWndwCoorByScrnOrigLong(m_pWayPntData[i].nGridLat,m_pWayPntData[i].nGridLon,&nScrnY0,&nScrnX0,1);
               #endif
                  }
           #endif

               if (nScrnY0 >= nLimitUp && nScrnY0 <= nLimitDn && nScrnX0 >= nLimitLt && nScrnX0 <= nLimitRt)
                  {
                   if ((m_nWayPntDspMode == WPT_DSP_MODE_SHOW_ALL_I || m_nWayPntDspMode == WPT_DSP_MODE_SHOW_ALL_I_N) ||
                       (m_nWayPntDspMode == WPT_DSP_MODE_SELECTED   && m_pWayPntData[i].bDspMode != WAYPNT_DSP_MODE_OFF))
                        return(i);
                  }
              }
          }

      return(WPT_NOT_FOUND);
}
int   cWayPntData::GetUsedWayPoints(void)
{
      int   i,nUsed = 0;

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (m_pWayPntData[i].nGridLat != GRID_LAT_VAL_UNKNOWN)
               ++nUsed;
          }

      return(nUsed);
}

void  cWayPntData::DrawAllWayPoint(int nUsingMap,int nWinLeft,int nWinBottom)
{
      int   i,nCount;
      INT32 nScrnX0,nScrnY0;
#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
      INT32 nScrnX1,nScrnY1;
      int   nTempX;
#endif
      int   nSonarDataDraw = GetHaveToDrawSonarData();

      if (m_nWayPntDspMode == WPT_DSP_MODE_HIDE_ALL)
          return;

//    if (nUsingMap == USING_MAP_SAM)
//        return;

      if (nSonarDataDraw == MODE_VAL_ON)
          DrawFishDataInDraw();

      nCount = 0;
      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (m_pWayPntData[i].nGridLat != GRID_LAT_VAL_UNKNOWN)
              {
//             if (SysIsUsingMapCMAP())
               if (nUsingMap == USING_MAP_CMAP)
                  {
               #if !defined(__NAVIONICS__)
                   cmMerc2Screen(m_pWayPntData[i].nMercLon,m_pWayPntData[i].nMercLat,(SLong *)&nScrnX0,(SLong *)&nScrnY0);
                   nScrnX0 = cmgWinGetRealScrX(nWinLeft,nScrnX0);
                   nScrnY0 = cmgWinGetRealScrY(nWinBottom,nScrnY0);
               #endif
                  }
           #ifdef  __SAMYUNG__
               else
                  {
               #if defined(__NEW_MERCATOR__)
                   CalcWndwCoorByScrnOrigLong(m_pWayPntData[i].nMc32Y  ,m_pWayPntData[i].nMc32X  ,&nScrnY0,&nScrnX0,1);
               #else
                   CalcWndwCoorByScrnOrigLong(m_pWayPntData[i].nGridLat,m_pWayPntData[i].nGridLon,&nScrnY0,&nScrnX0,1);
               #endif
                  }
           #endif

               DrawOneWayPoint(nScrnX0,nScrnY0,m_pWayPntData + i);

           #if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
               if (m_pWayPntData[i].bMark == WAYPNT_MARK_LINE_CNNCT && m_pWayPntData[i].dDate != WAYPNT_DATE_UNKNOWN)
                  {
                   nTempX = m_pWayPntData[i].dDate;        // L-Link

                   if (m_pWayPntData[nTempX].bMark == WAYPNT_MARK_LINE_CNNCT || m_pWayPntData[nTempX].bMark == WAYPNT_MARK_LINE_START)
                      {
                       if (m_pWayPntData[nTempX].nGridLat != GRID_LAT_VAL_UNKNOWN)
                          {
                           if (nUsingMap == USING_MAP_CMAP)
                              {
                           #if !defined(__NAVIONICS__)
                               cmMerc2Screen(m_pWayPntData[nTempX].nMercLon,m_pWayPntData[nTempX].nMercLat,(SLong *)&nScrnX1,(SLong *)&nScrnY1);
                               nScrnX1 = cmgWinGetRealScrX(nWinLeft,nScrnX1);
                               nScrnY1 = cmgWinGetRealScrY(nWinBottom,nScrnY1);
                           #endif
                              }
                       #ifdef  __SAMYUNG__
                           else
                              {
                           #if defined(__NEW_MERCATOR__)
                               CalcWndwCoorByScrnOrigLong(m_pWayPntData[nTempX].nMc32Y  ,m_pWayPntData[nTempX].nMc32X  ,&nScrnY1,&nScrnX1,1);
                           #else
                               CalcWndwCoorByScrnOrigLong(m_pWayPntData[nTempX].nGridLat,m_pWayPntData[nTempX].nGridLon,&nScrnY1,&nScrnX1,1);
                           #endif
                              }
                       #endif

                           if (GrClippingScreenLong(&nScrnX0,&nScrnY0,&nScrnX1,&nScrnY1))
                               GrFastLine(nScrnX0,nScrnY0,nScrnX1,nScrnY1,m_pWayPntData[i].bColor);
                          }
                      }
                  }
           #endif

               if (nSonarDataDraw == MODE_VAL_ON && nCount > 200)
                  {
                   DrawFishDataInDraw();
                   nCount = 0;
                  }

               ++nCount;
              }
          }

      if (nSonarDataDraw == MODE_VAL_ON)
          DrawFishDataInDraw();
}
void  cWayPntData::DrawOneWayPoint(int nIndex,int nWinLeft,int nWinBottom)
{
      DrawOneWayPoint(m_pWayPntData + nIndex,nWinLeft,nWinBottom);
}
void  cWayPntData::DrawOneWayPoint(xWPTPOINT *pWptData,int nWinLeft,int nWinBottom)
{
      INT32 nScrnX0,nScrnY0;

      if (m_nWayPntDspMode == WPT_DSP_MODE_HIDE_ALL)
          return;

      if (pWptData->nGridLat != GRID_LAT_VAL_UNKNOWN)
         {
          if (SysIsUsingMapCMAP())
             {
          #if defined(__NAVIONICS__)
              CalcNvsScrPosByMRC32(pWptData->nMc32Y,pWptData->nMc32X,(int *)&nScrnY0,(int *)&nScrnX0);
          #else
              cmMerc2Screen(pWptData->nMercLon,pWptData->nMercLat,(SLong *)&nScrnX0,(SLong *)&nScrnY0);
              nScrnX0 = cmgWinGetRealScrX(nWinLeft,nScrnX0);
              nScrnY0 = cmgWinGetRealScrY(nWinBottom,nScrnY0);
          #endif
             }
      #ifdef  __SAMYUNG__
          else
             {
          #if defined(__NEW_MERCATOR__)
              CalcWndwCoorByScrnOrigLong(pWptData->nMc32Y  ,pWptData->nMc32X  ,&nScrnY0,&nScrnX0,1);
          #else
              CalcWndwCoorByScrnOrigLong(pWptData->nGridLat,pWptData->nGridLon,&nScrnY0,&nScrnX0,1);
          #endif
             }
      #endif

          DrawOneWayPoint(nScrnX0,nScrnY0,pWptData);
         }
}
void  cWayPntData::DrawOneWayPoint(int nScrnX,int nScrnY,xWPTPOINT *pWptData)
{
      if (pWptData->bDspMode == WAYPNT_DSP_MODE_OFF && m_nWayPntDspMode == WPT_DSP_MODE_SELECTED)
          return;

      DrawWayPntIconToChart(nScrnX,nScrnY,pWptData);

      if (m_nWayPntDspMode == WPT_DSP_MODE_SHOW_ALL_I_N || (pWptData->bDspMode == WAYPNT_DSP_MODE_ALL && m_nWayPntDspMode == WPT_DSP_MODE_SELECTED))
          DrawWayPntNameToChart(nScrnX,nScrnY,pWptData);
}
void  cWayPntData::DrawWayPntIconToChart(int nScrnX,int nScrnY,xWPTPOINT *pWptData)
{
      extern UCHAR  _WayPntMarkFont16x16[];
      extern XSCRN   G_xScrnMetric;
      CLRCHART wColor;
      HWORD    wData;
      int      nTempX;
      int      nLines,nShift,nSkips;
      UCHAR    *pFontData;
	    CLRCHART *pScreenX;
	    CLRCHART *pScreenY;

      nScrnX -= 8;             // 16
      nScrnY -= 8;             // 16

      if (nScrnY < (G_xScrnMetric.nClipY0 - 8) || nScrnY >= G_xScrnMetric.nClipY1 || nScrnX < (G_xScrnMetric.nClipX0 - 8) || nScrnX >= G_xScrnMetric.nClipX1)
          return;

      pFontData  = _WayPntMarkFont16x16 + pWptData->bMark * 32;

      nLines = 16;
      nShift =  0;

      if (nScrnY < G_xScrnMetric.nClipY0)
         {
          nSkips  = nScrnY - G_xScrnMetric.nClipY0;
         	nLines += nSkips;
         	pFontData  -= (nSkips * 2);
         	nScrnY = G_xScrnMetric.nClipY0;
         }

      if ((nScrnY + nLines) > G_xScrnMetric.nClipY1)
          nLines = G_xScrnMetric.nClipY1 - nScrnY + 1;

      if (nScrnX < G_xScrnMetric.nClipX0)
         {
          nShift = G_xScrnMetric.nClipX0 - nScrnX;
          nScrnX = G_xScrnMetric.nClipX0;
         }

#if (SCR_ROTATE_MODE == SCR_ROTATE_NONE)
	    pScreenX = (CLRCHART *)GrGetLineAddrOfY(nScrnY) + nScrnX;
#endif

#if (SCR_ROTATE_MODE == SCR_ROTATE_CW)
      pScreenX = (CLRCHART *)GrGetLineAddrOfY(nScrnX) + nScrnY;
#endif

#if (SCR_ROTATE_MODE == SCR_ROTATE_CCW)
      pScreenX = (CLRCHART *)GrGetLineAddrOfY(nScrnX) - nScrnY;
#endif

      wColor = GetRgbColorByPalNo(pWptData->bColor);

      while (nLines--)
            {
             nTempX   = nScrnX;
             pScreenY = pScreenX;

             wData = *pFontData++;
             wData = (wData << 8) | *pFontData++;
             wData <<= nShift;

             while (wData && nTempX <= G_xScrnMetric.nClipX1)
                   {
                    if (wData & 0x8000)
                        *pScreenY = wColor;

                    wData <<= 1;

               #if (SCR_ROTATE_MODE == SCR_ROTATE_NONE)
                    ++pScreenY;
               #endif

               #if (SCR_ROTATE_MODE == SCR_ROTATE_CW)
                     pScreenY -= G_xScrnMetric.nHeight;
               #endif

               #if (SCR_ROTATE_MODE == SCR_ROTATE_CCW)
                     pScreenY += G_xScrnMetric.nHeight;
               #endif

                    ++nTempX;
                   }

        #if (SCR_ROTATE_MODE == SCR_ROTATE_NONE)
             pScreenX += G_xScrnMetric.nWidth;
        #endif

        #if (SCR_ROTATE_MODE == SCR_ROTATE_CW)
             pScreenX++;
        #endif

        #if (SCR_ROTATE_MODE == SCR_ROTATE_CCW)
             pScreenX--;
        #endif
            }
}
void  cWayPntData::DrawWayPntNameToChart(int nScrnX,int nScrnY,xWPTPOINT *pWptData)
{
      static UCHAR vText[16] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};
      int  nLen;

#if  defined(__SAMYUNG__) && !defined(_ICON_GME_)
//    memmove(vText,pWptData->vText,WAYPNT_TEXT_LEN);
      vText[0] = pWptData->vText[0];
      vText[1] = pWptData->vText[1];
      vText[2] = pWptData->vText[2];
      vText[3] = pWptData->vText[3];
      vText[4] = pWptData->vText[4];
      vText[5] = pWptData->vText[5];
      vText[6] = pWptData->vText[6];
      vText[7] = pWptData->vText[7];

      for (nLen = (WAYPNT_TEXT_LEN - 1);nLen > 1;nLen--)
         {
          if (vText[nLen] != ' ')
              break;
         }

      ++nLen;
      vText[nLen] = 0x00;

//    nLen = strlen((char *)vText);

      GrDrawTextStr16x08(vText,nScrnX - nLen * 8 / 2,nScrnY + 10,pWptData->bColor);
#else
      memmove(vText,pWptData->vText,WAYPNT_TEXT_LEN);

      nLen = strlen((char *)vText);

      GrDrawTextStr16x08(vText,nScrnX - nLen * 8 / 2,nScrnY + 10,pWptData->bColor);
#endif
}

#if  defined(__NAVIONICS__)
void  cWayPntData::DrawAllWayPointNavionicsMap(void)
{
      int   i,nCount;
      INT32 nScrnX0,nScrnY0;
#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
      INT32 nScrnX1,nScrnY1;
      int   nTempX;
#endif
      int   nSonarDataDraw = GetHaveToDrawSonarData();

      if (m_nWayPntDspMode == WPT_DSP_MODE_HIDE_ALL)
          return;

//    if (nUsingMap == USING_MAP_SAM)
//        return;

      if (nSonarDataDraw == MODE_VAL_ON)
          DrawFishDataInDraw();

      nCount = 0;
      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (m_pWayPntData[i].nGridLat != GRID_LAT_VAL_UNKNOWN)
              {
               CalcNvsScrPosByMRC32(m_pWayPntData[i].nMc32Y,m_pWayPntData[i].nMc32X,(int *)&nScrnY0,(int *)&nScrnX0);

               DrawOneWayPoint(nScrnX0,nScrnY0,m_pWayPntData + i);

           #if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
               if (m_pWayPntData[i].bMark == WAYPNT_MARK_LINE_CNNCT && m_pWayPntData[i].dDate != WAYPNT_DATE_UNKNOWN)
                  {
                   nTempX = m_pWayPntData[i].dDate;        // L-Link

                   if (m_pWayPntData[nTempX].bMark == WAYPNT_MARK_LINE_CNNCT || m_pWayPntData[nTempX].bMark == WAYPNT_MARK_LINE_START)
                      {
                       if (m_pWayPntData[nTempX].nGridLat != GRID_LAT_VAL_UNKNOWN)
                          {
                           CalcNvsScrPosByMRC32(m_pWayPntData[nTempX].nMc32Y,m_pWayPntData[nTempX].nMc32X,(int *)&nScrnY1,(int *)&nScrnX1);

                           if (GrClippingScreenLong(&nScrnX0,&nScrnY0,&nScrnX1,&nScrnY1))
                               GrFastLine(nScrnX0,nScrnY0,nScrnX1,nScrnY1,m_pWayPntData[i].bColor);
                          }
                      }
                  }
           #endif

               if (nSonarDataDraw == MODE_VAL_ON && nCount > 200)
                  {
                   DrawFishDataInDraw();
                   nCount = 0;
                  }

               ++nCount;
              }
          }

      if (nSonarDataDraw == MODE_VAL_ON)
          DrawFishDataInDraw();
}
#endif

#if 0	// Temp
void  cWayPntData::DrawSmallWayPntMark(cWindow *pWindow,sPoint xPnt,int nMarkType,int nBackColorID,int nForeColorID)
{
      extern UCHAR  _WayPntMarkFont16x16[];

      DrawRealWayPntMark(pWindow,xPnt,nMarkType,nBackColorID,nForeColorID,_WayPntMarkFont16x16,16,16);
}
void  cWayPntData::DrawLargeWayPntMark(cWindow *pWindow,sPoint xPnt,int nMarkType,int nBackColorID,int nForeColorID)
{
      extern UCHAR  _WayPntMarkFont32x32[];

      DrawRealWayPntMark(pWindow,xPnt,nMarkType,nBackColorID,nForeColorID,_WayPntMarkFont32x32,32,32);
}
void  cWayPntData::DrawRealWayPntMark(cWindow *pWindow,sPoint xPnt,int nMarkType,int nBackColorID,int nForeColorID,UCHAR *pMarkData,int nMarkWidth,int nMarkHeight)
{
      PEGCOLOR dForeColor;
      PEGCOLOR dBackColor;
      DWORD    dMaskX,dMaskY;
      DWORD    dDataX;
      int      nWidthBytes;
      int      nFontBytes;
      int      i;
      UCHAR    *pFontData;
      int      nScrnX,nScrnY;
      PegScreen *pScreen;

      pWindow->BeginDraw(pWindow->mClient);

      nWidthBytes = (nMarkWidth >> 3);
      if (nMarkWidth & 0x07)
          ++nWidthBytes;

      nFontBytes = nWidthBytes * nMarkHeight;
      pFontData  = pMarkData + nMarkType * nFontBytes;

      dForeColor = PegResourceManager::GetColor(nForeColorID);
      dBackColor = PegResourceManager::GetColor(nBackColorID);

      dMaskX = 1 << (nMarkWidth - 1);

      pScreen= pWindow->Screen();

      nScrnY = xPnt.y;

      for (i = 0;i < nMarkHeight;i++)
          {
           dMaskY = dMaskX;

           dDataX = *pFontData++;
           if (nMarkWidth >  8)  dDataX = (dDataX << 8) | *pFontData++;
           if (nMarkWidth > 16)  dDataX = (dDataX << 8) | *pFontData++;
           if (nMarkWidth > 24)  dDataX = (dDataX << 8) | *pFontData++;

           nScrnX = xPnt.x;
           while (dMaskY)
                 {
                  if (dMaskY & dDataX)
                     {
                      pScreen->PutPixel(nScrnX,nScrnY,dForeColor);
                     }
                  else
                     {
                      if (nBackColorID != -1)
                          pScreen->PutPixel(nScrnX,nScrnY,dBackColor);
                     }
                  ++nScrnX;
                  dMaskY >>= 1;
                 }
           ++nScrnY;
          }

      pWindow->EndDraw();
}
#endif

int   cWayPntData::CheckTextValid(xWPTPOINT *pWptData)
{
      int  i,nError = 0;

      for (i = 0;i < WAYPNT_TEXT_LEN;i++)
           if (pWptData->vText[i] != 0x00 && (pWptData->vText[i] < 0x20 || pWptData->vText[i] >= 0x7f))
              {
               pWptData->vText[i] = ' ';
               nError = 1;
              }

      return(nError);
}

int   cWayPntData::CheckAllDangerAlarm(void)
{
#if 0	// Temp
      int   i;
      REAL  rShipLat,rShipLon;
      REAL  rAlarmLimit;
      REAL  rDst,rCrs;
      int   nAlarmCnt = 0;
      int   nSleepCnt = 0;

      rShipLat = GetShipRealLat();
      rShipLon = GetShipRealLon();

      rAlarmLimit = cAlarmData::m_nAlarmNavDangerValue / 100.0;

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           if (m_pWayPntData[i].bDangerMode == MODE_VAL_ON && m_pWayPntData[i].nGridLat != GRID_LAT_VAL_UNKNOWN)
              {
               GetDistanceAndCourse(rShipLat,rShipLon,GridToReal(m_pWayPntData[i].nGridLat),GridToReal(m_pWayPntData[i].nGridLon),&rDst,&rCrs);
               if (rDst <= rAlarmLimit)
                  {
                   if (m_pWayPntData[i].nAlarmStatus != ALARM_STATUS_SLEEP)
                      {
                       m_pWayPntData[i].nAlarmStatus = ALARM_STATUS_ACTIVE;
                       ++nAlarmCnt;
                      }
                  else
                      {
                       ++nSleepCnt;
                      }
                  }
               else
                  {
                   m_pWayPntData[i].nAlarmStatus = ALARM_STATUS_NONE;
                  }
              }
           else
               m_pWayPntData[i].nAlarmStatus = ALARM_STATUS_NONE;
          }

      if (nAlarmCnt)
          cAlarmData::m_pAlarmDngrWpt->SetAlarmStatus(ALARM_STATUS_ACTIVE);
      else
         {
          if (nSleepCnt == 0)
              cAlarmData::m_pAlarmDngrWpt->SetAlarmStatus(ALARM_STATUS_NONE);
         }
#endif
      return(0);
}

void  cWayPntData::SaveAllBackData(UCHAR *pBackData)
{
      int  i;

      for (i = 0;i < WAYPNT_POINT_SIZE;i++)
          {
           memmove(pBackData,m_pWayPntData + i,WAYPNT_ONE_BACK_SIZE);
           pBackData += WAYPNT_ONE_BACK_SIZE;
          }
}
void  cWayPntData::LoadAllBackData(UCHAR *pBackData)
{
      int  i;

      for (i = 0;i < WAYPNT_POINT_SIZE;i++)
          {
           memmove(m_pWayPntData + i,pBackData,WAYPNT_ONE_BACK_SIZE);
           pBackData += WAYPNT_ONE_BACK_SIZE;

           if (IsGridLatRangeError(m_pWayPntData[i].nGridLat) || IsGridLonRangeError(m_pWayPntData[i].nGridLon))
              {
               MakeWayPntDataNull(m_pWayPntData + i);
              }
           else
              {
               if (m_pWayPntData[i].bMark    > WAYPNT_MARK_MAX)      m_pWayPntData[i].bMark    = WAYPNT_MARK_DFLT;
               if (m_pWayPntData[i].bColor   > WAYPNT_COLOR_MAX)     m_pWayPntData[i].bColor   = WAYPNT_COLOR_DFLT;

               CheckTextValid(m_pWayPntData + i);

               m_pWayPntData[i].bDangerMode &= 0x01;

               if (m_pWayPntData[i].bDspMode > WAYPNT_DSP_MODE_ALL)  m_pWayPntData[i].bDspMode = WAYPNT_DSP_MODE_ALL;

           #if defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
               if (m_pWayPntData[i].dDate <  WAYPNT_DATA_START || m_pWayPntData[i].dDate >= WAYPNT_POINT_SIZE) m_pWayPntData[i].dDate = WAYPNT_DATE_UNKNOWN;
               if (m_pWayPntData[i].dTime <  WAYPNT_DATA_START || m_pWayPntData[i].dTime >= WAYPNT_POINT_SIZE) m_pWayPntData[i].dTime = WAYPNT_DATE_UNKNOWN;
           #endif

               ConvertGridToMercOne(i);
              }
          }
}

void  cWayPntData::ClearAllData(void)
{
      int   i;

      m_nWayPntMarkType  = WAYPNT_MARK_DFLT;         // WAYPNT_MARK_MIN,..,WAYPNT_MARK_MAX
      m_nWayPntMarkColor = WAYPNT_COLOR_DFLT;        // WAYPNT_COLOR_MIN,..,WAYPNT_COLOR_MAX

      for (i = 0;i < WAYPNT_POINT_SIZE;i++)
           MakeWayPntDataNull(i);
}
void  cWayPntData::ClearWayPntMenu(void)
{
      m_nWayPntDspMode = WPT_DSP_MODE_SELECTED;    // WPT_DSP_MODE_HIDE_ALL,..,WPT_DSP_MODE_SELECTED
}
void  cWayPntData::ClearFactoryReset(void)
{
      ClearWayPntMenu();
}

int   cWayPntData::SaveData(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
      BACK16 vDummy[16];

      pTemp = pBackData;

      memmove(pTemp,&m_nWayPntMarkType      ,sizeof(m_nWayPntMarkType    )); pTemp += sizeof(m_nWayPntMarkType    );
      memmove(pTemp,&m_nWayPntMarkColor     ,sizeof(m_nWayPntMarkColor   )); pTemp += sizeof(m_nWayPntMarkColor   );

      memmove(pTemp, vDummy                 ,sizeof(vDummy               )); pTemp += sizeof(vDummy               );

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
int   cWayPntData::RestData(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
      BACK16 vDummy[16];

      pTemp = pBackData;

      memmove(&m_nWayPntMarkType     ,pTemp,sizeof(m_nWayPntMarkType    )); pTemp += sizeof(m_nWayPntMarkType    );
      memmove(&m_nWayPntMarkColor    ,pTemp,sizeof(m_nWayPntMarkColor   )); pTemp += sizeof(m_nWayPntMarkColor   );

      memmove( vDummy                ,pTemp,sizeof(vDummy               )); pTemp += sizeof(vDummy               );

      TestData();

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
void  cWayPntData::TestData(void)
{
      RangeCheckBack16(&m_nWayPntMarkType    ,WAYPNT_MARK_MIN     ,WAYPNT_MARK_MAX      ,WAYPNT_MARK_DFLT     );
      RangeCheckBack16(&m_nWayPntMarkColor   ,WAYPNT_COLOR_MIN    ,WAYPNT_COLOR_MAX     ,WAYPNT_COLOR_DFLT    );
}

int   cWayPntData::SaveWayPntMenu(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
      BACK16 vDummy[15];
#else
      BACK16 vDummy[16];
#endif

      pTemp = pBackData;

      memmove(pTemp,&m_nWayPntDspMode      ,sizeof(m_nWayPntDspMode    )); pTemp += sizeof(m_nWayPntDspMode    );

#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
      memmove(pTemp,&m_nPrevSaveSlotNo     ,sizeof(m_nPrevSaveSlotNo   )); pTemp += sizeof(m_nPrevSaveSlotNo   );
#endif

      memmove(pTemp, vDummy                ,sizeof(vDummy              )); pTemp += sizeof(vDummy              );

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
int   cWayPntData::RestWayPntMenu(UCHAR *pBackData)
{
      int    nSize;
      UCHAR  *pTemp;
#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
      BACK16 vDummy[15];
#else
      BACK16 vDummy[16];
#endif

      pTemp = pBackData;

      memmove(&m_nWayPntDspMode     ,pTemp,sizeof(m_nWayPntDspMode    )); pTemp += sizeof(m_nWayPntDspMode    );

#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
      memmove(&m_nPrevSaveSlotNo    ,pTemp,sizeof(m_nPrevSaveSlotNo   )); pTemp += sizeof(m_nPrevSaveSlotNo   );
#endif

      memmove( vDummy               ,pTemp,sizeof(vDummy              )); pTemp += sizeof(vDummy              );

      TestWayPntMenu();

      nSize = (DWORD)pTemp - (DWORD)pBackData;
      nSize = MakeSizeOfDoubleWord(nSize);

      return(nSize);
}
void  cWayPntData::TestWayPntMenu(void)
{
      RangeCheckBack16(&m_nWayPntDspMode   ,WPT_DSP_MODE_HIDE_ALL ,WPT_DSP_MODE_SELECTED ,WPT_DSP_MODE_SELECTED );

#if  defined(_MARK_LINE_DRAW_FUNC_ENABLED_)
      if (m_nPrevSaveSlotNo < WAYPNT_DATA_START || m_nPrevSaveSlotNo >= WAYPNT_POINT_SIZE)
          m_nPrevSaveSlotNo = WAYPNT_DATE_UNKNOWN;
#endif
}

#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
int   cWayPntData::LoadOldSamWayPntData(UCHAR *pFileName,int nConvertMode)
{
      if (pFileName[0] == 'M')
          return(LoadOldSamWayPntDataMark(pFileName,nConvertMode));

      return(LoadOldSamWayPntDataWayPT(pFileName,nConvertMode));
}
int   cWayPntData::LoadOldSamWayPntDataMark(UCHAR *pFileName,int nConvertMode)
{
#if 0	// Temp
      FHANDLE hHandle;
      UCHAR   bHeadX;
      UCHAR   bHeadY;
      HWORD   wSizeX;
      int     nError;
      int     i;
      DWORD   dReadSize;
      OLDMARK xOldMarkData;
      INT32   vLat[WAYPNT_POINT_SIZE];
      INT32   vLon[WAYPNT_POINT_SIZE];
      UCHAR   vCol[WAYPNT_POINT_SIZE];
      UCHAR   vMrk[WAYPNT_POINT_SIZE];
      xWPTPOINT xWptData;
      UCHAR vText[WAYPNT_TEXT_LEN * 2];
      LGRID nGridLat,nGridLon;
      int   nWptNo;
      INT32 nLat,nLon;
      INT32 nMark,nColor;
      char  vFileName[256];

      strcpy(vFileName,OsfGetSdCardDriveStr());
      strcat(vFileName,(char *)pFileName);

      hHandle = OsfOpen((char *)vFileName,OSF_READ_ONLY);
      if (hHandle < OSF_NO_ERROR)
          return(0);

      nError = OsfRead(hHandle,&bHeadX, 1,&dReadSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      nError = OsfRead(hHandle,&bHeadY, 1,&dReadSize);

      if (nError < OSF_NO_ERROR || bHeadX != SAM_OLD_DATA_HEAD_X || bHeadY != SAM_OLD_DATA_HEAD_Y)
         {
          OsfClose(hHandle);
          return(0);
         }

      nError = OsfRead(hHandle,&wSizeX, 2,&dReadSize);

      if (nError < OSF_NO_ERROR || wSizeX == 0)
         {
          OsfClose(hHandle);
          return(0);
         }

      if (wSizeX >= WAYPNT_POINT_SIZE)
          wSizeX  = WAYPNT_POINT_SIZE - 1;

      for (i = 0;i < wSizeX;i++)
          {
           nError = OsfRead(hHandle,&xOldMarkData,sizeof(OLDTRACK),&dReadSize);

           if (nError < OSF_NO_ERROR)
              {
               OsfClose(hHandle);
               return(0);
              }

           if (GetLatLonMarkColorDataFromOldMark(&xOldMarkData,&nLat,&nLon,&nMark,&nColor,nConvertMode) == 0)
              {
               OsfClose(hHandle);
               return(0);
              }

           vLat[i] = nLat;
           vLon[i] = nLon;
           vCol[i] = (UCHAR)nColor;
           vMrk[i] = (UCHAR)nMark;
          }

      OsfClose(hHandle);

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
           MakeWayPntDataNull(i);

      for (i = 0;i < wSizeX;i++)
          {
           nGridLat = vLat[i];
           nGridLon = vLon[i];

           nWptNo = FindFirstEmptySlot(1);

           strcpy((char *)vText,MakeNormalWayPntName(nWptNo,NULL));

           MakeWayPntDataByVal(&xWptData,nGridLat,nGridLon,vText,vMrk[i],vCol[i],0,WAYPNT_DSP_MODE_ALL);

           SetWayPntDataOne(nWptNo,&xWptData,1);
          }
#endif
      return(1);
}
int   cWayPntData::LoadOldSamWayPntDataWayPT(UCHAR *pFileName,int nConvertMode)
{
#if 0	// Temp
      FHANDLE hHandle;
      int     nError;
      int     i;
      DWORD   dReadSize;
      OLDWPNT   xWptDataX;
      xWPTPOINT xWptDataY;
      UCHAR vText[WAYPNT_TEXT_LEN * 4];
      LGRID nGridLat,nGridLon;
      int   nWptNo;
      REAL  rLat,rLon;
      INT32 nLat,nLon;
      INT32 nMark,nColor;
      char  vFileName[256];
      #define  OLD_WPT_NO_SIZE         10000

      if (pFileName[0] != 'W')
          return(0);

      strcpy(vFileName,OsfGetSdCardDriveStr());
      strcat(vFileName,(char *)pFileName);

      hHandle = OsfOpen((char *)vFileName,OSF_READ_ONLY);
      if (hHandle < OSF_NO_ERROR)
          return(0);

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
           MakeWayPntDataNull(i);

      for (i = 0;i < OLD_WPT_NO_SIZE;i++)
          {
           nError = OsfRead(hHandle,&xWptDataX,sizeof(xWptDataX),&dReadSize);

           if (nError < OSF_NO_ERROR)
              {
               OsfClose(hHandle);
               return(0);
              }

           nLat = xWptDataX.nLat;
           nLon = xWptDataX.nLon;

           if (nLat <= -ABS_COOR_090 || nLat >= +ABS_COOR_090 ||
               nLon <  +ABS_COOR_000 || nLon >= +ABS_COOR_360 || i == 0)
              {
              }
           else
              {
               if (nConvertMode == SAM_DATA_CNVT_MODE_W84 || nConvertMode == SAM_DATA_CNVT_MODE_KOR)
                  {
                   rLat = GridToReal(nLat);
                   rLon = GridToReal(nLon);

                   if (nConvertMode == SAM_DATA_CNVT_MODE_W84)
                       cDATUM::LocalDatumToWGS84(KOREA_DATUM_NO,rLat,rLon,&rLat,&rLon);
                   else
                       cDATUM::WGS84ToLocalDatum(KOREA_DATUM_NO,rLat,rLon,&rLat,&rLon);

                   nLat = RealToGrid(rLat);
                   nLon = RealToGrid(rLon);
                  }

               nWptNo = FindFirstEmptySlot(1);

//             strcpy((char *)vText,MakeNormalWayPntName(nWptNo,NULL));
               strcpy((char *)vText,(char *)xWptDataX.vText);
               vText[WAYPNT_TEXT_LEN] = 0x00;

               MakeWayPntDataByVal(&xWptDataY,nLat,nLon,vText,xWptDataX.bMark,xWptDataX.bColor,0,WAYPNT_DSP_MODE_ALL);

               SetWayPntDataOne(nWptNo,&xWptDataY,1);
              }
          }

      OsfClose(hHandle);
#endif
      return(1);
}
int   cWayPntData::LoadNewSamWayPntData(UCHAR *pFileName,int nConvertMode)
{
#if 0	// Temp
      FHANDLE hHandle;
      int     nError;
      int     i,k;
      DWORD   dReadSize;
      HWORD   wSizeX;
      NEWMARK   xGridWayPntX;
      xWPTPOINT xGridWayPntY;
      REAL  rLat,rLon;
      INT32 nLat,nLon;
      INT32 nType,nColor;
      char  vHeadData[128];
      char  vFileName[128];

      if (CheckExistSamDataDirName(1) == 0)
          return(0);

      strcpy(vFileName,GetAppendedSamDataDirName((const char *)pFileName));

      hHandle = OsfOpen((char *)vFileName,OSF_READ_ONLY);
      if (hHandle < OSF_NO_ERROR)
          return(0);

      nError = OsfRead(hHandle,vHeadData,SAM_ALL_DATA_HEAD_LENTH,&dReadSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      if (strncmp(vHeadData,SAM_WAYPT_DATA_HEAD_STR,SAM_ALL_DATA_HEAD_LENTH) != 0)
         {
          OsfClose(hHandle);
          return(0);
         }

      nError = OsfRead(hHandle,&wSizeX,sizeof(wSizeX),&dReadSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
           MakeWayPntDataNull(i);

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           nError = OsfRead(hHandle,&xGridWayPntX,sizeof(xGridWayPntX),&dReadSize);

           if (nError < OSF_NO_ERROR)
              {
               OsfClose(hHandle);
               return(0);
              }

           nLat = xGridWayPntX.nGridLat;
           nLon = xGridWayPntX.nGridLon;

           if (nLat <= -ABS_COOR_090 || nLat >= +ABS_COOR_090 ||
               nLon <  +ABS_COOR_000 || nLon >= +ABS_COOR_360)
              {
               MakeWayPntDataNull(i);
              }
           else
              {
               if (nConvertMode == SAM_DATA_CNVT_MODE_W84 || nConvertMode == SAM_DATA_CNVT_MODE_KOR)
                  {
                   rLat = GridToReal(nLat);
                   rLon = GridToReal(nLon);

                   if (nConvertMode == SAM_DATA_CNVT_MODE_W84)
                       cDATUM::LocalDatumToWGS84(KOREA_DATUM_NO,rLat,rLon,&rLat,&rLon);
                   else
                       cDATUM::WGS84ToLocalDatum(KOREA_DATUM_NO,rLat,rLon,&rLat,&rLon);

                   nLat = RealToGrid(rLat);
                   nLon = RealToGrid(rLon);
                  }

               MakeWayPntDataByVal(&xGridWayPntY,nLat,nLon,xGridWayPntX.vText,xGridWayPntX.bMark,xGridWayPntX.bColor,xGridWayPntX.bDangerMode,xGridWayPntX.bDspMode);

               SetWayPntDataOne(i,&xGridWayPntY,1);
              }
          }

      OsfClose(hHandle);
#endif
      return(1);
}
int   cWayPntData::SaveNewSamWayPntData(UCHAR *pFileName)
{
      FHANDLE hHandle;
      int     nError;
      int     i;
      DWORD   dWriteSize;
      HWORD   wSizeX;
      NEWMARK xGridWayPnt;
      char  vFileName[128];

      if (CheckExistSamDataDirName(1) == 0)
          return(0);

      strcpy(vFileName,GetAppendedSamDataDirName((const char *)pFileName));

      hHandle = OsfOpen((char *)vFileName,OSF_READ_WRITE | OSF_CREATE_ALWAYS);
      if (hHandle < OSF_NO_ERROR)
          return(0);

      nError = OsfWrite(hHandle,SAM_WAYPT_DATA_HEAD_STR,SAM_ALL_DATA_HEAD_LENTH,&dWriteSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      wSizeX = GetUsedWayPoints();

      nError = OsfWrite(hHandle,&wSizeX,sizeof(wSizeX),&dWriteSize);

      if (nError < OSF_NO_ERROR)
         {
          OsfClose(hHandle);
          return(0);
         }

      for (i = WAYPNT_DATA_START;i < WAYPNT_POINT_SIZE;i++)
          {
           memmove(&xGridWayPnt,m_pWayPntData + i,sizeof(xGridWayPnt));
 
           nError = OsfWrite(hHandle,&xGridWayPnt,sizeof(xGridWayPnt),&dWriteSize);

           if (nError < OSF_NO_ERROR)
              {
               OsfClose(hHandle);
               return(0);
              }
          }

      OsfClose(hHandle);

      return(1);
}
int   cWayPntData::GetLatLonMarkColorDataFromOldMark(OLDMARK *pOldMarkData,INT32 *pLat,INT32 *pLon,INT32 *pMark,INT32 *pColor,int nConvertMode)
{
#if 0	// Temp
      REAL  rLat,rLon;
      INT32 nLat,nLon;
      INT32 nMark,nColor;

      nLat  = ((pOldMarkData->nMaskLat & OLD_LAT_MASK) << 3) - ABS_COOR_090;
      nLon  = (pOldMarkData->nMaskLon  & OLD_LON_MASK) << 3;

      nMark = (pOldMarkData->nMaskLat & OLD_MRK_MASK) >> 27;
      nColor= (pOldMarkData->nMaskLon & OLD_COL_MASK) >> 28;

      if (nLat <= -ABS_COOR_090 || nLat >= +ABS_COOR_090 ||
          nLon <  +ABS_COOR_000 || nLon >= +ABS_COOR_360)
          return(0);

      if (nConvertMode == SAM_DATA_CNVT_MODE_W84 || nConvertMode == SAM_DATA_CNVT_MODE_KOR)
         {
          rLat = GridToReal(nLat);
          rLon = GridToReal(nLon);

          if (nConvertMode == SAM_DATA_CNVT_MODE_W84)
              cDATUM::LocalDatumToWGS84(KOREA_DATUM_NO,rLat,rLon,&rLat,&rLon);
          else
              cDATUM::WGS84ToLocalDatum(KOREA_DATUM_NO,rLat,rLon,&rLat,&rLon);

          nLat = RealToGrid(rLat);
          nLon = RealToGrid(rLon);
         }

      *pLat  = nLat;
      *pLon  = nLon;
      *pMark = nMark;
      *pColor= nColor;
#endif
      return(1);
}
int   cWayPntData::GetWayPntDataFileNames(SAMDATAFILE *pSamDataFiles,int nCounts)
{
      OSFDOSDirEntry xDirEntry;
      char    vDirName[256];
      char    vFileName[256];
      char    vFullName[256];
      char    vHeadName[SAM_ALL_DATA_HEAD_LENTH * 2];
      FHANDLE hDirHandle;
      FHANDLE hFileHandle;
      int     nError;
      UCHAR   bHeadX;
      UCHAR   bHeadY;
      HWORD   wSizeX;
      int     nSizeX;
      DWORD   dReadSize,i;
      int     nFoundFiles = 0;

      if (!SdCardGetCardInsertStatus())
          return(0);

      if (SdCardGetCardChangStatus())
         {
          SdCardSetCardChangStatus(0);
          OsfResetDisk(SDCARD_MOUNT_NO);
          OsfMountDisk(SDCARD_MOUNT_NO);
         }

      strcpy(vDirName,OsfGetSdCardDriveStr());
      strcat(vDirName,"\\*");

      hDirHandle = OsfFindFirstEx(vDirName,&xDirEntry,vFileName,sizeof(vFileName));
      if (hDirHandle < OSF_NO_ERROR)
          return(0);

      while (1)
            {
             if ((vFileName[0] == 'M' && vFileName[5] == '.' && vFileName[6] == 'D' && vFileName[7] == 'A' && vFileName[8] == 'T') ||
                 (vFileName[0] == 'W' && vFileName[5] == '.' && vFileName[6] == 'D' && vFileName[7] == 'A' && vFileName[8] == 'T'))
                {
                 hFileHandle = OsfOpen((char *)vFileName,OSF_READ_ONLY);
                 if (hFileHandle >= OSF_NO_ERROR)
                    {
                     if (vFileName[0] == 'M')
                        {
                         OsfRead(hFileHandle,&bHeadX, 1,&dReadSize);
                         OsfRead(hFileHandle,&bHeadY, 1,&dReadSize);

                         nError = OsfRead(hFileHandle,&wSizeX, 2,&dReadSize);

                         nSizeX = wSizeX;
                        }
                     else
                        {
                         bHeadX = SAM_OLD_DATA_HEAD_X;
                         bHeadY = SAM_OLD_DATA_HEAD_Y;

                         nSizeX = OsfSeek(hFileHandle,0,OSF_FILE_END);

                         if (nSizeX == 240000)
                             nError = OSF_NO_ERROR;
                         else
                             nError = OSF_ERROR_GENERAL;

                         wSizeX = nSizeX / 12;
                        }

                     if (nError >= OSF_NO_ERROR && bHeadX == SAM_OLD_DATA_HEAD_X && bHeadY == SAM_OLD_DATA_HEAD_Y)
                        {
                         if (nFoundFiles < nCounts && wSizeX)
                            {
                             strcpy(pSamDataFiles->vFileName,vFileName);
                             pSamDataFiles->nPointCnt = wSizeX;
                             pSamDataFiles->nFileType = SAM_DATA_FILE_TYPE_OLD;
                             strcpy(pSamDataFiles->vTimeDate,GetTimeChrStr(xDirEntry.CreateDateTime.Hour,xDirEntry.CreateDateTime.Minute,GetTimeTimeFormat()));
                             strcat(pSamDataFiles->vTimeDate," ");
                             strcat(pSamDataFiles->vTimeDate,GetDateChrStr(xDirEntry.CreateDateTime.Year1980 + 1980,xDirEntry.CreateDateTime.Month,xDirEntry.CreateDateTime.Day,GetTimeDateFormat()));

                             pSamDataFiles->nYear   = xDirEntry.CreateDateTime.Year1980 + 1980;
                             pSamDataFiles->nMonth  = xDirEntry.CreateDateTime.Month;
                             pSamDataFiles->nDay    = xDirEntry.CreateDateTime.Day;
                             pSamDataFiles->nHour   = xDirEntry.CreateDateTime.Hour;
                             pSamDataFiles->nMinute = xDirEntry.CreateDateTime.Minute;

                             ++nFoundFiles;
                             ++pSamDataFiles;
                            }
                        }

                     OsfClose(hFileHandle);
                    }
                }

             nError = OsfFindNextEx(hDirHandle,&xDirEntry,vFileName,sizeof(vFileName));
             if (nError < OSF_NO_ERROR)
                 break;
            }

      OsfFindClose(hDirHandle);

      strcpy(vDirName,GetAppendedSamDataDirName("*"));

      hDirHandle = OsfFindFirstEx(vDirName,&xDirEntry,vFileName,sizeof(vFileName));
      if (hDirHandle < OSF_NO_ERROR)
          return(0);

      while (1)
            {
             strcpy(vFullName,GetAppendedSamDataDirName(vFileName));

             hFileHandle = OsfOpen((char *)vFullName,OSF_READ_ONLY);
             if (hFileHandle >= OSF_NO_ERROR)
                {
                 memset(vHeadName,0x00,SAM_ALL_DATA_HEAD_LENTH);

                 nError = OsfRead(hFileHandle,vHeadName,SAM_ALL_DATA_HEAD_LENTH,&dReadSize);
                 nError = OsfRead(hFileHandle,&wSizeX,sizeof(wSizeX),&dReadSize);

                 if (nError >= OSF_NO_ERROR && (strncmp(vHeadName,SAM_WAYPT_DATA_HEAD_STR,SAM_ALL_DATA_HEAD_LENTH) == 0))
                    {
                     nSizeX = wSizeX;

//                   if (nFoundFiles < nCounts && wSizeX)
                     if (nFoundFiles < nCounts)
                        {
                         strcpy(pSamDataFiles->vFileName,vFileName);
                         pSamDataFiles->nPointCnt = nSizeX;
                         pSamDataFiles->nFileType = SAM_DATA_FILE_TYPE_NEW;
                         strcpy(pSamDataFiles->vTimeDate,GetTimeChrStr(xDirEntry.CreateDateTime.Hour,xDirEntry.CreateDateTime.Minute,GetTimeTimeFormat()));
                         strcat(pSamDataFiles->vTimeDate," ");
                         strcat(pSamDataFiles->vTimeDate,GetDateChrStr(xDirEntry.CreateDateTime.Year1980 + 1980,xDirEntry.CreateDateTime.Month,xDirEntry.CreateDateTime.Day,GetTimeDateFormat()));

                         pSamDataFiles->nYear   = xDirEntry.CreateDateTime.Year1980 + 1980;
                         pSamDataFiles->nMonth  = xDirEntry.CreateDateTime.Month;
                         pSamDataFiles->nDay    = xDirEntry.CreateDateTime.Day;
                         pSamDataFiles->nHour   = xDirEntry.CreateDateTime.Hour;
                         pSamDataFiles->nMinute = xDirEntry.CreateDateTime.Minute;

                         ++nFoundFiles;
                         ++pSamDataFiles;
                        }
                    }

                 OsfClose(hFileHandle);
                }

             nError = OsfFindNextEx(hDirHandle,&xDirEntry,vFileName,sizeof(vFileName));
             if (nError < OSF_NO_ERROR)
                 break;
            }

      OsfFindClose(hDirHandle);

      return(nFoundFiles);
}
#endif
