/*...........................................................................*/
/*.                  File Name : FileSysDrv.c                               .*/
/*.                                                                         .*/
/*.                       Date : 2009.01.12                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "AllConst.h"
#include "CpuAddr.h"
#include "SysLib.h"
#include "GrLib.h"

#include "ffreturn.h"
#include "filesysp.h"
#include "cdos2plt.h"
#include "FATImage.h"

#include "fsextern.h"

#include "FileSysDrv.h"

#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

//============================================================================
#define MAX_FS_DEVICE				2
static  struct DeviceNew *G_vDevceTable[MAX_FS_DEVICE];
//============================================================================

void  InitFileStsDrv(void)
{
#if !defined(__NAVIONICS__)
      SWord  FSReturn;
      String DevName[4];
      String DevVolumeName[12];

      G_vDevceTable[0] = FAT_GetDevicePtr(1);   // FAT_GetDevicePtr(1) meand emulation of C: drive letter.
      G_vDevceTable[1] = NULL;

      // Install device table. 
      FS_SetDeviceTable(G_vDevceTable);

      sprintf(DevName,"A:");

      // Initialize the File System. 
      FSReturn = FS_Init(DevName);

//    __wrap_printf( "FS_Init Ret Code = %d\n",FSReturn);

//    sprintf(DevVolumeName,"A:UserDisk");
//    if (FSReturn == DEVICE_NOT_FORMATTED)
//       {
//        FSReturn = FS_Format((SByte* )DevVolumeName);
//        if (FSReturn != NO_ERRORS)
//            __wrap_printf("ERROR : Format Fail ( %d )\n",FSReturn);
//        else
//            __wrap_printf("Format OK\n");
//       }
//    else
//       {
//        if (FSReturn == NO_ERRORS)
//           {
//            FSReturn = DoCd((SByte *)DevVolumeName);
//            if (FSReturn != NO_ERRORS)
//                __wrap_printf("ERROR : DoCd Fail = %d\n",FSReturn);
//            else
//                __wrap_printf("DoCd OK\n");
//           }
//        else
//            __wrap_printf("ERROR File System = %d\n",FSReturn);
//       }
#endif
}

//============================================================================
_fs_time_t FS_mkgmtime ( struct _fs_tm *timeptr )
{
      // Convert the passed structure in local time (seconds since Jan 1st 1970).

      _fs_time_t xFsTime;

      return(xFsTime);
}
struct _fs_tm *FS_gmtime ( _fs_time_t *time )
{
      // Convert the passed argument _fs_time_t time into one _fs_tm structure.

      static struct _fs_tm  pFsTm;

      return(&pFsTm);
}
void	INTR_Disable ( void )
{
      SysDisableIRQ();
}
void  INTR_Enable ( void )
{
      SysEnableIRQ();
}
void  TIMER_Delay ( Long MiliSeconds )
{
      SysDelayMiliSec(MiliSeconds);
}
//============================================================================

