#include <stdio.h>
#include "VDM.hpp"
#include "Decoder.hpp"

CVdm::CVdm() : CSentence()
{
}
    
CVdm::CVdm(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CVdm::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_VDM;
}

/******************************************************************************
 * 
 * VDO - VHF Data-link Own-vessel Message
 * 
 * !--VDO,x,x,x,a,s--s,x*hh<CR><LF>
 *        | | | |  |   |
 *        1 2 3 4  5   6
 *
 * 1. Total number of sentences needed to transfer the message, 1 to 9
 * 2. Sentence number, 1 to 9
 * 3. Sequential message identifier, 0 to 9
 * 4. AIS Channel, "A" or "B"
 * 5. Encapsulated ITU-R M.1371 radio message
 * 6. Number of fill-bits, 0 to 5
 *
 ******************************************************************************/
void CVdm::Parse()
{
    m_nTotalNo    = GetFieldInteger(1);   // 1 to 9
    m_nSentNo     = GetFieldInteger(2);   // 1 to 9
    m_nSeqId      = GetFieldInteger(3);   // 0 to 9
    m_chChannel   = GetFieldChar(4);      // "A" or "B"
    GetFieldString(5, m_szMessage);
    m_nFillBitsNo = GetFieldInteger(6);   // 0 to 5
}

void CVdm::GetPlainText(char *pszPlainText)
{
	char szTemp[128];

	pszPlainText[0] = '\0';

	if( m_nTotalNo != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Total number of sentence : %d\n", m_nTotalNo);
		strcat(pszPlainText, szTemp);
	}

	if( m_nSentNo != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Sentence number          : %d\n", m_nSentNo);
		strcat(pszPlainText, szTemp);
	}

	if( m_nSeqId != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Sequential message ID    : %d\n", m_nSeqId);
		strcat(pszPlainText, szTemp);
	}

	if( m_chChannel != NMEA_NULL_CHAR ) {
		sprintf(szTemp, "AIS Channel              : %c\n", m_chChannel);
		strcat(pszPlainText, szTemp);
	}

	sprintf(szTemp, "Encapsulated message     : %s\n", m_szMessage);
	strcat(pszPlainText, szTemp);
}

