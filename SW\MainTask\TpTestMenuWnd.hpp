#include "Wnd.hpp"

#ifndef __TRANSPONDER_TEST_MENU_WND_HPP__
#define __TRANSPONDER_TEST_MENU_WND_HPP__

class CTpTestMenuWnd : public CWnd {
	private:
		int  m_nSelNum;

	public:
		CTpTestMenuWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void DrawWnd(BOOL bRedraw=1/*TRUE*/);
		void DrawSubMenu(int nSelNum);
		void OnKeyEvent(int nKey, DWORD nFlags=0);
		
		void SetFocus(int nFocus)   { m_nFocus = nFocus; }
		void SetSelNum(int nSelNum) { m_nSelNum = nSelNum; }
		int  GetSelNum()            { return m_nSelNum;    }
};

#endif
