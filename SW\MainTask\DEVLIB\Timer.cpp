/*...........................................................................*/
/*.                  File Name : TIMER.CPP                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.07.11                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysGPIO.h"
#include "SysTimer.h"
#include "Uart.hpp"
#include "Timer.hpp"

#include <string.h>

//=============================================================================
cTimer::cTimer(DWORD dBaseAddr,DWORD dMatchVal)
{
      m_dBaseAddr = dBaseAddr;
      m_dMatchVal = dMatchVal;
      m_dCounter  = 0;
}
cTimer::~cTimer(void)
{
}
DWORD cTimer::GetTimerCounter(void)
{
      return(m_dCounter);
}
void  cTimer::SetTimerCounter(DWORD dValue)
{
      m_dCounter = 0;
}
//=============================================================================

//=============================================================================
cTimerSYS::cTimerSYS(DWORD dBaseAddr,DWORD dMatchVal)
         : cTimer(dBaseAddr,dMatchVal)
{
      m_pSysTimer = (xSYS_TIMER *)dBaseAddr;
      SysInitTimer(m_pSysTimer, 1, 1, 0,dMatchVal);
}
cTimerSYS::~cTimerSYS(void)
{
}
DWORD cTimerSYS::ReadTimerValue(void)
{
      return(SysGetTimerCounter(m_pSysTimer));
}
void  cTimerSYS::RunTimerIsrHandler(void)
{
      ++m_dCounter;
      SysClearTimerIntPending(m_pSysTimer);
}
//=============================================================================


