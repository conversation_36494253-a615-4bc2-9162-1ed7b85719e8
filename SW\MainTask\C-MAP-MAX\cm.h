#ifndef __CM
#define __CM

#ifdef DEBUG_INCLUDES
#pragma message( "+++++++++ including cm.h +++++++++" )
#endif

#include "cmaptype.h"
#include "cmg/cmg.h"
#include "cmperspective.h"

/* DEPTH UNITS */
#define CM_FT	0
#define CM_FM	1
#define CM_MT	2
#define CM_DFM	3
#define CM_PB	4

/* ZOOM DEFINES */
#define ZOOM_ZERO		0
#define ZOOM_MUL_2		1
#define ZOOM_DIV_2		-1

/* CHART LEVELS */
#define LEVEL_W 0
#define LEVEL_X 1
#define LEVEL_Y 2
#define LEVEL_Z 3
#define LEVEL_A 4
#define LEVEL_B 5
#define LEVEL_C 6
#define LEVEL_D 7
#define LEVEL_E 8
#define LEVEL_F 9
#define LEVEL_G 10
/* virtual levels */
#define LEVEL_H 11
#define LEVEL_I 12
#define LEVEL_J 13

/* Adds for raymarine integration */
#define CMAP_LOWEST_LEVEL   LEVEL_W
#define CMAP_HIGHEST_LEVEL	LEVEL_J
#define CMAP_INVALID_LEVEL	10000

/*  Settings for Lat/Lon ftoa */
#define	DD_MM			1
#define	DD_MM_SS		2
#define	DD_MM_SS_HH		3
#define DD_MM_HH_C		4
#define C_DD_MM_HH		5
#define DD_MM_TTT_C		6
#define C_DD_MM_TTT		7

/*  Settings for SetUseTrueScale */
#define NO_TRUE_SCALE				0x00
#define LOWER_SCALES_EXPANSION		0x01
#define UPPER_SCALES_COMPRESSION	0x02
#define ALL_SCALES					0x03

/* Setting for the Change Level */
#define PREV_LEVEL -1
#define NEXT_LEVEL 1

/* Cartridge Application Types for cmGetCdgInfo */

#define CDG_TYPE_MARINE        0x01
#define CDG_TYPE_TERRESTRIAL   0x02
#define CDG_TYPE_AERONAUTICAL  0x04
#define CDG_TYPE_SUBMARINE     0x08
#define CDG_TYPE_INLAND_WATERS 0x10
#define CDG_TYPE_ROAD		   0x20
#define CDG_TYPE_UPDATE		   0x40

/* Cartridge Priority Modes */
#define CDG_PRIORITY_MARINE			0
#define CDG_PRIORITY_TERRESTRIAL	1
#define CDG_PRIORITY_AERONAUTIC		2

typedef enum
{
	MARINE_PRESENTATION	=0,
	TERRESTRIAL_PRESENTATION,
	AERONAUTIC_PRESENTATION	
} eCartographicPresentation;

/* Defines for Modes in the cmSetObjMode function */
#define cmMODE_OFF			0
#define cmMODE_ON			1
#define cmMODE_AREA_ON		2
#define cmMODE_CONTOUR_ON	3

typedef enum
    {
	cm_NO_ERROR=0,
	cm_NO_CARTRIDGES,
	cm_BAD_CARTRIDGES,
	cm_CARTRIDGE_CHANGE,
	cm_NO_MORE_LEVELS,
	cm_BAD_HOME_POSITION,
	cm_BAD_OBJECT_ATTRIBUTES,
	cm_GENERAL_GRAPHICS_ERROR,
	cm_GENERAL_CARTRIDGE_ERROR,
	cm_INTERNAL_MALFUNCTION,
	cm_LEVEL_ERROR,
	cm_GENERAL_ERROR,
	cm_INTERRUPTED_DRAW,
	cm_NOTHING_PAINT,
    cm_UPDATE_NOT_FINISHED,
    cm_HOME_OUT_OF_RANGE,
	cm_HARDWARE_KEY_NOT_PRESENT,
	cm_TOO_MANY_CARTRIDGES,
	cm_CDG_NOT_OPEN,
	cm_SCREEN_RES_NOT_SET
    } cmErr;

#define MIN_ZOOM_FACTOR 0.15

#define SI_CDG_REMOVABLE      0x01L

#define	LEVEL_MAP_MASK_W		0x00000001L
#define	LEVEL_MAP_MASK_X		0x00000002L
#define	LEVEL_MAP_MASK_Y		0x00000004L
#define	LEVEL_MAP_MASK_Z		0x00000008L
#define	LEVEL_MAP_MASK_A		0x00000010L
#define	LEVEL_MAP_MASK_B		0x00000020L
#define	LEVEL_MAP_MASK_C		0x00000040L
#define	LEVEL_MAP_MASK_D		0x00000080L
#define	LEVEL_MAP_MASK_E		0x00000100L
#define	LEVEL_MAP_MASK_F		0x00000200L
#define	LEVEL_MAP_MASK_G		0x00000400L
#define	LEVEL_MAP_MASK_H		0x00000800L
#define	LEVEL_MAP_MASK_I		0x00001000L
#define	LEVEL_MAP_MASK_J		0x00002000L

typedef struct
    {
	Long	CheckSum;
	Long	CdgLen;
	Byte	Type;
	Word	Release;
	Word	ReleaseDate;
    String	Name[41] ;
	SLong	MinLatM;
	SLong	MinLonM;
	SLong	MaxLatM;
	SLong	MaxLonM;
	Byte	NumOfLevels;
    String	CdgCode[11];
	Long	SerialNumber;
	Byte	PLUSFormat;
	Long	LevelMap;
    Long    SpecialInfo;
	Byte	ImprovedFormat;
	Byte	PriceClass;
    } sCmInfo;


typedef struct
   {
   Pixel HalfPixWidth ;
   Pixel HalfPixHeight ;
   Double ConversionX ;
   Double ConversionY ;
   SLong CurrMetX ;
   SLong CurrMetY ;
   Pixel ClipOffX ;
   Pixel ClipOffY ;
   Word RotAngle ;
   Word RotType ;
   } sScreenMetrics ;


typedef enum
    {
	AS_COMPLEX,
	AS_SINGLE
    } eComplexObjDrawMode;

typedef enum
    {
	MARINE_PRESENTATION_INTERNATIONAL,
	MARINE_PRESENTATION_AMERICAN
    } eMarinePresentation;

typedef enum
    {
	NAMES_MODE_NO_ROTATION,
	NAMES_MODE_BASIC_ROTATION,
	NAMES_MODE_FULL_ROTATION
    } eNamesRotationMode;

typedef enum
   {
   charDEFAULT = 0,
   charCAPS,
   charSMALL
   } TypeStyle ;

/* Library Release Extended Information Record */

#define LIB_ALPHA		0x01
#define LIB_BETA 		0x02
#define LIB_RELEASE		0x04
#define LIB_BLOCKED		0x08


typedef struct
    {
	Word	Version;
	Word	SubVersion;
	Word	BuildNumber;
	Word	BranchBuildNumber;

	Word	ReleaseStatus;


	Word	ReleaseDay;
	Word	ReleaseMonth;
	Word	ReleaseYear;
    String	Project[33] ;
    } sReleaseInfo;


#ifdef __cplusplus
extern "C"
{
#endif

PRE_EXPORT_H cmErr   IN_EXPORT_H cmInit(void);

typedef PRE_EXPORT_H void (IN_EXPORT_H * cmErrorCallBack)(cmErr error);
PRE_EXPORT_H void IN_EXPORT_H cmSetErrorCallBack(cmErrorCallBack function);

PRE_EXPORT_H cmErr   IN_EXPORT_H cmSetChartRegion( Pixel Left, Pixel Bottom, Pixel Right, Pixel Top );
PRE_EXPORT_C void IN_EXPORT_C cmGetChartRegion( Pixel *Left, Pixel *Bottom, Pixel *Right, Pixel *Top );
PRE_EXPORT_H cmErr   IN_EXPORT_H cmHome(SLong MetY, SLong MetX);
PRE_EXPORT_H cmErr   IN_EXPORT_H cmHomeWithOffset(SLong MetY, SLong MetX, Pixel OffY, Pixel OffX);

PRE_EXPORT_H cmErr  IN_EXPORT_H cmSetViewPort(Pixel minx, Pixel miny,Pixel maxx, Pixel maxy);

PRE_EXPORT_H cmErr   IN_EXPORT_H cmGetHome(SLong *Lat, SLong *Lon);
PRE_EXPORT_H cmErr   IN_EXPORT_H cmChangeLevel( SWord Direction );

typedef enum
{
	eScalesOverZoom=0, /* some compilers do not guarantee 0, 1, order in enums*/
	eZoomStepOverAndUnderZoom=1,
	eScalesOverAndUnderZoom=2
} eChangeLevelMode;

PRE_EXPORT_H cmErr   IN_EXPORT_H cmSetChangeLevelMode( eChangeLevelMode Mode);
PRE_EXPORT_H eChangeLevelMode   IN_EXPORT_H cmGetChangeLevelMode( void );

PRE_EXPORT_H cmErr   IN_EXPORT_H cmChangeLevelFixedScales( SWord Direction, Long FixedScales[] );
PRE_EXPORT_H SWord	 IN_EXPORT_H cmGetLevel(void);
PRE_EXPORT_H String  IN_EXPORT_H cmGetLevelLetter(void);
PRE_EXPORT_H cmErr	 IN_EXPORT_H cmSetLevel(Word index);

PRE_EXPORT_H Bool IN_EXPORT_H cmSetUnderZoomPossible(Bool Value);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetUnderZoomPossible(void);

PRE_EXPORT_H Bool IN_EXPORT_H cmSetCacheBuffer(Byte* BufferPtr,Long BufferSize);
PRE_EXPORT_H void IN_EXPORT_H cmEnableCache( void );
PRE_EXPORT_H void IN_EXPORT_H cmDisableCache( void );

/* Functions for setting the way change level works */
#define ZOOM_FIXED_SCALE_STEPS	1
#define ZOOM_USE_DATA_SCALES	2

typedef struct
{
	Word	ZoomMode;				/* ZOOM_FIXED_SCALE_STEPS or ZOOM_USE_DATA_SCALES */
	Bool	PlotterMode;			/* OFF - pan/change level only where coverage, ON - everywhere */
	SWord	ZoomState;				/* in ZOOM_USE_DATA_SCALES mode, counts if the chart is overzoomed or underzoomed */

	Bool	OverZoomAllowed;		/* flag ON/OFF */
	Double	MinOverZoomFactor;		/* means the minimum zoom factor allowed to allow over zoom step */
	Double	MaxOverZoomFactor;		/* means the maximum zoom factor allowed to allow over zoom step */

	Bool	UnderZoomAllowed;		/* flag ON/OFF */
	Double	MinUnderZoomFactor;		/* means the minimum zoom factor allowed to allow under zoom step */
	Double	MaxUnderZoomFactor;		/* means the maximum zoom factor allowed to allow under zoom step */

	Bool	EmptyOverZoomAllowed;	/* flag ON/OFF means zoom in empty screens while cartography is OFF */

	Long	MinScale;				/* the minimum allowed scale for both ZoomModes */
	Long	MaxScale;				/* the maximum allowed scale for both ZoomModes */
	Double	ZoomStepFactor;			/* the zoom factor between 2 levels in ZOOM_FIXED_SCALE_STEPS mode */


	SWord	ZoomStep;
	SWord	PureOverZoomStep;
	SWord	EmptyOverZoomStep;

	Double No_need_to_zoom_factor;
	Double Over_zoom_step;
}sChangeLevelParams;

PRE_EXPORT_H void	IN_EXPORT_H cmSetChangeLevelParams(sChangeLevelParams *data);
PRE_EXPORT_H void	IN_EXPORT_H cmGetChangeLevelParams(sChangeLevelParams *data);

/*  Context change functions ( allow multiple library instances ) */
PRE_EXPORT_H Word	 IN_EXPORT_H cmInitSwapLibState(void);
PRE_EXPORT_H Bool	 IN_EXPORT_H cmRestoreLibState(SLong SessionID);
PRE_EXPORT_H Bool	 IN_EXPORT_H cmBackupLibState(SLong SessionID);
PRE_EXPORT_H Bool	 IN_EXPORT_H cmEraseLibStateSession(SLong SessionID);
PRE_EXPORT_H SLong	 IN_EXPORT_H cmGetLibStateSession(void);
PRE_EXPORT_H Bool	 IN_EXPORT_H cmSet3dView(Bool val, Bool ShowFPS);
PRE_EXPORT_H Bool	 IN_EXPORT_H cmIs3dEnabled( void );
PRE_EXPORT_H void	 IN_EXPORT_H cmSetXYZ3dPos(int X, int Y, int Z, char Type);
PRE_EXPORT_H void	 IN_EXPORT_H cmSetXYZ3dRot(int X, int Y, int Z, char Type);
PRE_EXPORT_H Word	 IN_EXPORT_H cmSetLand3dLayerExpFac(Word NewVal);
PRE_EXPORT_H Word	 IN_EXPORT_H cmSetWater3dLayerExpFac(Word NewVal);
PRE_EXPORT_H Bool	 IN_EXPORT_H cmSet3dWireFrame(Bool val);
PRE_EXPORT_H Bool	 IN_EXPORT_H cmEnable3dTexture(Bool Value);
PRE_EXPORT_H Bool IN_EXPORT_H cmOGLESInit(void* _hInstance, void* _hWnd);
PRE_EXPORT_H Long IN_EXPORT_H cmGetTotTringTime( void );
PRE_EXPORT_H void IN_EXPORT_H cmGet3dTotVertexesAndTriangles(Long *TotT, Long *TotV);/**/
PRE_EXPORT_H SWord   IN_EXPORT_H cmGet3dFPS( void );

/*  ------------------------------------------------------------- */

/* Cartridge overlap test */
PRE_EXPORT_H void	IN_EXPORT_H cmEnableCellOverlapTest(Bool flag);


typedef enum
    {
    INIT = 0,
    CLEAR,
    RESET,
    DRAW_STANDARD,

	DRAW_1D_MULTI,
	DRAW_1D,

    DRAW_QTAREAS,
    DRAW_COMP,
	DRAW_MULTI_COMP,
	DRAW_MULTI_COMP_MM,
	DRAW_MULTI_COMP_MM_OVERZOOMED,
    DRAW_3D,
	DRAW_MULTI_3D,
	DRAW_ROAD_STEMS,

	/* Outside Viewport Point Drawing steps */
	DRAW_1D_MULTI_OUT,
	DRAW_1D_OUT,
    DRAW_COMP_OUT,
	DRAW_MULTI_COMP_OUT,
	DRAW_DECORATION,
    DRAW_3D_OUT,

    DRAW_GRID,
    DRAW_BOUNDS,
    DRAW_NO_DATA,
    DRAW_PERSPECTIVE,
    IDLE_STATE
    } usScreenStateType ;

PRE_EXPORT_H void	IN_EXPORT_H cmUpdateScreenInit(void);
PRE_EXPORT_H usScreenStateType IN_EXPORT_H cmGetUpdateScreenStep( void );
PRE_EXPORT_H cmErr  IN_EXPORT_H cmUpdateScreenStart( void ) ;
PRE_EXPORT_H cmErr  IN_EXPORT_H cmUpdateScreenNext( void ) ;
PRE_EXPORT_H void	IN_EXPORT_H cmUpdateScreenEnd(void);
PRE_EXPORT_H void IN_EXPORT_H cmSetUpdateScreenScrollMode( Bool Enable );



PRE_EXPORT_H cmErr   IN_EXPORT_H cmUpdateScreen(void);
PRE_EXPORT_H cmErr	IN_EXPORT_H cmMerc2Screen(SLong mX, SLong mY, SLong *sX, SLong *sY) ;
PRE_EXPORT_H void   IN_EXPORT_H cmMerc2ScreenNoTransformed(SLong mX, SLong mY, SLong *sX, SLong *sY);
PRE_EXPORT_H void   IN_EXPORT_H cmScreen2MercNoTransformed(Pixel sX, Pixel sY, SLong *Mx, SLong *My) ;
PRE_EXPORT_H cmErr	IN_EXPORT_H cmMercLine2Screen(SLong *X1, SLong *Y1, SLong *X2, SLong *Y2);
PRE_EXPORT_H cmErr	IN_EXPORT_H cmScreen2Merc(Pixel sX, Pixel sY, SLong *Mx, SLong *My) ;
PRE_EXPORT_H void	   IN_EXPORT_H cmMerc2Degree(SLong My, SLong Mx, Double *Lat, Double *Lon);
PRE_EXPORT_H void	   IN_EXPORT_H cmDegree2Merc(Double Lat, Double Lon, SLong *My, SLong *Mx);

PRE_EXPORT_H void 	IN_EXPORT_H cmGetScreenMetrics(sScreenMetrics *Metrics) ;


PRE_EXPORT_H void    IN_EXPORT_H cmDegree2MercLat(Double Lat, SLong *Mty);
PRE_EXPORT_H void    IN_EXPORT_H cmDegree2MercLon(Double Lon, SLong *Mtx);

PRE_EXPORT_H void    IN_EXPORT_H cmMerc2DegreeLat(SLong Mty, Double *Lat);
PRE_EXPORT_H void    IN_EXPORT_H cmMerc2DegreeLon(SLong Mtx, Double *Lon);


PRE_EXPORT_H cmErr	IN_EXPORT_H cmZoom(SWord);
PRE_EXPORT_H cmErr	IN_EXPORT_H cmSetExpFactor(Double zf);
PRE_EXPORT_H cmErr	IN_EXPORT_H cmSetOverallExpFactor(Double OverExpFactor);
PRE_EXPORT_H Double	IN_EXPORT_H cmGetZoomFactor(void);
PRE_EXPORT_H Double IN_EXPORT_H cmGetMaxZoomFactor(void);
PRE_EXPORT_H Double	IN_EXPORT_H cmGetScreenResolutionFac(void);
PRE_EXPORT_H Long	   IN_EXPORT_H cmGetScale(void);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetDisplayScale(Long scale);
PRE_EXPORT_H Long	   IN_EXPORT_H cmGetDisplayScale(void);
PRE_EXPORT_H Long	   IN_EXPORT_H cmGetZoomedDisplayScale(void);
PRE_EXPORT_H Double	   IN_EXPORT_H cmGetScaleFactor(void);
PRE_EXPORT_H void    IN_EXPORT_H cmSetTextoFont( SWord logicFont );
PRE_EXPORT_H void    IN_EXPORT_H cmSetTextoStyle( CharStyleType *TextStyle, TypeStyle ts );

typedef enum neTextoStyleSource
{
	eTSS_Cartography=0,
	eTSS_TextoStyle=1
}eTextoStyleSource;
PRE_EXPORT_H eTextoStyleSource    IN_EXPORT_H cmSetTextoStyleSource(eTextoStyleSource styleSrc);

PRE_EXPORT_H void    IN_EXPORT_H cmSetSeabedLabelDrawStyle( const DrawStyleType *Style );
PRE_EXPORT_H void	IN_EXPORT_H cmSetInvertedDepareColoring(Bool flag);
PRE_EXPORT_H void	IN_EXPORT_H cmSetDepareColoringRange(Bool State, Long From, Long To, Bool Inverted);

#define TEXT_STYLES	8
PRE_EXPORT_H void	 IN_EXPORT_H cmSetTextoStyleFonts( SWord *StyleFonts );


PRE_EXPORT_H void IN_EXPORT_H cmSetNumTextFont( SWord logicFont ) ;
PRE_EXPORT_H void	   IN_EXPORT_H cmSetGrid(Bool angle);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetBoundings(Bool flag);
PRE_EXPORT_H void      IN_EXPORT_H cmSetBoundingsLetterMode(Bool flag);
PRE_EXPORT_H void      IN_EXPORT_H cmSetBoundingsMode(Word cdg, Bool OnOff);

PRE_EXPORT_C void IN_EXPORT_C CF95_IncPerspectiveHorizon(void);
PRE_EXPORT_C void IN_EXPORT_C CF95_DecPerspectiveHorizon(void);
PRE_EXPORT_C void IN_EXPORT_C CF95_SetPerspectiveCurve(void);
PRE_EXPORT_C void IN_EXPORT_C CF95_IncPerspectiveB(void);
PRE_EXPORT_C void IN_EXPORT_C CF95_DecPerspectiveB(void);
PRE_EXPORT_C void IN_EXPORT_C CF95_IncPerspectiveC(void);
PRE_EXPORT_C void IN_EXPORT_C CF95_DecPerspectiveC(void);
PRE_EXPORT_C void IN_EXPORT_C CF95_GetPerspectiveValue(Double *K,Double *B, Double *C);

PRE_EXPORT_H void IN_EXPORT_H cmSetHumanDataDictionaryState(Bool state);
PRE_EXPORT_H void IN_EXPORT_H cmSetSimplifiedInfoState(Bool state);

PRE_EXPORT_H void	IN_EXPORT_H cmSetRotAngle(SWord flag);
PRE_EXPORT_H SWord	IN_EXPORT_H cmGetRotAngle(void) ;
PRE_EXPORT_H cmErr	IN_EXPORT_H cmSetVirtualCartography(Bool flag);
PRE_EXPORT_H cmErr	IN_EXPORT_H cmSetSkipEmptyLevels(Bool flag);
PRE_EXPORT_H Bool	IN_EXPORT_H cmGetSkipEmptyLevels( void );
PRE_EXPORT_H cmErr	IN_EXPORT_H cmGetLevelScales(Word Level, SLong *sMin, SLong *sMax);
PRE_EXPORT_H cmErr	IN_EXPORT_H cmGetTrueScale(SLong *Scale);
PRE_EXPORT_H void 	IN_EXPORT_H cmSetMixingLevels(Bool flag);
PRE_EXPORT_H Bool	IN_EXPORT_H cmGetMixingLevels( void );

PRE_EXPORT_H Word	   IN_EXPORT_H cmGetNumOfCartridges(void);
PRE_EXPORT_H Word	   IN_EXPORT_H cmGetCurrCdg(void);
PRE_EXPORT_H cmErr	   IN_EXPORT_H cmGetCdgInfo(Word CdgNum, sCmInfo *Info);
PRE_EXPORT_H Word	   IN_EXPORT_H cmGetCdgVersion(Word CdgNum);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetLightSectors(Bool flag);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetLightSectorsDrawLevel(Word Level);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetUseTrueScale(Word flag);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetComplexObjDrawMode(eComplexObjDrawMode mode);
PRE_EXPORT_H eComplexObjDrawMode
                        IN_EXPORT_H cmGetComplexObjDrawMode(void);

PRE_EXPORT_H void		IN_EXPORT_H cmSetMarinePresentationMode(eMarinePresentation pres);
PRE_EXPORT_H eMarinePresentation
						IN_EXPORT_H cmgetMarinePresentationMode(void);


PRE_EXPORT_H void		IN_EXPORT_H cmSetNamesRotationMode(eNamesRotationMode mode);
PRE_EXPORT_H eNamesRotationMode IN_EXPORT_H cmGetNamesRotationMode(void);

PRE_EXPORT_H void IN_EXPORT_H cmEnableLandElevation(Bool flag);
PRE_EXPORT_H void IN_EXPORT_H cmSetLandElevationDisplayScale(Long Scale);

/**
 * Land elevation presentation enum.
 *
 * It represent the enumeration type to specify the presentation of land elevation object \n
 * Used in function cmSetLandElevation().
 * 
 *	\b eAVIO_LAND_ELEVATION used to enable AVIO land elevation type (present only in avio cartridges from version 175).\n
 *	\b eMARINE_LAND_ELEVATION used to enable MAX 2 land elevation (present only in marine cartridges from version 202).\n
 *	\b eCONTOUR_LAND_ELEVATION used to enable MAX land elevation (present only in marine cartridges from version 200).\n
 *	\b eDISABLE_LAND_ELEVATION used to disable all types ofland elevation.\n
 *
 * @version 1.0.
 * 
 * @see	\c cmSetLandElevation()
 *
 * @ingroup DISPLAY_OPTION
 */
typedef enum
{
	eAVIO_LAND_ELEVATION,
	eMARINE_LAND_ELEVATION,
	eCONTOUR_LAND_ELEVATION,
	eDISABLE_LAND_ELEVATION
}eLandElevationMode;

PRE_EXPORT_H void IN_EXPORT_H cmSetLandElevation(eLandElevationMode Mode);
PRE_EXPORT_H void IN_EXPORT_H cmSetLandElevationBlur(Bool Active);
PRE_EXPORT_H void IN_EXPORT_H cmEnableSatImages(Bool flag);
/****************MULTI_LANGUAGE_SUPPORT*************/

#define LANG_CODE_LEN 3
#define LANG_NAME_LEN 15

/*
Language state:
ML_STATE_OFF: the library set the current language to one of the available languages in the list below
ML_STATE_INT: all the strings that have the flag isLocalLanguage set to 0 are displayed in the
international language (english), independently of the setting of the current language
ML_STATE_LOCAL: all the strings that have the flag isLocalLanguage set to 1 are displayed in the
first local language, independently of the setting of the current language
So, this setting allows to override the current language setting, unless it is set to ML_STATE_OFF and
unless the string has isLocalLanguage set other than 0 (international) or 1 (local). E.g. the
objects/attributes/library hard coded strings should have isLocalLanguage set to at least 2, so that
the current language setting is always active and the descriptions are displayed in the current
language. The manager function is cmSetCurrentLanguageState()
*/
#define ML_STATE_OFF 0		//local/international language state OFF
#define ML_STATE_INT 1		//local/international language state INTERNATIONAL
#define ML_STATE_LOCAL 2	//local/international language state LOCAL

/** List of languages available. The cartography and objects/attributes/library hard coded strings
are displayed based on this setting, unless one of the STATE flags is set (see above) **/

#define UNKNOWN_LANG 0
#define ENGLISH_LANG 1
#define AFAR_LANG 2
#define ABKHAZIAN_LANG 3
#define AFRIKAANS_LANG 4
#define AMHARIC_LANG 5
#define ARABIC_LANG 6
#define ASSAMESE_LANG 7
#define AYMARA_LANG 8
#define AZERBAIJANI_LANG 9
#define BASHKIR_LANG 10
#define BYELORUSSIAN_LANG 11
#define BULGARIAN_LANG 12
#define BIHARI_LANG 13
#define BISLAMA_LANG 14
#define BENGALI_LANG 15
#define TIBETAN_LANG 16
#define BRETON_LANG 17
#define CATALAN_LANG 18
#define CORSICAN_LANG 19
#define CZECH_LANG 20
#define WELSH_LANG 21
#define DANISH_LANG 22
#define GERMAN_LANG 23
#define BHUTANI_LANG 24
#define GREEK_LANG 25
#define ESPERANTO_LANG 26
#define SPANISH_LANG 27
#define ESTONIAN_LANG 28
#define BASQUE_LANG 29
#define PERSIAN_LANG 30
#define FINNISH_LANG 31
#define FIJI_LANG 32
#define FAEROESE_LANG 33
#define FRENCH_LANG 34
#define ZULU_LANG 35
#define IRISH_LANG 36
#define GAELIC_LANG 37
#define GALICIAN_LANG 38
#define GUARANI_LANG 39
#define GUJARATI_LANG 40
#define HAUSA_LANG 41
#define HINDI_LANG 42
#define CROATIAN_LANG 43
#define HUNGARIAN_LANG 44
#define ARMENIAN_LANG 45
#define URDU_LANG 46
#define UZBEK_LANG 47
#define INUPIAK_LANG 48
#define INDONESIAN_LANG 49
#define ICELANDIC_LANG 50
#define ITALIAN_LANG 51
#define HEBREW_LANG 52
#define JAPANESE_LANG 53
#define YIDDISH_LANG 54
#define JAVANESE_LANG 55
#define GEORGIAN_LANG 56
#define KAZAKH_LANG 57
#define GREENLANDIC_LANG 58
#define CAMBODIAN_LANG 59
#define KANNADA_LANG 60
#define KOREAN_LANG 61
#define KASHMIRI_LANG 62
#define KURDISH_LANG 63
#define KIRGHIZ_LANG 64
#define VIETNAMESE_LANG 65
#define LINGALA_LANG 66
#define LAOTHIAN_LANG 67
#define LITHUANIAN_LANG 68
#define LATVIAN_LANG 69
#define MALAGASY_LANG 70
#define MAORI_LANG 71
#define MACEDONIAN_LANG 72
#define MALAYALAM_LANG 73
#define MONGOLIAN_LANG 74
#define MOLDAVIAN_LANG 75
#define MARATHI_LANG 76
#define MALAY_LANG 77
#define MALTESE_LANG 78
#define BURMESE_LANG 79
#define NAURU_LANG 80
#define NEPALI_LANG 81
#define DUTCH_LANG 82
#define NORWEGIAN_LANG 83
#define YORUBA_LANG 84
#define OROMO_LANG 85
#define ORIYA_LANG 86
#define PUNJABI_LANG 87
#define POLISH_LANG 88
#define PUSHTO_LANG 89
#define PORTUGUESE_LANG 90
#define QUECHUA_LANG 91
#define RHAETOROMANCE_LANG 92
#define KIRUNDI_LANG 93
#define ROMANIAN_LANG 94
#define RUSSIAN_LANG 95
#define KINYARWANDA_LANG 96
#define VOLAPUK_LANG 97
#define SINDHI_LANG 98
#define XHOSA_LANG 99
#define SERBOCROATIAN_LANG 100
#define SINGHALESE_LANG 101
#define SLOVAK_LANG 102
#define SLOVENIAN_LANG 103
#define SAMOAN_LANG 104
#define SHONA_LANG 105
#define SOMALI_LANG 106
#define ALBANIAN_LANG 107
#define SERBIAN_LANG 108
#define WOLOF_LANG 109
#define SESOTHO_LANG 110
#define SUDANESE_LANG 111
#define SWEDISH_LANG 112
#define SWAHILI_LANG 113
#define TAMIL_LANG 114
#define TEGULU_LANG 115
#define TAJIK_LANG 116
#define THAI_LANG 117
#define TIGRINYA_LANG 118
#define TURKMEN_LANG 119
#define TAGALOG_LANG 120
#define SETSWANA_LANG 121
#define TONGA_LANG 122
#define TURKISH_LANG 123
#define TSONGA_LANG 124
#define TATAR_LANG 125
#define CHINESE_LANG 126
#define UKRAINIAN_LANG 127
#define INTERLINGUA_LANG 128
#define INTERLINGUE_LANG 129
#define LATIN_LANG 130
#define SANSKRIT_LANG 131
#define SISWATI_LANG 132
#define SANGRO_LANG 133
#define OCCITAN_LANG 134
#define TWI_LANG 135
#define FRISIAN_LANG 136

/** Language description structure
*
* It is used to store info about a specific language.
* @ingroup MULTI_LANGUAGE_SUPPORT
*/
typedef struct nsLanguageDescription
{ 
    /** Language short name. Ex: "EN", "IT". */
	String code[LANG_CODE_LEN];
    /** Language name. Ex: "English", "Italiano".  */
	UnicodeString description[LANG_NAME_LEN];
    /** Language identifier */
	Byte   LanguageID;
}sLanguageDescription;

PRE_EXPORT_H Byte IN_EXPORT_H cmGetNumOfAvailableLanguages(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetFirstAvailableLanguage(sLanguageDescription *Lang);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetNextAvailableLanguage(sLanguageDescription *Lang);
PRE_EXPORT_H Bool IN_EXPORT_H cmSetLanguage(Byte LanguageID);
PRE_EXPORT_H Bool IN_EXPORT_H cmSetLanguageState(Byte LanguageState);
PRE_EXPORT_H Byte IN_EXPORT_H cmGetLanguage(void);
PRE_EXPORT_H Byte IN_EXPORT_H cmGetLanguageState(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmIsLanguageAvailable(const String *code);
PRE_EXPORT_H Bool IN_EXPORT_H cmIsLanguageIdAvailable(Byte languageID);
PRE_EXPORT_H Byte IN_EXPORT_H cmGetDefaultLanguageID(void);
/****************************************************/

/**************TEXT & ICON ENLARGMENT**************/
typedef enum
{
	eNORMAL_Size,
	eMEDIUM_Size,
	eLARGE_Size
}eIconTextSize;

PRE_EXPORT_H Float IN_EXPORT_H cmSetPlaceNameSizeMultiplier(Float m);
PRE_EXPORT_H Float IN_EXPORT_H cmSetIconTextSize(eIconTextSize Size);
PRE_EXPORT_H eIconTextSize IN_EXPORT_H cmGetIconTextSize(void);
PRE_EXPORT_H Float IN_EXPORT_H cmSetIconTextExpansion(Float Expansion);

/***************************************************/
PRE_EXPORT_H void IN_EXPORT_H cmSet3dModellingWait(Long Wait);
PRE_EXPORT_H void IN_EXPORT_H cmSet3dOpenGlWait(Long Wait);

/* display */
PRE_EXPORT_H void	   IN_EXPORT_H cmSetCoverage(Word Level, Bool flag);
PRE_EXPORT_H Bool	   IN_EXPORT_H cmTestCoverage(Word Level);
PRE_EXPORT_H Bool	   IN_EXPORT_H cmIsEmptyLevel(void);
PRE_EXPORT_H void	   IN_EXPORT_H CF95_Merc2Degree(SLong Mty, SLong Mtx, Double *Lat, Double *Lon);
PRE_EXPORT_H void	   IN_EXPORT_H CF95_Degree2Merc(Double Lat, Double Lon, SLong *Mty, SLong *Mtx);
PRE_EXPORT_H void      IN_EXPORT_H CF95_GetCurrLevelStr(String *buff);
PRE_EXPORT_H void	   IN_EXPORT_H CF95_GetHomeM(SLong *My, SLong *Mx);
PRE_EXPORT_H void	   IN_EXPORT_H CF95_GetHomeG(Double *Gy, Double *Gx);
PRE_EXPORT_H void	   IN_EXPORT_H CF95_SetLLStringMode(Byte Mode);
PRE_EXPORT_H UnicodeString *
                        IN_EXPORT_H CF95_LatFtoa(Float what, UnicodeString *res, Byte Mode);
PRE_EXPORT_H UnicodeString *
                        IN_EXPORT_H CF95_LonFtoa(Float what, UnicodeString *res, Byte Mode);
PRE_EXPORT_H void	   IN_EXPORT_H CF95_GoHome(void);
PRE_EXPORT_H char	   IN_EXPORT_H CF95_LevelToLetter(SWord level);
PRE_EXPORT_H SWord	IN_EXPORT_H CF95_LetterToLevel(char level);

/* Preslib Config Functions */
PRE_EXPORT_H void    IN_EXPORT_H cmSetClearScreenPattern(FillStyleType *cls);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetDepthAreaRanges(Word n, SWord *ranges, SByte *col);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetSoundgDecimalMode(Bool Mode);
PRE_EXPORT_H cmErr	IN_EXPORT_H cmSetDatumOffset(SLong aLatOff, SLong aLonOff);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetMixingLevelsClearScreenPattern(FillStyleType *fst);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetExist(SWord Label, Bool Flag);
PRE_EXPORT_H Bool	   IN_EXPORT_H cmGetExist(SWord Label);
PRE_EXPORT_H Bool	IN_EXPORT_H cmForceDisablePoiOnLineACL(Bool Flag);

PRE_EXPORT_H void IN_EXPORT_H cmBackupObjectsStatus( Bool TurnOff );
PRE_EXPORT_H void IN_EXPORT_H cmRestoreObjectsStatus( void );

PRE_EXPORT_H void	   IN_EXPORT_H cmSetObjMode(SWord Label, Word Mode);
PRE_EXPORT_H Word	   IN_EXPORT_H cmGetObjMode(SWord Label);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetRefDepth(SWord depth);
PRE_EXPORT_H Double	IN_EXPORT_H cmGetExpFactor(void);
PRE_EXPORT_H Double IN_EXPORT_H cmGetOverallExpFactor(void);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetAutoExpFactor(Double fac);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetChartLock(Bool flag);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetOverZoom(Bool flag);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetZoomStepFactor( Double ZoomFactor );
PRE_EXPORT_H void	   IN_EXPORT_H cmGetReleaseDate(SWord, char *);
PRE_EXPORT_H SWord	   IN_EXPORT_H cmGetOverZoomState(void);
PRE_EXPORT_H void      IN_EXPORT_H cmSetSoundgRange(SWord, SWord );
PRE_EXPORT_H void    IN_EXPORT_H cmSetUnderwaterRange(SWord, SWord );
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetBeaconName(Bool Flag);
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetBuoyName(Bool Flag);
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetTowersName(Bool Flag);
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetUnderwaterRocksDepthDisplay(Bool Flag);
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetObstructionsDepthDisplay(Bool Flag);
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetDiffusersDepthDisplay(Bool Flag);
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetWrecksDepthDisplay(Bool Flag);
PRE_EXPORT_H void	   IN_EXPORT_H cmGetSoundgRange(SWord *MinValue, SWord *MaxValue);
PRE_EXPORT_H void	   IN_EXPORT_H CF95_SetInterruptDisplay(Bool);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetInterruptDisplay(Bool);
PRE_EXPORT_H void	   IN_EXPORT_H cmSetWisePan(Bool flag);
PRE_EXPORT_H cmErr   IN_EXPORT_H cmWisePan(SLong My, SLong Mx);
PRE_EXPORT_H Bool    IN_EXPORT_H cmPointInCharts(SLong X, SLong Y, SWord LvlIndex);
PRE_EXPORT_H void    IN_EXPORT_H cmRefreshLibSettings(void);
PRE_EXPORT_H void    IN_EXPORT_H cmSetCursorLock(Bool flag);
PRE_EXPORT_H void    IN_EXPORT_H cmSetDepthLabels(Bool flag);
PRE_EXPORT_H void    IN_EXPORT_H cmSetHeightLabels(Bool flag);
PRE_EXPORT_H void	 IN_EXPORT_H cmSetBridgeLabels(Bool flag);
PRE_EXPORT_H Double  IN_EXPORT_H cmGeomDistMerc( SLong Lat1, SLong Lon1, SLong Lat2, SLong Lon2 );
PRE_EXPORT_H Double  IN_EXPORT_H cmGeomDistDeg( Double lat1, Double lon1 , Double lat2, Double lon2);
PRE_EXPORT_H void    IN_EXPORT_H cmSetBackgroundClearScreenPattern(FillStyleType *cls);
PRE_EXPORT_H void    IN_EXPORT_H cmSetOneObjectToDraw(Word Label);
PRE_EXPORT_H void    IN_EXPORT_H cmSetSecondObjectToDraw(Word Label);
PRE_EXPORT_H cmErr   IN_EXPORT_H cmUpdateScreenWithOneObject(Bool clearscreen);
PRE_EXPORT_H cmErr   IN_EXPORT_H cmUpdateScreenWithTwoObject(Bool clearscreen);
PRE_EXPORT_H void    IN_EXPORT_H cmSetLightLetters(Bool flag);
PRE_EXPORT_H void    IN_EXPORT_H cmSetLightRadius(SWord r);
PRE_EXPORT_H Bool    IN_EXPORT_H cmSetMeasuringUnitConversion(SWord GreatCode, SWord MeasCode);
PRE_EXPORT_H void    IN_EXPORT_H cmClose(void);
PRE_EXPORT_H void    IN_EXPORT_H cmSetBoundingsDrawStyle(DrawStyleType *dst);
PRE_EXPORT_H void    IN_EXPORT_H cmSetRedrawInProgressFunction(void (*fun)(void));
PRE_EXPORT_H void	IN_EXPORT_H cmDrawObjectIcon(Pixel x, Pixel y, Word label);
PRE_EXPORT_H void   IN_EXPORT_H cmDrawObjectIconEx(Pixel x, Pixel y, Word ObjLabel, Word AttrLabel, SLong AttrValue);

PRE_EXPORT_H Bool	IN_EXPORT_H cmGetObjectDescriptor(SWord Label, UnicodeString * name, SWord BufLen);
PRE_EXPORT_H Bool   IN_EXPORT_H cmGetAttributeDescriptor(SWord Label, UnicodeString * name, SWord BufLen);

PRE_EXPORT_H void	IN_EXPORT_H cmEnableDrawAreaContours(Bool flag);
PRE_EXPORT_H void	IN_EXPORT_H cmEnableDrawAreas(Bool flag);

PRE_EXPORT_H Word	IN_EXPORT_H cmGetNumOfRoyalties(Word cdg);
PRE_EXPORT_H Bool	IN_EXPORT_H cmGetRoyalty(Word cdg, Word index,  UnicodeString *RoyName, Word BufLen);

PRE_EXPORT_H Long	IN_EXPORT_H cmGetLibraryVersionNumber(void);
PRE_EXPORT_H void	IN_EXPORT_H cmGetLibraryReleaseInfo(sReleaseInfo* RI);

PRE_EXPORT_H void	IN_EXPORT_H cmSetAirspacesLevelLimit(SWord limit);
PRE_EXPORT_H SWord  IN_EXPORT_H cmGetAirspacesLevelLimit(void);

/*utility*/

PRE_EXPORT_H Bool IN_EXPORT_H cmCheckCheckSum( SWord CartridgeNum );
PRE_EXPORT_H Long IN_EXPORT_H cmCalcCheckSum( SWord CartridgeNum );
PRE_EXPORT_H Bool IN_EXPORT_H cmIsCartridgeOver1GB( SWord CartridgeNum );

PRE_EXPORT_H void IN_EXPORT_H cmAscii2Morse(char *string);
PRE_EXPORT_H void IN_EXPORT_H cmBinaryMorse2AsciiMorse(char c, char* string);

/* POI modes */
#define POI_SET_ALL		0

#define POI_ICON_ONLY	0
#define POI_TEXT_ONLY	1
#define POI_ICON_TEXT	2

PRE_EXPORT_H void IN_EXPORT_H cmSetPOIMode(Word Label,Byte Mode);
PRE_EXPORT_H Byte IN_EXPORT_H cmGetPOIMode(Word Label);


PRE_EXPORT_H void IN_EXPORT_H cmSetSimpleROADPTDrawMode(Bool mode);
PRE_EXPORT_H void IN_EXPORT_H cmDrawRoadDirection(Bool mode);

PRE_EXPORT_H void IN_EXPORT_H cmSetSelectedRoadName(Word AttrLabel,UnicodeString *Name);
PRE_EXPORT_H void IN_EXPORT_H cmSetSelectedRoadStyle(DrawStyleType *dst);

PRE_EXPORT_H void IN_EXPORT_H cmSetROADPTLabels(Bool flag);

/* ROADPT stem modes */
#define ROADPT_STEM_OFF			0
#define ROADPT_STEM_ON			1

PRE_EXPORT_H void IN_EXPORT_H  cmSetROADPTStemMode(Word StemMode);

/* ROADPT Presentation Library modes */
#define ROADPT_PL_DEFAULT		0
#define ROADPT_PL_SMART			1

PRE_EXPORT_H void IN_EXPORT_H  cmSetROADPTPLMode(Word PLMode);

/* AeroID Font Style defines */
#define		ID_DEFAULT_STYLE	0
#define		ID_MEDIUM_STYLE		1
#define		ID_LARGE_STYLE		2
#define		ID_SMALL_STYLE		3
#define		ID_HUGE_STYLE		4

/*****************************************************************/
#define PORT_MARINA_NAME_ALWAYS_ON		0xFFFFFFFF
#define PORT_MARINA_NAME_ALWAYS_OFF		0x0

PRE_EXPORT_H void IN_EXPORT_H cmSetDrawPortName(Bool flag);
PRE_EXPORT_H void IN_EXPORT_H cmSetPortInfoNameScale(Long Scale);
/*****************************************************************/

PRE_EXPORT_H void IN_EXPORT_H cmSetAeroID(Bool flag);
PRE_EXPORT_H void IN_EXPORT_H cmSetAeroIDFontStyle(Word Style);
PRE_EXPORT_H void IN_EXPORT_H cmSetAeroGrayMode(Bool mode);
PRE_EXPORT_H void IN_EXPORT_H cmSetAeroGrayModeStartLevel(SWord Level);
PRE_EXPORT_H void IN_EXPORT_H cmSetMCMMEnableStep(SWord Step);

#define NP_TYPE_LAND	0
PRE_EXPORT_H void IN_EXPORT_H cmSetNightPalette(Bool mode, Word Type);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetNightPalette(Word *Type);

PRE_EXPORT_H void IN_EXPORT_H cmSetAutoCSScale(SLong Scale);
PRE_EXPORT_H void IN_EXPORT_H cmSetAutoCSMode(Bool OnOff);

PRE_EXPORT_H SLong IN_EXPORT_H cmGetAutoCSScale(void);
PRE_EXPORT_H Bool  IN_EXPORT_H cmGetAutoCSMode(void);

PRE_EXPORT_H void	IN_EXPORT_H cmSetZoomStep(SWord ZoomStep);
PRE_EXPORT_H SWord	IN_EXPORT_H cmGetZoomStep(void);

PRE_EXPORT_H void  IN_EXPORT_H cmSetCartridgePriority(Word mode);
PRE_EXPORT_H Word  IN_EXPORT_H cmGetCartridgePriority(void);
PRE_EXPORT_H eCartographicPresentation IN_EXPORT_H cmGetCartographicPresentationMode(void);

PRE_EXPORT_H Word	IN_EXPORT_H cmGetCdgRealIndex(Word cdg);

PRE_EXPORT_H void IN_EXPORT_H cmSetObjOverlap(Bool mode);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetObjOverlap(void);

typedef PRE_EXPORT_H void (IN_EXPORT_H * RadarFunPtr_t)(void* parameter);
typedef void* RadarParPtr_t;
PRE_EXPORT_H void IN_EXPORT_H cmSetRadarFunPtrAfterAllAreas(RadarFunPtr_t rfp,RadarParPtr_t rpp);
PRE_EXPORT_H void IN_EXPORT_H cmSetRadarFunPtrAfterBaseAreas(RadarFunPtr_t rfp,RadarParPtr_t rpp);

//## EKP-IV Integration Start
#define EA_AIRLINE_AIRWAY				0
#define EA_CONTROL_AIRWAY				1
#define EA_DIRECT_ROUTE_AIRWAY			2
#define EA_OFFICIAL_DESIGNATED_AIRWAY	3
#define EA_RNAV_AIRWAY					4

#define EA_LAST_AIRWAY_CLASS			EA_RNAV_AIRWAY

#define EA_MAX_AIRWAY_ROUTE_IDENTIFIER_LEN		7

PRE_EXPORT_H void IN_EXPORT_H cmSetSelectedAirway(const UnicodeString* routeIDT);
PRE_EXPORT_H void IN_EXPORT_H cmClearSelectedAirway(void);

//## EKP-IV Integration End
/* clipping functions */
#define CLIP_INSIDE_SCREEN  1
#define CLIP_OUTSIDE_SCREEN 0

PRE_EXPORT_H void IN_EXPORT_H cmSetClipRect(SLong minx, SLong miny, SLong maxx, SLong maxy);
PRE_EXPORT_H SWord	IN_EXPORT_H cmClip( SLong *X1, SLong *Y1, SLong *X2, SLong *Y2 );

/*Drawing Function*/

/**
 * Geometry enum.
 *
 * It represent the enumeration type to specify the format of coordinate
 *  that will be passed to cmMoveTo and cmLineTo functions.
 *
 * @version 1.0.
 * 
 * @see	\c cmDrawPointInit(); \c cmPolyLineInit(); \c cmDrawPolygonInit();
 *		\c cmMoveTo(); \c cmLineTo();
 *
 * @ingroup CM_GRAPHIC_FUNCTIONS
 */
typedef enum
{
	eFOC_UNDEFINED=0,	///< Value only for internal use. The use of this can produce unexpected behaviour
	eFOC_PIXELS,		///< Pixel format coordinates, chart's transformation are not applied
	eFOC_TPIXELS,		///< Pixel format coordinates, chart's transformation are applied
	eFOC_METERS		///< Meters format coordinates, chart's transformation are applied

}eFORMAT_OF_COORDINATES;

PRE_EXPORT_H void IN_EXPORT_H cmDrawPointInit	( eFORMAT_OF_COORDINATES foc );
PRE_EXPORT_H void IN_EXPORT_H cmDrawPointStart	( void );
PRE_EXPORT_H void IN_EXPORT_H cmDrawPointEnd	( void );

PRE_EXPORT_H void IN_EXPORT_H cmPolyLineInit	( eFORMAT_OF_COORDINATES foc, Bool noWideLine );
PRE_EXPORT_H void IN_EXPORT_H cmPolyLineStart	( void );
PRE_EXPORT_H void IN_EXPORT_H cmPolyLineEnd		( void );
PRE_EXPORT_H void IN_EXPORT_H cmPolyLineFlush	( void );

PRE_EXPORT_H void IN_EXPORT_H cmDrawPolygonInit		( eFORMAT_OF_COORDINATES foc );
PRE_EXPORT_H void IN_EXPORT_H cmDrawPolygonStart	( void );
PRE_EXPORT_H void IN_EXPORT_H cmDrawPolygonEnd		( void );
PRE_EXPORT_H void IN_EXPORT_H cmDrawPolygonFlush	( void );

PRE_EXPORT_H Bool IN_EXPORT_H cmMoveToWithOffset( SLong x,SLong y ,Pixel offsetX,Pixel offsetY);
PRE_EXPORT_H Bool IN_EXPORT_H cmMoveTo	(SLong x,SLong y);
PRE_EXPORT_H void IN_EXPORT_H cmLineTo	(SLong x,SLong y);

PRE_EXPORT_H Bool IN_EXPORT_H cmPointInScreen(SLong x,SLong y);

/* LIGHTS blinking functions */

/*Drawing Function*/
/**
 * Light presentation enum.
 *
 * It represent the enumeration type to specify the presentation of light object \n
 * Used in function cmSetLightAndSectorFeature().
 * 
 *	\b eLIGHT_OFF_SECTOR_OFF means light and sector not drawn,\n
 *	\b eLIGHT_ON_SECTOR_OFF means light is drawn but not sectors,\n
 *	\b eLIGHT_ON_SECTOR_ON means light and sectors drawn,\n
 *	\b eLIGHT_FLASH_SECTOR_OFF means flashing light and sector not drawn,\n
 *	\b eLIGHT_FLASH_SECTOR_ON means flashing light and sector drawn,\n
 *
 * @version 1.0.
 * 
 * @see	\c cmSetLightAndSectorFeature()
 *
 * @ingroup DISPLAY_OPTION
 */
typedef enum
{
	eLIGHT_OFF_SECTOR_OFF = 0,
	eLIGHT_ON_SECTOR_OFF,
	eLIGHT_ON_SECTOR_ON,
	eLIGHT_FLASH_SECTOR_OFF,
	eLIGHT_FLASH_SECTOR_ON
} eLIGHT_SECTOR_SETTING;

PRE_EXPORT_H eLIGHT_SECTOR_SETTING IN_EXPORT_H cmSetLightAndSectorFeature(eLIGHT_SECTOR_SETTING Mode);


PRE_EXPORT_H Word IN_EXPORT_H cmInitLightAnimation(Long *BuffPtr, SLong BuffLenght, Word TickMillis);
PRE_EXPORT_H void IN_EXPORT_H cmSetFixInfo(SLong BoatPosX, SLong BoatPosY, SWord BoatAngle, SWord BoatSpeed);
PRE_EXPORT_H void IN_EXPORT_H cmLightAnimationTick(Bool Mode);
PRE_EXPORT_H void IN_EXPORT_H cmSetLightAnimation(Bool flag);
PRE_EXPORT_H Word IN_EXPORT_H cmGetBlinkingImageSize(eIconTextSize Mode);

/* Value added data functions */

PRE_EXPORT_H void IN_EXPORT_H cmTurnValueAddedData(Bool Mode);
PRE_EXPORT_H Bool IN_EXPORT_H cmIsValueAddedDataObject(Long DBPtr, SWord cdgNum);
PRE_EXPORT_H Bool IN_EXPORT_H cmSetEnhancedAntiClutter(Bool Active);
PRE_EXPORT_H Bool IN_EXPORT_H cmIsAnyObjRemovedByEnhancedAntiClutter(void);
PRE_EXPORT_H Bool IN_EXPORT_H cmSetDynamicColoring(Bool Active);
PRE_EXPORT_H Bool IN_EXPORT_H cmSetElevationDynamicColoring(Bool Active);
PRE_EXPORT_H Bool IN_EXPORT_H cmSetDepthAreasDynamicColoring(Bool Active);
PRE_EXPORT_H Byte IN_EXPORT_H cmGetNumOfDynamicColoringRanges(SWord Label);
PRE_EXPORT_H Bool IN_EXPORT_H cmGetDynamicColoringRangeAndColor(SWord Label, Byte Index, Float *Range, SWord *Color);


/*True Type Font Functions*/

PRE_EXPORT_H void IN_EXPORT_H cmSetUseTrueTypeFonts(Bool Active);
PRE_EXPORT_H void IN_EXPORT_H cmEnableAntiAliasingText(Bool b);
PRE_EXPORT_H void IN_EXPORT_H cmEnableAntiAliasingSymbol(Bool b);

PRE_EXPORT_H Bool IN_EXPORT_H cmDrawString(const void *str);
PRE_EXPORT_H void IN_EXPORT_H cmGetTextExtent(UnicodeString *string, SWord *SX, SWord *SY, SWord *BL);
PRE_EXPORT_H void IN_EXPORT_H cmGetTextExtentAndMetrics(UnicodeString *string, TextExtentAndMetrics_t *EM);
PRE_EXPORT_H void IN_EXPORT_H cmGetTextABCWidths(UnicodeString *string, SWord *A,SWord *B, SWord *C);

PRE_EXPORT_H Bool IN_EXPORT_H cmDrawUnicodeString(const void *UnicStr);
PRE_EXPORT_H void IN_EXPORT_H cmGetTextUnicodeExtent(const void *UnicStr, SWord *SX, SWord *SY, SWord *BL);
PRE_EXPORT_H void IN_EXPORT_H cmGetTextUnicodeExtentAndMetrics(const void *UnicStr, TextExtentAndMetrics_t *EM);
PRE_EXPORT_H void IN_EXPORT_H cmGetTextUnicodeABCWidths(const void *UnicStr, SWord *A,SWord *B, SWord *C);



/* -------------------------------------------------------------------------- */
/* Special 3D functions	                                                      */
/* -------------------------------------------------------------------------- */
#define IS_3D_MOVETO	1
#define IS_3D_LINETO	2
typedef struct Data3D
{
	SWord Label;
	SWord Info;
	Double	X;	/* Degrees */
	Double	Y;	/* Degrees */
	Double	Z;	/* Meters */
	SLong px;
	SLong py;
} sData3D;

PRE_EXPORT_H void IN_EXPORT_H cmStart3DExtraction(sData3D *arr3D, SLong *arr3Dsize);
PRE_EXPORT_H void IN_EXPORT_H cmStop3DExtraction(void);

/* -------------------------------------------------------------------------- */
/* Repeater C-Card Functions	                                              */
/* -------------------------------------------------------------------------- */
void cmSetRepeatedCartridgeState(long CdgCode, Bool state );
void cmGetRepeatedCartridgeCodeState(SWord Id, long *CdgCode, Bool *state, long *MagicWord);
void cmSetRepeatedCartridgeStateId(SWord Id, Bool state);
void cmSetRepeatedCartridgeCodeState(SWord Id, long CdgCode, Bool state, long MagicWord);

/* -------------------------------------------------------------------------- */
/* Special conversion functions	                                              */
/* -------------------------------------------------------------------------- */
/* STRUCTURE DEFINITION                                                       */
/* -------------------------------------------------------------------------- */

#define FRAC_SHORT_INT

typedef enum 
{
	MM_SS,
	MM_MM,
	MM_MMM,
	MM_MMMM,

	MAX_FRAC_TYPE			/* this must be the latest item	*/
	
} eFracType;


typedef struct GCoord             /* generic coordinate type */
{
	SWord deg;
	SWord min;
#ifdef FRAC_SHORT_INT
	SWord frac;             /* seconds, Hundreds or Thousandths */
#else
	SLong frac;             /* seconds, Hundreds or Thousandths or Tenthousanths*/
#endif
	SByte cardinal;
	eFracType FT;
} DMxC;

/* -------------------------------------------------------------------------- */
/* FUNCTIONS DEFINITION                                                       */
/* -------------------------------------------------------------------------- */

PRE_EXPORT_H void IN_EXPORT_H cmDMxCLat2Degree ( DMxC *Lat, Double *DegLat, eFracType FracType );
PRE_EXPORT_H void IN_EXPORT_H cmDMxCLon2Degree ( DMxC *Lon, Double *DegLon, eFracType FracType );
PRE_EXPORT_H void IN_EXPORT_H cmDegree2DMxCLat ( Double DegLat, DMxC *Lat, eFracType FracType );
PRE_EXPORT_H void IN_EXPORT_H cmDegree2DMxCLon ( Double DegLon, DMxC *Lon, eFracType FracType );

PRE_EXPORT_H void IN_EXPORT_H cmDegree2DMxC ( Double DegLat, Double DegLon, DMxC *Lat, DMxC *Lon, eFracType FracType );
PRE_EXPORT_H void IN_EXPORT_H cmDMxC2Degree ( DMxC *Lat, DMxC *Lon, Double *DegLat, Double *DegLon, eFracType FracType );


PRE_EXPORT_H void IN_EXPORT_H cmSetSelectedObjectDbPtr(Long ObjPtr, Word CdgNum);
PRE_EXPORT_H void IN_EXPORT_H cmGetSelectedObjectDbPtr(Long *ObjPtr, Word *CdgNum);

/* Hard Key test. */
PRE_EXPORT_H Bool IN_EXPORT_H cmTestHardKey ( void );
PRE_EXPORT_H void IN_EXPORT_H cmGetScreenSizeFor3D(SLong *MinX,SLong *MinY,SLong *Width,SLong *Height);
PRE_EXPORT_H void IN_EXPORT_H cmGetFailurePerc(Float* perc,Long* totCell,Long* totFailures,Long* numCirRep,Long* numSeqRep);

/******************************* Temporary function definition ***********************************/
/* These functions will be removed when cmMerc2Screen() and cmScreen2Merc() supports re-entrant. */
PRE_EXPORT_H void IN_EXPORT_H CF95_Add180(SLong *Mx) ;
PRE_EXPORT_H SLong IN_EXPORT_H CF95_Sin(SLong Mul, SWord angle);
PRE_EXPORT_H SLong IN_EXPORT_H CF95_Cos(SLong Mul, SWord angle);
PRE_EXPORT_H SWord IN_EXPORT_H CF95_SinW(SWord Mul, SWord angle);
PRE_EXPORT_H SWord IN_EXPORT_H CF95_CosW(SWord Mul, SWord angle);

typedef enum neFindObjectsMode
{
	eFOM_FindAllAvailableObjects=0,
	eFOM_FindAllEnabledObjects=1,
}eFindObjectsMode;

PRE_EXPORT_H eFindObjectsMode IN_EXPORT_H cmSetFindObjectsMode(eFindObjectsMode lfom);


#ifdef __cplusplus
}
#endif

#endif
