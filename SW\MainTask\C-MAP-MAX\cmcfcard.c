/*##################################################################
FILE    : CMCFCARD.C

USE     :	Cartographic File Technologies Library.
PROJECT : C-Map Library.

AUTHOR  : GE[050120].
UPDATED : 
##################################################################

*/

/* System include */
#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>

/* Local include  */
#include "cmcfcard.h"

#include "DataType.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysSDCD.h"
#include "SdCardDRV.h"
#include "osfile.h"

//==================================================================
FDEV_TYPE	F_Detect(char *DriveLetter,Bool *Changed);
SLong F_DeviceInfo(char *DriveLetter,FDEVInfoType *FDevInfo);
//==================================================================
#define _FILE_HANDLE_FOR_WWB_          0x7ffffff0
//==================================================================

//==================================================================
DWORD  G_dCmapCardChangedMode = 0;
//==================================================================

//==================================================================
static DWORD GetFileOpenModeFlag(char *pMode)
{
       DWORD dOpenMode;

       dOpenMode = 0;
       while (*pMode)
             {
              if (*pMode == 'r' || *pMode == 'R') dOpenMode |= OSF_READ_ONLY;
              if (*pMode == 'w' || *pMode == 'W') dOpenMode |= (OSF_READ_WRITE | OSF_CREATE);
              if (*pMode == 'a' || *pMode == 'A') dOpenMode |= (OSF_READ_WRITE | OSF_CREATE);
              ++pMode;
             }
       if (dOpenMode == 0)
           dOpenMode |= OSF_READ_ONLY;

       return(dOpenMode);
}
//==================================================================

/* fh ##############################################################
FUNCTION : F_Open().
USE      : Open a file.

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : filename: full name of the file (example "C:\\CHARTS\EMC12303.MSR").
			Valid Drive Letter could be "C:", "D:", "E:", "F:", "G:", "H:", ...
			mode: file open mode: 
			'r' = read only (to preserve the SD card could be better the FileSystem 
			does not update the last access time in the directory strcuture).
			'b' = binary mode,
			'w' = write mode (for future use),
			'h' = hidden file.
OUTPUT   : None.
GLOBAL   : None.
RETURN   : Result of the operation:
			= 0 : File found
			< 0 : Error code
NOTE     :
##################################################################*/
SLong F_Open ( char *filename, char *mode )
{
//    SLong	RetValue;
//
//    RetValue = (SLong)fopen(filename,mode);
//    if (!RetValue)
//        RetValue = -1;
//    return(RetValue);

      FHANDLE hFile;
      DWORD dOpenMode;

      if ((filename[0] == 'B' && filename[1] == ':') ||
          (filename[0] == 'W' && filename[1] == 'W' && filename[2] == 'B'))
         {
          return(_FILE_HANDLE_FOR_WWB_);
         }

      dOpenMode = GetFileOpenModeFlag(mode) | OSF_CACHE_DATA;
      hFile = OsfOpen(filename,dOpenMode);
//__wrap_printf("F_Open = %s (mode = %s) ==> FileHandle = %08x\n",filename,mode,hFile);
      if (hFile < OSF_NO_ERROR)
          hFile = -1;

      return(hFile);
}
/* fh ##############################################################
FUNCTION : F_Close().
USE      : Read data from file.

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : Handle: File Handle returned by F_Open() function.
OUTPUT   : None.
GLOBAL   : None.
RETURN   : Result of the operation:
			= 0 : OK
			< 0 : Error code
NOTE     :
##################################################################*/
SLong F_Close ( SLong Handle )
{
//    SLong RetValue;
//
//    RetValue = fclose( (FILE*) Handle );
//    return(RetValue);

      SLong nRetValue;

      if (Handle == _FILE_HANDLE_FOR_WWB_)
          return(0);

//__wrap_printf("F_Close = %08x\n",Handle);
      nRetValue = OsfClose(Handle);
      if (nRetValue < OSF_NO_ERROR)
          nRetValue = -1;

      return(nRetValue);
}
SLong F_Delete(char *filename)
{
//__wrap_printf("F_Delete = %s\n",filename);
      if (OsfFDelete(filename) < OSF_NO_ERROR)
          return(-1);

      return(0);
}
/* fh ##############################################################
FUNCTION : F_Get().
USE      : Read data from file.

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : Handle: File Handle returned by F_Open() function,
			Address: position referred to the beginning of the file,
			Size: Number of bytes to read from the file (usually the block 
			size is 512 bytes).
OUTPUT   : Buffer: buffer filled with the data read from the specific file.
GLOBAL   : None.
RETURN   : Result of the operation:
			>= 0 : Number of bytes read from the file (Size) in case of success
			<  0 : Error code
NOTE     :
##################################################################*/
SLong F_Get ( SLong Handle, Long Address, Byte *Buffer, SLong Size )
{
//    SLong	Byte_Read = 0;
//    int   SeekResult= 0;
//
//    SeekResult = fseek( (FILE*)Handle, Address, SEEK_SET );
//    if (SeekResult == 0)
//        Byte_Read = fread( Buffer, sizeof(Byte), Size, (FILE*)Handle );
//    return(Byte_Read);
      extern void  ReadNandFlashByCpuAddrInC(UCHAR *pTarget,DWORD dCpuAddr,DWORD dSize);

      SLong nRetValue;
      DWORD nReadSize;

      if (Handle == _FILE_HANDLE_FOR_WWB_)
         {
          ReadNandFlashByCpuAddrInC(Buffer,CMAP_WWB_ROM_START_ADDR + 16 + Address,Size);

          return(Size);
         }

//__wrap_printf("F_Get => Handle = %08x  Offset = %08x  Buffer = %08x  Size = %08x\n",Handle,Address,(DWORD)Buffer,Size);
      nRetValue = OsfSeek(Handle,Address,OSF_FILE_BEGIN);
      if (nRetValue >= OSF_NO_ERROR)
          nRetValue =  OsfRead(Handle,Buffer,Size,&nReadSize);
      if (nRetValue >= OSF_NO_ERROR)
          nRetValue  = nReadSize;
      else
          nRetValue  = -1;

      return(nRetValue);
}
SLong F_Put(SLong Handle,Byte *Buffer,SLong Size)
{
      SLong nRetValue;
      UINT  nWriteSize;

      nRetValue = OsfWrite(Handle,Buffer,Size,&nWriteSize);
//__wrap_printf("F_Put => Handle = %08x  Buffer = %08x  Size = %08x  (Write-Size = %d) ",Handle,(DWORD)Buffer,Size,nWriteSize);
      if (nRetValue >= OSF_NO_ERROR)
          nRetValue  = nWriteSize;
      else
          nRetValue  = -1;
//if (nRetValue == -1)
//  __wrap_printf("Put Error ..... \n");
//else
//  __wrap_printf("Put Ok ..... \n");

      return(nRetValue);
}
SLong F_Seek(SLong Handle,Long Address)
{
      SLong nRetValue;

      nRetValue = OsfSeek(Handle,Address,OSF_FILE_BEGIN);
      if (nRetValue < OSF_NO_ERROR)
          nRetValue = -1;

      return(nRetValue);
}
SLong F_SeekEx(SLong Handle,Long Address,int nWhence)
{
      SLong nRetValue;

      nRetValue = OsfSeek(Handle,Address,nWhence);
      if (nRetValue < OSF_NO_ERROR)
          nRetValue = -1;

      return(nRetValue);
}
/* fh ##############################################################
FUNCTION : F_FileAttrib().
USE      : Read file attribute ( sizeof ).

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : Handle: File Handle returned by F_Open() function.
OUTPUT   : FileSize: size of the file in bytes.
GLOBAL   : None.
RETURN   : Result of the operation:
			= 0 : OK
			< 0 : Error code
NOTE     :
##################################################################*/
SLong F_FileAttrib ( SLong Handle, SLong *FileSize )
{
//    SLong RetValue;
//    long  CurrPosition;
//
//    CurrPosition = ftell((FILE*)Handle);
//    fseek( (FILE*)Handle, 0, SEEK_END);
//    *FileSize = ftell((FILE*)Handle);
//    fseek( (FILE*)Handle, CurrPosition, SEEK_SET);
//    RetValue = 0;
//    return(RetValue);

      SLong nRetValue;

      nRetValue = OsfSeek(Handle,0,OSF_FILE_END);
      if (nRetValue < OSF_NO_ERROR)
          nRetValue = -1;
      else
         {
          *FileSize = nRetValue;
          nRetValue = 0;
         }

      return(nRetValue);
}
/* fh ##############################################################
FUNCTION : F_FindFirst().
USE      : Provides information about the first instance of a filename
that matches the file specified in the filespec argument.

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : FileName: name of the file to be searched. It could contain wild
			chars. Example: "C:\\CHARTS\\*.LIC"

OUTPUT   : FoundFileName: full name of the found file.
GLOBAL   : None.
RETURN   : Result of the operation:
			>= 0 : Find Handle to be used in F_FindNext() and F_Close()
			<  0 : Error code / file not found
NOTE     :
##################################################################*/
SLong F_FindFirst ( char *FileName, char *FoundFileName )
{
//    SLong RetValue;
//    struct _finddata_t	FindData;
//
//    RetValue = _findfirst(FileName, &FindData);
//    if (RetValue != -1)
//        strcpy(FoundFileName,FindData.name);
//    return(RetValue);

      OSFDOSDirEntry xDirEntry;
      CHAR  vFileName[256];
      FHANDLE hHandle;
//__wrap_printf("F_FindFirst = %s , %s\n",FileName,FoundFileName);

      hHandle = OsfFindFirstEx(FileName,&xDirEntry,vFileName,sizeof(vFileName));
      if (hHandle < OSF_NO_ERROR)
          return(-1);

      strcpy(FoundFileName,vFileName);

      return(hHandle);
}
/* fh ##############################################################
FUNCTION : F_FindNext().
USE      : Find the next name, if any, that matches the filespec
argument in a previous call to _findfirst, and then
alters the fileinfo structure contents accordingly.
AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : FindHandle: handle returned by F_FindFirst() function.
OUTPUT   : FoundFileName: full name of the found file.
GLOBAL   : None.
RETURN   : Result of the operation:
			= 0 : File found
			< 0 : Error code / file not found
NOTE     :
##################################################################*/
SLong F_FindNext ( SLong FindHandle, char *FoundFileName)
{
//    SLong RetValue;
//    struct _finddata_t FindData;
//
//    RetValue = _findnext( FindHandle, &FindData );
//    if (RetValue == 0)
//        strcpy(FoundFileName,FindData.name);
//    return(RetValue);

      OSFDOSDirEntry xDirEntry;
      CHAR  vFileName[256];
      SLong nRetValue;
//__wrap_printf("F_FindNext = %08x , %s\n",FindHandle,FoundFileName);

      nRetValue = OsfFindNextEx(FindHandle,&xDirEntry,vFileName,sizeof(vFileName));
      if (nRetValue < OSF_NO_ERROR)
          return(-1);

      strcpy(FoundFileName,vFileName);

      return(0);
}
/* fh ##############################################################
FUNCTION : F_FindClose().
USE      : Closes the specified search handle and releases associated resources.

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : FindHandle: handle returned by F_FindFirst() function.
OUTPUT   : None.
GLOBAL   : None.
RETURN   : Result of the operation:
			= 0 : OK
			< 0 : Error code.
NOTE     :
##################################################################*/
SLong F_FindClose ( SLong FindHandle )
{
//    return(_findclose(FindHandle));

      SLong nRetValue;
//__wrap_printf("F_FindClose = %08x\n",FindHandle);

      nRetValue = OsfFindClose(FindHandle);
      if (nRetValue < OSF_NO_ERROR)
          return(-1);

      return(0);
}
/* fh ##############################################################
FUNCTION : F_Detect().
USE      : 

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : DriveLetter: Letter of the drive that identifies a local 
           Device slot. Valid Drive Letter are: 
					 "C:", "D:", "E:", "F:", "G:", "H:", ...

OUTPUT   : Changed: status of the device
				   TRUE if the Device status is changed
				   FALSE if the Device status is not changed

				   previous		current		
				   status			status				output
				   --------------------------------------
				   Not Present		Not Present				FALSE
				   Present			Not Present				TRUE
				   Not Present		Present					TRUE
				   Present			Present & Changed (*)	TRUE
				   Present			Present & Not Changed	FALSE

				   (*) This means the device has been removed and that a new one
					 has been plugged into the system.

GLOBAL   : None.

RETURN   : Status of SD Card Slot. Valid return value is:
				   FDEV_NOT_PRESENT
				   FDEV_SD_PRESENT	
				   FDEV_MMC_PRESENT
				   FDEV_CF_PRESENT	
				   FDEV_UNKNOWN	

NOTES	   : This function is called in the cmcScanCartrdge() function to check 
			     the status of the SD card slots present in the system. Since in our
			     CMC_XP module we have a cartographic data caching algorithm and in
			     case a SD card is removed or changed we need to reset the cache data.
			     Inside this function, all the files open, and associated with the 
			     device which has been removed or changed, MUST be closed.
			     The cmc_XP form does not execute closing operation.
##################################################################*/
FDEV_TYPE F_Detect(char *DriveLetter,Bool *Changed)
{
//__wrap_printf("F_Detect ==> DriveLetter :  %c   ",DriveLetter[0]);
      if (DriveLetter[0] != 'c' && DriveLetter[0] != 'C')
         {
          *Changed = FALSE;
//__wrap_printf("return (FDEV_NOT_PRESENT)  DriveLetter Error \n");
          return(FDEV_NOT_PRESENT);
         }

      if (SdCardGetCardChangStatus())
         {
          G_dCmapCardChangedMode = CALC_SEC_TO_TICK(10);
          *Changed = TRUE;
         }
      else
          *Changed = FALSE;

      if (SdCardGetCardInsertStatus())
         {
          SdCardSetCardChangStatus(SDCARD_CARD_CHANGE_CLEARED);
          if (*Changed == TRUE)
             {
              OsfCloseAll();
              SdCardSetCardChangStatus(SDCARD_CARD_CHANGE_OCCURED);
              OsfResetDisk(SDCARD_MOUNT_NO);
              OsfMountDisk(SDCARD_MOUNT_NO);
             }
//__wrap_printf("return (FDEV_SD_PRESENT) \n");
          G_dCmapCardChangedMode = CALC_SEC_TO_TICK(10);
          return(FDEV_SD_PRESENT);
         }

      SdCardSetCardChangStatus(SDCARD_CARD_CHANGE_CLEARED);

//__wrap_printf("return (FDEV_NOT_PRESENT) \n");
      return(FDEV_NOT_PRESENT);
}
/* fh ##############################################################
FUNCTION : F_DeviceInfo().
USE      : 

AUTHOR   : GE[041019].
UPDATED  : 

INPUT    : DriveLetter: Letter of the drive that identifies a local device 
           SD card slot.

OUTPUT   : FDevInfo: structure containing all necessary information to de-crypt 
			     the chart file.

GLOBAL   : None.

RETURN   : Result of the operation:
				   = 0 : ok
				   < 0 : Error code

NOTES    : See the example on the beginning of this file to know how to format
           special fields in this strcuture. 


{
      // Here is an example on how you can calculate SectorsNumber and BytesPerSector
      // value to set the relative fields in FDEVInfoType.

      // In case FDEVInfoType structure is not filled properly, Navnet2 will not be 
      // able to read cartographic data properly.

      DWORD SD_Card_Rca;
      DWORD SDResp[4];

      // Read RCA register
      SD_Cmd( 3, 0, (0x01UL<<9), &SDResp );

      // Check for errors in the sequence of authentication process
      if (!(SDResp & 0x8))
          SD_Card_Rca = ( SDResp & 0xFFFF0000 ) >> 16;
      else
          return(BIOS_ERR_FATAL);        // Error during authentication process

      // Read CSD register
      SD_Cmd( 9, ( SD_Card_Rca << 16 ), (0x03UL<<9), SDResp );

      // Get block size
      SD_BlkSize = ( BYTE )(( SDResp[1] & 0xF0000 ) >> 16 );

      // Get device size
      SD_CSize = ( WORD )((( SDResp[1] & 0x3FF ) << 2 ) | (( SDResp[2] & 0xC0000000 ) >> 30 ));

      // Get device size multiplier
      SD_CSizeMul = ( BYTE )(( SDResp[2] & 0x38000 ) >> 15 );

      SectorsNumber  = ( DWORD )(( double )( SD_CSize + 1 ) * pow( 2, ( double )( SD_CSizeMul + 2 )));
      BytesPerSector = ( WORD )( pow( 2, ( double )SD_BlkSize ));
}

{
      // Here is an example on how you can read the CID register from the selected 
      // SD card and how you have to format SerialNum, CrtlRevNum, ModelNum fields 
      // in FDEVInfoType structure.

      // In case FDEVInfoType structure is not filled properly, Navnet2 will not be 
      // able to read cartographic data properly.

      BYTE  Month, Year;
      DWORD SD_Serial;
      DWORD SDResp[4];
      SDInfoType SD_CurrentInfo[SD_CARD_NUM];

      // Get SD card CID register
      // prototype of SD_Cmd() function is:
      // SD_Cmd( DWORD Cmd, DWORD Arg, DWORD Ctrl, DWORD* Resp );
      // where: 
      // Cmd  is the command number
      // Arg  is the command Argument
      // Ctrl is the command control parameter (short response/long response/no response)
      // Resp: pointer to buffer where store command response (one DWORD or four DWORDs).
      SD_Cmd( 2, 0, (0x03UL<<9), SDResp );

      SD_Serial= (SDResp[2]<<8)|(SDResp[3]&0xFF000000)>>24;

      Month = ((SDResp[3]&0x00FFFF00)>>8 & 0xF );
      Year  = (((SDResp[3]&0x00FFFF00)>>8) >> 4 );

      sprintf(SD_CurrentInfo[ Card ].ModelNum,
              "SD type: %c%c%c%c%c produced in %2d, %4d",
              SDResp[0]&0xFF, 
              (SDResp[1]&0xFF000000) >>24, 
              (SDResp[1]&0xFF0000)   >>16, 
              (SDResp[1]&0xFF00)     >>8 , 
              (SDResp[1]&0xFF), 
              Month, 
              2000+Year);

      sprintf(SD_CurrentInfo[ Card ].CtrlRevNum,
              "Rev= %d.%d",
              (((SDResp[2]&0xFF000000)>>24 ) >> 4 ),
              (((SDResp[2]&0xFF000000)>>24 ) & 0xF ));

       // Save serial number
       SD_CurrentInfo[ Card ].SerialNum[ 0 ] = ( BYTE )(( SDResp[ 0 ] & 0xFF000000 ) >> 24 );
       SD_CurrentInfo[ Card ].SerialNum[ 1 ] = ( BYTE )(( SDResp[ 0 ] & 0x00FF0000 ) >> 16 );
       SD_CurrentInfo[ Card ].SerialNum[ 2 ] = ( BYTE )(( SDResp[ 0 ] & 0x0000FF00 ) >>  8 );
       SD_CurrentInfo[ Card ].SerialNum[ 3 ] = ( BYTE )(( SDResp[ 2 ] & 0xFF000000 ) >> 24 );
       SD_CurrentInfo[ Card ].SerialNum[ 4 ] = ( BYTE )(( SDResp[ 2 ] & 0x00FF0000 ) >> 16 );
       SD_CurrentInfo[ Card ].SerialNum[ 5 ] = ( BYTE )(( SDResp[ 2 ] & 0x0000FF00 ) >>  8 );
       SD_CurrentInfo[ Card ].SerialNum[ 6 ] = ( BYTE )(( SDResp[ 2 ] & 0x000000FF ) >>  0 );
       SD_CurrentInfo[ Card ].SerialNum[ 7 ] = ( BYTE )(( SDResp[ 3 ] & 0xFF000000 ) >> 24 );
}
##################################################################*/
SLong F_DeviceInfo(char *DriveLetter,FDEVInfoType *FDevInfo)
{
      xSDCARD *pSdCard;
      int   nMount;
      int   nSdBlkSize;
      int   nSdDevSize;
      int   nSdMulSize;

      SdCardSetCardChangStatus(SDCARD_CARD_CHANGE_OCCURED);
      OsfRawMediaChanged(SDCARD_MOUNT_NO);
      nMount = OsfMountDisk(SDCARD_MOUNT_NO);
      if (nMount < OSF_NO_ERROR)
          return(-1);

//    SdCardAllDataStructure(&xSdCard);
      pSdCard = (xSDCARD *)SdCardGetSdCardPtr();

                                                      // 0 : 127 -- 96
                                                      // 1 :  95 -- 64
                                                      // 2 :  63 -- 32
                                                      // 3 :  31 --  0
      nSdBlkSize = pSdCard->m_xCSD.bReadBlLen;         // SD_BlkSize = ((SDResp[1] & 0xF0000) >> 16);
      nSdDevSize = pSdCard->m_xCSD.wDeviceSize;        // SD_CSize   = (((SDResp[1]& 0x3FF) << 2 ) | ((SDResp[2] & 0xC0000000) >> 30));
      nSdMulSize = pSdCard->m_xCSD.bDeviceSizeMul;     // SD_CSizeMul= ((SDResp[2] & 0x38000) >> 15);

      FDevInfo->SectorsNumber = (nSdDevSize + 1) * (1 << (nSdMulSize + 2));  // SectorsNumber = (SD_CSize + 1) * pow(2,(SD_CSizeMul + 2));
      FDevInfo->BytesPerSector= (1 << nSdBlkSize);                           // BytesPerSector= pow(2,SD_BlkSize);

      //==============================================================================
      // SD_CurrentInfo[ Card ].SerialNum[0] = (BYTE)((SDResp[0] & 0xFF000000) >> 24);
      // SD_CurrentInfo[ Card ].SerialNum[1] = (BYTE)((SDResp[0] & 0x00FF0000) >> 16);
      // SD_CurrentInfo[ Card ].SerialNum[2] = (BYTE)((SDResp[0] & 0x0000FF00) >>  8);
      // SD_CurrentInfo[ Card ].SerialNum[3] = (BYTE)((SDResp[2] & 0xFF000000) >> 24);
      // SD_CurrentInfo[ Card ].SerialNum[4] = (BYTE)((SDResp[2] & 0x00FF0000) >> 16);
      // SD_CurrentInfo[ Card ].SerialNum[5] = (BYTE)((SDResp[2] & 0x0000FF00) >>  8);
      // SD_CurrentInfo[ Card ].SerialNum[6] = (BYTE)((SDResp[2] & 0x000000FF) >>  0);
      // SD_CurrentInfo[ Card ].SerialNum[7] = (BYTE)((SDResp[3] & 0xFF000000) >> 24);
      // =============================================================================
      memset(FDevInfo->SerialNum,0x00,sizeof(FDevInfo->SerialNum));
      FDevInfo->SerialNum[0] = pSdCard->m_xCID.bMID;
      FDevInfo->SerialNum[1] = (UCHAR)((pSdCard->m_xCID.wOID >>  8) & 0xff);
      FDevInfo->SerialNum[2] = (UCHAR)((pSdCard->m_xCID.wOID >>  0) & 0xff);
      FDevInfo->SerialNum[3] = pSdCard->m_xCID.bPRV;
      FDevInfo->SerialNum[4] = (UCHAR)((pSdCard->m_xCID.dPSN >> 24) & 0xff);
      FDevInfo->SerialNum[5] = (UCHAR)((pSdCard->m_xCID.dPSN >> 16) & 0xff);
      FDevInfo->SerialNum[6] = (UCHAR)((pSdCard->m_xCID.dPSN >>  8) & 0xff);
      FDevInfo->SerialNum[7] = (UCHAR)((pSdCard->m_xCID.dPSN >>  0) & 0xff);

      return(0);
}

