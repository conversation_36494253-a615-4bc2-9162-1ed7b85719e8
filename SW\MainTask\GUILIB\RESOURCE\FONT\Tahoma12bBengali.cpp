/*...........................................................................*/
/*.                  File Name : Tahoma12bBengali.cpp                       .*/
/*.                                                                         .*/
/*.                       Date : 2014.04.10                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma12bArabic_Font;

ROMDATA PEGUSHORT Tahoma12bBengali1_offset_table[129] = {
0x0000,0x0014,0x0028,0x003c,0x004f,0x0063,0x006e,0x007f,0x0091,0x00a3,0x00af,0x00bb,0x00c9,0x00dd,0x00f1,0x0105,
0x0114,0x0128,0x0134,0x0141,0x0155,0x0162,0x016f,0x0183,0x0197,0x01ab,0x01bf,0x01cf,0x01dc,0x01e9,0x01f6,0x0203,
0x0211,0x021d,0x0229,0x0236,0x024a,0x025e,0x0272,0x0280,0x028c,0x0299,0x02a6,0x02b3,0x02c7,0x02d4,0x02e2,0x02ef,
0x02f8,0x0305,0x0315,0x0329,0x0334,0x0340,0x034c,0x0359,0x0366,0x0373,0x0380,0x038d,0x0399,0x03ad,0x03bb,0x03cf,
0x03e3,0x03ef,0x03fc,0x0408,0x0415,0x0422,0x0430,0x043d,0x0451,0x0465,0x0479,0x048a,0x049e,0x04b2,0x04c6,0x04da,
0x04ec,0x04ff,0x0512,0x051c,0x0527,0x0531,0x0545,0x054f,0x0563,0x0575,0x0589,0x059d,0x05b1,0x05c5,0x05d9,0x05ed,
0x0601,0x0615,0x0629,0x063d,0x0651,0x0665,0x0679,0x068d,0x06a1,0x06b5,0x06c9,0x06dd,0x06f1,0x0705,0x0719,0x072d,
0x0741,0x0755,0x0769,0x077d,0x0791,0x07a5,0x07b9,0x07cd,0x07e1,0x07f5,0x0809,0x081d,0x0831,0x0845,0x0859,0x086d,
0x0881};



ROMDATA PEGUBYTE Tahoma12bBengali1_data_table[6552 + 273] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xff, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0xff, 0xf8, 0x3f, 0xff, 0x83, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0f, 0xff, 0xe0, 0x00, 0x03, 0xff, 0xf8, 0x3f, 0xff, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x01, 
0x80, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xe0, 0x00, 0x3f, 0xff, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 
0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 
0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 
0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x08, 0x00, 0x20, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x01, 
0x60, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x75, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x25, 0x00, 0x00, 0x10, 0x7c, 0x03, 0xc0, 0x1f, 0x00, 
0x02, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x3e, 
0x00, 0x00, 0x0f, 0x80, 0x03, 0x00, 0x18, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x1f, 0x00, 0x08, 0x07, 0xc0, 0x02, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x20, 0x00, 0x0f, 
0x80, 0x00, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x01, 0x01, 0xf0, 0x00, 0x00, 0x3e, 0x01, 0xf0, 
0x00, 0x00, 0x03, 0x08, 0x00, 0x20, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x83, 0xe0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x01, 
0x60, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0x83, 0xe0, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x55, 0x00, 0x00, 0x29, 0x86, 0x04, 0x30, 0x61, 0x80, 
0x02, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x00, 0x61, 0x80, 0x00, 0x00, 0x00, 0xc3, 
0x00, 0x00, 0x30, 0xc0, 0x06, 0x00, 0x30, 0x00, 0x61, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
0x21, 0x80, 0x14, 0x18, 0x60, 0x05, 0x00, 0x00, 0x30, 0xc3, 0x80, 0x00, 0x00, 0x50, 0x00, 0x10, 
0xc0, 0x00, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x02, 0x83, 0x0c, 0x00, 0x00, 0xc3, 0x06, 0x18, 
0x00, 0x00, 0x0c, 0x08, 0x00, 0x20, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x8c, 0x30, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x01, 
0xc0, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x3f, 0xef, 0xf8, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x80, 0x4e, 0x00, 0x00, 0x20, 0x01, 0x07, 0xd8, 0x00, 0x40, 
0x02, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x00, 0x80, 0xc0, 0x00, 0x00, 0x00, 0x01, 
0x80, 0x00, 0x00, 0x60, 0x08, 0x00, 0x20, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 
0x40, 0xc0, 0x16, 0x00, 0x30, 0x04, 0x80, 0x01, 0xe0, 0x01, 0xc0, 0x00, 0x00, 0x48, 0x00, 0x20, 
0x60, 0x00, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x02, 0x84, 0x06, 0x00, 0x00, 0x01, 0x80, 0x0c, 
0x00, 0x00, 0x18, 0x08, 0x00, 0x20, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x80, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x01, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x28, 0x38, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x30, 0x20, 0x00, 0x83, 0x90, 0x72, 0x00, 
0x39, 0x00, 0x0e, 0x40, 0x01, 0xe0, 0x1e, 0x00, 0x70, 0x01, 0xc0, 0x01, 0xc6, 0x00, 0x1c, 0x60, 
0x00, 0xe3, 0x81, 0xc7, 0x00, 0x34, 0xc3, 0x44, 0x07, 0xc1, 0xa1, 0x99, 0x0d, 0xf8, 0x66, 0x40, 
0x02, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x71, 0xe0, 0x00, 0xc1, 0xdc, 0x1c, 0x70, 0xe1, 
0x83, 0x1c, 0x1c, 0x73, 0x8e, 0x38, 0x60, 0xe1, 0xe0, 0x4e, 0x0f, 0xe0, 0x70, 0xf0, 0x06, 0x38, 
0x00, 0xc0, 0x12, 0x1c, 0x30, 0xe3, 0x80, 0x7f, 0x00, 0x38, 0xc3, 0x3c, 0x07, 0x38, 0x38, 0x00, 
0x61, 0xc7, 0x80, 0x80, 0x02, 0x0f, 0x87, 0x18, 0x3a, 0x40, 0x02, 0x38, 0xe0, 0xd9, 0x86, 0xec, 
0x00, 0xc0, 0x70, 0x08, 0x00, 0x20, 0x3f, 0x02, 0x00, 0x08, 0x20, 0x00, 0x8e, 0x18, 0x3b, 0xc3, 
0x18, 0x38, 0xc3, 0x8f, 0x03, 0xb8, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x01, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x20, 0x28, 0x28, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0x00, 0x78, 
0x00, 0x07, 0x80, 0x00, 0x7c, 0x07, 0x87, 0x80, 0x00, 0x78, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x04, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x48, 0x20, 0x00, 0x83, 0x50, 0x6a, 0x38, 
0x35, 0x00, 0x0d, 0x40, 0x07, 0xf8, 0x63, 0x00, 0x68, 0x01, 0xa1, 0x71, 0xa9, 0x1c, 0x1a, 0x91, 
0xc1, 0xd5, 0xc3, 0xab, 0x97, 0x2b, 0xc2, 0xbc, 0x1c, 0x21, 0x59, 0x6c, 0x8d, 0x7c, 0x5b, 0x25, 
0xc2, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0xca, 0x38, 0x3c, 0x63, 0x3e, 0x1a, 0xf1, 0x50, 
0xc6, 0x9e, 0x1a, 0x33, 0x52, 0x34, 0xd1, 0x96, 0x10, 0x0d, 0x70, 0xd0, 0xe9, 0x68, 0x0d, 0x68, 
0x3c, 0x61, 0x8f, 0x12, 0x18, 0xd0, 0x81, 0x9c, 0x00, 0x78, 0x66, 0xc7, 0x08, 0x08, 0x34, 0x18, 
0x33, 0x28, 0xe0, 0x80, 0x02, 0x16, 0xc6, 0xa4, 0x35, 0xe1, 0xc3, 0x34, 0xf1, 0xdc, 0xc6, 0xb6, 
0x19, 0x20, 0x8c, 0x08, 0x00, 0x20, 0xc1, 0x82, 0x00, 0x08, 0x20, 0x00, 0x8d, 0x0c, 0x63, 0xc2, 
0xac, 0x35, 0x23, 0x5f, 0x04, 0x44, 0x1d, 0xc0, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x01, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x01, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x34, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0xe1, 0xc6, 
0x00, 0x1c, 0x60, 0x01, 0xc3, 0x1c, 0x1c, 0x60, 0x01, 0xc6, 0x00, 0x1c, 0x60, 0x00, 0x00, 0x2e, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x03, 0x88, 
0x00, 0x26, 0x00, 0x6c, 0xc0, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x03, 0x80, 0x00, 0x48, 0x20, 0x00, 0x80, 0x28, 0x05, 0x4c, 
0x02, 0x80, 0x00, 0xa0, 0x0e, 0x1c, 0x61, 0x80, 0x04, 0x00, 0x12, 0xf8, 0x29, 0x26, 0x02, 0x92, 
0x6a, 0x14, 0x24, 0x28, 0x6f, 0x08, 0x60, 0x86, 0x19, 0xd0, 0x42, 0x5c, 0x93, 0xec, 0x97, 0x2b, 
0xe2, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0xcc, 0x18, 0x7e, 0x62, 0x27, 0x02, 0x91, 0xf0, 
0xc9, 0xa7, 0x02, 0x31, 0xde, 0x05, 0x51, 0x98, 0x08, 0x11, 0xc0, 0x19, 0x0a, 0x0c, 0x11, 0x68, 
0x60, 0x62, 0x03, 0x02, 0x18, 0x10, 0xc1, 0xe3, 0x00, 0x84, 0x66, 0x83, 0x10, 0x0c, 0x02, 0x20, 
0x33, 0x30, 0x60, 0x80, 0x02, 0x14, 0x60, 0xa4, 0x04, 0x63, 0xe3, 0x05, 0x39, 0x5c, 0xcb, 0xf6, 
0x21, 0x21, 0x84, 0x08, 0x00, 0x21, 0x80, 0xc2, 0x00, 0x08, 0x20, 0x00, 0x81, 0x0c, 0xc4, 0xe3, 
0xbc, 0x05, 0x20, 0x53, 0x87, 0xfc, 0x3f, 0xe0, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x02, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x01, 0x31, 0xba, 
0x00, 0x1b, 0xa0, 0x01, 0x9d, 0x99, 0x9b, 0xa0, 0x01, 0xba, 0x00, 0x1b, 0xa0, 0x00, 0x00, 0x5f, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x04, 0xd0, 
0x00, 0x6f, 0x00, 0x6d, 0xc0, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x04, 0x40, 0x00, 0x30, 0x20, 0x00, 0x87, 0xe8, 0xfd, 0x4c, 
0x7e, 0xbc, 0x1f, 0xaf, 0x0d, 0xcc, 0xc1, 0x83, 0xfc, 0x0f, 0xf2, 0x59, 0xef, 0x06, 0x1e, 0xf0, 
0x62, 0xf7, 0xa5, 0xef, 0x65, 0x78, 0x67, 0x86, 0x33, 0x6b, 0xc2, 0x44, 0x92, 0x2c, 0x91, 0x29, 
0x62, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x1f, 0x9c, 0xc3, 0x66, 0x43, 0x3e, 0xf1, 0x80, 
0xca, 0x23, 0x3e, 0x33, 0x13, 0x1d, 0xe0, 0x3f, 0x78, 0x13, 0xf3, 0xf9, 0x3d, 0xfc, 0x17, 0x78, 
0x40, 0x64, 0x03, 0x3e, 0x19, 0xf0, 0xc1, 0xd3, 0xb8, 0xfc, 0x61, 0xf3, 0x90, 0x0c, 0x7e, 0x40, 
0x30, 0x3c, 0x70, 0x80, 0x02, 0x2f, 0xe3, 0xbc, 0x1c, 0x66, 0x33, 0x3e, 0x19, 0x44, 0xc9, 0xe6, 
0x41, 0xe3, 0x06, 0x08, 0x00, 0x21, 0x1e, 0xc2, 0x00, 0x08, 0x20, 0x00, 0x87, 0x0c, 0xc8, 0x67, 
0x1c, 0x3d, 0xe1, 0xe1, 0x8c, 0x00, 0x72, 0x70, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x02, 0x60, 0x00, 0xf0, 
0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0x33, 0x5d, 
0x00, 0x35, 0xd0, 0x03, 0x36, 0xb3, 0x35, 0xd0, 0x03, 0x5d, 0x00, 0x35, 0xd0, 0x00, 0x00, 0x4b, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x00, 0xc0, 
0x00, 0x6f, 0x01, 0x77, 0xd8, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x04, 0x40, 0x00, 0x00, 0x20, 0x00, 0x8c, 0x31, 0x86, 0x0c, 
0xc3, 0x04, 0x30, 0xc2, 0x0d, 0x2c, 0xc1, 0x87, 0x00, 0x1c, 0x01, 0x9b, 0x01, 0x8e, 0x30, 0x18, 
0xe3, 0x00, 0xe6, 0x01, 0xd9, 0x60, 0x66, 0x06, 0x33, 0x6b, 0x02, 0x38, 0x91, 0xcc, 0x8e, 0x26, 
0x62, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x30, 0xcc, 0x81, 0x66, 0x63, 0x10, 0x19, 0x00, 
0xcc, 0x63, 0x10, 0x33, 0x1f, 0x30, 0x30, 0x61, 0xc0, 0x12, 0x0f, 0x01, 0x67, 0x80, 0x18, 0x1c, 
0x40, 0x64, 0x03, 0x30, 0x19, 0x80, 0xc7, 0x73, 0x4c, 0xc0, 0x61, 0x19, 0x90, 0x0c, 0xc0, 0x40, 
0x30, 0x46, 0x30, 0x80, 0x02, 0x38, 0x06, 0x0e, 0x70, 0x64, 0x13, 0x13, 0x19, 0x38, 0xc8, 0x06, 
0x40, 0x33, 0x06, 0x08, 0x00, 0x23, 0x04, 0xc2, 0x00, 0x08, 0x20, 0x00, 0x8c, 0x0c, 0xcc, 0x66, 
0xf6, 0x60, 0x33, 0x31, 0x9c, 0x08, 0x62, 0x30, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x60, 0x00, 0x10, 
0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x00, 0x73, 0x7d, 
0x00, 0x37, 0xd0, 0x03, 0x36, 0x33, 0x37, 0xd0, 0x03, 0x7d, 0x00, 0x37, 0xd0, 0x00, 0x00, 0x33, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x01, 0xc0, 
0x00, 0x3b, 0x03, 0xb6, 0xf0, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x04, 0x40, 0x00, 0x30, 0x20, 0x00, 0x8c, 0x21, 0x84, 0x0c, 
0xc2, 0x08, 0x30, 0x83, 0x8c, 0xec, 0xc1, 0x8c, 0x00, 0x30, 0x00, 0x1b, 0x11, 0xb6, 0x31, 0x1b, 
0x6b, 0x00, 0x66, 0x00, 0xc1, 0x60, 0x66, 0x06, 0x30, 0x63, 0x02, 0x10, 0x90, 0x8c, 0x84, 0x20, 
0x62, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x24, 0x4c, 0x81, 0xe6, 0x33, 0x37, 0x19, 0x10, 
0xcc, 0x33, 0x30, 0x73, 0x03, 0x30, 0x30, 0x48, 0xc4, 0x14, 0x46, 0x01, 0x43, 0x00, 0x18, 0x0c, 
0x40, 0xe4, 0x23, 0x31, 0x19, 0x88, 0xcb, 0xe3, 0x4c, 0xc2, 0x62, 0x0d, 0x90, 0x1c, 0xc0, 0x42, 
0x30, 0xc6, 0x30, 0x80, 0x02, 0x30, 0x06, 0x06, 0x60, 0x64, 0x13, 0x31, 0x19, 0x81, 0xc9, 0xc6, 
0x42, 0x33, 0x06, 0x08, 0x00, 0x23, 0x03, 0x82, 0x00, 0x08, 0x20, 0x00, 0x8c, 0x1c, 0xc4, 0xe6, 
0xf6, 0x62, 0x33, 0x19, 0x9a, 0x18, 0x60, 0x30, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x60, 0x00, 0x20, 
0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 0x20, 0x00, 0x80, 0x01, 0xb3, 0x0c, 
0x00, 0x30, 0xc0, 0x03, 0x06, 0x30, 0x30, 0xc0, 0x03, 0x0c, 0x00, 0x30, 0xc0, 0x00, 0x00, 0x03, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x06, 0xd8, 
0x00, 0x03, 0x03, 0xa4, 0xf0, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x03, 0x80, 0x00, 0x48, 0x20, 0x00, 0x8c, 0x21, 0x84, 0x4c, 
0xc2, 0x08, 0x30, 0x81, 0x07, 0xcc, 0xe3, 0x0c, 0x00, 0x30, 0x00, 0x9b, 0x11, 0xb6, 0x31, 0x1b, 
0x69, 0x80, 0xc3, 0x01, 0x89, 0x60, 0xc6, 0x0c, 0x38, 0xe3, 0x03, 0x11, 0x98, 0x9c, 0xc4, 0x62, 
0x62, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x44, 0x4c, 0xf1, 0xc7, 0x33, 0x34, 0x99, 0x11, 
0xce, 0x33, 0x30, 0x63, 0x87, 0x30, 0x70, 0x88, 0xc6, 0x58, 0x46, 0x01, 0xe3, 0x00, 0x0c, 0x1c, 
0x61, 0xc6, 0x27, 0x31, 0x39, 0x89, 0xc1, 0x87, 0x4c, 0xc2, 0x62, 0x0d, 0x8c, 0x38, 0xc0, 0x62, 
0x70, 0x86, 0x30, 0x80, 0x02, 0x30, 0x06, 0x06, 0x60, 0xc7, 0xb7, 0x31, 0x18, 0xc3, 0x8d, 0x2e, 
0x62, 0x33, 0x8c, 0x08, 0x00, 0x23, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x8c, 0x18, 0xe5, 0x67, 
0x0e, 0x62, 0x33, 0x19, 0x9b, 0xf0, 0x60, 0x30, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x02, 0x60, 0x00, 0x20, 
0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x0c, 0x20, 0x00, 0x80, 0x01, 0xb3, 0x9c, 
0x00, 0x39, 0xc0, 0x03, 0x8e, 0x38, 0xb9, 0xc0, 0x03, 0x9c, 0x00, 0x39, 0xc0, 0x00, 0x00, 0x13, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x06, 0xd8, 
0x00, 0x1b, 0x37, 0xa0, 0x98, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x48, 0x20, 0x00, 0x86, 0x28, 0xc5, 0x4c, 
0x62, 0x98, 0x18, 0xa2, 0x00, 0x18, 0x7f, 0x0c, 0x01, 0x30, 0x04, 0x9b, 0xff, 0x36, 0x3f, 0xf3, 
0x69, 0xff, 0xc3, 0xff, 0x89, 0x3f, 0xc3, 0xfc, 0x1f, 0xc1, 0xf9, 0xff, 0x0f, 0xf8, 0x7f, 0xc2, 
0x62, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0x7f, 0xc8, 0x53, 0xc3, 0xe6, 0x3c, 0xf0, 0xff, 
0x87, 0xf6, 0x1f, 0xc1, 0xfe, 0x1f, 0xe0, 0xff, 0xff, 0xcf, 0xff, 0x08, 0xff, 0x84, 0x0f, 0xf8, 
0x3f, 0x83, 0xfe, 0x3f, 0xf1, 0xff, 0x80, 0xfd, 0xcc, 0x7f, 0xc7, 0xfb, 0x8f, 0xf0, 0x62, 0x3f, 
0xe3, 0xfc, 0x20, 0x80, 0x02, 0x18, 0x23, 0xfc, 0x3f, 0xc2, 0xae, 0x3f, 0x30, 0xff, 0x07, 0x3c, 
0x3f, 0xe1, 0xfc, 0x08, 0x00, 0x23, 0x00, 0x42, 0x00, 0x08, 0x20, 0x00, 0x87, 0xf0, 0x7d, 0x63, 
0xfc, 0x7f, 0xe1, 0xfb, 0x19, 0xe2, 0x30, 0x60, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x02, 0x60, 0x00, 0x60, 
0x00, 0x04, 0x00, 0x00, 0x00, 0x03, 0x08, 0x00, 0x20, 0x0c, 0x20, 0x00, 0x80, 0x01, 0xb1, 0xf8, 
0x00, 0x1f, 0x80, 0x01, 0xfc, 0x1f, 0x9f, 0x80, 0x01, 0xf8, 0x00, 0x1f, 0x80, 0x00, 0x00, 0x13, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x06, 0xd8, 
0x00, 0x1a, 0x3d, 0x00, 0x10, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x30, 0x20, 0x00, 0x83, 0xf0, 0x7e, 0x38, 
0x3f, 0x18, 0x0f, 0xc6, 0x01, 0xf0, 0x3c, 0x0c, 0x01, 0x30, 0x04, 0x71, 0xee, 0x1e, 0x1e, 0xe1, 
0xe0, 0x7f, 0x00, 0xfe, 0x07, 0x1f, 0x01, 0xf0, 0x0f, 0x80, 0xf8, 0xee, 0x07, 0x70, 0x3b, 0x81, 
0xc2, 0x00, 0x08, 0x20, 0x00, 0x82, 0x00, 0x08, 0xbb, 0xb8, 0x77, 0x81, 0xcc, 0x1c, 0xe0, 0xef, 
0x03, 0xfc, 0x0f, 0x80, 0xf8, 0x0f, 0xc1, 0x77, 0xbb, 0xcb, 0xb9, 0xf0, 0x7c, 0xf8, 0x03, 0xf0, 
0x1f, 0x01, 0xdc, 0x1e, 0xe0, 0xf7, 0x00, 0x78, 0x38, 0x3d, 0xc4, 0xf3, 0x03, 0xe0, 0x3c, 0x1d, 
0xc0, 0xf8, 0xe0, 0x80, 0x02, 0x0f, 0xc1, 0xf8, 0x1f, 0x03, 0x9c, 0x1e, 0xe0, 0x3e, 0x07, 0x38, 
0x1d, 0xc0, 0xf0, 0x08, 0x00, 0x23, 0x80, 0x42, 0x00, 0x08, 0x20, 0x00, 0x83, 0xe0, 0x38, 0xc1, 
0xf8, 0x3d, 0xc0, 0xe6, 0x1c, 0x02, 0x1d, 0xc0, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 
0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x01, 0xc0, 0x00, 0x60, 
0x00, 0x0c, 0x00, 0x00, 0x00, 0x03, 0x08, 0x00, 0x20, 0x0c, 0x20, 0x00, 0x80, 0x00, 0xf0, 0xf0, 
0x00, 0x0f, 0x00, 0x00, 0xf8, 0x0f, 0x8f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x0e, 
0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 
0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x03, 0xc8, 
0x00, 0x0e, 0x18, 0x00, 0x00, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 0x08, 0x00, 0x20, 0x80, 0x02, 
0x00, 

0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xff, 0x80, 0x20, 0x04, 0x00, 
0x02, 0x18, 0x00, 0x86, 0x03, 0x30, 0x00, 0x06, 0x02, 0x18, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0xff, 0xf8, 0x3f, 0xff, 0x83, 0xff, 0xf8, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x08, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x00, 0x00, 0x00, 0xff, 0xfe, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x0f, 0xff, 0xe1, 0xc0, 0x83, 0xff, 0xf8, 0x3f, 0xff, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0e, 0x04, 0x00, 0x00, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x00, 
0x00, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x60, 
0x00, 0x0c, 0x00, 0x00, 0x00, 0x03, 0x0f, 0xff, 0xe0, 0x0c, 0x3f, 0xff, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 
0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 
0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 
0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 
0x02, 0x1c, 0x00, 0x87, 0x03, 0x30, 0x00, 0x03, 0x86, 0x0e, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe1, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 
0x00, 0x0e, 0x00, 0x00, 0x01, 0x03, 0x00, 0x00, 0x04, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 
0x02, 0x0f, 0x00, 0x83, 0xc1, 0xe0, 0x00, 0x01, 0xf8, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 
0x00, 0x07, 0x80, 0x00, 0x00, 0x83, 0x00, 0x00, 0x02, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 
0x02, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x1c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0x00, 
0x02, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0x00, 0x00, 0x07, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 


};

xFONTYY Tahoma12bBengali1 = {0x01, 25, 0, 25, 0, 0, 25, 273, 0x0d80, 0x0dff,
(PEGUSHORT *) Tahoma12bBengali1_offset_table, &Tahoma12bArabic_Font,
(PEGUBYTE *) Tahoma12bBengali1_data_table};


ROMDATA PEGUSHORT Tahoma12bBengali_offset_table[129] = {
0x0000,0x0014,0x001e,0x0031,0x0042,0x0056,0x0064,0x0075,0x007e,0x008a,0x0097,0x00a5,0x00b0,0x00b9,0x00cd,0x00e1,
0x00ea,0x00f5,0x0109,0x011d,0x0126,0x0131,0x013e,0x014a,0x0155,0x0160,0x016b,0x0175,0x0180,0x018d,0x019a,0x01a6,
0x01b0,0x01bb,0x01c8,0x01d2,0x01dc,0x01e9,0x01f5,0x01ff,0x020a,0x0215,0x0229,0x0234,0x0241,0x024c,0x0259,0x0265,
0x0270,0x027b,0x028f,0x029a,0x02ae,0x02c2,0x02d6,0x02e1,0x02ec,0x02f7,0x0300,0x0314,0x0328,0x0332,0x033a,0x034d,
0x035f,0x0372,0x037e,0x038a,0x0393,0x039b,0x03af,0x03c3,0x03d4,0x03e5,0x03f9,0x040d,0x0421,0x0435,0x0440,0x0448,
0x045c,0x0470,0x0484,0x0498,0x04ac,0x04c0,0x04d4,0x04e8,0x04fa,0x050e,0x0522,0x0536,0x054a,0x0557,0x0561,0x0575,
0x0580,0x058a,0x0593,0x059d,0x05a7,0x05bb,0x05cf,0x05d8,0x05e1,0x05ea,0x05f6,0x05ff,0x0608,0x0613,0x061c,0x0629,
0x0633,0x063e,0x0649,0x0653,0x065e,0x0668,0x0674,0x0680,0x0688,0x0692,0x0699,0x06a4,0x06b8,0x06cc,0x06e0,0x06f4,
0x0708};



ROMDATA PEGUBYTE Tahoma12bBengali_data_table[5400 + 225] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x1f, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x04, 0x00, 
0x80, 0x08, 0x00, 0x20, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x00, 0x00, 0x00, 0xff, 
0xfe, 0x0f, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0xff, 0xf8, 0x00, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x07, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x1f, 0xf0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0xff, 0xf8, 0x3f, 0xff, 0x80, 0x00, 0x08, 0x00, 0x00, 0xff, 0xfe, 0x0f, 
0xff, 0xe0, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x1f, 0xff, 
0xc1, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x03, 0x80, 0x07, 
0xff, 0xf0, 0x7f, 0xff, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xff, 0x83, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 
0xfc, 

0x10, 0x00, 0x40, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x10, 0x00, 0x00, 0x00, 0x07, 0xc1, 
0xfc, 0x1f, 0xe0, 0x7f, 0x80, 0x00, 0x00, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x0f, 0x80, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x08, 0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x00, 0x30, 0x38, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x80, 0x00, 0x0c, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x01, 0xf8, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x10, 0x00, 0x00, 0x00, 0x03, 0xe0, 
0x7e, 0x07, 0xf0, 0x1f, 0xc0, 0x00, 0x00, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x0f, 0xe0, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xf8, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x08, 0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x3f, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x80, 0x00, 0x0f, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x3c, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x42, 0x10, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x03, 0x00, 0x18, 0x00, 0x60, 0x00, 0x00, 0x08, 0x00, 0x20, 0x80, 0x02, 0x00, 0x00, 0x70, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x80, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x08, 0x00, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x1f, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x80, 0x00, 0x03, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x00, 0x00, 0x00, 0x08, 0x00, 0x20, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x41, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x13, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xfb, 0x63, 0x20, 0x08, 0x00, 0x20, 0x80, 0x02, 0x07, 0x87, 0xb8, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x78, 0x3b, 0xff, 0xfc, 0x47, 0xc7, 0x7f, 0xff, 0x17, 0x1f, 0xff, 0xff, 
0xff, 0xff, 0xf3, 0xc7, 0x83, 0xff, 0xff, 0xff, 0xff, 0xff, 0xce, 0xff, 0xff, 0x9e, 0x7f, 0xfe, 
0x73, 0xff, 0xf8, 0x80, 0x02, 0x0e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 
0x00, 0x09, 0xff, 0xc4, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x13, 0xff, 0xff, 0xff, 0xff, 0xff, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x3c, 0x00, 0x07, 0xe7, 0xe0, 0x00, 0x01, 0xff, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x87, 0x80, 0x07, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x21, 0xe0, 0x07, 0x9e, 0x00, 0x78, 0x00, 0x3e, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x1f, 0x84, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x3f, 0xff, 0xff, 0x88, 0x00, 0x27, 0xff, 
0x6c, 0xc8, 0x00, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x60, 0x30, 0x00, 0x00, 0x70, 
0x18, 0x0c, 0x03, 0x83, 0x80, 0x10, 0x1f, 0xff, 0xff, 0x80, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x18, 0x60, 0x00, 0x06, 0x21, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x13, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff, 0xff, 0xfb, 0xf3, 0x78, 0x08, 0x00, 0x20, 0x80, 0x02, 0x0f, 0xcf, 0xd8, 0x80, 
0x02, 0x08, 0x00, 0x20, 0xfc, 0x7d, 0xff, 0xfd, 0xe7, 0xcf, 0xff, 0xff, 0x3f, 0x9f, 0xff, 0xff, 
0xff, 0xff, 0xf3, 0xcf, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xbf, 0x7f, 0xfe, 
0xf3, 0xff, 0xf8, 0x80, 0x02, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 
0x00, 0x09, 0xff, 0xc4, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x13, 0xff, 0xff, 0xff, 0xff, 0xff, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x3c, 0x00, 0x07, 0xe7, 0xe0, 0x00, 0x00, 0x3f, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x8f, 0x80, 0x06, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x23, 0xe0, 0x07, 0xbe, 0x00, 0x78, 0x00, 0x7f, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x1f, 0x84, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x3f, 0xff, 0xff, 0x88, 0x00, 0x27, 0xff, 
0x7e, 0xde, 0x00, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x60, 0x18, 0x01, 0xe1, 0xfc, 
0x78, 0x0c, 0x0f, 0xe7, 0xce, 0x3c, 0x1f, 0xff, 0xff, 0x80, 0x07, 0xf8, 0x00, 0x00, 0x00, 0x06, 
0x18, 0x70, 0x00, 0x36, 0x61, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x38, 0x00, 0x07, 0x04, 0x00, 0x10, 0x00, 0xc0, 0x03, 0x61, 0x80, 
0xc0, 0x03, 0x00, 0x0c, 0x01, 0xb3, 0x1e, 0x08, 0x00, 0x20, 0x80, 0x02, 0x0c, 0xcc, 0xd8, 0x80, 
0x02, 0x08, 0x00, 0x20, 0xcc, 0x6d, 0x83, 0x01, 0xb6, 0x18, 0xe3, 0x0c, 0x1d, 0x8c, 0x03, 0x00, 
0x19, 0x80, 0x33, 0x0c, 0xfd, 0x80, 0x06, 0x01, 0x80, 0x60, 0x19, 0xc0, 0x00, 0x3b, 0x63, 0x00, 
0xc3, 0x00, 0x60, 0x80, 0x02, 0x39, 0xc1, 0x80, 0x00, 0xc0, 0x00, 0x0c, 0x61, 0xcc, 0x01, 0x82, 
0x00, 0x08, 0x03, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x66, 0x1c, 0xc6, 0x18, 0x60, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x0c, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x8c, 0x00, 0x0e, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x23, 0x00, 0x01, 0xb0, 0x00, 0x18, 0x00, 0x63, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x03, 0x00, 0xc0, 0x08, 0x00, 0x21, 0xcc, 
0x36, 0xc7, 0x80, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x38, 0x60, 0x0e, 0x33, 0xf1, 0x8c, 
0x70, 0x0d, 0x8c, 0x66, 0xcf, 0x0f, 0x00, 0x30, 0x06, 0x30, 0x07, 0xf8, 0x06, 0x00, 0x63, 0xc6, 
0x18, 0x30, 0x00, 0x30, 0xc1, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x28, 0x00, 0x05, 0x04, 0x00, 0x10, 0x38, 0xc0, 0x73, 0x61, 0xe0, 
0xf3, 0x03, 0x30, 0x2c, 0xc0, 0x73, 0x07, 0x08, 0x00, 0x20, 0x80, 0x02, 0x0e, 0xce, 0xd8, 0x80, 
0x02, 0x08, 0x00, 0x20, 0x0c, 0x0d, 0x8f, 0xc0, 0x36, 0x1e, 0x63, 0xec, 0x0f, 0x8c, 0x03, 0xf8, 
0x1c, 0xe0, 0xf3, 0x0e, 0xcd, 0x80, 0x07, 0x01, 0x98, 0x60, 0x1e, 0xc0, 0x78, 0x3b, 0x63, 0x38, 
0xff, 0x0f, 0x60, 0x80, 0x02, 0x2c, 0xc1, 0x80, 0x07, 0xcc, 0x60, 0x0e, 0x60, 0xcc, 0x0f, 0x82, 
0x00, 0x08, 0xff, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0xf6, 0x0e, 0xc3, 0x18, 0x78, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x0f, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x98, 0x00, 0x0c, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x26, 0x00, 0x01, 0xe0, 0x00, 0x18, 0x00, 0x67, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x03, 0x30, 0xc0, 0x08, 0x00, 0x20, 0xcc, 
0x0e, 0xc1, 0xc0, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x7c, 0x30, 0x03, 0x33, 0x39, 0x8c, 
0xce, 0xcd, 0x8c, 0x60, 0xc3, 0x03, 0x81, 0xf0, 0x3e, 0x3c, 0x03, 0x00, 0x0e, 0x00, 0xe7, 0xe6, 
0x18, 0x31, 0x9e, 0x31, 0xc1, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x38, 0x00, 0x07, 0x04, 0x00, 0x10, 0x7c, 0xc0, 0xfb, 0x63, 0xf1, 
0xff, 0x33, 0xf8, 0x2f, 0xe1, 0xf3, 0x39, 0x88, 0x00, 0x20, 0x80, 0x02, 0x0e, 0xce, 0xf8, 0x80, 
0x02, 0x08, 0x00, 0x26, 0x3b, 0x1f, 0x1f, 0xe0, 0x36, 0x1f, 0x61, 0xec, 0x6f, 0x0f, 0x03, 0xfc, 
0xde, 0xe1, 0xf3, 0x0e, 0xdd, 0xb8, 0x0f, 0x99, 0xfc, 0x6e, 0x1e, 0xcc, 0xfc, 0x06, 0x63, 0xf8, 
0x7f, 0x1f, 0xe0, 0x80, 0x02, 0x0f, 0xc1, 0xbc, 0x0f, 0xce, 0xcc, 0x1e, 0x60, 0xcc, 0x1f, 0x82, 
0x00, 0x09, 0xff, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0xf6, 0x0f, 0xc1, 0xf8, 0xfc, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x1f, 0x80, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x98, 0x00, 0x0c, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x26, 0x00, 0x01, 0xe0, 0x00, 0x18, 0x00, 0x66, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x33, 0xf8, 0xdc, 0x08, 0x00, 0x20, 0xcc, 
0x3e, 0xce, 0x60, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0xc6, 0x1c, 0x03, 0x1b, 0xd8, 0xf8, 
0xce, 0xcf, 0xcf, 0xe0, 0xff, 0x1c, 0xc3, 0xf0, 0xfe, 0x0f, 0x03, 0x38, 0x0c, 0x38, 0xc6, 0x66, 
0x18, 0x37, 0xbf, 0x33, 0x81, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x10, 0x6c, 0xcc, 0xdb, 0x63, 0xb1, 
0xdf, 0x31, 0xd8, 0x97, 0x61, 0xb3, 0x7d, 0x88, 0x00, 0x20, 0x80, 0x02, 0x00, 0xc0, 0xf0, 0x80, 
0x02, 0x08, 0x00, 0x27, 0x3f, 0x9f, 0x33, 0x30, 0xe6, 0x03, 0x63, 0x0c, 0x6c, 0x4f, 0xe3, 0x6c, 
0xc6, 0x63, 0xbb, 0x00, 0xdd, 0xbc, 0x7d, 0x98, 0xec, 0x6f, 0x0e, 0xce, 0xc6, 0x3e, 0x63, 0xd8, 
0xe3, 0x18, 0xe0, 0x80, 0x02, 0x1e, 0xc7, 0x3e, 0x18, 0xc6, 0xfe, 0x3f, 0xe3, 0xcc, 0x31, 0x82, 
0x00, 0x09, 0x9b, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x06, 0x1d, 0xc1, 0xf8, 0xec, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x1d, 0x80, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x98, 0x00, 0x0c, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x26, 0x00, 0x01, 0xe0, 0x00, 0x18, 0x00, 0x38, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x31, 0xd8, 0xde, 0x08, 0x00, 0x23, 0xcc, 
0x36, 0xdf, 0x60, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0xc6, 0x0e, 0x1f, 0x19, 0xd8, 0xf8, 
0xcc, 0xe6, 0xc3, 0xe0, 0xfe, 0x3e, 0xc7, 0x30, 0xc6, 0x03, 0x83, 0x7c, 0x1c, 0x7d, 0xc0, 0xec, 
0x18, 0x3d, 0xb3, 0x3f, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x30, 0x00, 0x07, 0x04, 0x00, 0x13, 0x0e, 0xc6, 0x1b, 0x60, 0x30, 
0x1b, 0x18, 0x18, 0xd0, 0x63, 0xbb, 0x6d, 0x88, 0x00, 0x20, 0x80, 0x02, 0x00, 0xc0, 0xc0, 0x80, 
0x02, 0x08, 0x00, 0x23, 0x0d, 0x86, 0x3b, 0x70, 0xfe, 0x03, 0x63, 0xcc, 0x37, 0xec, 0xe3, 0xec, 
0x66, 0x63, 0xbf, 0x00, 0xcd, 0x8c, 0x79, 0x8c, 0x0c, 0x63, 0x00, 0xc6, 0x76, 0x3f, 0x61, 0x18, 
0xf3, 0x1f, 0x60, 0x80, 0x02, 0x18, 0xc7, 0xf6, 0x3c, 0xc6, 0x76, 0x37, 0xe3, 0xfc, 0x79, 0x82, 
0x00, 0x09, 0xc3, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x06, 0x3c, 0xc7, 0xb8, 0x0c, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x01, 0x80, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x98, 0x00, 0x0c, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x26, 0x00, 0x01, 0xe0, 0x00, 0x18, 0x00, 0x1c, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x18, 0x18, 0xc6, 0x08, 0x00, 0x23, 0xfc, 
0x77, 0xdb, 0x60, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0xc6, 0x03, 0x1e, 0x1c, 0x19, 0x8c, 
0xcc, 0x60, 0xc0, 0x60, 0xcc, 0x36, 0xcc, 0xf1, 0xe6, 0x01, 0x83, 0x6c, 0x38, 0x6f, 0x83, 0xcc, 
0x18, 0x39, 0xb3, 0x1e, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x38, 0x00, 0x05, 0x04, 0x00, 0x11, 0xff, 0xc3, 0xff, 0x63, 0xf0, 
0xf3, 0x1c, 0x38, 0x68, 0xe0, 0xff, 0x0f, 0x88, 0x00, 0x20, 0x80, 0x02, 0x33, 0xf3, 0xc0, 0x80, 
0x02, 0x08, 0x00, 0x23, 0x8d, 0xc6, 0x0f, 0x60, 0x1e, 0x0f, 0x63, 0xfc, 0x38, 0x6c, 0x61, 0xc8, 
0x66, 0x60, 0xf7, 0x33, 0xfd, 0x9c, 0x61, 0x8e, 0x1c, 0x67, 0x00, 0xc7, 0x0e, 0x03, 0xe0, 0x18, 
0xff, 0x1f, 0x60, 0x80, 0x02, 0x00, 0xc0, 0xf6, 0x3f, 0xc3, 0x0e, 0x3e, 0xe0, 0x7c, 0x7f, 0x82, 
0x00, 0x08, 0xc3, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x06, 0x3f, 0xc7, 0x18, 0xf8, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x1f, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x98, 0x00, 0x0c, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x26, 0x00, 0x01, 0xe0, 0x00, 0x18, 0x00, 0x06, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x1c, 0x38, 0xce, 0x08, 0x00, 0x20, 0x7c, 
0x1f, 0xc3, 0xe0, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x7c, 0x73, 0x07, 0x0e, 0x39, 0x8c, 
0x6c, 0x71, 0xc0, 0x60, 0xcc, 0x07, 0xcf, 0x30, 0x3e, 0x01, 0xc3, 0x0c, 0x70, 0x0f, 0x03, 0x38, 
0x18, 0x11, 0xbf, 0x00, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x1c, 0x00, 0x07, 0x04, 0x00, 0x10, 0x79, 0xc1, 0xf7, 0x63, 0xe1, 
0xe1, 0x8f, 0xf0, 0x3f, 0xc0, 0x37, 0x1f, 0x08, 0x00, 0x20, 0x80, 0x02, 0x3f, 0xff, 0xc0, 0x80, 
0x02, 0x08, 0x00, 0x21, 0xfc, 0xfe, 0x03, 0x00, 0x06, 0x0e, 0x60, 0x3c, 0x1f, 0xef, 0xc0, 0x0c, 
0x7e, 0x60, 0x73, 0x3f, 0xf9, 0xf8, 0x3f, 0x07, 0xf8, 0x7e, 0x00, 0xc3, 0xfc, 0x00, 0xe0, 0x18, 
0x0f, 0x0e, 0x60, 0x80, 0x02, 0x00, 0xc0, 0x76, 0x03, 0xc3, 0xfe, 0x1c, 0x60, 0x1c, 0x07, 0x82, 
0x00, 0x08, 0x03, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x06, 0x03, 0xc0, 0x18, 0xfc, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x1f, 0x80, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x02, 0x00, 0x08, 0x20, 0x00, 0x8c, 0x00, 0x06, 0x00, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x23, 0x00, 0x01, 0xb0, 0x00, 0x18, 0x00, 0x0e, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x0f, 0xf0, 0xfc, 0x08, 0x00, 0x20, 0x1c, 
0x06, 0xc7, 0xc0, 0x00, 0x00, 0x20, 0x00, 0x82, 0x00, 0x08, 0x38, 0x7f, 0x03, 0x87, 0xf1, 0xfc, 
0x7e, 0x3f, 0x80, 0x60, 0xfc, 0x0f, 0x8f, 0xf1, 0xce, 0x00, 0xc1, 0xf9, 0xe0, 0x1e, 0x03, 0xf0, 
0x18, 0x01, 0x9e, 0x00, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x10, 0x00, 0x40, 0x00, 0x00, 0x06, 0x00, 0x00, 0x04, 0x00, 0x10, 0x00, 0xc0, 0x03, 0x60, 0x30, 
0x01, 0x83, 0xe0, 0x1f, 0x80, 0x33, 0x1e, 0x08, 0x00, 0x20, 0x80, 0x02, 0x1c, 0xdc, 0xc0, 0x80, 
0x02, 0x08, 0x00, 0x20, 0xf8, 0x7c, 0x03, 0x00, 0x06, 0x00, 0x60, 0x0c, 0x0f, 0xc7, 0x80, 0x02, 
0x3c, 0x60, 0x33, 0x1c, 0xc0, 0xf0, 0x1e, 0x01, 0xf0, 0x3c, 0x00, 0xc0, 0xf8, 0x00, 0x60, 0x18, 
0x03, 0x00, 0x60, 0x80, 0x02, 0x00, 0xc0, 0x30, 0x00, 0xc0, 0xf8, 0x00, 0x60, 0x0c, 0x01, 0x82, 
0x00, 0x08, 0x03, 0x04, 0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x06, 0x00, 0xc0, 0x18, 0x0e, 
0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x01, 0xc0, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x82, 0x00, 0x08, 0x20, 0x00, 0x8f, 0x00, 0x07, 0x80, 0x00, 0x80, 0x02, 0x08, 
0x00, 0x23, 0xc0, 0x01, 0xbc, 0x00, 0x18, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 
0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x00, 0x06, 0x04, 
0x00, 0x10, 0x40, 0x01, 0x04, 0x00, 0x10, 0x40, 0x01, 0x03, 0xe0, 0x78, 0x08, 0x00, 0x20, 0x0c, 
0x06, 0xc7, 0x80, 0x00, 0x30, 0x20, 0x00, 0x82, 0x00, 0x08, 0x00, 0x7e, 0x01, 0x03, 0xe0, 0x70, 
0x1e, 0x1f, 0x00, 0x60, 0x78, 0x0f, 0x00, 0x71, 0xf6, 0x00, 0xc0, 0xf1, 0x80, 0x18, 0x01, 0xe0, 
0x18, 0x01, 0x80, 0x00, 0x01, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 0x04, 0x10, 0x00, 0x41, 0x00, 
0x04, 

0x1f, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xe0, 0xff, 0xfe, 0x00, 0x00, 0x00, 0xff, 
0xfe, 0x0f, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x03, 
0xff, 0xf8, 0x00, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x07, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x0c, 0x00, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x18, 0x03, 0x00, 
0x30, 0x02, 0x01, 0x03, 0xff, 0xf8, 0x3f, 0xff, 0x87, 0x00, 0x03, 0x80, 0x00, 0xff, 0xfe, 0x0f, 
0xff, 0xe1, 0xc0, 0x01, 0x9c, 0x00, 0x18, 0x00, 0x00, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x1f, 0xff, 
0xc1, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x00, 0x06, 0x07, 
0xff, 0xf0, 0x7f, 0xff, 0x07, 0xff, 0xf0, 0x7f, 0xff, 0x00, 0x00, 0x00, 0x0f, 0xff, 0xe3, 0x00, 
0x1e, 0x00, 0xc3, 0x00, 0x30, 0x3f, 0xff, 0x83, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 0xfc, 0x1f, 0xff, 0xc1, 0xff, 
0xfc, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 
0x70, 0x04, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x1f, 0x07, 0x20, 0xc1, 0xc8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b, 0xc0, 
0x80, 0x0e, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x86, 0xa7, 0x21, 0xa8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x30, 
0x9c, 0x01, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x80, 0xc6, 0xa0, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x73, 0x00, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xc3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 


};

xFONTYY Tahoma12bBengali_Font = {0x01, 25, 0, 25, 0, 0, 25, 225, 0x0980, 0x09ff,
(PEGUSHORT *) Tahoma12bBengali_offset_table, &Tahoma12bBengali1, 
(PEGUBYTE *) Tahoma12bBengali_data_table};


