#ifndef __SP57
#define __SP57

#ifdef DEBUG_INCLUDES
#pragma message( "+++++++++ including sp57.h +++++++++" )
#endif


/*********************************************************************/
/* marine objects                                                    */
/*********************************************************************/

#define DUMMYO 0
#define AIRARE 1
#define ACHPNT 2
#define ACHBRT 3
#define ACHARE 4
#define BCNCAR 5
#define BCNISD 6
#define BCNLAT 7
#define BCNSAW 8
#define BCNSPP 9
#define BRTFAC 10
#define BRIDGE 11
#define BUIREL 12
#define BUISGL 13
#define BUAARE 14
#define BOYCAR 15
#define BOY<PERSON><PERSON> 16
#define BOYISD 17
#define BOYLAT 18
#define BOY<PERSON><PERSON> 19
#define BOYSPP 20
#define CBLOHD 21
#define CBLSUB 22
#define CBLARE 23
#define CAIRNS 24
#define CANALS 25
#define CANBNK 26
#define CTSARE 27
#define CAUSWY 28
#define CTNARE 29
#define CEMTRY 30
#define CHNWIR 31
#define CHKPNT 32
#define CHIMNY 33
#define CGUSTA 34
#define COALNE 35
#define CONZNE 36
#define COSARE 37
#define CTRPNT 38
#define CRANES 39
#define CUSZNE 40
#define DAMCON 41
#define DWRTPT 42
#define DWRTCL 43
#define DEPARE 44
#define DEPCNT 45
#define DIFFUS 46
#define DSHAER 47
#define DISMAR 48
#define DOCARE 49
#define DRGARE 50
#define DRYDOC 51
#define DMPGRD 52
#define DUNARE 53
#define DYKARE 54
#define DYKCRW 55
#define EXEZNE 56
#define FAIRWY 57
#define FNCLNE 58
#define FERYRT 59
#define FSHHAV 60
#define FSHZNE 61
#define FSHFAC 62
#define FSHGRD 63
#define FLGSTF 64
#define FLASTK 65
#define FLODOC 66
#define FOGSIG 67
#define FORSTC 68
#define FRPARE 69
#define GATCON 70
#define GRIDRN 71
#define HRBARE 72
#define HRBFAC 73
#define HILARE 74
#define HULKES 75
#define ICEARE 76
#define ICNARE 77
#define ITDARE 78
#define LAKARE 79
#define LAKSHR 80
#define LNDARE 81
#define LNDELV 82
#define LNDRGN 83
#define LNDPLC 84
#define LNDSTS 85
#define LIGHTS 86
#define LITMOI 87
#define LITFLT 88
#define LITVES 89
#define LOKBSN 90
#define LOGPON 91
#define MARCUL 92
#define MSTCON 93
#define MIPARE 94
#define MONUMT 95
#define MORFAC 96
#define NATARE 97
#define NAVLNE 98
#define OBSTRN 99
#define OFSPLF 100
#define OFSPRD 101
#define OILBAR 102
#define PILPNT 103
#define PILBOP 104
#define PINGOS 105
#define PIPOHD 106
#define PIPSOL 107
#define PIPARE 108
#define PONTON 109
#define PRCARE 110
#define PRDINS 111
#define PYLONS 112
#define RADDOM 113
#define RADLNE 114
#define RADRNG 115
#define RADRFL 116
#define RADSTA 117
#define RTPBCN 118
#define RDOCAL 119
#define RDOSTA 120
#define RAILWY 121
#define RMPARE 122
#define RAPIDS 123
#define RCRTCL 124
#define RECTRC 125
#define RCTLPT 126
#define RSCSTA 127
#define RESARE 128
#define RIVERS 129
#define RIVBNK 130
#define RODCRS 131
#define ROADPT 132
#define RUNWAY 133
#define SLTPAN 134
#define SNDWAV 135
#define SEAARE 136
#define SPLARE 137
#define SBDARE 138
#define SLCONS 139
#define SISTAT 140
#define SISTAW 141
#define SILBUI 142
#define SLIPWY 143
#define SLOTOP 144
#define SLOGRD 145
#define SMCFAC 146
#define SOUNDG 147
#define SPOGRD 148
#define SPRING 149
#define SQUARE 150
#define STSLNE 151
#define SUBTLN 152
#define TNKCON 153
#define TELPHC 154
#define TESARE 155
#define TIDEWY 156
#define TOPMAR 157
#define TOWERS 158
#define TSELNE 159
#define TSSBND 160
#define TSSCRS 161
#define TSSLPT 162
#define TSSRON 163
#define TSEZNE 164
#define TREPNT 165
#define TNLENT 166
#define TWRTPT 167
#define UWTROC 168
#define VEGARE 169
#define WATFAL 170
#define WATTUR 171
#define WEDKLP 172
#define WIRLNE 173
#define WNDMIL 174
#define WIMCON 175
#define WRECKS 176
#define ZEMCNT 177
#define AIRPOR 178
#define ANCHOR 179  /* Composite objects    */
#define CHAEDG 180
#define DPWTRT 181
#define DEFWAT 182
#define HARBOR 183
#define regiog 184	/* was RANGES 184 - changed on 27/3/2003 */
#define LITHOU 185
#define MORTRO 186
#define NAMFLO 187
#define NAMFIX 188
#define TSSSYS 189
#define _CLOLN 190  /* Cartographic objects   */
#define _COMPS 191
#define _CSYMB 192
#define _LINES 193
#define _AREAS 194
#define _SHABL 195
#define _TEXTS 196
#define _cmapl 197  /* Cartographic line cmap */
#define _texto 198  /* Standard text cmap     */
#define _slgto 199  /* Standard light cmap    */
#define _boygn 200  /* Generic boy c-map      */
#define _extgn 201
#define _cmapa 202  /* Generic area cmap      */
#define _bcngn 203  /* Beacon generic         */
#define ndtare 204  /* No data area           */
#define LOCMAG 205
#define M_ACCY 206
#define M_CSCL 207
#define M_HDAT 208
#define M_NPUB 209
#define M_PROD 210
#define M_SDAT 211
#define M_SREL 212
#define M_SSOR 213
#define M_UNIT 214
#define M_VDAT 215
#define _NTEXT 216
#define _m_sor 217
#define incsur 218
#define _m_bug 219
#define filler 220
#define clpare 221

#define nrelne 222 /* ex 440 replacing C_PRCL 222									*/
#define hrblne 223 /* ex 446 replacing C_BL_1 223									*/
#define curlne 224 /* ex 425 replacing C_BL_2 224									*/
#define lmalne 225 /* ex 426 replacing C_BL_S 225									*/
#define lines1 226 /* ex dnglne 423, brklne 424 replacing C_BL_B 226				*/
#define lines2 227 /* ex lnolne 438, reslne 439 replacing C_RC_1 227				*/
#define lines3 228 /* ex klplne 435, roclne 436, corlne 437 replacing C_RC_B 228	*/
#define lines4 229 /* ex natlne 441, teslne 442, conlne 443, fshlne 444,			*/
                   /* eezlne 445 replacing C_RS_1 229								*/
#define lines5 230 /* ex icelne 427, tunlne 428, drglne 429, wrklne 430,			*/
                   /* ffclne 431, limlne 432, loglne 433, spolne 434				*/
                   /* replacing C_RS_S 230											*/


#define cwyitd 231 /* ex 400 replacing C_RS_B	*/
#define cwysub 232 /* ex 401 replacing C_INST	*/
#define dpg_02 233 /* ex 402 replacing C_AOGR	*/
#define dpg_03 234 /* ex 403 replacing C_DSLN	*/
#define dpg_04 235 /* ex 404 replacing C_DPLN	*/
#define prisub 236 /* ex 405 replacing C_ARRW	*/
#define slcitd 237 /* ex 419 replacing C_CRSS	*/
#define slcsub 238 /* ex 420 replacing C_HOOK	*/
#define slpitd 239 /* ex 421 replacing C_UNIO	*/
#define slpsub 240 /* ex 422 replacing C_FLTG	*/

#define P_OTHR 241

/*
#define C_PRCL 222
#define C_BL_1 223
#define C_BL_2 224
#define C_BL_S 225
#define C_BL_B 226
#define C_RC_1 227
#define C_RC_B 228
#define C_RS_1 229
#define C_RS_S 230
#define C_RS_B 231
#define C_INST 232
#define C_AOGR 233
#define C_DSLN 234
#define C_DPLN 235
#define C_ARRW 236
#define C_CRSS 237
#define C_HOOK 238
#define C_UNIO 239
#define C_FLTG 240
#define C_MRKR 241
*/

#define TIDHGH 242 /* marine object */

/*********************************************************************/
/* port info objects                                                 */
/*********************************************************************/

#define P_AREA 243
#define P_HRBM 244
#define P_COAG 245
#define P_POLI 246
#define P_CSTM 247
#define P_EMRG 248
#define P_PSTO 249
#define P_YCCL 250
#define P_BTYR 251
#define P_ACCE 252
#define P_ELER 253
#define P_ENGR 254
#define P_SAIL 255
#define P_DIVI 256
#define P_HOTE 257
#define P_REST 258
#define P_BANK 259
#define P_PHAR 260
#define P_PORT 261
#define P_WAYP 262
#define P_SLIP 263
#define P_BHST 264
#define P_CRNE 265
#define P_FUEL 266
#define P_WATR 267
#define P_ELEC 268
#define P_SHWR 269
#define P_LNDR 270
#define P_TOIL 271
#define P_PSTB 272
#define P_TELE 273
#define P_RFSB 274
#define P_VBER 275
#define P_CHAN 276
#define P_PROV 277
#define P_BGAS 278
#define P_CRPR 279
#define P_BTPR 280
#define P_CVST 281
#define P_CMST 282
#define P_SEWE 283
#define P_GRAP 284
#define P_RDIO 285
#define P_RTEL 286
#define P_TRNS 287
#define P_CMFH 288
#define P_REPR 289

#define DRYHGH 290
#define BANNER 291
#define rea_02 292 /* ex 406	*/
#define rea_03 293 /* ex 407	*/
#define P_TOWB 294
#define P_PART 295
#define P_TACK 296
#define TIDSTR 297

/*********************************************************************/
/* aeronautical objects                                              */
/*********************************************************************/

#define GRDMOR 300
#define VHFNAV 301
#define NDBNAV 302
#define ENRWAY 303
#define ENRMKR 304
#define HOLPAT 305
#define ENRAIR 306
#define ENRCOM 307
#define ARPORT 308
#define ARPRPT 309
#define AIRGAT 310
#define TERWAY 311
#define __SIDS 312
#define _STARS 313
#define APPROU 314
#define RUNWYS 315
#define ILSDAT 316
#define ILSMKR 317
#define TERNDB 318
#define ___MSA 319
#define ARPCOM 320
#define ARPSEC 321
#define ___FIR 322
#define ___UIR 323
#define CONAIR 324
#define RESAIR 325
#define CRUTAB 326
#define COMROU 327
#define VEROBS 328
#define a_arpt 329 /* AOPA */
#define a__fbo 330 /* AOPA */
#define WAYPNT 331
#define VFRROT 332
#define VFRREP 333
#define APPWAY 334

/*****************************************************************************
OTHER MAGELLAN POINTS OF INTEREST OBJECTS
*****************************************************************************/

#define aviati 350
#define ___atm 351
#define autrep 352
#define gassta 353
#define exramp 354
#define hotels 355
#define medfac 356
#define eparks 357
#define restau 358
#define rvserv 359
#define _truck 360
#define _boats 361
#define __dive 362
#define _marin 363 /* #define marina 363 */
#define fixnav 364
#define flonav 365
#define lgthou 366
#define wreobs 367
#define campgr 368
#define firarm 369
#define aeract 370
#define __golf 371
#define hunfis 372
#define resort 373
#define __wine 374
#define museum 375
#define garden 376
#define aquzoo 377
#define sarena 378
#define touatt 379
#define sitsee 380
#define aparks 381
#define _parks 382
#define region 383
#define _tapoi 384
#define _water 385
#define cmgare 386
#define cmpgnd 387
#define cmpare 388
#define cmpsit 389

#define P_AIRP 390
#define P_TURI 391
#define P_HULR 392
#define P_SPRT 393
#define P_BREN 394
#define P_CREN 395
#define P_OFFC 396
#define P_INTN 397
#define P_CHRT 398
#define P_NEWS 399
#define P_BOUT 400
#define P_LIGH 401
#define P_WATC 402
#define P_DIVS 403
#define P_MORS 404
#define P_PRDV 405
#define P_BARP 406
#define P_FAID 407
#define declab 408
#define P_LBVS 409
#define ELVARE 410
#define mmcont 411
#define reagb1 412
#define reagb2 413
#define reagb3 414
#define reagb4 415
#define reagb5 416
#define reagb6 417
#define reagb7 418
#define reagb8 419
#define reagb9 420
#define TIBHGH 421
#define TIBSTR 422
#define ertskn 423
#define O_REAR 424
#define O_STAT 425
#define O_FISH 426
#define O_SPFE 427
#define O_SIDE 428
#define O_WASO 429
#define O_SHOR 430
#define O_BOTY 431
#define O_WACO 432
#define O_COVE 433
#define O_FICO 434
#define O_SENO 435
#define O_TYFO 436
#define O_STRE 437
#define O_WATR 438
#define O_GETT 439
#define O_HUNT 440
#define O_SBLI 441
#define O_RECO 442
#define O_ACCE 443
#define O_WARE 444
#define barfre 445
#define bmpelv 446
#define pwrlin 447
#define zip7uk 448
#define GRID3D 449
#define SATGRD 450
#define HGTCRT 451

/*****************************************************************************
RASTER LAYER
*****************************************************************************/
#define TEXT3D 452
#define SATTXT 453
#define MAPTXT 454


#define W_PORT 455
#define mpwrn1 456	//##SSDAMSEG dummy object for "Upgrade Warning" message. Look for it all over the lib for details

/*********************************************************************/
/* marine attributes                                                 */
/*********************************************************************/

#define BURDEP 1
#define CATAIR 2
#define CATACH 3
#define CATBRG 4
#define CATBUA 5
#define CATCBL 6
#define CATCAM 7
#define CATCHP 8
#define CATCTR 9
#define CATCRN 10
#define CATDIS 11
#define CATDPG 12
#define CATDYK 13
#define CATFNC 14
#define CATFRY 15
#define CATFIF 16
#define CATFOG 17
#define CATFOR 18
#define CATGAT 19
#define CATHAF 20
#define CATICE 21
#define CATINB 22
#define CATLND 23
#define CATLAM 24
#define CATLIT 25
#define CATMFA 26
#define CATMST 27
#define CATMPA 28
#define CATMOR 29
#define CATNAV 30
#define CATOFP 31
#define CATOLB 32
#define CATPLE 33
#define CATPIL 34
#define CATPIP 35
#define CATPRI 36
#define CATRAS 37
#define CATRTB 38
#define CATROS 39
#define CATTRK 40
#define CATREB 41
#define CATRSC 42
#define CATREA 43
#define CATROD 44
#define CATSEA 45
#define CATSLC 46
#define CATSIT 47
#define CATSIW 48
#define CATBUI 49
#define CATSCF 50
#define CATSPM 51
#define CATTOW 52
#define CATTSS 53
#define CATTRE 54
#define CATVEG 55
#define CATWAT 56
#define CATWRK 57
#define LITCHR 58
#define COLOUR 59
#define COLMAR 60
#define COLPAT 61
#define COMCHA 62
#define CONDTN 63
#define CONRAD 64
#define CONVIS 65
#define DRVAL1 66
#define DRVAL2 67
#define EXCLIT 68
#define EXPSOU 69
#define SIGFRQ 70
#define SIGGEN 71
#define SIGGRP 72
#define HEIGHT 73
#define HORCLR 74
#define HORDAT 75
#define HORLEN 76
#define HORWID 77
#define INFORM 78
#define LIFCAP 79
#define OBJNAM 80
#define NATION 81
#define NATSUR 82
#define NATCON 83
#define MARSYS 84
#define ORIENT 85
#define SIGPER 86
#define PICREP 87
#define PILDST 88
#define PRODCT 89
#define NATQUA 90
#define QUAPOS 91
#define QUASOU 92
#define QUAVEM 93
#define NLPDST 94
#define RADIUS 95
#define RECDAT 96
#define RECIND 97
#define SCAMAX 98
#define SCAMIN 99
#define SECTR1 100
#define SECTR2 101
#define SIGSEQ 102
#define BCNSHP 103
#define BUISHP 104
#define BOYSHP 105
#define TOPSHP 106
#define SORDAT 107
#define SORIND 108
#define STATUS 109
#define SUPLIT 110
#define TECSOU 111
#define TRAFIC 112
#define VALDCO 113
#define VALMXR 114
#define VALNMR 115
#define VALSOU 116
#define VERCLR 117
#define VERCCL 118
#define VERCOP 119
#define VERCSA 120
#define VERDAT 121
#define VERLEN 122
#define LITVIS 123
#define WATLEV 124
#define _CSIZE 125
#define _VARIA 126
#define _CYEAR 127
#define _ANNCH 128
#define _SCODE 129
#define _ROTAT 130
#define _SCALE 131
#define _TINTS 132
#define _TXSTR 133
#define _JUSTH 134
#define _JUSTV 135
#define SPACE_ 136
#define _CHARS 137
#define _lcode 138  /* Cmap generic line attribute code           */
#define _texta 139  /* Cmap text attribute code                   */
#define _slgta 140  /* Cmap standard light attribute code         */
#define _acode 141  /* Cmap generic area code                     */
#define _COLOR 142
#define DATEND 143
#define DATSTA 144
#define PEREND 145
#define PERSTA 146
#define CATCOA 147
#define CATMNT 148
#define AGENCY 149
#define CPDATE 150
#define CSCALE 151
#define DUNITS 152
#define ESTRNG 153
#define HUNITS 154
#define HORACC 155
#define NMDATE 156
#define PRCTRY 157
#define PUBREF 158
#define RADWAL 159
#define SCVAL1 160
#define SCVAL2 161
#define SDISMX 162
#define SDISMN 163
#define SURATH 164
#define SUREND 165
#define SURSTA 166
#define SURTYP 167
#define VALLMA 168
#define VERACC 169
#define NINFOM 170
#define NOBJNM 171
#define _NTXST 172
#define _chcod 173
#define _dgdat 174
#define _quart 175
#define _refco 176
#define _sorhd 177
#define _wgsox 178
#define _wgsoy 179
#define _hvdat 180
#define _textl 181
#define MARKER 182
#define XOFFST 183
#define YOFFST 184
#define FONTSX 185
#define FONTSY 186
#define HIDDEN 187
#define TEXTTY 188
#define _err_c 189
#define _err_s 190

/*********************************************************************/
/* cadastrial attributes                                             */
/*********************************************************************/

#define PRCLNO 191
#define BLDGNO 192
#define CATARW 193
#define CATCRS 194

#define TIMEZN 195 /* marine attribute */
#define COEFFS 196 /* marine attribute */

/*********************************************************************/
/* port info attributes                                              */
/*********************************************************************/

#define ACCESS 197
#define BERTNO 198
#define CMAPST 199
#define CRNCAP 200
#define DANGER 201
#define DSTRCT 202
#define FUELTY 203
#define LATVAL 204
#define LONVAL 205
#define MAXDRA 206
#define MAXLEN 207
#define PREWIN 208
#define PROHIB 209
#define FATHID 210
#define REFUGE 211
#define SHELTR 212
#define SPELIM 213
#define TELCOD 214
#define TELNUM 215
#define TIME_1 216
#define TIME_2 217
#define VHFCHA 218
#define PLEVEL 219
#define CLEVEL 220
#define HBRSIZ 221
#define HBRTYP 222
#define ENTRES 223
#define TURARE 224
#define PILOTA 225
#define __TUGS 226
#define DEGAUS 227
#define DIRBAL 228
#define CRNTYP 229
#define BHSCAP 230

/* Colored buoy stripes attribute */
#define STRIPS 231

/*****************************************************************************
OTHER PORT INFO/MULTICOMPLEX ATTRIBUTES
*****************************************************************************/

#define MARCAT 232
#define AIRCAT 233
#define FSHCAT 234
#define REPCAT 235
#define DLEVEL 236
#define DRPPOS 237

/* Colored buoy/light drop attribute */
#define DRPPOS 237
#define _IMAGE 238
#define NTCHRS 239

/*********************************************************************/
/* aeronautical attributes                                           */
/*********************************************************************/

#define RCDTPE 300
#define CSTARE 301
#define ARPIDT 302
#define ROUTPE 303
#define RTEIDT 304
#define SIDSTA 305
#define APPIDT 306
#define TRSIDT 307
#define SEQNBR 308
#define FIXIDT 309
#define ICACOD 310
#define DESCOD 311
#define BDYCOD 312
#define _LEVEL 313
#define TURDIR 314
#define PATTER 315
#define TURVAL 316
#define RECNAV 317
#define THEANG 318
#define RHOANG 319
#define OBMCRS 320
#define ROUDIS 321
#define INMCRS 322
#define ALTDES 323
#define ALTMIN 324
#define VORIDT 325
#define VORFRE 326
#define NDBFRE 327
#define VHFCLS 328
#define LMFCLS 329
#define LOCCLS 330
#define DMEIDT 331
#define MAGVAR 332
#define DMEELE 333
#define REGCOD 334
#define EWAYTY 335
#define TWAYTY 336
#define WAYDES 337
#define LOCIDT 338
#define LOCFRE 339
#define RWYIDT 340
#define LOCBRG 341
#define LOCPOS 342
#define LOCREF 343
#define GLDPOS 344
#define LOCWTH 345
#define GLDANG 346
#define MINANG 347
#define TRAALT 348
#define LONRWY 349
#define ARPELE 350
#define GATIDT 351
#define RWYLGH 352
#define RWYMAG 353
#define RWYDES 354
#define _NOTES 355
#define INBCRS 356
#define __TURN 357
#define LEGLGH 358
#define LEGTME 359
#define STNDEC 360
#define TSDHGT 361
#define TSDELE 362
#define TSDDIS 363
#define VERANG 364
#define NAMFLD 365
#define SPDLIM 366
#define SPDALT 367
#define COMELE 368
#define ARPFIX 369
#define COMIDT 370
#define VIACOD 371
#define VIARTE 372
#define STOWAY 373
#define _DATUM 374
#define ATCIND 375
#define WAYUSA 376
#define _TOFIX 377
#define RWYTRS 378
#define ENRTRS 379
#define CRSALT 380
#define TERARP 381
#define ALTDIS 382
#define CSTIDX 383
#define ILSBIS 384
#define FACELE 385
#define FACCHR 386
#define TRUBRG 387
#define GOVSOU 388
#define GLDWTH 389
#define TCHELE 390
#define TCHLOC 391
#define MKRTPE 392
#define MINBRG 393
#define COMTPE 394
#define _RADAR 395
#define COMFRE 396
#define FREUNI 397
#define CALSIG 398
#define ASEIND 399
#define ESEIND 400
#define ATAIAT 401
#define IFRCAP 402
#define RWYWTH 403
#define MRKIDT 404
#define MRKCOD 405
#define MRKSPE 406
#define HGHLOW 407
#define DUPIND 408
#define DIRRES 409
#define FIRIDT 410
#define FIRIND 411
#define BRYVIA 412
#define ARCDIS 413
#define ARCBRG 414
#define LOWLIM 415
#define UPRLIM 416
#define FIRRUS 417
#define FIRRUA 418
#define FIRENT 419
#define FIRNAM 420
#define RESNAM 421
#define ALTMAX 422
#define RESTPE 423
#define RESDES 424
#define MULCOD 425
#define TMECOD 426
#define _NOTAM 427
#define UNIND1 428
#define UNIND2 429
#define CRUIDT 430
#define COURSE 431
#define CRULEV 432
#define VERSEP 433
#define TMEIND 434
#define CONAGE 435
#define X_MORA 436
#define Y_MORA 437
#define V_MORA 438
#define CENFIX 439
#define RADLIM 440
#define SECBRG 441
#define SECALT 442
#define ENRARP 443
#define FMERIT 444
#define FREDIS 445
#define FIRADD 446
#define STAIND 447
#define STADAT 448
#define RESIDT 449
#define AIRDAT 450
#define UNIALT 451
#define RESALT 452
#define STEIND 453
#define RESNOT 454
#define EUINDI 455
#define MAGIND 456
#define CHANNE 457
#define MLSBRG 458
#define PROANG 459
#define ELESPA 460
#define DECHGT 461
#define MINDEC 462
#define COVSEC 463
#define NOMANG 464
#define RESLIN 465
#define HOLSPD 466
#define PADDIM 467
#define PUBMIL 468
#define TIMZON 469
#define DAYIND 470
#define PADIDT 471
#define H24IND 472
#define GUATRA 473
#define SECBEG 474
#define SECEND 475
#define COMAL1 476
#define COMAL2 477
#define SECFAC 478
#define NARRAT 479
#define DISDES 480
#define COMDIS 481
#define REMNAM 482
#define RDOIDT 483
#define TRISTA 484
#define GRPINT 485
#define ADDFAC 486
#define INIARP 487
#define TMEOPE 488
#define NAMIND 489
#define ILSCAT 490
#define MODULA 491
#define SIGEMI 492
#define REMFAC 493
#define RESREC 494
#define EXCIND 495
#define BLOIND 496
#define ARCRAD 497
#define NAVLIM 498
#define COMIND 499
#define SECFRO 500
#define DISLIM 501
#define ALTLIM 502
#define SEQEND 503
#define __CITY 504
#define _STATE 505
#define COUNTR 506
#define CASIND 507
#define CASIDT 508
#define CASICA 509
#define FUETPE 510
#define OXYGEN 511
#define REPAIR 512
#define LANIND 513
#define PATALT 514
#define JETUNI 515
#define WINALE 516
#define BCNLGT 517
#define SFCTPE 518
#define RWYSUR 519
#define RWYLIG 520
#define APPLIG 521
#define CENLIG 522
#define RWYEND 523
#define TCHLIG 524
#define TMELIG 525
#define PILLIG 526
#define PATIND 527
#define LANLGH 528
#define TAKLGH 529
#define TAKDIS 530
#define ACCDIS 531
#define OVERRU 532
#define QLTPOS 533
#define SECCEN 534
#define AIRTPE 535
#define AIRCEN 536
#define CONNAM 537
#define RWYUSE 538
#define CUSTOM 539
#define CYCDAT 540
#define SECICA 541
#define LATPNT 542
#define LONPNT 543
#define __AMSL 544
#define ___AGL 545
#define OBSTYP 546
#define CATOBS 547
#define ARPAID 548
#define VERSTS 549
#define STBIND 550
#define MRKIND 551
#define ALOLIM 552
#define AUPLIM 553
#define _restr 554 /* AOPA */
#define _hotel 555 /* AOPA */
#define _carnt 556 /* AOPA */
#define __taxi 557 /* AOPA */
#define _hours 558 /* AOPA */
#define _geser 559 /* AOPA */
#define _piser 560 /* AOPA */
#define _acren 561 /* AOPA */
#define _afrep 562 /* AOPA */
#define _pprep 563 /* AOPA */
#define _prrep 564 /* AOPA */
#define _rdrep 565 /* AOPA */
#define _rirep 566 /* AOPA */
#define _ftrep 567 /* AOPA */
#define _flins 568 /* AOPA */
#define _crcrd 569 /* AOPA */
#define _trchk 570 /* AOPA */
#define AREACT 571
#define catreg 572 /* TeleAtlas */
#define catpoi 573 /* TeleAtlas */
#define catcmp 574
#define cmpsvc 575
#define _texts 576 /* _texto styles and offsets, previously inside the _texta attribute */
#define roaidp 577 /* TeleAtlas positive restriction attribute */
#define roaidn 578 /* TeleAtlas negative restriction attribute */
#define APPTYP 579 
#define TSDDIR 580
#define fstdb1 581
#define fstdb2 582
#define ___kph 583
#define _felev 584
#define _telev 585
#define ternod 586
#define manidt 587
#define manseq 588
#define mancat 589 
#define sgnidt 590
#define sgnseq 591
#define sgnnam 592
#define clpstp 593
#define _hntyp 594
#define _hnrng 595
#define tidjkr 596
#define entacl 597
#define entvad 598
#define CATPIS 599
#define FAXNUM 600
#define ADDRES 601
#define E_MAIL 602
#define WEBSIT 603
#define CRDCAR 604
#define PARKTY 605
#define DISACC 606
#define DELVRY 607
#define CATHUR 608
#define CTYNAM 609
#define FRENUM 610
#define CATHRB 611
#define PMNGMT 612
#define PORTYP 613
#define PORCTN 614
#define MORTYP 615
#define PILCND 616
#define ELECTY 617
#define PO_BOX 618
#define INTCON 619
#define AFFNTY 620
#define PPDISC 621
#define PROVTY 622
#define TRNSTY 623
#define isnlnd 624
#define fnctnm 625
#define catnmk 626
#define catwwm 627
#define comobj 628
#define yuzoom 629
#define newtpm 630
#define depnum 631
#define lndnum 632
#define MLTMDA 633
#define XYOFSM 634
#define strtyp 635
#define INFBCN 636
#define eMCacl 637
#define ROT200 638
#define MOORNO 639
#define HASDCK 640
#define HGTMIN 641
#define HGTMAX 642
#define ELEVAT 643
#define catdec 644
#define NATRES 645
#define mlinfo 646
#define stmtyp 647
#define txtdsc 648	
#define zipcod 649
#define staten 650
#define prefix 651
#define suffix 652
#define zipcd1 653
#define rsinfo 654
#define loclty 655
#define tmcpsb 656
#define tmcloc 657
#define cobpoi 658
#define provin 659
#define comune 660
#define rgarea 661
#define __MMSI 662
#define county 663
#define sealnk 664
#define zipcd7 665
#define subcat 666
#define catob3 667
#define RESTRN 668
#define bmpmin 669 
#define bmpmax 670
#define OBTCRS 671
#define INTCRS 672
#define ALBMIN 673
#define ALBMAX 674
#define rtudel 675
#define inftyp 678
#define _rtdir 679
#define _flag_ 680
#define triang 681
#define vert3d 682
#define conv_m 683
#define hgt_3d 684
#define hgt3dm 685
#define dummya 686
#define declb1 687
#define declb2 688
#define _ntacl 689

#define SP57_LAST_ATTRIBUTE	_ntacl
#define SP57_LAST_OBJECT	mpwrn1

#endif
