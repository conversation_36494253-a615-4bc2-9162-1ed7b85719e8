#include "Wnd.hpp"

#ifndef __STATUS_LIST_WND_HPP__
#define __STATUS_LIST_WND_HPP__

class CStatusListWnd : public CWnd {
	protected:
		int m_nCurSel;
		int m_nStartViewPos;

	public:
		CStatusListWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		void DrawTextListScrollBar();
		void DrawTextList();
		
		void SetFocus(int nFocus)   { m_nFocus = nFocus;   }
		void SetCurSel(int nCurSel) { m_nCurSel = nCurSel; }
		int  GetCurSel()            { return m_nCurSel;    }
		
		//void InitStatusMsg();
};

#endif
