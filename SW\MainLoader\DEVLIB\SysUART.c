/*...........................................................................*/
/*.                  File Name : SYSUART.C                                  .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysUART.h"

#include <string.h>

void  SysSetUART(xSYS_UART *pSysUART,int nSpeed,int nParity,int nDataBit,int nStopBit,int nTxInt,int nRxInt)
{
      HWORD wTempX;
      DWORD dTempX;
      DWORD dClkDiv = 2;

      wTempX = 0x0000;                               // 5,n,1
      if (nParity == UART_PARITY_ODD)  wTempX |= (4 << 3);
      if (nParity == UART_PARITY_EVEN) wTempX |= (5 << 3);
      if (nStopBit== UART_STOP_BIT_2)  wTempX |= (1 << 2);
      if (nDataBit== UART_DATA_BIT_6)  wTempX |= (1 >> 0);
      if (nDataBit== UART_DATA_BIT_7)  wTempX |= (2 >> 0);
      if (nDataBit== UART_DATA_BIT_8)  wTempX |= (3 >> 0);
      pSysUART->wLCON    = wTempX;
      pSysUART->wUCON    = 0;
      pSysUART->wFCON    = (1 << 2) | (1 << 1);      // FIFO Reset
      pSysUART->wMCON    = (1 << 7) | (1 << 6);      // Half-UART,UART TX/RX, No AFC
      pSysUART->wTIMEOUT = 0x001F;
      pSysUART->wINTCON	&= 0x000F;

      pSysUART->dCLKENB &= (~(1 << 2));              // Clock     Disable
      pSysUART->dCLKENB |= (1 << 3);                 // PCLKMODE  Enable
      dTempX = pSysUART->dCLKGEN & ~(7 << 1);
      pSysUART->dCLKGEN  = dTempX | (1 << 1);        // CLKSRCSEL = PLL1

      dTempX = pSysUART->dCLKGEN & ~(0x3f << 4);
      pSysUART->dCLKGEN  = dTempX | ((dClkDiv - 1) << 4);

      pSysUART->dCLKENB |= (1 << 2);                 // Clock     Enable

      SysClearUartIntPendingAll(pSysUART);
      SysSetUartIntEnableAllMode(pSysUART,0);        // All Int   Disable

      pSysUART->wFCON &= (~(1 << 0));                // FIFO      Disable

      pSysUART->wUCON |= (1 << 9);                   // TX_INT    Level-Type
      pSysUART->wUCON &= (~(3 << 2));
      pSysUART->wUCON |= (1 << 2);                   // TX        Int or Poll

      SysResetUartTxFIFO(pSysUART);
      SysSetUartTxFifoTrgLevel(pSysUART,UART_TX_FIFO_TRG_LEVEL_4);

      SysResetUartRxFIFO(pSysUART);
      SysSetUartRxFifoTrgLevel(pSysUART,UART_RX_FIFO_TRG_LEVEL_12);

      SysGetUartErrorStatus(pSysUART);

      pSysUART->wUCON |= (1 << 8);                   // RX_INT    Level-Type
      pSysUART->wUCON &= (~(3 << 0));
      pSysUART->wUCON |= (1 << 0);                   // RX        Int or Poll

      pSysUART->wFCON |= (1 << 0);                   // FIFO      Enable
      SysResetUartRxFIFO(pSysUART);

      SysSetUartSyncPendClear(pSysUART);

      SysClearUartIntPendingAll(pSysUART);
      SysSetUartIntEnableAllMode(pSysUART,0);        // All Int   Disable

      SysSetUartSyncPendClear(pSysUART);

      SysSetUartBaudRate(pSysUART,nSpeed);

      SysSetUartRxTimeOutIntEnable(pSysUART);
      if (nTxInt == UART_TX_INT_ENABLE)
          SysSetUartTxIntEnable(pSysUART);
      if (nRxInt == UART_RX_INT_ENABLE)
         {
          SysSetUartRxIntEnable(pSysUART);
          SysSetUartRxTimeOutIntEnable(pSysUART);
         }
      SysSetUartErrorIntEnable(pSysUART);
}
void  SysSetUartBaudRate(xSYS_UART *pSysUART,int nSpeed)
{
      DWORD dClkSrc;
      DWORD dClkDiv;
      DWORD dTempX;

      dClkDiv= (pSysUART->dCLKGEN >> 4) & 0x3f;
      dClkDiv= dClkDiv + 1;
//    dClkSrc= ARM_PCLK / dClkDiv;
      dClkSrc= CPU_PLL1_FREQUENCY / dClkDiv;
      
      dClkSrc= dClkSrc * 10;
      dTempX = dClkSrc / (nSpeed * 16);
      dTempX = (dTempX + 5) / 10;
      pSysUART->wBRD = dTempX - 1;
}
void  SysClearUartIntPendingAll(xSYS_UART *pSysUART)
{
      pSysUART->wINTCON |= (0xf << 0);
}
void  SysSetUartIntEnableAllMode(xSYS_UART *pSysUART,int nIntAllMode)
{
      HWORD wIntMode;

      if (nIntAllMode)
          wIntMode = 0xf;
      else
          wIntMode = 0x0;
      pSysUART->wINTCON &= ~(0xf << 4);
      pSysUART->wINTCON |= (wIntMode << 4);
}
void  SysSetUartTxIntEnable(xSYS_UART *pSysUART)
{
      pSysUART->wINTCON |= (1 << 4);
}
void  SysSetUartTxIntDisable(xSYS_UART *pSysUART)
{
      pSysUART->wINTCON &= ~(1 << 4);
}
void  SysSetUartRxIntEnable(xSYS_UART *pSysUART)
{
      pSysUART->wINTCON |= (1 << 5);
}
void  SysSetUartRxIntDisable(xSYS_UART *pSysUART)
{
      pSysUART->wINTCON &= ~(1 << 5);
}
void  SysSetUartRxTimeOutIntEnable(xSYS_UART *pSysUART)
{
      pSysUART->wUCON |= (1 << 7);
}
void  SysSetUartRxTimeOutIntDisable(xSYS_UART *pSysUART)
{
      pSysUART->wUCON &= ~(1 << 7);
}
void  SysSetUartErrorIntEnable(xSYS_UART *pSysUART)
{
      pSysUART->wUCON |= (1 << 6);
}
void  SysSetUartErrorIntDisable(xSYS_UART *pSysUART)
{
      pSysUART->wUCON &= ~(1 << 6);
}
void	SysResetUartTxFIFO(xSYS_UART *pSysUART)
{
      pSysUART->wFCON |= (1 << 2);                   // TX-FIFO Clear
      while (pSysUART->wFCON & (1 << 2));
}
void	SysResetUartRxFIFO(xSYS_UART *pSysUART)
{
      pSysUART->wFCON |= (1 << 1);                   // RX-FIFO Clear
      while (pSysUART->wFCON & (1 << 1));
}
void  SysSetUartTxFifoTrgLevel(xSYS_UART *pSysUART,int nTrgLevel)
{
      HWORD wTempX;

      wTempX = UART_TX_FIFO_TRG_LEVEL_8;
      if (nTrgLevel >= UART_TX_FIFO_TRG_LEVEL_0 && nTrgLevel <= UART_TX_FIFO_TRG_LEVEL_12)
          wTempX = nTrgLevel;

      pSysUART->wFCON &= (~(3 << 6));
      pSysUART->wFCON |= (wTempX << 6);
}
void  SysSetUartRxFifoTrgLevel(xSYS_UART *pSysUART,int nTrgLevel)
{
      HWORD wTempX;

      wTempX = UART_RX_FIFO_TRG_LEVEL_8;
      if (nTrgLevel >= UART_RX_FIFO_TRG_LEVEL_1 && nTrgLevel <= UART_RX_FIFO_TRG_LEVEL_12)
          wTempX = nTrgLevel;

      pSysUART->wFCON &= (~(3 << 4));
      pSysUART->wFCON |= (wTempX << 4);
}
DWORD SysGetUartErrorStatus(xSYS_UART *pSysUART)
{
      return(pSysUART->wESTATUS & 0x000f);
}
void  SysSetUartSyncPendClear(xSYS_UART *pSysUART)
{
      pSysUART->wLCON |= (1 << 7);                   // SYNC_PEND Clear
      while (pSysUART->wLCON & (1 << 7));
}

