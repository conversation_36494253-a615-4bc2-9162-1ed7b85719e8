/*...........................................................................*/
/*.                  File Name : SYSLIB.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "CpuAddr.h"
#include "SysConstSpica.h"
#include "SysMMU.h"
#include "SysPLL.h"
#include "SysMCUS.h"
#include "SysGPIO.h"
#include "SysAlive.h"
#include "SysTimer.h"
#include "SysUART.h"
#include "SysMLC.h"
#include "SysDPC.h"
#include "SysLCD.h"
#include "SysPWM.h"
#include "SysSDCD.h"
#include "SdCardDRV.h"
#include "Nand64.h"
#include "osfile.h"
#include "DataType.h"

#include <sys/stat.h>
#ifndef  __SYSLIB_H__
#define  __SYSLIB_H__

#if defined(__USE_RTOS__)
    #include  "includes.h"
#endif

#ifdef  __cplusplus
extern "C" {
#endif

//========================================================================
extern int    FPGA_MUL_FACTOR;
//========================================================================
long  SysGetStartOfHeapMemory(void);
long  SysGetLastOfHeapMemory(void);
long  SysGetStartOfFreeMemory(void);
void  SysDisableICache(void);
void  SysEnableICache(void);
void  SysDisableDCache(void);
void  SysEnableDCache(void);
void  SysDisableAlignFault(void);
void  SysEnableAlignFault(void);
void  SysDisableMMU(void);
void  SysEnableMMU(void);
void  SysSetFastBusMode(void);
void  SysSetAsyncBusMode(void);
void  SysSetTTBase0(DWORD dBase);
void  SysSetTTBase1(DWORD dBase);
void  SysSetTTBaseControl(DWORD dCtrl);
void  SysSetDomain(DWORD dDomain);
void  SysInvalidateICache(void);
void  SysInvalidateICacheMVA(DWORD dMVA);
void  SysPrefetchICacheMVA(DWORD dMVA);
void  SysInvalidateDCache(void);
void  SysInvalidateDCacheMVA(DWORD dMVA);
void  SysInvalidateIDCache(void);
void  SysCleanDCacheMVA(DWORD dMVA);
void  SysCleanInvalidateDCacheMVA(DWORD dMVA);
void  SysCleanDCacheIndex(DWORD nIndex);
void  SysCleanInvalidateDCacheIndex(DWORD nIndex);
void  SysCleanAllDCache926(void);
void  SysCleanFlushAllDCache926(void);
void  SysWaitForInterrupt(void);
void  SysInvalidateTLB(void);
void  SysInvalidateITLB(void);
void  SysInvalidateITLBMVA(DWORD dMVA);
void  SysInvalidateDTLB(void);
void  SysInvalidateDTLBMVA(DWORD dMVA);
void  SysSetDCacheLockdownBase(DWORD dBase);
void  SysSetICacheLockdownBase(DWORD dBase);
void  SysSetDTLBLockdown(DWORD dBase);
void  SysSetITLBLockdown(DWORD dBase);
void  SysSetProcessID(DWORD dID);
void  SysSetResetVectorLow(void);
void  SysSetResetVectorHigh(void);
void  SysSetNormalVectorBaseAddr(DWORD dBaseAddr);
void  SysSetMonitorVectorBaseAddr(DWORD dBaseAddr);
DWORD SysGetInterruptStatusRegister(void);
void  SysSetPeriPortReMapRegister(DWORD dBaseAddr);
void  SysEnableIRQ(void);
void  SysDisableIRQ(void);
void  SysEnableFIQ(void);
void  SysDisableFIQ(void);
long  SysGetDataFaultStatusRegister(void);
long  SysGetInstFaultStatusRegister(void);
long  SysGetFaultAddressRegister(void);
//========================================================================
DWORD SysSaveStatusRegInCPU(void);
void  SysRestStatusRegInCPU(DWORD dStatusReg);
//========================================================================

//========================================================================
typedef struct _MEMLINK {
     struct _MEMLINK *pNext;
     struct _MEMLINK *pPrev;
     DWORD  dSize;
     HWORD  wFixed;
     HWORD  wUsed;
    }MEMLINK;
typedef struct {
     MEMLINK *pMemLink;
     void    *pStart;
     DWORD    dSize;
    }MEMBLOCK;
//========================================================================
#define MEMORY_ALIGN   16
#define MEMLINK_SIZE   ((sizeof(MEMLINK) % MEMORY_ALIGN == 0) ? \
                        sizeof(MEMLINK) : \
                        ((sizeof(MEMLINK) / MEMORY_ALIGN + 1) * 16))
//========================================================================
//#if defined(__NAVIONICS__)
#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
typedef struct _FIXEDMEM {
     int    nBlockSize;
     int    nFullCount;                 // �A�� ����
     int    nFreeCount;                 // �q�e ����
     int    nLastUsedNo;                // �a���b�A �i�w�E slot ��ѡ
     int    nLastFreeNo;                // �a���b�A Ё�A�E slot ��ѡ
     UCHAR *pUsedState;                 // 0=���a�w,1=�a�w
     UCHAR *pFreeMemory;
    }FIXEDMEM;
#endif
//========================================================================

//========================================================================
void  SysInitFreeMemory(void);
#if  defined(__NAVIONICS__)
void  SysRunCTORS(void);
#endif
DWORD SysGetAllFreeMemorySize(void);
DWORD SysGetMaxFreeMemorySize(void);
int   SysMakeMemoryTree(DWORD *pMemTree);
int   SysCheckMemoryTree(DWORD *pErrorMem,MEMLINK *pMemLink);
int   SysGetNumberOfFragment(void);
DWORD SysGetLastFreeAddress(DWORD *pBlockSize);
int   SysRemoveMemFragment(void);
//========================================================================
//#if  defined(__NAVIONICS__)
#if defined(__SAMYUNG__) && !defined(_ICON_GME_)
DWORD SysGetFixedStartAddrX(void);
DWORD SysGetFixedMemorySize(void);
int   SysGetFixedMemoryEnable(void);
void  SysSetFixedMemoryEnable(int nMode);
void  *MallocFixedMemory(FIXEDMEM *pFixedMemory,int nSize);
int    FreeFixedMemory(FIXEDMEM *pFixedMemory,void *pMemAddr);
#endif
void  *__wrap_malloc(DWORD nSize);
void  __wrap_free(void *pMemAddr);
void  *__wrap_calloc(DWORD nItems,DWORD dSize);
void  __wrap_abort(void);
int   __wrap_open(char *Path,int Flags);
int   __wrap_close(int file);
int   __wrap_fstat(int file,struct stat *st);
int   __wrap_isatty(int file);
int   __wrap_lseek(int file,int ptr,int dir);
int   __wrap_read(int file,char *ptr,int len);
caddr_t __wrap_sbrk(int incr);
int   __wrap_write(int file,char *ptr,int len);
int   __wrap_fputc(int character,void *stream);
int   __wrap_fputs(const char *str,void *stream);
int   __wrap_puts(const char *);
int   __wrap_printf(const char *format,...);
//========================================================================
int   __wrap__open_r(void *REENT,const char *file,int flags,int mode);
int   __wrap__close_r(void *REENT,int FD);
int   __wrap__fstat_r(void *REENT,int FD,struct stat *PSTAT);
off_t __wrap__lseek_r(void *REENT,int FD,off_t POS,int WHENCE);
long  __wrap__read_r(void *REENT,int FD,void *BUF,size_t CNT);
char *__wrap__sbrk_r(void *REENT, size_t INCR);
long  __wrap__write_r(void *REENT,int FD, const void *BUF, size_t CNT);
//========================================================================
void  SysCheckCpuType(void);
void  SysSetCpuType(int nCpuType);
int   SysGetCpuType(void);
void  SysCheckDeviceType(void);
void  SysSetDeviceType(int nDeviceType);
int   SysGetDeviceType(void);
void  SysCheckNavisModelType(void);
void  SysSetNavisModelType(int nNavisType);
int   SysGetNavisModelType(void);
void  SysCheckSGP330ScrnMode(void);
void  SysSetSGP330ScrnMode(int nScrnMode);
int   SysGetSGP330ScrnMode(void);
void  SysSetNavis5100AModelMode(int nMode);
int   SysGetNavis5100AModelMode(void);
int   SysIsLedBackLight5100(void);
int   SysCheckLCD3800Mitsubishi(void);
void  SysCheckFpgaType(void);
void  SysSetFpgaType(int nFpgaType);
int   SysGetFpgaType(void);
int   SysIs240x320Device(void);
int   SysIs480x640Device(void);
int   SysIs800x600Device(void);
int   SysIs800x600Greater(void);
int   SysIs640x480Device(void);
int   SysIs1024x768Device(void);
int   SysIs800x480Device(void);
int   SysIsLandscapeMode(void);
int   SysIsLargeNANDmode(void);
void  SysSetSonarMode(int nMode);
int   SysGetSonarMode(void);
void  SysSetRadarMode(int nMode);
int   SysGetRadarMode(void);
void  SysSetVideoMode(int nMode);
int   SysGetVideoMode(void);
int   SysCanSetExtSonarData(void);
int   SysCanViewRadarMenu(void);
int   SysCanViewVideoMenu(void);
int   SysCanHaveSonar(void);
int   SysCanHaveRadar(void);
int   SysCanHaveVideo(void);
void  SysSetCanHaveVideoMode(int nMode);
int   SysCanHaveInternalGPS(void);
int   SysCanHaveNMEA2000(void);

#if !defined(__SONAR__)
int   SysCanHaveDGPS(void);
#endif

DWORD SysGetChartScreenAddress(void);
DWORD SysGetMenuScreenAddress(void);
DWORD SysGetRadarScreenAddress(void);
DWORD SysGetVideoScreenAddress(void);
DWORD SysGetScreenAddress(void);
DWORD SysGetScreenWidth(void);
DWORD SysGetScreenHeight(void);
DWORD SysGetScreenWidthByDevice(int nDeviceType);
DWORD SysGetScreenHeightByDevice(int nDeviceType);
DWORD SysGetVirtScreenWidth(void);
DWORD SysGetVirtScreenHeight(void);
void  SysGetVirtHoriCenter(int nHoriSize,SHORT *pLeft,SHORT *pRight);
void  SysGetVirtVertCenter(int nVertSize,SHORT *pTop,SHORT *pBottom);
void  SysGetVirtAllCenter(int nHoriSize,int nVertSize,SHORT *pLeft,SHORT *pTop,SHORT *pRight,SHORT *pBottom);
int   SysGetChartLayerNo(void);
int   SysGetMenuLayerNo(void);
void  SysRunChartLayerDirtyFlag(void);
void  SysRunMenuLayerDirtyFlag(void);
//========================================================================
void  SysDelayLoop(volatile DWORD dDelayCnt);
void  SysDelayMicroSec(DWORD dDelayMicro);
void  SysDelayMiliSec(DWORD dDelayMili);
DWORD SysGetSystemTimer(void);
DWORD SysIncSystemTimer(void);
DWORD SysCalcTickToMili(DWORD dTick);
DWORD SysCalcMiliToTick(DWORD dMili);
DWORD SysCalcTickToScnd(DWORD dTick);
DWORD SysGetDiffTimeTick(DWORD dTime);
DWORD SysGetDiffTimeMili(DWORD dTime);
DWORD SysGetDiffTimeScnd(DWORD dTime);
//========================================================================
void  SysSetUTCDate(int nYear,int nMonth,int nDay);
void  SysGetUTCDate(int *pYear,int *pMonth,int *pDay);
void  SysSetUTCTime(int nHour,int nMinute,int nSecond);
void  SysGetUTCTime(int *pHour,int *pMinute,int *pSecond);
void  SysSetLOCDate(int nYear,int nMonth,int nDay);
void  SysGetLOCDate(int *pYear,int *pMonth,int *pDay);
void  SysSetLOCTime(int nHour,int nMinute,int nSecond);
void  SysGetLOCTime(int *pHour,int *pMinute,int *pSecond);
//========================================================================
void  SetTransponderReset(void);
void Au1100DelayMiliSec(DWORD DelayMili);

//========================================================================
void  SysClearPwrPinMask(void);
void  SysRunSystemPowerOff(void);
void  SysSetPowerMask(int nMask);
int   SysGetPowerask(void);
int   SysCheckSaveStart(DWORD dStartTick);
//========================================================================
void  SysSetAlphaBlendPrcnt(int nPercent);
int   SysGetAlphaBlendPrcnt(void);
void  SysSetAlphaBlendValue(UCHAR bAlpha);
UCHAR SysGetAlphaBlendValue(void);
void  SysSetOriginAlphaBlendValue(void);
void  SysSetBrightValue(int nPercent);
int   SysGetBrightValue(void);
void  SysSetPaletteData(int nSize,DWORD *pColorTable);
void  SysSetMapLayerPalData(UCHAR *pPalData);
void  SysGetMapLayerPalData(HWORD wColor,UCHAR *pR,UCHAR *pG,UCHAR *pB);
void  SysSetRadarNormPalData(UCHAR *pPalData);
void  SysSetRadarOverPalData(UCHAR *pPalData);
void  SysSetRadarOverTransparencyValue(int nValue);
HWORD SysGetMapLayerOriginColor(HWORD wColor);
//========================================================================
void  SysSetGlobalUsingMap(int nUsingMap);
int   SysGetGlobalUsingMap(void);
int   SysIsUsingMapCMAP(void);
int   SysIsUsingMapSAMYUNG(void);
void  SysSetSamMapTypeMode(int nMapType);
int   SysGetSamMapTypeMode(void);
int   SysIsSamMapTypeNewPEC(void);
void  SysSetSamMapNewPecFound(int nFound);
int   SysGetSamMapNewPecFound(void);
void  SysSetSamMapKoreaZone(int nKoreaZone);
int   SysGetSamMapKoreaZone(void);
//========================================================================
void  SysSetGlobalLangCode(int nLangCode);
int   SysGetGlobalLangCode(void);
//========================================================================
void  SysSetGlobalNightMode(int nMode);
int   SysGetGlobalNightMode(void);
void  SysSetGlobalChartBodyMode(int nMode);
int   SysGetGlobalChartBodyMode(void);
void  SysSetGlobalMenuBackColor(int nColor);
int   SysGetGlobalMenuBackColor(void);
//========================================================================
int   SysGetUsbDiskMountedStatus(void);
void  SysAppendDriveName(char *pFileName);
//========================================================================
void  SysSetSdCardLastChangeStatus(int nChanged);
int   SysGetSdCardLastChangeStatus(void);
//========================================================================
void  SysSetKeyDataGetEnabledMode(int nMode);
int   SysGetKeyDataGetEnabledMode(void);
//========================================================================
void  SysSetFpgaMulFactor(int nFactor);
int   SysGetFpgaMulFactor(void);
void  SysWriteDataToFPGA(DWORD dAddr,UCHAR bData);
UCHAR SysReadDataFromFPGA(DWORD dAddr);
void  SysReadFpgaVersionData(char *pVerData);
//========================================================================
void  SysCheckSonarHwPcbVersion(void);
void  SysSetSonarHwPcbVersion(int nHwVer);
int   SysIsSonarHwPcbVersionOld(void);
int   SysIsSonarHwPcbVersionNew(void);
//========================================================================
int   SysIsMyanmareseLanguage(void);
int   SysIsArabianLanguage(void);
int   SysIsPersianLanguage(void);
int   SysIsArabianOrPersianLanguage(void);
//========================================================================
void  SetCheckPowerStatus(int nMode);
void  CheckPowerStatus(void);
void  SetPowerStatus(int nStatus);
int   GetPowerStatus(void);
int   IsLcdLedType(void);
int   SysIs640x480Mode(void);
int   SysIs800x480Mode(void);

#ifdef  __cplusplus
}
#endif

#endif

