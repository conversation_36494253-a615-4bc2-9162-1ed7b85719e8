#include "Sentence.hpp"

#ifndef __VDM_HPP__
#define __VDM_HPP__

/******************************************************************************
 * 
 * VDM - VHF Data-link Message
 * 
 * !--VDM,x,x,x,a,s--s,x*hh<CR><LF>
 *        | | | |  |   |
 *        1 2 3 4  5   6
 *
 * 1. Total number of sentences needed to transfer the message, 1 to 9
 * 2. Sentence number, 1 to 9
 * 3. Sequential message identifier, 0 to 9
 * 4. AIS Channel, "A" or "B"
 * 5. Encapsulated ITU-R M.1371 radio message
 * 6. Number of fill-bits, 0 to 5
 *
 ******************************************************************************/
class CVdm : public CSentence {
protected:
    int  m_nTotalNo;       // 1 to 9
    int  m_nSentNo;        // 1 to 9
    int  m_nSeqId;         // 0 to 9
    char m_chChannel;      // "A" or "B"
    char m_szMessage[170];
    int  m_nFillBitsNo;    // 0 to 5
    
public:
    CVdm();
    CVdm(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat()   { return m_nFormat;   }
	void GetPlainText(char *pszPlainText);

	int  GetTotalNo()  { return m_nTotalNo;  }
	int  GetSentNo()   { return m_nSentNo;   }
	int  GetSeqMsgID() { return m_nSeqId;    }
	char GetChannel()  { return m_chChannel; }
	void GetMessage(char *pszMessage) { strcpy(pszMessage, m_szMessage); }
	const char *GetMessage() { return m_szMessage; }
	int  GetFillBitsNo() { return m_nFillBitsNo; }
};

#endif

