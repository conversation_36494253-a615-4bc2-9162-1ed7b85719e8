/*...........................................................................*/
/*.                  File Name : SYSTIMER.C                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysTimer.h"

#include <string.h>

void  SysInitTimer(xSYS_TIMER *pSysTimer,int nRunMode,int nIntMode,int nWatchDogMode,DWORD dMATCH)
{
/*
      DWORD dTempX;
      DWORD dCLKDIV;
      int   nSELTCLK = TIMER0_TCLKDIV_VALUE;                // Divide by 2

      pSysTimer->dTMRCLKENB  = (1 << 3) | (1 << 2);
      dCLKDIV = TIMER0_CLKDIV_VALUE - 1;                    // Divide By 1
      pSysTimer->dTMRCLKGEN = (dCLKDIV << 4) | (0 << 1);    // 0=PLL0
      dTempX = pSysTimer->dTMRCONTROL & (~0x3);
      if (nSELTCLK == 1) dTempX = dTempX | (3 << 0);        // 3=TimerClock / 1
      if (nSELTCLK == 2) dTempX = dTempX | (0 << 0);        // 0=TimerClock / 2
      if (nSELTCLK == 4) dTempX = dTempX | (1 << 0);        // 1=TimerClock / 4
      if (nSELTCLK == 8) dTempX = dTempX | (2 << 0);        // 2=TimerClock / 8
      pSysTimer->dTMRCONTROL = dTempX;
      pSysTimer->dTMRCOUNT   = 0;
      pSysTimer->dTMRMATCH   = dMATCH / nSELTCLK;
      pSysTimer->dTMRCONTROL|=  (1 << 5);                    // Pending Clear

      if (nWatchDogMode)
          pSysTimer->dTMRCONTROL|=  (1 << 2);                // Watchdog Enable
      else
          pSysTimer->dTMRCONTROL&= ~(1 << 2);                // Normal Operation

      if (nIntMode)
          pSysTimer->dTMRCONTROL|=  (1 << 4);                // Interrupt Enable
      else
          pSysTimer->dTMRCONTROL&= ~(1 << 4);                // Interrupt Disable

      if (nRunMode)
          pSysTimer->dTMRCONTROL|=  (1 << 3);                // Timer RUN
      else
          pSysTimer->dTMRCONTROL&= ~(1 << 3);                // Timer STOP
*/

      DWORD dTempX;
      DWORD dCLKDIV;
      int   nSELTCLK = TIMER0_TCLKDIV_VALUE;                // Divide by 2

      pSysTimer->dTMRCLKENB  = (1 << 3) | (1 << 2);
      dCLKDIV = TIMER0_CLKDIV_VALUE - 1;                    // Divide By 1
      pSysTimer->dTMRCLKGEN = (dCLKDIV << 4) | (0 << 1);    // 0=PLL0
      dTempX = pSysTimer->dTMRCONTROL & (~0x3);
      if (nSELTCLK == 1) dTempX = dTempX | (3 << 0);        // 3=TimerClock / 1
      if (nSELTCLK == 2) dTempX = dTempX | (0 << 0);        // 0=TimerClock / 2
      if (nSELTCLK == 4) dTempX = dTempX | (1 << 0);        // 1=TimerClock / 4
      if (nSELTCLK == 8) dTempX = dTempX | (2 << 0);        // 2=TimerClock / 8
      pSysTimer->dTMRCONTROL = dTempX;
      pSysTimer->dTMRCOUNT   = 0;
      pSysTimer->dTMRMATCH   = dMATCH / nSELTCLK;
      pSysTimer->dTMRCONTROL|=  (1 << 5);                    // Pending Clear
      pSysTimer->dTMRCONTROL&= ~(1 << 2);                    // Normal Operation
      pSysTimer->dTMRCONTROL|=  (1 << 4);                    // Interrupt Enable
      pSysTimer->dTMRCONTROL|=  (1 << 3);                    // Timer RUN
}
void  SysTimerDelayMicroSec(DWORD dDelayMicro)
{
      DWORD dTempX;
      DWORD dTempY;
      DWORD dTempZ;
      DWORD dTempT;

      if (dDelayMicro > 1000000)
          dDelayMicro = 1000000;
      dTempX = CPU_PLL0_FREQUENCY / 1000000 * (DWORD)dDelayMicro;
      dTempY = SysGetTimerCounter((xSYS_TIMER *)TIMER0_PHSY_BASE_ADDR);
      dTempT = 0;
      while (1)
            {
             dTempZ = SysGetTimerCounter((xSYS_TIMER *)TIMER0_PHSY_BASE_ADDR);
             if (dTempZ >= dTempY)
                 dTempT += (dTempZ - dTempY);
             else
                 dTempT += ((TIMER0_MATCH_COUNTER / TIMER0_TCLKDIV_VALUE) - dTempY + dTempZ);
             if (dTempT >= dTempX)
                 break;
             dTempY = dTempZ;
            }
}
void  SysTimerDelayMiliSec(DWORD dDelayMili)
{
      DWORD dTempX;
      DWORD dTempY;
      DWORD dTempZ;
      DWORD dTempT;

      if (dDelayMili > 5000)
          dDelayMili = 5000;
      dTempX = CPU_PLL0_FREQUENCY / 1000 * (DWORD)dDelayMili;
      dTempY = SysGetTimerCounter((xSYS_TIMER *)TIMER0_PHSY_BASE_ADDR);
      dTempT = 0;
      while (1)
            {
             dTempZ = SysGetTimerCounter((xSYS_TIMER *)TIMER0_PHSY_BASE_ADDR);
             if (dTempZ >= dTempY)
                 dTempT += (dTempZ - dTempY);
             else
                 dTempT += ((TIMER0_MATCH_COUNTER / TIMER0_TCLKDIV_VALUE) - dTempY + dTempZ);
             if (dTempT >= dTempX)
                 break;
             dTempY = dTempZ;
            }
}
DWORD SysGetTimerCounter(xSYS_TIMER *pSysTimer)
{
      DWORD dTimerCounter;

      pSysTimer->dTMRCONTROL |=  (1 <<  6);                  // LDCNT = 1
      dTimerCounter = pSysTimer->dTMRCOUNT;
      pSysTimer->dTMRCONTROL &= ~(1 <<  6);                  // LDCNT = 0
      return(dTimerCounter);
}
void  SysClearTimerIntPending(xSYS_TIMER *pSysTimer)
{
      pSysTimer->dTMRCONTROL |= (1 << 5);   // Pending Clear
}



