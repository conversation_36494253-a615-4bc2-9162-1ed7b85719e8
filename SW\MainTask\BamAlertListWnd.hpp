#include "Wnd.hpp"
#include "BamAlert.hpp"

#ifndef __BAMALERT_LIST_WND_HPP__
#define __BAMALERT_LIST_WND_HPP__

class CBamAlertListWnd : public CWnd {
	protected:
		int  m_nCurSel;
		int  m_nStartViewPos;
		int  m_nCurAlarmID;
		int  m_nCurAlarmInstance;
		int  m_nCurAlarmStatus;
		BOOL m_bAckable;

	public:
		CBamAlertListWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		void OnKeyEvent(int nKey, DWORD nFlags);
		
		void DrawFuncBtn();	
		void DrawAlarmListScrollBar();
		void DrawBamAlertList();
		void DrawWnd(BOOL bRedraw = 1);

		int GetCurSelAlarmID();
		int GetCurSelAlarmInstance();
		int GetCurSelAlarmStatus();		
		void SetFocus(int nFocus)   { m_nFocus = nFocus;   }
		void SetCurSel(int nCurSel) { m_nCurSel = nCurSel; }
		int  GetCurSel() { return m_nCurSel; }
		void SendAck();

};
		bool CompBamAlertByPriorityFunctor(const CBamAlert &alert1, const CBamAlert &alert2);

#endif

