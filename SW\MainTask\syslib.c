/*...........................................................................*/
/*.                  File Name : SYSLIB.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.31                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "au1100.h"
#include "comlib.h"
#include "syslib.h"

#include <math.h>
#include <stdlib.h>
#include <string.h>

#if defined(__CPU_AU1100__)
//////////////////////////////////////////////////////////////////////////////
#define  TP_RESET_ENABLE_PORT_BIT_A                     (1 << (16 + 13))
#define  TP_RESET_ON_OFF_PORT_BIT_A                     (1 << ( 0 + 13))
//-----------------------------------------------------------------------------
#define  TP_RESET_ON_OFF_PORT_BIT_B                     (1 << ( 0 + 13))
//-----------------------------------------------------------------------------
#define  LCD_CHECK_IN_PORT_BIT                          (1 << 30)
#define  LCD_CHECK_OUT_PORT_BIT                         (1 << 31)
//-----------------------------------------------------------------------------
static MEMBLOCK G_AllMemBlock;
static DWORD G_dSystemTickCounter = 0;
//////////////////////////////////////////////////////////////////////////////
void  SysDelayMicroSec(DWORD dDelayMicro)
{
#if defined(__GNUC__) && defined(__CPU_AU1X00__)
	DWORD dTempX;
	DWORD dTempY;
	DWORD dTempZ;

	dTempX = SYS_CLK_FREQ / 1000000 * (DWORD)dDelayMicro;
	dTempY = Read32BitCP0Register(CP0_COUNT);
	while (1)
	{
		dTempZ = Read32BitCP0Register(CP0_COUNT);
		if (dTempZ >= dTempY)
			dTempZ -= dTempY;
		else
			dTempZ += (0xffffffff - dTempY);
		if (dTempZ >= dTempX)
			break;
	}
#endif

#if defined(__GNUC__) && defined(__CPU_S3C24X0__)
#endif
}
void  SysDelayMiliSec(DWORD dDelayMili)
{
#if defined(__GNUC__) && defined(__CPU_AU1X00__)
	DWORD dTempX;
	DWORD dTempY;
	DWORD dTempZ;

	dTempX = SYS_CLK_FREQ / 1000 * (DWORD)dDelayMili;
	dTempY = Read32BitCP0Register(CP0_COUNT);
	while (1)
	{
		dTempZ = Read32BitCP0Register(CP0_COUNT);
		if (dTempZ >= dTempY)
			dTempZ -= dTempY;
		else
			dTempZ += (0xffffffff - dTempY);
		if (dTempZ >= dTempX)
			break;
	}
#endif

#if defined(__GNUC__) && defined(__CPU_S3C24X0__)
#endif
}
void  SysIncSystemTimer(void)
{
      ++G_dSystemTickCounter;
}
DWORD SysGetSystemTimer(void)
{
	return(G_dSystemTickCounter);
}
DWORD SysCalcTickToMili(DWORD dTick)
{
	return(CALC_TICK_TO_MILI(dTick));
}
DWORD SysCalcMiliToTick(DWORD dMili)
{
	return(CALC_MILI_TO_TICK(dMili));
}
DWORD SysGetDiffTimeTick(DWORD dTime)
{
	DWORD dTemp;

	dTemp = SysGetSystemTimer();
	if (dTemp >= dTime)
		dTemp -= dTime;
	else
		dTemp += (0xffffffff - dTime);
	return(dTemp);
}
DWORD SysGetDiffTimeMili(DWORD dTime)
{
	return(CALC_TICK_TO_MILI(SysGetDiffTimeTick(dTime)));
}
void  InitFreeMemory(void)
{
	    DWORD Buffer;
	    DWORD nSize;
      MEMLINK *pBuffer;

      Buffer= SysGetStartOfFreeMemory();
      nSize = 64 * 1024 * 1024;             // 64 MB
      nSize = nSize - (Buffer - AU1100_KSEG0_BASE);
      G_AllMemBlock.pStart = (void *)Buffer;
      G_AllMemBlock.nSize  = nSize;
      if ((Buffer % MEMORY_ALIGN) != 0)
        {
         nSize -= (MEMORY_ALIGN - Buffer % MEMORY_ALIGN);
         Buffer = Buffer + (MEMORY_ALIGN - Buffer % MEMORY_ALIGN);
        }
     if ((nSize % MEMORY_ALIGN) != 0)
         nSize -= (nSize % MEMORY_ALIGN);
     pBuffer = (MEMLINK *)Buffer;
     pBuffer->pPrev = NULL;
     pBuffer->pNext = NULL;
     pBuffer->nSize = nSize - MEMLINK_SIZE;
     pBuffer->Fixed = 1;
     pBuffer->nUsed = 0;
     G_AllMemBlock.pMemLink = pBuffer;
}
void *__wrap_malloc(DWORD nSize)
{
     MEMLINK *pLink;
     MEMLINK *pTemp;

     nSize = (nSize + MEMORY_ALIGN - 1) / MEMORY_ALIGN * MEMORY_ALIGN;
     if (nSize < MEMORY_ALIGN)
         nSize = MEMORY_ALIGN;
     pLink = G_AllMemBlock.pMemLink;
     while (pLink != NULL)
           {
            if ((pLink->nSize >= nSize) && !pLink->nUsed)
               {
                if ((pLink->nSize - nSize) > MEMLINK_SIZE)
                   {
                    pTemp = (MEMLINK *)((char *)pLink + MEMLINK_SIZE + nSize);
                    pTemp->pNext = pLink->pNext;
                    pTemp->pPrev = pLink;
                    pLink->pNext = pTemp;
                    pTemp->Fixed = 0;
                    pTemp->nSize = pLink->nSize - nSize - MEMLINK_SIZE;
                    pTemp->nUsed = 0;
                    pLink->nSize = nSize;
                   }
                pLink->nUsed = 1;
                break;
               }
            pLink = pLink->pNext;
           }
     if (pLink == NULL)
         return(NULL);
     return((char *)pLink + MEMLINK_SIZE);
}
void  __wrap_free(void *pMemAddr)
{
     MEMLINK *pLink;
     MEMLINK *pTemp;
     MEMLINK *pPrev;

     pMemAddr = (char *)pMemAddr - MEMLINK_SIZE;
     pLink = G_AllMemBlock.pMemLink;
     pPrev = NULL;
     while (pLink != NULL)
           {
            if (pLink == pMemAddr)
               {
                pLink->nUsed = 0;
                pTemp = pLink->pNext;
                if ((pTemp != NULL) && !pTemp->nUsed && !pTemp->Fixed)
                   {
                    pLink->nSize += pTemp->nSize + MEMLINK_SIZE;
                    pLink->pNext = pTemp->pNext;
                   }
                if (!pLink->Fixed && (pPrev != NULL) && !pPrev->nUsed)
                   {
                    pPrev->nSize += pLink->nSize + MEMLINK_SIZE;
                    pPrev->pNext = pLink->pNext;
                   }
                break;
               }
            pPrev = pLink;
            pLink = pLink->pNext;
           }
}
void *__wrap_calloc(DWORD nItems,DWORD nSize)
{
	    void *pMem;

	    pMem = malloc(nItems * nSize);
	    memset(pMem,0x00,nItems * nSize);
	    return(pMem);
}
void  __wrap_abort(void)
{
}
int   __wrap_close(int file)
{
      return(-1);
}
int   __wrap_fstat(int file,struct stat *st)
{
      st->st_mode = S_IFCHR;
      return(0);
}
int   __wrap_isatty(int file)
{
      return(1);
}
int   __wrap_lseek(int file,int ptr,int dir)
{
      return(0);
}
int   __wrap_read(int file,char *ptr,int len)
{
      return(0);
}
caddr_t __wrap_sbrk(int incr)
{
      static char *heap_end = NULL;
      char *prev_heap_end;

      if (heap_end == NULL)
          heap_end = (char *)SysGetStartOfHeapMemory();
      prev_heap_end = heap_end;
      if ((heap_end + incr) > (char *)SysGetLastOfHeapMemory())
          abort();
      heap_end += incr;
      return((caddr_t)prev_heap_end);
}
int   __wrap_write(int file,char *ptr,int len)
{
      return(len);
}
DWORD ReadCpuRegister(volatile DWORD *pMem)
{
	    return(*pMem);
}
void  WriteCpuRegister(volatile DWORD *pMem,DWORD Data)
{
	    *pMem = Data;
      __asm__ __volatile__("sync");
}
void  SetSysPinFunc(void)
{
	    volatile DWORD *pMem;

	    pMem = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_PINFUNC);
//    *pMem= *pMem & 0xffffaff7;  // 1111 1111 1111 1111 1010 1111 1111 0111
	    *pMem= *pMem & 0xfffefff7;  // 1111 1111 1111 1110 1111 1111 1111 0111
	                                //                   0                     = EXT0
	                                //                      0                  = UART3
	                                //                        0                = UART1
	                                //                                    0    = UART0
      __asm__ __volatile__("sync");
	    *pMem= *pMem | 0x00000230;  // 0000 0000 0000 0000 0000 0010 0011 0000
	                                //                            1            = EXT0
	                                //                                 1       = I2S GPIO[31:29]
	                                //                                  1      = NI  GPIO[28:24] , GPIO[215]
      __asm__ __volatile__("sync");
}
void  SetFreqControl(int nFreq,DWORD dFrqDiv,int nFE,int nFS)
{
	    volatile DWORD *pMem;
	    DWORD dTemp;
	    DWORD dMask;

      dTemp = dFrqDiv << 2;
      if (nFE)
          dTemp |= 0x00000002;
      if (nFS)
          dTemp |= 0x00000001;
      if (nFreq > 2)  // FREQ3 -- FREQ5
         {
          dMask = 0x000003ff << ((nFreq - 3) * 10);
          dTemp = dTemp << ((nFreq - 3) * 10);
          pMem = (volatile DWORD *)(AU1100_CLOCK_CONTROLLER + SYS_FREQCTRL1);
         }
      else            // FREQ0 -- FREQ2
         {
          dMask = 0x000003ff << ((nFreq - 0) * 10);
          dTemp = dTemp << ((nFreq - 0) * 10);
          pMem = (volatile DWORD *)(AU1100_CLOCK_CONTROLLER + SYS_FREQCTRL0);
         }
      dMask = dMask ^ 0xffffffff;
      *pMem = *pMem & dMask;
      __asm__ __volatile__("sync");
      *pMem = *pMem | dTemp;
      __asm__ __volatile__("sync");
}
void  SetExtClkControl(int nExtClk,int nUseFreq,int nME,int nDE,int nCE,int nCpuAux)
{
      // nExtClk   : 0 -- 1      ;0=EXTCLK0
      // nUseFreq  : 0 -- 255    ;FRDIV
      // nME       : 1 -- 7      ;1=AUX,2=FREQ0,3=FREQ1,...,7=FREQ5
	    volatile DWORD *pMem;
	    DWORD dTemp;
	    DWORD dMask;

      SetFreqControl(nME - 2,nUseFreq,1,nCpuAux);
      dTemp = nME << 2;
      if (nDE)
          dTemp |= 0x00000002;
      if (nCE)
          dTemp |= 0x00000001;
      if (nExtClk == 0)
         {
          dMask = 0xfe0fffff;
          dTemp = dTemp << 20;
         }
      else
         {
          dMask = 0x01ffffff;
          dTemp = dTemp << 25;
         }
      pMem = (volatile DWORD *)(AU1100_CLOCK_CONTROLLER + SYS_CLKSRC);
      *pMem = *pMem & dMask;
      __asm__ __volatile__("sync");
      *pMem = *pMem | dTemp;
      __asm__ __volatile__("sync");
}
void  SetAuxPll(DWORD dPllVal)
{
	    volatile DWORD *pMem;

	    pMem = (volatile DWORD *)(AU1100_CLOCK_CONTROLLER + SYS_AUXPLL);
	    *pMem= dPllVal;
      Au1100DelayMiliSec(50);
}
void  SetGpioMode(void)
{
	    volatile DWORD *pMem;

	    pMem = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_PININPUTEN);
	    *pMem= 0x00000000;          // GPIO All input
	    pMem = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTCLR);
	    *pMem= TP_RESET_ON_OFF_PORT_BIT_B;      // B-Board Transponder reset = 0
	    pMem = (volatile DWORD *)(AU1100_GPIO2_BASE + GPIO2_ENABLE);
	    *pMem= 0x00000003;          // 0000 0000 0000 0000 0000 0000 0000 0011
	                                //                                       1 = CE (Clock Enable)
	                                //                                      1  = MR (Module Reset)
      Au1100DelayMiliSec(10);
	    *pMem= 0x00000001;          // 0000 0000 0000 0000 0000 0000 0000 0001
	                                //                                       1 = CE (Clock Enable)
      Au1100DelayMiliSec(10);
	    pMem = (volatile DWORD *)(AU1100_GPIO2_BASE + GPIO2_OUTPUT);
	    *pMem= *pMem | 0x80000000;  // 1000 0000 0000 0000 0000 0000 0000 0000
	                                // 1                                       = 215 output enable
	                                //                     0                   = 215 output 0
	    *pMem= *pMem | 0x20000000;  // 0010 0000 0000 0000 0000 0000 0000 0000
	                                //   1                                     = 213 output enable
	                                //                       0                 = 213 output 0
	    pMem = (volatile DWORD *)(AU1100_GPIO2_BASE + GPIO2_DIR);
	    *pMem= *pMem | 0x00008000;  // 0000 0000 0000 0000 1000 0000 0000 0000
	                                //                     1                   = 215 output
	    *pMem= *pMem | 0x00002000;  // 0000 0000 0000 0000 0010 0000 0000 0000
	                                //                       1                 = 213 output
}
void  SetTransponderReset(void)
{
	    volatile DWORD *pMem;

	    pMem = (volatile DWORD *)(AU1100_GPIO2_BASE + GPIO2_OUTPUT);
 	    *pMem = *pMem | TP_RESET_ENABLE_PORT_BIT_A | TP_RESET_ON_OFF_PORT_BIT_A;
	    pMem = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTSET);
	    *pMem= TP_RESET_ON_OFF_PORT_BIT_B;      // B-Board Transponder reset = 0
 	    Au1100DelayMiliSec(1000);
		  pMem = (volatile DWORD *)(AU1100_GPIO2_BASE + GPIO2_OUTPUT);
 	    *pMem = (*pMem | TP_RESET_ENABLE_PORT_BIT_A) & (~TP_RESET_ON_OFF_PORT_BIT_A);
	    pMem = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTCLR);
	    *pMem= TP_RESET_ON_OFF_PORT_BIT_B;      // B-Board Transponder reset = 0
}
void  SetSysUartPara(DWORD BaseAddr,int Speed,BYTE Parity,int DataBit,int StopBit,int TxInt,int RxInt)
{
	    DWORD Temp;
	    volatile DWORD *pMem;

      WriteCpuRegister((volatile DWORD *)(BaseAddr + UART_ENABLE),UART_ENABLE_CE);
                                          // ClockEnable
      LoopDelay(10);
      WriteCpuRegister((volatile DWORD *)(BaseAddr + UART_ENABLE),UART_ENABLE_CE | UART_ENABLE_E);
                                          // ClockEnable & Enable
      WriteCpuRegister((volatile DWORD *)(BaseAddr + UART_FIFOCTRL),0x000000ff);
                                          // 00000000000000000000000011111111
                                          //                                1 = FIFO Enable
                                          //                               1  = Receiver Reset
                                          //                              1   = Tranmtr  Reset
                                          //                             1    = FIFO Mode
                                          //                           11     = TFT=12
                                          //                         11       = RFT=14
      Temp = DataBit - 5;
      if (StopBit != 1)
          Temp |= 0x00000004;
      if (Parity == 'O' || Parity == 'E')
         {
         	Temp |= 0x00000008;
         	if (Parity == 'O') Temp |= 0x00000000;
         	if (Parity == 'E') Temp |= 0x00000010;
         }
      WriteCpuRegister((volatile DWORD *)(BaseAddr + UART_LINECTRL),Temp);
      Temp = SYS_CLK_FREQ / 64 / Speed;   // BaudRate = CPU/(SD*2*ClkDiv*16)
      WriteCpuRegister((volatile DWORD *)(BaseAddr + UART_CLKDIV),Temp);
                                          // BaudRate
      Temp = 0x00000000;
      if (TxInt) Temp |= UART_INTEN_TIE;
      if (RxInt) Temp |= UART_INTEN_RIE;
      WriteCpuRegister((DWORD *)(BaseAddr + UART_INTEN),Temp);
}
void  Au1100DelayMiliSec(DWORD DelayMili)
{
	    DWORD TempX;
	    DWORD TempY;
	    DWORD TempZ;

	    TempX = SYS_CLK_FREQ / 1000 * DelayMili;
	    TempY = Read32BitCP0Register(CP0_COUNT);
	    while (1)
	          {
   	         TempZ = Read32BitCP0Register(CP0_COUNT);
   	         if (TempZ >= TempY)
   	             TempZ -= TempY;
	           else
	               TempZ += (0xffffffff - TempY);
	           if (TempZ >= TempX)
	               break;
	          }
}
int   GetMKDLcdType(void)
{
	    volatile DWORD *pMemClear  = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTCLR);
	    volatile DWORD *pMemSelect = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTSET);
	    volatile DWORD *pMemReturn = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_PINSTATERD);
      int  i,nLcdType;

//    nLcdType = LCD_TYPE_MONO;
      nLcdType = 0;
      *pMemSelect = LCD_CHECK_OUT_PORT_BIT;
      LoopDelay(1000);
      for (i = 0;i < 100000;i++)
           if ((*pMemReturn & LCD_CHECK_IN_PORT_BIT) != LCD_CHECK_IN_PORT_BIT)
               return(nLcdType);
      *pMemClear = LCD_CHECK_OUT_PORT_BIT;
      LoopDelay(1000);
      for (i = 0;i < 100000;i++)
           if ((*pMemReturn & LCD_CHECK_IN_PORT_BIT) != 0)
               return(nLcdType);
      *pMemSelect = LCD_CHECK_OUT_PORT_BIT;
      LoopDelay(1000);
      for (i = 0;i < 100000;i++)
           if ((*pMemReturn & LCD_CHECK_IN_PORT_BIT) != LCD_CHECK_IN_PORT_BIT)
               return(nLcdType);
//    nLcdType = LCD_TYPE_COLOR;
      nLcdType = 1;
      return(nLcdType);
}
void  SetLcdControlBitOnOff(int nOnOff)
{
      #define  LCD_ENABLE_PORT_BIT                          (1 << 31)
      #define  LCD_ON_OFF_PORT_BIT                          (1 << 15)
	    volatile DWORD *pMem;

	    pMem = (volatile DWORD *)(AU1100_GPIO2_BASE + GPIO2_OUTPUT);
      if (nOnOff)
         {
    	    *pMem = *pMem | (LCD_ENABLE_PORT_BIT | LCD_ON_OFF_PORT_BIT);
	        pMem  = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTSET);
	        *pMem = LCD_ON_OFF_PORT_BIT;
         }
      else
         {
    	    *pMem = (*pMem | LCD_ON_OFF_PORT_BIT) & (~LCD_ENABLE_PORT_BIT);
	        pMem  = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTCLR);
	        *pMem = LCD_ON_OFF_PORT_BIT;
         }
      __asm__ __volatile__("sync");
}
//==============================================================================
static int  G_nPowerStaus = 1;                          // o=off,1=on
static int  G_nCanCheckPowerStatus = 0;
//-----------------------------------------------------------------------------
#define  PWR_KEY_IN_PORT_BIT                (1 <<  9)
#define  PWR_LCD_OUT_PORT_BIT               (1 << 10)
#define  TX_LED_PORT_BIT                    (1 <<  0)
#define  RX_LED_PORT_BIT                    (1 <<  1)
#define  PWR_LED_PORT_BIT                   (1 <<  4)
#define  BUZZER_PORT_BIT                    (1 << 29)
//-----------------------------------------------------------------------------
#define  AU1100_LCD_ADDR                  0xB5000000
#define  LCD_PWMDIV                           0x0024
#define  LCD_PWMHI                            0x0028
//==============================================================================
void  SetCheckPowerStatus(int nMode)
{
      G_nCanCheckPowerStatus = nMode;
}
void  CheckPowerStatus(void)
{
	    volatile DWORD *pMemReturn = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_PINSTATERD);
	    static DWORD dSysTimer = 0;
	    static DWORD dPwrTimer = 0;
      static int   nPwrChange= 0;

      dSysTimer++;
      if (G_nCanCheckPowerStatus && !(*pMemReturn & PWR_KEY_IN_PORT_BIT))
         {
          if (dPwrTimer == 0)
             {
              dPwrTimer = dSysTimer;
             }
          else
             {
              if (nPwrChange == 0)
                 {
                  if (dPwrTimer > dSysTimer)
                      dPwrTimer = dSysTimer;
                  if ((dSysTimer - dPwrTimer) > 100)     // 1 sec
                     {
                      G_nPowerStaus = 1 - G_nPowerStaus;
                      SetPowerStatus(G_nPowerStaus);
                      nPwrChange = 1;
                     }
                 }
             }
         }
      else
         {
          dPwrTimer = 0;
          nPwrChange= 0;
         }
}
void  SetPowerStatus(int nStatus)
{
      static DWORD dPwmDiv = 0x00000000;
      static DWORD dPwmHi  = 0xffffffff;
	    volatile DWORD *pMemClear  = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTCLR);
	    volatile DWORD *pMemSelect = (volatile DWORD *)(AU1100_POWER_MANAGEMENT + SYS_OUTPUTSET);
	    volatile DWORD *pLcdPwmDiv = (volatile DWORD *)(AU1100_LCD_ADDR + LCD_PWMDIV);
	    volatile DWORD *pLcdPwmHi  = (volatile DWORD *)(AU1100_LCD_ADDR + LCD_PWMHI);

      G_nPowerStaus = nStatus;
      if (G_nPowerStaus)
         {
          if (dPwmHi == 0xffffffff) dPwmHi = *pLcdPwmHi;

          *pLcdPwmHi  = dPwmHi;
          __asm__ __volatile__("sync");

          *pMemSelect = PWR_LCD_OUT_PORT_BIT;
          __asm__ __volatile__("sync");

          *pMemSelect = PWR_LED_PORT_BIT;
          __asm__ __volatile__("sync");
         }
      else
         {
          dPwmDiv = *pLcdPwmDiv & ~(1 << 12);

          dPwmHi  = *pLcdPwmHi;
          *pLcdPwmHi  = dPwmDiv << 12;
          __asm__ __volatile__("sync");

          *pMemClear  = PWR_LCD_OUT_PORT_BIT;
          __asm__ __volatile__("sync");

          *pMemClear  = PWR_LED_PORT_BIT | TX_LED_PORT_BIT | RX_LED_PORT_BIT;
          __asm__ __volatile__("sync");
         }
}
int   GetPowerStatus(void)
{
      return(G_nPowerStaus);
}
int   IsLcdLedType(void)
{
      BYTE *pMemX;

      pMemX = (BYTE *)0xbfc00000;
      if (pMemX[0x50] == 'L' && pMemX[0x51] == 'E' && pMemX[0x52] == 'D')
          return(1);
      return(0);
}
//==========// AU1100==========================================================
#endif      // AU1100

