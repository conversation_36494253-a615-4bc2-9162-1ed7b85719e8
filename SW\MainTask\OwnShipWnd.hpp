#ifndef __OWN_SHIP_H__
#define __OWN_SHIP_H__
#include "Wnd.hpp"
#include "const.h"
#include "Ship.hpp"

class COwnShipWnd : public CWnd {
	private:
		int m_nCurPageNo;

	public:
		enum {
			PAGE_STATIC = 0,
			PAGE_VOYAGE,
			PAGE_POSITION,
			PAGE_ANT,
			MAX_PAGE,
		};

	private:
		void DrawFuncBtn(int nLangMode);
		void DrawCurPageIndi(int nLangMode);

		// Static Data Page
		void DrawMMSI(CShip *pShip, int nLangMode);
		void DrawStationType(CShip *pShip, int nLangMode);
		void DrawTargetShipName(CShip *pShip, int nLangMode);
		void DrawIMO(CShip *pShip, int nLangMode);
		void DrawCallSign(CShip *pShip, int nLangMode);
		void DrawHasDTE(CShip *pShip, int nLangMode);
		void DrawEPFS(CShip *pShip, int nLangMode);
		void DrawExtName(CShip *pShip, int nLangMode);
		void DrawStaticDataPage(CShip *pShip, int nLangMode);

		// Voyage Data Page
		void DrawShipCargoType(CShip *pShip, int nLangMode);
		void DrawDraught(CShip *pShip, int nLangMode);
		void DrawDestination(CShip *pShip, int nLangMode);
		void DrawETA(CShip *pShip, int nLangMode);
		void DrawPersons(CShip *pShip, int nLangMode);
		void DrawNavStatus(CShip *pShip, int nLangMode);
		void DrawVoyageDataPage(CShip *pShip, int nLangMode);

		// Position Data Page
		void DrawLat(CShip *pShip, int nLangMode);
		void DrawLon(CShip *pShip, int nLangMode);
		void DrawPosQuality(CShip *pShip, int nLangMode);
		void DrawTimeStamp(CShip *pShip, int nLangMode);
		void DrawPA(CShip *pShip, int nLangMode);
		void DrawRAIM(CShip *pShip, int nLangMode);
		void DrawCOG(CShip *pShip, int nLangMode);
		void DrawSOG(CShip *pShip, int nLangMode);
		void DrawHDG(CShip *pShip, int nLangMode);
		void DrawROT(CShip *pShip, int nLangMode);
		void DrawAltitude(CShip *pShip, int nLangMode);
		void DrawOffPosFlag(CShip *pShip, int nLangMode);
		void DrawPosDataPage(CShip *pShip, int nLangMode);

#ifdef EN_61993_ED3
		void DrawExtAntPosData(CShip *pShip, int nLangMode);
		void DrawGuideLine(POINT ptS, POINT ptE,HWORD *pUnicode, int nStrW, int nFontH, COLORT clrLine, COLORT clrTxt, COLORT clrTxtBack);
		void DrawExtShipShape(RECT rect,BOOL bRedraw, COLORT clrLine, COLORT clrTxt, COLORT clrBack);
#endif	// End of (EN_61993_ED3)		
		void DrawShipShape(int nLangMode);
		void DrawAntPosData(CShip *pShip, int nLangMode);
		void DrawAntPosDataPage(CShip *pShip, int nLangMode);
		
	public:
		COwnShipWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
		~COwnShipWnd();

		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		void OnKeyEvent(int nKey, DWORD nFlags);
		void SetPage(int nPage)   { m_nCurPageNo = nPage; }
		void SetFocus(int nFocus) { m_nFocus = nFocus; }
		int  GetPage()            { return m_nCurPageNo;  }
		void GetStationTypeString(int nType, BYTE *pStr);
};

#endif
