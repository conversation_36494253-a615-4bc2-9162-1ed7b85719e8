#ifndef _TIDELIB_NVTYPES_H_
#define _TIDELIB_NVTYPES_H_
/* $Id: nvtypes.h,v 1.4 2004/10/04 13:57:50 flaterco Exp $ */

/*****************************************************************************\

                            DISTRIBUTION STATEMENT

    This source file is unclassified, distribution unlimited, public
    domain.  It is distributed in the hope that it will be useful, but
    WITHOUT ANY WARRANTY; without even the implied warranty of
    <PERSON><PERSON><PERSON><PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.

\*****************************************************************************/

/* nvtypes.h, NAVO Standard Data Type Definitions */

/* Supposedly: */
/*   The following definitions should be made at the command line:
     NVHP-UX, NVIRIX, NVCLIX, NVLinux, NVSUNOS, NVWINNT, NVWIN3X */
/* However, the only ones referenced in libtcd are NVWIN3X and WINNT
   (see below). */

#ifndef __NVDEFS__
#define __NVDEFS__

#include "DataType.h"

#ifdef  __cplusplus
extern "C" {
#endif

//------------------------------------------
// ISKANG, to avoid compile error, temporary
//------------------------------------------
//typedef unsigned char	BYTE;
//typedef unsigned short  WORD;
//typedef unsigned long   DWORD;
//typedef unsigned int	UINT;
//typedef long LONG;
#if 0	// 20180102 SMAP+
typedef int		 		BOOL;
#endif
typedef	void*			HWND;
#if 0	// 20180102 SMAP+
typedef WORD			WCHAR;
#endif
typedef void*			HANDLE;
typedef HANDLE HINSTANCE;
typedef HANDLE HPEN;
typedef HANDLE HICON;
typedef HANDLE HDC;
typedef HANDLE HFONT;
typedef HICON HCURSOR;

#if 0	// 20180102 SMAP+	
typedef DWORD   COLORREF;
#endif

typedef DWORD   *LPCOLORREF;

typedef struct _SYSTEMTIME {
    WORD wYear;
    WORD wMonth;
    WORD wDayOfWeek;
    WORD wDay;
    WORD wHour;
    WORD wMinute;
    WORD wSecond;
    WORD wMilliseconds;
} SYSTEMTIME, *PSYSTEMTIME, *LPSYSTEMTIME;

typedef struct _TIME_ZONE_INFORMATION {
    LONG Bias;
    WCHAR StandardName[ 32 ];
    SYSTEMTIME StandardDate;
    LONG StandardBias;
    WCHAR DaylightName[ 32 ];
    SYSTEMTIME DaylightDate;
    LONG DaylightBias;
} TIME_ZONE_INFORMATION, *PTIME_ZONE_INFORMATION, *LPTIME_ZONE_INFORMATION;

#if 0	// 20180102 SMAP+
typedef struct tagRECT
{
    LONG    left;
    LONG    top;
    LONG    right;
    LONG    bottom;
} RECT, *PRECT;
#endif

#define FALSE	0
#define TRUE	1

#define LF_FACESIZE         32

//------------------------------------------


#define NVFalse 0
#define NVTrue 1
#define NV_BOOL unsigned char
#define NV_U_CHAR unsigned char
#define NV_U_BYTE unsigned char
#define NV_CHAR char
#define NV_BYTE signed char



#if defined (NVWIN3X)
#define NV_INT16 signed short
#define NV_INT32 signed long
#define NV_U_INT16 unsigned short
#define NV_U_INT32 unsigned long
typedef signed __int64 NV_INT64;
typedef unsigned __int64 NV_U_INT64;
#else
#define NV_INT16 signed short
#define NV_INT32 signed int
#define NV_INT64 signed long long
#define NV_U_INT16 unsigned short
#define NV_U_INT32 unsigned int
#define NV_U_INT64 unsigned long long
#endif



#define NV_U_INT32_MAX  4294967295
#define NV_INT32_MAX    2147483647
#define NV_U_INT16_MAX  65535
#define NV_INT16_MAX    32767


#define NV_FLOAT32 float
#define NV_FLOAT64 double

/* DWF 2004-10-04 not sure if this is deliberate or accidental change
   from NVWINNT. */
#ifdef WINNT
#define NV_FLOAT80 long double
#endif



typedef struct
{
    NV_INT32                address;
    NV_U_INT32              record_size;
    NV_U_INT16              tzfile;
    NV_INT32                reference_station;
    NV_INT32                lat;
    NV_INT32                lon;
    NV_U_BYTE               record_type;
#ifdef WIN32_ORG
    NV_CHAR                 *name;
#else
#define	MAXLEN_STRNAME	90
	NV_CHAR                 name[MAXLEN_STRNAME];
#endif
} TIDE_INDEX;

/* These struct types aren't currently used... DWF 2004-10-04 */

    typedef struct
    {
        NV_U_INT16      r;
        NV_U_INT16      g;
        NV_U_INT16      b;
    } NV_C_RGB;



    typedef struct
    {
        NV_U_INT16      h;
        NV_U_INT16      s;
        NV_U_INT16      v;
    } NV_C_HSV;



    typedef struct
    {
        NV_FLOAT64      x;
        NV_FLOAT64      y;
    } NV_F64_COORD2;



    typedef struct
    {
        NV_FLOAT64      x;
        NV_FLOAT64      y;
        NV_FLOAT64      z;
    } NV_F64_COORD3;



    typedef struct
    {
        NV_INT32        x;
        NV_INT32        y;
    } NV_I32_COORD2;



    typedef struct
    {
        NV_INT32        x;
        NV_INT32        y;
        NV_INT32        z;
    } NV_I32_COORD3;



    typedef struct
    {
        NV_FLOAT64      lat;
        NV_FLOAT64      lon;
    } NV_F64_POS;



    typedef struct
    {
        NV_FLOAT64      lat;
        NV_FLOAT64      lon;
        NV_FLOAT64      dep;
    } NV_F64_POSDEP;



    typedef struct
    {
        NV_FLOAT64      slat;
        NV_FLOAT64      wlon;
        NV_FLOAT64      nlat;
        NV_FLOAT64      elon;
    } NV_F64_MBR;


    typedef struct
    {
        NV_FLOAT64      min_y;
        NV_FLOAT64      min_x;
        NV_FLOAT64      max_y;
        NV_FLOAT64      max_x;
    } NV_F64_XYMBR;

#ifdef  __cplusplus
}
#endif

#endif
#endif
