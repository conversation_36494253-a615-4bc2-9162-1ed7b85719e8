/*...........................................................................*/
/*.                  File Name : Tahoma10bTai.cpp                           .*/
/*.                                                                         .*/
/*.                       Date : 2009.06.02                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY WinInwa11nMiy_Font;

//...........................................................................
ROMDATA PEGUSHORT Tahoma10bAdd_Font_offset_table[257] = {
0x0000,0x000a,0x0012,0x001a,0x0022,0x002a,0x0032,0x003a,0x0042,0x004a,0x0051,0x005a,0x0062,0x006b,0x0073,0x007c,
0x0084,0x008d,0x0095,0x009e,0x00a6,0x00ad,0x00b5,0x00bc,0x00c4,0x00cb,0x00d3,0x00da,0x00e2,0x00e9,0x00f1,0x00f7,
0x00fc,0x0104,0x010c,0x0115,0x011d,0x0126,0x012e,0x0137,0x013f,0x0148,0x0150,0x0159,0x0161,0x0167,0x016d,0x0172,
0x0177,0x017f,0x0186,0x018e,0x0195,0x019d,0x01a4,0x01ab,0x01ae,0x01b5,0x01ba,0x01c1,0x01c6,0x01cd,0x01d2,0x01dd,
0x01e8,0x01f3,0x01fe,0x0209,0x0214,0x021c,0x0224,0x022c,0x0234,0x023c,0x0244,0x024c,0x0254,0x025d,0x0265,0x026e,
0x0276,0x027f,0x0287,0x0290,0x0298,0x02a0,0x02a8,0x02b0,0x02b8,0x02c1,0x02c7,0x02d0,0x02d6,0x02df,0x02e5,0x02ee,
0x02f4,0x02fc,0x0303,0x030b,0x0312,0x031a,0x0321,0x0329,0x0330,0x0338,0x033f,0x0346,0x034c,0x0353,0x0359,0x0360,
0x0366,0x036d,0x0373,0x037b,0x0383,0x038b,0x0393,0x039b,0x03a3,0x03ab,0x03b3,0x03bb,0x03c3,0x03cd,0x03d6,0x03e0,
0x03e9,0x03f7,0x0402,0x0410,0x041b,0x0429,0x0434,0x0442,0x044d,0x045b,0x0466,0x0470,0x0478,0x0482,0x048a,0x0494,
0x049d,0x04a4,0x04ab,0x04b2,0x04b9,0x04c0,0x04c7,0x04cf,0x04d5,0x04e0,0x04e9,0x04f1,0x04f5,0x0502,0x050f,0x0518,
0x0525,0x052f,0x0537,0x0541,0x0549,0x0553,0x055b,0x0565,0x056e,0x0578,0x0581,0x058b,0x0593,0x059d,0x05a5,0x05af,
0x05b7,0x05c1,0x05c9,0x05d3,0x05db,0x05e5,0x05ed,0x05f7,0x05ff,0x0606,0x060e,0x0615,0x061d,0x0624,0x062c,0x0633,
0x063b,0x0643,0x064c,0x0654,0x065d,0x0664,0x066c,0x0673,0x067b,0x0680,0x0683,0x0688,0x068b,0x0694,0x069c,0x06a5,
0x06ad,0x06b6,0x06be,0x06c7,0x06d0,0x06d9,0x06e2,0x06eb,0x06f3,0x06fc,0x0704,0x070e,0x0716,0x0720,0x0728,0x0732,
0x073a,0x0744,0x074c,0x0756,0x075e,0x0766,0x076e,0x0776,0x077e,0x0787,0x0790,0x0799,0x07a2,0x07ab,0x07b4,0x07bd,
0x07c6,0x07cf,0x07d8,0x07e2,0x07eb,0x07f5,0x07fe,0x0808,0x0811,0x081b,0x0824,0x0831,0x083e,0x084b,0x0858,0x0865,
0x0872};



ROMDATA PEGUBYTE Tahoma10bAdd_Font_data_table[4336] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x86, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x60, 0xa1, 0xb0, 0xc0, 
0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x06, 0x04, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0x85, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 
0x1f, 0x06, 0x80, 0x00, 0x00, 0x20, 0x00, 0x20, 0x00, 0x0c, 0x00, 0x03, 0x41, 0xa0, 0x00, 0x00, 
0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x03, 0xe3, 0x40, 0x00, 0x1c, 
0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x0f, 0x86, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xc3, 0x00, 0xc0, 0xc0, 0x00, 0x00, 0x00, 0x11, 0x00, 0x18, 0x30, 
0x00, 0x01, 0x80, 0xc0, 0x00, 0x00, 0xd8, 0xd8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x0c, 0x18, 
0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x00, 0x01, 0x80, 0x00, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x40, 0xc0, 0x51, 0xb0, 0x60, 
0xc0, 0x18, 0x0c, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x01, 0x80, 0x19, 0x80, 0x18, 0x00, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x83, 0x00, 0x0d, 0x83, 0x20, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x60, 0x00, 0x01, 0x98, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x1b, 
0x00, 0x03, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb0, 0x60, 0x0c, 0x08, 0x30, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x0e, 0x80, 0x0b, 0x80, 0x00, 0x71, 0x03, 
0x8e, 0x0b, 0x03, 0x80, 0x01, 0x50, 0x10, 0x54, 0x10, 0x15, 0x06, 0x05, 0x82, 0xc1, 0x10, 0x00, 
0x00, 0x00, 0x20, 0x03, 0x20, 0x07, 0x40, 0x0b, 0x80, 0x07, 0x10, 0x39, 0xc5, 0x83, 0x80, 0x04, 
0x00, 0x00, 0x00, 0x00, 0x40, 0x01, 0xd0, 0x01, 0x70, 0x00, 0x38, 0x81, 0xc7, 0x0b, 0x03, 0x80, 
0x00, 0xc0, 0x00, 0xc0, 0x00, 0x04, 0x00, 0x0c, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 
0x60, 0x00, 0x30, 0x00, 0x02, 0x00, 0x03, 0x20, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 
0x08, 0x00, 0x0c, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xf1, 0x83, 0xe1, 0x80, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x18, 0x03, 
0xe0, 0x01, 0x80, 0xc0, 0x00, 0x00, 0xd8, 0xd8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x80, 0x30, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xc0, 0x00, 0xa0, 0x00, 0xf8, 
0x60, 0x7c, 0x18, 0x30, 0x00, 0x18, 0x00, 0x18, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x03, 0x03, 0x0f, 0x33, 0x18, 0x00, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x05, 0x80, 0x0f, 0x8d, 0x84, 0xc0, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 
0x00, 0xc0, 0x00, 0x01, 0x98, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x1b, 
0x00, 0x03, 0x00, 0x03, 0x60, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb0, 0x90, 0x12, 0x04, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x1c, 0x1b, 0x00, 0x06, 0xc0, 0x00, 0xda, 0x00, 
0x9b, 0x00, 0x06, 0xc0, 0x00, 0xe0, 0x20, 0x38, 0x08, 0x0e, 0x02, 0x07, 0xc0, 0x00, 0xe0, 0x00, 
0x00, 0x00, 0x41, 0xc4, 0xc0, 0x0d, 0x80, 0x06, 0xc0, 0x0d, 0xa0, 0x0b, 0x60, 0x06, 0xc0, 0x08, 
0xe0, 0x00, 0x00, 0x00, 0x81, 0xc3, 0x60, 0x00, 0xd8, 0x00, 0x6d, 0x00, 0x4d, 0x80, 0x06, 0xc0, 
0x01, 0x80, 0x00, 0x60, 0x00, 0x08, 0x0e, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0xe0, 
0xc2, 0x00, 0x18, 0x80, 0x04, 0x27, 0x04, 0xc8, 0x00, 0x02, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x1c, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x33, 0x00, 0x30, 0x00, 0x30, 0x00, 0x03, 0x00, 0x0d, 0x80, 0x00, 0xc0, 0x00, 
0x60, 0x00, 0x30, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x00, 0x70, 
0x00, 0x00, 0x06, 0x00, 0x03, 0x00, 0x01, 0x80, 0x00, 0xc0, 0x00, 0x60, 0x00, 0x60, 0x36, 0x01, 
0x80, 0x03, 0x00, 0x06, 0x00, 0x18, 0x03, 0x00, 0x30, 0x03, 0x00, 0x00, 0x18, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xa0, 0x00, 0x68, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x80, 0x00, 0x00, 0x06, 0x00, 0x1e, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x80, 0x00, 0x00, 0x00, 0xc8, 0x00, 0x00, 0x00, 0x00, 0x30, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x19, 0x80, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 
0x0d, 0x80, 0x00, 0xc0, 0x03, 0x80, 0x00, 0x00, 0x01, 0x80, 0x00, 0x90, 0x12, 0x04, 0x3b, 0xff, 
0x1f, 0xf8, 0x00, 0x7f, 0xe0, 0x00, 0x00, 0x00, 0x04, 0x00, 0x07, 0x40, 0x02, 0xe0, 0x00, 0x39, 
0x00, 0x07, 0x00, 0x01, 0xc0, 0x00, 0x88, 0x00, 0x22, 0x00, 0x08, 0x80, 0x02, 0x20, 0x00, 0x88, 
0x00, 0x00, 0x00, 0x40, 0x03, 0x20, 0x07, 0x40, 0x0b, 0x80, 0x03, 0x90, 0x03, 0x80, 0x07, 0x00, 
0x20, 0xc0, 0x00, 0x00, 0x00, 0x40, 0x00, 0xe8, 0x00, 0xb8, 0x00, 0x1c, 0x80, 0x07, 0x00, 0x03, 
0x80, 0x00, 0x30, 0x00, 0x60, 0x00, 0x02, 0x00, 0x06, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 
0x02, 0x18, 0x00, 0xb0, 0x00, 0x21, 0x00, 0x09, 0x90, 0x02, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x00, 0x03, 0x27, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 

0x1c, 0x00, 0x3f, 0x33, 0x3f, 0x30, 0x3f, 0x30, 0x1f, 0x06, 0x7e, 0x0d, 0xbf, 0x00, 0xdf, 0x80, 
0x6f, 0xc0, 0x37, 0xe0, 0x1b, 0xf3, 0xe7, 0xe7, 0xcf, 0xc0, 0x1f, 0x80, 0x3f, 0x1c, 0x7e, 0xc7, 
0xc7, 0xcc, 0x36, 0x06, 0x1b, 0x03, 0x0d, 0x81, 0x86, 0xc0, 0xc3, 0x60, 0x3c, 0x67, 0xb7, 0x8d, 
0x83, 0x1b, 0x06, 0x36, 0x0c, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x38, 0x70, 0x30, 0xe1, 0xc1, 0x83, 
0x87, 0x00, 0x0e, 0x21, 0x8e, 0x20, 0x0e, 0x20, 0x0e, 0x20, 0x07, 0xe2, 0xc3, 0xf0, 0xb1, 0xf8, 
0xf8, 0xfc, 0x7c, 0xfc, 0x30, 0xfc, 0x18, 0xfc, 0x19, 0xf8, 0x03, 0xf1, 0xf7, 0xe0, 0x07, 0xc1, 
0x8f, 0x80, 0x1f, 0x04, 0x3e, 0x0c, 0x7c, 0x19, 0xf9, 0x8f, 0xcc, 0x7e, 0x63, 0xf3, 0x18, 0xc0, 
0x18, 0xc0, 0x18, 0xc0, 0x18, 0xcb, 0x18, 0xcf, 0x8c, 0x31, 0x31, 0x86, 0x00, 0x31, 0x8c, 0x18, 
0x18, 0xc6, 0x0c, 0x0c, 0x63, 0x19, 0x86, 0x31, 0x83, 0x03, 0x18, 0xc0, 0x01, 0x86, 0x18, 0x61, 
0x8d, 0x98, 0x60, 0xc7, 0xe6, 0xdf, 0x80, 0x7e, 0x01, 0x80, 0xc0, 0x60, 0x0c, 0x08, 0x62, 0x01, 
0x10, 0x08, 0xf8, 0x40, 0x20, 0xe0, 0x00, 0x38, 0x08, 0x0e, 0x0d, 0x83, 0x81, 0xb0, 0x70, 0x6c, 
0x0e, 0x0d, 0x83, 0x83, 0x60, 0xe0, 0x70, 0x38, 0x1c, 0x0e, 0x07, 0x03, 0x81, 0xc0, 0xe0, 0x71, 
0xf8, 0x03, 0xf0, 0x87, 0xe4, 0xcf, 0xcd, 0x8f, 0xc6, 0xcf, 0xc6, 0xc7, 0xe6, 0xcf, 0xcd, 0x9e, 
0x5e, 0xcf, 0xc0, 0x07, 0xe0, 0x83, 0xf1, 0xb1, 0xf8, 0x6c, 0x7e, 0x36, 0x1f, 0x8d, 0x8f, 0xc6, 
0xc7, 0xe4, 0x65, 0xf9, 0x31, 0x7e, 0x44, 0x5f, 0x99, 0x97, 0xe4, 0x07, 0x18, 0x03, 0x18, 0x43, 
0x1c, 0x31, 0xc7, 0x18, 0x71, 0xc2, 0x1c, 0x72, 0x67, 0x1c, 0x01, 0x61, 0x86, 0x0c, 0x30, 0x01, 
0x86, 0x08, 0x30, 0xc4, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x1c, 0x00, 0x31, 0xb0, 0x31, 0xb0, 0x31, 0xb0, 0x30, 0x80, 0x63, 0x01, 0xb1, 0x80, 0xd8, 0xc0, 
0x6c, 0x60, 0x36, 0x30, 0x1b, 0x00, 0x06, 0x00, 0x0c, 0x00, 0x18, 0x00, 0x30, 0x00, 0x60, 0xcc, 
0x20, 0x0c, 0x36, 0x06, 0x1b, 0x03, 0x0d, 0x81, 0x86, 0xc0, 0xc3, 0x60, 0x18, 0x03, 0x01, 0x99, 
0x83, 0x33, 0x06, 0x66, 0x0c, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x38, 0x70, 0x00, 0xe1, 0xc0, 0x03, 
0x87, 0x00, 0x0e, 0x20, 0x0e, 0x20, 0x0e, 0x20, 0x0e, 0x20, 0x0c, 0x30, 0x06, 0x18, 0x03, 0x0c, 
0x01, 0x86, 0x00, 0xc6, 0x00, 0xc6, 0x00, 0xc6, 0x01, 0x8c, 0x03, 0x18, 0x06, 0x30, 0x0c, 0x20, 
0x18, 0x40, 0x30, 0x80, 0x61, 0x00, 0xc2, 0x00, 0x61, 0x83, 0x0c, 0x18, 0x60, 0xc3, 0x18, 0xc0, 
0x18, 0xc0, 0x18, 0xc0, 0x18, 0xc0, 0x18, 0xc0, 0x0c, 0x30, 0x01, 0x86, 0x00, 0x31, 0x8c, 0x00, 
0x18, 0xc6, 0x00, 0x0c, 0x63, 0x00, 0x06, 0x31, 0x80, 0x03, 0x18, 0xc0, 0x01, 0x86, 0x00, 0x61, 
0x80, 0x18, 0x60, 0x00, 0x60, 0x01, 0x80, 0x06, 0x01, 0x80, 0xc0, 0x00, 0x00, 0x00, 0x62, 0x01, 
0x10, 0x09, 0x8c, 0x40, 0x20, 0xe0, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x03, 0x80, 0x00, 0x70, 0x00, 
0x0e, 0x00, 0x03, 0x80, 0x00, 0xe0, 0x00, 0x38, 0x00, 0x0e, 0x00, 0x03, 0x80, 0x00, 0xe0, 0x01, 
0x80, 0x03, 0x00, 0x06, 0x00, 0x0c, 0x00, 0x0c, 0x00, 0x0c, 0x00, 0x06, 0x00, 0x0c, 0x00, 0x0c, 
0x0c, 0x18, 0x60, 0x0c, 0x30, 0x06, 0x18, 0x03, 0x0c, 0x00, 0xc3, 0x00, 0x30, 0xc0, 0x18, 0x60, 
0x0c, 0x34, 0x07, 0x0d, 0x01, 0xc3, 0x40, 0x70, 0xd0, 0x1c, 0x34, 0x07, 0x18, 0x03, 0x18, 0x03, 
0x18, 0x01, 0xc6, 0x00, 0x71, 0x80, 0x1c, 0x60, 0x07, 0x18, 0x01, 0x61, 0x80, 0x0c, 0x30, 0x01, 
0x86, 0x00, 0x30, 0xc0, 0x04, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x36, 0x0f, 0x31, 0xb7, 0x31, 0xb7, 0x31, 0xb7, 0x30, 0x9e, 0x61, 0x9f, 0xb0, 0xcf, 0xd8, 0x67, 
0xec, 0x33, 0xf6, 0x19, 0xfb, 0x03, 0xe6, 0x07, 0xcc, 0x0f, 0x98, 0x1f, 0x30, 0x3e, 0x61, 0xec, 
0x27, 0xec, 0x36, 0xe6, 0x1b, 0x73, 0x0d, 0xb9, 0x86, 0xfc, 0xc3, 0x6e, 0x18, 0x63, 0x19, 0xb1, 
0x9b, 0x63, 0x36, 0xc6, 0x6c, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x2c, 0xb7, 0xdc, 0xb2, 0xdf, 0x72, 
0xcb, 0x7d, 0xcb, 0x2d, 0xcb, 0x2d, 0xcb, 0x2d, 0xcb, 0x2d, 0xcc, 0x33, 0xe6, 0x19, 0xf3, 0x0c, 
0xf9, 0x86, 0x7c, 0xc6, 0xdc, 0xc6, 0xdc, 0xc6, 0x6d, 0x8c, 0xdb, 0x19, 0xb6, 0x33, 0x6c, 0x27, 
0x98, 0x4f, 0x30, 0x9e, 0x61, 0x3c, 0xc2, 0x78, 0x63, 0xe3, 0x1f, 0x18, 0xf8, 0xc7, 0xd8, 0xd8, 
0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xcc, 0x33, 0x19, 0x86, 0x63, 0x31, 0x8d, 0x99, 
0x98, 0xc6, 0xcc, 0xcc, 0x63, 0x66, 0x66, 0x31, 0xb3, 0x33, 0x18, 0xd9, 0x98, 0xcc, 0x66, 0x33, 
0x19, 0x8c, 0xc6, 0x30, 0x6f, 0xc1, 0xbf, 0x06, 0xfd, 0xb9, 0xf6, 0x66, 0x63, 0x1e, 0x62, 0x01, 
0x10, 0x09, 0x8c, 0x40, 0x21, 0xb0, 0x78, 0x6c, 0x1e, 0x1b, 0x07, 0x86, 0xc0, 0xf0, 0xd8, 0x3c, 
0x1b, 0x07, 0x86, 0xc1, 0xe1, 0xb0, 0x78, 0x6c, 0x1e, 0x1b, 0x07, 0x86, 0xc1, 0xe1, 0xb0, 0x79, 
0x81, 0xf3, 0x03, 0xe6, 0x07, 0xcc, 0x0f, 0x8c, 0x07, 0xcc, 0x07, 0xc6, 0x07, 0xcc, 0x0f, 0x8c, 
0xcc, 0xd8, 0x67, 0xcc, 0x33, 0xe6, 0x19, 0xf3, 0x0c, 0x7c, 0xc3, 0x3e, 0x30, 0xcf, 0x98, 0x67, 
0xcc, 0x39, 0xfb, 0x0e, 0x7e, 0xc3, 0x9f, 0xb0, 0xe7, 0xec, 0x39, 0xfb, 0x1b, 0x1b, 0x1b, 0x1b, 
0x19, 0x8f, 0xc6, 0x63, 0xf1, 0x98, 0xfc, 0x66, 0x3f, 0x19, 0x8f, 0x33, 0x18, 0xc6, 0x63, 0x18, 
0xcc, 0x63, 0x19, 0x8c, 0x64, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x36, 0x11, 0xb1, 0xb9, 0xb1, 0xb9, 0xb1, 0xb9, 0xb0, 0x31, 0x61, 0xb1, 0xb0, 0xd8, 0xd8, 0x6c, 
0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x06, 0x36, 0x0c, 0x6c, 0x18, 0xd8, 0x31, 0xb0, 0x63, 0x60, 0xcc, 
0x0c, 0x6c, 0x37, 0x36, 0x1b, 0x9b, 0x0d, 0xcd, 0xfe, 0xc6, 0xc3, 0x73, 0x18, 0x63, 0x19, 0xe1, 
0xb3, 0xc3, 0x67, 0x86, 0xcc, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x2c, 0xb6, 0x66, 0xb2, 0xd9, 0x9a, 
0xcb, 0x66, 0x6b, 0x2e, 0x6b, 0x2e, 0x6b, 0x2e, 0x6b, 0x2e, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 
0x8d, 0x86, 0xc6, 0xc6, 0xe6, 0xc6, 0xe6, 0xc6, 0x7d, 0x8c, 0xfb, 0x19, 0xf6, 0x33, 0xec, 0x0c, 
0x58, 0x18, 0xb0, 0x31, 0x60, 0x62, 0xc0, 0xc4, 0x61, 0x83, 0x0c, 0x18, 0x60, 0xc3, 0x18, 0xd8, 
0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc6, 0x63, 0x18, 0xcc, 0x63, 0x33, 0xcd, 0x99, 
0x99, 0xe6, 0xcc, 0xcc, 0xf3, 0x66, 0x66, 0x79, 0xb3, 0x33, 0x3c, 0xd9, 0x98, 0x78, 0x66, 0x1e, 
0x19, 0x8c, 0xc6, 0x30, 0xc0, 0xc3, 0x03, 0x0c, 0x0d, 0xcc, 0xc6, 0x66, 0x63, 0x23, 0x62, 0x01, 
0x10, 0x09, 0x8c, 0x40, 0x21, 0xb0, 0x8c, 0x6c, 0x23, 0x1b, 0x08, 0xc6, 0xc1, 0x18, 0xd8, 0x46, 
0x1b, 0x08, 0xc6, 0xc2, 0x31, 0xb0, 0x8c, 0x6c, 0x23, 0x1b, 0x08, 0xc6, 0xc2, 0x31, 0xb0, 0x8d, 
0x83, 0x1b, 0x06, 0x36, 0x0c, 0x6c, 0x18, 0xcc, 0x0c, 0x6c, 0x0c, 0x66, 0x0c, 0x6c, 0x18, 0xcc, 
0xcc, 0xd8, 0x6c, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0c, 0xc6, 0xc3, 0x63, 0x30, 0xd8, 0xd8, 0x6c, 
0x6c, 0x33, 0x1b, 0x0c, 0xc6, 0xc3, 0x31, 0xb0, 0xcc, 0x6c, 0x33, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 
0x19, 0x8c, 0xc6, 0x63, 0x31, 0x98, 0xcc, 0x66, 0x33, 0x19, 0x8c, 0x33, 0x18, 0xc6, 0x63, 0x18, 
0xcc, 0x63, 0x19, 0x8c, 0x64, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x63, 0x01, 0xbf, 0x31, 0xbf, 0x31, 0xbf, 0x31, 0xb0, 0x30, 0x61, 0xb1, 0xb0, 0xd8, 0xd8, 0x6c, 
0x6c, 0x36, 0x36, 0x1b, 0x1b, 0xe6, 0x37, 0xcc, 0x6f, 0x98, 0xdf, 0x31, 0xbf, 0x63, 0x7c, 0xcc, 
0xec, 0x6f, 0xf6, 0x37, 0xfb, 0x1b, 0xfd, 0x8d, 0x86, 0xc6, 0xff, 0x63, 0x18, 0x63, 0x19, 0xc1, 
0xe3, 0x83, 0xc7, 0x07, 0x8c, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x27, 0x36, 0x66, 0x9c, 0xd9, 0x9a, 
0x73, 0x66, 0x69, 0xac, 0x69, 0xac, 0x69, 0xac, 0x69, 0xac, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 
0x8d, 0x86, 0xc6, 0xc6, 0xc6, 0xc6, 0xc6, 0xfc, 0x61, 0xf8, 0xc3, 0xf1, 0x87, 0xe3, 0x07, 0xce, 
0x0f, 0x9c, 0x1f, 0x38, 0x3e, 0x70, 0x7c, 0xe0, 0x61, 0x83, 0x0c, 0x18, 0x60, 0xc3, 0x18, 0xd8, 
0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc6, 0x63, 0x18, 0xcc, 0x63, 0x1b, 0xd8, 0xdb, 
0x0d, 0xec, 0x6d, 0x86, 0xf6, 0x36, 0xc3, 0x7b, 0x1b, 0x61, 0xbd, 0x8d, 0xb0, 0x30, 0x3c, 0x0c, 
0x0f, 0x07, 0x86, 0x31, 0x81, 0x86, 0x06, 0x18, 0x19, 0x8c, 0xc3, 0x6c, 0x63, 0x03, 0x62, 0x01, 
0x10, 0x09, 0x9c, 0x40, 0x23, 0x18, 0x0c, 0xc6, 0x03, 0x31, 0x80, 0xcc, 0x60, 0x19, 0x8c, 0x06, 
0x31, 0x80, 0xcc, 0x60, 0x33, 0x18, 0x0c, 0xc6, 0x03, 0x31, 0x80, 0xcc, 0x60, 0x33, 0x18, 0x0d, 
0xf3, 0x1b, 0xe6, 0x37, 0xcc, 0x6f, 0x98, 0xcf, 0x8c, 0x6f, 0x8c, 0x67, 0xcc, 0x6f, 0x98, 0xcc, 
0xcc, 0xd8, 0x6c, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0c, 0xc6, 0xc3, 0x63, 0x30, 0xd8, 0xd8, 0x6c, 
0x6c, 0x33, 0x1b, 0x0c, 0xc6, 0xc3, 0x31, 0xb0, 0xcc, 0x6c, 0x33, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 
0x19, 0x8c, 0xc6, 0x63, 0x31, 0x98, 0xcc, 0x66, 0x33, 0x19, 0x8c, 0x1e, 0x18, 0xc3, 0xc3, 0x18, 
0x78, 0x63, 0x0f, 0x0c, 0x64, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x7f, 0x1f, 0xb1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb0, 0x30, 0x61, 0xb1, 0xb0, 0xd8, 0xd8, 0x6c, 
0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x07, 0xf6, 0x0f, 0xec, 0x1f, 0xd8, 0x3f, 0xb0, 0x7f, 0x60, 0xcc, 
0x6c, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0x63, 0x18, 0x63, 0x19, 0xe1, 
0xc3, 0xc3, 0x87, 0x87, 0x0c, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x27, 0x36, 0x66, 0x9c, 0xd9, 0x9a, 
0x73, 0x66, 0x69, 0xac, 0x69, 0xac, 0x69, 0xac, 0x69, 0xac, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 
0x8d, 0x86, 0xc6, 0xfc, 0xc6, 0xfc, 0xc6, 0xd8, 0x61, 0xb0, 0xc3, 0x61, 0x86, 0xc3, 0x00, 0x67, 
0x80, 0xcf, 0x01, 0x9e, 0x03, 0x3c, 0x06, 0x78, 0x61, 0x83, 0x0c, 0x18, 0x60, 0xc3, 0x18, 0xd8, 
0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc3, 0xc1, 0xb0, 0x78, 0x36, 0x1e, 0x78, 0xdb, 
0x0f, 0x3c, 0x6d, 0x87, 0x9e, 0x36, 0xc3, 0xcf, 0x1b, 0x61, 0xe7, 0x8d, 0xb0, 0x78, 0x18, 0x1e, 
0x06, 0x03, 0x03, 0x63, 0x03, 0x0c, 0x0c, 0x30, 0x31, 0x8c, 0xc3, 0x6c, 0x36, 0x3f, 0x62, 0x01, 
0x10, 0x09, 0x86, 0x40, 0x23, 0xf8, 0xfc, 0xfe, 0x3f, 0x3f, 0x8f, 0xcf, 0xe1, 0xf9, 0xfc, 0x7e, 
0x3f, 0x8f, 0xcf, 0xe3, 0xf3, 0xf8, 0xfc, 0xfe, 0x3f, 0x3f, 0x8f, 0xcf, 0xe3, 0xf3, 0xf8, 0xfd, 
0x83, 0xfb, 0x07, 0xf6, 0x0f, 0xec, 0x1f, 0xcc, 0x0f, 0xec, 0x0f, 0xe6, 0x0f, 0xec, 0x1f, 0xcc, 
0xcc, 0xd8, 0x6c, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0c, 0xc6, 0xc3, 0x63, 0x30, 0xd8, 0xd8, 0x6c, 
0x6c, 0x33, 0x1b, 0x0c, 0xc6, 0xc3, 0x31, 0xb0, 0xcc, 0x6c, 0x33, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 
0x19, 0x8c, 0xc6, 0x63, 0x31, 0x98, 0xcc, 0x66, 0x33, 0x19, 0x8c, 0x0c, 0x0d, 0x81, 0x81, 0xb0, 
0x30, 0x36, 0x06, 0x06, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0x63, 0x31, 0xb1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb0, 0xb0, 0x61, 0xb1, 0xb0, 0xd8, 0xd8, 0x6c, 
0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x06, 0x06, 0x0c, 0x0c, 0x18, 0x18, 0x30, 0x30, 0x60, 0x60, 0xcc, 
0x6c, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0x63, 0x18, 0x63, 0x19, 0xb1, 
0xe3, 0x63, 0xc6, 0xc7, 0x8c, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x22, 0x36, 0x66, 0x88, 0xd9, 0x9a, 
0x23, 0x66, 0x68, 0xec, 0x68, 0xec, 0x68, 0xec, 0x68, 0xec, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 
0x8d, 0x86, 0xc6, 0xc0, 0xc6, 0xc0, 0xc6, 0xcc, 0x61, 0x98, 0xc3, 0x31, 0x86, 0x63, 0x08, 0x61, 
0xd0, 0xc3, 0xa1, 0x87, 0x43, 0x0e, 0x86, 0x1c, 0x61, 0x83, 0x0c, 0x18, 0x60, 0xc3, 0x18, 0xd8, 
0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xd8, 0xc3, 0xc1, 0xb0, 0x78, 0x36, 0x1e, 0x78, 0xe7, 
0x0f, 0x3c, 0x73, 0x87, 0x9e, 0x39, 0xc3, 0xcf, 0x1c, 0xe1, 0xe7, 0x8e, 0x70, 0xcc, 0x3c, 0x33, 
0x0f, 0x03, 0x03, 0x66, 0x06, 0x18, 0x18, 0x60, 0x61, 0x8c, 0xc3, 0x9c, 0x36, 0x63, 0x62, 0x01, 
0x10, 0x09, 0x86, 0x40, 0x23, 0x19, 0x8c, 0xc6, 0x63, 0x31, 0x98, 0xcc, 0x63, 0x19, 0x8c, 0xc6, 
0x31, 0x98, 0xcc, 0x66, 0x33, 0x19, 0x8c, 0xc6, 0x63, 0x31, 0x98, 0xcc, 0x66, 0x33, 0x19, 0x8d, 
0x83, 0x03, 0x06, 0x06, 0x0c, 0x0c, 0x18, 0x0c, 0x0c, 0x0c, 0x0c, 0x06, 0x0c, 0x0c, 0x18, 0x0c, 
0xcc, 0xd8, 0x6c, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0c, 0xc6, 0xc3, 0x63, 0x30, 0xd8, 0xd8, 0x6c, 
0x6c, 0x33, 0x1b, 0x0c, 0xc6, 0xc3, 0x31, 0xb0, 0xcc, 0x6c, 0x33, 0x1b, 0x1b, 0x1b, 0x1b, 0x1b, 
0x19, 0x8c, 0xc6, 0x63, 0x31, 0x98, 0xcc, 0x66, 0x33, 0x19, 0x8c, 0x0c, 0x0d, 0x81, 0x81, 0xb0, 
0x30, 0x36, 0x06, 0x06, 0xc4, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0xc1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb1, 0xb0, 0xb1, 0x63, 0x33, 0xb1, 0x99, 0xd8, 0xcc, 
0xec, 0x66, 0x76, 0x33, 0x3b, 0x06, 0x16, 0x0c, 0x2c, 0x18, 0x58, 0x30, 0xb0, 0x61, 0x60, 0xcc, 
0x6c, 0xec, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0x86, 0xc6, 0xc3, 0x63, 0x18, 0x63, 0x19, 0x99, 
0xb3, 0x33, 0x66, 0x66, 0xcc, 0x1b, 0x03, 0x30, 0x33, 0x03, 0x22, 0x36, 0x66, 0x88, 0xd9, 0x9a, 
0x23, 0x66, 0x68, 0xec, 0x68, 0xec, 0x68, 0xec, 0x68, 0xec, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 
0x8d, 0x86, 0xc6, 0xc0, 0xc6, 0xc0, 0xc6, 0xc6, 0x61, 0x8c, 0xc3, 0x19, 0x86, 0x33, 0x08, 0x68, 
0xd0, 0xd1, 0xa1, 0xa3, 0x43, 0x46, 0x86, 0x8c, 0x61, 0x83, 0x0c, 0x18, 0x60, 0xc3, 0x18, 0xd9, 
0xd8, 0xd9, 0xd8, 0xd9, 0xd8, 0xd9, 0xd8, 0xd9, 0xc1, 0x80, 0xe0, 0x30, 0x1c, 0x0c, 0x30, 0x66, 
0x06, 0x18, 0x33, 0x03, 0x0c, 0x19, 0x81, 0x86, 0x0c, 0xc0, 0xc3, 0x06, 0x61, 0x86, 0x66, 0x61, 
0x99, 0x83, 0x01, 0xc6, 0x0c, 0x18, 0x30, 0x60, 0xc1, 0x8c, 0xc1, 0x98, 0x1c, 0x63, 0x62, 0x01, 
0x10, 0x09, 0x86, 0x40, 0x26, 0x0d, 0x8d, 0x83, 0x63, 0x60, 0xd8, 0xd8, 0x33, 0x1b, 0x06, 0xc6, 
0x60, 0xd8, 0xd8, 0x36, 0x36, 0x0d, 0x8d, 0x83, 0x63, 0x60, 0xd8, 0xd8, 0x36, 0x36, 0x0d, 0x8d, 
0x83, 0x0b, 0x06, 0x16, 0x0c, 0x2c, 0x18, 0x4c, 0x0c, 0x2c, 0x0c, 0x26, 0x0c, 0x2c, 0x18, 0x4c, 
0xcc, 0xd8, 0x6c, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0c, 0xc6, 0xc3, 0x63, 0x30, 0xd8, 0xd8, 0x6c, 
0x6c, 0x33, 0x1b, 0x0c, 0xc6, 0xc3, 0x31, 0xb0, 0xcc, 0x6c, 0x33, 0x1b, 0x1b, 0x3b, 0x1b, 0x3b, 
0x19, 0x9c, 0xc6, 0x67, 0x31, 0x99, 0xcc, 0x66, 0x73, 0x19, 0x9c, 0x0c, 0x07, 0x01, 0x80, 0xe0, 
0x30, 0x1c, 0x06, 0x03, 0x84, 0x02, 0x20, 0x11, 0x00, 0x88, 0x04, 0x40, 0x22, 0x01, 0x00, 

0xc1, 0x9f, 0xbf, 0x3f, 0x3f, 0x3f, 0x3f, 0x3f, 0x1f, 0x1e, 0x7e, 0x1d, 0xbf, 0x0e, 0xdf, 0x87, 
0x6f, 0xc3, 0xb7, 0xe1, 0xdb, 0xf3, 0xe7, 0xe7, 0xcf, 0xcf, 0x9f, 0x9f, 0x3f, 0x3e, 0x60, 0xc7, 
0xe7, 0x6c, 0x36, 0x36, 0x1b, 0x1b, 0x0d, 0x8d, 0xc6, 0xe6, 0xc3, 0x63, 0x3c, 0x67, 0x99, 0x8d, 
0x9b, 0x1b, 0x36, 0x36, 0x6f, 0xdb, 0xf3, 0x3f, 0x33, 0xf3, 0x20, 0x36, 0x66, 0x80, 0xd9, 0x9a, 
0x03, 0x66, 0x68, 0x6c, 0x68, 0x6c, 0x68, 0x6c, 0x68, 0x6c, 0x67, 0xe3, 0xe3, 0xf1, 0xf1, 0xf8, 
0xf8, 0xfc, 0x7c, 0xc0, 0xfc, 0xc0, 0xfc, 0xc3, 0x61, 0x86, 0xc3, 0x0d, 0x86, 0x1b, 0x07, 0xc7, 
0x8f, 0x8f, 0x1f, 0x1e, 0x3e, 0x3c, 0x7c, 0x78, 0x60, 0xe3, 0x07, 0x18, 0x38, 0xc1, 0xcf, 0x8e, 
0xcf, 0x8e, 0xcf, 0x8e, 0xcf, 0x8e, 0xcf, 0x8e, 0xc1, 0x80, 0xe0, 0x30, 0x1c, 0x0c, 0x30, 0x66, 
0x06, 0x18, 0x33, 0x03, 0x0c, 0x19, 0x81, 0x86, 0x0c, 0xc0, 0xc3, 0x06, 0x61, 0x86, 0x66, 0x61, 
0x99, 0x83, 0x01, 0xc7, 0xef, 0xdf, 0xbf, 0x7e, 0xfd, 0x8c, 0x71, 0x98, 0x1c, 0x3f, 0x63, 0xff, 
0x1f, 0xf9, 0xbc, 0x7f, 0xe6, 0x0c, 0xfd, 0x83, 0x3f, 0x60, 0xcf, 0xd8, 0x31, 0xfb, 0x06, 0x7e, 
0x60, 0xcf, 0xd8, 0x33, 0xf6, 0x0c, 0xfd, 0x83, 0x3f, 0x60, 0xcf, 0xd8, 0x33, 0xf6, 0x0c, 0xfd, 
0xf9, 0xf3, 0xf3, 0xe7, 0xe7, 0xcf, 0xcf, 0x8f, 0xc7, 0xcf, 0xc7, 0xc7, 0xe7, 0xcf, 0xcf, 0x9e, 
0xde, 0xcf, 0xc7, 0xc7, 0xe3, 0xe3, 0xf1, 0xf1, 0xf8, 0x7c, 0x7e, 0x3e, 0x1f, 0x8f, 0x8f, 0xc7, 
0xc7, 0xe1, 0xf1, 0xf8, 0x7c, 0x7e, 0x1f, 0x1f, 0x87, 0xc7, 0xe1, 0xf1, 0xf1, 0xd9, 0xf1, 0xd9, 
0xf0, 0xec, 0x7c, 0x3b, 0x1f, 0x0e, 0xc7, 0xc3, 0xb1, 0xf0, 0xec, 0x0c, 0x07, 0x01, 0x80, 0xe0, 
0x30, 0x1c, 0x06, 0x03, 0x87, 0xfe, 0x3f, 0xf1, 0xff, 0x8f, 0xfc, 0x7f, 0xe3, 0xff, 0x00, 

0x1c, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x08, 0x00, 0x00, 
0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x60, 
0x00, 0x0c, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x14, 0x0a, 0x00, 0x00, 0x06, 0x06, 0x00, 0x00, 0x02, 0x02, 0x00, 0x00, 0x06, 0x03, 0x00, 0x00, 
0x01, 0x80, 0x60, 0xe0, 0xe0, 0x00, 0x00, 0x00, 0x03, 0x87, 0x0c, 0x8c, 0x84, 0x08, 0x00, 0x00, 
0x08, 0x60, 0x00, 0x00, 0xc0, 0x60, 0x00, 0x00, 0x40, 0x20, 0x22, 0x22, 0x32, 0xc8, 0x00, 0x00, 
0x00, 0x60, 0xc0, 0x00, 0x03, 0x18, 0xc3, 0x00, 0x00, 0xe3, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x30, 0x06, 0x00, 0x00, 0x01, 0x81, 0x80, 0x00, 0x03, 0x83, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0xc0, 0x00, 0x00, 0x30, 0x30, 0x60, 0x60, 0x00, 0x00, 0x00, 
0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0x30, 0x30, 0x00, 0x03, 0x06, 0x00, 0x00, 0xe1, 0xcd, 0x8d, 
0x86, 0x46, 0x47, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x0c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x01, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 
0x60, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 
0x0c, 0xc3, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x60, 0x60, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x00, 0x06, 0x01, 0x80, 0xcc, 
0x00, 0x18, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x1c, 0x0e, 0x00, 0x00, 0x06, 0x06, 0x1f, 0x1f, 0x0c, 0x0c, 0x00, 0x00, 0x06, 0x03, 0x07, 0xc7, 
0xc3, 0x00, 0xc1, 0xb1, 0xb0, 0x00, 0x00, 0x00, 0x06, 0xcd, 0x93, 0x13, 0x18, 0x30, 0x00, 0x00, 
0x07, 0xc0, 0x00, 0x00, 0xc0, 0x60, 0x00, 0x01, 0x80, 0xc0, 0x1c, 0x1c, 0x4d, 0x30, 0x00, 0x00, 
0x00, 0x60, 0xc3, 0xe3, 0xe3, 0x18, 0xc3, 0x1f, 0x7d, 0xb6, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x30, 0x06, 0x00, 0x00, 0x01, 0x81, 0x87, 0xc7, 0xc6, 0xc6, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0xc0, 0x00, 0x00, 0x30, 0x30, 0x60, 0x61, 0xf3, 0xe0, 0x00, 
0x06, 0x06, 0x00, 0x00, 0x00, 0x00, 0x30, 0x30, 0x00, 0x03, 0x06, 0x3e, 0x7d, 0xb3, 0x6d, 0x8d, 
0x89, 0x89, 0x8d, 0x8d, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x0c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x01, 0x80, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0x00, 0x06, 0x0c, 0x3e, 0x7c, 0xf8, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x80, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 
0x60, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 
0x0c, 0xc3, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x60, 0x60, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x00, 0x06, 0x01, 0x80, 0xcc, 
0x00, 0x18, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma10bAdd_Font = {0x01, 16, 0, 16, 0, 0, 16,271, 0x1e00, 0x1eff,
(PEGUSHORT *) Tahoma10bAdd_Font_offset_table, &WinInwa11nMiy_Font,
(PEGUBYTE *) Tahoma10bAdd_Font_data_table};
//...........................................................................


ROMDATA PEGUSHORT Tahoma10bTai_offset_table[92] = {
0x0000,0x0009,0x0012,0x001b,0x0024,0x002d,0x0037,0x003e,0x0046,0x004f,0x0059,0x0063,0x006f,0x007b,0x0084,0x008d,
0x0095,0x00a0,0x00ad,0x00ba,0x00c3,0x00cc,0x00d5,0x00de,0x00e6,0x00ef,0x00f8,0x0101,0x010a,0x0113,0x011d,0x0127,
0x0130,0x0139,0x0142,0x0149,0x0152,0x015a,0x0163,0x016a,0x0173,0x017c,0x0184,0x018d,0x0198,0x01a0,0x01a8,0x01af,
0x01b5,0x01bc,0x01c3,0x01ce,0x01d6,0x01de,0x01e6,0x01ee,0x01f1,0x01f6,0x01f8,0x0205,0x0212,0x021f,0x022c,0x0234,
0x0238,0x0240,0x0246,0x024c,0x0253,0x025a,0x0261,0x0267,0x026a,0x0270,0x0275,0x0279,0x027d,0x0281,0x0285,0x028d,
0x0295,0x029e,0x02a8,0x02b1,0x02bb,0x02c5,0x02ce,0x02db,0x02e5,0x02f0,0x02fa,0x0308};



ROMDATA PEGUBYTE Tahoma10bTai_data_table[1552] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x50, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x8c, 0xc9, 0x18, 
0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x06, 0x20, 0x08, 0x01, 0xe1, 0xf1, 0xe9, 0xf0, 0x00, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xc1, 0x00, 0x00, 0x7d, 0xe9, 0x80, 0x00, 0x40, 0x8d, 0x2b, 0xe2, 
0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x07, 0xc0, 0x14, 0x03, 0xf3, 0xf3, 0xf3, 0xf0, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4f, 0xc0, 0x00, 0xc3, 0x35, 0xc0, 0x00, 0x3c, 0x86, 0x31, 0x45, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0xc0, 0x36, 0x00, 0x00, 0xc0, 0x30, 0x06, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0x60, 0x00, 0x06, 0x00, 0x02, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4d, 0x60, 0x00, 0xf3, 0x32, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0xc3, 0x33, 0x00, 0x00, 0xc0, 0x30, 0x06, 0x00, 0x00, 
0x00, 

0x7e, 0x39, 0x94, 0xcf, 0xc6, 0xe2, 0x8c, 0x31, 0xf1, 0xf0, 0xe3, 0x28, 0xcf, 0x8c, 0xf8, 0xc7, 
0xc3, 0xe1, 0xf2, 0x8e, 0x36, 0x33, 0xe3, 0x1f, 0x8d, 0xc7, 0xe6, 0x19, 0xfb, 0x0d, 0x86, 0xc3, 
0x31, 0x98, 0xd8, 0xb6, 0x2c, 0x7c, 0xc3, 0x31, 0x9f, 0x3f, 0x3f, 0x0f, 0x9f, 0x1f, 0x98, 0x6f, 
0xcc, 0x36, 0x26, 0xfc, 0x7c, 0xcd, 0x88, 0x0f, 0x81, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4d, 0x6c, 0x66, 0x18, 0x30, 0xdf, 0x1a, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xc0, 0x00, 0x03, 0x00, 0x00, 0x01, 0x83, 0x61, 0x00, 0x00, 0xc0, 0x60, 0x06, 0xcd, 0x8e, 
0x00, 

0xc3, 0x6d, 0xbe, 0xd8, 0x6d, 0x37, 0xcc, 0x38, 0x18, 0x19, 0xb6, 0x7d, 0x98, 0xcd, 0x8c, 0xcc, 
0x66, 0x33, 0x07, 0xd6, 0x6b, 0x36, 0x33, 0x30, 0xda, 0x6c, 0x37, 0x3b, 0x03, 0x8d, 0xc6, 0xe3, 
0x71, 0xb8, 0xdc, 0xb7, 0x2c, 0xc6, 0xe3, 0x71, 0xb0, 0x61, 0x81, 0x98, 0xc1, 0xb3, 0xdc, 0x61, 
0xee, 0x37, 0x26, 0x06, 0x8c, 0xfd, 0xf0, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4d, 0x6c, 0x66, 0x18, 0x60, 0xc1, 0xb7, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x23, 0xe3, 0xf3, 0x3c, 0x76, 0x3f, 0x0f, 0xc1, 0xf1, 0xdc, 0xcf, 0xc3, 0xb4, 0xfd, 0x91, 
0x00, 

0xe3, 0x6d, 0xb6, 0xd8, 0x6c, 0x36, 0xcc, 0x18, 0xdb, 0x0d, 0xb3, 0x6c, 0xdc, 0xcd, 0xcc, 0xce, 
0x67, 0x33, 0xe6, 0xd6, 0xc1, 0xb7, 0x33, 0x30, 0xd8, 0x6e, 0x33, 0x5b, 0xf1, 0x8c, 0xc6, 0x63, 
0x61, 0xb0, 0xcd, 0x73, 0x5c, 0xe6, 0x63, 0x61, 0xb0, 0x71, 0x99, 0x9c, 0xc1, 0xb0, 0xcc, 0x66, 
0x66, 0x23, 0x56, 0x66, 0x76, 0x0c, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4f, 0xcc, 0x66, 0x18, 0x60, 0xc1, 0xb3, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x16, 0x36, 0x1b, 0x66, 0xcb, 0x60, 0x18, 0x00, 0x1b, 0x26, 0xd8, 0x06, 0x6c, 0x0d, 0xa0, 
0x00, 

0x43, 0x0d, 0x86, 0xdb, 0x6d, 0xb0, 0xcd, 0x98, 0xfb, 0x8c, 0x33, 0x0c, 0xc8, 0xcc, 0x8c, 0xc4, 
0x62, 0x30, 0x31, 0xa6, 0xd9, 0xb2, 0x33, 0x36, 0xdb, 0x64, 0x33, 0x59, 0x99, 0x8c, 0xc6, 0x63, 
0x65, 0xb2, 0xcd, 0x73, 0x5c, 0x46, 0x63, 0x31, 0xbe, 0x21, 0xb5, 0x88, 0xc1, 0xb6, 0xcd, 0x7d, 
0x66, 0x73, 0x56, 0xe6, 0x66, 0x0c, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4d, 0x6c, 0x66, 0x18, 0x60, 0xc1, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x96, 0x36, 0x1b, 0x66, 0xcb, 0x63, 0x18, 0xc0, 0x1b, 0x26, 0xd8, 0x36, 0x60, 0x0d, 0xa0, 
0xa9, 

0xc3, 0x19, 0x8c, 0xdf, 0x6f, 0xb1, 0x8c, 0xd8, 0x19, 0x8c, 0x63, 0x18, 0xd8, 0xcd, 0x8c, 0xcc, 
0x66, 0x31, 0xb1, 0xa6, 0xd9, 0xb6, 0x33, 0x36, 0xdb, 0x6c, 0x33, 0x99, 0x99, 0x8c, 0xc6, 0x63, 
0x6b, 0xb5, 0xce, 0x73, 0x9c, 0xc6, 0x63, 0x61, 0x83, 0x61, 0xb3, 0x98, 0xc1, 0xbe, 0xcd, 0xec, 
0xe6, 0xb3, 0x8e, 0xc6, 0xe6, 0x0d, 0x88, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4d, 0x6c, 0x66, 0x18, 0x60, 0xc1, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x04, 0x16, 0x36, 0xdb, 0x06, 0xc3, 0x63, 0x18, 0xc1, 0x9b, 0x06, 0xd9, 0xb6, 0x60, 0x0d, 0xa0, 
0xf9, 

0xc3, 0x19, 0x8c, 0xd8, 0x6c, 0x31, 0xec, 0xd8, 0x19, 0xbc, 0x63, 0x18, 0xdd, 0xed, 0xcc, 0xdc, 
0x6e, 0x31, 0xf1, 0xc6, 0xd3, 0xf7, 0x37, 0xb4, 0xda, 0x6e, 0x33, 0x99, 0x99, 0xbc, 0xc6, 0x63, 
0x73, 0xb9, 0xce, 0x33, 0x8d, 0xc6, 0xfb, 0x61, 0x87, 0x71, 0xbb, 0xb8, 0xc3, 0xb0, 0xcc, 0x6e, 
0xe7, 0x33, 0x8e, 0xc6, 0xc6, 0x0d, 0xf0, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0x22, 0x01, 0x10, 0x08, 0x80, 0x4d, 0x6e, 0x77, 0x1c, 0x70, 0xe1, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x02, 0x26, 0x36, 0xdb, 0x06, 0xf3, 0x62, 0x18, 0x81, 0x9b, 0xc6, 0xda, 0xd7, 0x30, 0x0d, 0x91, 
0x56, 

0xc3, 0x1f, 0x8f, 0xd8, 0x6c, 0x31, 0x9c, 0x70, 0x19, 0xcc, 0x7f, 0x1f, 0xcd, 0x9c, 0xcf, 0xd8, 
0x6c, 0x30, 0x31, 0xc6, 0xe3, 0x33, 0x39, 0xb8, 0xdc, 0x66, 0x33, 0x1b, 0xf1, 0xcc, 0xfe, 0x7f, 
0x61, 0xb0, 0xcc, 0x33, 0x0d, 0x86, 0xef, 0x7f, 0x86, 0x31, 0x99, 0xb0, 0xc3, 0x30, 0xcf, 0xe6, 
0x66, 0x33, 0x06, 0xfe, 0xfe, 0x0c, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 
0xe3, 0xff, 0x1f, 0xf8, 0xff, 0xcf, 0xc6, 0x33, 0x0c, 0x30, 0x61, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xc3, 0xe3, 0x9b, 0xfc, 0x73, 0x3f, 0xcf, 0xf0, 0xf1, 0xc7, 0x8e, 0x63, 0x30, 0x0d, 0x8e, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
0xe1, 0x30, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x68, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x87, 
0xee, 0x33, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x6a, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x07, 
0x6f, 0xf3, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x38, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 


};

xFONTYY Tahoma10bTai_Font = {0x01, 16, 0, 16, 0, 0, 16, 97, 0x0e01, 0x0e5b,
(PEGUSHORT *) Tahoma10bTai_offset_table, &Tahoma10bAdd_Font,
(PEGUBYTE *) Tahoma10bTai_data_table};


