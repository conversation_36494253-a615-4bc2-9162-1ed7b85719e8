#include <stdio.h>
#include <string.h>
#include <math.h>
#include "Regional.hpp"
#include "const.h"

CRegional::CRegional()
{
	m_nChannelA   = 2087;
	m_nChannelB   = 2088;
	m_nBandWidthA = 0;
	m_nBandWidthB = 0;
	m_nModeA      = MODE_RXTX;
	m_nModeB      = MODE_RXTX;
	m_bPower      = 1;
	m_nZoneSize   = 5;
	m_nLatNE = 0;
	m_nLonNE = 0;
	m_nLatSW = 0;
	m_nLonSW = 0;

	m_nUpdateYear   = UTC_YEAR_NULL_VALUE;
	m_nUpdateMonth  = UTC_MONTH_NULL_VALUE;
	m_nUpdateDay    = UTC_DAY_NULL_VALUE;
	m_nUpdateHour   = UTC_HOUR_NULL_VALUE;
	m_nUpdateMinute = UTC_MINUTE_NULL_VALUE;
	m_nUpdateSecond = UTC_SECOND_NULL_VALUE;

	m_nUseHour   = UTC_HOUR_NULL_VALUE;
	m_nUseMinute = UTC_MINUTE_NULL_VALUE;
	m_nUseSecond = UTC_SECOND_NULL_VALUE;
}

CRegional::~CRegional()
{
}

void CRegional::GetNEPosition(BYTE *pszLatNE, BYTE *pszLonNE)
{
	int    nDeg;
	double rMin;
	char   cDir;
	
	if( m_nLatNE == LAT_NULL_VALUE || m_nLonNE == LON_NULL_VALUE )
	{
		sprintf((char *)pszLatNE,  "__%c__._N", DEG_CHR);
		sprintf((char *)pszLonNE, "___%c__._E", DEG_CHR);
		return;
	}

	double rLatNE = static_cast<double>(m_nLatNE) / static_cast<double>(DEG_MUL_FACTOR);
	double rLonNE = static_cast<double>(m_nLonNE) / static_cast<double>(DEG_MUL_FACTOR);

	cDir = (rLatNE >= 0) ? 'N' : 'S';
	nDeg = static_cast<int>(fabs(rLatNE));
	rMin = (fabs(rLatNE) - static_cast<double>(nDeg)) * 60.f;
	sprintf((char *)pszLatNE, "%02d%c%04.1f%c", nDeg, DEG_CHR, rMin, cDir);
	
	cDir = (rLonNE >= 0) ? 'E' : 'W';
	nDeg = static_cast<int>(fabs(rLonNE));
	rMin = (fabs(rLonNE) - static_cast<double>(nDeg)) * 60.f;
	sprintf((char *)pszLonNE, "%03d%c%04.1f%c", nDeg, DEG_CHR, rMin, cDir);
}

void CRegional::GetSWPosition(BYTE *pszLatSW, BYTE *pszLonSW)
{
	int    nDeg;
	double rMin;
	char   cDir;
	
	if( m_nLatSW == LAT_NULL_VALUE || m_nLonSW == LON_NULL_VALUE )
	{
		sprintf((char *)pszLatSW,  "__%c__._N", DEG_CHR);
		sprintf((char *)pszLonSW, "___%c__._E", DEG_CHR);
		return;
	}
	
	double rLatSW = static_cast<double>(m_nLatSW) / static_cast<double>(DEG_MUL_FACTOR);
	double rLonSW = static_cast<double>(m_nLonSW) / static_cast<double>(DEG_MUL_FACTOR);

	cDir = (rLatSW >= 0) ? 'N' : 'S';
	nDeg = static_cast<int>(fabs(rLatSW));
	rMin = (fabs(rLatSW) - static_cast<double>(nDeg)) * 60.f;
	sprintf((char *)pszLatSW, "%02d%c%04.1f%c", nDeg, DEG_CHR, rMin, cDir);
	
	cDir = (rLonSW >= 0) ? 'E' : 'W';
	nDeg = static_cast<int>(fabs(rLonSW));
	rMin = (fabs(rLonSW) - static_cast<double>(nDeg)) * 60.f;
	sprintf((char *)pszLonSW, "%03d%c%04.1f%c", nDeg, DEG_CHR, rMin, cDir);
}

void CRegional::GetUpdateTimeString(BYTE *pszTime)
{
	if( m_nUpdateYear   == UTC_YEAR_NULL_VALUE   ||
		m_nUpdateMonth  == UTC_MONTH_NULL_VALUE  ||
		m_nUpdateDay    == UTC_DAY_NULL_VALUE    ||
		m_nUpdateHour   >= UTC_HOUR_NULL_VALUE   ||
		m_nUpdateMinute >= UTC_MINUTE_NULL_VALUE ||
		m_nUpdateSecond >= UTC_SECOND_NULL_VALUE )
	{
		strcpy((char *)pszTime, "__-__ __:__");
	}		
	else
	{
		sprintf((char *)pszTime, "%02d-%02d %02d:%02d", m_nUpdateMonth, m_nUpdateDay, m_nUpdateHour,  m_nUpdateMinute);
	}		
}

void CRegional::GetUseTimeString(BYTE *pszTime)
{
	if( m_nUseHour   >= UTC_HOUR_NULL_VALUE   ||
		m_nUseMinute >= UTC_MINUTE_NULL_VALUE ||
		m_nUseSecond >= UTC_SECOND_NULL_VALUE )
	{
		strcpy((char *)pszTime, "__:__");
	}		
	else
	{
		sprintf((char *)pszTime, "%02d:%02d", m_nUseHour, m_nUseMinute);
	}		
}

/* Add in use time parsing function : HSI 12.05.07 */
void CRegional::SetUseTime(char * szInUseUTC) 
{
	char szTemp[5];
	if( szInUseUTC[0] != '\0' ) 
	{
		strncpy(szTemp, szInUseUTC, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nUseHour);

		strncpy(szTemp, szInUseUTC+2, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nUseMinute);

		strncpy(szTemp, szInUseUTC+4, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nUseSecond);
	} 
	else 
	{
		m_nUseHour = UTC_HOUR_NULL_VALUE;
		m_nUseMinute  = UTC_MINUTE_NULL_VALUE;
		m_nUseSecond  = UTC_SECOND_NULL_VALUE;
	}

}

