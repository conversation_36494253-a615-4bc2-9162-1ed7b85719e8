/*...........................................................................*/
/*.                  File Name : SDCARDDRV.H                                .*/
/*.                                                                         .*/
/*.                       Date : 2008.06.14                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef _LIB_FILE_MAKE_
    #include "DataType.h"
#endif

#ifndef  __SDCARDDRV_H__
#define  __SDCARDDRV_H__

//=============================================================================
#define  SDCARD_ERROR_NO_ERROR                    0
#define  SDCARD_ERROR_NO_RESPONSE                 1
#define  SDCARD_ERROR_OUT_OF_RANGE                2
#define  SDCARD_ERROR_ADDRESS                     3
#define  SDCARD_ERROR_BLOCK_LEN                   4
#define  SDCARD_ERROR_ERASE_SEQ                   5
#define  SDCARD_ERROR_ERASE_PARAM                 6
#define  SDCARD_ERROR_WP_VIOLATION                7
#define  SDCARD_ERROR_CARD_IS_LOCKED              8
#define  SDCARD_ERROR_LOCK_UNLOCK_FAILED          9
#define  SDCARD_ERROR_COM_CRC                    10
#define  SDCARD_ERROR_ILLEGAL_COMMAND            11
#define  SDCARD_ERROR_CARD_ECC_FAILED            12
#define  SDCARD_ERROR_CC                         13
#define  SDCARD_ERROR_GENERAL                    14
#define  SDCARD_ERROR_UNDERRUN                   15
#define  SDCARD_ERROR_OVERRUN                    16
#define  SDCARD_ERROR_CID_CSD_OVERWRITE          17
#define  SDCARD_ERROR_STATE_MISMATCH             18
#define  SDCARD_ERROR_HEADER_MISMATCH            19
#define  SDCARD_ERROR_TIMEOUT                    20
#define  SDCARD_ERROR_CRC                        21
#define  SDCARD_ERROR_DRIVER_FAILURE             22
//=============================================================================
#define  SDCARD_MOUNT_NO                          0
//-----------------------------------------------------------------------------
#define  SDCARD_CARD_CHANGE_CLEARED               0
#define  SDCARD_CARD_CHANGE_OCCURED               1
//=============================================================================

#ifdef  __cplusplus
extern "C" {
#endif

//=============================================================================
void  SdCardSetSdCardPtr(void *pSdCard);
//=============================================================================
DWORD SdCardReadBlockData(DWORD dBlockNo,void *pReadAddr,DWORD dReadSize);
DWORD SdCardWriteBlockData(DWORD dBlockNo,void *pWritedAddr,DWORD dWriteSize);
DWORD SdCardSetUpAndDetect(void);
DWORD SdCardSendSelectCard(void);
DWORD SdCardSendPullUpMode(int nDisableEnableMode);
DWORD SdCardSendBusWidth(DWORD dBusWidth);
DWORD SdCardSendBlockLength(DWORD dBlockLength);
//=============================================================================
DWORD SdCardGetStartingHead(int nPartitionNo);
DWORD SdCardGetStartingSect(int nPartitionNo);
DWORD SdCardGetStartingCyli(int nPartitionNo);
DWORD SdCardGetEndingHead(int nPartitionNo);
DWORD SdCardGetEndingSect(int nPartitionNo);
DWORD SdCardGetEndingCyli(int nPartitionNo);
DWORD SdCardGetRelativeSectors(int nPartitionNo);
DWORD SdCardGetTotalSectors(int nPartitionNo);
DWORD SdCardGetLBAbyCHS(int nPartitionNo,int nCyl,int nHead,int nSector);
int   SdCardGetWriteBlockSizeFromCSD(void);
int   SdCardGetBlocksPerSectorFromCSD(void);
int   SdCardGetReadBlockSizeFromCSD(void);
//=============================================================================
void  SdCardSetCardChangStatus(int nChanged);
int   SdCardGetCardChangStatus(void);
void  SdCardSetCardInsertStatus(int nInserted);
int   SdCardGetCardInsertStatus(void);
//=============================================================================

#ifdef  __cplusplus
}
#endif

#endif

