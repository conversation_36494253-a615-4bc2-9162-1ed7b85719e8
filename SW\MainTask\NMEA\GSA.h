#ifndef __NMEA_0183_GSA_H__
#define __NMEA_0183_GSA_H__

#include "Sentence.hpp"

/******************************************************************************
 * 
 * CGsv - GNSS Satellites in View
 * 
 * $--CGsv,x,x,xx,xx,xx,xxx,xx,................,xx,xx,xxx,xx*hh&lt;CR&gt;&lt;LF&gt;
 *        | | |  |   |  |  |      |            |  |   |  |
 *        1 2 3  4   5  6  7     n~m         m+1 m+2 m+3 m+4
 *
 *	1. Total number of sentence, 1 to 9
 *  2. Sentence number, 1 to 9
 *  3. Total number of satellites in view
 *  4. Satellites ID number
 *  5. Elevation, degrees, 90 maximum
 *  6. Azimuth, degree True, 000 to 359
 *  7. SNR(C/No) 00-99 dB-Hz, null when not tracking
 *  n ~ m. 2nd, 3rd SV
 *  m+1 ~ m+4. 4th SV
 *
 ******************************************************************************/

class CGsa : public CSentence {
	public:
		enum{
			MODE_NONE,
			MODE_MANUAL,
			MODE_AUTO,
			MAX_MODE
		};

		enum{
			MODE_FIX_NONE,
			MODE_FIX_NOT_AVAILABLE,
			MODE_FIX_2D,
			MODE_FIX_3D,
			MAX_MODE_FIX
		};
		
	public:
		CGsa();
		CGsa(char *pszSentence);
		~CGsa() {}
		
		void Parse();
		void SetSentence(char *pszSentence);
		void GetPlainText(char *pszPlainText);

		int GetMode()	{ return m_nMode; }
		int GetFixMode()	{ return m_nFixMode; }
		double GetPDOP()	{ return m_lfPDOP; }
		double GetHDOP()	{ return m_lfHDOP; }
		double GetVDOP()	{ return m_lfVDOP; }
		int GetInUseSatID(int nIdx);
		

	protected:
		int m_nMode;
		int m_nFixMode;
		double m_lfPDOP;
		double m_lfHDOP;
		double m_lfVDOP;
		int m_nInUseSatID[12];
};

#endif ///End of __NMEA_0183_GSA_H__
