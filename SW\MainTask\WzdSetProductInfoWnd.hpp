#ifndef __WIZARD_SET_PRODUCT_INFO_HPP__
#define __WIZARD_SET_PRODUCT_INFO_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "ComboCtrl.hpp"
#include "CheckCtrl.hpp"

class CWzdSetProductInfoWnd : public CWnd {

	protected:
		enum {
			FOCUS_SN = 0,
			FOCUS_COUNT
		};
		
		CEditCtrl  *m_pEditSN;
		CWnd       *m_pCtrls[FOCUS_COUNT];
		BOOL        m_bEditMode;
		BYTE        m_strSN[MAX_SERIAL_NUM_STR];

	public:
		CWzdSetProductInfoWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		virtual void OnKeyEvent(int nKey, DWORD nFlags);
		virtual void OnCursorEvent(int nState);
		virtual void OnActivate();
		void DrawFocusOutLine();
		void DrawControls();
		void DrawCaption();
		void DrawFuncBtn();
		virtual void DrawWnd(BOOL bRedraw = TRUE);

		void SetFocus(int nFocus);
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
		BOOL IsEditMode();
		void UpdateSerialNum();
		void SaveSerialNumber();

	private:
		void InitVariable();
		void InitControls(cSCREEN *pScreen);
		void InitWzdSetProductInfoWnd(cSCREEN *pScreen);
};

#endif	// End of __WIZARD_SET_PRODUCT_INFO_HPP__


