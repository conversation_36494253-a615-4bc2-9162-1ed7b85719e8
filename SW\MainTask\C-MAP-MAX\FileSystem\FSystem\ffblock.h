/*##################################################################
  FILE    : FFBLOCK.H

  USE     : virtual block functions header file.
  PROJECT : C-Map's File system

  AUTHOR  : MLP[24Aug95]
  UPDATED : SiS[030617
  ##################################################################


*/

#ifndef __FFBLOCK__
	#define __FFBLOCK__


/*****************************************************************************
  Interface Functions prototypes.
 *****************************************************************************/


SWord VerifyDeviceType	( struct DeviceNew *d, SWord InitDeviceFlag );
SWord VerifyDeviceFormat	( struct DeviceNew *d );
SWord GetFirstDirBlock	( struct DeviceNew *d, SWord *Block );
SWord GetLastDirBlock		( struct DeviceNew *d, SWord *Block );
SWord GetNextFatBlock		( struct DeviceNew *d, SWord *CurrentBlock, SWord *NextBlock );
SWord InvalidateFatBlock	( struct DeviceNew *d, SWord Block, unsigned char * Buffer );

SWord GetBlock			( FS_FILE *ff, SWord Block ) ;

SWord FreeBlock			( struct DeviceNew *d, SWord Block ) ;
SWord MarkFileBlocksAsDeleted(struct DeviceNew *d, SWord FirstBlock, SWord NumOfBlocks );
SWord NewBlock			( struct DeviceNew *d ) ;

SWord WriteBamBlock		( struct DeviceNew *d, SWord DirBlk );
SWord InitBamBlock		( struct DeviceNew *d );

#ifdef __cplusplus
extern "C"
{
#endif

extern	SWord LoadBamBlock( struct DeviceNew *d );

#ifdef __cplusplus
}
#endif

/*****************************************************************************
  END of Code.
 *****************************************************************************/


#endif /* #ifndef __FFBLOCK__ */

/*------------------------------------------------------------------------
 * $Log: /CplotTools/FileSystem/SourceFiles/H/FFBLOCK.H $
 * 
 * 2     23-09-99 13:19 Andrea
 * Moved the LOG to the end of the file.
 * 
 * 1     22-09-99 18:30 Andrea
 * 
 *-----------------------------------------------------------------------*/
