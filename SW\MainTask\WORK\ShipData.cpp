/*...........................................................................*/
/*.                  File Name : ShipData.cpp                               .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>


#include "DataType.h"
#include "_AllStringY.hpp"
#include "AllVarY.hpp"
#include "CpuAddr.h"
#include "SysConst.h"
#include "AllConst.h"
#include "KeyConst.h"
#include "SysLib.h"
#include "ComLib.h"
#include "GpsLib.h"
#include "GrLib.h"
#include "datum.hpp"
#include "KeyBD.hpp"
#include "DataBack.hpp"
#include "TargetPlotWnd.hpp"
#include "Uart.hpp"

#include "ShipData.hpp"

//===========================================================================
//extern cMainWin       *G_pMainWin;
//===========================================================================

//===========================================================================
#define  SHIP_IMAGE_SIZE_08x24_X                  24
#define  SHIP_IMAGE_SIZE_08x24_Y                  24

#define  SHIP_IMAGE_SIZE_10x32_X                  32
#define  SHIP_IMAGE_SIZE_10x32_Y                  32
//===========================================================================
#define  SHIP24_IMAGE_COUNT                      360
#define  SHIP32_IMAGE_COUNT                      360
//===========================================================================
#include "ShipImg24x24.h"
#include "ShipImg32x32.h"

//===========================================================================
static UCHAR G_vShipMarkDataUCHAR08x24[SHIP_IMAGE_SIZE_08x24_X * SHIP_IMAGE_SIZE_08x24_Y] = {
#ifdef  __PLASTIMO__
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,18,19,19,19,19,18,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,22,22,22,22,22,22,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,24,25,25,25,25,24,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,24,25,25,25,25,24,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,21,23,23,23,23,21,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#endif
#ifdef  __SAMYUNG__
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#endif
//---------------------------------------------------------------------------
static UCHAR G_vShipMarkDataUCHAR08x24RED[SHIP_IMAGE_SIZE_08x24_X * SHIP_IMAGE_SIZE_08x24_Y] = {
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0};
//---------------------------------------------------------------------------
static HWORD G_vShipMarkDataHWORD08x24RED[SHIP_IMAGE_SIZE_08x24_X * SHIP_IMAGE_SIZE_08x24_Y] = {
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000};
//===========================================================================
static UCHAR G_vShipMarkDataUCHAR10x32[SHIP_IMAGE_SIZE_10x32_X * SHIP_IMAGE_SIZE_10x32_Y] = {
#ifdef  __PLASTIMO__
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,18,19,19,19,19,19,19,18,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,18,19,19,19,19,19,19,18,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,22,22,22,22,22,22,22,22,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,24,25,25,25,25,25,25,24,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,25,26,26,26,26,26,26,25,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,24,25,25,25,25,25,25,24,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,21,23,23,23,23,23,23,21,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,16,16,16,16,16,16,16,16,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#endif
#ifdef  __SAMYUNG__
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,10,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,10,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,10,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,10,10,10,10,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,25,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,15,15,15,15,15,15,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
#endif
//---------------------------------------------------------------------------
static UCHAR G_vShipMarkDataUCHAR10x32RED[SHIP_IMAGE_SIZE_10x32_X * SHIP_IMAGE_SIZE_10x32_Y] = {
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,12,12,12,12,12,12,12,12,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
               0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,20,20,20,20,20,20,20,20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
//---------------------------------------------------------------------------
static HWORD G_vShipMarkDataHWORD10x32RED[SHIP_IMAGE_SIZE_10x32_X * SHIP_IMAGE_SIZE_10x32_Y] = {
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xffff,0xffff,0xffff,0xffff,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0xf800,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000};
//===========================================================================
#ifdef  __SAMYUNG__
static HWORD G_vShipMarkDataHWORD08x24ALL[SHIP_IMAGE_SIZE_08x24_X * SHIP_IMAGE_SIZE_08x24_Y] = {
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000};
//---------------------------------------------------------------------------
static HWORD G_vShipMarkDataHWORD10x32ALL[SHIP_IMAGE_SIZE_10x32_X * SHIP_IMAGE_SIZE_10x32_Y] = {
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0xf800,0xf800,0xf800,0xf800,0x57ea,0x57ea,0x57ea,0x57ea,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0xffea,0xffea,0xffea,0xffea,0xffea,0xffea,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,
              0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0821,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000};
#endif
//===========================================================================

extern cUART *G_pUart3;

/*********************************************************************************************************/
// Name		: cShipData
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
cShipData::cShipData(CWnd *pParent,cSCREEN *pScreen)
{
	int   nMaxWidth;
	DWORD dVirtScrnWidth,dVirtScrnHeight;

	dVirtScrnWidth = SysGetVirtScreenWidth();
	dVirtScrnHeight= SysGetVirtScreenHeight();

	if (dVirtScrnWidth >= dVirtScrnHeight)
		nMaxWidth = dVirtScrnWidth;
	else
		nMaxWidth = dVirtScrnHeight;

	m_pParent = pParent;
	m_pScreen = pScreen;

#if defined(__N430_MODEL__)
	if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
#else
	if (SysGetDeviceType() == DEVICE_TYPE_05_0)
#endif
	{
#if   SHIP_IMAGE_BIT_SIZE_USE == SHIP_IMAGE_BIT_SIZE_08
		m_pShipMarkFontX  = G_vShipMarkDataUCHAR08x24;
		m_pShipMarkFontY  = G_vShipMarkDataUCHAR08x24RED;
#else
		m_pShipMarkFontX  = (HWORD *)G_vShipMarkDataHWORD08x24;
		m_pShipMarkFontY  = (HWORD *)G_vShipMarkDataHWORD08x24RED;
#endif
		m_nShipMarkWidth  = SHIP_IMAGE_SIZE_08x24_X;
		m_nShipMarkHeight = SHIP_IMAGE_SIZE_08x24_Y;
	}
	else
	{
#if   SHIP_IMAGE_BIT_SIZE_USE == SHIP_IMAGE_BIT_SIZE_08
		m_pShipMarkFontX  = G_vShipMarkDataUCHAR10x32;
		m_pShipMarkFontY  = G_vShipMarkDataHCHAR10x32RED;
#else
		m_pShipMarkFontX  = (HWORD *)G_vShipMarkDataHWORD10x32;
		m_pShipMarkFontY  = (HWORD *)G_vShipMarkDataHWORD10x32RED;
#endif
		m_nShipMarkWidth  = SHIP_IMAGE_SIZE_10x32_X;
		m_nShipMarkHeight = SHIP_IMAGE_SIZE_10x32_Y;
	}

	m_pSaveMemMark = new CLRCHART[m_nShipMarkWidth * m_nShipMarkHeight + 16];
	m_pSaveMemPrjt = new CLRCHART[nMaxWidth * 4 * 4 + 128];
	m_pSaveMemCrcl = NULL;
	m_pSaveMemRing = new CLRCHART[nMaxWidth * 4 * 5 + 128];

	m_nShipMarkPrevX = 0;
	m_nShipMarkPrevY = 0;
	m_nShipMarkPrevC = 0;

	m_nRangeRingPrevX = 0;
	m_nRangeRingPrevY = 0;
	m_nRangeRingPrevR = 0;
	m_nRangeRingPrevC = 0;

	this->InitAllData();
}

/*********************************************************************************************************/
// Name		: ~cShipData
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
cShipData::~cShipData(void)
{
	delete [] m_pSaveMemMark;
	delete [] m_pSaveMemPrjt;
	delete [] m_pSaveMemRing;
}

/*********************************************************************************************************/
// Name		: InitAllData
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void  cShipData::InitAllData(void)
{
}

/*********************************************************************************************************/
// Name		: CalcCurrScrnPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void  cShipData::CalcCurrScrnPos(void)
{
	if (SysIsUsingMapCMAP())
	{
#if defined(__NAVIONICS__)
		((CTargetPlotWnd *)m_pParent)->CalcImgPosByLatLon(GetShipMercLon(),GetShipMercLat(),(int *)&m_xCurrVirtPos.nX,(int *)&m_xCurrVirtPos.nY);
		((CTargetPlotWnd *)m_pParent)->CalcScrPosByLatLon(GetShipMercLon(),GetShipMercLat(),(int *)&m_xCurrScrnPos.nX,(int *)&m_xCurrScrnPos.nY);
#else
		cmMerc2Screen(GetShipMercLon(),GetShipMercLat(),(SLong *)&m_xCurrVirtPos.nX,(SLong *)&m_xCurrVirtPos.nY);

		m_xCurrScrnPos.nX = cmgWinGetRealScrX(m_pParent->mClient.Left,m_xCurrVirtPos.nX);
		m_xCurrScrnPos.nY = cmgWinGetRealScrY(m_pParent->mClient.Bottom,m_xCurrVirtPos.nY);
#endif
	}
#ifdef  __SAMYUNG__
	else
	{
#if defined(__NEW_MERCATOR__)
		CalcWndwCoorByScrnOrigLong(LongLatToMRC32(GetShipGridLat()),LongLonToMRC32(GetShipGridLon()),(INT32 *)&m_xCurrScrnPos.nY,(INT32 *)&m_xCurrScrnPos.nX,1);
#else
		CalcWndwCoorByScrnOrigLong(GetShipGridLat(),GetShipGridLon(),(INT32 *)&m_xCurrScrnPos.nY,(INT32 *)&m_xCurrScrnPos.nX,1);
#endif
	}
#endif
}

/*********************************************************************************************************/
// Name		: GetCurrScrnX
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int cShipData::GetCurrScrnX(void)
{
	return(m_xCurrScrnPos.nX);
}

/*********************************************************************************************************/
// Name		: GetCurrScrnY
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int cShipData::GetCurrScrnY(void)
{
	return(m_xCurrScrnPos.nY);
}

/*********************************************************************************************************/
// Name		: GetVirtScrnX
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int cShipData::GetVirtScrnX(void)
{
	return(m_xCurrVirtPos.nX);
}

/*********************************************************************************************************/
// Name		: GetVirtScrnY
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int cShipData::GetVirtScrnY(void)
{
	return(m_xCurrVirtPos.nY);
}

/*********************************************************************************************************/
// Name		: SetPrevScrnPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::SetPrevScrnPos(void)
{
	m_xPrevScrnPos.nX = m_xCurrScrnPos.nX;
	m_xPrevScrnPos.nY = m_xCurrScrnPos.nY;
}

/*********************************************************************************************************/
// Name		: SetPrevScrnPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::SetPrevScrnPos(int nScrnX,int nScrnY)
{
	m_xPrevScrnPos.nX = nScrnX;
	m_xPrevScrnPos.nY = nScrnY;
}

/*********************************************************************************************************/
// Name		: GetCurrScrnPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::GetCurrScrnPos(int *pScrnX,int *pScrnY)
{
	*pScrnX = m_xCurrScrnPos.nX;
	*pScrnY = m_xCurrScrnPos.nY;
}

/*********************************************************************************************************/
// Name		: GetPrevScrnPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::GetPrevScrnPos(int *pScrnX,int *pScrnY)
{
	*pScrnX = m_xPrevScrnPos.nX;
	*pScrnY = m_xPrevScrnPos.nY;
}

/*********************************************************************************************************/
// Name		: SetPrjtStrtPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::SetPrjtStrtPos(int nScrnX,int nScrnY)
{
	m_xPrjtStrtPos.nX = nScrnX;
	m_xPrjtStrtPos.nY = nScrnY;
}

/*********************************************************************************************************/
// Name		: GetPrjtStrtPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void  cShipData::GetPrjtStrtPos(int *pScrnX,int *pScrnY)
{
	*pScrnX = m_xPrjtStrtPos.nX;
	*pScrnY = m_xPrjtStrtPos.nY;
}

/*********************************************************************************************************/
// Name		: SetPrjtLastPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void  cShipData::SetPrjtLastPos(int nScrnX,int nScrnY)
{
	m_xPrjtLastPos.nX = nScrnX;
	m_xPrjtLastPos.nY = nScrnY;
}

/*********************************************************************************************************/
// Name		: GetPrjtLastPos
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void  cShipData::GetPrjtLastPos(int *pScrnX,int *pScrnY)
{
	*pScrnX = m_xPrjtLastPos.nX;
	*pScrnY = m_xPrjtLastPos.nY;
}

/*********************************************************************************************************/
// Name		: DrawShipMark
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::DrawShipMark(int nEraseDraw)
{
	this->DrawShipMark(nEraseDraw,m_xCurrScrnPos.nX,m_xCurrScrnPos.nY);
}

/*********************************************************************************************************/
// Name		: DrawShipMark
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::DrawShipMark(int nEraseDraw,int nScrnX,int nScrnY)
{
#if   SHIP_IMAGE_BIT_SIZE_USE == SHIP_IMAGE_BIT_SIZE_08
	UCHAR  vShipImageData[8192];
#else
	HWORD  vShipImageData[8192];
#endif

	int    nCRS;
	int    nDEG;
	HWORD  *pImgDataP;
	CLRCHART *pMemTempX;
	CTargetPlotWnd *pChartMainWin = (CTargetPlotWnd *)m_pParent;

	((CTargetPlotWnd *)m_pParent)->SaveGrLibClipArea();

	if (nEraseDraw == DRAW_MODE_DRAW)
	{
		m_nShipMarkPrevX = nScrnX;
		m_nShipMarkPrevY = nScrnY;

		
#ifdef _SI_70AM_
		nCRS = pChartMainWin->GetRunCOGorHDG();
#else
//		nCRS = GetShipCogVal();
		nCRS = G_pMainWin->GetRunCOGorHDG();
#endif

//		G_pUart3->OutputDbgMsg("[cShipData::DrawShipMark]ScrnX=%d, ScrnY=%d, Crs=%d\r\n",nScrnX,nScrnY,nCRS);
		
		if (nCRS == COG_VAL_UNKNOWN)
			nCRS = 0;
#if   HDG_LINE_DRAW_MODE_RUN == HDG_LINE_DRAW_MODE_TRUE
		else
			nCRS = ConvertCrsToCrs(nCRS,GetCmpssUnitMode(),CMPS_UNIT_T);
#endif

		if (pChartMainWin->GetCursorMode() == MODE_VAL_ON)
			nCRS = CheckCompassValue(nCRS - pChartMainWin->GetPrevCrsUpVal());
		else
			nCRS = CheckCompassValue(nCRS - GetGlobalCrsUpDegree());

#if   SHIP_IMAGE_BIT_SIZE_USE == SHIP_IMAGE_BIT_SIZE_08
		//    #ifdef  __PLASTIMO__
		//        if (GetGpsFixStatus() == GPS_FIX_STATUS_LOST)
		//            GrRotateImageInUCHAR(m_pShipMarkFontY,m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,0);
		//        else
		//            GrRotateImageInUCHAR(m_pShipMarkFontX,m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,nCRS);
		//
		//        GrShipMarkSaveDrawInUCHAR(m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData ,nScrnX - m_nShipMarkWidth / 2,nScrnY - m_nShipMarkHeight / 2,m_pSaveMemMark);
		//        m_nShipMarkPrevC = nCRS;
		//    #endif
		//    #ifdef  __SAMYUNG__
		//    #endif
#else
#ifdef  __PLASTIMO__
		if (GetGpsFixStatus() == GPS_FIX_STATUS_LOST)
		{
			pImgDataP = m_pShipMarkFontY;
			nDEG = 0;
		}
		else
		{
			nDEG = (nCRS + 5) / 30;
			if ((nCRS % 30) >= 20)
				++nDEG;

			if (nDEG >= (360 / 3))
				nDEG = 0;

			pImgDataP = m_pShipMarkFontX + nDEG * m_nShipMarkWidth * m_nShipMarkHeight;
		}

		GrShipMarkSaveDrawInHWORD(m_nShipMarkWidth,m_nShipMarkHeight,pImgDataP ,nScrnX - m_nShipMarkWidth / 2,nScrnY - m_nShipMarkHeight / 2,m_pSaveMemMark);
		m_nShipMarkPrevC = nDEG;
#endif

#ifdef  __SAMYUNG__
#if defined(__N430_MODEL__)
		if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
#else
		if (SysGetDeviceType() == DEVICE_TYPE_05_0)
#endif
			pImgDataP = G_vShipMarkDataHWORD08x24ALL;
		else
			pImgDataP = G_vShipMarkDataHWORD10x32ALL;

		if (GetGpsFixStatus() == GPS_FIX_STATUS_LOST)
		{
			nDEG = 0;
			GrRotateImageInHWORD(m_pShipMarkFontY,m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,0,0x0000);
		}
		else
		{
			nDEG = nCRS;
			GrRotateImageInHWORD(pImgDataP       ,m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,nCRS,0x0000);
		}

		GrShipMarkSaveDrawInHWORD(m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData ,nScrnX - m_nShipMarkWidth / 2,nScrnY - m_nShipMarkHeight / 2,m_pSaveMemMark);
		m_nShipMarkPrevC = nDEG;
#endif
#endif
	}
	else
	{
		nScrnX = m_nShipMarkPrevX;
		nScrnY = m_nShipMarkPrevY;

#if   SHIP_IMAGE_BIT_SIZE_USE == SHIP_IMAGE_BIT_SIZE_08
		//    #ifdef  __PLASTIMO__
		//        GrRotateImageInUCHAR(m_pShipMarkFontX,m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,m_nShipMarkPrevC);
		//
		//        GrShipMarkRestDrawInUCHAR(m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,nScrnX - m_nShipMarkWidth / 2,nScrnY - m_nShipMarkHeight / 2,m_pSaveMemMark);
		//    #endif
		//    #ifdef  __SAMYUNG__
		//    #endif
#else
#ifdef  __PLASTIMO__
		pImgDataP = m_pShipMarkFontX + m_nShipMarkPrevC * m_nShipMarkWidth * m_nShipMarkHeight;

		GrShipMarkRestDrawInHWORD(m_nShipMarkWidth,m_nShipMarkHeight,pImgDataP,nScrnX - m_nShipMarkWidth / 2,nScrnY - m_nShipMarkHeight / 2,m_pSaveMemMark);
#endif

#ifdef  __SAMYUNG__
#if defined(__N430_MODEL__)
		if (SysGetDeviceType() == DEVICE_TYPE_05_0 || SysGetDeviceType() == DEVICE_TYPE_04_3)
#else
		if (SysGetDeviceType() == DEVICE_TYPE_05_0)
#endif
			pImgDataP = G_vShipMarkDataHWORD08x24ALL;
		else
			pImgDataP = G_vShipMarkDataHWORD10x32ALL;

		GrRotateImageInHWORD(pImgDataP  ,m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,m_nShipMarkPrevC,0x0000);

		GrShipMarkRestDrawInHWORD(m_nShipMarkWidth,m_nShipMarkHeight,vShipImageData,nScrnX - m_nShipMarkWidth / 2,nScrnY - m_nShipMarkHeight / 2,m_pSaveMemMark);
#endif
#endif
	}

	((CTargetPlotWnd *)m_pParent)->RestGrLibClipArea();
}

/*********************************************************************************************************/
// Name		: DrawShipPrjt
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::DrawShipPrjt(int nEraseDraw,int nStartScrnX,int nStartScrnY,int nLastScrnX,int nLastScrnY)
{
	int   nPrjtLineThick= 2;
	int   nCircleRadius = 5;

	((CTargetPlotWnd *)m_pParent)->SaveGrLibClipArea();

	if (nEraseDraw == DRAW_MODE_DRAW)
	{
		m_xPrjtStrtPos.nX = nStartScrnX;
		m_xPrjtStrtPos.nY = nStartScrnY;

		m_xPrjtLastPos.nX = nLastScrnX;
		m_xPrjtLastPos.nY = nLastScrnY;

		if (GrClippingScreenLong(&nStartScrnX,&nStartScrnY,&nLastScrnX,&nLastScrnY))
		{
			if (GetGpsFixStatus() != GPS_FIX_STATUS_LOST)
			{
				m_pSaveMemCrcl = GrSaveThickLine(nStartScrnX,nStartScrnY,nLastScrnX,nLastScrnY,CHART_CLR_ID_LIGHT_RED,nPrjtLineThick,m_pSaveMemPrjt);
				GrSaveCircle(m_xPrjtLastPos.nX,m_xPrjtLastPos.nY,nCircleRadius,CHART_CLR_ID_LIGHT_RED,m_pSaveMemCrcl);
			}
		}
	}
	else
	{
		nStartScrnX = m_xPrjtStrtPos.nX;
		nStartScrnY = m_xPrjtStrtPos.nY;

		nLastScrnX = m_xPrjtLastPos.nX;
		nLastScrnY = m_xPrjtLastPos.nY;

		if (GrClippingScreenLong(&nStartScrnX,&nStartScrnY,&nLastScrnX,&nLastScrnY))
		{
			if (m_pSaveMemCrcl != NULL)
			{
				GrRestCircle(m_xPrjtLastPos.nX,m_xPrjtLastPos.nY,nCircleRadius,m_pSaveMemCrcl);
				GrRestThickLine(nStartScrnX,nStartScrnY,nLastScrnX,nLastScrnY,nPrjtLineThick,m_pSaveMemPrjt);
			}
		}
	}

	((CTargetPlotWnd *)m_pParent)->RestGrLibClipArea();
}

/*********************************************************************************************************/
// Name		: DrawRangeRing
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void  cShipData::DrawRangeRing(int nEraseDraw,int nRadius,int nRings)
{
      this->DrawRangeRing(nEraseDraw,m_xCurrScrnPos.nX,m_xCurrScrnPos.nY,nRadius,nRings);
}

/*********************************************************************************************************/
// Name		: DrawRangeRing
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void cShipData::DrawRangeRing(int nEraseDraw,int nScrnX,int nScrnY,int nRadius,int nRings)
{
	int  nNextRadius;
	CLRCHART *pSaveMemRing;

	((CTargetPlotWnd *)m_pParent)->SaveGrLibClipArea();

	if (nEraseDraw == DRAW_MODE_DRAW)
	{
		m_nRangeRingPrevX = nScrnX;
		m_nRangeRingPrevY = nScrnY;
		m_nRangeRingPrevR = nRadius;
		m_nRangeRingPrevC = nRings; 
	}
	if (nEraseDraw == DRAW_MODE_ERASE)
	{
		nScrnX  = m_nRangeRingPrevX;
		nScrnY  = m_nRangeRingPrevY;
		nRadius = m_nRangeRingPrevR;
		nRings  = m_nRangeRingPrevC;
	}

	nNextRadius = nRadius;
	pSaveMemRing= m_pSaveMemRing;

	while (nRings-- > 0)
	{
		if (nEraseDraw == DRAW_MODE_DRAW)
			pSaveMemRing = GrSaveCircle(nScrnX,nScrnY,nNextRadius,CHART_CLR_ID_BLACK,pSaveMemRing);
		else
			pSaveMemRing = GrRestCircle(nScrnX,nScrnY,nNextRadius,pSaveMemRing);

		nNextRadius += nRadius;
	}

	((CTargetPlotWnd *)m_pParent)->RestGrLibClipArea();
}

