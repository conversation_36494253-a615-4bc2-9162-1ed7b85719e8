/*...........................................................................*/
/*.                  File Name : link.xn                                    .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

OUTPUT_FORMAT("elf32-littlearm", "elf32-littlearm", "elf32-littlearm")
/*OUTPUT_FORMAT("elf32-arm", "elf32-arm", "elf32-arm")*/
OUTPUT_ARCH(arm)
ENTRY(_start)
__DYNAMIC  =  0;

SECTIONS
{
	. = 0x82000000;
	__begin_of_text__ = .;

	startup : { *(.startup)}

	prog :
	{
		*(.text)
    *(.stub)
    *(.text.*)
		*(.rodata)
		*(.rodata*)
    *(.gnu.warning)
    *(.gnu.linkonce.r*)
    *(.gnu.linkonce.t*)
    *(.glue_7t) *(.glue_7)
	}
  .rel.dyn :
  {
    *(.rel.init)
    *(.rel.text .rel.text.* .rel.gnu.linkonce.t.*)
    *(.rel.fini)
    *(.rel.rodata .rel.rodata.* .rel.gnu.linkonce.r.*)
    *(.rel.data .rel.data.* .rel.gnu.linkonce.d.*)
    *(.rel.tdata .rel.tdata.* .rel.gnu.linkonce.td.*)
    *(.rel.tbss .rel.tbss.* .rel.gnu.linkonce.tb.*)
    *(.rel.ctors)
    *(.rel.dtors)
    *(.rel.got)
    *(.rel.bss .rel.bss.* .rel.gnu.linkonce.b.*)
  }

	__end_of_text__ = .;

	.data :
	{
		__data_beg__ = .;
		__data_beg_src__ = __end_of_text__;
		*(.data)
		*(.data.rel*)
    *(.got*)
    *(.gcc_except_table*)
    *(.gnu.linkonce.d*)
    *(.rel.dyn)
		__IRQ_HANDLER_VECT = .;
		__data_end__ = .;
	}

	.bss :
	{
		__bss_beg__ = .;
		*(.bss)
	}

	/* Align here to ensure that the .bss section occupies space up to
	 * _end.  Align after .bss to ensure correct alignment even if the
	 * .bss section disappears because there are no input sections.
  */

	. = ALIGN(32 / 8);
	_end = .;
	_bss_end__ = . ; __bss_end__ = . ; __end__ = . ;
	PROVIDE (end = .);

  /*
   * Allocate room for heap
   */
  .   =  ALIGN(16);
  __heap_begin__ = .;
  .  += 0x00040000;             /* HEAP_SIZE =  256 KB */
  __heap_end__  = . - 16;

  /*
   * Allocate room for stack
   */
  .   =  ALIGN(16);
  .  +=  0x00001000;
  .  += 0x00040000;             /* STACK_SIZE = 256 KB */
  __StackPointer__ = . - 16;
  . = ALIGN(16);
  __StartOfFreeRam__ = .;       /* Start of free ram */
}

