/*  XTide  Harmonic tide clock and tide predictor station index builder.
    Last modified 2002-02-06

    XTide is:
    Copyright (C) 1997  <PERSON>.
    This auxilliary program written by <PERSON>.

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

    Purpose:
    This program generates a station index file for use with the WXTide32
    station locator and station map.
*/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctype.h>
#include <math.h>
#include "tcd.h"

/* Global Variables */

#define FALSE 0
#define TRUE !FALSE

#define ControlName "tide2idx.ctl"
#define LONSTR  "# !longitude:"
#define LATSTR  "# !latitude:"

#define MAXNAME 90

FILE *InputFile, *OutputFile;
char InputName[MAXNAME],InputLine[1024], OutputName[MAXNAME], OutputLine[256];
char ch, sort_ID[40];
int skipcount, copyheader, skip2header, clean_index_file=FALSE, display_fixup=FALSE, no_dupes=0, line_num;
double Lon, Lat, refLat, refLon, too_far_NM = 200.0;
int is_subreg_file, is_harmonic_file, is_tcd_file, harmonic_file_num;
int long_state_found, country_found, other_found, region_found, short_state_found;

char *tznames[][2] = {
#include "tzdata.h"
/* Terminator */
{NULL, NULL}};


/* -----------------10/11/97 2:31PM------------------
  Strings in harmonic files to ignore when searching for Country/State names.
 --------------------------------------------------*/
char ignore_texts[][20]= {
   LONSTR,
   LATSTR,
   ""
};

/* Control file structures */

typedef struct {
   char *short_name;
   char *long_name;
} ref_station_entry;

static ref_station_entry **ref_station_list;

#define STATE   1
#define COUNTRY 2
#define REGION  4
#define OTHER   8

typedef struct {
   char *state;
   char *country;
   char *region;
} SCR_entry;

static SCR_entry **state_country_region;

typedef struct {
   char *long_name;
   char *abbreviation;
   int   type;
} abbreviation_entry;

static abbreviation_entry **abbreviation_list;

typedef struct {
   char *sort_ID;
   char  *region_NE;
   char  *region_SW;
   float *lon;
   float *lat;
} region_fixup_entry;

static region_fixup_entry **region_fixup_list;

#define TIMEZONE 1
#define LONLAT 2

typedef struct {
   int  type;
   char *id;
   char *old_lon;
   char *new_lat;
   char *name;
} Auto_fixup_entry;

static Auto_fixup_entry **Auto_fixup_list;

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   strip leading blanks and trailing blanks and controls from a string.

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
void strip_blanks(char *string) {
int i, j;
  while (string[0] == ' ') memmove(string, string+1, strlen(string));
  for (i=(strlen(string)-1); (i>=0) && (string[i] <= ' ') && (string[i] >= 0); i--)
      string[i] = '\0';
//for (i=0; i<strlen(string); i++)
//   if (string[i] & 0x80) {
//      j = string[i];
//      err_msg("Char:0x%2x",j);
//   }
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Error mesage handler

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
//int printf(const char *fstring, ...)
void err_msg(const char *fstring, ...)
/*const char *fstring;*/
{
char szOutput[1024];
static char oldout[1024];

   va_list marker;
   va_start(marker,fstring);
   vsprintf(szOutput,fstring,marker);
   strip_blanks(szOutput);
   if (strcmp(szOutput, oldout)) {
     printf("%s\n", szOutput);
     strcpy(oldout, szOutput);
   }
   if (!clean_index_file)
      fprintf(OutputFile,"#%s\n", szOutput);
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Find region/country/state

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int find_state_country_region(char *text, int type) {
int i;

   i = 0;
   do {
      if (((type==STATE)   && (!strcmp(state_country_region[i]->state,  text)))
        ||((type==COUNTRY) && (!strcmp(state_country_region[i]->country,text)))
        ||((type==REGION)  && (!strcmp(state_country_region[i]->region, text))))
         return(i);
   } while (state_country_region[++i]->region != NULL);
   return(-1);
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Check for line containing LAT/LON

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
void check_4_lat_lon() {
   if (!strncmp(LONSTR, InputLine, strlen(LONSTR)))
      sscanf(InputLine, LONSTR" %lf",&Lon);
   if (!strncmp(LATSTR, InputLine, strlen(LATSTR)))
      sscanf(InputLine, LATSTR" %lf",&Lat);
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Check for one string (sloppily) containing another
   This uses the unique (to WATCOM) "strnicmp" which compares 2 strings
   for a fixed length with case insensitivity.  I do this for speed since
   this routine is called over and over and over and over and over again.

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int check_4_string(char *long_line, char *short_line, int check_delimited) {
int j,k,l;
char delim1, delim2;

   l = strlen( short_line );
   k = strlen( long_line ) - l + 1;
   for (j=0; j<k; j++)
      if (!strnicmp(short_line, long_line+j, l)) {
         if (!check_delimited) return(j);
         else {
            if (j==0) delim1 = ' ';
            else      delim1 = long_line[j-1];
            delim2 = long_line[j+strlen(short_line)];
            if (!isalpha(delim1) && (!isalpha(delim2)) &&
               (delim1 != '/') && (delim2 != '/') )
               return(j);
         }
      }
   return(-1);
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Check line for entry abbreviation list.

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int check_4_name(char *line, int type_flag, int long_flag) {
char *t;
int i, found, foundl;
   i = foundl = 0;
   found = -1;
   do {
      if (abbreviation_list[i]->type & type_flag) {
         if (long_flag)
              t = abbreviation_list[i]->long_name;
         else t = abbreviation_list[i]->abbreviation;
//         found = (check_4_string(line, t, TRUE) >= 0) ;
         if (check_4_string(line, t, TRUE) >= 0) {
            if (strlen(t) > foundl) { // Find LONGEST matching name
               found = i;
               foundl= strlen(t);
            }
         }
      }
   }
//   while ((abbreviation_list[++i]->long_name != NULL) && !found);
   while (abbreviation_list[++i]->long_name != NULL);
   if (found!=-1) return(found+1);
   else           return(0);
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Check for line containing country or state name

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
void check_4_country_state(char *line) {
int found;
   found = check_4_name(line, OTHER, TRUE); // Look for specials
   if (found && (strcmp("IGNORE",abbreviation_list[found-1]->abbreviation) &&
                 strcmp("SKIP",  abbreviation_list[found-1]->abbreviation))) {
      other_found = found;
      country_found = long_state_found = region_found = short_state_found =0;
   }
   else if (!found || (found && !strcmp("SKIP",abbreviation_list[found-1]->abbreviation))) {
      found = check_4_name(line,COUNTRY,TRUE);
      if (found && (!country_found || strlen(abbreviation_list[        found-1]->long_name) >=
                                      strlen(abbreviation_list[country_found-1]->long_name)))
         country_found = found; // Set index for LONGEST matched name

      found = check_4_name(line,STATE,TRUE);
      if (found && (!long_state_found || strlen(abbreviation_list[        found-1]->long_name) >
                                      strlen(abbreviation_list[long_state_found-1]->long_name)))
         long_state_found = found; // Set index for LONGEST matched name

      found = check_4_name(line,STATE,FALSE);
      if (found && (!short_state_found || strlen(abbreviation_list[        found-1]->long_name) >
                                      strlen(abbreviation_list[short_state_found-1]->long_name)))
         short_state_found = found; // Set index for LONGEST matched name

      found = check_4_name(line,REGION,TRUE);
      if (found && (!region_found || strlen(abbreviation_list[       found-1]->long_name) >
                                     strlen(abbreviation_list[region_found-1]->long_name)))
         region_found = found; // Set index for LONGEST matched name
   }
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Fixup sort-ID

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
void fixup_sort_ID(double lon, double lat) {
int i,col, ll, fixed;
float dlat, dlon, minlat, minlon;
region_fixup_entry *rf;

   i = 0;
   while (((rf=region_fixup_list[i++])!=NULL) && (rf->sort_ID != NULL)) {
      if (strlen(sort_ID) == strlen(rf->sort_ID)) {
         for (col=0; (col<strlen(sort_ID) &&
            ((rf->sort_ID[col] == '?') ||
             (rf->sort_ID[col] == sort_ID[col]))); col++);
         if (col == strlen(sort_ID)) { // Not this entry
//if (!strcmp(sort_ID, "CAP:Pan::"))
//err_msg("sort_ID=%s, rf->sort_ID=%s, lat=%.2f, rf->lat[1]=%.2f, rf->lat[0]=%.2f\n",
//sort_ID, rf->sort_ID,lat, rf->lat[0], rf->lat[1]);
// We found an entry to fix, check for E/W first
            fixed = 0;
            for (ll=1; (ll<5) && (rf->lon[ll] < 500.0); ll++)
               if (((rf->lat[ll-1] <= lat) && (rf->lat[ll] >= lat)) ||
                   ((rf->lat[ll-1] >= lat) && (rf->lat[ll] <= lat))) {
// We have two points above/below target point, find E/W fix
                  dlat = rf->lat[ll] - rf->lat[ll-1];
                  if (dlat == 0.0)
                       dlon = rf->lon[ll-1];
                  else dlon = rf->lon[ll-1] + (((lat - rf->lat[ll-1]) / dlat) *
                     (rf->lon[ll] - rf->lon[ll-1]));
if (display_fixup)
err_msg("Info: E/W Region fixup for %s", sort_ID);
                  if (lon > dlon)
                       strcpy(sort_ID, rf->region_NE);
                  else strcpy(sort_ID, rf->region_SW);
                  fixed = 1;
//                  return;
                  }
            if (fixed) return;
// Didn't find E/W, try for N/S
            for (ll=1; (ll<5) && (rf->lon[ll] < 500.0); ll++)
               if (((rf->lon[ll-1] <= lon) && (rf->lon[ll] >= lon)) ||
                  ((rf->lon[ll-1] >= lon) && (rf->lon[ll] <= lon))) {
// We have two points left/right of target point, find N/S fix
                  dlon = rf->lon[ll] - rf->lon[ll-1];
                  if (dlon == 0.0)
                       dlat = rf->lat[ll-1];
                  else dlat = rf->lat[ll-1] + (((lon - rf->lon[ll-1]) / dlon) *
                     (rf->lat[ll] - rf->lat[ll-1]));
if (display_fixup)
err_msg("Info: N/S Region fixup for %s", sort_ID);
                  if (lat > dlat)
                       strcpy(sort_ID, rf->region_NE);
                  else strcpy(sort_ID, rf->region_SW);
                  return;
                  }
// Didn't find anything completely defined, do min/max checks
            minlon = minlat = 180;
            for (ll=0; (ll<5) && (rf->lon[ll] < 500.0); ll++) {
               if (rf->lon[ll] < minlon) minlon = rf->lon[ll];
               if (rf->lat[ll] < minlat) minlat = rf->lat[ll];
            }
            if ((lat < minlat) || (lon > minlon))
                 strcpy(sort_ID, rf->region_SW);
            else strcpy(sort_ID, rf->region_NE);
if (display_fixup)
err_msg("Info: Default region fixup for %s", sort_ID);
            return;
         }
      }
   }
   return;
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Build region:country:state sort identifier.

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int build_sort_ID(char *station_name) {
int s,c,r, nReturn, other_type;
char *other, *region, *country, *state;

   region = state = country = "";
   nReturn = 2;
   if (other_found) {
      other      = abbreviation_list[other_found-1]->abbreviation;
//      other_type = abbreviation_list[other_found-1]->type;
      if ((s = find_state_country_region(other,STATE)) >= 0) {
         long_state_found = other_found;
         country_found = 0;
      }
      else if ((c = find_state_country_region(other,COUNTRY)) >= 0) {
         country_found = other_found;
         long_state_found = 0;
      }
      else {
         region_found = other_found;
         long_state_found = country_found = 0;
      }
   }

   if (long_state_found) {
      s = find_state_country_region(abbreviation_list[long_state_found-1]->abbreviation,STATE);
      if (s >= 0) {
         if (country_found) {
            c = find_state_country_region(abbreviation_list[country_found-1]->abbreviation,COUNTRY);
            if (c < 0) {
               c = s;
               nReturn = 4;
            }
         }
         else c = s;

         if (strcmp(state_country_region[s]->country, state_country_region[c]->country)==0)
              nReturn = 0;              // State matches country found
         else nReturn = 1;              // State didn't match country found

         region  = state_country_region[s]->region;
         state   = state_country_region[s]->state;
         country = state_country_region[s]->country;
      }
      else nReturn = 4;
   }

   else if (short_state_found) {
      s = find_state_country_region(abbreviation_list[short_state_found-1]->abbreviation,STATE);
      if (s >= 0) {
         if (country_found) {
            c = find_state_country_region(abbreviation_list[country_found-1]->abbreviation,COUNTRY);
            if (c < 0) {
               c = s;
               nReturn = 4;
            }
         }
         else c = s;

         if (strcmp(state_country_region[s]->country, state_country_region[c]->country)==0) {
            region  = state_country_region[s]->region;
            state   = state_country_region[s]->state;
            country = state_country_region[s]->country;
            nReturn = 0;
         }
         else {                         // State:country didn't match country found
            region  = state_country_region[c]->region;
            country = state_country_region[c]->country;
            state = "";
//            nReturn = 1;
            nReturn = 0;
         }
      }
      else nReturn = 4;
   }

   else if (country_found) {
      c = find_state_country_region(abbreviation_list[country_found-1]->abbreviation,COUNTRY);
      if (c >= 0) {
         region  = state_country_region[c]->region;
         country = state_country_region[c]->country;
         state = "";
         nReturn = 0;
      }
      else nReturn = 4;
   }

   else if (region_found) {
      region  = abbreviation_list[region_found-1]->abbreviation;
      country = "";
      state   = "";
      nReturn = 0;
   }

   if ((strcmp(country,"US")==0) && (state==""))
       nReturn = 3;

   sprintf( sort_ID, "%s:%s:%s:", region, country, state);
   switch ( nReturn ) {
      case 1:
         err_msg("Warning: Country/State mismatched for %s %s", sort_ID, station_name);
         break;

      case 2:
         err_msg("Warning: No country or state for %s", station_name);
         break;

      case 3:
         err_msg("ERROR: US state name missing for %s %s", sort_ID, station_name);
         break;

      case 4:
         err_msg("ERROR: Abbreviation mixup in control file for %s %s", sort_ID, station_name);
         break;
   }

   return( nReturn );
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   find reference station

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
char *find_refsta( char *subRef , int direct) {
static char *refName, lineName[MAXNAME], entry_type, line_ID[40], lineTZ[40];
int i, found, h_num;
double lineLat, lineLon;
static char lastRef[80];

   if (direct) {
     refName = subRef;
     harmonic_file_num = 0;
     return( refName );
   }
   else {
     while (refName=strchr(subRef,' ')) // Get rid of embedded blanks in name
        strcpy(refName, refName+1);

     refName = "";
     i=0;
     do
        if (!strcmpi(ref_station_list[i]->short_name,subRef)) {
           refName = ref_station_list[i]->long_name;
           break;
        }
     while (ref_station_list[++i]->short_name != NULL);
   }

   if (refName == "") {
      if (strcmpi(lastRef,subRef)) {  // Only output name first time encountered
         err_msg("ERROR: No cross-index for Reference Station %s",subRef);
         strcpy(lastRef, subRef);
      }
   }
   else if (strcmp(lastRef, refName)) {
      fflush ( OutputFile );
      fseek  ( OutputFile, 0L, SEEK_SET );  // Rewind to start of file
      h_num = 0;
      found = FALSE;
      while ((fgets( InputLine, 1024, OutputFile) != NULL) && !found) {
         if (InputLine[0] == 'H' || InputLine[0] == 'B') h_num++;
         else if ((InputLine[0] == 'T') || (InputLine[0] == 'C')) {
            if (6==sscanf(InputLine, "%c%s%lf%lf%s%[^\n]", &entry_type, &line_ID, &lineLon, &lineLat, &lineTZ, lineName)) {
              while (lineName[0] == ' ') memmove(lineName,lineName+1,strlen(lineName));
              found = (!strcmpi(lineName, refName));
            }
         }
      }
      fseek( OutputFile, 0L, SEEK_END );
      if (found) {
         strcpy(sort_ID, line_ID);
         refLat = lineLat;
         refLon = lineLon;
         harmonic_file_num = h_num;
         strcpy(lastRef, refName);// Save this name for next go-round
      }
      else {
         err_msg("ERROR: Could not locate reference station %s", refName);
         refName = "";
      }
   }
   return( refName );
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   find if station is duplicate
   0 = unique name and location or duplicate location and name is (2), (3) . . .
   1 = duplicate location and station name

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int is_duplicate( char *staName , double lat, double lon, char type) {
static char lineName[MAXNAME], entry_type, line_ID[40], lineTZ[40];
int    i, found;
double lineLat, lineLon;
char *p1, *p2, *p3;

  if (!no_dupes) return 0;
  fflush ( OutputFile );
  fseek  ( OutputFile, 0L, SEEK_SET );  // Rewind to start of file
  found = FALSE;
  while ((fgets( InputLine, 1024, OutputFile) != NULL) && !found) {
    if (strchr("TtCc",InputLine[0])) {
      if (6==sscanf(InputLine, "%c%s%lf%lf%s%[^\n]", &entry_type, &line_ID, &lineLon, &lineLat, &lineTZ, lineName)) {
        if (fabs(lat-lineLat) < 0.0002 && fabs(lon-lineLon) < 0.0002 && !((type ^ entry_type)&0xdf)) {
          p1 = lineName;
          p2 = staName;
          while (*p1==' ') p1++;        // Skip leading blanks
          p3 = p1;
          while (*(++p1) == *(++p2));   // Compare the strings
          if (*(p1-1)==0 && *(p2-1)==0) found = 1;  // Exact match
          else if (*p1==0 && *(p2+1) == '(');  // Allow (2) etc
          else if (*p1==0 && *(p2+0) == '(');  // Allow (2) etc
          else if (*p2==0 && *(p1+1) == '(');  // Allow (2) etc
          else if (*p2==0 && *(p1+0) == '(');  // Allow (2) etc
          else if (*(p2-1)=='(' && isdigit(*p2));                  // Allow (2) etc
          else {
            err_msg("Warning: =location, =type, !=name [%s], [%s]", p3, staName);
            if (no_dupes==2 &&
               ((entry_type=='T' || entry_type=='C')) ||           // Original was ref, so skip this one
               ((entry_type=='t' || entry_type=='c') && strchr("TC",type)))   // Original was sub and this is sub, so skip
                found = 1;      // Even with different name, skip this location
          }
        }
      }
    }
  }
  fseek( OutputFile, 0L, SEEK_END );

  return( found );
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   return minimal floating point string

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
void minimize_float_s(char *str) {
   while (str[strlen(str)-1] == '0') str[strlen(str)-1] = '\0'; // Strip trailing 0's
   if    (str[strlen(str)-1] == '.') str[strlen(str)-1] = '\0'; // Strip trailing blnk
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   return timezone string

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
void format_TZ(char *dst, int TZHr, int TZMin, char *id, char *name) {
int i, tHr = TZHr, tMin = TZMin;

   if (TZHr < 0 || TZMin < 0)
        sprintf(dst, "-%d:%02d", abs(TZHr), abs(TZMin));
   else sprintf(dst,  "%d:%02d",     TZHr,      TZMin );

   for (i=0; Auto_fixup_list[i]->type; i++) {
      if ((Auto_fixup_list[i]->type == TIMEZONE) &&
          !strcmp(dst, Auto_fixup_list[i]->old_lon) &&
           strstr(id,  Auto_fixup_list[i]->id) ) {
         err_msg("Warning: Auto-correct timezone from %s to %s for %s %s",
             dst, Auto_fixup_list[i]->new_lat, id, name);
         strcpy(dst, Auto_fixup_list[i]->new_lat);
         sscanf(dst, "%d:%d", &tHr, &tMin);
         break;
      }
   }

   if ((abs(tHr) > 13) || (tMin % 15))
      err_msg("ERROR: Bad timezone (%s) for %s %s", dst, id, name);
   if (tHr < 0 || tMin < 0)
        sprintf(dst, "-%d:%d", abs(tHr), abs(tMin));
   else sprintf(dst,  "%d:%d",     tHr,      tMin );
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   format lon/lat strings

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int format_lat_lon(char *sLat,char *sLon,double *Lat,double *Lon,char *id,char *name){
int i;
   sprintf(sLat, "%.4lf", *Lat);
   sprintf(sLon, "%.4lf", *Lon);
   for (i=0; Auto_fixup_list[i]->type; i++) {
      if ((Auto_fixup_list[i]->type == LONLAT) &&
          !strcmp(id,   Auto_fixup_list[i]->id) &&
          !strcmp(name, Auto_fixup_list[i]->name) ) {
         err_msg("Warning: Auto-correct Lon/Lat from (%sE,%sN) to (%sE,%sN) for %s %s",
             sLon,sLat, Auto_fixup_list[i]->old_lon, Auto_fixup_list[i]->new_lat, id, name);
         strcpy(sLon, Auto_fixup_list[i]->old_lon);
         strcpy(sLat, Auto_fixup_list[i]->new_lat);
         sscanf(sLon, "%lf", Lon);
         sscanf(sLat, "%lf", Lat);
         break;
      }
   }
   if (fabs(*Lon) > 180.0 && fabs(*Lon) < 190.0) {
      double old_lon = *Lon;
      if (*Lon > 180.0) *Lon -= 360.0;
      else              *Lon += 360.0;
      err_msg("Warning: Auto-correct Lon from %.4lfE to %.4lfE for %s %s", old_lon, *Lon, id, name);
   }

   sprintf(sLat, "%.4lf", *Lat);
   minimize_float_s(sLat);
   sprintf(sLon, "%.4lf", *Lon);
   minimize_float_s(sLon);

   if ((*Lat == 0.0) || (*Lon == 0.0) || (*Lat > 360.0) || (*Lon > 360.0) ||
         (fabs(*Lat) > 90.0) || (fabs(*Lon) > 180.0)) {
      err_msg("ERROR: Lat/Lon missing or bad (%.4lfN,%.4lfE) for %s %s", *Lat, *Lon, id, name);
      return(1);
   }
   return(0);
}

/*-----------------6/26/2005 10:13AM----------------
 * process tcd file
 * --------------------------------------------------*/
int process_tcd_file(char * fn) {
static char current_ch, station_name[MAXNAME], time_zone[80], sLon[40], sLat[40], tzname[80], sHTmpy[30], sLTmpy[30];
char   sHToff[30], sLToff[30], sHTTime[30], sLTTime[30], *refName, *level_units;
double Lat, Lon, dLat, dLon, delNM;
int    TZHr, TZMin, ignore, i, isCurrent;
int    recnum=0, subID=0;
int    subHTLevM,subHTLevA,subLTLevM,subLTLevA,subTZ,subNotes;
int    subHTTimH,subHTTimM,subLTTimH,subLTTimM;
TIDE_RECORD tr, rr;

  if (open_tide_db(fn)) {
    while (read_tide_record(recnum++, &tr) >= 0) {
      level_units = get_level_units(tr.level_units);
      isCurrent = NULL != strstr(level_units, "nots");
//      printf("%d, %s %s\n", (int)tr.header.record_type, level_units, tr.header.name);

      if (tr.header.record_type == 1) {
// Header record
        strcpy(station_name, tr.header.name);
        check_4_country_state(station_name);
        build_sort_ID(station_name);

        TZHr  = tr.zone_offset / 100;
        TZMin = tr.zone_offset % 100;
        if (tr.zone_offset < 0 && TZHr == 0) TZMin *= -1;
        format_TZ(time_zone, TZHr, TZMin, sort_ID, station_name);

        strcpy(tzname, get_tzfile(tr.header.tzfile));
        if (!strcmp("Unknown", tzname)) err_msg("Error: timezone name not found %s", tzname);

        Lat = tr.header.latitude;
        Lon = tr.header.longitude;
        format_lat_lon(sLat, sLon, &Lat, &Lon, sort_ID, station_name);
        fixup_sort_ID( Lon, Lat );

        current_ch = isCurrent? 'C' : 'T';
        if (is_duplicate( station_name , Lat, Lon, current_ch))
          err_msg("Warning1: Station %s is duplicate and skipped", station_name);
        else
          fprintf( OutputFile,"%c%s %s %s %s %s\n",
                   current_ch, sort_ID, sLon, sLat, time_zone, station_name);
      }

      else if (tr.header.record_type == 2) {
        ignore = FALSE;
// Secondary station record
        if (read_tide_record(tr.header.reference_station, &rr) >= 0) {
          if ((refName=find_refsta(rr.header.name, TRUE)) == "")
             return( FALSE );
          strcpy(station_name, tr.header.name);
          check_4_country_state(station_name);
          if (!country_found && !long_state_found && !other_found)
            check_4_country_state(rr.header.name);
          build_sort_ID(station_name);

          TZHr  = 0;
          TZMin = 0;
          format_TZ(time_zone, TZHr, TZMin, sort_ID, station_name);

          strcpy(tzname, get_tzfile(tr.header.tzfile));
          if (!strcmp("Unknown", tzname)) err_msg("Error: timezone name not found %s", tzname);

          Lat = tr.header.latitude;
          Lon = tr.header.longitude;
          format_lat_lon(sLat, sLon, &Lat, &Lon, sort_ID, station_name);
          fixup_sort_ID( Lon, Lat );

          current_ch = isCurrent? 'c' : 't';
          subLTLevA = tr.min_level_add;
          subHTLevA = tr.max_level_add;
          if (strstr(level_units, "eter")) {  // Units are meters so convert to feet
            float m2ft = 0.3048;
            subLTLevA *= m2ft;
            subHTLevA *= m2ft;
          }

          subLTLevM = tr.min_level_multiply*100;
          subHTLevM = tr.max_level_multiply*100;
          if (subLTLevM==0) subLTLevM = 100;;
          if (subHTLevM==0) subHTLevM = 100;;

          subLTTimH = tr.min_time_add / 100;
          subLTTimM = tr.min_time_add % 100;
          if (tr.min_time_add < 0 && subLTTimH == 0) subLTTimM *= -1;
          subHTTimH = tr.max_time_add / 100;
          subHTTimM = tr.max_time_add % 100;
          if (tr.min_time_add < 0 && subHTTimH == 0) subHTTimM *= -1;

// Any multiplier < 0.05 or > 5.0 MUST be in error
          if ((subLTLevM < 5) || (subLTLevM > 500) ||
              (subHTLevM < 5) || (subHTLevM > 500)) {
              err_msg("ERROR: Tide multipliers bad (H*%.4f,L*%.4f), ignoring %s %s",
                       (float)subHTLevM/100.0, (float)subLTLevM/100.0, sort_ID, station_name);
              ignore = TRUE;
          }
          else if (abs(subLTLevM-subHTLevM) > 0.7 * (subLTLevM>subHTLevM?subLTLevM:subHTLevM))
              err_msg("Warning: Suspicious tide multipliers (H*%.2f,L*%.2f) for %s %s",
                      (float)subHTLevM/100.0, (float)subLTLevM/100.0, sort_ID, station_name);

          sprintf(sHTmpy, "%.2f", (float)subHTLevM/100.0);
          sprintf(sHToff, "%.1f", (float)subHTLevA/10.0);
          minimize_float_s(sHTmpy);
          minimize_float_s(sHToff);

          sprintf(sLTmpy, "%.2f", (float)subLTLevM/100.0);
          sprintf(sLToff, "%.1f", (float)subLTLevA/10.0);
          minimize_float_s(sLTmpy);
          minimize_float_s(sLToff);

          if ((subHTTimH<0) || (subHTTimM<0))
               sprintf(sHTTime,"-%d",(abs(subHTTimH)*60 + abs(subHTTimM)));
          else sprintf(sHTTime, "%d",(    subHTTimH *60 +     subHTTimM));

          if ((subLTTimH<0) || (subLTTimM<0))
               sprintf(sLTTime,"-%d",(abs(subLTTimH)*60 + abs(subLTTimM)));
          else sprintf(sLTTime, "%d",(    subLTTimH *60 +     subLTTimM));

          fixup_sort_ID( Lon, Lat );

          if (is_duplicate( station_name , Lat, Lon, current_ch))
            err_msg("Warning: Station %s is duplicate and skipped", station_name);
          else

          if (!ignore) {

             fprintf(OutputFile,
                "%c%s %s %s %s %s\n"
                "&%s %s %s %s %s %s %d %s %d %s\n",
                current_ch, sort_ID, sLon, sLat, time_zone, station_name,
                sHTTime, sHTmpy, sHToff, sLTTime, sLTmpy, sLToff,
                subID, tzname+1, harmonic_file_num, refName);
          }
        }
        else {
          printf("Could not read reference station info for record %d secondary station.\n", recnum);
        }
      }
      else {
        printf("Unrecognized record type in TCD file at record %d\n", recnum);
        close_tide_db();
        return(1);
      }
      long_state_found = country_found = other_found = region_found = short_state_found = FALSE;
    }
    close_tide_db();
    return (0);
  }
  else
    return (1);
}
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   process header

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
void process_header_line() {
static char current_ch, station_name[MAXNAME], time_zone[80], sLon[40], sLat[40], tzname[80];
static int had_name = FALSE;
int TZHr, TZMin, i;

   check_4_lat_lon();

   if (InputLine[0] != '#') {           // End of header, dump data found
      if (!had_name) {
         strip_blanks(InputLine);
         strcpy(station_name, InputLine);
         had_name = TRUE;
      }
      else {
         sscanf(InputLine, "%d:%d", &TZHr, &TZMin);
         sscanf(InputLine, "%s %s", time_zone, tzname);
         check_4_country_state(station_name);
         build_sort_ID(station_name);
         if (time_zone[0] == '-' && TZHr == 0) TZMin *= -1;
         format_TZ(time_zone,TZHr, TZMin, sort_ID, station_name);

         for (i=0; tznames[i][0]!=NULL && stricmp(tznames[i][0], tzname+1); i++);
         if (tznames[i][0]==NULL) err_msg("Error: timezone name not found %s", tzname);

         format_lat_lon(sLat, sLon, &Lat, &Lon, sort_ID, station_name);
         fixup_sort_ID( Lon, Lat );

         if (check_4_string( station_name, "Current", FALSE )>=0)
            current_ch = 'C';
         else current_ch = 'T';

         if (is_duplicate( station_name , Lat, Lon, current_ch))
           err_msg("Warning: Station %s is duplicate and skipped", station_name);
         else
           fprintf( OutputFile,"%c%s %s %s %s %s\n",
                    current_ch, sort_ID, sLon, sLat, time_zone, station_name);

         skip2header = TRUE;
         copyheader = FALSE;
         long_state_found = country_found = other_found = region_found = short_state_found = had_name = FALSE;
      }
   }
   else {
      /* This is a keeper line */
      check_4_country_state(InputLine);
//      fputs( InputLine, OutputFile );  // Copy line to output
   }
}

int have_CAPS( char *dst, char *src ) {
int i, j;

   for (i=0; (i<strlen(src)) && (src[i]==' '); i++);
   if (i < strlen(src)) {
      j = i;
      while ((i < strlen(src)) && ((src[i]==' ') ||
         ((src[i] >= 'A') && (src[i] <= 'Z')))) i++;
      if ((i-j) > 3) {
//         src[i]='\0';
         strcpy( dst, src );
         return( 1 );
      }
   }
   return(0);
}
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   process subreg file line

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int process_subreg_line(){
int subID,subLatD,subLatM,subLonD,subLonM;
int subHTTimH,subHTTimM,subLTTimH,subLTTimM;
int subHTLevM,subHTLevA,subLTLevM,subLTLevA,subTZ,subNotes;
int subTZHr, subTZMin, ignore, i;
char subLoc[80], subRef[13], sHToff[30], sLToff[30], sHTTime[30], sLTTime[30], sTZTime[30];
char *refName, full_name[MAXNAME], sLat[30], sLon[30], sHTmpy[30], sLTmpy[30];
static int had_indent, had_CAPS=0, holding_major_prefix=0, holding_CAPS=0, item_cnt;
double Lat, Lon, dLat, dLon, delNM;
static char name_prefix[MAXNAME], major_prefix[MAXNAME], CAPS_prefix[MAXNAME], save_sort_ID[40];

   if (strlen(save_sort_ID)<3) strcpy(save_sort_ID, ":::");
   subLoc[0] = subRef[0] = '\0';
   item_cnt = sscanf(InputLine,"%d,\"%[^\"]\",\"%[^\"]\",%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d",
      &subID,subLoc,subRef,&subLatD,&subLatM,&subLonD,&subLonM,
      &subHTTimH,&subHTTimM,&subLTTimH,&subLTTimM,
      &subHTLevM,&subHTLevA,&subLTLevM,&subLTLevA,&subTZ,&subNotes);
   if (item_cnt < 2) return ( item_cnt < 1 ); // TRUE if end of file

   had_indent = (subLoc[0] == ' ');

   if (line_num == 1) {
      if (check_4_string(InputName,"subreg.43",FALSE) >= 0) {
         strcpy(major_prefix, " California");
         holding_major_prefix = 1;
         err_msg("Warning: Adding header line of \"%s\"",major_prefix);
      }
      else if (check_4_string(InputName,"subreg.46",FALSE) >= 0) {
         strcpy(CAPS_prefix, " British Columbia");
         holding_CAPS = had_CAPS = 1;
         err_msg("Warning: Adding header line of \"%s\"",CAPS_prefix);
      }
      else if (check_4_string(InputName,"subreg.51",FALSE) >= 0 &&
          strstr(subLoc,"Bering Sea")) {
         strcat(subLoc,", Russia");
         err_msg("Warning: Modifying header line to \"%s\"",subLoc);
      }
   }

   if (subID == 0) {

      if (had_indent) { // ID of 0 and indented is usually state or country identifier
         if (holding_major_prefix)
            err_msg("Warning: throwing away unused area prefix %s",major_prefix);
         strcpy( major_prefix, subLoc );  // Used for country/state
         if (have_CAPS(CAPS_prefix, subLoc)) {
            holding_CAPS = had_CAPS = 1;
            holding_major_prefix = 0;
         }
         else
            holding_major_prefix = 1;
      }
      else { // ID of 0 with no indent is area or info data
         strip_blanks(subLoc);
         strcpy(name_prefix,subLoc);       // ID's of 0 mean leadin
      }
   }
   else if (item_cnt >= 2) {
      strip_blanks(subLoc);
      strip_blanks(subRef);

      if ((refName=find_refsta(subRef, FALSE)) == "") {
        if (!subHTTimH && !subHTTimM && !subLTTimH && !subLTTimM &&
             subHTLevM==100 && !subHTLevA && subLTLevM==100 && !subLTLevA) {
          Lat =  abs(subLatD) + (abs(subLatM) / 60.0);
          Lon = (abs(subLonD) + (abs(subLonM) / 60.0)) * -1.0;
          if (subLatD<0 || subLatM<0) Lat = -Lat;
          if (subLonD<0 || subLonM<0) Lon = -Lon;
          err_msg("... checking for near Reference for %s ...", subLoc);
          is_duplicate(subRef, Lat, Lon, 0);
        }
        return( FALSE );
      }

      ignore = FALSE;
      strcpy( save_sort_ID, sort_ID );
      country_found = long_state_found = other_found = region_found = short_state_found = 0;
      if (had_CAPS)   check_4_country_state( CAPS_prefix );
      if (had_indent) check_4_country_state( name_prefix );
      check_4_country_state( major_prefix );
      check_4_country_state( subLoc );
      short_state_found = 0; // Ignore state abbreviations
      if (build_sort_ID( subLoc ) > 1)    // OK or warnings are left intact
         strcpy( sort_ID, save_sort_ID ); // Else use reference station ID

      if (!had_indent) strcpy(full_name,subLoc);
      else {
         strcpy(full_name,name_prefix);
         strcat(full_name, ": ");
         strcat(full_name,subLoc);
      }

      if (item_cnt != 17) return( FALSE ); // If not full input, ignore rest of processing

// I've found lots of multipliers of 1.0 and .1 (typos) so autocorrect
      if (((subLTLevM == 10) && (subHTLevM == 100)) ||
          ((subHTLevM == 10) && (subLTLevM == 100))) {
         err_msg("Warning: Auto-correct multipliers (0.1 & 1.0) for %s %s",
                  sort_ID, full_name);
         subLTLevM = subHTLevM = 100;
      }

// Any multiplier < 0.05 or > 5.0 MUST be in error
      if ((subLTLevM < 5) || (subLTLevM > 500) ||
          (subHTLevM < 5) || (subHTLevM > 500)) {
          err_msg("ERROR: Tide multipliers bad (H*%.2f,L*%.2f), ignoring %s %s",
                   (float)subHTLevM/100.0, (float)subLTLevM/100.0, sort_ID, full_name);
          ignore = TRUE;
      }
      else if (abs(subLTLevM-subHTLevM) > 0.7 * (subLTLevM>subHTLevM?subLTLevM:subHTLevM))
          err_msg("Warning: Suspicious tide multipliers (H*%.2f,L*%.2f) for %s %s",
                  (float)subHTLevM/100.0, (float)subLTLevM/100.0, sort_ID, full_name);

      sprintf(sHTmpy, "%.2f", (float)subHTLevM/100.0);
      sprintf(sHToff, "%.1f", (float)subHTLevA/10.0);
      minimize_float_s(sHTmpy);
      minimize_float_s(sHToff);

      sprintf(sLTmpy, "%.2f", (float)subLTLevM/100.0);
      sprintf(sLToff, "%.1f", (float)subLTLevA/10.0);
      minimize_float_s(sLTmpy);
      minimize_float_s(sLToff);

      if ((subHTTimH<0) || (subHTTimM<0))
           sprintf(sHTTime,"-%d",(abs(subHTTimH)*60 + abs(subHTTimM)));
      else sprintf(sHTTime, "%d",(    subHTTimH *60 +     subHTTimM));

      if ((subLTTimH<0) || (subLTTimM<0))
           sprintf(sLTTime,"-%d",(abs(subLTTimH)*60 + abs(subLTTimM)));
      else sprintf(sLTTime, "%d",(    subLTTimH *60 +     subLTTimM));

      subTZHr = abs(subTZ) / 100;
      subTZMin = ((float)((abs(subTZ) % 100) / 100.0) * 60.0) + 0.5;
      if (subTZ > 0) {
         subTZHr  *= -1;
         subTZMin *= -1;
      }
      format_TZ(sTZTime,subTZHr, subTZMin, sort_ID, full_name);

      Lat =  abs(subLatD) + (abs(subLatM) / 60.0);
      Lon = (abs(subLonD) + (abs(subLonM) / 60.0)) * -1.0;
      if (subLatD<0 || subLatM<0) Lat = -Lat;
      if (subLonD<0 || subLonM<0) Lon = -Lon;

      if (!format_lat_lon(sLat, sLon, &Lat, &Lon, sort_ID, full_name)) {
#define DEG2RAD 0.0174532778L
         dLat = Lat - refLat;
         dLon = Lon - refLon;
         if      (dLat >  90.0) dLat =  180.0 - dLat;
         else if (dLat < -90.0) dLat = -180.0 - dLat;
         if      (dLon > 180.0) dLon =  360.0 - dLon;
         else if (dLon <-180.0) dLon = -360.0 - dLon;

         if (abs(Lat) > abs(refLat))
              dLon *= cos(   Lat*DEG2RAD);
         else dLon *= cos(refLat*DEG2RAD);
         delNM= sqrt((dLat*dLat) + (dLon*dLon)) * 60.0;  // 1 deg = 60 NM

         if (delNM > too_far_NM) {
            err_msg("Warning: %.0fNM move from %s to (%.1fE,%.1fN) Ref: %s",
               delNM, full_name, refLon, refLat, refName);
//            ignore = TRUE;
         }

         if (subHTLevM==100 && !subHTLevA && !subHTTimH && !subHTTimM &&
             subLTLevM==100 && !subLTLevA && !subLTTimH && !subLTTimM) {
            if (delNM >= 1.0)
              err_msg("RefCheck: %.1fNM %s %s to %s",delNM,sort_ID,full_name,refName);
            else
              ignore = TRUE;
         }
      }

      fixup_sort_ID( Lon, Lat );

      // Now check for Quebec stations to skip
      i = check_4_name(full_name, OTHER, TRUE); // Look for skippers
      if (i && (!strcmp(sort_ID,"NAA:Can:Qbc:") || !strcmp(sort_ID,"NAP:US:AK:")))
         if (!strcmp("SKIP",abbreviation_list[i-1]->abbreviation)) {
            ignore = TRUE;
            err_msg("Warning: Skipping station %s %s", sort_ID, full_name);
         }

      if (!ignore) {
         if (holding_CAPS) {               // Only output if we are writing something out
            fprintf(OutputFile, "I%s 0 0 0:0 %s\n",sort_ID, CAPS_prefix);
            holding_CAPS = 0;
         }
         if (holding_major_prefix) {       // Only output if we are writing something out
            fprintf(OutputFile, "I%s 0 0 0:0 %s\n",sort_ID, major_prefix);
            holding_major_prefix = 0;
         }

//         if (is_duplicate( full_name , Lat, Lon))
//           err_msg("Warning: Station %s is duplicate and skipped", full_name);
//         else
           fprintf(OutputFile, "t%s %s %s %s %s\n"
              "&%s %s %s %s %s %s %d %d %s\n",
              sort_ID, sLon, sLat, sTZTime, full_name,
              sHTTime, sHTmpy, sHToff, sLTTime, sLTmpy, sLToff,
              subID, harmonic_file_num, refName);
      }

      long_state_found = country_found = region_found = other_found = short_state_found = FALSE;
   }
   return( FALSE );
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Allocate space and copy string

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
// int allocate_copy_string(char **dst, char *string) {
//    *dst=(char *)malloc( (int)(strlen(string)) +1);
//    if (dst != NULL) {
//       strcpy(*dst,string);
//       return(0);
//    } else return (-1);
// }

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   Allocate float array space

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int allocate_float_array(float **dst, int how_many) {
   if (NULL != (*dst=(float *)malloc( how_many * sizeof(float))))
      return(0);
   else return (-1);
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   load control file

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
int load_control_file() {
FILE *ControlFile;
int  nReturn, file_line;
int abr_list_num, ref_list_num, SCR_list_num, fix_list_num,TZ_list_num;
int doing_abr, doing_ref, doing_SCR, doing_fix, doing_TZ;
int found_abr, found_ref, found_SCR, found_fix, found_TZ;
char s1[80],s2[80],s3[80],s4[80],s5[80];
int i,j,just_testing, ctl_len, iTZ, iTZnew;
float f[10], fTZmpy;

   ControlFile = fopen( ControlName, "rt");
   if (ControlFile == NULL) {
      printf("Can't open control file [%s]\n",ControlName);
      return(1);
   }

   just_testing = 1;
   do {
      abr_list_num= ref_list_num= SCR_list_num= fix_list_num= TZ_list_num= 0;
      doing_abr= doing_ref= doing_SCR= doing_fix= doing_TZ = 0;
      found_abr= found_ref= found_SCR= found_fix= found_TZ = 0;
      nReturn  = file_line = 0;

      while ((fgets( InputLine, 1024, ControlFile) != NULL) && !nReturn) {
         file_line++;
         if ((InputLine[0] != '#') && (InputLine[0] > ' ')) {
            if (!strncmp(InputLine,"*END*",5)) {
               if (!(doing_ref | doing_abr | doing_SCR | doing_fix | doing_TZ))  // If not doing any list
                  nReturn = file_line;
               doing_ref = doing_abr = doing_SCR = doing_fix = doing_TZ = 0;
            }

            else if (doing_ref) {
               sscanf(InputLine,"%s %[^\n]",s1,s2);
               strip_blanks(s1);
               strip_blanks(s2);
               if (strlen(s2) < 4)
                  nReturn = file_line;
               else if (!just_testing) {
                  nReturn |= allocate_copy_string(&ref_station_list[ref_list_num]->short_name, s1);
                  nReturn |= allocate_copy_string(&ref_station_list[ref_list_num]->long_name,  s2);
               }
               ref_list_num++;
            }

            else if (doing_abr) {
               sscanf(InputLine,"%s %s %[^\n]",s1,s2,s3);
               strip_blanks(s1);
               strip_blanks(s2);
               strip_blanks(s3);
               if ((!strcmp(s1,"REGION")) ||
                   (!strcmp(s1,"COUNTRY")) ||
                   (!strcmp(s1,"STATE")) ||
                   (!strcmp(s1,"OTHER"))) {
                  if (!just_testing) {
                     nReturn |= allocate_copy_string(&abbreviation_list[abr_list_num]->abbreviation, s2);
                     nReturn |= allocate_copy_string(&abbreviation_list[abr_list_num]->long_name,    s3);
                     if      (!strcmp(s1,"REGION"))
                          abbreviation_list[abr_list_num]->type = REGION;
                     else if (!strcmp(s1,"COUNTRY"))
                          abbreviation_list[abr_list_num]->type = COUNTRY;
                     else if (!strcmp(s1,"STATE"))
                          abbreviation_list[abr_list_num]->type = STATE;
                     else abbreviation_list[abr_list_num]->type = OTHER;
                  }
                  abr_list_num++;
               }
               else nReturn = file_line;
            }

            else if (doing_SCR) {
               sscanf(InputLine,"%s %s %[^\n]",s1,s2,s3);
               strip_blanks(s1);
               strip_blanks(s2);
               strip_blanks(s3);
               if ((strlen(s1) < 2) || (strlen(s2) < 2))
                  nReturn = file_line;
               else if (!just_testing) {
                  nReturn |= allocate_copy_string(&state_country_region[SCR_list_num]->region,  s1);
                  nReturn |= allocate_copy_string(&state_country_region[SCR_list_num]->country, s2);
                  nReturn |= allocate_copy_string(&state_country_region[SCR_list_num]->state,   s3);
               }
               SCR_list_num++;
            }

            else if (doing_fix) {
               ctl_len = sscanf(InputLine,"%s%s%s%f%f%f%f%f%f%f%f%f%f",
                     s1,s2,s3,&f[0],&f[1],&f[2],&f[3],&f[4],&f[5],&f[6],&f[7],&f[8],&f[9]);
               strip_blanks(s1);
               strip_blanks(s2);
               strip_blanks(s3);
               if ((ctl_len < 5) ||     // Must have at least 1 pair
                  ((ctl_len & 1) != 1)) // must be an even number
                  nReturn = file_line;
               else if (!just_testing) {
                  nReturn |= allocate_copy_string(&region_fixup_list[fix_list_num]->sort_ID,s1);
                  nReturn |= allocate_float_array(&region_fixup_list[fix_list_num]->lon, 5);
                  nReturn |= allocate_float_array(&region_fixup_list[fix_list_num]->lat, 5);
                  if (!nReturn) {
                     allocate_copy_string(&region_fixup_list[fix_list_num]->region_NE,s2);
                     allocate_copy_string(&region_fixup_list[fix_list_num]->region_SW,s3);
                     for (i=0;i < ((ctl_len-3) / 2); i++) {
                        region_fixup_list[fix_list_num]->lon[i] = f[(i*2)+0];
                        region_fixup_list[fix_list_num]->lat[i] = f[(i*2)+1];
                     }
                     if (i < 5) {
                        region_fixup_list[fix_list_num]->lon[i] = 1000.0;
                        region_fixup_list[fix_list_num]->lat[i] = 1000.0;
                     }
                  }
               }
               fix_list_num++;
            }

            else if (doing_TZ) {
               if (4 == sscanf(InputLine,"%s %s %s %s %[^\n]",s1,s2,s3,s4,s5))
                  strcpy(s5,"");
               strip_blanks(s1);
               strip_blanks(s2);
               strip_blanks(s3);
               strip_blanks(s4);
               strip_blanks(s5);
               if ((!strcmp(s1,"TIMEZONE")) ||
                   (!strcmp(s1,"LONLAT"))) {
                  if (!just_testing) {
                     nReturn |= allocate_copy_string(&Auto_fixup_list[TZ_list_num]->id,     s2);
                     nReturn |= allocate_copy_string(&Auto_fixup_list[TZ_list_num]->old_lon,s3);
                     nReturn |= allocate_copy_string(&Auto_fixup_list[TZ_list_num]->new_lat,s4);
                     nReturn |= allocate_copy_string(&Auto_fixup_list[TZ_list_num]->name,   s5);
                     if (!strcmp(s1,"TIMEZONE"))
                          Auto_fixup_list[TZ_list_num]->type = TIMEZONE;
                     else Auto_fixup_list[TZ_list_num]->type = LONLAT;
                  }
                  TZ_list_num++;
               }
               else nReturn = file_line;
            }

            else if (!strncmp(InputLine,"REF_STATION_LIST",16)) {
               if ((doing_ref|doing_abr|doing_SCR|doing_fix|doing_TZ) || found_ref)
                  nReturn = file_line;
               else
                  found_ref = doing_ref = TRUE;
            }

            else if (!strncmp(InputLine,"REGION_COUNTRY_STATE_LIST",25)) {
               if ((doing_ref|doing_abr|doing_SCR|doing_fix|doing_TZ) || found_SCR)
                  nReturn = file_line;
               else
                  found_SCR = doing_SCR = TRUE;
            }

            else if (!strncmp(InputLine,"ABBREVIATION_LIST",17)) {
               if ((doing_ref|doing_abr|doing_SCR|doing_fix|doing_TZ) || found_abr)
                  nReturn = file_line;
               else
                  found_abr = doing_abr = TRUE;
            }

            else if (!strncmp(InputLine,"REGION_FIXUP",12)) {
               if ((doing_ref|doing_abr|doing_SCR|doing_fix|doing_TZ) || found_fix)
                  nReturn = file_line;
               else
                  found_fix = doing_fix = TRUE;
            }

            else if (!strncmp(InputLine,"AUTO_FIXUP",10)) {
               if ((doing_ref|doing_abr|doing_SCR|doing_fix|doing_TZ) || found_TZ)
                  nReturn = file_line;
               else
                  found_TZ = doing_TZ = TRUE;
            }

            else if (!strncmp(InputLine,"TOO_FAR_NM",10)) {
               sscanf(InputLine,"%*s%lf",&too_far_NM);
            }

            else if (!strncmp(InputLine,"CLEAN_INDEX_FILE",16)) {
               clean_index_file = TRUE;
            }

            else if (!strncmp(InputLine,"DISPLAY_FIXUP",13)) {
               display_fixup = TRUE;
            }

            else if (!strncmp(InputLine,"NO_DUPES1",9)) {
               no_dupes = 2;
            }

            else if (!strncmp(InputLine,"NO_DUPES",8)) {
               if (no_dupes==0) no_dupes = 1;
            }

            else nReturn = file_line;
         }
      }
      if ((  doing_abr|doing_ref|doing_SCR|doing_fix|doing_TZ)||// If any lists left open..
          (!(found_abr&found_ref&found_SCR&found_fix&found_TZ)))// or any lists not found..
          nReturn = file_line;                                  // .. then flag an error.

      if (!nReturn && just_testing) {
/* -----------------10/10/97 7:15AM------------------
   Allocate memory and initialize ref_station_list array
 --------------------------------------------------*/
         if (NULL==(ref_station_list=
            (ref_station_entry **)malloc((ref_list_num+1)*sizeof(ref_station_entry *))))
            nReturn = -1;
         else {
            for (i=0; i<=ref_list_num; i++)
               if (NULL==(ref_station_list[i] = (ref_station_entry *) malloc(sizeof(ref_station_entry)))) {
                  nReturn = -1;
                  break;
               }
            if (!nReturn) {
               ref_station_list[ref_list_num]->short_name = NULL;
               ref_station_list[ref_list_num]->long_name  = NULL;
            }
         }
/* -----------------10/10/97 7:15AM------------------
   Allocate memory and initialize abbreviation_list
 --------------------------------------------------*/
         if (nReturn || NULL==(abbreviation_list=
            (abbreviation_entry **)malloc((abr_list_num+1)*sizeof(abbreviation_entry *))))
            nReturn = -1;
         else {
            for (i=0; i<=abr_list_num; i++)
               if (NULL==(abbreviation_list[i] = (abbreviation_entry *) malloc(sizeof(abbreviation_entry)))) {
                  nReturn = -1;
                  break;
               }
            if (!nReturn) {
               abbreviation_list[abr_list_num]->abbreviation = NULL;
               abbreviation_list[abr_list_num]->long_name    = NULL;
               abbreviation_list[abr_list_num]->type         = 0;
            }
         }
/* -----------------10/10/97 7:15AM------------------
   Allocate memory and initialize SCR_list
 --------------------------------------------------*/
         if (nReturn || NULL==(state_country_region=
            (SCR_entry **)malloc((SCR_list_num+1)*sizeof(SCR_entry *))))
            nReturn = -1;
         else {
            for (i=0; i<=SCR_list_num; i++)
               if (NULL==(state_country_region[i] = (SCR_entry *) malloc(sizeof(SCR_entry)))) {
                  nReturn = -1;
                  break;
               }
            if (!nReturn) {
               state_country_region[SCR_list_num]->state   = NULL;
               state_country_region[SCR_list_num]->country = NULL;
               state_country_region[SCR_list_num]->region  = NULL;
            }
         }
/* -----------------10/10/97 7:15AM------------------
   Allocate memory and initialize region_fixup_list
 --------------------------------------------------*/
         if (nReturn || NULL==(region_fixup_list=
            (region_fixup_entry **)malloc((fix_list_num+1)*sizeof(region_fixup_entry *))))
            nReturn = -1;
         else {
            for (i=0; i<=fix_list_num; i++)
               if (NULL==(region_fixup_list[i] =
                  (region_fixup_entry *) malloc(sizeof(region_fixup_entry)))) {
                  nReturn = -1;
                  break;
               }
            if (!nReturn)
               region_fixup_list[fix_list_num]->sort_ID   = NULL;
         }
/* -----------------03/15/98 7:15AM------------------
   Allocate memory and initialize Auto_fixup_list
 --------------------------------------------------*/
         if (nReturn || NULL==(Auto_fixup_list=
            (Auto_fixup_entry **)malloc((TZ_list_num+1)*sizeof(Auto_fixup_entry *))))
            nReturn = -1;
         else {
            for (i=0; i<=TZ_list_num; i++)
               if (NULL==(Auto_fixup_list[i] =
                  (Auto_fixup_entry *) malloc(sizeof(Auto_fixup_entry)))) {
                  nReturn = -1;
                  break;
               }
            if (!nReturn)
               Auto_fixup_list[TZ_list_num]->type = 0;
         }
      }
      fseek(ControlFile,0L,SEEK_SET);
   } while ((--just_testing >= 0) && !nReturn);

   fclose( ControlFile );

   if (nReturn == -1)
      printf("Could not allocate memory for structures defined in control file %s!\n", ControlName);
   else if (nReturn)
      printf("Syntax error at line %d in control file %s!\n", nReturn, ControlName);

   return( nReturn );
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *

   main
   Opens the source file.
   Reads lines.
   Extracts tokens.
   Prints results.
   Closes file.

 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

int main(int argc, const char *argv[] ) {
long int i,j;

   printf("WXTide32 station index builder, version 2.3 - Jan 22, 2006\n");
   if (argc < 3) {
      printf(
"Tide2idx is part of the free WXTide32 program located at http://WXTide32.com\n"
"Purpose is to build an index file for use by WXTides32 containing one or more\n"
"sets of Reference stations (Harmonic) followed by any number of substations.\n"
"Substations are defined with a name, the name of the reference station they\n"
"are based upon, the location, and time and level offsets from that reference\n"
"station.\n"
"\n"
"The file \"Tide2IDX.ctl\" has all control parameters for station parsing.\n"
"\n"
"The reference station files follow the XTide Harmonic file format.\n"
"Substation files follow the format of Hans Pieper's Tides 2.x DOS program.\n"
"\n"
"The first command line parameter is the name of a source file.  The program\n"
"looks for file names starting with \"Har\" and \"subreg\" to determine the\n"
"operation needed.\n"
"\n"
"The second command line parameter is the destination file name, usually\n"
"\"Harmonic.idx\".  The destination file name is preceeded by '+' to indicate\n"
"ADDing to an existing index file. Otherwise the destination is overwritten.\n"
"\n"
"Note: ALL Harmonic files should be loaded BEFORE the substation files since\n"
"substation files will go back through the index file to fill reference\n"
"information and to validate entries\n"
"\n"
"Also note that any validation errors and warnings appear both on the screen\n"
"and in the index file.\n"
"\n"
"Example batch file:\n"
"del Harmonic.idx\n"
"tide2idx Harmonic      Harmonic.idx\n"
"tide2idx Harmonic.adm +Harmonic.idx\n"
"for %%i in (subreg.*) do tide2idx %%i +Harmonic.idx\n"
);

      return(1);
   }

   if (load_control_file())
      return(1);

/* -----------------10/9/97 7:26PM-------------------
   Get input file name and determine type
 --------------------------------------------------*/
   is_tcd_file = is_harmonic_file = is_subreg_file = FALSE;
   strcpy(InputName,argv[1]);
   if      (check_4_string(InputName,".tcd",FALSE)    >= 0) is_tcd_file      = TRUE;
   else if (check_4_string(InputName,"har",FALSE)     >= 0) is_harmonic_file = TRUE;
   else if (check_4_string(InputName,"subreg.",FALSE) >= 0) is_subreg_file   = TRUE;

   if (!is_harmonic_file && !is_subreg_file && !is_tcd_file) {
      printf("Input files must be named '*.tcd', 'Har..' or 'subreg..'\n");
      return(1);
   }

   if (check_4_string(InputName,"subreg.40",FALSE) >= 0) {
      printf("Skipping subreg.40 (duplicate of subreg.42)\n\n");
      return(1);
   }

   InputFile = fopen( InputName, "rt");
   if (InputFile == NULL) {
      printf("Can't open input file [%s]\n",InputName);
      return(1);
      }

/* -----------------10/9/97 7:26PM-------------------
   Get output file name and see if it is append
 --------------------------------------------------*/
   strcpy(OutputName,argv[2]);
   if (OutputName[0] == '+') {
      memmove(OutputName+0,OutputName+1,strlen(OutputName));  // Remove leading '+'
      OutputFile = fopen( OutputName, "a+t");  // Open for append
      fseek( OutputFile, 0L, SEEK_END );
   }
   else {
      if ((OutputFile = fopen( OutputName, "rt")) != NULL) {
        fclose(OutputFile);
        printf("Warning: Output index file %s already exists, Overwrite?",OutputName);
        scanf("%c",&ch);
        if ((ch != 'Y') && (ch != 'y')) {
           return(1);
        }
        else printf("\n");
      }
      OutputFile = fopen( OutputName, "wt");
   }

   if (OutputFile == NULL) {
      printf("Can't open output file [%s]\n",OutputName);
      return(1);
      }
/* -----------------10/9/97 7:27PM-------------------
   If this is a new file, start things off by putting
   a cross reference table of Region abbreviations to
   full name to the index file.
 --------------------------------------------------*/
   if (ftell( OutputFile ) == 0) {      // Start of index file
//      printf("Processing %s......",InputName);
      fprintf(OutputFile,
      "# Note: This file is automatically generated by Tide2idx version 2.2 (2005-06-26)\n"
      "# and distributed as part of the free WXTide32 program located at http://WXTide32.com\n"
      "# Please do not modify this file unless you know EXACTLY what you're doing!\n\n"
      "# Basic line formats:\n"
      "# Reference stations: T|C Reg:Co:St Lon Lat Meridian Name\n"
      "# Info lines:         I Reg:Co:St 0 0 0:0 (blank) Information\n"
      "# Subordinate sta'ns: t|c Reg:Co:St Lon Lat Meridian Name\n"
      "# Subordinate extra:  &Hmin Hmpy Hoff Lmin Lmpy Loff StaID [tzname] RefFileNum RefName\n\n");

      fprintf(OutputFile,"XREF #Start of abbreviation cross reference\n");
      fprintf(OutputFile,"PATCHTZ Usertz1 EST5EDT,M4.1.0/2:00,M10.5.0/2:00\n");
      i = 0;
      do
         if (abbreviation_list[i]->type==REGION) {
            fprintf(OutputFile, "REGION %s %s\n",
               abbreviation_list[i]->abbreviation,
               abbreviation_list[i]->long_name);
         }
      while (abbreviation_list[++i]->type != 0);

      i = 0;
      do
         if (abbreviation_list[i]->type==COUNTRY) {
            for (j=i-1; (j>=0) && ((abbreviation_list[j]->type!=COUNTRY) ||
                 strcmp(abbreviation_list[i]->abbreviation,abbreviation_list[j]->abbreviation));
                  j--);
            if (j < 0) fprintf(OutputFile, "COUNTRY %s %s\n",
               abbreviation_list[i]->abbreviation,
               abbreviation_list[i]->long_name);
         }
      while (abbreviation_list[++i]->type != 0);

      i = 0;
      do
         if (abbreviation_list[i]->type==STATE) {
            for (j=i-1; (j>=0) && ((abbreviation_list[j]->type!=STATE) ||
                 strcmp(abbreviation_list[i]->abbreviation,abbreviation_list[j]->abbreviation));
                  j--);
            if (j < 0) fprintf(OutputFile, "STATE %s %s\n",
               abbreviation_list[i]->abbreviation,
               abbreviation_list[i]->long_name);
         }
      while (abbreviation_list[++i]->type != 0);
      fprintf(OutputFile, "*END*\n");
   }

   if (is_harmonic_file)
      fprintf(OutputFile,"\nHarmonic");
   else if (is_subreg_file)
      fprintf(OutputFile,"\nSubreg");
   else if (is_tcd_file)
      fprintf(OutputFile,"\nBinary TCD");
   fprintf( OutputFile, " %s file start - - - - - - - - -\n",InputName);

   printf("Processing %s into %s\n",InputName, OutputName);
   long_state_found = country_found = region_found = other_found = short_state_found = FALSE;
   copyheader = FALSE;
   skip2header = TRUE;
   skipcount = 2;
   line_num = 0;
   if (is_harmonic_file || is_subreg_file) {
     while (fgets( InputLine, 1024, InputFile) != NULL) {
        if (is_harmonic_file) {
           if (skipcount <= 0) {
              if (skip2header) {
                 if (InputLine[0] == '#') {
                    skip2header = FALSE;
                    copyheader = TRUE;
                    Lon = Lat = 0.0;
                 }
              }
              if (copyheader) {
                 process_header_line();
              }
           }
           else if (check_4_string(InputLine,"*END*",TRUE) == 0) // Skip 1st 2 END's
                 skipcount--;
        }
        else if (++line_num > 0) {
           if (process_subreg_line()) {
              printf("subreg file processing stopped at line %d\n",line_num);
              line_num = -1;
           }
        }
        else line_num = -1;
     }
   }
   else {                               // is TCD file
     if (process_tcd_file(InputName)) return 1;
   }
   fclose( InputFile);
   fclose( OutputFile);
   printf("\n");
   return(0);
}