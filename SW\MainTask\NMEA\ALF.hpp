#include "Sentence.hpp"
#include "uart.hpp"

#ifndef __ALF_HPP__
#define __ALF_HPP__


extern cUART    *G_pUart3;

/******************************************************************************
 * 
 * ALF - Alert sentence
 *
 * $--ALF,x,x,x,hhmmss.ss,a,a,a,aaa,x.x,x.x,x.x,x,c--c*hh<CR><LF>
 *        | | |     |     | | |  |   |   |   |  |   |
 *        1 2 3     4     5 6 7  8   9   10  11 12  13
 *
 * 1. Total number of ALF sentences for this message, 1 to 2
 * 2. Sentence number, 1 to 2
 * 3. Sequential message identifier, 0 to 9
 * 4. Time of last change (null or UTC)
 * 5. Alert category, A, B or C
 * 6. Alert priority, E, A, W or C 
 * 7. Alert state, A, S, N, O, U or V 
		active-unacknowledged: V
		active-silenced: S
		active-acknowledged or active: A
		active-responsibility transferred: O
		rectified-unacknowledged: U
		normal: N
 * 8. Manufacturer mnemonic code
 * 9. Alert identifier
 * 10. Alert instance, 1 to 999999
 * 11. Revision counter, 1 to 99
 * 12. Escalation counter, 0 to 9 (1 after 0 is used)
 * 13. Alert text
 *
 **
 
 ******************************************************************************/
class CAlf : public CSentence {
protected:
	int m_nTotalNumber;	// Total number of ALF sentences for this message, 1 to 2
	int m_nSentenceNum; // 1~2
	int m_nSeqNumber;
	char m_szUTC[7];    // 6 + 1(NULL)
	int  m_nUTCHour;
	int  m_nUTCMin;
	int  m_nUTCSec;
	char m_cCategory;	// Alert category, A, B or C 
	char m_cPriority;	// Alert priority, E, A, W or C 
	char m_cState;		// Alert state, A, S, N, O, U or V	
	int  m_nID;
	int  m_nInstance;
	int m_nRevCnt;
	int m_nEscalationCnt;
	char m_szText1[128];
	char m_szText2[128];	
    
public:
    CAlf();
    CAlf(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);

	int	GetTotalNumber()	{ return m_nTotalNumber;	}
	int	GetSentenceNumber()	{ return m_nSentenceNum;	}
	int	GetSeqNumber()	{ return m_nSeqNumber;	}	
	int  GetUTCHour()   { return m_nUTCHour;    }
	int  GetUTCMin()    { return m_nUTCMin;     }
	int  GetUTCSec()    { return m_nUTCSec;     }
	void GetUTC(char szUTC[7]) { strcpy(szUTC, m_szUTC); }
	char GetCategory() { return m_cCategory; }
	char GetPriority() { return m_cPriority; }
	char GetState() { return m_cState; }	
	
	int  GetID()   { return m_nID;         }
	int GetInstance() { return m_nInstance; }
	int GetRevCnt() { return m_nRevCnt; }
	int GetEscalationCnt() { return m_nEscalationCnt; }	
	void GetAlertText1(char szText[128]) { strcpy(szText, m_szText1); }
	void GetAlertText2(char szText[128]) { strcpy(szText, m_szText2); }	
};

#endif

