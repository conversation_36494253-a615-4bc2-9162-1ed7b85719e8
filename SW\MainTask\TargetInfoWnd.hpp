#include "Wnd.hpp"
#include "ship.hpp"

#ifndef __TARGET_INFO_WND_HPP__
#define __TARGET_INFO_WND_HPP__

class CTargetInfoWnd : public CWnd 
{
	private:
		int   m_nCurPageNo;   // 1 ~ 3
		CShip *m_poTarget;
		CShip *m_pBackUpTarget;
		int    m_nSelMMSI;

	public:
		enum {
			PAGE_STATIC = 0,
			PAGE_VOYAGE,				
			PAGE_POSITION,
			PAGE_ANT,
			MAX_PAGE,
		};

				
	private:
		void DrawFuncBtn(int nLangMode);
		void DrawCurPageIndi(int nLangMode);

		// Static Data Page
		void DrawMMSI(CShip *pShip, int nLangMode);	
		void DrawStationType(CShip *pShip, int nLangMode);
		void DrawTargetShipName(CShip *pShip, int nLangMode);
		void DrawIMO(CShip *pShip, int nLangMode);
		void DrawCallSign(CShip *pShip, int nLangMode);
		void DrawHasDTE(CShip *pShip, int nLangMode);
		void DrawEPFS(CShip *pShip, int nLangMode);
		void DrawExtName(CShip *pShip, int nLangMode);
		void DrawStaticDataPage(CShip *pShip, int nLangMode);

		// Voyage Data Page
		void DrawShipCargoType(CShip *pShip, int nLangMode);
		void DrawDraught(CShip *pShip, int nLangMode);
		void DrawDestination(CShip *pShip, int nLangMode);
		void DrawETA(CShip *pShip, int nLangMode);
		void DrawNavStatus(CShip *pShip, int nLangMode);
		void DrawVoyageDataPage(CShip *pShip, int nLangMode);

		// Position Data Page
		void DrawLat(CShip *pShip, int nLangMode);
		void DrawLon(CShip *pShip, int nLangMode);
		void DrawPosQuality(CShip *pShip, int nLangMode);
		void DrawTimeStamp(CShip *pShip, int nLangMode);
		void DrawPA(CShip *pShip, int nLangMode);
		void DrawRAIM(CShip *pShip, int nLangMode);
		void DrawCOG(CShip *pShip, int nLangMode);
		void DrawSOG(CShip *pShip, int nLangMode);
		void DrawHDG(CShip *pShip, int nLangMode);
		void DrawROT(CShip *pShip, int nLangMode);
		void DrawAltitude(CShip *pShip, int nLangMode);
		void DrawOffPosFlag(CShip *pShip, int nLangMode);
		void DrawPosDataPage(CShip *pShip, int nLangMode);

		// Antenna Data Page
		void DrawShipShape(int nLangMode);
		void DrawAntPosData(CShip *pShip, int nLangMode);
		void DrawAntPosDataPage(CShip *pShip, int nLangMode);
		
	public:
		CTargetInfoWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
		~CTargetInfoWnd();

		void  OnKeyEvent(int nKey, DWORD nFlags=0);
		void  DrawWnd(BOOL bRedraw=1/*TRUE*/);

		void  SetFocus(int nFocus) { m_nFocus = nFocus; }
		void  SetPage(int nPage) { m_nCurPageNo = nPage; }
		void  SetTarget(CShip *poTarget);
		DWORD GetTargetMMSI() { return m_nSelMMSI; }
		void GetStationTypeString(DWORD dwMMSI, int nStationType, int nNavStatus, BYTE *pStr);
		static const char *GetShipTypeString(int nType);
#ifdef EN_61993_ED3
		static const char *GetShipTypeString2(DWORD dwMMSI, int nStationType, int nNavStatus, int nExpMode = 1);
#else	// Else of (EN_61993_ED3)
		static const char *GetShipTypeString2(DWORD dwMMSI, int nStationType);
#endif	// End of (EN_61993_ED3)	
};

#endif
