#ifndef __SETUP_CARGO_TYPE_WND_HPP__
#define __SETUP_CARGO_TYPE_WND_HPP__

#include "Wnd.hpp"
#include "EditCtrl.hpp"
#include "ComboCtrl.hpp"
#include "UniComboCtrl.hpp"

#define MAX_CARGO_TYPE_30		10
#define MAX_CARGO_TYPE_NORMAL	10
#define MAX_CARGO_TYPE_100_199	10
#define MAX_CARGO_TYPE_200_249	10
#define MAX_CARGO_TYPE_250_255	6

#define MAX_CARGO_TYPE_STR  	70

class CSetupCargoTypeWnd : public CWnd {
	protected:
		enum {
			CT_NONE,
			CT_30,
			CT_NORMAL,
			CT_100_199,
			CT_200_249,
			CT_250_255,
			MAX_CT
		};

		CUniComboCtrl *m_pCargoType30;
		CUniComboCtrl *m_pCargoTypeNormal;
		CUniComboCtrl *m_pCargoType100_199;
		CUniComboCtrl *m_pCargoType200_249;
		CUniComboCtrl *m_pCargoType250_255;
		
		int			m_nCurCargoTypeCmbCtrl;
		int         m_nFocus;
		int			m_nShipCargoType;
		int			m_nFirstDigit;
		int			m_nSecondDigit;
		HWORD       *m_pUniStrCargoType30[MAX_CARGO_TYPE_30];
		HWORD       *m_pUniStrCargoTypeNormal[MAX_CARGO_TYPE_NORMAL];
		HWORD       *m_pUniStrCargoType100_199[MAX_CARGO_TYPE_100_199];
		HWORD       *m_pUniStrCargoType_200_249[MAX_CARGO_TYPE_200_249];
		HWORD       *m_pUniStrCargoType_250_255[MAX_CARGO_TYPE_250_255];

	private:
		void CreateCargoTypeCtrl(cSCREEN *pScreen);
		void InitCargoTypeCtrl();
		void DrawShipType(int nLangMode);
		void DrawSelCargoTypeCtrl(int nLangMode);
		void DrawSelCargoType(int nLangMode);
		void DrawFunctionBtn(int nLangMode);
		
	public:
		CSetupCargoTypeWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

		virtual void OnActivate();
		void OnKeyEvent(int nKey, DWORD nFlags);
		void DrawWnd(BOOL bRedraw=1 /*TRUE*/);
		void InitCargoTypeData();
		int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

		void SetFocus(int nFocus);
		int  GetFocus();
		void ComboCollapse();
};

#endif	// End of __SETUP_CARGO_TYPE_WND_HPP__

