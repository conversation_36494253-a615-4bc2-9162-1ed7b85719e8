#include <stdio.h>
#include "VSD.hpp"
#include "Uart.hpp"
extern cUART *G_pUart3;

CVsd::CVsd() : CSentence()
{
	m_nShipCargoType    = 0;
	m_dblDraught        = 0;
	m_nPersons          = 0;
	strcpy(m_szDestination, "@@@@@@@@@@@@@@@@@@@@");
	strcpy(m_szEstUTC, "246000");
	m_nEstDay           = 0;
	m_nEstMonth         = 0;
	m_nNavStatus        = 15;
	m_nRegionalAppFlags = 0;
}
    
CVsd::CVsd(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CVsd::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_VSD;
}

/******************************************************************************
 * 
 * VSD - Voyage Static Data
 *
 * $--VSD,x.x,x.x,x.x,c--c,hhmmss.ss,xx,xx,x.x,x.x*hh<CR><LF>
 *         |   |   |   |     |       |  |   |   |
 *         1   2   3   4     5       6  7   8   9
 *
 * 1. Type of ship and cargo category, 0 to 255
 * 2. Maximum present static draught, 0 to 25.5
 * 3. Persons on-board, 0 to 8191
 * 4. Destination, 1-20 characters
 * 5. Est. UTC of destination arrival
 * 6. Est. day of arrival at destination, 00 to 31(UTC)
 * 7. Est. month of arrival at destination, 00 to 12(UTC)
 * 8. Navigational status, 0 to 15
 * 9. Regional application flags, 0 to 15
 *
 * Add talkerId check : HSI 2012.04.26
 *
 ******************************************************************************/
void CVsd::Parse()
{
	char szTemp[BUFSIZ];
	char *pSzTemp = NULL;
	char szTempUTC[20];
	int nStrLen = 0;

	GetTalkerID(m_szTalkID);
	
	m_nShipCargoType    = GetFieldInteger(1);
	m_dblDraught        = GetFieldDouble(2);

	//G_pUart3->OutputDbgMsg("[Receive VSD]%f\r\n",szTemp,m_dblDraught);
	
	m_nPersons          = GetFieldInteger(3);

	// Parse Destination
	memset(szTemp,0x00,sizeof(char)*BUFSIZ);
	GetFieldString(4, szTemp);
	pSzTemp = ConvertNormalString(szTemp);
	nStrLen = strlen(pSzTemp);
	
	if(strcmp(pSzTemp,"@@@@@@@@@@@@@@@@@@@@") == 0)
	{
		sprintf(m_szDestination,"@@@@@@@@@@@@@@@@@@@@");
	}
	else
	{
		if(nStrLen > 20)
		{
			strncpy(m_szDestination, pSzTemp,sizeof(char)*20);
			m_szDestination[20] = '\0';
		}
		else
		{
			strncpy(m_szDestination, pSzTemp,sizeof(char)*nStrLen);
			m_szDestination[nStrLen] = '\0';
		}			
	}
	//G_pUart3->OutputDbgMsg("[CVsd::Parse]Src Dest=[[%s]],Result Dest=[[%s]]\r\n",pSzTemp,m_szDestination);

	// Parse Utc Time
	GetFieldString(5, szTempUTC);
	memset(m_szEstUTC,0,sizeof(m_szEstUTC));
	strncpy(m_szEstUTC,szTempUTC,6);

	if( m_szEstUTC[0] != '\0' ) 
	{
		strncpy(szTemp, m_szEstUTC, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nEstUTCHour);

		strncpy(szTemp, m_szEstUTC+2, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nEstUTCMin);
	} 
	else 
	{
		m_nEstUTCHour = 0;
		m_nEstUTCMin  = 0;
	}

	m_nEstDay           = GetFieldInteger(6);
	m_nEstMonth         = GetFieldInteger(7);
	m_nNavStatus        = GetFieldInteger(8);
	m_nRegionalAppFlags = GetFieldInteger(9);
}

void CVsd::SetDestination(const char *pszDest)
{
	int nStrLen = 0;
	//G_pUart3->OutputDbgMsg("[CVsd::SetDestination]Input(pszDest)=[[%s]], (m_szDestination)=[[%s]]\r\n",pszDest,m_szDestination);
	memset(m_szDestination,0x00,sizeof(char)*61);
	if(pszDest == NULL)
	{
		strcpy(m_szDestination, "@@@@@@@@@@@@@@@@@@@@");
	}
	else
	{
		nStrLen = strlen(pszDest);
		strncpy(m_szDestination, pszDest, nStrLen);	
	}
}

int CVsd::MakeSentence(BYTE *pszSentence)
{
	int  nChecksum;
	BYTE szTemp[MAX_NMEA_LEN];
	BYTE szDest[MAX_NMEA_LEN];

	strcpy((char *)szDest, ConvertNMEAString(m_szDestination));

	sprintf((char *)szTemp, "$AIVSD,%03d,%04.1f,%03d,%s,%6s,%02d,%02d,%02d,%02d*",
			m_nShipCargoType, m_dblDraught, m_nPersons, szDest, m_szEstUTC,
			m_nEstDay, m_nEstMonth, m_nNavStatus, m_nRegionalAppFlags);
	
	nChecksum = CSentence::ComputeChecksum((char *)szTemp);
	sprintf((char *)pszSentence, "%s%02X\x0D\x0A", szTemp, nChecksum);

	return strlen((char *)pszSentence);
}

void CVsd::GetPlainText(char *pszPlainText)
{
	char szTemp[128];

	pszPlainText[0] = '\0';

	if( m_nShipCargoType != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Type of ship and cargo category      : %d\n", m_nShipCargoType);
		strcat(pszPlainText, szTemp);
	}

	if( m_dblDraught != NMEA_NULL_DOUBLE ) {
		sprintf(szTemp, "Maximum present static draught       : %.1f\n", m_dblDraught);
		strcat(pszPlainText, szTemp);
	}

	if( m_nPersons != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Presons on-board                     : %d\n", m_nPersons);
		strcat(pszPlainText, szTemp);
	}

	sprintf(szTemp, "Destination                          : %s\n", m_szDestination);
	strcat(pszPlainText, szTemp);

	sprintf(szTemp, "Est. UTC of destination arrival      : %02d:%02d:%02d\n", m_nEstUTCHour, m_nEstUTCMin, 0);
	strcat(pszPlainText, szTemp);
	
	if( m_nEstDay != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Est. day of arrival at destination   : %d\n", m_nEstDay);
		strcat(pszPlainText, szTemp);
	}

	if( m_nEstMonth != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Est. month of arrival at destination : %d\n", m_nEstMonth);
		strcat(pszPlainText, szTemp);
	}

	if( m_nNavStatus != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Navigational status                  : %d\n", m_nNavStatus);
		strcat(pszPlainText, szTemp);
	}

	if( m_nRegionalAppFlags != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Regional appication flags            : %d\n", m_nRegionalAppFlags);
		strcat(pszPlainText, szTemp);
	}
}

