#include "Sentence.hpp"

#ifndef __ACK_HPP__
#define __ACK_HPP__
/******************************************************************************
*
* ACK- Acknowledge Alarm
*
* $--ACK,xxx*hh<CR><LF>
*        |
*        1
*
* 1.  Unique alram number (identifier) at alarm source
*
******************************************************************************/
class CAck : public CSentence {
protected:
	int    m_nAlramNumber;

public:
    CAck();
    CAck(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);
	int  MakeSentence(BYTE *pszSentence) { return 0; }

	int  GetAlramNumber()     { return m_nAlramNumber;     }
};

#endif
