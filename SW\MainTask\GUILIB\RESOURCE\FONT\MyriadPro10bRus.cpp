/*...........................................................................*/
/*.                  File Name : MyriadPro10bRus.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma10bTai_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

ROMDATA PEGUSHORT MyriadPro10bRus_offset_table[257] = {
0x0000,0x0003,0x000a,0x0013,0x0019,0x0021,0x0028,0x002c,0x0031,0x0036,0x0042,0x004e,0x0057,0x005f,0x0062,0x006a,
0x0073,0x007c,0x0084,0x008c,0x0092,0x009b,0x00a2,0x00ae,0x00b5,0x00be,0x00c7,0x00cf,0x00d7,0x00e2,0x00eb,0x00f4,
0x00fd,0x0105,0x010d,0x0114,0x011c,0x0126,0x012e,0x0137,0x013f,0x014b,0x0157,0x0160,0x016b,0x0173,0x017b,0x0187,
0x018f,0x0196,0x019e,0x01a5,0x01aa,0x01b2,0x01b9,0x01c3,0x01c9,0x01d1,0x01d9,0x01e0,0x01e7,0x01f0,0x01f8,0x0200,
0x0207,0x020f,0x0215,0x021b,0x0222,0x022b,0x0232,0x023a,0x0241,0x024c,0x0257,0x025f,0x0269,0x0270,0x0276,0x0280,
0x0287,0x028a,0x0291,0x0299,0x029e,0x02a4,0x02aa,0x02ae,0x02b3,0x02b7,0x02c2,0x02cd,0x02d5,0x02dc,0x02df,0x02e6,
0x02ed,0x02f0,0x02f3,0x02f6,0x02f9,0x02fc,0x02ff,0x0302,0x0305,0x0308,0x030b,0x030e,0x0311,0x0314,0x0317,0x031a,
0x031d,0x0320,0x0323,0x0326,0x0329,0x032c,0x032f,0x0332,0x0335,0x0338,0x033b,0x033e,0x0341,0x0344,0x0347,0x034a,
0x034d,0x0350,0x0353,0x0356,0x0359,0x035c,0x035f,0x0362,0x0365,0x0368,0x036b,0x036e,0x0371,0x0374,0x0377,0x037a,
0x037d,0x0383,0x0389,0x038c,0x038f,0x0392,0x0395,0x0398,0x039b,0x039e,0x03a1,0x03a4,0x03a7,0x03aa,0x03ad,0x03b0,
0x03b3,0x03b6,0x03b9,0x03bc,0x03bf,0x03c2,0x03c5,0x03c8,0x03cb,0x03ce,0x03d1,0x03d4,0x03d7,0x03da,0x03dd,0x03e0,
0x03e3,0x03e6,0x03e9,0x03ec,0x03ef,0x03f2,0x03f5,0x03f8,0x03fb,0x03fe,0x0401,0x0404,0x0407,0x040a,0x040d,0x0410,
0x0413,0x0416,0x0419,0x041c,0x041f,0x0422,0x0425,0x0428,0x042b,0x042e,0x0431,0x0434,0x0437,0x043a,0x043d,0x0440,
0x0443,0x0446,0x0449,0x044c,0x044f,0x0452,0x0455,0x0458,0x045b,0x045e,0x0464,0x0467,0x046a,0x046d,0x0470,0x0473,
0x0476,0x0479,0x047c,0x047f,0x0482,0x0485,0x0488,0x048b,0x048e,0x0491,0x0494,0x0497,0x049a,0x049d,0x04a0,0x04a3,
0x04a6,0x04a9,0x04ac,0x04af,0x04b2,0x04b5,0x04b8,0x04bb,0x04be,0x04c1,0x04c4,0x04c7,0x04ca,0x04cd,0x04d0,0x04d3,
0x04d6};



ROMDATA PEGUBYTE MyriadPro10bRus_data_table[2480] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0d, 0x80, 0x03, 0x80, 0x00, 0x0e, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xbf, 0xcf, 0x8f, 0x9f, 0x63, 0x0c, 0xf8, 0x19, 0x83, 0xfc, 0xc6, 0x30, 0xd8, 0xc3, 0x07, 
0xc7, 0xc7, 0xcf, 0xcf, 0xb9, 0x9f, 0xe3, 0x19, 0x8c, 0xce, 0x7e, 0xe3, 0x18, 0xc3, 0xc7, 0xf3, 
0xe1, 0xf3, 0xfe, 0x31, 0xe3, 0x9d, 0x8c, 0xc6, 0xcc, 0xcc, 0xcd, 0xe0, 0x60, 0xcc, 0x1f, 0x0c, 
0x78, 0x7c, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x1b, 0x30, 0x1c, 0x00, 0x1b, 0x6c, 0x00, 0x00, 0x03, 0x00, 0xe0, 0x70, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x0c, 0x0c, 0x18, 0x30, 0x63, 0x0c, 0xd8, 0x19, 0x80, 0xc0, 0xcc, 0x18, 0x98, 0xc7, 0x86, 
0x06, 0x66, 0x0c, 0xcc, 0x19, 0x99, 0x33, 0x39, 0x9c, 0xcc, 0x66, 0xe7, 0x18, 0xc6, 0x66, 0x33, 
0x33, 0x00, 0xc6, 0x66, 0xd9, 0x99, 0x8c, 0xc6, 0xcc, 0xcc, 0xcc, 0x60, 0x60, 0xcc, 0x01, 0x8c, 
0xcc, 0xcc, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x0c, 0x0c, 0x30, 0x30, 0x63, 0x0c, 0xd8, 0x19, 0x80, 0xc0, 0xd8, 0x19, 0x98, 0xc7, 0x86, 
0x06, 0x66, 0x0c, 0xcc, 0x0d, 0xb0, 0x33, 0x39, 0x9c, 0xd8, 0x66, 0xa5, 0x18, 0xcc, 0x36, 0x33, 
0x36, 0x00, 0xc6, 0x6c, 0xcc, 0xf1, 0x8c, 0xc6, 0xcc, 0xcc, 0xcc, 0x60, 0x60, 0xcc, 0x00, 0xcd, 
0x86, 0xcc, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x0f, 0xcc, 0x30, 0x3c, 0x63, 0x0c, 0xd8, 0x19, 0x80, 0xfc, 0xf0, 0x09, 0x98, 0xc6, 0x86, 
0x06, 0x66, 0x0c, 0xcc, 0x07, 0xe0, 0x33, 0x79, 0xbc, 0xf0, 0x67, 0xa5, 0x98, 0xcc, 0x36, 0x33, 
0x36, 0x00, 0xc3, 0x6c, 0xcc, 0xf1, 0x8c, 0xc6, 0xcc, 0xcc, 0xcc, 0x7e, 0x7c, 0xcf, 0x80, 0xcd, 
0x86, 0xcd, 0xf3, 0xf1, 0xf3, 0xdf, 0x8f, 0x76, 0xef, 0x37, 0x37, 0x37, 0x7c, 0xe7, 0x66, 0x7c, 
0x7e, 0xf8, 0xf7, 0xf8, 0xcf, 0x9d, 0xd9, 0x9b, 0x36, 0x66, 0xcd, 0xe0, 0xc3, 0x30, 0xf9, 0x9c, 
0x3c, 0x0f, 0x3e, 0x3d, 0xe3, 0x98, 0xcc, 0xf8, 0x19, 0x83, 0xe3, 0x70, 0xd9, 0x98, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0x8c, 0xec, 0x3f, 0x1e, 0x63, 0x0c, 0xdf, 0x9f, 0xf8, 0xc6, 0xf8, 0x0d, 0x18, 0xcc, 0xc7, 
0xc7, 0xc6, 0x0c, 0xcf, 0x87, 0xe0, 0xe3, 0x59, 0xac, 0xf8, 0x67, 0xb5, 0x9f, 0xcc, 0x36, 0x33, 
0x36, 0x00, 0xc3, 0x4c, 0xcc, 0x61, 0x8c, 0xc6, 0xcc, 0xcc, 0xcc, 0x67, 0x66, 0xcc, 0xcf, 0xcf, 
0x86, 0xcd, 0x1b, 0x19, 0x9b, 0x19, 0xb1, 0x96, 0x81, 0xb7, 0x37, 0x34, 0x6c, 0xe7, 0x66, 0xc6, 
0x66, 0xcd, 0x81, 0x8d, 0x9b, 0xcd, 0x99, 0x9b, 0x36, 0x66, 0xcc, 0x60, 0xc3, 0x30, 0x1d, 0xb3, 
0x6c, 0x31, 0xb3, 0x33, 0x06, 0x18, 0xcc, 0xd8, 0x19, 0x83, 0x33, 0x40, 0xd9, 0x98, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x0c, 0x6c, 0x30, 0x07, 0x63, 0x0c, 0xd8, 0xd9, 0x8c, 0xc6, 0xdc, 0x0f, 0x18, 0xcc, 0xc6, 
0x66, 0x66, 0x0c, 0xcc, 0x0d, 0xb0, 0x1b, 0x59, 0xac, 0xdc, 0x67, 0xb9, 0x98, 0xcc, 0x36, 0x33, 
0xe6, 0x00, 0xc1, 0xcc, 0xcc, 0xf1, 0x8c, 0x7e, 0xcc, 0xcc, 0xcc, 0x63, 0x66, 0xcc, 0xc0, 0xcd, 
0x86, 0x3c, 0x7b, 0x19, 0xf3, 0x19, 0xbf, 0x9f, 0x87, 0x37, 0x37, 0x3c, 0x6c, 0xdb, 0x7e, 0xc6, 
0x66, 0xcd, 0x81, 0x8d, 0xb3, 0x67, 0x19, 0x9b, 0x36, 0x66, 0xcc, 0x7c, 0xfb, 0x3e, 0x7d, 0xf3, 
0x6c, 0x3f, 0xb3, 0x33, 0xe7, 0x18, 0xcc, 0xdf, 0x1f, 0xf3, 0x33, 0xc0, 0xd9, 0x98, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x0c, 0x6c, 0x30, 0x03, 0x63, 0x0c, 0xd8, 0xd9, 0x8c, 0xc6, 0xcc, 0x07, 0x18, 0xcf, 0xc6, 
0x66, 0x66, 0x18, 0xcc, 0x19, 0x98, 0x1b, 0x99, 0xcc, 0xcc, 0x67, 0x99, 0x98, 0xcc, 0x36, 0x33, 
0x06, 0x00, 0xc1, 0xc6, 0xd8, 0xf1, 0x8c, 0x06, 0xcc, 0xcc, 0xcc, 0x63, 0x66, 0xcc, 0xc0, 0xcd, 
0x86, 0x6d, 0x9b, 0x19, 0x9b, 0x19, 0xb0, 0x36, 0xc1, 0xbb, 0x3b, 0x36, 0x6c, 0xdb, 0x66, 0xc6, 
0x66, 0xcd, 0x81, 0x85, 0xb3, 0x67, 0x19, 0x8f, 0x36, 0x66, 0xcc, 0x66, 0xcb, 0x33, 0x0d, 0xb3, 
0x1c, 0x30, 0x33, 0x33, 0x03, 0x98, 0xcc, 0xd9, 0x99, 0x9b, 0x33, 0x60, 0x51, 0x98, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x0c, 0x6c, 0x18, 0x23, 0x63, 0x0d, 0x98, 0xd9, 0x8c, 0xc6, 0xcc, 0x06, 0x18, 0xdc, 0xe6, 
0x66, 0x66, 0x18, 0xcc, 0x19, 0x99, 0x1b, 0x99, 0xcc, 0xcd, 0xc7, 0x99, 0x98, 0xc6, 0x66, 0x33, 
0x03, 0x00, 0xc1, 0x83, 0xf1, 0x99, 0x8c, 0x06, 0xcc, 0xcc, 0xcc, 0x67, 0x66, 0xcc, 0xc1, 0x8c, 
0xcc, 0xcd, 0x99, 0x19, 0x9b, 0x11, 0xb8, 0x36, 0xc9, 0xbb, 0x3b, 0x33, 0xcc, 0xdb, 0x66, 0xc6, 
0x66, 0xcd, 0x81, 0x87, 0x1b, 0xcd, 0x99, 0x83, 0x36, 0x66, 0xcc, 0x66, 0xcb, 0x33, 0x19, 0xb2, 
0x6c, 0x38, 0x33, 0x33, 0x81, 0x98, 0xcc, 0x99, 0x99, 0x9b, 0x33, 0x30, 0x71, 0x98, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0f, 0xcc, 0xec, 0x0f, 0xbe, 0x63, 0x7b, 0x1f, 0x99, 0xf8, 0xc6, 0xc6, 0x1c, 0x1f, 0xd8, 0x67, 
0xc7, 0xc6, 0x3f, 0xef, 0xf1, 0x8d, 0xf3, 0x99, 0xcc, 0xc6, 0x87, 0x99, 0x98, 0xc3, 0xc6, 0x33, 
0x01, 0xf0, 0xc7, 0x00, 0xc3, 0x9d, 0xfe, 0x06, 0xff, 0xcf, 0xfe, 0x7e, 0x7c, 0xcf, 0x9f, 0x0c, 
0x78, 0xcc, 0xf8, 0xf1, 0xf3, 0x3f, 0xcf, 0xe6, 0x6f, 0x3b, 0x3b, 0x33, 0xcc, 0xdb, 0x66, 0x7c, 
0x66, 0xf8, 0xf1, 0x87, 0x0f, 0x9d, 0xdf, 0xc3, 0x3f, 0xe7, 0xfe, 0x7c, 0xfb, 0x3e, 0xf1, 0x9e, 
0x6c, 0x0f, 0xb3, 0x31, 0xe7, 0x18, 0xcd, 0x9f, 0x19, 0xf3, 0x33, 0x30, 0x31, 0xf8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xc0, 0x00, 0x06, 0x03, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x30, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xc0, 0x00, 0x0e, 0x03, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xc0, 0x00, 0x0c, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY MyriadPro10bRus_Font = {0x01, 16, 0, 16, 0, 0, 16,155, 0x0400, 0x04ff,
(PEGUSHORT *) MyriadPro10bRus_offset_table, &Tahoma10bTai_Font,
(PEGUBYTE *) MyriadPro10bRus_data_table};


