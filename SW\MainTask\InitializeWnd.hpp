#ifndef __INITIALIZE_WND_HPP__
#define __INITIALIZE_WND_HPP__

#include "Wnd.hpp"
#include "CheckCtrl.hpp"

class CInitializeWnd : public CWnd {
	public:
		enum{
			INIT_MODE_NONE = 0,
			INIT_MODE_MKD,
			INIT_MODE_TRANS,
			INIT_MODE_ALL,
			INIT_MODE_QA,
			MAX_INIT_MODE
		};
		
	private:
		enum {
			FOCUS_CLR_MKD = 0,
			FOCUS_CLR_TRANS,
			FOCUS_CLR_ALL,
		};

		CCheckCtrl *m_pChkClearMKD;
		CCheckCtrl *m_pChkClearTrans;
		CCheckCtrl *m_pChkClearAll;

		int m_nInitMode;
		int m_nWaitingTimeCnt;
		BOOL m_bStartInit;
		BOOL m_bShowResultMsg;

	public:
		CInitializeWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);
		~CInitializeWnd();

		virtual void OnKeyEvent(int nKey, DWORD nFlags);
		void DrawWaitingMsg(BYTE *pMsg);
		void DrawWaitingMsg();
		BOOL GetStartInitialize();
		BOOL GetShowResultMsg();
		void SetShowResultMsg(BOOL bShow);

		virtual void DrawWnd(BOOL bRedraw = TRUE);
		virtual void OnActivate();
		virtual void OnTimer();
		
		void SetFocus(int nFocus);
		void SetInitMode(int nMode);
		int  GetInitMode();
		bool IsNumChar(char ch);
		DWORD GetQAModeMMSI();			
		void JudgeInitMode();
		int  CloseAlert(int nKey, BOOL bMkdAlert = FALSE);
		void InitializeTrans(BOOL bInitMode = TRUE);

		void ResetUserPwd();
#ifdef EN_61993_ED3		
		void ResetPreSharedKey();
#endif	// End of (EN_61993_ED3)
		void ResetLCDSetting();
		void ResetSSDData(int nMode);
		void ResetVSDData(int nMode);
		void ResetEPFSType();
		void ResetEtcSetting(int nMode);
		void ResetIgnoreAlarms();
		void ResetEnOutRMC();
		void ResetAdvSetting();
		void ResetKeyBuzzerSetting();
		void ResetSecurityLog();
#ifdef _EN_POLICE_MODE_		
		void ResetEnableTransmiter();
#endif
		void ResetFavMsg();
		void ResetIOPortBaud();
		void ResetAlarmBuzzerSetting();

	private:
		void DrawFuncBtn();
		
		void ResetAndSyncTPBackupData();
		void StartInitializing();

		void ResetCpaTcpa();
		
		void ResetOwnShipInitPos();
		void ResetLocalTimeOffset();

#ifdef EN_61993_ED3
		void ResetEnExtEPFS();
		void ResetEnExtHeading();
		void ResetEnExtROT();
		void ResetEnSilentMode();
		void ResetEnALR14();
#endif	// End of (EN_61993_ED3)		
		
};

#endif	// End of __INITIALIZE_WND_HPP__

