/*...........................................................................*/
/*.                  File Name : MyriadPro12bEng.cpp                        .*/
/*.                                                                         .*/
/*.                       Date : 2008.10.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY MyriadPro12bGrk_Font;

/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

#if defined(_FONT_PLASTIMO_)
ROMDATA PEGUSHORT MyriadPro12bExt_offset_table[337] = {
0x0000,0x000a,0x0012,0x001c,0x0024,0x002f,0x0038,0x0042,0x004a,0x0054,0x005c,0x0066,0x006e,0x0078,0x0080,0x008b,
0x0096,0x00a1,0x00ab,0x00b3,0x00bb,0x00c3,0x00cb,0x00d3,0x00db,0x00e4,0x00ec,0x00f4,0x00fc,0x0107,0x0110,0x011b,
0x0124,0x012f,0x0138,0x0143,0x014c,0x0157,0x0160,0x016b,0x0174,0x0179,0x017e,0x0182,0x0186,0x018a,0x018e,0x0192,
0x0196,0x019a,0x019e,0x01a8,0x01b0,0x01b7,0x01bc,0x01c5,0x01ce,0x01d6,0x01de,0x01e3,0x01eb,0x01ef,0x01f7,0x01fd,
0x0205,0x020b,0x0214,0x0218,0x0223,0x022c,0x0237,0x0240,0x024b,0x0254,0x025d,0x0268,0x0271,0x027c,0x0285,0x0290,
0x0299,0x02a4,0x02ad,0x02bc,0x02ca,0x02d3,0x02d9,0x02e2,0x02e8,0x02f1,0x02f7,0x02ff,0x0306,0x030e,0x0315,0x031d,
0x0324,0x032c,0x0333,0x033b,0x0341,0x0349,0x0350,0x0358,0x035e,0x0369,0x0372,0x037d,0x0386,0x0391,0x039a,0x03a5,
0x03ae,0x03b9,0x03c2,0x03cd,0x03d7,0x03e5,0x03f1,0x03fb,0x0403,0x040d,0x0416,0x041d,0x0426,0x042d,0x0436,0x043d,
0x0444,0x0448,0x044c,0x0450,0x0454,0x0458,0x045c,0x0460,0x0464,0x0468,0x046c,0x0470,0x0474,0x0478,0x047c,0x0480,
0x0484,0x0488,0x048c,0x0495,0x0499,0x049d,0x04a1,0x04a5,0x04a9,0x04ad,0x04b1,0x04b5,0x04b9,0x04bd,0x04c1,0x04c5,
0x04c9,0x04d5,0x04df,0x04e3,0x04e7,0x04eb,0x04ef,0x04f3,0x04f7,0x04fb,0x04ff,0x0503,0x0507,0x050b,0x050f,0x0513,
0x051f,0x0529,0x052d,0x0531,0x0535,0x0539,0x053d,0x0541,0x0545,0x0549,0x054d,0x0551,0x0555,0x0559,0x055d,0x0561,
0x0565,0x0569,0x056d,0x0571,0x0575,0x0579,0x057d,0x0581,0x0585,0x0589,0x058d,0x0591,0x0595,0x0599,0x059d,0x05a1,
0x05a5,0x05a9,0x05ad,0x05b1,0x05b5,0x05b9,0x05bd,0x05c1,0x05c5,0x05c9,0x05cd,0x05d1,0x05d5,0x05d9,0x05dd,0x05e1,
0x05e5,0x05e9,0x05ed,0x05f1,0x05f5,0x05f9,0x05fd,0x0601,0x0605,0x0609,0x060d,0x0611,0x0615,0x0619,0x061d,0x0621,
0x0625,0x0629,0x062d,0x0631,0x0635,0x0639,0x063d,0x0641,0x0645,0x0649,0x064d,0x0658,0x0660,0x066d,0x067a,0x0685,
0x068e,0x0692,0x0696,0x069a,0x069e,0x06a2,0x06a6,0x06aa,0x06ae,0x06b2,0x06b6,0x06ba,0x06be,0x06c2,0x06c6,0x06ca,
0x06ce,0x06d2,0x06d6,0x06da,0x06de,0x06e2,0x06e6,0x06ea,0x06ee,0x06f6,0x06fd,0x0705,0x070b,0x0713,0x071a,0x071e,
0x0722,0x0726,0x072a,0x072e,0x0732,0x0736,0x073a,0x073e,0x0742,0x0746,0x074a,0x074e,0x0752,0x0756,0x075a,0x075e,
0x0762,0x0766,0x076a,0x0774,0x077d,0x0781,0x0785,0x0789,0x078d,0x0791,0x0795,0x0799,0x079d,0x07a1,0x07a5,0x07a9,
0x07ad,0x07b1,0x07b5,0x07b9,0x07bd,0x07c1,0x07c5,0x07c9,0x07cd,0x07d1,0x07d5,0x07d9,0x07dd,0x07e1,0x07e5,0x07e9,
0x07ed,
};


ROMDATA PEGUBYTE MyriadPro12bExt_data_table[4826] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x1e, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x07, 0x80, 0x03, 0x80, 0x00, 0x60, 0x00, 0x7e, 0x00, 
0x3b, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x03, 0x00, 0x01, 0x80, 0x00, 0x00, 0x07, 0xe0, 0x00, 
0xe0, 0x00, 0x0d, 0x80, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x30, 0x00, 0x00, 0x0f, 0x03, 
0xc3, 0xc0, 0x01, 0x80, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 
0x00, 0x78, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x37, 0x00, 0x0c, 
0x00, 0xf8, 0x00, 0x00, 0x03, 0x60, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x38, 0x00, 0x07, 0xc0, 
0x00, 0x6c, 0x00, 0x03, 0xc0, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x06, 0x00, 
0x06, 0xc0, 0x30, 0x00, 0x60, 0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x1e, 0x00, 0xc0, 0x00, 0x01, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0e, 0x00, 0x3c, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0xb8, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc1, 0xb0, 0xc0, 0x00, 0x0c, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0xf8, 
0x03, 0x00, 0x06, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x38, 
0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 
0x00, 0x38, 0x00, 0x01, 0x80, 0x60, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x19, 0x80, 0x00, 0x0f, 0x00, 
0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x03, 0x80, 0x00, 0x00, 
0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x0c, 0x00, 0x03, 0x03, 0xc0, 0xe0, 0x00, 0x0f, 0xc1, 0x81, 0xf1, 0xc0, 0xfc, 0x30, 0x3f, 0x36, 
0x7f, 0x00, 0x3d, 0xfc, 0x00, 0xcf, 0xc0, 0x0f, 0xc6, 0xcf, 0xe1, 0x8f, 0xe0, 0x07, 0xe3, 0x60, 
0x7c, 0x1c, 0x0f, 0xc7, 0x60, 0xfc, 0x18, 0x0f, 0xc3, 0x06, 0x0c, 0xc0, 0x60, 0xcc, 0x06, 0x7d, 
0x81, 0xbd, 0x99, 0x81, 0x86, 0x66, 0x0c, 0x66, 0x3b, 0x00, 0x01, 0x81, 0x8c, 0x0c, 0xdc, 0xdb, 
0x03, 0x06, 0x06, 0x60, 0xc0, 0xe6, 0x0c, 0x00, 0x60, 0xc7, 0xe6, 0x03, 0x06, 0x00, 0x07, 0x80, 
0x00, 0x78, 0x37, 0x07, 0x00, 0x38, 0x7f, 0xe0, 0x00, 0x1f, 0x83, 0x3f, 0x00, 0x7e, 0x3c, 0x3e, 
0x1c, 0x7c, 0x60, 0xf8, 0x01, 0xf7, 0xff, 0xe0, 0x7f, 0x83, 0xff, 0x01, 0x83, 0x0f, 0x98, 0x30, 
0x01, 0x83, 0x1b, 0x18, 0x30, 0xf1, 0x83, 0x0f, 0x98, 0x30, 0x01, 0x86, 0x18, 0x30, 0x30, 0xe3, 
0x0c, 0x3b, 0xfc, 0x33, 0xfc, 0x63, 0xfd, 0xf0, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x1c, 0x07, 0xf8, 0x1c, 0x03, 
0xf0, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x07, 
0xf8, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x70, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x1e, 0x0f, 0x87, 0x81, 0x80, 0xe0, 0x00, 0x18, 0x07, 0x07, 0x03, 0x61, 0x80, 0x00, 0x60, 0x1c, 
0x61, 0xc0, 0x3d, 0x87, 0x0f, 0xec, 0x07, 0xcc, 0x03, 0x8c, 0x00, 0x0c, 0x00, 0x06, 0x01, 0xc1, 
0xc0, 0x76, 0x18, 0x01, 0xc1, 0x80, 0x00, 0x18, 0x03, 0x86, 0x0c, 0xc0, 0x60, 0xdf, 0xc6, 0x61, 
0xbd, 0x99, 0x99, 0x81, 0x86, 0x00, 0x0c, 0xf6, 0x63, 0x00, 0x01, 0x81, 0x8c, 0x0c, 0xcc, 0xfb, 
0x03, 0x06, 0x06, 0x70, 0xc3, 0x87, 0x0c, 0x00, 0x70, 0xc1, 0x8e, 0x03, 0x86, 0x00, 0x0c, 0xe3, 
0xe0, 0xce, 0x1c, 0x0d, 0xe3, 0xe0, 0xc6, 0x00, 0x00, 0x18, 0xc6, 0x31, 0x80, 0x63, 0x18, 0xe0, 
0x71, 0xc1, 0xfb, 0x80, 0x07, 0x01, 0x83, 0x06, 0x0c, 0x1e, 0x18, 0x31, 0x83, 0x18, 0x18, 0x31, 
0xf1, 0x83, 0x0e, 0x18, 0x30, 0xf1, 0x83, 0x19, 0x98, 0x30, 0x00, 0xcf, 0x30, 0x78, 0x19, 0x87, 
0x86, 0x60, 0x18, 0xe0, 0x18, 0x00, 0x18, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x36, 0x0f, 0x00, 0x30, 0x0e, 
0x30, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 
0xc1, 0x8c, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xc3, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x1e, 0x00, 0x07, 0x80, 0x01, 0xe0, 0x00, 0x30, 0x00, 0x0c, 0x00, 0x03, 0x00, 0x00, 0xc0, 0x00, 
0x60, 0xc0, 0x3d, 0x83, 0x00, 0xcc, 0x00, 0x0c, 0x00, 0x0c, 0x00, 0x0c, 0x00, 0x06, 0x00, 0x03, 
0x00, 0x00, 0x30, 0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x06, 0x0c, 0xc0, 0xff, 0xec, 0x06, 0x01, 
0x81, 0x81, 0x81, 0x81, 0x86, 0x00, 0x0c, 0x06, 0x63, 0x00, 0x01, 0x81, 0x8c, 0x0c, 0xf8, 0xc3, 
0x03, 0x06, 0x06, 0x78, 0xc0, 0x07, 0x8c, 0x00, 0x78, 0xc0, 0x00, 0x03, 0xc6, 0x00, 0x18, 0x60, 
0x01, 0x86, 0x00, 0x18, 0x60, 0x01, 0x86, 0x00, 0x00, 0x18, 0x60, 0x30, 0xc0, 0x61, 0x80, 0xc0, 
0x01, 0x80, 0x03, 0x00, 0x06, 0x00, 0x03, 0x06, 0x0c, 0x18, 0x18, 0x31, 0x83, 0x00, 0x18, 0x30, 
0x01, 0x83, 0x00, 0x18, 0x30, 0x61, 0x83, 0x00, 0x18, 0x30, 0x00, 0xcf, 0x30, 0x00, 0x19, 0x80, 
0x06, 0x60, 0x30, 0x00, 0x18, 0x00, 0x30, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x1c, 0x0f, 0x00, 0x00, 0x0c, 
0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 
0xc1, 0x80, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xb0, 0x78, 0x60, 0x07, 0xd8, 0x01, 0xf6, 0x00, 0x7d, 0x80, 0x1f, 
0x60, 0x63, 0xf1, 0x81, 0x8f, 0xcc, 0x03, 0x8c, 0x03, 0x8c, 0x03, 0xcc, 0x01, 0xe6, 0x01, 0xc6, 
0x00, 0x3f, 0x60, 0x03, 0xf6, 0x00, 0x3f, 0x60, 0x03, 0xf6, 0x0c, 0xfe, 0x60, 0xcf, 0xe6, 0x31, 
0x99, 0x99, 0x99, 0x99, 0x86, 0x66, 0x0c, 0x66, 0xc3, 0x19, 0x9d, 0x81, 0x8c, 0x0c, 0xc0, 0xc3, 
0x03, 0x07, 0x87, 0x78, 0xcf, 0xe7, 0x8c, 0xfe, 0x78, 0xcf, 0xe7, 0xf3, 0x66, 0x7f, 0x30, 0x31, 
0xe3, 0x03, 0x1e, 0x30, 0x31, 0xe3, 0x06, 0x01, 0xe7, 0x98, 0x6f, 0x30, 0xde, 0x61, 0xbc, 0xc0, 
0x7d, 0x80, 0xfb, 0x01, 0xf6, 0x03, 0xe3, 0x0f, 0x8c, 0x3e, 0x18, 0x7d, 0x83, 0x31, 0x98, 0x33, 
0x19, 0x83, 0x31, 0x98, 0x33, 0x19, 0x83, 0x31, 0x98, 0x33, 0x18, 0xcf, 0x36, 0x31, 0x8f, 0x18, 
0x63, 0xc0, 0x33, 0xf8, 0x31, 0xf8, 0x33, 0xfb, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x30, 0xf6, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0xc7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x3c, 0x0f, 0x01, 0xef, 0x18, 
0xd8, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0xf8, 
0xc3, 0xe0, 0xcf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x8e, 0x38, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x33, 0x19, 0x8c, 0xc6, 0x61, 0xb0, 0xcc, 0x60, 0x0c, 0x18, 0x03, 0x06, 0x00, 0xc1, 0x80, 0x30, 
0x60, 0x66, 0x71, 0x81, 0x99, 0xcc, 0x06, 0xcc, 0x06, 0xcc, 0x06, 0x6c, 0x03, 0x36, 0x03, 0x66, 
0x00, 0x67, 0x60, 0x06, 0x76, 0x00, 0x67, 0x60, 0x06, 0x76, 0x0c, 0xe3, 0x60, 0xce, 0x36, 0x31, 
0x99, 0x99, 0x99, 0x99, 0x86, 0x66, 0x0c, 0x67, 0x83, 0x31, 0xb1, 0x81, 0x8c, 0x0c, 0xc0, 0xc3, 
0x03, 0x67, 0x06, 0x6c, 0xce, 0x36, 0xcc, 0xe3, 0x6c, 0xce, 0x37, 0x1b, 0x66, 0x71, 0xb0, 0x33, 
0x33, 0x03, 0x33, 0x30, 0x33, 0x33, 0x06, 0x03, 0x3c, 0xd8, 0xce, 0x31, 0x9c, 0x63, 0x38, 0x60, 
0xc0, 0xc1, 0x81, 0x83, 0x03, 0x06, 0x03, 0x06, 0x0c, 0x18, 0x18, 0x31, 0x83, 0x31, 0x98, 0x33, 
0x19, 0x83, 0x31, 0x98, 0x33, 0x19, 0x83, 0x31, 0x98, 0x33, 0x18, 0xcf, 0x33, 0x7b, 0x0f, 0x0c, 
0xc3, 0xc0, 0x60, 0x30, 0x60, 0x30, 0x60, 0x31, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x31, 0x9c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x98, 0x66, 0x1b, 0x03, 0x39, 0x98, 
0xd9, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0x80, 
0xc1, 0x81, 0x99, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x86, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x33, 0x01, 0x8c, 0xc0, 0x61, 0xb0, 0x0c, 0x60, 0x18, 0x18, 0x06, 0x06, 0x01, 0x81, 0x80, 0x60, 
0x60, 0x6c, 0x33, 0xf1, 0xb0, 0xcf, 0xcc, 0x6f, 0xcc, 0x6f, 0xec, 0x6f, 0xe6, 0x37, 0xe6, 0x36, 
0x3c, 0xc3, 0x63, 0xcc, 0x36, 0x3c, 0xc3, 0x63, 0xcc, 0x37, 0xfc, 0xc3, 0x7f, 0xcc, 0x36, 0x31, 
0x99, 0x99, 0x99, 0x99, 0x86, 0x66, 0x0c, 0x67, 0x83, 0x61, 0xe1, 0x81, 0x8c, 0x0c, 0xc0, 0xc3, 
0x33, 0x06, 0x06, 0x66, 0xcc, 0x36, 0x6c, 0xc3, 0x66, 0xcc, 0x36, 0x1b, 0x36, 0x61, 0xb0, 0x36, 
0x1b, 0x03, 0x61, 0xb0, 0x36, 0x1b, 0x07, 0xe6, 0x18, 0xdf, 0x8c, 0x3f, 0x18, 0x7e, 0x30, 0x38, 
0xc0, 0x71, 0x80, 0xe3, 0x01, 0xc6, 0x03, 0x06, 0x0c, 0x18, 0x7e, 0x31, 0x83, 0x31, 0x98, 0x33, 
0x19, 0x83, 0x31, 0x98, 0x33, 0x19, 0x83, 0x31, 0x98, 0x33, 0x18, 0x6f, 0x63, 0x7b, 0x06, 0x0c, 
0xc1, 0x80, 0xc0, 0x60, 0x60, 0x30, 0xc0, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x33, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x8c, 0x06, 0x1b, 0xf0, 0x30, 0xd9, 
0x9b, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x80, 
0xc1, 0x87, 0xc1, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x33, 0x07, 0x8c, 0xc1, 0xe3, 0x18, 0x3c, 0x60, 0x18, 0x18, 0x06, 0x06, 0x01, 0x81, 0x80, 0x60, 
0x60, 0x6c, 0x31, 0x81, 0xb0, 0xcc, 0x0f, 0xec, 0x0f, 0xec, 0x0f, 0xec, 0x07, 0xf6, 0x07, 0xf6, 
0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x31, 
0x99, 0x99, 0x99, 0x99, 0x86, 0x66, 0x0c, 0x66, 0xc3, 0xc1, 0xc1, 0x81, 0x8c, 0x0c, 0xc0, 0xc3, 
0x03, 0x0e, 0x0e, 0x66, 0xcc, 0x36, 0x6c, 0xc3, 0x66, 0xcc, 0x36, 0x1b, 0x1e, 0x61, 0xb0, 0x36, 
0x1b, 0x03, 0x61, 0xb0, 0x36, 0x1b, 0x06, 0x06, 0x1f, 0xd8, 0xcc, 0x33, 0x98, 0x63, 0x30, 0x0c, 
0x70, 0x18, 0xe0, 0x31, 0xc0, 0x63, 0x83, 0x06, 0x0c, 0x18, 0x18, 0x7d, 0x83, 0x31, 0x98, 0x33, 
0x19, 0x83, 0x31, 0x98, 0x33, 0x19, 0x83, 0x31, 0x98, 0x33, 0x18, 0x79, 0xe3, 0x7b, 0x06, 0x06, 
0xc1, 0x80, 0xc0, 0x60, 0xc0, 0x60, 0xc0, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x33, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x8c, 0x1e, 0x3f, 0x80, 0xff, 0xdb, 
0x1b, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xe0, 
0xc1, 0x80, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3f, 0x1d, 0x8f, 0xc7, 0x63, 0xf8, 0xec, 0x60, 0x18, 0x18, 0x06, 0x06, 0x01, 0x81, 0x80, 0x60, 
0x60, 0x6c, 0x31, 0x81, 0xb0, 0xcc, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x06, 0x06, 0x06, 0x06, 
0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x31, 
0x99, 0x99, 0x99, 0x99, 0x86, 0x66, 0x0c, 0x66, 0x63, 0x61, 0xe1, 0x81, 0x8c, 0x0c, 0xc0, 0xc3, 
0x03, 0x1e, 0x06, 0x63, 0xcc, 0x36, 0x3c, 0xc3, 0x63, 0xcc, 0x36, 0x1b, 0x1e, 0x61, 0xb0, 0x36, 
0x1b, 0x03, 0x61, 0xb0, 0x36, 0x1b, 0x06, 0x06, 0x18, 0x18, 0xcc, 0x31, 0x98, 0x63, 0x30, 0x06, 
0x18, 0x0c, 0x30, 0x18, 0x60, 0x30, 0xc3, 0x06, 0x0c, 0x18, 0x18, 0x31, 0x83, 0x31, 0x98, 0x33, 
0x19, 0x83, 0x31, 0x98, 0x33, 0x19, 0x83, 0x31, 0x98, 0x33, 0x18, 0x79, 0xe3, 0x7e, 0x06, 0x07, 
0x81, 0x81, 0x80, 0xc0, 0xc0, 0x61, 0x80, 0xc1, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x33, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xfc, 0x76, 0x31, 0x83, 0xb0, 0x1b, 
0x1b, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x30, 
0xc1, 0x80, 0x6f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x61, 0x99, 0x98, 0x66, 0x63, 0x18, 0xcc, 0x30, 0x18, 0x0c, 0x06, 0x03, 0x01, 0x80, 0xc0, 0x60, 
0x60, 0xcc, 0x31, 0x83, 0x30, 0xcc, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x06, 0x06, 0x06, 0x03, 
0x0c, 0xc3, 0x30, 0xcc, 0x33, 0x0c, 0xc3, 0x30, 0xcc, 0x36, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x31, 
0x99, 0x99, 0x99, 0x99, 0x86, 0x66, 0x0c, 0x66, 0x63, 0x31, 0xb1, 0x81, 0x8c, 0x0c, 0xc0, 0xc3, 
0x03, 0x06, 0x06, 0x61, 0xcc, 0x36, 0x1c, 0xc3, 0x61, 0xcc, 0x36, 0x1b, 0x0e, 0x61, 0x98, 0x66, 
0x19, 0x86, 0x61, 0x98, 0x66, 0x19, 0x86, 0x06, 0x18, 0x18, 0x6c, 0x30, 0xd8, 0x61, 0xb0, 0x06, 
0x0c, 0x0c, 0x18, 0x18, 0x30, 0x30, 0x63, 0x06, 0x0c, 0x18, 0x18, 0x31, 0x83, 0x31, 0x98, 0x33, 
0x19, 0x83, 0x31, 0x98, 0x33, 0x19, 0x83, 0x31, 0x98, 0x33, 0x18, 0x79, 0xe1, 0xfe, 0x06, 0x07, 
0x81, 0x81, 0x81, 0x81, 0x80, 0xc1, 0x81, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x63, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x18, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x06, 0x66, 0x61, 0x83, 0x30, 0x0e, 
0x33, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x18, 
0xc1, 0x80, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x61, 0x9b, 0x98, 0x66, 0xe6, 0x0c, 0xcc, 0x18, 0x0c, 0x06, 0x03, 0x01, 0x80, 0xc0, 0x60, 0x30, 
0x61, 0xc6, 0x71, 0x87, 0x19, 0xcc, 0x06, 0x0c, 0x06, 0x0c, 0x06, 0x0c, 0x03, 0x06, 0x03, 0x03, 
0x8c, 0x67, 0x38, 0xc6, 0x73, 0x8c, 0x67, 0x38, 0xc6, 0x76, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x31, 
0x99, 0x99, 0x99, 0x99, 0x86, 0x66, 0x1c, 0x66, 0x33, 0x31, 0x99, 0x81, 0x8c, 0x0c, 0xc0, 0xc3, 
0x03, 0x06, 0x06, 0x61, 0xcc, 0x36, 0x1c, 0xc3, 0x61, 0xcc, 0x36, 0x1b, 0x06, 0x61, 0x9c, 0xe3, 
0x31, 0xce, 0x33, 0x1c, 0xe3, 0x30, 0xc6, 0x03, 0x3c, 0x18, 0x6c, 0x30, 0xd8, 0x61, 0xb0, 0xcc, 
0xcd, 0x99, 0x9b, 0x33, 0x36, 0x66, 0x63, 0x06, 0x0c, 0x18, 0x18, 0x30, 0xc6, 0x33, 0x8c, 0x63, 
0x38, 0xc6, 0x33, 0x8c, 0x63, 0x38, 0xc6, 0x33, 0x8c, 0x63, 0x38, 0x79, 0xc1, 0xce, 0x06, 0x03, 
0x01, 0x83, 0x01, 0x81, 0x81, 0x83, 0x01, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0xe1, 0x98, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x30, 0xce, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x06, 0x6e, 0x61, 0x83, 0x78, 0x0e, 
0x71, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x99, 0x98, 
0xc1, 0x80, 0x60, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0xc0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0xc0, 0xcf, 0xb0, 0x33, 0xee, 0x0c, 0x7e, 0x0f, 0xc7, 0xc3, 0xf1, 0xf0, 0xf8, 0x7c, 0x3f, 0x1f, 
0x7f, 0x03, 0xf1, 0xfc, 0x0f, 0xcf, 0xe3, 0xef, 0xe3, 0xef, 0xe3, 0xef, 0xf1, 0xf7, 0xf1, 0xf0, 
0xf8, 0x3f, 0x0f, 0x83, 0xf0, 0xf8, 0x3f, 0x0f, 0x83, 0xf6, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x31, 
0x99, 0x99, 0x99, 0x99, 0xfc, 0x66, 0x70, 0x66, 0x1b, 0x1d, 0x9d, 0xfd, 0x8f, 0xec, 0xfe, 0xc3, 
0xfb, 0x07, 0xf6, 0x60, 0xcc, 0x36, 0x0c, 0xc3, 0x60, 0xcc, 0x36, 0x1b, 0x06, 0x61, 0x87, 0x81, 
0xe0, 0x78, 0x1e, 0x07, 0x81, 0xe0, 0x7f, 0xf1, 0xe7, 0xd8, 0x6c, 0x30, 0xd8, 0x61, 0xb0, 0x78, 
0xf8, 0xf1, 0xf1, 0xe1, 0xe3, 0xc7, 0xc0, 0x03, 0x8c, 0x0e, 0x18, 0x1c, 0x7c, 0x1f, 0x87, 0xc1, 
0xf8, 0x7c, 0x1f, 0x87, 0xc1, 0xf8, 0x7c, 0x1f, 0x87, 0xc1, 0xfc, 0x30, 0xc1, 0x8c, 0x06, 0x03, 
0x01, 0x87, 0xff, 0xfb, 0xfd, 0xff, 0xff, 0xf9, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0xf0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x03, 0xe0, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x03, 0x3e, 0xc1, 0xf9, 0xcf, 0xdf, 
0xc3, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf1, 0xf0, 
0x00, 0xe0, 0xc0, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xc0, 0x00, 0x00, 
0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x03, 0x07, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x30, 0x00, 0x00, 0x06, 0x00, 0x61, 0x80, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x06, 0x01, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0xc1, 0x80, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0xe0, 
0xc0, 0x0f, 0x81, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xc0, 0x00, 0x00, 
0x00, 0xc6, 0x00, 0x0c, 0x60, 0x00, 0xc6, 0x03, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x03, 0x30, 0x00, 0x00, 0x06, 0x00, 0x60, 0xc0, 0x60, 0x00, 0x00, 0x01, 0x86, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x0f, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x60, 0xc0, 0x00, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x1e, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x60, 
0x60, 0xc6, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x60, 0x00, 0x00, 
0x00, 0x7c, 0x00, 0x07, 0xc0, 0x00, 0x7c, 0x07, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0xdc, 0x00, 0x00, 0x3c, 0x01, 0xc1, 0xc0, 0xc0, 0x00, 0x00, 0x03, 0x8e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x18, 0x06, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x1c, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x01, 0xc1, 0x80, 0x00, 0x03, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x0c, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0xc0, 
0xe1, 0xc0, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};
#else
ROMDATA PEGUSHORT MyriadPro12bExt_offset_table[337] = {
0x0000,0x000d,0x0017,0x0024,0x002e,0x003b,0x0046,0x0051,0x0059,0x0064,0x006c,0x0077,0x007f,0x008a,0x0092,0x009e,
0x00ac,0x00b8,0x00c2,0x00cc,0x00d6,0x00e0,0x00ea,0x00f4,0x00fe,0x0108,0x0112,0x011c,0x0126,0x0132,0x013c,0x0148,
0x0152,0x015e,0x0168,0x0174,0x017e,0x018a,0x0194,0x01a1,0x01ac,0x01b3,0x01b8,0x01bf,0x01c4,0x01cb,0x01d0,0x01d7,
0x01dc,0x01e3,0x01e8,0x01f7,0x0201,0x0209,0x020f,0x021a,0x0224,0x022e,0x0237,0x023c,0x0245,0x024a,0x0253,0x025b,
0x0264,0x026c,0x0275,0x027a,0x0286,0x0290,0x029c,0x02a6,0x02b2,0x02bc,0x02c9,0x02d5,0x02df,0x02eb,0x02f5,0x0301,
0x030b,0x0317,0x0321,0x0333,0x0343,0x034f,0x0356,0x0362,0x036a,0x0376,0x037d,0x0387,0x038f,0x0399,0x03a1,0x03ab,
0x03b3,0x03bd,0x03c5,0x03d0,0x03d7,0x03e2,0x03ec,0x03f7,0x03fe,0x040a,0x0414,0x0420,0x042a,0x0436,0x0440,0x044c,
0x0456,0x0462,0x046c,0x0478,0x0483,0x0493,0x04a1,0x04ad,0x04b7,0x04c3,0x04cd,0x04d6,0x04e0,0x04e9,0x04f3,0x04fc,
0x0501,0x0511,0x0521,0x0531,0x0541,0x0551,0x0561,0x0571,0x0581,0x0591,0x05a1,0x05b1,0x05c1,0x05d1,0x05e1,0x05f1,
0x05fd,0x060d,0x061d,0x0627,0x0637,0x0647,0x0657,0x0667,0x0677,0x0687,0x0697,0x06a7,0x06b7,0x06c7,0x06d7,0x06e7,
0x06f7,0x0704,0x070e,0x071e,0x072e,0x073e,0x074e,0x075e,0x076e,0x077e,0x078e,0x079e,0x07ae,0x07be,0x07ce,0x07de,
0x07ec,0x07f8,0x0808,0x0818,0x0828,0x0838,0x0848,0x0858,0x0862,0x0872,0x0882,0x0892,0x08a2,0x08b2,0x08c2,0x08d2,
0x08e2,0x08f2,0x0902,0x0912,0x0922,0x0932,0x0942,0x0952,0x0962,0x0972,0x0982,0x0992,0x09a2,0x09b2,0x09c3,0x09d4,
0x09e5,0x09f6,0x0a07,0x0a18,0x0a29,0x0a3a,0x0a4b,0x0a5c,0x0a6d,0x0a7e,0x0a8f,0x0aa0,0x0ab1,0x0ac2,0x0ad2,0x0ae2,
0x0af2,0x0b02,0x0b12,0x0b22,0x0b32,0x0b3e,0x0b48,0x0b54,0x0b5e,0x0b69,0x0b73,0x0b7f,0x0b89,0x0b99,0x0ba9,0x0bb3,
0x0bbb,0x0bcb,0x0bdb,0x0beb,0x0bfb,0x0c0b,0x0c1c,0x0c2c,0x0c3c,0x0c4c,0x0c55,0x0c62,0x0c6c,0x0c7c,0x0c8b,0x0c97,
0x0ca1,0x0cb1,0x0cc1,0x0cd1,0x0ce1,0x0cf1,0x0d01,0x0d11,0x0d21,0x0d31,0x0d41,0x0d51,0x0d61,0x0d71,0x0d81,0x0d91,
0x0da1,0x0db1,0x0dc1,0x0dd1,0x0de1,0x0df1,0x0e01,0x0e11,0x0e21,0x0e2b,0x0e33,0x0e3e,0x0e45,0x0e55,0x0e65,0x0e75,
0x0e85,0x0e95,0x0ea5,0x0eb5,0x0ec5,0x0ed5,0x0ee5,0x0ef5,0x0f05,0x0f0f,0x0f19,0x0f29,0x0f39,0x0f49,0x0f59,0x0f69,
0x0f79,0x0f89,0x0f99,0x0fa9,0x0fb9,0x0fc9,0x0fd9,0x0fe9,0x0ff9,0x1009,0x1019,0x1029,0x1039,0x1049,0x1059,0x1069,
0x1079,0x1089,0x1099,0x10a9,0x10b9,0x10c9,0x10d9,0x10e9,0x10f9,0x1109,0x1119,0x1129,0x1139,0x1149,0x1159,0x1169,
0x1179,
};

ROMDATA PEGUBYTE MyriadPro12bExt_data_table[10640] = {
0x00, 0x00, 0x00, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x63, 0x00, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x18, 0xc0, 0x00, 0x00, 0x00, 0x01, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 
0x00, 0x01, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x60, 0x00, 0x0e, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 0x00, 0xc6, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x19, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x07, 0x00, 0x00, 0x60, 0x00, 
0x36, 0x00, 0x06, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x00, 0x0d, 0x80, 0x00, 0x38, 0x00, 0x01, 0x98, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x38, 0x0d, 0x80, 0x00, 0x00, 0x07, 0x20, 0x00, 0x0c, 0xc0, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0xc0, 0x00, 0x00, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 
0x00, 0x01, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x0d, 0x80, 0x00, 
0x30, 0x00, 0x38, 0x00, 0x00, 0x00, 0x06, 0xc0, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 
0x72, 0x00, 0x00, 0x00, 0x00, 0x06, 0x60, 0x00, 0x11, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x70, 0x00, 0x00, 0x03, 0x80, 0x00, 0x33, 0x01, 0x80, 0x00, 0x30, 0x00, 0x1b, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x80, 0x00, 0x6c, 0x1b, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0xc0, 0x0c, 0x00, 
0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x0f, 0xc0, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x0d, 0x80, 0x00, 0x60, 0x00, 
0x1c, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xc0, 0x00, 0x78, 0x00, 0x03, 0x00, 0x00, 
0x00, 0x00, 0x07, 0x00, 0x00, 0x6c, 0x00, 0x00, 0xf0, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x6c, 0x00, 0x00, 0x00, 0x00, 0x09, 0xc0, 0xfc, 0x07, 0x80, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x00, 0x00, 0x78, 
0x00, 0x03, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 
0x60, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 
0x9c, 0x00, 0x01, 0xf8, 0x00, 0x03, 0xc0, 0x00, 0x11, 0x01, 0xc0, 0xd8, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xd8, 0x00, 0x00, 0x06, 0xc0, 0x00, 0x33, 0x03, 0x00, 0x00, 0x30, 0x00, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xdc, 0x00, 0x00, 0x00, 0x77, 0x00, 0x00, 0x00, 0x1d, 
0xc0, 0x00, 0x00, 0x07, 0x70, 0x00, 0x00, 0x01, 0xfc, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 
0x1d, 0x80, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x38, 0x0e, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x01, 0x80, 0x18, 0x00, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x03, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x18, 0xc0, 0x00, 0x06, 0x70, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x06, 0x30, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x30, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x08, 0x00, 0x30, 0x00, 0x00, 0x00, 0xc0, 0x03, 0x01, 0xd9, 0xc0, 0x06, 0x00, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x19, 0x80, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x18, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x30, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x11, 0x02, 0x20, 0x00, 0x0f, 0xc0, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x67, 
0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x0f, 
0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x01, 0xdc, 0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 
0x1f, 0xc0, 0x00, 0x00, 0x07, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x30, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x03, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x07, 0x00, 0x00, 0x0e, 0x03, 0x30, 0x1c, 0x00, 0x00, 0x7e, 0x06, 0x0f, 0xc3, 0x81, 0xf8, 0x30, 
0x3f, 0x0d, 0xbf, 0x80, 0x06, 0x67, 0xf0, 0x07, 0xff, 0xe0, 0x03, 0xfe, 0x33, 0x3f, 0xe0, 0xc3, 
0xfe, 0x00, 0x3f, 0xe3, 0x60, 0x7f, 0x07, 0x01, 0xfc, 0x33, 0x07, 0xf0, 0x30, 0x1f, 0xc0, 0xc3, 
0x01, 0xb0, 0x06, 0x03, 0x7c, 0x0f, 0xda, 0xfd, 0xef, 0xd2, 0xfc, 0xcf, 0xc0, 0xfc, 0x7c, 0xc3, 
0x3f, 0x1d, 0x83, 0x30, 0x00, 0x03, 0x00, 0xcc, 0x03, 0x31, 0x99, 0x98, 0x06, 0x06, 0x03, 0x30, 
0x08, 0x18, 0xc0, 0x20, 0x03, 0x00, 0x8d, 0x86, 0x00, 0x60, 0x10, 0x00, 0x3e, 0x00, 0x00, 0xf8, 
0x19, 0x83, 0xe0, 0x6c, 0x07, 0xff, 0xc0, 0x00, 0x1f, 0xc0, 0x33, 0xf8, 0x00, 0x3f, 0x81, 0xb1, 
0xf8, 0x18, 0x7e, 0x1c, 0x1f, 0x80, 0x07, 0xe3, 0x67, 0xfe, 0x61, 0xff, 0x98, 0x6f, 0xfc, 0xc3, 
0x01, 0x8e, 0x4c, 0x06, 0x00, 0x30, 0x18, 0xcc, 0xce, 0x62, 0x23, 0x01, 0x8d, 0x8c, 0x06, 0x00, 
0x18, 0x20, 0xc0, 0xe0, 0x30, 0x30, 0x70, 0xc0, 0xdf, 0xf0, 0x63, 0xfe, 0x18, 0x7f, 0xc6, 0xcc, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0x81, 
0xff, 0xe1, 0xff, 0xe0, 0x1e, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 
0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x3e, 
0x10, 0x04, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 
0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf3, 0x01, 0xe0, 0x01, 0x3f, 
0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0xff, 0x8f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xee, 0x00, 0x00, 0x00, 0x3b, 0x00, 0x00, 
0x00, 0x0e, 0xc0, 0x00, 0x00, 0x03, 0xb0, 0x01, 0xdc, 0x00, 0xfe, 0x00, 0x77, 0x00, 0x07, 0x80, 
0x1d, 0xc0, 0x0f, 0xc0, 0x07, 0x70, 0x03, 0xc0, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x03, 0xf0, 0x00, 0x1f, 0xc3, 0x63, 0x06, 0x60, 0x03, 0xe0, 
0x00, 0x1f, 0xfe, 0x1f, 0xfe, 0x7f, 0xc6, 0xc7, 0xff, 0x87, 0xff, 0x87, 0xff, 0x87, 0xff, 0x87, 
0xff, 0x80, 0x1c, 0x03, 0xff, 0xc3, 0xff, 0xc3, 0xff, 0xc0, 0x00, 0x38, 0x04, 0x40, 0xff, 0xe0, 
0x0c, 0x03, 0xe8, 0x18, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0x80, 0x1f, 0xf9, 0x81, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 
0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 
0xe7, 0xfc, 0x00, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x00, 


0x07, 0x01, 0xf8, 0x0e, 0x01, 0xe0, 0x1c, 0x00, 0x01, 0xc3, 0x0c, 0x38, 0x66, 0xc7, 0x0c, 0x30, 
0xe1, 0x87, 0x30, 0xe0, 0x06, 0xc6, 0x1c, 0x01, 0xb0, 0x03, 0xf3, 0x00, 0x1e, 0x30, 0x00, 0xc3, 
0x00, 0x00, 0x30, 0x01, 0xc1, 0xc1, 0x8d, 0x87, 0x06, 0x1e, 0x1c, 0x18, 0x30, 0x70, 0x61, 0x83, 
0x01, 0xb0, 0x0f, 0xff, 0xb0, 0x03, 0x16, 0x30, 0x03, 0x0c, 0x30, 0xc3, 0x00, 0x30, 0x0c, 0xc3, 
0x03, 0x37, 0x86, 0x30, 0x00, 0x03, 0x00, 0xcc, 0x03, 0x33, 0x1b, 0x18, 0x06, 0x06, 0x03, 0x38, 
0x08, 0x30, 0xe0, 0x20, 0x03, 0x80, 0x87, 0x0c, 0x00, 0x70, 0x10, 0x00, 0xe3, 0x87, 0xe3, 0x8e, 
0x0f, 0x0e, 0x38, 0xd8, 0x1c, 0x30, 0x00, 0x00, 0x18, 0x60, 0x63, 0x0c, 0x00, 0x30, 0xc0, 0xe3, 
0x0c, 0x30, 0xc3, 0x36, 0x30, 0xc0, 0x0c, 0x31, 0xc0, 0x60, 0x60, 0x18, 0x18, 0xc0, 0xc0, 0xc3, 
0x01, 0x93, 0x8c, 0x06, 0x3f, 0x30, 0x18, 0x78, 0xc0, 0x62, 0x23, 0x01, 0x9b, 0x0c, 0x06, 0x00, 
0x18, 0x70, 0xc1, 0xb0, 0x30, 0x30, 0xd8, 0xc0, 0xc0, 0x30, 0xc0, 0x06, 0x18, 0x00, 0xc3, 0x8c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x30, 0xc1, 
0x00, 0x21, 0x00, 0x20, 0x30, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0xe3, 
0x90, 0x04, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x80, 0x01, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x01, 0x88, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x00, 0x70, 0x00, 0x38, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x1f, 
0xc0, 0x03, 0x80, 0x1c, 0x1c, 0x00, 0xe0, 0x07, 0x07, 0x00, 0xee, 0x01, 0xc1, 0xc0, 0x3f, 0x80, 
0x70, 0x70, 0x0f, 0xe0, 0x1c, 0x1c, 0x03, 0xf8, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x0c, 0x38, 0x00, 0x70, 0x61, 0xc3, 0x0c, 0x60, 0x06, 0x30, 
0x00, 0x10, 0x02, 0x10, 0x02, 0x00, 0xc3, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0x38, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x41, 0x80, 0x38, 0x04, 0x41, 0x98, 0x00, 
0x18, 0x0e, 0x30, 0x30, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x30, 0xc0, 0x01, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x00, 0x00, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x0d, 0x80, 0x00, 0x1b, 0x00, 0x00, 0x36, 0x00, 0x01, 0x83, 0x00, 0x30, 0x60, 0x06, 0x0c, 0x00, 
0xc1, 0x80, 0x30, 0x30, 0x06, 0xc6, 0x0c, 0x01, 0xb0, 0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x03, 
0x00, 0x00, 0x30, 0x00, 0x01, 0x81, 0x80, 0x06, 0x06, 0x00, 0x18, 0x18, 0x00, 0x60, 0x60, 0x03, 
0x01, 0xb0, 0x06, 0x03, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x0c, 0x00, 
0x03, 0x01, 0x8c, 0x30, 0x00, 0x03, 0x00, 0xcc, 0x03, 0x33, 0x1b, 0x18, 0x06, 0x06, 0x03, 0x3c, 
0x08, 0x00, 0xf0, 0x20, 0x03, 0xc0, 0x80, 0x0c, 0x00, 0x78, 0x10, 0x00, 0xc1, 0x80, 0x03, 0x06, 
0x00, 0x0c, 0x18, 0x00, 0x18, 0x30, 0x00, 0x00, 0x18, 0x30, 0x03, 0x06, 0x00, 0x30, 0x60, 0x06, 
0x0c, 0x01, 0x83, 0x00, 0x60, 0xc0, 0x18, 0x30, 0x00, 0x60, 0x60, 0x18, 0x18, 0xc0, 0xc0, 0xc3, 
0x01, 0x80, 0x0c, 0x06, 0x00, 0x30, 0x18, 0x00, 0xc0, 0x61, 0xc3, 0x01, 0x80, 0x0c, 0x06, 0x00, 
0x18, 0x70, 0xc0, 0x00, 0x18, 0x60, 0x00, 0x61, 0x80, 0x60, 0x00, 0x0c, 0x00, 0x01, 0x80, 0x0c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x20, 0x61, 
0x00, 0x21, 0x00, 0x20, 0x60, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0xc1, 
0x90, 0x04, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x80, 0x01, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x03, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x38, 
0xe0, 0x00, 0x00, 0x1c, 0x1c, 0x00, 0x00, 0x07, 0x07, 0x00, 0xee, 0x01, 0xc1, 0xc0, 0x3b, 0x80, 
0x70, 0x70, 0x0e, 0xe0, 0x1c, 0x1c, 0x03, 0xb8, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x18, 0x18, 0x00, 0x60, 0x60, 0x03, 0x18, 0x60, 0x0c, 0x18, 
0x00, 0x10, 0x02, 0x10, 0x02, 0x01, 0x80, 0x04, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0x60, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x41, 0x80, 0x6c, 0x03, 0x81, 0x98, 0x00, 
0x00, 0x0c, 0x38, 0x01, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x60, 0xc0, 0x01, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x00, 0x00, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x0d, 0x81, 0xf0, 0x1b, 0x03, 0xe0, 0x36, 0x07, 0xc3, 0x00, 0x1e, 0x60, 0x03, 0xcc, 0x00, 0x79, 
0x80, 0x0f, 0x30, 0x30, 0xf6, 0x06, 0x06, 0x3d, 0xb0, 0x03, 0xe3, 0x00, 0x3e, 0x30, 0x03, 0xe3, 
0x00, 0x3e, 0x30, 0x03, 0xe3, 0x00, 0x0f, 0x6c, 0x00, 0x3d, 0xb0, 0x00, 0xf6, 0xc0, 0x03, 0xdb, 
0x01, 0xb7, 0x86, 0x03, 0x37, 0x83, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x0c, 0xcf, 
0x03, 0x3d, 0x98, 0x30, 0xcc, 0x33, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x18, 0x06, 0x06, 0x43, 0x2e, 
0x0b, 0x78, 0xb8, 0x2d, 0xe2, 0xe0, 0xb7, 0x81, 0xbc, 0x5c, 0x16, 0xf1, 0x80, 0xc7, 0xc6, 0x03, 
0x1f, 0x18, 0x0c, 0x7c, 0x30, 0x30, 0x07, 0xcf, 0x18, 0x31, 0xbb, 0x06, 0x1b, 0xb0, 0x63, 0x76, 
0x00, 0xf9, 0x80, 0x3e, 0x60, 0x0f, 0x98, 0x03, 0xe0, 0x60, 0xfc, 0x18, 0x3f, 0x00, 0xc1, 0xfb, 
0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 
0x98, 0x70, 0xd8, 0x43, 0x18, 0x63, 0x0c, 0x61, 0x80, 0xc7, 0xf8, 0x18, 0xff, 0x03, 0x1f, 0xec, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 0x31, 
0x00, 0x21, 0x00, 0x20, 0x60, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x09, 0x80, 
0xe3, 0xf8, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x8c, 0x1e, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x06, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x00, 0xd8, 0x00, 0x7c, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x70, 
0x70, 0x07, 0xc0, 0x1c, 0x1c, 0x07, 0x1c, 0x07, 0x07, 0x01, 0xc7, 0x01, 0xc1, 0xc0, 0x71, 0xc0, 
0x70, 0x70, 0x1c, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x30, 0x08, 0xfe, 0xc0, 0x03, 0xdb, 0x30, 0x61, 0x98, 0x0c, 
0x7c, 0x10, 0x02, 0x10, 0x02, 0x03, 0x0f, 0xc4, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0x7b, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x40, 0xc0, 0x6c, 0x0f, 0x81, 0x98, 0x03, 
0xcf, 0x18, 0x4c, 0x7e, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x60, 0x0f, 0x81, 0x83, 0xf1, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x00, 0x7c, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x18, 0xc3, 0x18, 0x31, 0x86, 0x30, 0x63, 0x0c, 0x63, 0x00, 0x31, 0x60, 0x06, 0x2c, 0x00, 0xc5, 
0x80, 0x18, 0xb0, 0x19, 0x8e, 0x06, 0x06, 0x63, 0xb0, 0x06, 0x33, 0x00, 0x63, 0x30, 0x06, 0x33, 
0x00, 0x63, 0x30, 0x06, 0x33, 0x00, 0x18, 0xec, 0x00, 0x63, 0xb0, 0x01, 0x8e, 0xc0, 0x06, 0x3b, 
0x01, 0xb8, 0xc7, 0xff, 0x38, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x0c, 0xc3, 
0x03, 0x0d, 0xb0, 0x31, 0x8c, 0x63, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x19, 0x86, 0x66, 0x83, 0xa7, 
0x0b, 0x8c, 0x9c, 0x2e, 0x32, 0x70, 0xb8, 0xc1, 0xc6, 0x4e, 0x17, 0x19, 0x80, 0xcc, 0x66, 0x03, 
0x31, 0x98, 0x0c, 0xc6, 0x30, 0x30, 0x0c, 0x79, 0x98, 0x31, 0xfb, 0x06, 0x1f, 0xb0, 0x63, 0xf7, 
0x01, 0x85, 0xc0, 0x61, 0x70, 0x18, 0x5c, 0x06, 0x10, 0x60, 0x60, 0x18, 0x18, 0x00, 0xc0, 0xc3, 
0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 
0x8c, 0xf9, 0x98, 0xe3, 0x0c, 0xc3, 0x0c, 0x33, 0x00, 0xc0, 0x18, 0x18, 0x03, 0x03, 0x00, 0x6c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x7f, 0xf1, 
0x00, 0x21, 0x00, 0x20, 0x60, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x09, 0x80, 
0xc6, 0x30, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x8c, 0x18, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x0c, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x01, 0xd8, 0x00, 0xee, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x60, 
0x30, 0x0f, 0xe0, 0x1c, 0x1c, 0x07, 0x1c, 0x07, 0x07, 0x01, 0xc7, 0x01, 0xc1, 0xc0, 0x71, 0xc0, 
0x70, 0x70, 0x1c, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x30, 0x01, 0x86, 0xc0, 0x06, 0x3b, 0x60, 0x63, 0x18, 0x0c, 
0xc6, 0x10, 0x02, 0x10, 0x02, 0x06, 0x00, 0xc4, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0xee, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x40, 0xe0, 0xc6, 0x18, 0xc3, 0x18, 0x06, 
0x79, 0x98, 0x4c, 0xc6, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x70, 0x18, 0x41, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x00, 0xc6, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x18, 0xc0, 0x0c, 0x31, 0x80, 0x18, 0x63, 0x00, 0x33, 0x00, 0x60, 0x60, 0x0c, 0x0c, 0x01, 0x81, 
0x80, 0x30, 0x30, 0x1b, 0x06, 0x0f, 0xc6, 0xc1, 0xbf, 0xec, 0x1b, 0xfe, 0xc1, 0xbf, 0xec, 0x1b, 
0xfe, 0xc1, 0xbf, 0xec, 0x1b, 0x00, 0x30, 0x6c, 0x00, 0xc1, 0xb0, 0x03, 0x06, 0xc0, 0x0c, 0x1b, 
0xff, 0xb0, 0x66, 0x03, 0x30, 0x63, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x0c, 0xc3, 
0x03, 0x0d, 0xe0, 0x33, 0x0c, 0xc3, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x19, 0x86, 0x67, 0x03, 0x23, 
0x8b, 0x06, 0x8e, 0x2c, 0x1a, 0x38, 0xb0, 0x61, 0x83, 0x47, 0x16, 0x0d, 0x80, 0xd8, 0x36, 0x03, 
0x60, 0xd8, 0x0d, 0x83, 0x30, 0x3f, 0xd8, 0x30, 0xd8, 0x31, 0x83, 0x06, 0x18, 0x30, 0x63, 0x03, 
0xf1, 0x80, 0xfc, 0x60, 0x3f, 0x18, 0x0f, 0xc6, 0x00, 0x60, 0x60, 0x18, 0x18, 0x03, 0xf0, 0xc3, 
0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 
0x8c, 0xd9, 0x8c, 0xe6, 0x07, 0x83, 0x0c, 0x1e, 0x01, 0x80, 0x30, 0x30, 0x06, 0x06, 0x00, 0xcc, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x60, 0x31, 
0x00, 0x21, 0x00, 0x21, 0xfc, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x09, 0x80, 
0xcc, 0x18, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x8c, 0x18, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x1e, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x01, 0xdc, 0x01, 0xc6, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0xe0, 
0x30, 0x1c, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x07, 0x07, 0x01, 0xc7, 0x01, 0xc1, 0xc0, 0x71, 0xc0, 
0x70, 0x70, 0x1c, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x30, 0x03, 0x06, 0xc0, 0x0c, 0x1b, 0xc0, 0x66, 0x18, 0x0d, 
0x83, 0x10, 0x02, 0x10, 0x02, 0x0f, 0x01, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0xce, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x47, 0xe0, 0xc6, 0x00, 0x63, 0x1f, 0xe0, 
0x30, 0xd8, 0x8d, 0x8b, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x3f, 0x18, 0x01, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x27, 0xfd, 0x83, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x18, 0xc0, 0xfc, 0x31, 0x81, 0xf8, 0x63, 0x03, 0xf3, 0x00, 0x60, 0x60, 0x0c, 0x0c, 0x01, 0x81, 
0x80, 0x30, 0x30, 0x1b, 0x06, 0x06, 0x06, 0xc1, 0xb0, 0x0c, 0x1b, 0x00, 0xc1, 0xb0, 0x0c, 0x1b, 
0x00, 0xc1, 0xb0, 0x0c, 0x1b, 0x0f, 0xb0, 0x6c, 0x3e, 0xc1, 0xb0, 0xfb, 0x06, 0xc3, 0xec, 0x1b, 
0x01, 0xb0, 0x66, 0x03, 0x30, 0x63, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x0c, 0xc3, 
0x03, 0x0d, 0xb0, 0x36, 0x0d, 0x83, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x19, 0x86, 0x66, 0x07, 0x21, 
0xcb, 0x06, 0x87, 0x2c, 0x1a, 0x1c, 0xb0, 0x61, 0x83, 0x43, 0x96, 0x0d, 0x80, 0xd8, 0x36, 0x03, 
0x60, 0xd8, 0x0d, 0x83, 0x30, 0x30, 0x18, 0x30, 0xd8, 0x61, 0x83, 0x0c, 0x18, 0x30, 0xc3, 0x01, 
0xf9, 0xe0, 0x7e, 0x78, 0x1f, 0x9e, 0x07, 0xe7, 0x80, 0x60, 0x60, 0x18, 0x18, 0x00, 0xc1, 0xf3, 
0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 
0x8c, 0xd9, 0x8d, 0xb6, 0x07, 0x81, 0x98, 0x1e, 0x03, 0x00, 0x60, 0x60, 0x0c, 0x0c, 0x01, 0x8c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x60, 0x31, 
0x00, 0x21, 0x00, 0x20, 0x60, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x09, 0x80, 
0xcc, 0x18, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x8c, 0x18, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x03, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x01, 0x8c, 0x00, 0x0e, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0xe0, 
0x38, 0x18, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x07, 0x07, 0x01, 0xc7, 0x01, 0xc1, 0xc0, 0x71, 0xc0, 
0x70, 0x70, 0x1c, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x30, 0x03, 0x06, 0xc3, 0xec, 0x1b, 0x60, 0x6c, 0x18, 0x0d, 
0x83, 0x10, 0x02, 0x10, 0x02, 0x01, 0x83, 0x04, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x81, 0xce, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x46, 0x30, 0xc6, 0x07, 0xe3, 0x18, 0x01, 
0xf0, 0xd8, 0x8d, 0x8b, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x1f, 0x9e, 0x01, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x01, 0x83, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x3f, 0xe3, 0x0c, 0x7f, 0xc6, 0x18, 0xff, 0x8c, 0x33, 0x00, 0x60, 0x60, 0x0c, 0x0c, 0x01, 0x81, 
0x80, 0x30, 0x30, 0x1b, 0x06, 0x06, 0x06, 0xc1, 0xb0, 0x0f, 0xfb, 0x00, 0xff, 0xb0, 0x0f, 0xfb, 
0x00, 0xff, 0xb0, 0x0f, 0xfb, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 
0x01, 0xb0, 0x66, 0x03, 0x30, 0x63, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x0c, 0xc3, 
0x03, 0x0d, 0x98, 0x3e, 0x0f, 0x83, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x18, 0x06, 0x0e, 0x03, 0x20, 
0xeb, 0x06, 0x83, 0xac, 0x1a, 0x0e, 0xb0, 0x61, 0x83, 0x41, 0xd6, 0x0d, 0x80, 0xd8, 0x36, 0x03, 
0x60, 0xd8, 0x0d, 0x83, 0x30, 0x30, 0x18, 0x3f, 0xdf, 0xc1, 0x83, 0xf8, 0x18, 0x3f, 0x83, 0x00, 
0x1c, 0xf8, 0x07, 0x3e, 0x01, 0xcf, 0x80, 0x73, 0xe0, 0x60, 0x60, 0x18, 0x18, 0x00, 0xc0, 0xc3, 
0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 
0x8d, 0x8d, 0x8d, 0xb6, 0x03, 0x01, 0x98, 0x0c, 0x06, 0x00, 0xc0, 0xc0, 0x18, 0x18, 0x03, 0x0c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x60, 0x31, 
0x00, 0x21, 0x00, 0x20, 0x60, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x09, 0x80, 
0xcc, 0x18, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x8c, 0x18, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x01, 0x88, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x03, 0xfe, 0x00, 0xfe, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x60, 
0x30, 0x18, 0x30, 0x1c, 0x1c, 0x07, 0x1c, 0x07, 0x07, 0x01, 0xc7, 0x01, 0xc1, 0xc0, 0x71, 0xc0, 
0x70, 0x70, 0x1c, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x33, 0xfb, 0x06, 0xc0, 0x6c, 0x1b, 0x30, 0x7c, 0x18, 0x0d, 
0x83, 0x10, 0x02, 0x10, 0x02, 0x00, 0xc3, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0xfc, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x46, 0x31, 0xff, 0x18, 0x67, 0xf8, 0x06, 
0x3f, 0xd9, 0x0d, 0x93, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x01, 0xcf, 0x81, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x01, 0xff, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x30, 0x66, 0x0c, 0x60, 0xcc, 0x18, 0xc1, 0x98, 0x33, 0x00, 0x60, 0x60, 0x0c, 0x0c, 0x01, 0x81, 
0x80, 0x30, 0x30, 0x33, 0x06, 0x06, 0x06, 0xc1, 0xb0, 0x0c, 0x03, 0x00, 0xc0, 0x30, 0x0c, 0x03, 
0x00, 0xc0, 0x30, 0x0c, 0x03, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 
0x01, 0xb0, 0x66, 0x03, 0x30, 0x63, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x0c, 0xc3, 
0x03, 0x0d, 0x8c, 0x33, 0x0c, 0xc3, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x18, 0x06, 0x06, 0x03, 0x20, 
0x7b, 0x06, 0x81, 0xec, 0x1a, 0x07, 0xb0, 0x61, 0x83, 0x40, 0xf6, 0x0d, 0x80, 0xd8, 0x36, 0x03, 
0x60, 0xd8, 0x0d, 0x83, 0x30, 0x30, 0x18, 0x30, 0x18, 0x61, 0x83, 0x0c, 0x18, 0x30, 0xc3, 0x00, 
0x0c, 0x3c, 0x03, 0x0f, 0x00, 0xc3, 0xc0, 0x30, 0xf0, 0x60, 0x60, 0x18, 0x18, 0x00, 0xc0, 0xc3, 
0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 
0x8d, 0x8d, 0x8d, 0xb6, 0x03, 0x01, 0x98, 0x0c, 0x06, 0x01, 0x80, 0xc0, 0x30, 0x18, 0x06, 0x0c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x70, 0x71, 
0x00, 0x21, 0x00, 0x20, 0xc0, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x09, 0x80, 
0xcc, 0x18, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x8c, 0x18, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x01, 0x88, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x03, 0x06, 0x01, 0xce, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x60, 
0x70, 0x18, 0x30, 0x1c, 0x1c, 0x07, 0x1c, 0x07, 0x07, 0x01, 0xc7, 0x01, 0xc1, 0xc0, 0x71, 0xc0, 
0x70, 0x70, 0x1c, 0x70, 0x1c, 0x1c, 0x07, 0x1c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x38, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x18, 0x66, 0x18, 0x0d, 
0x83, 0x10, 0x02, 0x10, 0x02, 0x00, 0xc1, 0xc4, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0xf8, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x46, 0x31, 0x83, 0x30, 0x66, 0x18, 0x0c, 
0x30, 0x19, 0x0d, 0xa3, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x00, 0xc3, 0xc1, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x01, 0x80, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x60, 0x36, 0x0c, 0xc0, 0x6c, 0x19, 0x80, 0xd8, 0x31, 0x83, 0x60, 0x30, 0x6c, 0x06, 0x0d, 0x80, 
0xc1, 0xb0, 0x30, 0x33, 0x06, 0x06, 0x0c, 0xc1, 0xb0, 0x0c, 0x03, 0x00, 0xc0, 0x30, 0x0c, 0x03, 
0x00, 0xc0, 0x30, 0x0c, 0x01, 0x81, 0xb0, 0x66, 0x06, 0xc1, 0x98, 0x1b, 0x06, 0x60, 0x6c, 0x1b, 
0x01, 0xb0, 0x66, 0x03, 0x30, 0x63, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x0c, 0xc3, 
0x03, 0x0d, 0x86, 0x31, 0x8c, 0x63, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x18, 0x06, 0x06, 0x03, 0x20, 
0x3b, 0x06, 0x80, 0xec, 0x1a, 0x03, 0xb0, 0x61, 0x83, 0x40, 0x76, 0x0c, 0xc1, 0x98, 0x33, 0x06, 
0x60, 0xcc, 0x19, 0x83, 0x18, 0x30, 0x18, 0x30, 0x18, 0x31, 0x83, 0x06, 0x18, 0x30, 0x63, 0x06, 
0x0c, 0x0d, 0x83, 0x03, 0x60, 0xc0, 0xd8, 0x30, 0x30, 0x60, 0x60, 0x18, 0x18, 0x00, 0xc0, 0xc3, 
0x01, 0xb0, 0x6c, 0x06, 0xc1, 0xb0, 0x1b, 0x06, 0xc0, 0x6c, 0x1b, 0x01, 0xb0, 0x6c, 0x06, 0xc1, 
0x87, 0x8f, 0x07, 0x1c, 0x03, 0x00, 0xf0, 0x0c, 0x0c, 0x03, 0x01, 0x80, 0x60, 0x30, 0x0c, 0x0c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x30, 0x61, 
0x00, 0x21, 0x00, 0x20, 0xc0, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0xc1, 
0x8c, 0x18, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x13, 0x01, 0x8c, 0x18, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x01, 0x88, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x07, 0x07, 0x01, 0xce, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x70, 
0x70, 0x1c, 0x70, 0x1c, 0x38, 0x07, 0x1c, 0x07, 0x0e, 0x01, 0xc7, 0x01, 0xc3, 0x80, 0x71, 0xc0, 
0x70, 0xe0, 0x1c, 0x70, 0x1c, 0x18, 0x07, 0x1c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x18, 0x3d, 0x86, 0x60, 0x6c, 0x1b, 0x0c, 0x63, 0x0c, 0x19, 
0x83, 0x10, 0x02, 0x10, 0x02, 0x00, 0xc0, 0xc4, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x81, 0x80, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x46, 0x33, 0x01, 0xb0, 0x6c, 0x18, 0x0c, 
0x30, 0x0e, 0x19, 0xa3, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x60, 0xc0, 0xc1, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x01, 0x80, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x60, 0x36, 0x1c, 0xc0, 0x6c, 0x39, 0x80, 0xd8, 0x71, 0xc3, 0x31, 0x38, 0x66, 0x27, 0x0c, 0xc4, 
0xe1, 0x98, 0xb0, 0xe1, 0x8e, 0x06, 0x1c, 0x63, 0xb0, 0x06, 0x1b, 0x00, 0x61, 0xb0, 0x06, 0x1b, 
0x00, 0x61, 0xb0, 0x06, 0x19, 0xc1, 0x98, 0xe7, 0x06, 0x63, 0x9c, 0x19, 0x8e, 0x70, 0x66, 0x3b, 
0x01, 0xb0, 0x66, 0x03, 0x30, 0x63, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0x18, 0xc3, 
0x06, 0x0d, 0x83, 0x30, 0xcc, 0x33, 0x00, 0xcc, 0x03, 0x30, 0x18, 0x18, 0x06, 0x06, 0x03, 0x20, 
0x1b, 0x06, 0x80, 0x6c, 0x1a, 0x01, 0xb0, 0x61, 0x83, 0x40, 0x36, 0x0c, 0xe3, 0x8c, 0x63, 0x8e, 
0x31, 0x8e, 0x38, 0xc6, 0x1c, 0x30, 0x0c, 0x78, 0xd8, 0x19, 0x83, 0x03, 0x18, 0x30, 0x33, 0x06, 
0x19, 0x0d, 0x86, 0x43, 0x61, 0x90, 0xd8, 0x64, 0x30, 0x60, 0x60, 0x18, 0x18, 0x00, 0xc0, 0xc1, 
0x83, 0x18, 0xe6, 0x0c, 0x63, 0x98, 0x31, 0x8e, 0x60, 0xc6, 0x39, 0x83, 0x18, 0xe6, 0x0c, 0x63, 
0x87, 0x07, 0x07, 0x1c, 0x03, 0x00, 0xf0, 0x0c, 0x18, 0x06, 0x03, 0x00, 0xc0, 0x60, 0x18, 0x0c, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x18, 0xc1, 
0x00, 0x21, 0x00, 0x20, 0xc0, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 
0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0xe3, 
0x86, 0x30, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 
0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x10, 0x80, 0x11, 0x83, 0x06, 0x38, 0x20, 
0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0x04, 0x01, 0x88, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x06, 0x07, 0x01, 0xce, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x3d, 
0xe0, 0x0e, 0xe0, 0x0e, 0x38, 0x07, 0x3c, 0x03, 0x8e, 0x01, 0xcf, 0x00, 0xe3, 0x80, 0x73, 0xc0, 
0x38, 0xe0, 0x1c, 0xf0, 0x0e, 0x38, 0x07, 0x3c, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 
0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0x0e, 0x18, 0xfe, 0x70, 0x66, 0x3b, 0x06, 0x61, 0x8e, 0x38, 
0xc6, 0x10, 0x02, 0x10, 0x02, 0x00, 0xc0, 0xc4, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 0x00, 0x84, 
0x00, 0x80, 0xfe, 0x02, 0x00, 0x42, 0x00, 0x42, 0x00, 0x46, 0x33, 0x01, 0xb0, 0xec, 0x18, 0x0c, 
0x78, 0xc6, 0x38, 0xc6, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 
0x10, 0x02, 0x10, 0x02, 0x61, 0x90, 0xc1, 0x81, 0x81, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 
0x26, 0x00, 0xc1, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 
0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x10, 0x02, 0x00, 


0x60, 0x33, 0xec, 0xc0, 0x67, 0xd9, 0x80, 0xcf, 0xb0, 0x7e, 0x1e, 0x0f, 0xc3, 0xc1, 0xf8, 0x78, 
0x3f, 0x0f, 0x3f, 0x80, 0xf6, 0x07, 0xf0, 0x3d, 0xbf, 0xe3, 0xf3, 0xfe, 0x3f, 0x3f, 0xe3, 0xf3, 
0xfe, 0x3f, 0x3f, 0xe3, 0xf0, 0x7f, 0x0f, 0x61, 0xfc, 0x3d, 0x87, 0xf0, 0xf6, 0x1f, 0xc3, 0xdb, 
0x01, 0xb0, 0x66, 0x03, 0x30, 0x6f, 0xcc, 0xfc, 0xcf, 0xcc, 0xfc, 0xcf, 0xcc, 0xfd, 0xf0, 0xc3, 
0x7c, 0x0d, 0x81, 0xb0, 0x6c, 0x1b, 0xfc, 0xcf, 0xf3, 0x3f, 0xd8, 0x1f, 0xe6, 0x07, 0xf3, 0x20, 
0x0b, 0x06, 0x80, 0x2c, 0x1a, 0x00, 0xb0, 0x61, 0x83, 0x40, 0x16, 0x0c, 0x3e, 0x07, 0xc0, 0xf8, 
0x1f, 0x03, 0xe0, 0x7c, 0x07, 0xff, 0xc7, 0xcf, 0x98, 0x0d, 0x83, 0x01, 0x98, 0x30, 0x1b, 0x03, 
0xf0, 0xf8, 0xfc, 0x3e, 0x3f, 0x0f, 0x8f, 0xc3, 0xe0, 0x60, 0x3c, 0x18, 0x0f, 0x00, 0xc0, 0x78, 
0xfe, 0x0f, 0x63, 0xf8, 0x3d, 0x8f, 0xe0, 0xf6, 0x3f, 0x83, 0xd8, 0xfe, 0x0f, 0x63, 0xf8, 0x3d, 
0x87, 0x07, 0x06, 0x0c, 0x03, 0x00, 0x60, 0x0c, 0x1f, 0xf7, 0xfb, 0xfe, 0xff, 0x7f, 0xdf, 0xec, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x0f, 0x81, 
0xff, 0xe1, 0xff, 0xe0, 0xc0, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 
0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x7f, 0xf8, 0x3e, 
0x03, 0xe0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 
0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xff, 0xf0, 0xfe, 0x03, 0xd8, 0x3f, 
0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x3f, 0xfc, 0x01, 0x8f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0e, 0x03, 0x80, 0xff, 0x00, 0x1c, 0x00, 0x0e, 0x00, 0x0f, 
0x80, 0x07, 0xc0, 0x07, 0xf0, 0x03, 0xfc, 0x01, 0xfc, 0x00, 0xff, 0x00, 0x7f, 0x00, 0x3f, 0xc0, 
0x1f, 0xc0, 0x0f, 0xf0, 0x07, 0xf0, 0x03, 0xfc, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 
0x0f, 0xff, 0x0f, 0xff, 0x0f, 0xff, 0x07, 0xf0, 0x06, 0x1f, 0xc3, 0xdb, 0x03, 0x60, 0xc3, 0xe0, 
0x7c, 0x1f, 0xfe, 0x1f, 0xfe, 0x00, 0xc0, 0xc7, 0xff, 0x87, 0xff, 0x87, 0xff, 0x87, 0xff, 0x87, 
0xff, 0x81, 0x83, 0x03, 0xff, 0xc3, 0xff, 0xc3, 0xff, 0xcf, 0x7b, 0x01, 0x9f, 0x6c, 0x1f, 0xe7, 
0xcf, 0x8b, 0xe0, 0xfc, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 
0x1f, 0xfe, 0x1f, 0xfe, 0x3f, 0x0f, 0x81, 0x80, 0xf1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 
0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 
0xe7, 0xfc, 0x7e, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 
0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x01, 0x80, 0x00, 0x06, 0x01, 0x00, 0x18, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x80, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x0c, 0x08, 0x02, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x04, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x80, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x01, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x40, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc0, 0x00, 0x63, 0x00, 0x01, 0x8c, 0x01, 0x06, 0x30, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x80, 0x00, 0x00, 0x00, 0x03, 
0x00, 0x0c, 0x08, 0x02, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x04, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x06, 0x30, 0x00, 0x00, 0x00, 0x80, 
0x10, 0x00, 0x00, 0x00, 0x00, 0x41, 0x89, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x06, 0x03, 0x01, 0x80, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x40, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x0e, 0x03, 0x80, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x3e, 0x00, 0x00, 0xf8, 0x0e, 0x03, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x70, 0x00, 0x00, 0x00, 0x1e, 
0x00, 0x78, 0x70, 0x1c, 0x00, 0x00, 0x00, 0x07, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1c, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x38, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0e, 0x0e, 0x00, 0x00, 0x00, 0xe0, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 
0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf8, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x00, 0x70, 
0x0e, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0c, 0x06, 0x03, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x80, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 



};
#endif



#if defined(_FONT_PLASTIMO_)
xFONTYY MyriadPro12bExt_Font = {0x01, 15, 4, 19, 0, 0, 19, 254, 0x0100, 0x024f,
(PEGUSHORT *) MyriadPro12bExt_offset_table,&MyriadPro12bGrk_Font,
(PEGUBYTE *) MyriadPro12bExt_data_table};
#else
xFONTYY MyriadPro12bExt_Font = {0x01, 16, 3, 19, 0, 0, 19, 560, 0x0100, 0x024f,
(PEGUSHORT *) MyriadPro12bExt_offset_table,&MyriadPro12bGrk_Font,
(PEGUBYTE *) MyriadPro12bExt_data_table};
#endif



#if defined(_FONT_PLASTIMO_)
ROMDATA PEGUSHORT MyriadPro12bEng_offset_table[257] = {
0x0000,0x0009,0x0014,0x001f,0x002a,0x0035,0x0040,0x004b,0x0056,0x0061,0x006c,0x0077,0x0082,0x008b,0x0094,0x009d,
0x00b0,0x00b9,0x00c2,0x00cb,0x00d4,0x00dd,0x00e6,0x00ef,0x00f8,0x0101,0x010a,0x0113,0x011c,0x0125,0x012e,0x0137,
0x0140,0x014b,0x014f,0x0155,0x015d,0x0166,0x0173,0x017e,0x0181,0x0186,0x018b,0x0192,0x019c,0x01a0,0x01a5,0x01a9,
0x01b0,0x01bb,0x01c6,0x01d1,0x01dc,0x01e7,0x01f2,0x01fd,0x0208,0x0213,0x021e,0x0222,0x0226,0x0230,0x023a,0x0244,
0x024b,0x0257,0x0261,0x026a,0x0274,0x027f,0x028a,0x0292,0x029d,0x02a8,0x02ac,0x02b3,0x02bc,0x02c4,0x02d1,0x02dc,
0x02e7,0x02f0,0x02fb,0x0304,0x030f,0x0317,0x0322,0x032c,0x0338,0x0342,0x034c,0x0355,0x035a,0x0360,0x0365,0x036f,
0x0378,0x037d,0x0385,0x038f,0x0397,0x03a0,0x03a8,0x03af,0x03b8,0x03c1,0x03c5,0x03cb,0x03d3,0x03d7,0x03e5,0x03ee,
0x03f7,0x0401,0x040a,0x0410,0x0417,0x041d,0x0426,0x042e,0x043a,0x0442,0x044a,0x0451,0x0456,0x045a,0x045f,0x0469,
0x0472,0x0476,0x047a,0x047e,0x0482,0x0486,0x048a,0x048e,0x0492,0x0496,0x049a,0x049e,0x04a2,0x04a6,0x04aa,0x04ae,
0x04b2,0x04b6,0x04ba,0x04be,0x04c2,0x04c6,0x04ca,0x04ce,0x04d2,0x04d6,0x04da,0x04de,0x04e2,0x04e6,0x04ea,0x04ee,
0x04f2,0x04f6,0x04fa,0x0503,0x050c,0x0515,0x051e,0x0522,0x052b,0x0530,0x053b,0x0541,0x0548,0x0552,0x0557,0x055f,
0x0564,0x056a,0x0574,0x0579,0x057e,0x0584,0x058d,0x0596,0x059a,0x059f,0x05a3,0x05a9,0x05b0,0x05bd,0x05ca,0x05d7,
0x05de,0x05e8,0x05f2,0x05fc,0x0606,0x0610,0x061b,0x0628,0x0632,0x063a,0x0642,0x064a,0x0652,0x0657,0x065c,0x0660,
0x0665,0x0670,0x067b,0x0686,0x0691,0x069c,0x06a7,0x06b2,0x06bc,0x06c7,0x06d2,0x06dd,0x06e8,0x06f3,0x06fd,0x0706,
0x070f,0x0717,0x071f,0x0727,0x072f,0x0737,0x073f,0x074c,0x0754,0x075c,0x0764,0x076c,0x0774,0x0779,0x077d,0x0781,
0x0786,0x078f,0x0798,0x07a1,0x07aa,0x07b3,0x07bc,0x07c5,0x07cf,0x07d8,0x07e1,0x07ea,0x07f3,0x07fc,0x0804,0x080e,
0x0816,
};


ROMDATA PEGUBYTE MyriadPro12bEng_data_table[4921] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0xfb, 0xfd, 0xfe, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x18, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x30, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x0d, 0x83, 0x00, 0x60, 0x0c, 0x01, 0x80, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 
0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x78, 
0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x0f, 0x07, 0x81, 
0xe0, 0x6c, 0x1b, 0x00, 0x00, 0x00, 0x1e, 0x07, 0x8e, 0x0d, 0xbc, 0xff, 0xd8, 0x00, 0x0e, 0x03, 
0xc0, 0x1e, 0x07, 0x81, 0xf0, 0x1e, 0x00, 0x00, 0x00, 0x70, 0x00, 0xe0, 0x70, 0x1b, 0x00, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x08, 0x84, 0x80, 0x90, 0x12, 0x02, 0x40, 0x90, 0x00, 0x00, 0x00, 0x03, 0x00, 0x60, 0x90, 
0x07, 0x80, 0x01, 0xf8, 0x07, 0x00, 0x60, 0x00, 0xc7, 0x86, 0xc1, 0xf8, 0x60, 0x00, 0x18, 0x1e, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x03, 0x00, 0x00, 0x03, 0x00, 0x1e, 0x00, 0x60, 0x00, 0x0c, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x8c, 
0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xc0, 
0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 
0x00, 0x00, 0x0c, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x06, 0x1c, 0x03, 0x80, 0xd8, 0x00, 0x01, 0x80, 
0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 

0x00, 0x0d, 0x84, 0xbc, 0x90, 0x12, 0x02, 0x40, 0x90, 0x00, 0x40, 0x10, 0x07, 0x80, 0x60, 0x67, 
0x8c, 0xc0, 0x01, 0x98, 0x07, 0x00, 0x70, 0x01, 0xcf, 0xc6, 0xc3, 0x78, 0x60, 0x00, 0x18, 0x3f, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xf1, 0xb0, 0x60, 0xe3, 0x83, 0xc1, 
0x8f, 0x0f, 0x80, 0x00, 0x00, 0x03, 0x0e, 0x01, 0xc0, 0x7c, 0x07, 0x80, 0x18, 0x1f, 0x01, 0xc1, 
0xfc, 0x0f, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x18, 0x3f, 0x03, 0xf7, 0xf0, 
0x7f, 0x1f, 0x83, 0xf3, 0x06, 0x60, 0xcc, 0x76, 0x03, 0x03, 0x30, 0x60, 0xf0, 0xfc, 0x0f, 0x0f, 
0xc0, 0xf9, 0xfe, 0xc1, 0xb0, 0x34, 0x21, 0x71, 0xd8, 0x77, 0xfb, 0xf0, 0xf0, 0x60, 0x00, 0xe0, 
0x03, 0x00, 0x00, 0x03, 0x00, 0x30, 0x00, 0x60, 0x30, 0xcc, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x98, 0x00, 0x3f, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x03, 0xc0, 0x03, 0x0d, 0x87, 0x80, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x78, 0x07, 0x80, 0x01, 0xb8, 
0x70, 0x00, 0xf8, 0x00, 0x60, 0x00, 0x70, 0xe3, 0x86, 0x0e, 0x18, 0x00, 0x30, 0x0c, 0x03, 0x00, 
0xc0, 0x38, 0x0e, 0x00, 0xff, 0x0f, 0xdf, 0x9f, 0x9f, 0x9f, 0x8c, 0xc6, 0x63, 0xf8, 0x60, 0xc1, 
0xe0, 0x3c, 0x03, 0x00, 0xf0, 0x1e, 0x00, 0x00, 0xfc, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xcc, 0x3b, 
0x00, 0xcc, 0x60, 0x1c, 0x30, 0x3c, 0x00, 0x6c, 0x00, 0x00, 0x03, 0x80, 0x60, 0xc0, 0x0e, 0x1b, 
0x00, 0x7c, 0x3e, 0x38, 0x03, 0x83, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x30, 0x03, 0x07, 0x00, 0x00, 
0xe6, 0x00, 0x00, 

0x00, 0x07, 0x03, 0x66, 0x6f, 0x8d, 0xf9, 0x9e, 0x68, 0x20, 0x60, 0x30, 0x0f, 0xc0, 0x60, 0x0c, 
0xcc, 0xc0, 0x01, 0xb8, 0x07, 0x00, 0x78, 0x03, 0xc3, 0x06, 0xc6, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xf1, 0xb1, 0xf1, 0xb3, 0x06, 0x61, 
0x99, 0x86, 0x00, 0x00, 0x00, 0x06, 0x1b, 0x03, 0xc0, 0xc6, 0x0c, 0xc0, 0x38, 0x30, 0x03, 0x00, 
0x0c, 0x39, 0x83, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0xf0, 0x3c, 0x31, 0x86, 0x06, 0x1c, 
0x60, 0x18, 0x06, 0x03, 0x06, 0x60, 0xcc, 0xc6, 0x03, 0x87, 0x38, 0x61, 0x9c, 0xc6, 0x19, 0xcc, 
0x63, 0x8c, 0x30, 0xc1, 0x98, 0x66, 0x73, 0x31, 0x8c, 0xc0, 0x33, 0x18, 0x30, 0x70, 0x00, 0x30, 
0x03, 0x00, 0x00, 0x03, 0x00, 0x30, 0x00, 0x60, 0x00, 0x0c, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x8c, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x06, 0x00, 0x01, 0x99, 0x8c, 0x1b, 0x19, 0x8f, 0x00, 0x00, 0x00, 0xfd, 0xfc, 0xc0, 0x03, 0x0c, 
0xc0, 0x01, 0xf8, 0x00, 0x6f, 0x00, 0xf0, 0xc7, 0x8c, 0x1b, 0x30, 0x00, 0x78, 0x1e, 0x07, 0x80, 
0xe0, 0x38, 0x0e, 0x01, 0xe0, 0x18, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x0e, 0x70, 0xc3, 
0x38, 0x67, 0x0f, 0xe3, 0x98, 0x33, 0x00, 0x03, 0x8c, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc6, 0x63, 
0x01, 0x8c, 0x30, 0x30, 0x78, 0x60, 0x6c, 0x6c, 0x00, 0x00, 0x00, 0xc1, 0xc3, 0xe3, 0x63, 0x37, 
0xec, 0xf8, 0x60, 0x0c, 0x06, 0x0f, 0xc6, 0x03, 0x60, 0x00, 0x00, 0x1c, 0x0e, 0x0d, 0x86, 0xc1, 
0x86, 0x00, 0xd8, 

0x00, 0x00, 0x00, 0xc2, 0x0f, 0x81, 0xf8, 0x33, 0x0c, 0x60, 0x70, 0x70, 0x1f, 0xe0, 0x60, 0x18, 
0x0c, 0xc0, 0x01, 0xf8, 0x07, 0x00, 0x7c, 0x07, 0xc3, 0x06, 0xc6, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xf1, 0xb3, 0x19, 0xb6, 0x06, 0x61, 
0x99, 0x9f, 0xc3, 0x00, 0x00, 0x06, 0x31, 0x80, 0xc0, 0x06, 0x00, 0xc0, 0x78, 0x30, 0x06, 0x00, 
0x18, 0x31, 0x86, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x18, 0x3c, 0x31, 0x8c, 0x06, 0x0c, 
0x60, 0x18, 0x0c, 0x03, 0x06, 0x60, 0xcd, 0x86, 0x03, 0xcf, 0x3c, 0x63, 0x0c, 0xc3, 0x30, 0xcc, 
0x33, 0x00, 0x30, 0xc1, 0x98, 0x66, 0x73, 0x1b, 0x0c, 0xc0, 0x63, 0x18, 0x30, 0xd8, 0x00, 0x00, 
0x03, 0x00, 0x00, 0x03, 0x00, 0x30, 0x00, 0x60, 0x00, 0x0c, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x8c, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 
0xc6, 0x0f, 0xf9, 0x99, 0x8c, 0x00, 0x30, 0xc1, 0x80, 0x00, 0x01, 0xfe, 0x0c, 0xc3, 0x06, 0x0c, 
0x00, 0x01, 0xf8, 0x00, 0x79, 0x80, 0x31, 0x81, 0x8c, 0x03, 0x30, 0x00, 0x78, 0x1e, 0x07, 0x80, 
0xf0, 0x6c, 0x1b, 0x01, 0xe0, 0x30, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x06, 0x78, 0xc6, 
0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x80, 0x03, 0x1c, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc6, 0x63, 
0xf1, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x00, 

0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x31, 0x0e, 0xe0, 0x78, 0xf0, 0x3f, 0xf0, 0x60, 0x18, 
0x07, 0x80, 0x01, 0x98, 0x07, 0x00, 0x7e, 0x0f, 0xc3, 0x06, 0xc6, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xf1, 0xb3, 0x01, 0xb6, 0x06, 0x61, 
0xb0, 0xc7, 0x03, 0x00, 0x00, 0x0c, 0x31, 0x80, 0xc0, 0x06, 0x00, 0xc0, 0x78, 0x30, 0x0c, 0x00, 
0x18, 0x31, 0x86, 0x30, 0x00, 0x06, 0x00, 0x18, 0x00, 0x66, 0x0c, 0x3c, 0x31, 0x98, 0x06, 0x06, 
0x60, 0x18, 0x18, 0x03, 0x06, 0x60, 0xcd, 0x86, 0x03, 0xcf, 0x3c, 0x66, 0x06, 0xc3, 0x60, 0x6c, 
0x33, 0x00, 0x30, 0xc1, 0x98, 0x66, 0x73, 0x1b, 0x07, 0x80, 0x63, 0x18, 0x30, 0xd8, 0x00, 0x01, 
0xe3, 0xf0, 0x3e, 0x3f, 0x1c, 0x7c, 0x3f, 0x7f, 0x30, 0xcc, 0xec, 0xfd, 0xe3, 0xf8, 0x78, 0xfc, 
0x0f, 0xde, 0x3e, 0xfb, 0x1b, 0x0f, 0x18, 0xf9, 0xf0, 0xff, 0x99, 0x8c, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x8c, 
0x06, 0x03, 0x60, 0xf1, 0x8e, 0x00, 0x67, 0xc7, 0x80, 0x00, 0x01, 0xf6, 0x07, 0x83, 0x0f, 0xf8, 
0x06, 0x33, 0xf8, 0x00, 0x79, 0x80, 0x31, 0x81, 0x98, 0x0e, 0x60, 0x30, 0x78, 0x1e, 0x07, 0x81, 
0xb0, 0x6c, 0x1b, 0x01, 0xe0, 0x60, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x03, 0x6c, 0xcc, 
0x0d, 0x81, 0xb0, 0x36, 0x06, 0xc0, 0xd8, 0x36, 0x36, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc3, 0xc3, 
0x19, 0x98, 0x78, 0x78, 0x78, 0x78, 0x78, 0x78, 0x7b, 0xc1, 0xe1, 0xc1, 0xc1, 0xc1, 0xc3, 0x33, 
0x30, 0x7c, 0xfe, 0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0x00, 0x3f, 0x63, 0x31, 0x98, 0xcc, 0x6c, 
0x37, 0xe1, 0x8c, 

0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0e, 0xef, 0xfd, 0xff, 0x83, 0x00, 0x60, 0x0c, 
0x03, 0x00, 0x01, 0x98, 0xff, 0xf8, 0x7f, 0x1f, 0xc3, 0x06, 0xc3, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x07, 0xfb, 0x01, 0xbc, 0x03, 0xc0, 
0x30, 0xcf, 0x03, 0x00, 0x00, 0x0c, 0x31, 0x80, 0xc0, 0x06, 0x01, 0x80, 0xd8, 0x3c, 0x0f, 0xc0, 
0x30, 0x1b, 0x06, 0x31, 0x98, 0x1c, 0x00, 0x0e, 0x01, 0xc6, 0xf6, 0x66, 0x31, 0x98, 0x06, 0x06, 
0x60, 0x18, 0x18, 0x03, 0x06, 0x60, 0xcf, 0x06, 0x03, 0xcf, 0x36, 0x66, 0x06, 0xc3, 0x60, 0x6c, 
0x61, 0x80, 0x30, 0xc1, 0x8c, 0xc6, 0x73, 0x0e, 0x07, 0x80, 0xc3, 0x0c, 0x31, 0x8c, 0x00, 0x03, 
0x33, 0x98, 0x60, 0x67, 0x36, 0x30, 0x67, 0x71, 0xb0, 0xcd, 0x8c, 0xe7, 0x33, 0x8c, 0xcc, 0xe6, 
0x19, 0xdc, 0x60, 0x63, 0x19, 0x99, 0xbd, 0x99, 0x99, 0x83, 0x19, 0x8c, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x98, 
0x06, 0x06, 0x30, 0xf1, 0x9b, 0x80, 0xcc, 0x6d, 0x9b, 0x00, 0x01, 0xcc, 0x00, 0x03, 0x00, 0x00, 
0x06, 0x31, 0xf8, 0x00, 0x19, 0xec, 0x33, 0x01, 0xb0, 0x03, 0x60, 0x30, 0xcc, 0x33, 0x0c, 0xc1, 
0xb0, 0x6c, 0x1b, 0x03, 0x60, 0x60, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x03, 0x6c, 0xcc, 
0x0d, 0x81, 0xb0, 0x36, 0x06, 0xc0, 0xcc, 0x66, 0x36, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc3, 0xc3, 
0x0d, 0x98, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xce, 0x63, 0x03, 0x63, 0x63, 0x63, 0x63, 0x33, 
0x30, 0xc6, 0xe3, 0x33, 0x19, 0x8c, 0xc6, 0x63, 0x30, 0x60, 0x66, 0x63, 0x31, 0x98, 0xcc, 0x66, 
0x67, 0x31, 0x8c, 

0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x3e, 0x0f, 0xef, 0xfd, 0xff, 0x83, 0x00, 0x60, 0x07, 
0x83, 0x00, 0x01, 0x98, 0xff, 0xf8, 0x7f, 0xbf, 0xc3, 0x06, 0xc1, 0xff, 0xff, 0xff, 0xf8, 0x0c, 
0x07, 0xdf, 0xef, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0xe1, 0xc1, 0xbf, 0xc7, 0x9c, 
0x30, 0xcd, 0x83, 0x00, 0x00, 0x0c, 0x31, 0x80, 0xc0, 0x0c, 0x07, 0x01, 0x98, 0x06, 0x0e, 0x60, 
0x30, 0x1e, 0x03, 0x71, 0x98, 0x70, 0x7f, 0xc3, 0x81, 0x8d, 0xb6, 0x66, 0x3f, 0x18, 0x06, 0x06, 
0x7e, 0x1f, 0x98, 0xf3, 0xfe, 0x60, 0xcf, 0x06, 0x03, 0xcf, 0xb3, 0x66, 0x06, 0xc6, 0x60, 0x6f, 
0xc0, 0xe0, 0x30, 0xc1, 0x8c, 0xc3, 0x76, 0x0c, 0x03, 0x01, 0x83, 0x0c, 0x31, 0x8c, 0x00, 0x00, 
0x33, 0x0c, 0xc0, 0xc3, 0x63, 0x30, 0xc3, 0x61, 0xb0, 0xcf, 0x0c, 0xc6, 0x33, 0x0d, 0x86, 0xc3, 
0x30, 0xd8, 0x60, 0x63, 0x19, 0x99, 0xbd, 0x8f, 0x19, 0x86, 0x19, 0x8c, 0x70, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
0x0f, 0xc6, 0x31, 0xf9, 0x98, 0xc0, 0xd8, 0x6d, 0xb6, 0x7f, 0xc0, 0x78, 0x00, 0x3f, 0xf0, 0x00, 
0x06, 0x31, 0xf8, 0x00, 0x1b, 0xb6, 0x36, 0x31, 0xb7, 0x03, 0xcc, 0x00, 0xcc, 0x33, 0x0c, 0xc1, 
0xb0, 0xcc, 0x31, 0x83, 0x7e, 0x60, 0x1f, 0x9f, 0x9f, 0x9f, 0x8c, 0xc6, 0x67, 0xe3, 0x66, 0xcc, 
0x0d, 0x81, 0xb0, 0x36, 0x06, 0xc0, 0xc6, 0xc6, 0x66, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc1, 0x83, 
0x0d, 0x98, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x36, 0x06, 0x36, 0x36, 0x36, 0x33, 0x33, 
0x31, 0x86, 0xc3, 0x61, 0xb0, 0xd8, 0x6c, 0x36, 0x18, 0x60, 0xcf, 0x63, 0x31, 0x98, 0xcc, 0x66, 
0x66, 0x19, 0x98, 

0x00, 0x00, 0x01, 0x80, 0x0f, 0x80, 0x60, 0x3f, 0x0d, 0x60, 0x78, 0xf0, 0x03, 0x07, 0xfe, 0x00, 
0xdf, 0xe0, 0x01, 0xb8, 0xff, 0xf8, 0x7f, 0x1f, 0xc3, 0x06, 0xc0, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0xe0, 0x70, 0xfe, 0x6c, 0xd8, 
0x30, 0xc0, 0x3f, 0xf0, 0xf8, 0x18, 0x31, 0x80, 0xc0, 0x0c, 0x01, 0x83, 0x18, 0x03, 0x0c, 0x60, 
0x30, 0x33, 0x01, 0xf0, 0x01, 0xc0, 0x00, 0x00, 0xe3, 0x0f, 0x36, 0x66, 0x31, 0x98, 0x06, 0x06, 
0x60, 0x18, 0x18, 0x33, 0x06, 0x60, 0xcd, 0x86, 0x07, 0x7b, 0xb3, 0x66, 0x06, 0xfc, 0x60, 0x6c, 
0x60, 0x30, 0x30, 0xc1, 0x8c, 0xc3, 0xde, 0x0e, 0x03, 0x01, 0x83, 0x0c, 0x31, 0x8c, 0x00, 0x00, 
0xf3, 0x0c, 0xc0, 0xc3, 0x7f, 0x30, 0xc3, 0x61, 0xb0, 0xce, 0x0c, 0xc6, 0x33, 0x0d, 0x86, 0xc3, 
0x30, 0xd8, 0x38, 0x63, 0x19, 0x99, 0xbd, 0x86, 0x0d, 0x86, 0x31, 0x86, 0xd9, 0xb1, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x98, 
0x03, 0x06, 0x30, 0x60, 0x0c, 0xc0, 0xd8, 0x67, 0xec, 0x00, 0xfe, 0x00, 0x00, 0x03, 0x00, 0x00, 
0x06, 0x30, 0xf9, 0x80, 0x0e, 0x1b, 0x36, 0x71, 0xed, 0x9e, 0xdc, 0x30, 0xcc, 0x33, 0x0c, 0xc3, 
0x18, 0xc6, 0x31, 0x87, 0xf0, 0x60, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x03, 0x66, 0xcc, 
0x0d, 0x81, 0xb0, 0x36, 0x06, 0xc0, 0xc3, 0x86, 0xc6, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc1, 0x83, 
0x0d, 0x8c, 0x3c, 0x3c, 0x3c, 0x3c, 0x3c, 0x3c, 0x3f, 0xf6, 0x07, 0xf7, 0xf7, 0xf7, 0xf3, 0x33, 
0x31, 0x86, 0xc3, 0x61, 0xb0, 0xd8, 0x6c, 0x36, 0x18, 0x00, 0xdb, 0x63, 0x31, 0x98, 0xcc, 0x63, 
0x66, 0x18, 0xd8, 

0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0d, 0x60, 0x70, 0x70, 0x03, 0x03, 0xfc, 0x00, 
0x43, 0x00, 0x01, 0xf8, 0x07, 0x00, 0x7e, 0x0f, 0xc3, 0x06, 0xc0, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x07, 0xf8, 0x18, 0x1e, 0x6c, 0xd8, 
0x30, 0xc0, 0x03, 0x00, 0x00, 0x18, 0x31, 0x80, 0xc0, 0x18, 0x00, 0xc3, 0xfc, 0x03, 0x0c, 0x60, 
0x60, 0x31, 0x80, 0x30, 0x00, 0xc0, 0x00, 0x00, 0xc3, 0x0f, 0x36, 0x7e, 0x30, 0xd8, 0x06, 0x06, 
0x60, 0x18, 0x18, 0x33, 0x06, 0x60, 0xcc, 0xc6, 0x06, 0x7b, 0xb1, 0xe6, 0x06, 0xc0, 0x60, 0x6c, 
0x60, 0x18, 0x30, 0xc1, 0x87, 0x83, 0xde, 0x1b, 0x03, 0x03, 0x03, 0x06, 0x33, 0x06, 0x00, 0x03, 
0xb3, 0x0c, 0xc0, 0xc3, 0x60, 0x30, 0xc3, 0x61, 0xb0, 0xcf, 0x0c, 0xc6, 0x33, 0x0d, 0x86, 0xc3, 
0x30, 0xd8, 0x0c, 0x63, 0x18, 0xf1, 0xbf, 0x06, 0x0f, 0x0c, 0x19, 0x8c, 0xcf, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x98, 
0x03, 0x03, 0x61, 0xf8, 0x07, 0x80, 0xcc, 0x60, 0x6c, 0x00, 0xc0, 0x00, 0x00, 0x03, 0x00, 0x00, 
0x06, 0x30, 0x79, 0x80, 0x00, 0x1b, 0x0d, 0xf0, 0x61, 0x81, 0xbc, 0x30, 0xfc, 0x3f, 0x0f, 0xc3, 
0xf8, 0xfe, 0x3f, 0x86, 0x30, 0x60, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x03, 0x63, 0xcc, 
0x0d, 0x81, 0xb0, 0x36, 0x06, 0xc0, 0xc3, 0x86, 0xc6, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc1, 0x83, 
0x19, 0x86, 0xec, 0xec, 0xec, 0xec, 0xec, 0xec, 0xec, 0x06, 0x06, 0x06, 0x06, 0x06, 0x03, 0x33, 
0x31, 0x86, 0xc3, 0x61, 0xb0, 0xd8, 0x6c, 0x36, 0x1b, 0xfc, 0xdb, 0x63, 0x31, 0x98, 0xcc, 0x63, 
0xc6, 0x18, 0xd8, 

0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0d, 0x60, 0x60, 0x30, 0x03, 0x01, 0xf8, 0x30, 
0x43, 0x00, 0x03, 0xf0, 0x07, 0x00, 0x7c, 0x07, 0xc3, 0x00, 0x00, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x60, 0x18, 0x36, 0x6c, 0x70, 
0x30, 0xc0, 0x03, 0x00, 0x00, 0x30, 0x31, 0x80, 0xc0, 0x30, 0x00, 0xc0, 0x18, 0x03, 0x0c, 0x60, 
0x60, 0x31, 0x80, 0x60, 0x00, 0x70, 0x7f, 0xc3, 0x80, 0x0f, 0x7c, 0xc3, 0x30, 0xcc, 0x06, 0x0c, 
0x60, 0x18, 0x0c, 0x33, 0x06, 0x60, 0xcc, 0xc6, 0x06, 0x79, 0xb0, 0xe3, 0x0c, 0xc0, 0x30, 0xcc, 
0x30, 0x18, 0x30, 0xc1, 0x87, 0x83, 0xde, 0x1b, 0x03, 0x03, 0x03, 0x06, 0x30, 0x00, 0x00, 0x03, 
0x33, 0x0c, 0xc0, 0xc3, 0x60, 0x30, 0xc3, 0x61, 0xb0, 0xcd, 0x8c, 0xc6, 0x33, 0x0d, 0x86, 0xc3, 
0x30, 0xd8, 0x06, 0x63, 0x18, 0xf0, 0xff, 0x0f, 0x0f, 0x18, 0x19, 0x8c, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x8c, 
0x03, 0x07, 0xf8, 0x61, 0x81, 0x80, 0x66, 0xc0, 0x36, 0x00, 0xc0, 0x00, 0x00, 0x03, 0x00, 0x00, 
0x06, 0x30, 0x78, 0x00, 0x00, 0x36, 0x0f, 0xf8, 0xc3, 0x01, 0xfe, 0x61, 0x86, 0x61, 0x98, 0x63, 
0x18, 0xc6, 0x60, 0xcc, 0x30, 0x30, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x06, 0x61, 0xc6, 
0x18, 0xc3, 0x18, 0x63, 0x0c, 0x61, 0x86, 0xc3, 0x8c, 0xc1, 0x98, 0x33, 0x06, 0x60, 0xc1, 0x83, 
0xf1, 0x86, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x06, 0x06, 0x06, 0x06, 0x06, 0x03, 0x33, 
0x31, 0x86, 0xc3, 0x61, 0xb0, 0xd8, 0x6c, 0x36, 0x18, 0x00, 0xf3, 0x63, 0x31, 0x98, 0xcc, 0x63, 
0xc6, 0x18, 0xf0, 

0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x40, 0x10, 0x03, 0x00, 0xf0, 0x30, 
0xc3, 0x00, 0x07, 0x80, 0x07, 0x00, 0x78, 0x03, 0xcf, 0xc0, 0x00, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x03, 0x63, 0x38, 0x36, 0x6e, 0x70, 
0x30, 0xc0, 0x03, 0x07, 0x03, 0x30, 0x1b, 0x00, 0xc0, 0x60, 0x18, 0xc0, 0x18, 0x66, 0x06, 0xc0, 
0xc0, 0x33, 0x80, 0xc1, 0x98, 0x1c, 0x00, 0x1e, 0x03, 0x0d, 0xd8, 0xc3, 0x31, 0x86, 0x06, 0x1c, 
0x60, 0x18, 0x0e, 0x33, 0x06, 0x61, 0xcc, 0x66, 0x06, 0x31, 0xb0, 0xe3, 0x9c, 0xc0, 0x39, 0xcc, 
0x33, 0x30, 0x30, 0x63, 0x07, 0x83, 0xde, 0x31, 0x83, 0x06, 0x03, 0x06, 0x30, 0x00, 0x00, 0x03, 
0x73, 0x98, 0x60, 0x67, 0x30, 0x30, 0x67, 0x61, 0xb0, 0xcc, 0xcc, 0xc6, 0x33, 0x0c, 0xcc, 0xe6, 
0x19, 0xd8, 0x66, 0x63, 0x38, 0xf0, 0xe7, 0x19, 0x86, 0x18, 0x19, 0x8c, 0x00, 0x31, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x87, 
0xc6, 0x00, 0x00, 0x61, 0x80, 0xc0, 0x39, 0x80, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x70, 0x78, 0x00, 0x00, 0x6c, 0x18, 0x30, 0xc6, 0x03, 0x0c, 0x61, 0x86, 0x61, 0x98, 0x66, 
0x0d, 0x86, 0x60, 0xcc, 0x30, 0x18, 0x18, 0x18, 0x18, 0x18, 0x0c, 0xc6, 0x63, 0x0e, 0x61, 0xc7, 
0x38, 0xe7, 0x1c, 0xe3, 0x18, 0x33, 0x0c, 0x63, 0x9c, 0x63, 0x0c, 0x61, 0x8c, 0x31, 0x81, 0x83, 
0x01, 0x86, 0xdc, 0xdc, 0xdc, 0xdc, 0xdc, 0xdc, 0xde, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x33, 
0x30, 0xcc, 0xc3, 0x33, 0x19, 0x8c, 0xc6, 0x63, 0x30, 0x60, 0x66, 0x67, 0x33, 0x99, 0xcc, 0xe1, 
0x87, 0x30, 0x70, 

0x00, 0x00, 0x00, 0xc2, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x00, 0x00, 0x03, 0x00, 0x60, 0x19, 
0x83, 0x00, 0x07, 0x00, 0x07, 0x00, 0x70, 0x01, 0xc7, 0x86, 0xc0, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x03, 0x61, 0xe0, 0x63, 0xc3, 0xdc, 
0x19, 0x80, 0x03, 0x06, 0x03, 0x30, 0x0e, 0x00, 0xc0, 0xfe, 0x0f, 0x80, 0x18, 0x3c, 0x03, 0x80, 
0xc0, 0x1e, 0x03, 0x81, 0x98, 0x06, 0x00, 0x30, 0x03, 0x06, 0x01, 0x81, 0xbf, 0x03, 0xf7, 0xf0, 
0x7f, 0x18, 0x03, 0xe3, 0x06, 0x67, 0x0c, 0x37, 0xf6, 0x31, 0xb0, 0x60, 0xf0, 0xc0, 0x0f, 0x0c, 
0x31, 0xe0, 0x30, 0x3e, 0x03, 0x01, 0x8c, 0x71, 0xc3, 0x0f, 0xfb, 0x03, 0x30, 0x00, 0x00, 0x01, 
0xf3, 0xf0, 0x3e, 0x3f, 0x1f, 0x30, 0x3f, 0x61, 0xb0, 0xcc, 0xec, 0xc6, 0x33, 0x0c, 0x78, 0xfc, 
0x0f, 0xd8, 0x7c, 0x39, 0xf8, 0x60, 0xc6, 0x39, 0xc6, 0x3f, 0x99, 0x8c, 0x00, 0x3f, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x83, 
0x0f, 0xe0, 0x00, 0x61, 0x98, 0xc0, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xf0, 0x00, 
0x07, 0xd8, 0x78, 0x00, 0x00, 0x00, 0x30, 0x31, 0x8f, 0x87, 0x0c, 0xc3, 0x03, 0xc0, 0xf0, 0x3e, 
0x0d, 0x83, 0xe0, 0xf8, 0x3f, 0x0f, 0xdf, 0xdf, 0xdf, 0xdf, 0xcc, 0xc6, 0x63, 0xf8, 0x60, 0xc1, 
0xe0, 0x3c, 0x07, 0x81, 0xf0, 0x1e, 0x18, 0x37, 0xf0, 0x3e, 0x07, 0xc0, 0xf8, 0x1f, 0x01, 0x83, 
0x01, 0xfc, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x73, 0xf1, 0xe1, 0xf1, 0xf1, 0xf1, 0xf3, 0x33, 
0x30, 0x78, 0xc3, 0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0x60, 0xfc, 0x3f, 0x1f, 0x8f, 0xc7, 0xe1, 
0x87, 0xe0, 0x60, 

0x00, 0x00, 0x00, 0x66, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 
0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x60, 0x00, 0xc3, 0x06, 0xc0, 0x00, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x19, 0x80, 0x00, 0x06, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x03, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 
0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x19, 0x8c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x83, 
0x00, 0x00, 0x00, 0x01, 0x8f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x78, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 
0x06, 0x00, 0x60, 

0x00, 0x00, 0x00, 0x3c, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x18, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 
0x0f, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0xf0, 0x01, 0xff, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 
0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x0d, 0x98, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 
0x06, 0x00, 0xc0, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 
0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 
0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x06, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 
0x06, 0x01, 0x80, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 
};
#else
ROMDATA PEGUSHORT MyriadPro12bEng_offset_table[257] = {
0x0000,0x0009,0x0014,0x001f,0x002a,0x0035,0x0040,0x004b,0x0056,0x0061,0x006c,0x0077,0x0082,0x008b,0x0094,0x009d,
0x00b0,0x00b9,0x00c2,0x00cb,0x00d4,0x00dd,0x00e6,0x00ef,0x00f8,0x0101,0x010a,0x0113,0x011c,0x0125,0x012e,0x0137,
0x0140,0x014b,0x0150,0x0157,0x0164,0x016e,0x0181,0x018e,0x0192,0x0199,0x01a0,0x01aa,0x01b7,0x01bc,0x01c3,0x01c8,
0x01d1,0x01dc,0x01e7,0x01f2,0x01fd,0x0208,0x0213,0x021e,0x0229,0x0234,0x023f,0x0245,0x024b,0x0258,0x0265,0x0272,
0x027b,0x028a,0x0297,0x02a2,0x02ad,0x02b9,0x02c4,0x02cd,0x02d9,0x02e5,0x02ec,0x02f4,0x02ff,0x0308,0x0316,0x0321,
0x032d,0x0338,0x0344,0x0350,0x035b,0x0366,0x0372,0x037e,0x038a,0x0396,0x03a2,0x03ac,0x03b3,0x03c2,0x03c9,0x03d6,
0x03e0,0x03e9,0x03f3,0x03fd,0x0405,0x040f,0x0419,0x041f,0x0429,0x0433,0x0438,0x043e,0x0448,0x044d,0x045c,0x0466,
0x0470,0x047a,0x0484,0x048b,0x0493,0x049a,0x04a4,0x04ae,0x04bc,0x04c6,0x04d0,0x04d9,0x04e3,0x04ed,0x04f7,0x0504,
0x050d,0x051d,0x052d,0x053d,0x054d,0x055d,0x056d,0x057d,0x058d,0x059d,0x05ad,0x05bd,0x05cd,0x05dd,0x05ed,0x05fd,
0x060d,0x061d,0x062d,0x063d,0x064d,0x065d,0x066d,0x067d,0x068d,0x069d,0x06ad,0x06bd,0x06cd,0x06dd,0x06ed,0x06fd,
0x070d,0x0712,0x0717,0x0721,0x072b,0x0735,0x0741,0x074b,0x0755,0x075e,0x076d,0x0775,0x0780,0x078d,0x0794,0x07a3,
0x07ad,0x07b5,0x07c2,0x07cb,0x07d4,0x07dd,0x07e7,0x07f1,0x07f7,0x0800,0x0809,0x0812,0x081d,0x082f,0x0841,0x0853,
0x085c,0x0869,0x0876,0x0883,0x0890,0x089d,0x08aa,0x08ba,0x08c5,0x08cf,0x08d9,0x08e3,0x08ed,0x08f4,0x08fb,0x0902,
0x0909,0x0915,0x0921,0x092d,0x0939,0x0945,0x0951,0x095d,0x096a,0x0976,0x0982,0x098e,0x099a,0x09a6,0x09b2,0x09bd,
0x09c7,0x09d1,0x09db,0x09e5,0x09ef,0x09f9,0x0a03,0x0a12,0x0a1a,0x0a24,0x0a2e,0x0a38,0x0a42,0x0a47,0x0a4c,0x0a51,
0x0a57,0x0a61,0x0a6b,0x0a75,0x0a7f,0x0a89,0x0a93,0x0a9d,0x0aaa,0x0ab4,0x0abe,0x0ac8,0x0ad2,0x0adc,0x0ae6,0x0af0,
0x0afa,
};

ROMDATA PEGUBYTE MyriadPro12bEng_data_table[6688] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x0f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x07, 0xfb, 0xfd, 0xfe, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0xf8, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 
0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0xc0, 0x1c, 
0x01, 0xc8, 0x18, 0xc0, 0x7c, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x18, 0x0e, 0x06, 0x63, 0x01, 0x87, 
0x33, 0x00, 0x00, 0xe4, 0x0c, 0x00, 0x30, 0x07, 0x00, 0xe4, 0x18, 0xc0, 0x00, 0x00, 0x00, 0x60, 
0x01, 0x80, 0x38, 0x0c, 0x60, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x0d, 0x83, 0x00, 0x60, 0x0c, 0x01, 0x80, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 
0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x18, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x1f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x01, 0x80, 0x36, 
0x02, 0x70, 0x18, 0xc0, 0x82, 0x00, 0x00, 0x00, 0x00, 0x60, 0x30, 0x1b, 0x06, 0x61, 0x83, 0x0d, 
0xb3, 0x00, 0x01, 0x38, 0x06, 0x00, 0x60, 0x0d, 0x81, 0x38, 0x18, 0xc0, 0x00, 0x00, 0x00, 0x30, 
0x03, 0x00, 0x6c, 0x0c, 0x60, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


0x00, 0x08, 0x84, 0x80, 0x90, 0x12, 0x02, 0x40, 0x90, 0x00, 0x00, 0x00, 0x03, 0x00, 0x60, 0x90, 
0x00, 0x00, 0x00, 0x38, 0x0f, 0x80, 0x60, 0x00, 0x03, 0x00, 0x00, 0x00, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x01, 0x83, 0x30, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 
0x00, 0x0c, 0x00, 0x1e, 0x00, 0x60, 0x00, 0x03, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc1, 0x87, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 


0x00, 0x0d, 0x84, 0xbc, 0x90, 0x12, 0x02, 0x40, 0x90, 0x00, 0x40, 0x10, 0x07, 0x80, 0x60, 0x67, 
0x87, 0x80, 0x01, 0xf8, 0x0f, 0x80, 0x70, 0x00, 0xc7, 0x86, 0xc1, 0xf8, 0x60, 0x00, 0x18, 0x1e, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0x10, 0x40, 0x81, 0xf0, 0x20, 
0x0f, 0x81, 0x86, 0x18, 0x4c, 0x80, 0x00, 0x00, 0x00, 0x01, 0x0f, 0x80, 0x60, 0x7e, 0x0f, 0xc0, 
0x0c, 0x3f, 0xc1, 0xf1, 0xff, 0x0f, 0x81, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x01, 
0xf8, 0x01, 0xc1, 0xfc, 0x07, 0xe7, 0xf0, 0x3f, 0xef, 0xf0, 0xfe, 0x60, 0x37, 0xe7, 0xec, 0x19, 
0x80, 0xc0, 0x19, 0x01, 0x0f, 0x87, 0xf8, 0x1f, 0x0f, 0xe0, 0x1f, 0x9f, 0xfb, 0x01, 0x98, 0x19, 
0x08, 0x58, 0x19, 0x81, 0xbf, 0xe6, 0x06, 0x31, 0x83, 0x03, 0x00, 0x00, 0x30, 0x00, 0x18, 0x00, 
0x00, 0x0c, 0x00, 0x30, 0x00, 0x60, 0x0c, 0x1b, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x01, 0x80, 0xc0, 0x00, 
0x00, 0x01, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe0, 0x00, 0x01, 0xff, 0xe1, 
0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 
0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 
0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 
0xff, 0xe0, 0x18, 0x08, 0x07, 0x80, 0x03, 0x03, 0x06, 0x07, 0xc1, 0x98, 0x1e, 0x03, 0xe0, 0x00, 
0x00, 0x00, 0x00, 0x78, 0x00, 0x01, 0xe0, 0x00, 0x0f, 0x07, 0x80, 0xc0, 0x00, 0x7f, 0x00, 0x00, 
0x0c, 0x1f, 0x00, 0x00, 0xc0, 0x80, 0x30, 0x20, 0x1e, 0x04, 0x03, 0x00, 0x70, 0x03, 0x80, 0x1c, 
0x00, 0xe0, 0x07, 0x00, 0x7c, 0x03, 0xff, 0x87, 0xe7, 0xfd, 0xff, 0x7f, 0xdf, 0xf7, 0xef, 0xdf, 
0xbf, 0x3f, 0x86, 0x01, 0x0f, 0x80, 0xf8, 0x0f, 0x80, 0xf8, 0x0f, 0x80, 0x00, 0x07, 0xd3, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x19, 0x81, 0xb0, 0x03, 0x18, 0x30, 0x03, 0x03, 0x81, 0xc8, 0x66, 0x08, 
0x80, 0x00, 0x00, 0x0c, 0x00, 0x60, 0x70, 0x33, 0x30, 0x66, 0x66, 0x1c, 0x1c, 0x86, 0x00, 0x30, 
0x38, 0x1c, 0x8c, 0x60, 0x00, 0x00, 0x01, 0x80, 0x18, 0x1c, 0x0c, 0xc0, 0x63, 0x00, 0x33, 0x00, 


0x00, 0x07, 0x03, 0x66, 0x6f, 0x8d, 0xf9, 0x9e, 0x68, 0x20, 0x60, 0x30, 0x0f, 0xc0, 0x60, 0x0c, 
0xcc, 0xc0, 0x01, 0x98, 0x0f, 0x80, 0x78, 0x01, 0xcf, 0xc6, 0xc3, 0x78, 0x60, 0x00, 0x18, 0x3f, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0x10, 0x43, 0xf3, 0x18, 0x40, 
0x18, 0xc1, 0x8c, 0x0c, 0x2d, 0x00, 0x00, 0x00, 0x00, 0x02, 0x18, 0xc0, 0x60, 0xc3, 0x18, 0x60, 
0x1c, 0x30, 0x03, 0x00, 0x03, 0x18, 0xc3, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x82, 
0x04, 0x01, 0xc1, 0x86, 0x1c, 0x36, 0x1c, 0x30, 0x0c, 0x03, 0x83, 0x60, 0x31, 0x80, 0x6c, 0x31, 
0x80, 0xe0, 0x39, 0x81, 0x38, 0xe6, 0x0c, 0x71, 0xcc, 0x30, 0x30, 0xc1, 0x83, 0x01, 0x98, 0x19, 
0x9c, 0xd8, 0x19, 0x81, 0x80, 0x66, 0x06, 0x31, 0x83, 0x04, 0x80, 0x00, 0x18, 0x00, 0x18, 0x00, 
0x00, 0x0c, 0x00, 0x30, 0x00, 0x60, 0x0c, 0x1b, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0x80, 0x60, 0x00, 
0x07, 0xf1, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0x08, 0x0c, 0xc0, 0x03, 0x03, 0x06, 0x0c, 0x21, 0x98, 0x61, 0x80, 0x30, 0x00, 
0x00, 0x00, 0x01, 0x86, 0x00, 0x02, 0x10, 0x00, 0x11, 0x88, 0xc1, 0x80, 0x00, 0xf9, 0x00, 0x00, 
0x3c, 0x31, 0x80, 0x03, 0xc1, 0x00, 0xf0, 0x40, 0x23, 0x08, 0x03, 0x00, 0x70, 0x03, 0x80, 0x1c, 
0x00, 0xe0, 0x07, 0x00, 0x38, 0x06, 0x60, 0x1c, 0x36, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0xe7, 0x01, 0x38, 0xe3, 0x8e, 0x38, 0xe3, 0x8e, 0x38, 0xe0, 0x00, 0x1c, 0x63, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x19, 0x81, 0xb0, 0x06, 0x18, 0x18, 0x06, 0x06, 0xc2, 0x70, 0x66, 0x08, 
0x80, 0x00, 0x00, 0x06, 0x00, 0xc0, 0xd8, 0x33, 0x18, 0xc9, 0x66, 0x66, 0x27, 0x03, 0x00, 0x60, 
0x6c, 0x27, 0x0c, 0x60, 0x00, 0x00, 0x00, 0xc0, 0x30, 0x36, 0x0c, 0xc0, 0xc3, 0x00, 0x33, 0x00, 


0x00, 0x00, 0x00, 0xc2, 0x0f, 0x81, 0xf8, 0x33, 0x0c, 0x60, 0x70, 0x70, 0x1f, 0xe0, 0x60, 0x18, 
0x0c, 0xc0, 0x01, 0xb8, 0x0f, 0x80, 0x7c, 0x03, 0xc3, 0x06, 0xc6, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0x20, 0x86, 0x9b, 0x18, 0x40, 
0x18, 0xc1, 0x8c, 0x0c, 0x1e, 0x00, 0x80, 0x00, 0x00, 0x02, 0x30, 0x61, 0xe0, 0xc1, 0x98, 0x30, 
0x2c, 0x30, 0x06, 0x00, 0x06, 0x30, 0x66, 0x0c, 0x00, 0x00, 0x0c, 0x00, 0x03, 0x00, 0x30, 0xc4, 
0x02, 0x03, 0x61, 0x83, 0x18, 0x36, 0x06, 0x30, 0x0c, 0x03, 0x03, 0x60, 0x31, 0x80, 0x6c, 0x61, 
0x80, 0xe0, 0x39, 0xc1, 0x30, 0x66, 0x06, 0x60, 0xcc, 0x18, 0x60, 0xc1, 0x83, 0x01, 0x98, 0x19, 
0x9c, 0xcc, 0x30, 0xc3, 0x00, 0xc6, 0x06, 0x31, 0x83, 0x08, 0x40, 0x00, 0x00, 0x00, 0x18, 0x00, 
0x00, 0x0c, 0x00, 0x30, 0x00, 0x60, 0x00, 0x03, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0x80, 0x60, 0x00, 
0x06, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x00, 0x08, 0x18, 0xc0, 0x01, 0x86, 0x06, 0x18, 0x00, 0x00, 0x80, 0x43, 0xf0, 0x00, 
0x00, 0x00, 0x02, 0x01, 0x00, 0x02, 0x10, 0x10, 0x01, 0x80, 0xc0, 0x00, 0x01, 0xf9, 0x00, 0x00, 
0x0c, 0x31, 0x80, 0x00, 0xc1, 0x00, 0x30, 0x40, 0x03, 0x08, 0x00, 0x00, 0xd8, 0x06, 0xc0, 0x36, 
0x01, 0xb0, 0x0d, 0x80, 0x6c, 0x06, 0x60, 0x18, 0x36, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0x67, 0x81, 0x30, 0x63, 0x06, 0x30, 0x63, 0x06, 0x30, 0x60, 0x00, 0x18, 0x73, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0xc3, 0x3f, 0xc6, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 


0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x31, 0x0e, 0xe0, 0x78, 0xf0, 0x3f, 0xf0, 0x60, 0x18, 
0x0c, 0xc0, 0x01, 0xfb, 0xff, 0xfe, 0x7e, 0x07, 0xc3, 0x06, 0xc6, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0xcc, 0xff, 0xec, 0x9b, 0x18, 0x80, 
0x18, 0xc1, 0x98, 0x06, 0x1e, 0x00, 0x80, 0x00, 0x00, 0x04, 0x30, 0x60, 0x60, 0x01, 0x80, 0x30, 
0x4c, 0x30, 0x0c, 0x00, 0x06, 0x30, 0x66, 0x0c, 0x61, 0x80, 0x30, 0x00, 0x00, 0xc0, 0x00, 0xc8, 
0xf9, 0x03, 0x61, 0x83, 0x30, 0x06, 0x06, 0x30, 0x0c, 0x06, 0x00, 0x60, 0x31, 0x80, 0x6c, 0xc1, 
0x80, 0xb0, 0x59, 0xe1, 0x60, 0x36, 0x06, 0xc0, 0x6c, 0x18, 0x60, 0x01, 0x83, 0x01, 0x8c, 0x31, 
0x9c, 0xc6, 0x60, 0xc3, 0x01, 0x86, 0x06, 0x79, 0x83, 0x08, 0x40, 0x00, 0x00, 0x1f, 0x1b, 0xc1, 
0xe1, 0xec, 0x7c, 0x7c, 0x7b, 0x6f, 0x0c, 0x7b, 0x0c, 0x66, 0xf3, 0xcd, 0xe0, 0xf8, 0xde, 0x0f, 
0x6d, 0xcf, 0x9f, 0xb0, 0x66, 0x1b, 0x08, 0x6c, 0x19, 0x86, 0xff, 0x0c, 0x01, 0x80, 0x60, 0x00, 
0x06, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0x3e, 0x18, 0x08, 0x10, 0xcc, 0x06, 0x18, 0x00, 0x01, 0x1e, 0x26, 0x30, 0x44, 
0x00, 0x00, 0x04, 0xf8, 0x80, 0x02, 0x10, 0x10, 0x03, 0x07, 0x80, 0x06, 0x0d, 0xf9, 0x18, 0x00, 
0x0c, 0x31, 0x91, 0x00, 0xc2, 0x00, 0x30, 0x80, 0x1e, 0x10, 0x03, 0x00, 0xd8, 0x06, 0xc0, 0x36, 
0x01, 0xb0, 0x0d, 0x80, 0x6c, 0x06, 0x60, 0x30, 0x06, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0x35, 0xc1, 0x60, 0x36, 0x03, 0x60, 0x36, 0x03, 0x60, 0x30, 0x82, 0x30, 0x9b, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0xc3, 0x30, 0x66, 0x18, 0x7c, 0x1f, 0x07, 0xc1, 0xf0, 0x7c, 0x1f, 
0x07, 0x9e, 0x0f, 0x0f, 0x83, 0xe0, 0xf8, 0x3e, 0x18, 0xc6, 0x18, 0x7f, 0x6f, 0x07, 0xc1, 0xf0, 
0x7c, 0x1f, 0x07, 0xc0, 0x30, 0x0f, 0xcc, 0x1b, 0x06, 0xc1, 0xb0, 0x66, 0x1b, 0x78, 0x61, 0x80, 


0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0e, 0xef, 0xfd, 0xff, 0x83, 0x00, 0x60, 0x0c, 
0x07, 0x80, 0x01, 0x9b, 0xff, 0xfe, 0x7f, 0x0f, 0xc3, 0x06, 0xc6, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0xff, 0xec, 0x83, 0x19, 0x3e, 
0x0d, 0x80, 0x18, 0x06, 0x2d, 0x00, 0x80, 0x00, 0x00, 0x04, 0x30, 0x60, 0x60, 0x01, 0x80, 0x60, 
0x8c, 0x3f, 0x0f, 0xe0, 0x0c, 0x18, 0xc6, 0x0c, 0x61, 0x80, 0xc0, 0x7f, 0xe0, 0x30, 0x00, 0xd1, 
0x98, 0x86, 0x31, 0x86, 0x30, 0x06, 0x03, 0x30, 0x0c, 0x06, 0x00, 0x60, 0x31, 0x80, 0x6d, 0x81, 
0x80, 0xb0, 0x59, 0x71, 0x60, 0x36, 0x06, 0xc0, 0x6c, 0x18, 0x70, 0x01, 0x83, 0x01, 0x8c, 0x31, 
0xbe, 0xc3, 0xc0, 0x66, 0x01, 0x86, 0x0f, 0xff, 0xc3, 0x10, 0x20, 0x00, 0x00, 0x31, 0x9c, 0x63, 
0x13, 0x1c, 0xc6, 0x30, 0xc7, 0x71, 0x8c, 0x1b, 0x18, 0x67, 0x1c, 0x6e, 0x31, 0x8c, 0xe3, 0x18, 
0xef, 0xd8, 0x4c, 0x30, 0x66, 0x1b, 0x1c, 0x66, 0x31, 0x86, 0x03, 0x0c, 0x01, 0x80, 0x60, 0x70, 
0x26, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0x69, 0x18, 0x05, 0xa0, 0xcc, 0x06, 0x0f, 0x00, 0x01, 0x31, 0x26, 0x30, 0xcc, 
0x00, 0x00, 0x04, 0xcc, 0x80, 0x02, 0x10, 0x10, 0x06, 0x00, 0xc0, 0x06, 0x0d, 0xf9, 0x18, 0x00, 
0x0c, 0x31, 0x99, 0x80, 0xc4, 0x00, 0x31, 0x00, 0x03, 0x20, 0x03, 0x01, 0x8c, 0x0c, 0x60, 0x63, 
0x03, 0x18, 0x18, 0xc0, 0xc6, 0x0c, 0x60, 0x30, 0x06, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0x34, 0xe1, 0x60, 0x36, 0x03, 0x60, 0x36, 0x03, 0x60, 0x30, 0x44, 0x30, 0x9b, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0x66, 0x30, 0x36, 0x70, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x31, 
0x8c, 0xf3, 0x18, 0x98, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x18, 0xc7, 0x71, 0x8c, 0x63, 0x18, 
0xc6, 0x31, 0x8c, 0x60, 0x30, 0x18, 0xcc, 0x1b, 0x06, 0xc1, 0xb0, 0x66, 0x1b, 0x8c, 0x61, 0x80, 


0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x3e, 0x0f, 0xef, 0xfd, 0xff, 0x83, 0x00, 0x60, 0x07, 
0x83, 0x00, 0x01, 0x9b, 0xff, 0xfe, 0x7f, 0x9f, 0xc3, 0x06, 0xc3, 0x78, 0x60, 0x00, 0x18, 0x0c, 
0x06, 0x00, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x20, 0x8c, 0x83, 0x19, 0x63, 
0x0f, 0x30, 0x18, 0x06, 0x4c, 0x80, 0x80, 0x00, 0x00, 0x04, 0x30, 0x60, 0x60, 0x03, 0x03, 0xc1, 
0x0c, 0x01, 0x8c, 0x30, 0x0c, 0x0f, 0x86, 0x0c, 0x61, 0x83, 0x00, 0x00, 0x00, 0x0c, 0x01, 0x93, 
0x18, 0x86, 0x31, 0xfe, 0x30, 0x06, 0x03, 0x3f, 0xef, 0xf6, 0x00, 0x7f, 0xf1, 0x80, 0x6f, 0x01, 
0x80, 0x98, 0x99, 0x31, 0x60, 0x36, 0x06, 0xc0, 0x6c, 0x18, 0x3f, 0x01, 0x83, 0x01, 0x8c, 0x31, 
0xb6, 0xc1, 0x80, 0x3c, 0x03, 0x06, 0x06, 0x79, 0x83, 0x20, 0x10, 0x00, 0x00, 0x00, 0xd8, 0x36, 
0x06, 0x0d, 0x83, 0x31, 0x83, 0x60, 0xcc, 0x1b, 0x30, 0x66, 0x18, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 
0x6c, 0x18, 0x0c, 0x30, 0x66, 0x19, 0x9c, 0xc3, 0x61, 0x86, 0x06, 0x18, 0x01, 0x80, 0x30, 0xf8, 
0x26, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0xc8, 0x18, 0x02, 0x40, 0x78, 0x06, 0x0c, 0xe0, 0x02, 0x60, 0x16, 0x31, 0x98, 
0x00, 0x00, 0x08, 0xcc, 0x40, 0x01, 0xe0, 0x10, 0x0c, 0x08, 0xc0, 0x06, 0x0c, 0xf9, 0x18, 0x00, 
0x0c, 0x31, 0x8c, 0xc0, 0xc4, 0x30, 0x31, 0x3c, 0x23, 0x21, 0x86, 0x01, 0x8c, 0x0c, 0x60, 0x63, 
0x03, 0x18, 0x18, 0xc0, 0xc6, 0x0c, 0x7f, 0xb0, 0x07, 0xfd, 0xff, 0x7f, 0xdf, 0xf1, 0x83, 0x06, 
0x0c, 0x7e, 0x34, 0x71, 0x60, 0x36, 0x03, 0x60, 0x36, 0x03, 0x60, 0x30, 0x28, 0x31, 0x1b, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0x3c, 0x30, 0x36, 0x18, 0x03, 0x00, 0xc0, 0x30, 0x0c, 0x03, 0x00, 
0xc0, 0x61, 0xb0, 0x30, 0x6c, 0x1b, 0x06, 0xc1, 0x98, 0xc6, 0x19, 0x83, 0x60, 0xd8, 0x36, 0x0d, 
0x83, 0x60, 0xd8, 0x30, 0x00, 0x31, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 0x66, 0x1b, 0x06, 0x61, 0x80, 


0x00, 0x00, 0x01, 0x80, 0x0f, 0x80, 0x60, 0x3f, 0x0d, 0x60, 0x78, 0xf0, 0x03, 0x07, 0xfe, 0x00, 
0xc3, 0x00, 0x01, 0x9b, 0xff, 0xfe, 0x7f, 0x3f, 0xc3, 0x06, 0xc1, 0xff, 0xff, 0xff, 0xf8, 0x0c, 
0x07, 0xdf, 0xef, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x41, 0x07, 0xf3, 0x1a, 0x63, 
0x1b, 0x30, 0x18, 0x06, 0x0c, 0x0f, 0xf8, 0x07, 0xc0, 0x08, 0x30, 0x60, 0x60, 0x06, 0x00, 0x62, 
0x0c, 0x00, 0xcc, 0x18, 0x18, 0x18, 0xc3, 0x0c, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x03, 0x03, 0x13, 
0x18, 0x86, 0x31, 0x83, 0x30, 0x06, 0x03, 0x30, 0x0c, 0x06, 0x1f, 0x60, 0x31, 0x80, 0x6d, 0x81, 
0x80, 0x98, 0x99, 0x19, 0x60, 0x36, 0x0c, 0xc0, 0x6c, 0x30, 0x1f, 0x81, 0x83, 0x01, 0x86, 0x61, 
0xb6, 0xc1, 0x80, 0x3c, 0x06, 0x06, 0x03, 0xcf, 0x03, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xd8, 0x36, 
0x06, 0x0d, 0x83, 0x31, 0x83, 0x60, 0xcc, 0x1b, 0x60, 0x66, 0x18, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 
0x6c, 0x1e, 0x0c, 0x30, 0x63, 0x31, 0xb6, 0xc1, 0xc0, 0xcc, 0x0c, 0x70, 0x01, 0x80, 0x1d, 0x8c, 
0x66, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0xc8, 0x3f, 0x84, 0x20, 0x30, 0x00, 0x18, 0x30, 0x02, 0x60, 0x13, 0xf3, 0x30, 
0x7f, 0xe3, 0xe8, 0xf8, 0x40, 0x00, 0x01, 0xff, 0x1f, 0x87, 0x80, 0x06, 0x0c, 0x79, 0x00, 0x00, 
0x3f, 0x1f, 0x06, 0x60, 0xc8, 0x70, 0x32, 0x46, 0x1e, 0x43, 0x8c, 0x01, 0x8c, 0x0c, 0x60, 0x63, 
0x03, 0x18, 0x18, 0xc0, 0xc6, 0x0c, 0x60, 0x30, 0x06, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0x34, 0x39, 0x60, 0x36, 0x03, 0x60, 0x36, 0x03, 0x60, 0x30, 0x10, 0x31, 0x1b, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0x3c, 0x30, 0x36, 0x0c, 0x3f, 0x0f, 0xc3, 0xf0, 0xfc, 0x3f, 0x0f, 
0xc3, 0xe1, 0xb0, 0x30, 0x6c, 0x1b, 0x06, 0xc1, 0x98, 0xc6, 0x19, 0x83, 0x60, 0xd8, 0x36, 0x0d, 
0x83, 0x60, 0xd8, 0x33, 0xff, 0x31, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 0x63, 0x33, 0x06, 0x33, 0x00, 


0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0d, 0x60, 0x70, 0x70, 0x03, 0x03, 0xfc, 0x00, 
0x5f, 0xe0, 0x01, 0xbb, 0xff, 0xfe, 0x7e, 0x1f, 0xc3, 0x06, 0xc0, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0xc6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0xff, 0xc0, 0x99, 0xf2, 0x63, 
0x31, 0xb0, 0x18, 0x06, 0x00, 0x00, 0x80, 0x00, 0x00, 0x08, 0x30, 0x60, 0x60, 0x0c, 0x00, 0x33, 
0xff, 0x00, 0xcc, 0x18, 0x18, 0x30, 0x61, 0xfc, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x03, 0x06, 0x13, 
0x18, 0x8f, 0xf9, 0x81, 0xb0, 0x06, 0x03, 0x30, 0x0c, 0x06, 0x03, 0x60, 0x31, 0x80, 0x6c, 0xc1, 
0x80, 0x8d, 0x19, 0x1d, 0x60, 0x37, 0xf8, 0xc0, 0x6f, 0xe0, 0x01, 0xc1, 0x83, 0x01, 0x86, 0x61, 
0xe3, 0xc3, 0xc0, 0x18, 0x0c, 0x06, 0x03, 0xcf, 0x03, 0x00, 0x00, 0x00, 0x00, 0x30, 0xd8, 0x36, 
0x06, 0x0d, 0xff, 0x31, 0x83, 0x60, 0xcc, 0x1b, 0xe0, 0x66, 0x18, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 
0x6c, 0x0f, 0x8c, 0x30, 0x63, 0x31, 0xb6, 0xc1, 0xc0, 0xcc, 0x18, 0x18, 0x01, 0x80, 0x31, 0x07, 
0xc6, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0xc8, 0x18, 0x04, 0x20, 0xfc, 0x00, 0x18, 0x30, 0x02, 0x60, 0x10, 0x03, 0x30, 
0x00, 0x20, 0x08, 0xd8, 0x40, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x09, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x60, 0x08, 0xb0, 0x02, 0x06, 0x00, 0x45, 0x98, 0x03, 0xfe, 0x1f, 0xf0, 0xff, 
0x87, 0xfc, 0x3f, 0xe1, 0xff, 0x1f, 0xe0, 0x30, 0x06, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0x34, 0x1d, 0x60, 0x36, 0x03, 0x60, 0x36, 0x03, 0x60, 0x30, 0x28, 0x32, 0x1b, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0x18, 0x30, 0x36, 0x0c, 0xc3, 0x30, 0xcc, 0x33, 0x0c, 0xc3, 0x30, 
0xcc, 0x7f, 0xb0, 0x3f, 0xef, 0xfb, 0xfe, 0xff, 0x98, 0xc6, 0x19, 0x83, 0x60, 0xd8, 0x36, 0x0d, 
0x83, 0x60, 0xd8, 0x30, 0x00, 0x32, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 0x63, 0x33, 0x06, 0x33, 0x00, 


0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0d, 0x60, 0x60, 0x30, 0x03, 0x01, 0xf8, 0x30, 
0x43, 0x00, 0x01, 0xf8, 0x0f, 0x80, 0x7c, 0x0f, 0xc3, 0x06, 0xc0, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x01, 0xff, 0xc0, 0x98, 0x04, 0x63, 
0x30, 0xe0, 0x18, 0x06, 0x00, 0x00, 0x80, 0x00, 0x00, 0x10, 0x30, 0x60, 0x60, 0x18, 0x00, 0x30, 
0x0c, 0x00, 0xcc, 0x18, 0x30, 0x30, 0x60, 0x0c, 0x00, 0x03, 0x00, 0x7f, 0xe0, 0x0c, 0x06, 0x13, 
0x18, 0x8c, 0x19, 0x81, 0xb0, 0x06, 0x06, 0x30, 0x0c, 0x06, 0x03, 0x60, 0x31, 0x80, 0x6c, 0x61, 
0x80, 0x86, 0x19, 0x0f, 0x60, 0x36, 0x00, 0xc0, 0x6c, 0x30, 0x00, 0xc1, 0x83, 0x01, 0x86, 0x61, 
0xe3, 0xc6, 0x60, 0x18, 0x0c, 0x06, 0x03, 0xcf, 0x03, 0x00, 0x00, 0x00, 0x00, 0x60, 0xd8, 0x36, 
0x06, 0x0d, 0x80, 0x31, 0x83, 0x60, 0xcc, 0x1b, 0x30, 0x66, 0x18, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 
0x6c, 0x03, 0xcc, 0x30, 0x63, 0x31, 0xb6, 0xc1, 0xc0, 0xcc, 0x30, 0x0c, 0x01, 0x80, 0x61, 0x03, 
0x86, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0xc8, 0x18, 0x02, 0x40, 0x30, 0x06, 0x18, 0x30, 0x02, 0x60, 0x10, 0x01, 0x98, 
0x00, 0x20, 0x08, 0xcc, 0x40, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x09, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0xc0, 0x11, 0x30, 0x04, 0x0c, 0x00, 0x89, 0x98, 0x03, 0x06, 0x18, 0x30, 0xc1, 
0x86, 0x0c, 0x30, 0x61, 0x83, 0x18, 0x60, 0x30, 0x06, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0x34, 0x0f, 0x60, 0x36, 0x03, 0x60, 0x36, 0x03, 0x60, 0x30, 0x44, 0x32, 0x1b, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0x18, 0x30, 0x66, 0x0d, 0x83, 0x60, 0xd8, 0x36, 0x0d, 0x83, 0x60, 
0xd8, 0x60, 0x30, 0x30, 0x0c, 0x03, 0x00, 0xc0, 0x18, 0xc6, 0x19, 0x83, 0x60, 0xd8, 0x36, 0x0d, 
0x83, 0x60, 0xd8, 0x30, 0x30, 0x34, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 0x63, 0x33, 0x06, 0x33, 0x00, 


0x00, 0x00, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x40, 0x10, 0x03, 0x00, 0xf0, 0x30, 
0xc3, 0x00, 0x03, 0xf0, 0x0f, 0x80, 0x78, 0x07, 0xc3, 0x00, 0x00, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x41, 0x0c, 0x98, 0x08, 0x63, 
0x30, 0x60, 0x18, 0x06, 0x00, 0x00, 0x80, 0xe0, 0x0c, 0x10, 0x30, 0x60, 0x60, 0x30, 0x18, 0x30, 
0x0c, 0x60, 0xcc, 0x18, 0x30, 0x30, 0x60, 0x18, 0x61, 0xc0, 0xc0, 0x00, 0x00, 0x30, 0x00, 0x11, 
0x99, 0x18, 0x0d, 0x81, 0x98, 0x36, 0x06, 0x30, 0x0c, 0x03, 0x03, 0x60, 0x31, 0x80, 0x6c, 0x31, 
0x80, 0x86, 0x19, 0x07, 0x30, 0x66, 0x00, 0x60, 0xcc, 0x18, 0x60, 0xc1, 0x83, 0x01, 0x83, 0xc1, 
0xe3, 0xcc, 0x30, 0x18, 0x18, 0x06, 0x01, 0x86, 0x03, 0x00, 0x00, 0x00, 0x00, 0x60, 0xd8, 0x36, 
0x06, 0x0d, 0x80, 0x31, 0x83, 0x60, 0xcc, 0x1b, 0x18, 0x66, 0x18, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 
0x6c, 0x00, 0xcc, 0x30, 0x61, 0xe0, 0xe3, 0x83, 0x60, 0x78, 0x60, 0x0c, 0x01, 0x80, 0x60, 0x00, 
0x06, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0xc8, 0x18, 0x05, 0xa0, 0x30, 0x06, 0x0e, 0x60, 0x01, 0x31, 0x20, 0x00, 0xcc, 
0x00, 0x20, 0x04, 0xc6, 0x80, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x06, 0x0c, 0x09, 0x00, 0x00, 
0x00, 0x00, 0x19, 0x80, 0x22, 0x30, 0x08, 0x18, 0x01, 0x11, 0x98, 0x66, 0x03, 0x30, 0x19, 0x80, 
0xcc, 0x06, 0x60, 0x33, 0x01, 0xb0, 0x60, 0x18, 0x36, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0x64, 0x07, 0x30, 0x63, 0x06, 0x30, 0x63, 0x06, 0x30, 0x60, 0x82, 0x1c, 0x33, 0x01, 
0xb0, 0x1b, 0x01, 0xb0, 0x18, 0x18, 0x3f, 0xc6, 0x0d, 0x83, 0x60, 0xd8, 0x36, 0x0d, 0x83, 0x60, 
0xd8, 0x60, 0x30, 0x30, 0x0c, 0x03, 0x00, 0xc0, 0x18, 0xc6, 0x19, 0x83, 0x60, 0xd8, 0x36, 0x0d, 
0x83, 0x60, 0xd8, 0x30, 0x30, 0x34, 0x6c, 0x1b, 0x06, 0xc1, 0xb0, 0x61, 0xe3, 0x06, 0x1e, 0x00, 


0x00, 0x00, 0x00, 0xc2, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x00, 0x00, 0x03, 0x00, 0x60, 0x19, 
0x83, 0x00, 0x07, 0x80, 0x0f, 0x80, 0x70, 0x03, 0xcf, 0xc0, 0x00, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x82, 0x0c, 0xb0, 0x08, 0x63, 
0x18, 0xf0, 0x18, 0x06, 0x00, 0x00, 0x80, 0xc0, 0x0c, 0x10, 0x18, 0xc0, 0x60, 0x60, 0x18, 0x60, 
0x0c, 0x61, 0x86, 0x30, 0x60, 0x18, 0xc0, 0x30, 0x61, 0x80, 0x30, 0x00, 0x00, 0xc0, 0x06, 0x08, 
0xee, 0x18, 0x0d, 0x83, 0x1c, 0x36, 0x1c, 0x30, 0x0c, 0x03, 0x83, 0x60, 0x31, 0x80, 0xcc, 0x19, 
0x80, 0x80, 0x19, 0x03, 0x38, 0xe6, 0x00, 0x71, 0xcc, 0x0c, 0x61, 0x81, 0x81, 0x83, 0x03, 0xc1, 
0xc1, 0xd8, 0x18, 0x18, 0x30, 0x06, 0x01, 0x86, 0x03, 0x00, 0x00, 0x00, 0x00, 0x61, 0xdc, 0x63, 
0x13, 0x1c, 0xc3, 0x30, 0xc7, 0x60, 0xcc, 0x1b, 0x0c, 0x66, 0x18, 0x6c, 0x19, 0x8c, 0xe3, 0x18, 
0xec, 0x10, 0xcc, 0x18, 0xe1, 0xe0, 0xe3, 0x86, 0x30, 0x78, 0xc0, 0x0c, 0x01, 0x80, 0x60, 0x00, 
0x06, 0x31, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x20, 0x00, 0x01, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 0x00, 0x21, 
0x00, 0x20, 0x18, 0x69, 0x30, 0x08, 0x10, 0x30, 0x06, 0x01, 0xe0, 0x01, 0x1e, 0x20, 0x00, 0x44, 
0x00, 0x20, 0x04, 0x00, 0x80, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x06, 0x1c, 0x09, 0x00, 0x00, 
0x00, 0x00, 0x11, 0x00, 0x23, 0xf8, 0x08, 0x30, 0x01, 0x1f, 0xcc, 0x66, 0x03, 0x30, 0x19, 0x80, 
0xcc, 0x06, 0x60, 0x33, 0x01, 0xb0, 0x60, 0x1c, 0x36, 0x01, 0x80, 0x60, 0x18, 0x01, 0x83, 0x06, 
0x0c, 0x30, 0xe4, 0x03, 0x38, 0xe3, 0x8e, 0x38, 0xe3, 0x8e, 0x38, 0xe0, 0x00, 0x0c, 0x71, 0x83, 
0x18, 0x31, 0x83, 0x18, 0x30, 0x18, 0x30, 0x06, 0x19, 0x87, 0x61, 0xd8, 0x76, 0x1d, 0x87, 0x61, 
0xd8, 0xf1, 0x98, 0x98, 0x66, 0x19, 0x86, 0x61, 0x98, 0xc6, 0x18, 0xc6, 0x60, 0xcc, 0x63, 0x18, 
0xc6, 0x31, 0x8c, 0x60, 0x30, 0x18, 0xc6, 0x39, 0x8e, 0x63, 0x98, 0xe1, 0xe3, 0x8c, 0x1e, 0x00, 


0x00, 0x00, 0x00, 0x66, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 
0x03, 0x00, 0x07, 0x00, 0x0f, 0x80, 0x60, 0x01, 0xc7, 0x86, 0xc0, 0x78, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x82, 0x07, 0xe0, 0x10, 0x3e, 
0x0f, 0x98, 0x0c, 0x0c, 0x00, 0x00, 0x00, 0xc0, 0x0c, 0x20, 0x0f, 0x81, 0xf8, 0xff, 0x8f, 0xc0, 
0x0c, 0x3f, 0x03, 0xe0, 0x60, 0x0f, 0x83, 0xe0, 0x61, 0x80, 0x0c, 0x00, 0x03, 0x00, 0x06, 0x04, 
0x00, 0x18, 0x0d, 0xfe, 0x07, 0xe7, 0xf0, 0x3f, 0xec, 0x00, 0xfe, 0x60, 0x37, 0xef, 0x8c, 0x0d, 
0xfe, 0x80, 0x19, 0x01, 0x0f, 0x86, 0x00, 0x1f, 0x0c, 0x06, 0x3f, 0x01, 0x80, 0xfe, 0x03, 0xc1, 
0xc1, 0xd8, 0x18, 0x18, 0x3f, 0xe6, 0x01, 0x86, 0x03, 0x00, 0x00, 0x00, 0x00, 0x3e, 0xdb, 0xc1, 
0xe1, 0xec, 0x7e, 0x30, 0x7b, 0x60, 0xcc, 0x1b, 0x06, 0x66, 0x18, 0x6c, 0x18, 0xf8, 0xde, 0x0f, 
0x6c, 0x0f, 0x87, 0x8f, 0x60, 0xc0, 0xc1, 0x8c, 0x18, 0x30, 0xff, 0x0c, 0x01, 0x80, 0x60, 0x00, 
0x07, 0xf1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe0, 0x00, 0x01, 0xff, 0xe1, 
0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 
0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 
0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 0xff, 0xe1, 
0xff, 0xe0, 0x18, 0x3e, 0x3f, 0xc0, 0x00, 0x30, 0x06, 0x00, 0x30, 0x00, 0x80, 0x40, 0x00, 0x00, 
0x00, 0x20, 0x02, 0x01, 0x00, 0x00, 0x01, 0xff, 0x00, 0x00, 0x00, 0x07, 0xec, 0x09, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x40, 0x30, 0x10, 0x7e, 0x02, 0x01, 0x87, 0xc6, 0x03, 0x30, 0x19, 0x80, 
0xcc, 0x06, 0x60, 0x33, 0x01, 0xb0, 0x7f, 0x87, 0xe7, 0xfd, 0xff, 0x7f, 0xdf, 0xf7, 0xef, 0xdf, 
0xbf, 0x3f, 0x84, 0x01, 0x0f, 0x80, 0xf8, 0x0f, 0x80, 0xf8, 0x0f, 0x80, 0x00, 0x17, 0xc0, 0xfe, 
0x0f, 0xe0, 0xfe, 0x0f, 0xe0, 0x18, 0x30, 0x06, 0x70, 0xfb, 0x3e, 0xcf, 0xb3, 0xec, 0xfb, 0x3e, 
0xcf, 0x9f, 0x0f, 0x0f, 0xc3, 0xf0, 0xfc, 0x3f, 0x18, 0xc6, 0x18, 0x7c, 0x60, 0xc7, 0xc1, 0xf0, 
0x7c, 0x1f, 0x07, 0xc0, 0x00, 0x1f, 0x83, 0xd8, 0xf6, 0x3d, 0x8f, 0x60, 0xc3, 0x78, 0x0c, 0x00, 


0x00, 0x00, 0x00, 0x3c, 0x0c, 0x00, 0x60, 0x33, 0x0c, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0xc3, 0x06, 0xc0, 0x00, 0x00, 0x30, 0x18, 0x0c, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x0c, 0x0c, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x0c, 0x01, 0x80, 0x60, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x30, 0x00, 0x61, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x01, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x09, 0x00, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x0c, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x18, 0x00, 
0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 
0x00, 0x00, 0x06, 0x18, 0x00, 0x00, 0x01, 0x80, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 
0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x03, 0x00, 0x03, 0xff, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xc6, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x06, 0x01, 0x80, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x06, 0x08, 0x60, 0x00, 0x1e, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x09, 0x00, 0x08, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc3, 0x00, 0x0c, 0x00, 


0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x03, 0x30, 0x00, 0x00, 0x01, 0x80, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x03, 0xc1, 0x87, 0x80, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x06, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x09, 0x00, 0x70, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x83, 0x00, 0x18, 0x00, 
};
#endif

#if defined(_FONT_PLASTIMO_)
xFONTYY MyriadPro12bEng_Font = {0x01, 15, 4, 19, 0, 0, 19, 259, 0x0000, 0x00ff,
(PEGUSHORT *) MyriadPro12bEng_offset_table,&MyriadPro12bExt_Font,
(PEGUBYTE *) MyriadPro12bEng_data_table};
#else
xFONTYY MyriadPro12bEng_Font = {0x01, 16, 3, 19, 0, 0, 19, 352, 0x0000, 0x00ff,
(PEGUSHORT *) MyriadPro12bEng_offset_table,&MyriadPro12bExt_Font,
(PEGUBYTE *) MyriadPro12bEng_data_table};
#endif


/*----------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------*/

