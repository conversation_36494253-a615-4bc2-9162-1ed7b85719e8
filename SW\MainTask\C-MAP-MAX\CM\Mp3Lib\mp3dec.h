#ifndef		__MP3DEC__
#define		__MP3DEC__


#ifdef __cplusplus
extern "C" {
#endif

#include <math.h>
#include "cmaptype.h"
	
typedef Byte byte;
typedef Long uint;
typedef Word ushort;


#define MP3_ERROR_UNKNOWN				1
#define MP3_ERROR_INVALID_PARAMETER		2
#define MP3_ERROR_INVALID_SYNC			3
#define MP3_ERROR_INVALID_HEADER		4
#define MP3_ERROR_OUT_OF_BUFFER			5

typedef struct nsMPEG_HEADER{
	SLong			version;		//1:MPEG-1, 2:MPEG-2, 3:MPEG-2.5
	SLong			layer;			//1:Layer1, 2:Layer2, 3:Layer3
	SLong			error_prot;		//1:CRC on, 0:CRC off
	SLong			br_index;
	SLong			fr_index;
	SLong			padding;
	SLong			extension;
	SLong			mode;
	SLong			mode_ext;
	SLong			copyright;
	SLong			original;
	SLong			emphasis;
} sMPEG_HEADER;

typedef struct nsMPEG_DECODE_OPTION{
	SLong			reduction;
	SLong			convert;
	SLong			freqLimit;
} sMPEG_DECODE_OPTION;

typedef struct nsMPEG_DECODE_INFO{
	sMPEG_HEADER	header;
	SLong			channels;		
	SLong			bitsPerSample;	
	SLong			frequency;		
	SLong			bitRate;		

	SLong			frames;			
	SLong			skipSize;		
	SLong			dataSize;		

	SLong			minInputSize;	
	SLong			maxInputSize;	
	SLong			outputSize;		
} sMPEG_DECODE_INFO;

typedef struct nsMPEG_DECODE_PARAM{
	sMPEG_HEADER	header;
	SLong			bitRate;		

	void*		inputBuf;
	SLong			inputSize;
	void*		outputBuf;
	SLong			outputSize;
} sMPEG_DECODE_PARAM;

void mp3DecodeInit(void);
SLong mp3GetLastError(void);
SLong mp3SetDecodeOption(sMPEG_DECODE_OPTION* option);
void mp3GetDecodeOption(sMPEG_DECODE_OPTION* option);
SLong mp3SetEqualizer(SLong* value);

SLong mp3FindSync(Byte* buf, SLong size, SLong* sync);
SLong mp3GetDecodeInfo(Byte* mpeg, SLong size, sMPEG_DECODE_INFO* info, SLong decFlag);
SLong mp3DecodeStart(Byte* buf, SLong size);
SLong mp3DecodeFrame(sMPEG_DECODE_PARAM* param);

void mp3MuteStart(sMPEG_DECODE_PARAM* param);
void mp3MuteEnd(sMPEG_DECODE_PARAM* param);


#ifdef __cplusplus
}
#endif

#endif	/* #ifndef		__MP3DEC__ */