/*...........................................................................*/
/*.                  File Name : SYSMMU.C                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.29                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "DataType.h"
#include "ArmCpu.h"
#include "CpuAddr.h"
#include "SysConst.h"
#include "SysLib.h"
#include "SysMMU.h"

#include <string.h>

void  SysMmuInit(void)
{
      int   i,k;

      SysDisableDCache();
      SysDisableICache();
      memset((UCHAR *)MMU_TTB_START_ADDRESS,0x00,4 * 1024 * sizeof(DWORD));

      DWORD *pTransTableBase;
      pTransTableBase = (DWORD *)MMU_TTB_START_ADDRESS;
      for (i = 0;i < 4096;i++)
      {
            *pTransTableBase++ = ARM_RW_NCNB | (i << 20);
      }

      //If write-back is used,the DCache should be cleared.
      for (i = 0;i < 64;i++)
    	     for(k = 0;k < 8;k++)
    	         SysCleanInvalidateDCacheIndex((i << 26) | (k << 5));
      SysInvalidateICache();
      SysDisableMMU();
      SysInvalidateTLB();

      // SysSetMmuMTT(DWORD dVirtAddrStart,DWORD dVirtAddrEnd,DWORD dPhysAddrStart,DWORD dAttribute)
      SysSetMmuMTT(CPU_MCUA_VIRT_BASE_ADDR,MCUA_SECT_TOTAL_SIZE,CPU_MCUA_PHSY_BASE_ADDR,ARM_RW_CNB);
      SysSetMmuMTT(CPU_MCUS_VIRT_BASE_ADDR,MCUS_SECT_TOTAL_SIZE,CPU_MCUS_PHSY_BASE_ADDR,ARM_RW_NCNB);
      SysSetMmuMTT(CPU_IORW_VIRT_BASE_ADDR,IORW_SECT_TOTAL_SIZE,CPU_IORW_PHSY_BASE_ADDR,ARM_RW_NCNB);

      SysSetTTBase0(MMU_TTB_START_ADDRESS | (0 << 3) | (0 << 2) | (0 << 1) | (0 << 0));
      SysSetTTBase1(MMU_TTB_START_ADDRESS | (0 << 3) | (0 << 2) | (0 << 1) | (0 << 0));
      SysSetTTBaseControl((0 << 5) | (0 << 4) | (0 << 0));

      SysSetDomain(0x55555550 | ARM_DOMAIN1_ATTR | ARM_DOMAIN0_ATTR);    // DOMAIN1: no_access, DOMAIN0,2~15=client(AP is checked)
      SysSetProcessID(0x0);
      SysEnableAlignFault();

      SysEnableMMU();
      SysEnableICache();
      SysEnableDCache();                                             // DCache should be turned on after MMU is turned on.
}
void  SysSetMmuMTT(DWORD dVirtAddrStart,DWORD dVirtAddrSize,DWORD dPhysAddrStart,DWORD dAttribute)
{
      DWORD *pTransTableBase;
      int   i,nSection;

      pTransTableBase = (DWORD *)(MMU_TTB_START_ADDRESS + (dVirtAddrStart >> 20) * 4);
      nSection = dVirtAddrSize / MEM_SECTION_SIZE;                 // 1MB Section
      for (i = 0;i < nSection;i++)
           *pTransTableBase++ = dAttribute | (((dPhysAddrStart >> 20) + i) << 20);
}

