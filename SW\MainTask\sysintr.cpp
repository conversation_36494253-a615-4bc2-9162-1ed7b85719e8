/*...........................................................................*/
/*.                  File Name : SYSINTR.CPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2004.01.31                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#if defined(__CPU_AU1100__)

#include "type.hpp"
#include "sysconst.h"
#include "au1100.h"
#include "syslib.h"
#include "comlib.h"
#include "keybd.hpp"
#include "lcd1100.hpp"
#include "screen.hpp"
#include "time.hpp"
#include "uart.hpp"
#include "buzzer.hpp"
#include "sysintr.hpp"
#include "lib/sdcard.h"

#include <stdlib.h>
#include <string.h>

///////////////////////////////////////////////////////////////////////////////
extern cKEYBD   *G_pKeyBD;
extern cTIME    *G_pTime;
extern cUART    *G_pUart0;
extern cUART    *G_pUart1;
extern cUART    *G_pUart2;
extern cUART    *G_pUart3;
extern cSCREEN  *G_pScreen;
extern cBUZZER  *G_pBuzzer;
/////////////////////////////////////////////////////////////////////////////

void  SetGlobalInterruptMask(DWORD Mask,DWORD IntVectSeg)
{
	    DWORD Status,Cause;

	    Cause  = Read32BitCP0Register(CP0_CAUSE);
	    Cause  = Cause  & 0xff7fffff;     // IV = 0 (general int-vector = 0x180)
      Write32BitCP0Register(CP0_CAUSE,Cause);
	    Status = Read32BitCP0Register(CP0_STATUS);
	    Status = Status & 0xfffffff9;     // ERL/EXL = 0;
	    if (Mask)
	        Status = Status | 0x00000001; // IE  = 1;
	    else
	        Status = Status & 0xfffffffe; // IE  = 0;
	    if (IntVectSeg == KSEG0_INT_VECTOR_BEV)
	        Status = Status & 0xffbfffff; // BEV = 0;
	    else
	        Status = Status | 0x00400000; // BEV = 1;
      Write32BitCP0Register(CP0_CAUSE,0x00000000);
      __asm__ __volatile__("sync");
      Write32BitCP0Register(CP0_STATUS,Status);
      __asm__ __volatile__("sync");
}
void  SetLocalInterruptMask(DWORD IntNo,DWORD Mask)
{
	    DWORD Status;
	    DWORD Temp;

	    Status = Read32BitCP0Register(CP0_STATUS);
	    Temp = 1 << IntNo;
	    if (Mask)
	        Status = Status | Temp;
	    else
	       {
	        Temp = Temp ^ 0xffffffff;
	        Status = Status & Temp;
	       }
      Write32BitCP0Register(CP0_STATUS,Status);
      __asm__ __volatile__("sync");
}
void  SetInterruptSource(DWORD BaseAddr,DWORD IntNo,DWORD Source)
{
	    volatile DWORD *pMem;

	    if (Source == INT_SOURCE_PERI)
	        pMem = (DWORD *)(BaseAddr + IC_SRCSET);
	    else
	        pMem = (DWORD *)(BaseAddr + IC_SRCCLR);
	    *pMem = 1 << IntNo;               // Interrupt Source
      __asm__ __volatile__("sync");
}
void  SetInterruptType(DWORD BaseAddr,DWORD IntNo,DWORD IntType)
{
	    volatile DWORD *pMem;

      if (IntType & 0x00000001)
	        pMem = (DWORD *)(BaseAddr + IC_CFG0SET);
	    else
	        pMem = (DWORD *)(BaseAddr + IC_CFG0CLR);
	    *pMem = 1 << IntNo;               // CFG0
      if (IntType & 0x00000002)
	        pMem = (DWORD *)(BaseAddr + IC_CFG1SET);
	    else
	        pMem = (DWORD *)(BaseAddr + IC_CFG1CLR);
	    *pMem = 1 << IntNo;               // CFG1
      if (IntType & 0x00000004)
	        pMem = (DWORD *)(BaseAddr + IC_CFG2SET);
	    else
	        pMem = (DWORD *)(BaseAddr + IC_CFG2CLR);
	    *pMem = 1 << IntNo;               // CFG2
      __asm__ __volatile__("sync");
}
void  SetInterruptRisingClear(DWORD BaseAddr,DWORD IntNo)
{
	    volatile DWORD *pMem;

      pMem = (DWORD *)(BaseAddr + IC_RISINGCLR);
	    *pMem= 1 << IntNo;               // Interrupt Source
      __asm__ __volatile__("sync");
}
void  SetInterruptFallingClear(DWORD BaseAddr,DWORD IntNo)
{
	    volatile DWORD *pMem;

      pMem = (DWORD *)(BaseAddr + IC_FALLINGCLR);
	    *pMem= 1 << IntNo;               // Interrupt Source
      __asm__ __volatile__("sync");
}
void  SetInterruptAssign(DWORD BaseAddr,DWORD IntNo,DWORD Assign)
{
	    volatile DWORD *pMem;

	    if (Assign == INT_ASSIGN_REQ0)
	        pMem = (DWORD *)(BaseAddr + IC_ASSIGNSET);
	    else
	        pMem = (DWORD *)(BaseAddr + IC_ASSIGNCLR);
	    *pMem = 1 << IntNo;               // Assign Request0 or Request1
      __asm__ __volatile__("sync");
}
void  SetInterruptWake(DWORD BaseAddr,DWORD IntNo,DWORD Wake)
{
	    volatile DWORD *pMem;

	    if (Wake == INT_WAKE_ENABLE)
	        pMem = (DWORD *)(BaseAddr + IC_WAKESET);
	    else
	        pMem = (DWORD *)(BaseAddr + IC_WAKECLR);
	    *pMem = 1 << IntNo;               // Interrupt Wake
      __asm__ __volatile__("sync");
}
void  SetInterruptMask(DWORD BaseAddr,DWORD IntNo,DWORD Mask)
{
	    volatile DWORD *pMem;

	    if (Mask == INT_MASK_ENABLE)
	        pMem = (DWORD *)(BaseAddr + IC_MASKSET);
	    else
	        pMem = (DWORD *)(BaseAddr + IC_MASKCLR);
	    *pMem = 1 << IntNo;               // Interrupt Mask
      __asm__ __volatile__("sync");
}
void  SetTimerInterrupt(void)
{
      Write32BitCP0Register(CP0_COUNT,0x00000000);
      Write32BitCP0Register(CP0_COMPARE,TIMER_INTERRUPT_TICK);
      __asm__ __volatile__("sync");
}
void  SetUartInterrupt(int UartNo)
{
	    DWORD IntNo;
      DWORD IntType = INT_TYPE_HIGH_LEVEL;

      IntNo = UART0_INT_NO + UartNo;
      SetInterruptSource(AU1100_INT_CONTROLLER0,IntNo,INT_SOURCE_PERI);
      SetInterruptType(AU1100_INT_CONTROLLER0,IntNo,IntType);
      SetInterruptAssign(AU1100_INT_CONTROLLER0,IntNo,INT_ASSIGN_REQ0);
      SetInterruptWake(AU1100_INT_CONTROLLER0,IntNo,INT_WAKE_ENABLE);
      if (IntType == INT_TYPE_RISING)  SetInterruptRisingClear(AU1100_INT_CONTROLLER0,IntNo);
      if (IntType == INT_TYPE_FALLING) SetInterruptFallingClear(AU1100_INT_CONTROLLER0,IntNo);
      SetInterruptMask(AU1100_INT_CONTROLLER0,IntNo,INT_MASK_ENABLE);
}
void  SetDmaInterrupt(int DmaNo)
{
	    DWORD IntNo;
      DWORD IntType = INT_TYPE_HIGH_LEVEL;

      IntNo = DMA0_INT_NO + DmaNo;
      SetInterruptSource(AU1100_INT_CONTROLLER0,IntNo,INT_SOURCE_PERI);
      SetInterruptType(AU1100_INT_CONTROLLER0,IntNo,IntType);
      SetInterruptAssign(AU1100_INT_CONTROLLER0,IntNo,INT_ASSIGN_REQ0);
      SetInterruptWake(AU1100_INT_CONTROLLER0,IntNo,INT_WAKE_ENABLE);
      if (IntType == INT_TYPE_RISING)  SetInterruptRisingClear(AU1100_INT_CONTROLLER0,IntNo);
      if (IntType == INT_TYPE_FALLING) SetInterruptFallingClear(AU1100_INT_CONTROLLER0,IntNo);
      SetInterruptMask(AU1100_INT_CONTROLLER0,IntNo,INT_MASK_ENABLE);
}
void  SetGpioInterrupt(int IntNo)
{
      DWORD IntType = INT_TYPE_FALLING;

      SetInterruptSource(AU1100_INT_CONTROLLER1,IntNo,INT_SOURCE_PERI);
      SetInterruptType(AU1100_INT_CONTROLLER1,IntNo,IntType);
      SetInterruptAssign(AU1100_INT_CONTROLLER1,IntNo,INT_ASSIGN_REQ0);
      SetInterruptWake(AU1100_INT_CONTROLLER1,IntNo,INT_WAKE_ENABLE);
      if (IntType == INT_TYPE_RISING)  SetInterruptRisingClear(AU1100_INT_CONTROLLER1,IntNo);
      if (IntType == INT_TYPE_FALLING) SetInterruptFallingClear(AU1100_INT_CONTROLLER1,IntNo);
      SetInterruptMask(AU1100_INT_CONTROLLER1,IntNo,INT_MASK_ENABLE);
}
void  SetSdCardInterrupt(void)
{
	DWORD dIntNo;
	DWORD dIntType = INT_TYPE_HIGH_LEVEL;

	dIntNo = SDCARD_INT_NO;
	SetInterruptSource(AU1100_INT_CONTROLLER0,dIntNo,INT_SOURCE_PERI);
	SetInterruptType(AU1100_INT_CONTROLLER0,dIntNo,dIntType);
	SetInterruptAssign(AU1100_INT_CONTROLLER0,dIntNo,INT_ASSIGN_REQ0);
	SetInterruptWake(AU1100_INT_CONTROLLER0,dIntNo,INT_WAKE_ENABLE);
	if (dIntType == INT_TYPE_RISING)  SetInterruptRisingClear(AU1100_INT_CONTROLLER0,dIntNo);
	if (dIntType == INT_TYPE_FALLING) SetInterruptFallingClear(AU1100_INT_CONTROLLER0,dIntNo);
	SetInterruptMask(AU1100_INT_CONTROLLER0,dIntNo,INT_MASK_ENABLE);
}
#ifdef  __cplusplus
extern "C" {
#endif
void  RunAllInterrupt(void)
{
	    DWORD Cause,Temp;

	    Cause = Read32BitCP0Register(CP0_CAUSE);
	    Temp = 1 << CP0_TIMER_INT_NO;
	    if (Cause & Temp)
	        RunTimerInterrupt();
	    Temp = 1 << CP0_ICR0_INT0_NO;
	    if (Cause & Temp)
	        RunIcr0Interrupt();
	    Temp = 1 << CP0_ICR1_INT0_NO;
	    if (Cause & Temp)
	        RunIcr1Interrupt();
}
#ifdef  __cplusplus
}
#endif

void  RunTimerInterrupt(void)
{
	    DWORD dTemp;

      CheckPowerStatus();
      cTIME::RunIntHandler();
	  SysIncSystemTimer();
      if (GetPowerStatus())
          cKEYBD::RunIntHandler();
      cBUZZER::RunIntHandler();
	    dTemp = Read32BitCP0Register(CP0_COMPARE) + TIMER_INTERRUPT_TICK;
      Write32BitCP0Register(CP0_COMPARE,dTemp);
}
void  RunIcr0Interrupt(void)
{
	    volatile DWORD *pMem;
	    DWORD Temp,Mask;
	    DWORD RiseRead;
	    DWORD FallRead;
	    int  i;
	    static void (*pHandler[])(void) = {(void (*)(void))RunUart0Handler,
	                                       (void (*)(void))RunUart1Handler,
	                                       (void (*)(void))RunSdCardHandler,
	                                       (void (*)(void))RunUart3Handler,
	                                       (void (*)(void))NULL,             // SSI0
	                                       (void (*)(void))NULL,             // SSI1
	                                       (void (*)(void))RunDma0Handler,   // DMA0
	                                       (void (*)(void))NULL,             // DMA1
	                                       (void (*)(void))NULL,             // DMA2
	                                       (void (*)(void))NULL,             // DMA3
	                                       (void (*)(void))NULL,             // DMA4
	                                       (void (*)(void))NULL,             // DMA5
	                                       (void (*)(void))NULL,             // DMA6
	                                       (void (*)(void))NULL,             // DMA7
	                                       (void (*)(void))NULL,             // TOY
	                                       (void (*)(void))NULL,             // TOY Match0
	                                       (void (*)(void))NULL,             // TOY Match1
	                                       (void (*)(void))NULL,             // TOY Match2
	                                       (void (*)(void))NULL,             // RTC
	                                       (void (*)(void))NULL,             // RTC Match0
	                                       (void (*)(void))NULL,             // RTC Match1
	                                       (void (*)(void))NULL,             // RTC Match2
	                                       (void (*)(void))NULL,             // IRDA Tx
	                                       (void (*)(void))NULL,             // IRDA Rx
	                                       (void (*)(void))NULL,             // USB DIRQ
	                                       (void (*)(void))NULL,             // USB SIRQ
	                                       (void (*)(void))NULL,             // USB Host
	                                       (void (*)(void))NULL,             // AC97 AcSync
	                                       (void (*)(void))NULL,             // MAC0 DMA Done
	                                       (void (*)(void))NULL,             // MAC1 DMA Done
	                                       (void (*)(void))NULL,             // Reserved
	                                       (void (*)(void))NULL};            // AC97 Cmd Done

      pMem = (volatile DWORD *)(AU1100_INT_CONTROLLER0 + IC_REQ0INT);
      Temp = *pMem;
      RiseRead = *(volatile DWORD *)(AU1100_INT_CONTROLLER0 + IC_RISINGRD);
      FallRead = *(volatile DWORD *)(AU1100_INT_CONTROLLER0 + IC_FALLINGRD);
      Mask = 0x00000001;
      for (i = 0;i < 32;i++)
          {
           if (pHandler[i] != NULL && (Temp & Mask))
              {
               if (RiseRead & Mask) *(volatile DWORD *)(AU1100_INT_CONTROLLER0 + IC_RISINGCLR) = Mask;
               if (FallRead & Mask) *(volatile DWORD *)(AU1100_INT_CONTROLLER0 + IC_FALLINGCLR) = Mask;
               pHandler[i]();
              }
           Mask <<= 1;
          }
}
void  RunUart0Handler(void)
{
	    DWORD dTemp;

      dTemp = *(volatile DWORD *)(AU1100_UART_CONTROLLER0 + UART_LINESTAT);
	    if (dTemp & 0x20) G_pUart0->RunTxIntHandler();
	    if (dTemp & 0x01) G_pUart0->RunRxIntHandler();
}
void  RunUart1Handler(void)
{
	    DWORD dTemp;

      if (G_pUart1 == NULL)
          return;
      dTemp = *(volatile DWORD *)(AU1100_UART_CONTROLLER1 + UART_LINESTAT);
	    if (dTemp & 0x20) G_pUart1->RunTxIntHandler();
	    if (dTemp & 0x01) G_pUart1->RunRxIntHandler();
}
void  RunSdCardHandler(void)
{
	SdCardRunIntHandler();
}
void  RunUart3Handler(void)
{
	    DWORD dTemp;

      if (G_pUart3 == NULL)
          return;
      dTemp = *(volatile DWORD *)(AU1100_UART_CONTROLLER3 + UART_LINESTAT);
	    if (dTemp & 0x20) G_pUart3->RunTxIntHandler();
	    if (dTemp & 0x01) G_pUart3->RunRxIntHandler();
}
void  RunDma0Handler(void)
{
	    volatile DWORD *pMemX;
	    volatile DWORD *pMemY;

      pMemX = (volatile DWORD *)(AU1100_DMA_CONTROLLER0 + DMA_MODEREAD);
}
void  RunIcr1Interrupt(void)
{
	    volatile DWORD *pMem;
	    DWORD Temp,Mask;
	    DWORD RiseRead;
	    DWORD FallRead;
	    int  i;
	    static void (*pHandler[])(void) = {(void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))RunGpio02Interrupt,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL,
	                                       (void (*)(void))NULL};

      pMem = (volatile DWORD *)(AU1100_INT_CONTROLLER1 + IC_REQ0INT);
      Temp = *pMem;
      RiseRead = *(volatile DWORD *)(AU1100_INT_CONTROLLER1 + IC_RISINGRD);
      FallRead = *(volatile DWORD *)(AU1100_INT_CONTROLLER1 + IC_FALLINGRD);
      Mask = 0x00000001;
      for (i = 0;i < 32;i++)
          {
           if (pHandler[i] != NULL && (Temp & Mask))
              {
               if (RiseRead & Mask) *(volatile DWORD *)(AU1100_INT_CONTROLLER1 + IC_RISINGCLR) = Mask;
               if (FallRead & Mask) *(volatile DWORD *)(AU1100_INT_CONTROLLER1 + IC_FALLINGCLR) = Mask;
               pHandler[i]();
              }
           Mask <<= 1;
          }
}
void  RunGpio02Interrupt(void)
{
/*
      static DWORD OldTick = 0;
      static DWORD NewTick = 0;
      DWORD  dTemp;
      volatile BYTE *pMemX;
      volatile BYTE *pMemY;
      int  i,nPos,nIndex,nReadCnt;
      WORD wTemp;
      BYTE bTemp;

      NewTick = Read32BitCP0Register(CP0_COUNT);
      if (NewTick < OldTick)
          dTemp = (0xffffffff - OldTick) + NewTick;
      else
          dTemp = NewTick - OldTick;
      if (dTemp < (SYS_CLK_FREQ / 10))          // 100 ms
          return;
      OldTick = NewTick;
      pMemX = (volatile BYTE *)KBD_L_TYPE_STATUS;
      if (*pMemX & 0x01)
         {
          pMemX = (volatile BYTE *)(KBD_W_LESS_DATA);
          bTemp = *pMemX;
          pMemY = (volatile BYTE *)(KBD_W_LESS_CSUM);
          if ((bTemp ^ 0x35) == *pMemY)
             {
              wTemp = KBD_PRSS_MASK | (WORD)bTemp;
              cKEYBD::RunIntHandler();
             }
         }
      else
         {
 	        pMemX = (volatile BYTE *)(KBD_W_USED_DATA);
 	        bTemp = *pMemX;
 	        wTemp = KBD_PRSS_MASK | (WORD)bTemp;
          cKEYBD::RunIntHandler();
         }
*/
}
#endif  // __CPU_AU1100__

