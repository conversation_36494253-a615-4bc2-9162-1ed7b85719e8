{"Aden, Yemen", "03:00", ":Asia/Aden", 1.300, "meters",
	{0.016, 0.397, 0.056, 0.019, 0.014, 0.475, 0.000, 0.002, 0.000, 0.000, 0.132, 0.018, 0.201, 0.009, 0.131, 0.046, 0.005, 0.002, 0.000, 0.206, 0.000, 0.000, 0.012, 0.003, 0.011, 0.026, 0.008, 0.000, 0.000, 0.009, 0.004, 0.000, 0.000, 0.000, 0.000, 0.112, 0.000, },
	{35.500, 35.000, 246.700, 231.000, 34.500, 224.000, 0.000, 300.000, 0.000, 0.000, 217.000, 210.000, 34.000, 36.000, 34.900, 32.000, 33.000, 245.800, 0.000, 245.000, 0.000, 0.000, 244.200, 233.700, 203.000, 217.900, 33.600, 0.000, 0.000, 105.000, 159.000, 0.000, 0.000, 0.000, 0.000, 346.000, 0.000, },
},
{"Air Musi, Sumatra, Indonesia", "07:00", ":Asia/Jakarta", 1.900, "meters",
	{0.047, 0.880, 0.035, 0.007, 0.043, 0.260, 0.000, 0.000, 0.000, 0.000, 0.050, 0.007, 0.600, 0.026, 0.291, 0.000, 0.016, 0.001, 0.000, 0.130, 0.000, 0.000, 0.008, 0.002, 0.006, 0.010, 0.023, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{190.700, 156.000, 236.300, 227.000, 121.000, 192.000, 0.000, 0.000, 0.000, 0.000, 157.000, 122.000, 86.000, 226.000, 150.800, 0.000, 16.600, 234.600, 0.000, 233.000, 0.000, 0.000, 231.400, 211.000, 151.000, 161.700, 56.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Amuay, Venezuela", "-04:00", ":America/Caracas", 0.650, "feet",
	{0.020, 0.408, 0.016, 0.000, 0.019, 0.338, 0.000, 0.038, 0.000, 0.000, 0.124, 0.017, 0.261, 0.011, 0.127, 0.044, 0.000, 0.000, 0.043, 0.058, 0.000, 0.000, 0.000, 0.000, 0.000, 0.025, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.272, 0.126, },
	{182.900, 183.100, 65.200, 0.000, 183.200, 136.500, 0.000, 35.200, 0.000, 0.000, 107.200, 77.900, 183.300, 182.800, 186.200, 174.900, 0.000, 0.000, 107.900, 65.500, 0.000, 0.000, 0.000, 0.000, 0.000, 111.100, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 169.600, 76.700, },
},
{"Antofagasta, Chile", "-04:00", ":America/Santiago", 0.800, "meters",
	{0.001, 0.150, 0.030, 0.012, 0.001, 0.376, 0.000, 0.000, 0.000, 0.000, 0.083, 0.011, 0.009, 0.000, 0.050, 0.017, 0.000, 0.001, 0.000, 0.109, 0.000, 0.000, 0.006, 0.003, 0.009, 0.016, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.031, 0.000, },
	{352.300, 333.000, 300.400, 316.000, 313.500, 282.000, 0.000, 0.000, 0.000, 0.000, 248.000, 214.000, 294.000, 372.000, 330.100, 273.000, 255.300, 299.700, 0.000, 299.000, 0.000, 0.000, 298.300, 289.900, 265.000, 252.600, 277.300, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 338.000, 0.000, },
},
{"Antwerpen, Belgium", "01:00", ":Europe/Brussels", 2.640, "meters",
	{0.007, 0.061, 0.130, 0.043, 0.006, 1.963, 0.000, 0.131, 0.000, 0.000, 0.300, 0.040, 0.090, 0.004, 0.020, 0.036, 0.002, 0.004, 0.000, 0.477, 0.000, 0.000, 0.028, 0.014, 0.047, 0.058, 0.003, 0.000, 0.000, 0.048, 0.086, 0.000, 0.000, 0.000, 0.000, 0.060, 0.000, },
	{126.800, 44.000, 185.200, 143.000, -39.500, 116.000, 0.000, 179.000, 0.000, 0.000, 89.000, 62.000, 237.000, 211.000, 31.500, 172.000, -288.700, 182.600, 0.000, 180.000, 0.000, 0.000, 177.400, 145.700, 52.000, 92.600, -194.600, 0.000, 0.000, 139.000, 242.000, 0.000, 0.000, 0.000, 0.000, 163.000, 0.000, },
},
{"Bahia Magdalena, Baja California Sur, Mexico", "-07:00", ":America/Mazatlan", 0.820, "meters",
	{0.014, 0.241, 0.082, 0.058, 0.012, 0.485, 0.000, 0.000, 0.000, 0.000, 0.119, 0.016, 0.171, 0.007, 0.079, 0.033, 0.004, 0.002, 0.000, 0.308, 0.000, 0.000, 0.018, 0.003, 0.012, 0.023, 0.006, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{71.200, 77.900, 266.700, 258.600, 84.700, 265.400, 0.000, 0.000, 0.000, 0.000, 271.200, 277.000, 91.500, 64.300, 78.400, 98.200, 105.000, 267.400, 0.000, 267.300, 0.000, 0.000, 267.200, 266.300, 263.500, 270.400, 97.300, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Bahia San Juanico, Baja California Sur, Mexico", "-07:00", ":America/Mazatlan", 0.850, "meters",
	{0.007, 0.125, 0.085, 0.024, 0.006, 0.524, 0.000, 0.000, 0.000, 0.000, 0.104, 0.014, 0.088, 0.004, 0.040, 0.017, 0.002, 0.002, 0.000, 0.311, 0.000, 0.000, 0.018, 0.004, 0.013, 0.020, 0.003, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{115.200, 100.200, 266.400, 336.200, 85.100, 268.000, 0.000, 0.000, 0.000, 0.000, 272.900, 277.800, 69.900, 130.500, 100.800, 54.900, 39.800, 266.900, 0.000, 266.900, 0.000, 0.000, 266.900, 267.500, 269.100, 272.200, 56.900, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Bahia de Ballenas, Baja California Sur, Mexico", "-07:00", ":America/Mazatlan", 1.010, "meters",
	{0.004, 0.131, 0.088, 0.110, 0.004, 0.652, 0.000, 0.000, 0.000, 0.000, 0.213, 0.028, 0.052, 0.002, 0.043, 0.010, 0.001, 0.003, 0.000, 0.326, 0.000, 0.000, 0.019, 0.005, 0.016, 0.041, 0.002, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{96.500, 98.300, 291.600, 355.400, 100.200, 285.200, 0.000, 0.000, 0.000, 0.000, 230.100, 175.000, 102.000, 94.600, 98.900, 103.800, 105.700, 292.400, 0.000, 292.100, 0.000, 0.000, 291.800, 288.400, 278.300, 237.500, 103.600, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Bahia de los Angeles, Baja California Norte, Mexico", "-08:00", ":America/Tijuana", 1.010, "meters",
	{0.018, 0.461, 0.092, 0.018, 0.016, 0.658, 0.000, 0.000, 0.000, 0.000, 0.128, 0.017, 0.228, 0.010, 0.153, 0.044, 0.006, 0.003, 0.000, 0.339, 0.000, 0.000, 0.020, 0.005, 0.016, 0.025, 0.009, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{111.600, 99.800, 71.700, 67.200, 87.900, 62.800, 0.000, 0.000, 0.000, 0.000, 58.400, 54.000, 76.000, 123.600, 98.000, 64.200, 52.400, 71.300, 0.000, 71.000, 0.000, 0.000, 70.700, 66.600, 54.600, 59.000, 65.800, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Bangkok, Thailand", "07:00", ":Asia/Bangkok", 2.470, "meters",
	{0.035, 0.671, 0.076, 0.013, 0.032, 0.550, 0.000, 0.006, 0.000, 0.000, 0.089, 0.012, 0.449, 0.019, 0.222, 0.087, 0.012, 0.002, 0.000, 0.280, 0.000, 0.000, 0.017, 0.004, 0.013, 0.017, 0.017, 0.000, 0.000, 0.000, 0.008, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{182.300, 161.000, 214.800, 170.000, 139.500, 138.000, 0.000, 164.000, 0.000, 0.000, 106.000, 74.000, 118.000, 204.000, 157.800, 95.000, 75.300, 211.800, 0.000, 209.000, 0.000, 0.000, 206.200, 170.900, 67.000, 110.300, 99.600, 0.000, 0.000, 0.000, 233.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Beira, Mozambique", "02:00", ":Africa/Maputo", 11.450, "feet",
	{0.016, 0.039, 0.935, 0.276, 0.020, 5.804, 0.246, 0.341, 0.062, 0.000, 0.912, 0.223, 0.171, 0.000, 0.000, 0.049, 0.016, 0.102, 0.098, 3.412, 0.151, 0.000, 0.210, 0.167, 0.440, 0.157, 0.000, 0.030, 0.000, 0.184, 0.259, 0.246, 0.062, 0.135, 0.072, 0.489, 0.220, },
	{12.400, 40.200, 168.600, 139.500, 308.000, 124.600, 299.100, 215.800, 66.600, 0.000, 111.700, 160.400, 19.600, 0.000, 0.000, 0.400, 313.000, 14.200, 98.500, 171.000, 272.400, 0.000, 151.200, 109.800, 182.100, 90.500, 0.000, 267.800, 0.000, 200.600, 215.600, 342.300, 40.600, 70.300, 53.800, 326.700, 12.100, },
},
{"Belawan, Sumatra, Indonesia", "07:00", ":Asia/Jakarta", 1.500, "meters",
	{0.003, 0.210, 0.087, 0.017, 0.003, 0.610, 0.000, 0.000, 0.000, 0.000, 0.120, 0.016, 0.040, 0.002, 0.070, 0.000, 0.001, 0.003, 0.000, 0.320, 0.000, 0.000, 0.019, 0.004, 0.015, 0.023, 0.002, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{380.700, 347.000, 73.200, 37.000, 313.000, 30.000, 0.000, 0.000, 0.000, 0.000, 23.000, 16.000, 279.000, 415.000, 341.900, 0.000, 211.500, 71.600, 0.000, 70.000, 0.000, 0.000, 68.400, 48.600, -10.000, 23.900, 249.800, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Bergen, Norway", "01:00", ":Europe/Oslo", 2.618, "feet",
	{0.000, 0.105, 0.151, 0.039, 0.000, 1.440, 0.000, 0.000, 0.000, 0.000, 0.276, 0.000, 0.098, 0.000, 0.036, 0.023, 0.000, 0.020, 0.020, 0.518, 0.000, 0.000, 0.026, 0.000, 0.052, 0.052, 0.000, 0.000, 0.000, 0.000, 0.039, 0.000, 0.052, 0.069, 0.033, 0.253, 0.075, },
	{0.000, 179.700, 356.500, 350.900, 0.000, 316.400, 0.000, 0.000, 0.000, 0.000, 287.800, 0.000, 25.600, 0.000, 161.700, 325.100, 0.000, 162.400, 97.700, 354.400, 0.000, 0.000, 304.400, 0.000, 309.400, 310.900, 0.000, 0.000, 0.000, 0.000, 356.800, 0.000, 6.100, 164.000, 162.500, 234.000, 186.100, },
},
{"Bombay, India", "05:30", ":Asia/Calcutta", 2.520, "meters",
	{0.016, 0.425, 0.130, 0.041, 0.014, 1.227, 0.000, 0.035, 0.000, 0.000, 0.287, 0.038, 0.201, 0.009, 0.141, 0.041, 0.005, 0.004, 0.000, 0.479, 0.000, 0.000, 0.028, 0.009, 0.029, 0.056, 0.008, 0.000, 0.000, 0.007, 0.032, 0.000, 0.000, 0.000, 0.000, 0.043, 0.000, },
	{58.000, 56.000, 28.200, 363.000, 54.000, 345.000, 0.000, 332.000, 0.000, 0.000, 327.000, 309.000, 52.000, 60.000, 55.700, 58.000, 48.000, 26.600, 0.000, 25.000, 0.000, 0.000, 23.400, 3.600, -55.000, 329.400, 50.300, 0.000, 0.000, 296.000, 43.000, 0.000, 0.000, 0.000, 0.000, 33.000, 0.000, },
},
{"Bremerhaven, Germany", "01:00", ":Europe/Berlin", 2.040, "meters",
	{0.007, 0.076, 0.101, 0.034, 0.007, 1.519, 0.000, 0.138, 0.000, 0.000, 0.236, 0.031, 0.094, 0.004, 0.025, 0.028, 0.002, 0.003, 0.000, 0.371, 0.000, 0.000, 0.022, 0.011, 0.036, 0.046, 0.004, 0.000, 0.000, 0.045, 0.088, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{138.400, 63.000, 108.200, 51.000, -13.000, 26.000, 0.000, 247.000, 0.000, 0.000, 1.000, -24.000, 271.000, 215.000, 51.600, 199.000, -239.800, 105.000, 0.000, 102.000, 0.000, 0.000, 99.000, 61.300, -50.000, 4.400, -154.200, 0.000, 0.000, 227.000, 331.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Brest, France", "01:00", ":Europe/Paris", 13.193, "feet",
	{0.009, 0.206, 0.692, 0.219, 0.000, 6.682, 0.065, 0.190, 0.095, 0.006, 1.364, 0.183, 0.213, 0.000, 0.075, 0.065, 0.000, 0.000, 0.059, 2.457, 0.000, 0.000, 0.134, 0.091, 0.285, 0.246, 0.000, 0.000, 0.000, 0.068, 0.108, 0.062, 0.036, 0.006, 0.009, 0.193, 0.036, },
	{123.500, 90.800, 175.200, 133.300, 0.000, 137.300, 58.900, 161.000, 81.300, 0.400, 118.100, 101.800, 343.200, 0.000, 83.200, 295.900, 0.000, 0.000, 11.800, 177.500, 0.000, 0.000, 166.000, 99.800, 130.400, 114.000, 0.000, 0.000, 0.000, 115.700, 246.800, 335.000, 168.400, 306.500, 200.700, 251.500, 78.200, },
},
{"Buenaventura, Colombia", "-05:00", ":America/Bogota", 6.480, "feet",
	{0.000, 0.390, 0.296, 0.158, 0.000, 5.037, 0.000, 0.224, 0.046, 0.032, 1.056, 0.140, 0.087, 0.000, 0.123, 0.016, 0.000, 0.010, 0.047, 1.329, 0.000, 0.000, 0.078, 0.035, 0.127, 0.205, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.253, 0.182, },
	{0.000, 347.800, 161.000, 107.900, 0.000, 110.200, 0.000, 298.500, 336.100, 239.000, 84.200, 58.200, 6.300, 0.000, 347.400, 15.500, 0.000, 166.100, 81.300, 166.300, 0.000, 0.000, 166.500, 136.200, 99.900, 87.600, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 159.200, 149.600, },
},
{"Buenos Aires, Argentina", "-03:00", ":America/Buenos_Aires", 2.591, "feet",
	{0.049, 0.269, 0.072, 0.068, 0.042, 0.961, 0.016, 0.108, 0.029, 0.000, 0.347, 0.039, 0.531, 0.019, 0.118, 0.108, 0.026, 0.029, 0.236, 0.183, 0.000, 0.000, 0.026, 0.062, 0.026, 0.072, 0.111, 0.032, 0.000, 0.062, 0.039, 0.026, 0.127, 0.164, 0.183, 0.429, 0.226, },
	{251.500, 39.800, 282.000, 298.300, 179.300, 204.100, 99.100, 292.400, 5.500, 0.000, 161.400, 148.300, 216.000, 193.200, 28.700, 168.400, 193.300, 18.500, 234.400, 283.300, 0.000, 0.000, 252.300, 270.100, 80.700, 187.500, 190.300, 69.400, 0.000, 255.800, 21.300, 225.900, 289.800, 114.900, 70.700, 314.600, 21.000, },
},
{"Cabo San Lucas, Baja California Sur, Mexico", "-07:00", ":America/Mazatlan", 0.602, "meters",
	{0.012, 0.225, 0.065, 0.013, 0.011, 0.364, 0.000, 0.000, 0.000, 0.000, 0.093, 0.012, 0.148, 0.006, 0.074, 0.029, 0.004, 0.002, 0.000, 0.237, 0.000, 0.000, 0.014, 0.003, 0.009, 0.018, 0.006, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{78.900, 77.600, 250.500, 252.600, 76.300, 251.400, 0.000, 0.000, 0.000, 0.000, 250.200, 249.000, 74.900, 80.300, 77.400, 73.600, 72.200, 250.600, 0.000, 250.600, 0.000, 0.000, 250.600, 251.000, 252.200, 250.400, 73.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Callao, Peru", "-05:00", ":America/Lima", 1.690, "feet",
	{0.019, 0.495, 0.089, 0.022, 0.018, 0.784, 0.000, 0.000, 0.000, 0.000, 0.230, 0.031, 0.241, 0.010, 0.160, 0.035, 0.000, 0.000, 0.000, 0.263, 0.000, 0.000, 0.015, 0.000, 0.047, 0.044, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.185, 0.000, },
	{330.600, 313.100, 180.300, 203.100, 295.700, 171.300, 0.000, 0.000, 0.000, 0.000, 139.500, 107.700, 278.200, 348.000, 310.900, 257.300, 0.000, 0.000, 0.000, 178.300, 0.000, 0.000, 178.500, 0.000, 146.400, 143.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 21.900, 0.000, },
},
{"Campeche City, Campeche, Mexico", "-6:00", ":America/Mexico_City", 0.240, "meters",
	{0.000, 0.266, 0.030, 0.019, 0.015, 0.220, 0.000, 0.011, 0.002, 0.000, 0.042, 0.000, 0.183, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.067, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{0.000, 314.300, 208.600, 107.900, 248.600, 97.200, 0.000, 54.300, 93.500, 0.000, 88.400, 0.000, 306.900, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 118.100, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Cape Town, South Africa", "02:00", ":Africa/Johannesburg", 1.050, "meters",
	{0.001, 0.058, 0.059, 0.016, 0.001, 0.493, 0.000, 0.006, 0.000, 0.000, 0.111, 0.015, 0.015, 0.001, 0.019, 0.009, 0.000, 0.002, 0.000, 0.216, 0.000, 0.000, 0.013, 0.003, 0.012, 0.022, 0.001, 0.000, 0.000, 0.003, 0.004, 0.000, 0.000, 0.000, 0.000, 0.023, 0.000, },
	{77.500, 140.000, 116.800, 101.000, 203.000, 93.000, 0.000, 177.000, 0.000, 0.000, 85.000, 77.000, 266.000, 14.000, 149.500, 254.000, 391.000, 115.900, 0.000, 115.000, 0.000, 0.000, 114.100, 103.200, 71.000, 86.100, 320.100, 0.000, 0.000, 122.000, 252.000, 0.000, 0.000, 0.000, 0.000, 316.000, 0.000, },
},
{"Casablanca, Morocco", "00:00", ":Africa/Casablanca", 2.140, "meters",
	{0.005, 0.070, 0.101, 0.028, 0.004, 1.010, 0.000, 0.000, 0.000, 0.000, 0.196, 0.026, 0.060, 0.003, 0.023, 0.012, 0.002, 0.003, 0.000, 0.370, 0.000, 0.000, 0.022, 0.007, 0.024, 0.038, 0.002, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.090, 0.000, },
	{98.100, 48.000, 84.000, 70.400, 357.500, 57.000, 0.000, 0.000, 0.000, 0.000, 43.600, 30.200, 307.000, 149.000, 40.400, 256.900, 206.800, 83.000, 0.000, 82.000, 0.000, 0.000, 81.000, 68.600, 32.000, 45.400, 263.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 221.000, 0.000, },
},
{"Cebu, Philippines", "08:00", ":Asia/Manila", 0.720, "meters",
	{0.022, 0.308, 0.063, 0.009, 0.020, 0.417, 0.000, 0.000, 0.000, 0.000, 0.062, 0.008, 0.284, 0.012, 0.102, 0.060, 0.007, 0.002, 0.000, 0.230, 0.000, 0.000, 0.014, 0.003, 0.010, 0.012, 0.011, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.084, 0.000, },
	{348.300, 326.000, 14.400, 338.000, 303.500, 316.000, 0.000, 0.000, 0.000, 0.000, 294.000, 272.000, 281.000, 371.000, 322.600, 261.000, 236.400, 12.200, 0.000, 10.000, 0.000, 0.000, 7.800, -18.900, -98.000, 296.900, 261.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 140.000, 0.000, },
},
{"Cherbourg, France", "01:00", ":Europe/Paris", 3.810, "meters",
	{0.005, 0.079, 0.191, 0.051, 0.004, 1.864, 0.000, 0.137, 0.000, 0.000, 0.359, 0.048, 0.060, 0.003, 0.026, 0.017, 0.002, 0.006, 0.000, 0.704, 0.000, 0.000, 0.042, 0.013, 0.045, 0.070, 0.002, 0.000, 0.000, 0.047, 0.085, 0.000, 0.000, 0.000, 0.000, 0.046, 0.000, },
	{195.000, 132.000, 304.600, 276.000, 68.500, 257.000, 0.000, 50.000, 0.000, 0.000, 238.000, 219.000, 5.000, 259.000, 122.500, 321.000, -121.000, 302.800, 0.000, 301.000, 0.000, 0.000, 299.200, 277.400, 213.000, 240.500, -49.500, 0.000, 0.000, 23.000, 99.000, 0.000, 0.000, 0.000, 0.000, 205.000, 0.000, },
},
{"Chi-Lung, Taiwan", "09:00", ":Asia/Taipei", 0.580, "meters",
	{0.012, 0.185, 0.014, 0.007, 0.011, 0.191, 0.000, 0.009, 0.000, 0.000, 0.051, 0.007, 0.153, 0.007, 0.061, 0.031, 0.004, 0.000, 0.000, 0.051, 0.000, 0.000, 0.003, 0.001, 0.005, 0.010, 0.006, 0.000, 0.000, 0.000, 0.014, 0.000, 0.000, 0.000, 0.000, 0.126, 0.000, },
	{261.900, 245.000, 310.900, 342.000, 228.000, 312.000, 0.000, 344.000, 0.000, 0.000, 282.000, 252.000, 211.000, 279.000, 242.500, 193.000, 177.300, 311.000, 0.000, 311.000, 0.000, 0.000, 311.000, 311.500, 313.000, 286.000, 196.400, 0.000, 0.000, 0.000, 350.000, 0.000, 0.000, 0.000, 0.000, 135.000, 0.000, },
},
{"Chinnampo, North Korea", "09:00", ":Asia/Pyongyang", 3.040, "meters",
	{0.020, 0.369, 0.163, 0.045, 0.018, 1.815, 0.000, 0.051, 0.000, 0.000, 0.318, 0.042, 0.250, 0.011, 0.122, 0.041, 0.007, 0.005, 0.000, 0.601, 0.000, 0.000, 0.035, 0.013, 0.044, 0.062, 0.010, 0.000, 0.000, 0.000, 0.047, 0.000, 0.000, 0.000, 0.000, 0.240, 0.000, },
	{366.300, 344.000, 277.200, 287.000, 321.500, 261.000, 0.000, 325.000, 0.000, 0.000, 235.000, 209.000, 299.000, 389.000, 340.600, 270.000, 254.400, 276.600, 0.000, 276.000, 0.000, 0.000, 275.400, 268.000, 246.000, 238.500, 279.700, 0.000, 0.000, 0.000, 286.000, 0.000, 0.000, 0.000, 0.000, 130.000, 0.000, },
},
{"Colombo, Sri Lanka", "05:30", ":Asia/Colombo", 0.380, "meters",
	{0.002, 0.073, 0.032, 0.003, 0.002, 0.176, 0.000, 0.005, 0.000, 0.000, 0.022, 0.003, 0.029, 0.001, 0.024, 0.010, 0.001, 0.001, 0.000, 0.119, 0.000, 0.000, 0.007, 0.001, 0.004, 0.004, 0.001, 0.000, 0.000, 0.007, 0.003, 0.000, 0.000, 0.000, 0.000, 0.095, 0.000, },
	{24.600, 36.000, 104.100, 69.000, 47.500, 50.000, 0.000, 169.000, 0.000, 0.000, 31.000, 12.000, 59.000, 13.000, 37.700, 82.000, 81.800, 102.000, 0.000, 100.000, 0.000, 0.000, 98.000, 73.200, 0.000, 33.500, 68.900, 0.000, 0.000, 269.000, 258.000, 0.000, 0.000, 0.000, 0.000, 308.000, 0.000, },
},
{"Comodoro Rivadavia, Argentina", "-03:00", ":America/Buenos_Aires", 3.140, "meters",
	{0.010, 0.198, 0.110, 0.056, 0.009, 2.062, 0.000, 0.104, 0.000, 0.000, 0.394, 0.052, 0.125, 0.005, 0.066, 0.031, 0.003, 0.003, 0.000, 0.405, 0.000, 0.000, 0.024, 0.014, 0.049, 0.076, 0.005, 0.000, 0.000, 0.056, 0.051, 0.000, 0.000, 0.000, 0.000, 0.098, 0.000, },
	{249.600, 203.000, 226.000, 183.000, 156.000, 133.000, 0.000, 255.000, 0.000, 0.000, 83.000, 33.000, 109.000, 297.000, 196.000, 68.000, 15.800, 222.400, 0.000, 219.000, 0.000, 0.000, 215.600, 172.900, 47.000, 89.700, 68.700, 0.000, 0.000, 229.000, 319.000, 0.000, 0.000, 0.000, 0.000, 7.000, 0.000, },
},
{"Dakar, Senegal", "00:00", ":Africa/Dakar", 1.010, "meters",
	{0.003, 0.063, 0.045, 0.013, 0.003, 0.461, 0.000, 0.010, 0.000, 0.000, 0.091, 0.012, 0.038, 0.002, 0.021, 0.012, 0.001, 0.001, 0.000, 0.166, 0.000, 0.000, 0.010, 0.003, 0.011, 0.018, 0.001, 0.000, 0.000, 0.000, 0.006, 0.000, 0.000, 0.000, 0.000, 0.087, 0.000, },
	{399.100, 350.000, 295.900, 279.000, 300.500, 257.000, 0.000, 282.000, 0.000, 0.000, 235.000, 213.000, 251.000, 449.000, 342.600, 194.000, 152.800, 294.400, 0.000, 293.000, 0.000, 0.000, 291.600, 273.700, 221.000, 237.900, 208.500, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 153.000, 0.000, },
},
{"Daliang, China", "08:00", ":Asia/Shanghai", 1.750, "meters",
	{0.016, 0.270, 0.079, 0.027, 0.015, 0.988, 0.000, 0.009, 0.000, 0.000, 0.190, 0.025, 0.205, 0.009, 0.089, 0.040, 0.005, 0.002, 0.000, 0.291, 0.000, 0.000, 0.017, 0.007, 0.024, 0.037, 0.008, 0.000, 0.000, 0.000, 0.041, 0.000, 0.000, 0.000, 0.000, 0.232, 0.000, },
	{20.300, 0.000, 348.500, 326.000, -20.500, 288.000, 0.000, 256.000, 0.000, 0.000, 250.000, 212.000, 319.000, 41.000, -3.100, 302.000, -81.700, 346.200, 0.000, 344.000, 0.000, 0.000, 341.800, 314.000, 232.000, 255.100, -58.600, 0.000, 0.000, 0.000, 285.000, 0.000, 0.000, 0.000, 0.000, 118.000, 0.000, },
},
{"Dar Es Salaam, Tanzania", "03:00", ":Africa/Dar_es_Salaam", 1.530, "meters",
	{0.008, 0.171, 0.146, 0.027, 0.008, 1.074, 0.000, 0.011, 0.000, 0.000, 0.191, 0.025, 0.106, 0.005, 0.057, 0.022, 0.003, 0.004, 0.000, 0.536, 0.000, 0.000, 0.032, 0.008, 0.026, 0.037, 0.004, 0.000, 0.000, 0.004, 0.007, 0.000, 0.000, 0.000, 0.000, 0.035, 0.000, },
	{41.500, 42.000, 159.600, 136.000, 42.500, 111.000, 0.000, 316.000, 0.000, 0.000, 86.000, 61.000, 43.000, 41.000, 42.100, 30.000, 44.000, 157.800, 0.000, 156.000, 0.000, 0.000, 154.200, 131.900, 66.000, 89.400, 43.400, 0.000, 0.000, 250.000, 31.000, 0.000, 0.000, 0.000, 0.000, 311.000, 0.000, },
},
{"Davao, Philippines", "08:00", ":Asia/Manila", 0.750, "meters",
	{0.008, 0.153, 0.080, 0.015, 0.007, 0.586, 0.000, 0.000, 0.000, 0.000, 0.102, 0.014, 0.105, 0.005, 0.051, 0.022, 0.003, 0.002, 0.000, 0.295, 0.000, 0.000, 0.017, 0.004, 0.014, 0.020, 0.004, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.081, 0.000, },
	{251.400, 238.000, 200.400, 169.000, 224.500, 155.000, 0.000, 0.000, 0.000, 0.000, 141.000, 127.000, 211.000, 265.000, 236.000, 208.000, 184.200, 198.700, 0.000, 197.000, 0.000, 0.000, 195.300, 174.500, 113.000, 142.900, 199.400, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 145.000, 0.000, },
},
{"Do Son, Vietnam", "07:00", ":Asia/Saigon", 1.860, "meters",
	{0.059, 0.725, 0.012, 0.002, 0.053, 0.054, 0.000, 0.008, 0.000, 0.000, 0.012, 0.002, 0.741, 0.032, 0.240, 0.140, 0.019, 0.000, 0.000, 0.043, 0.000, 0.000, 0.003, 0.000, 0.001, 0.002, 0.028, 0.000, 0.000, 0.000, 0.005, 0.000, 0.000, 0.000, 0.000, 0.102, 0.000, },
	{122.200, 91.000, 110.800, 15.000, 59.500, 47.000, 0.000, 262.000, 0.000, 0.000, 79.000, 111.000, 28.000, 154.000, 86.300, 358.000, -34.500, 108.400, 0.000, 106.000, 0.000, 0.000, 103.600, 74.400, -12.000, 74.700, 1.000, 0.000, 0.000, 0.000, 323.000, 0.000, 0.000, 0.000, 0.000, 204.000, 0.000, },
},
{"Esbjerg, Denmark", "01:00", ":Europe/Copenhagen", 0.120, "meters",
	{0.000, 0.051, 0.044, 0.051, 0.011, 0.656, 0.002, 0.061, 0.019, 0.005, 0.106, 0.020, 0.080, 0.003, 0.022, 0.021, 0.006, 0.000, 0.021, 0.162, 0.002, 0.000, 0.011, 0.027, 0.067, 0.043, 0.003, 0.005, 0.000, 0.022, 0.029, 0.013, 0.032, 0.031, 0.032, 0.110, 0.024, },
	{0.000, 88.800, 130.000, 81.700, 243.300, 64.500, 69.600, 285.200, 180.300, 77.300, 35.200, 335.800, 290.300, 314.300, 96.800, 241.400, 165.400, 0.000, 234.200, 127.600, 325.200, 0.000, 65.300, 73.000, 157.700, 38.000, 269.900, 49.100, 0.000, 246.700, 351.600, 1.100, 79.100, 212.400, 279.700, 296.900, 229.500, },
},
{"Gibraltar", "01:00", ":Europe/Gibraltar", 0.530, "meters",
	{0.001, 0.020, 0.029, 0.009, 0.001, 0.298, 0.000, 0.017, 0.000, 0.000, 0.064, 0.009, 0.009, 0.000, 0.007, 0.004, 0.000, 0.001, 0.000, 0.107, 0.000, 0.000, 0.006, 0.002, 0.007, 0.012, 0.000, 0.000, 0.000, 0.006, 0.013, 0.000, 0.000, 0.000, 0.000, 0.052, 0.000, },
	{129.600, 146.000, 104.200, 87.000, 162.500, 75.000, 0.000, 220.000, 0.000, 0.000, 63.000, 51.000, 179.000, 113.000, 148.500, 220.000, 211.700, 103.100, 0.000, 102.000, 0.000, 0.000, 100.900, 87.500, 48.000, 64.600, 193.200, 0.000, 0.000, 182.000, 281.000, 0.000, 0.000, 0.000, 0.000, 211.000, 0.000, },
},
{"Guayaquil, Ecuador", "-05:00", ":America/Guayaquil", 6.350, "feet",
	{0.000, 0.329, 0.362, 0.380, 0.000, 4.888, 0.000, 0.548, 0.219, 0.078, 0.800, 0.106, 0.070, 0.000, 0.110, 0.013, 0.000, 0.000, 0.000, 1.080, 0.000, 0.000, 0.064, 0.034, 0.392, 0.156, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{0.000, 18.900, 249.500, 215.800, 0.000, 213.300, 0.000, 337.000, 5.500, 93.900, 190.900, 168.600, 315.100, 0.000, 18.400, 283.100, 0.000, 0.000, 0.000, 275.000, 0.000, 0.000, 275.300, 242.000, 315.200, 194.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Helgoland, Germany", "01:00", ":Europe/Berlin", 4.659, "feet",
	{0.009, 0.210, 0.275, 0.000, 0.000, 3.445, 0.006, 0.262, 0.065, 0.013, 0.574, 0.072, 0.302, 0.006, 0.085, 0.095, 0.009, 0.000, 0.042, 0.984, 0.013, 0.000, 0.045, 0.127, 0.324, 0.203, 0.019, 0.016, 0.000, 0.082, 0.141, 0.085, 0.059, 0.049, 0.108, 0.400, 0.223, },
	{144.400, 40.000, 44.700, 0.000, 0.000, 340.000, 182.800, 205.700, 18.800, 238.200, 313.300, 302.400, 250.000, 170.100, 42.000, 186.100, 131.300, 0.000, 231.600, 45.000, 357.100, 0.000, 31.800, 357.300, 68.800, 301.900, 171.000, 287.400, 0.000, 180.900, 265.700, 268.100, 145.600, 140.900, 260.600, 288.400, 123.100, },
},
{"Hong Kong, China", "08:00", ":Asia/Hong_Kong", 1.378, "meters",
	{0.015, 0.359, 0.044, 0.010, 0.021, 0.404, 0.016, 0.025, 0.004, 0.000, 0.085, 0.011, 0.289, 0.012, 0.112, 0.054, 0.008, 0.001, 0.002, 0.162, 0.002, 0.000, 0.010, 0.003, 0.019, 0.011, 0.011, 0.013, 0.000, 0.008, 0.017, 0.000, 0.018, 0.030, 0.005, 0.100, 0.057, },
	{311.600, 299.200, 297.300, 313.800, 274.300, 267.800, 335.300, 332.800, 161.500, 0.000, 250.300, 237.100, 249.300, 349.100, 293.900, 225.400, 199.800, 300.100, 135.000, 298.900, 8.100, 0.000, 284.100, 282.100, 215.800, 227.200, 227.900, 39.300, 0.000, 267.800, 19.200, 0.000, 61.700, 102.500, 3.600, 215.200, 34.700, },
},
{"Inch'on, South Korea", "09:00", ":Asia/Seoul", 15.220, "feet",
	{0.088, 1.332, 1.044, 0.298, 0.050, 9.672, 0.000, 0.351, 0.125, 0.000, 1.707, 0.372, 0.852, 0.064, 0.351, 0.169, 0.023, 0.031, 0.081, 3.808, 0.047, 0.000, 0.225, 0.068, 0.443, 0.256, 0.032, 0.098, 0.190, 0.111, 0.298, 0.348, 0.105, 0.049, 0.079, 0.692, 0.190, },
	{354.700, 304.100, 189.200, 168.300, 271.300, 137.100, 0.000, 79.700, 74.700, 0.000, 118.900, 116.700, 265.300, 346.400, 300.700, 238.800, 226.600, 196.200, 260.200, 195.900, 235.700, 0.000, 195.500, 164.400, 212.800, 103.700, 248.600, 258.200, 190.200, 52.400, 148.100, 86.600, 196.400, 63.200, 105.200, 129.100, 33.900, },
},
{"Isla Baltra, Galapagos Islands", "-05:00", ":Pacific/Galapagos", 1.150, "meters",
	{0.001, 0.071, 0.054, 0.024, 0.001, 0.730, 0.000, 0.000, 0.000, 0.000, 0.165, 0.022, 0.010, 0.000, 0.024, 0.002, 0.000, 0.002, 0.000, 0.200, 0.000, 0.000, 0.012, 0.005, 0.018, 0.032, 0.000, 0.000, 0.000, 0.002, 0.003, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{323.600, 330.000, 142.300, 128.000, 336.500, 98.000, 0.000, 0.000, 0.000, 0.000, 68.000, 38.000, 343.000, 317.000, 331.000, 110.000, 355.900, 140.600, 0.000, 139.000, 0.000, 0.000, 137.400, 117.000, 57.000, 72.000, 348.600, 0.000, 0.000, 39.000, 87.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Jolo, Philippines", "08:00", ":Asia/Manila", 0.340, "meters",
	{0.020, 0.258, 0.029, 0.004, 0.018, 0.173, 0.000, 0.000, 0.000, 0.000, 0.026, 0.003, 0.254, 0.011, 0.085, 0.052, 0.007, 0.001, 0.000, 0.105, 0.000, 0.000, 0.006, 0.001, 0.004, 0.005, 0.010, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{334.800, 315.000, 275.600, 249.000, 295.000, 227.000, 0.000, 0.000, 0.000, 0.000, 205.000, 183.000, 275.000, 355.000, 312.000, 258.000, 235.300, 273.800, 0.000, 272.000, 0.000, 0.000, 270.200, 247.900, 182.000, 207.900, 257.800, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Karachi, Pakistan", "05:00", ":Asia/Karachi", 1.640, "meters",
	{0.016, 0.403, 0.082, 0.026, 0.014, 0.793, 0.000, 0.004, 0.000, 0.000, 0.179, 0.024, 0.204, 0.009, 0.133, 0.038, 0.005, 0.002, 0.000, 0.300, 0.000, 0.000, 0.018, 0.006, 0.019, 0.035, 0.008, 0.000, 0.000, 0.004, 0.006, 0.000, 0.000, 0.000, 0.000, 0.032, 0.000, },
	{58.000, 56.000, 346.900, 327.000, 54.000, 308.000, 0.000, 61.000, 0.000, 0.000, 289.000, 270.000, 52.000, 60.000, 55.700, 58.000, 48.000, 345.400, 0.000, 344.000, 0.000, 0.000, 342.600, 324.700, 272.000, 291.500, 50.300, 0.000, 0.000, 61.000, 9.000, 0.000, 0.000, 0.000, 0.000, 48.000, 0.000, },
},
{"Karang Jamuang, Java, Indonesia", "07:00", ":Asia/Jakarta", 1.050, "meters",
	{0.021, 0.540, 0.022, 0.003, 0.018, 0.040, 0.000, 0.000, 0.000, 0.000, 0.020, 0.003, 0.260, 0.011, 0.179, 0.000, 0.007, 0.001, 0.000, 0.080, 0.000, 0.000, 0.005, 0.000, 0.001, 0.004, 0.010, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{345.800, 317.000, 354.400, -12.000, 288.000, 16.000, 0.000, 0.000, 0.000, 0.000, 44.000, 72.000, 259.000, 375.000, 312.700, 0.000, 201.500, 355.200, 0.000, 356.000, 0.000, 0.000, 356.800, 366.700, 396.000, 40.200, 234.100, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Korsakov, Russia", "11:00", ":Asia/Magadan", 0.730, "meters",
	{0.017, 0.219, 0.026, 0.004, 0.015, 0.200, 0.000, 0.001, 0.000, 0.000, 0.031, 0.004, 0.218, 0.009, 0.072, 0.046, 0.006, 0.001, 0.000, 0.095, 0.000, 0.000, 0.006, 0.001, 0.005, 0.006, 0.008, 0.000, 0.000, 0.000, 0.002, 0.000, 0.000, 0.000, 0.000, 0.034, 0.000, },
	{239.900, 224.000, 191.100, 170.000, 208.000, 136.000, 0.000, 328.000, 0.000, 0.000, 102.000, 68.000, 192.000, 256.000, 221.600, 168.000, 160.300, 189.000, 0.000, 187.000, 0.000, 0.000, 185.000, 159.700, 85.000, 106.600, 178.300, 0.000, 0.000, 0.000, 222.000, 0.000, 0.000, 0.000, 0.000, 221.000, 0.000, },
},
{"La Union, El Salvador", "-06:00", ":America/El_Salvador", 5.100, "feet",
	{0.014, 0.373, 0.215, 0.070, 0.013, 3.978, 0.000, 0.181, 0.058, 0.000, 0.834, 0.113, 0.185, 0.000, 0.107, 0.044, 0.000, 0.000, 0.086, 0.883, 0.000, 0.000, 0.052, 0.028, 0.145, 0.162, 0.000, 0.056, 0.000, 0.075, 0.088, 0.000, 0.000, 0.000, 0.000, 0.142, 0.061, },
	{347.500, 3.600, 131.100, 97.300, 19.600, 77.300, 0.000, 284.300, 122.100, 0.000, 50.100, 24.100, 35.900, 0.000, 3.500, 55.100, 0.000, 0.000, 80.600, 141.400, 0.000, 0.000, 141.700, 107.100, 42.900, 52.200, 0.000, 163.800, 0.000, 261.700, 5.800, 0.000, 0.000, 0.000, 0.000, 153.100, 205.200, },
},
{"Le Havre, France", "01:00", ":Europe/Paris", 4.870, "meters",
	{0.004, 0.094, 0.239, 0.070, 0.003, 2.608, 0.000, 0.252, 0.000, 0.000, 0.491, 0.065, 0.049, 0.002, 0.031, 0.011, 0.001, 0.007, 0.000, 0.878, 0.000, 0.000, 0.052, 0.018, 0.063, 0.095, 0.002, 0.000, 0.000, 0.089, 0.174, 0.000, 0.000, 0.000, 0.000, 0.075, 0.000, },
	{180.100, 131.000, 6.900, 336.000, 81.500, 315.000, 0.000, 136.000, 0.000, 0.000, 294.000, 273.000, 32.000, 230.000, 123.600, 357.000, -66.200, 4.900, 0.000, 3.000, 0.000, 0.000, 1.100, -22.700, -93.000, 296.800, -10.500, 0.000, 0.000, 113.000, 190.000, 0.000, 0.000, 0.000, 0.000, 236.000, 0.000, },
},
{"Legaspi, Philippines", "08:00", ":Asia/Manila", 0.740, "meters",
	{0.009, 0.158, 0.060, 0.014, 0.008, 0.553, 0.000, 0.000, 0.000, 0.000, 0.100, 0.013, 0.108, 0.005, 0.052, 0.024, 0.003, 0.002, 0.000, 0.221, 0.000, 0.000, 0.013, 0.004, 0.013, 0.019, 0.004, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.077, 0.000, },
	{207.400, 198.000, 196.000, 177.000, 188.500, 156.000, 0.000, 0.000, 0.000, 0.000, 135.000, 114.000, 179.000, 217.000, 196.600, 171.000, 160.200, 194.500, 0.000, 193.000, 0.000, 0.000, 191.500, 173.200, 119.000, 137.800, 170.800, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 132.000, 0.000, },
},
{"Lisbon, Portugal", "01:00", ":Europe/Lisbon", 2.200, "meters",
	{0.005, 0.068, 0.109, 0.034, 0.004, 1.150, 0.000, 0.077, 0.000, 0.000, 0.238, 0.032, 0.061, 0.003, 0.023, 0.015, 0.002, 0.003, 0.000, 0.401, 0.000, 0.000, 0.024, 0.008, 0.028, 0.046, 0.002, 0.000, 0.000, 0.000, 0.048, 0.000, 0.000, 0.000, 0.000, 0.020, 0.000, },
	{122.100, 73.000, 138.400, 123.000, 23.500, 106.000, 0.000, 303.000, 0.000, 0.000, 89.000, 72.000, 334.000, 172.000, 65.600, 277.000, -124.200, 137.200, 0.000, 136.000, 0.000, 0.000, 134.800, 119.900, 76.000, 91.300, -68.500, 0.000, 0.000, 0.000, 342.000, 0.000, 0.000, 0.000, 0.000, 33.000, 0.000, },
},
{"Madras, India", "05:30", ":Asia/Calcutta", 0.650, "meters",
	{0.002, 0.090, 0.037, 0.010, 0.002, 0.332, 0.000, 0.002, 0.000, 0.000, 0.072, 0.010, 0.028, 0.001, 0.030, 0.001, 0.001, 0.001, 0.000, 0.137, 0.000, 0.000, 0.008, 0.002, 0.008, 0.014, 0.001, 0.000, 0.000, 0.001, 0.002, 0.000, 0.000, 0.000, 0.000, 0.102, 0.000, },
	{354.900, 341.000, 281.900, 251.000, 327.000, 243.000, 0.000, 102.000, 0.000, 0.000, 235.000, 227.000, 313.000, 369.000, 338.900, 86.000, 285.200, 280.400, 0.000, 279.000, 0.000, 0.000, 277.600, 259.700, 207.000, 236.100, 301.000, 0.000, 0.000, 77.000, 179.000, 0.000, 0.000, 0.000, 0.000, 221.000, 0.000, },
},
{"Makung, China", "08:00", ":Asia/Shanghai", 1.600, "meters",
	{0.017, 0.250, 0.063, 0.022, 0.016, 0.810, 0.000, 0.000, 0.000, 0.000, 0.154, 0.020, 0.220, 0.009, 0.083, 0.042, 0.006, 0.002, 0.000, 0.230, 0.000, 0.000, 0.014, 0.006, 0.019, 0.030, 0.008, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{305.900, 288.000, 35.800, 365.000, 270.000, 345.000, 0.000, 0.000, 0.000, 0.000, 325.000, 305.000, 252.000, 324.000, 285.300, 232.000, 216.300, 33.900, 0.000, 32.000, 0.000, 0.000, 30.100, 6.800, -62.000, 327.700, 236.600, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Malecon, Venezuela", "-04:00", ":America/Caracas", 0.810, "meters",
	{0.006, 0.134, 0.012, 0.020, 0.006, 0.407, 0.000, 0.008, 0.000, 0.000, 0.142, 0.019, 0.080, 0.003, 0.044, 0.016, 0.002, 0.000, 0.000, 0.045, 0.000, 0.000, 0.003, 0.003, 0.010, 0.028, 0.003, 0.000, 0.000, 0.008, 0.008, 0.000, 0.000, 0.000, 0.000, 0.048, 0.000, },
	{190.500, 187.000, 67.400, 187.000, 183.500, 156.000, 0.000, 145.000, 0.000, 0.000, 125.000, 94.000, 180.000, 194.000, 186.500, 165.000, 173.100, 70.700, 0.000, 74.000, 0.000, 0.000, 77.300, 118.000, 238.000, 129.200, 177.000, 0.000, 0.000, 100.000, 13.000, 0.000, 0.000, 0.000, 0.000, 199.000, 0.000, },
},
{"Manila, Philippines", "08:00", ":Asia/Manila", 0.480, "meters",
	{0.022, 0.299, 0.018, 0.004, 0.020, 0.191, 0.000, 0.004, 0.000, 0.000, 0.030, 0.004, 0.278, 0.012, 0.099, 0.055, 0.007, 0.001, 0.000, 0.065, 0.000, 0.000, 0.004, 0.001, 0.005, 0.006, 0.011, 0.000, 0.000, 0.002, 0.001, 0.000, 0.000, 0.000, 0.000, 0.124, 0.000, },
	{345.800, 320.000, 336.100, 331.000, 294.000, 295.000, 0.000, 343.000, 0.000, 0.000, 259.000, 223.000, 268.000, 372.000, 316.100, 243.000, 216.400, 334.500, 0.000, 333.000, 0.000, 0.000, 331.500, 312.600, 257.000, 263.800, 245.700, 0.000, 0.000, 312.000, 313.000, 0.000, 0.000, 0.000, 0.000, 144.000, 0.000, },
},
{"Matarani, Peru", "-05:00", ":America/Lima", 0.430, "meters",
	{0.007, 0.157, 0.023, 0.010, 0.006, 0.303, 0.000, 0.001, 0.000, 0.000, 0.069, 0.009, 0.085, 0.004, 0.052, 0.014, 0.002, 0.001, 0.000, 0.084, 0.000, 0.000, 0.005, 0.002, 0.007, 0.013, 0.003, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{334.900, 317.000, 255.400, 276.000, 299.000, 237.000, 0.000, 61.000, 0.000, 0.000, 198.000, 159.000, 281.000, 353.000, 314.300, 251.000, 245.300, 254.700, 0.000, 254.000, 0.000, 0.000, 253.300, 244.900, 220.000, 203.200, 265.600, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Mergui, Myanmar", "06:30", ":Asia/Rangoon", 2.790, "meters",
	{0.005, 0.160, 0.243, 0.045, 0.005, 1.675, 0.000, 0.037, 0.000, 0.000, 0.316, 0.042, 0.064, 0.003, 0.053, 0.009, 0.002, 0.007, 0.000, 0.892, 0.000, 0.000, 0.053, 0.012, 0.040, 0.061, 0.002, 0.000, 0.000, 0.043, 0.048, 0.000, 0.000, 0.000, 0.000, 0.180, 0.000, },
	{345.900, 333.000, 350.700, 306.000, 320.000, 301.000, 0.000, 115.000, 0.000, 0.000, 296.000, 291.000, 307.000, 359.000, 331.100, 271.000, 281.200, 348.800, 0.000, 347.000, 0.000, 0.000, 345.200, 322.300, 255.000, 296.700, 295.800, 0.000, 0.000, 169.000, 165.000, 0.000, 0.000, 0.000, 0.000, 146.000, 0.000, },
},
{"Mina Salman, Bahrain", "03:00", ":Asia/Bahrain", 1.460, "meters",
	{0.004, 0.071, 0.059, 0.021, 0.003, 0.661, 0.000, 0.014, 0.000, 0.000, 0.147, 0.020, 0.048, 0.002, 0.024, 0.008, 0.001, 0.002, 0.000, 0.218, 0.000, 0.000, 0.013, 0.005, 0.016, 0.029, 0.002, 0.000, 0.000, 0.008, 0.008, 0.000, 0.000, 0.000, 0.000, 0.116, 0.000, },
	{79.700, 44.000, 217.900, 182.000, 8.000, 152.000, 0.000, 204.000, 0.000, 0.000, 122.000, 92.000, 332.000, 116.000, 38.600, 260.000, -99.400, 215.400, 0.000, 213.000, 0.000, 0.000, 210.600, 180.300, 91.000, 126.000, -58.900, 0.000, 0.000, 189.000, 244.000, 0.000, 0.000, 0.000, 0.000, 127.000, 0.000, },
},
{"Narvik, Norway", "01:00", ":Europe/Oslo", 5.892, "feet",
	{0.000, 0.384, 0.433, 0.082, 0.000, 3.074, 0.000, 0.138, 0.000, 0.000, 0.699, 0.000, 0.046, 0.000, 0.102, 0.052, 0.000, 0.069, 0.000, 1.112, 0.000, 0.000, 0.177, 0.000, 0.092, 0.102, 0.000, 0.000, 0.000, 0.000, 0.105, 0.059, 0.000, 0.000, 0.000, 0.381, 0.171, },
	{0.000, 210.600, 36.300, 46.700, 0.000, 0.200, 0.000, 343.400, 0.000, 0.000, 337.600, 0.000, 31.500, 0.000, 206.600, 344.000, 0.000, 113.200, 0.000, 40.200, 0.000, 0.000, 357.200, 0.000, 312.200, 276.700, 0.000, 0.000, 0.000, 0.000, 25.400, 140.200, 0.000, 0.000, 0.000, 271.000, 223.100, },
},
{"Orange Bay, Chile", "-04:00", ":America/Santiago", 1.350, "meters",
	{0.014, 0.216, 0.025, 0.021, 0.013, 0.589, 0.000, 0.005, 0.000, 0.000, 0.150, 0.020, 0.179, 0.008, 0.071, 0.035, 0.005, 0.001, 0.000, 0.092, 0.000, 0.000, 0.005, 0.004, 0.014, 0.029, 0.007, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.048, 0.000, },
	{66.300, 44.000, 153.200, 160.000, 21.500, 124.000, 0.000, 237.000, 0.000, 0.000, 88.000, 52.000, 359.000, 89.000, 40.600, 337.000, -45.600, 152.100, 0.000, 151.000, 0.000, 0.000, 149.900, 136.500, 97.000, 92.800, -20.300, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 92.000, 0.000, },
},
{"Pei-Hai, China", "08:00", ":Asia/Shanghai", 9.740, "feet",
	{0.298, 3.447, 0.107, 0.036, 0.267, 1.282, 0.000, 0.000, 0.000, 0.000, 0.249, 0.033, 3.771, 0.162, 1.140, 0.731, 0.098, 0.000, 0.000, 0.395, 0.000, 0.000, 0.023, 0.000, 0.000, 0.048, 0.143, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{106.000, 87.000, 232.000, 197.000, 69.000, 193.000, 0.000, 0.000, 0.000, 0.000, 172.000, 184.000, 51.000, 123.000, 87.000, 34.000, 16.000, 0.000, 0.000, 231.000, 0.000, 0.000, 230.000, 0.000, 0.000, 189.000, 36.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Pointe de Grave, France", "01:00", ":Europe/Paris", 3.210, "meters",
	{0.006, 0.045, 0.142, 0.044, 0.005, 1.575, 0.000, 0.075, 0.000, 0.000, 0.306, 0.041, 0.071, 0.003, 0.015, 0.025, 0.002, 0.004, 0.000, 0.521, 0.000, 0.000, 0.031, 0.011, 0.038, 0.059, 0.003, 0.000, 0.000, 0.031, 0.021, 0.000, 0.000, 0.000, 0.000, 0.049, 0.000, },
	{155.000, 99.000, 175.800, 157.000, 42.500, 138.000, 0.000, 69.000, 0.000, 0.000, 119.000, 100.000, 346.000, 212.000, 90.500, 304.000, -126.100, 174.400, 0.000, 173.000, 0.000, 0.000, 171.600, 154.200, 103.000, 121.500, -62.500, 0.000, 0.000, 18.000, 176.000, 0.000, 0.000, 0.000, 0.000, 255.000, 0.000, },
},
{"Ponta Delgada, Sao Miguel, Azores", "-01:00", ":Atlantic/Azores", 3.280, "feet",
	{0.000, 0.144, 0.141, 0.036, 0.000, 1.611, 0.000, 0.013, 0.000, 0.000, 0.371, 0.043, 0.082, 0.000, 0.049, 0.023, 0.000, 0.000, 0.013, 0.587, 0.000, 0.000, 0.036, 0.000, 0.069, 0.085, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.016, 0.062, 0.115, 0.151, },
	{0.000, 51.500, 46.800, 183.500, 0.000, 34.200, 0.000, 58.400, 0.000, 0.000, 20.400, 13.500, 303.500, 0.000, 42.100, 243.200, 0.000, 0.000, 299.100, 53.400, 0.000, 0.000, 52.700, 0.000, 352.300, 22.400, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 226.500, 27.200, 210.700, 246.400, },
},
{"Port Kem, Russia", "03:00", ":Europe/Moscow", 1.100, "meters",
	{0.000, 0.068, 0.039, 0.017, 0.000, 0.586, 0.000, 0.048, 0.000, 0.000, 0.118, 0.016, 0.006, 0.000, 0.023, 0.010, 0.000, 0.001, 0.000, 0.144, 0.000, 0.000, 0.008, 0.004, 0.014, 0.023, 0.000, 0.000, 0.000, 0.000, 0.024, 0.000, 0.000, 0.000, 0.000, 0.056, 0.000, },
	{167.000, 109.000, 200.800, 193.000, 50.500, 150.000, 0.000, 234.000, 0.000, 0.000, 107.000, 64.000, 352.000, 226.000, 100.200, 212.000, -124.100, 198.900, 0.000, 197.000, 0.000, 0.000, 195.100, 171.800, 103.000, 112.800, -58.200, 0.000, 0.000, 0.000, 238.000, 0.000, 0.000, 0.000, 0.000, 143.000, 0.000, },
},
{"Puerto Gallegos, Argentina", "-03:00", ":America/Buenos_Aires", 6.190, "meters",
	{0.017, 0.263, 0.274, 0.139, 0.015, 3.733, 0.000, 0.205, 0.000, 0.000, 0.971, 0.129, 0.211, 0.009, 0.087, 0.041, 0.005, 0.008, 0.000, 1.007, 0.000, 0.000, 0.059, 0.026, 0.090, 0.188, 0.008, 0.000, 0.000, 0.102, 0.147, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{130.800, 101.000, 362.300, 330.000, 71.000, 305.000, 0.000, 183.000, 0.000, 0.000, 280.000, 255.000, 41.000, 161.000, 96.500, 25.000, -18.500, 360.100, 0.000, 358.000, 0.000, 0.000, 355.900, 329.600, 252.000, 283.400, 15.300, 0.000, 0.000, 159.000, 249.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Puerto Montt, Chile", "-04:00", ":America/Santiago", 3.600, "meters",
	{0.013, 0.237, 0.215, 0.068, 0.011, 1.929, 0.000, 0.015, 0.000, 0.000, 0.475, 0.063, 0.159, 0.007, 0.078, 0.033, 0.004, 0.006, 0.000, 0.791, 0.000, 0.000, 0.047, 0.014, 0.046, 0.092, 0.006, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{29.800, 11.000, 63.400, 69.000, -8.000, 45.000, 0.000, 330.000, 0.000, 0.000, 21.000, -3.000, 333.000, 49.000, 8.200, 308.000, -64.700, 62.700, 0.000, 62.000, 0.000, 0.000, 61.300, 52.900, 28.000, 24.200, -43.300, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Punta Arenas, Chile", "-04:00", ":America/Santiago", 1.200, "meters",
	{0.018, 0.308, 0.058, 0.011, 0.016, 0.517, 0.000, 0.000, 0.000, 0.000, 0.077, 0.010, 0.223, 0.010, 0.102, 0.048, 0.006, 0.002, 0.000, 0.215, 0.000, 0.000, 0.013, 0.004, 0.012, 0.015, 0.008, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{89.800, 68.000, 91.200, 391.000, 46.000, 355.000, 0.000, 0.000, 0.000, 0.000, 319.000, 283.000, 24.000, 112.000, 64.700, 355.000, -19.600, 87.600, 0.000, 84.000, 0.000, 0.000, 80.400, 36.300, -94.000, 323.800, 5.100, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Punta Gorda, Venezuela", "-04:00", ":America/Caracas", 1.010, "meters",
	{0.007, 0.126, 0.065, 0.023, 0.006, 0.810, 0.000, 0.064, 0.000, 0.000, 0.158, 0.021, 0.088, 0.004, 0.042, 0.012, 0.002, 0.002, 0.000, 0.240, 0.000, 0.000, 0.014, 0.006, 0.019, 0.031, 0.003, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.142, 0.000, },
	{201.000, 197.000, 185.500, 168.000, 193.000, 152.000, 0.000, 196.000, 0.000, 0.000, 136.000, 120.000, 189.000, 205.000, 196.400, 183.000, 181.100, 184.200, 0.000, 183.000, 0.000, 0.000, 181.800, 166.400, 121.000, 138.100, 185.600, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 184.000, 0.000, },
},
{"Pusan, South Korea", "09:00", ":Asia/Seoul", 0.640, "meters",
	{0.001, 0.044, 0.051, 0.011, 0.001, 0.400, 0.000, 0.011, 0.000, 0.000, 0.079, 0.011, 0.016, 0.001, 0.015, 0.001, 0.000, 0.002, 0.000, 0.189, 0.000, 0.000, 0.011, 0.003, 0.010, 0.015, 0.001, 0.000, 0.000, 0.005, 0.012, 0.000, 0.000, 0.000, 0.000, 0.102, 0.000, },
	{159.900, 143.000, 276.000, 249.000, 126.000, 236.000, 0.000, 190.000, 0.000, 0.000, 223.000, 210.000, 109.000, 177.000, 140.500, 201.000, 75.300, 274.500, 0.000, 273.000, 0.000, 0.000, 271.500, 253.200, 199.000, 224.700, 94.400, 0.000, 0.000, 173.000, 232.000, 0.000, 0.000, 0.000, 0.000, 131.000, 0.000, },
},
{"Ra's al Qulay`ah, Saudi Arabia", "03:00", ":Asia/Riyadh", 1.070, "meters",
	{0.008, 0.180, 0.044, 0.014, 0.007, 0.480, 0.000, 0.010, 0.000, 0.000, 0.100, 0.013, 0.100, 0.004, 0.060, 0.020, 0.003, 0.001, 0.000, 0.160, 0.000, 0.000, 0.009, 0.003, 0.012, 0.019, 0.004, 0.000, 0.000, 0.000, 0.010, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{348.300, 319.000, 187.900, 152.000, 289.500, 123.000, 0.000, 204.000, 0.000, 0.000, 94.000, 65.000, 260.000, 378.000, 314.600, 244.000, 201.500, 185.400, 0.000, 183.000, 0.000, 0.000, 180.600, 150.800, 63.000, 97.900, 234.700, 0.000, 0.000, 0.000, 294.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Rangoon River, Myanmar", "06:30", ":Asia/Rangoon", 3.660, "meters",
	{0.008, 0.227, 0.197, 0.048, 0.007, 1.799, 0.000, 0.086, 0.000, 0.000, 0.339, 0.045, 0.098, 0.004, 0.075, 0.008, 0.003, 0.006, 0.000, 0.726, 0.000, 0.000, 0.043, 0.013, 0.043, 0.066, 0.004, 0.000, 0.000, 0.058, 0.089, 0.000, 0.000, 0.000, 0.000, 0.257, 0.000, },
	{31.400, 21.000, 145.500, 118.000, 10.500, 99.000, 0.000, 80.000, 0.000, 0.000, 80.000, 61.000, 0.000, 42.000, 19.400, 342.000, -20.800, 143.700, 0.000, 142.000, 0.000, 0.000, 140.300, 119.000, 56.000, 82.500, -9.000, 0.000, 0.000, 54.000, 125.000, 0.000, 0.000, 0.000, 0.000, 140.000, 0.000, },
},
{"Sagar Island, India", "05:30", ":Asia/Calcutta", 3.000, "meters",
	{0.004, 0.150, 0.180, 0.039, 0.004, 1.402, 0.000, 0.048, 0.000, 0.000, 0.276, 0.037, 0.052, 0.002, 0.050, 0.004, 0.001, 0.005, 0.000, 0.661, 0.000, 0.000, 0.039, 0.010, 0.034, 0.054, 0.002, 0.000, 0.000, 0.023, 0.048, 0.000, 0.000, 0.000, 0.000, 0.276, 0.000, },
	{353.900, 345.000, 318.200, 287.000, 336.000, 275.000, 0.000, 133.000, 0.000, 0.000, 263.000, 251.000, 327.000, 363.000, 343.700, 54.000, 309.100, 316.600, 0.000, 315.000, 0.000, 0.000, 313.400, 293.600, 235.000, 264.600, 319.300, 0.000, 0.000, 112.000, 170.000, 0.000, 0.000, 0.000, 0.000, 147.000, 0.000, },
},
{"San Cristobal, Galapagos Islands", "-05:00", ":Pacific/Galapagos", 1.130, "meters",
	{0.001, 0.082, 0.058, 0.022, 0.001, 0.709, 0.000, 0.003, 0.000, 0.000, 0.153, 0.020, 0.013, 0.001, 0.027, 0.003, 0.000, 0.002, 0.000, 0.214, 0.000, 0.000, 0.013, 0.005, 0.017, 0.030, 0.000, 0.000, 0.000, 0.002, 0.006, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{338.400, 332.000, 145.300, 128.000, 325.500, 101.000, 0.000, 154.000, 0.000, 0.000, 74.000, 47.000, 319.000, 345.000, 331.000, 108.000, 306.100, 143.600, 0.000, 142.000, 0.000, 0.000, 140.400, 120.000, 60.000, 77.600, 313.400, 0.000, 0.000, 343.000, 120.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"San Fernando, Philippines", "08:00", ":Asia/Manila", 0.370, "meters",
	{0.016, 0.244, 0.007, 0.002, 0.014, 0.076, 0.000, 0.000, 0.000, 0.000, 0.017, 0.002, 0.201, 0.009, 0.081, 0.041, 0.005, 0.000, 0.000, 0.025, 0.000, 0.000, 0.001, 0.001, 0.002, 0.003, 0.008, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.138, 0.000, },
	{334.300, 312.000, 284.500, 260.000, 289.500, 264.000, 0.000, 0.000, 0.000, 0.000, 268.000, 272.000, 267.000, 357.000, 308.600, 250.000, 222.400, 283.800, 0.000, 283.000, 0.000, 0.000, 282.200, 272.800, 245.000, 267.500, 247.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 141.000, 0.000, },
},
{"Sfax, Tunisia", "01:00", ":Africa/Tunis", 0.990, "meters",
	{0.001, 0.018, 0.073, 0.009, 0.001, 0.416, 0.000, 0.003, 0.000, 0.000, 0.062, 0.008, 0.008, 0.000, 0.006, 0.002, 0.000, 0.002, 0.000, 0.267, 0.000, 0.000, 0.016, 0.003, 0.010, 0.012, 0.000, 0.000, 0.000, 0.000, 0.010, 0.000, 0.000, 0.000, 0.000, 0.044, 0.000, },
	{-17.700, 20.000, 135.300, 112.000, 58.000, 105.000, 0.000, 117.000, 0.000, 0.000, 98.000, 91.000, 96.000, -56.000, 25.700, 109.000, 171.400, 134.100, 0.000, 133.000, 0.000, 0.000, 131.900, 118.000, 77.000, 98.900, 128.600, 0.000, 0.000, 0.000, 109.000, 0.000, 0.000, 0.000, 0.000, 218.000, 0.000, },
},
{"Shaat al Arab, Iraq", "03:00", ":Asia/Baghdad", 1.740, "meters",
	{0.024, 0.497, 0.078, 0.024, 0.021, 0.841, 0.000, 0.027, 0.000, 0.000, 0.166, 0.022, 0.298, 0.013, 0.165, 0.047, 0.008, 0.002, 0.000, 0.286, 0.000, 0.000, 0.017, 0.006, 0.020, 0.032, 0.011, 0.000, 0.000, 0.009, 0.047, 0.000, 0.000, 0.000, 0.000, 0.113, 0.000, },
	{318.800, 295.000, 13.900, 342.000, 271.000, 308.000, 0.000, 205.000, 0.000, 0.000, 274.000, 240.000, 247.000, 343.000, 291.400, 234.000, 199.400, 11.400, 0.000, 9.000, 0.000, 0.000, 6.600, -23.700, -113.000, 278.600, 226.400, 0.000, 0.000, 137.000, 284.000, 0.000, 0.000, 0.000, 0.000, 132.000, 0.000, },
},
{"Shanghai, China", "08:00", ":Asia/Shanghai", 2.020, "meters",
	{0.011, 0.229, 0.113, 0.023, 0.010, 0.939, 0.000, 0.169, 0.000, 0.000, 0.161, 0.021, 0.143, 0.006, 0.076, 0.022, 0.004, 0.003, 0.000, 0.415, 0.000, 0.000, 0.024, 0.007, 0.023, 0.031, 0.005, 0.000, 0.000, 0.063, 0.137, 0.000, 0.000, 0.000, 0.000, 0.247, 0.000, },
	{241.300, 215.000, 64.200, 21.000, 188.500, 8.000, 0.000, 307.000, 0.000, 0.000, 355.000, -18.000, 162.000, 268.000, 211.000, 154.000, 109.400, 62.100, 0.000, 60.000, 0.000, 0.000, 57.900, 32.100, -44.000, -3.300, 139.300, 0.000, 0.000, 289.000, 357.000, 0.000, 0.000, 0.000, 0.000, 132.000, 0.000, },
},
{"Singapore (Victoria Dock)", "08:00", ":Asia/Singapore", 1.596, "meters",
	{0.012, 0.294, 0.088, 0.035, 0.011, 0.796, 0.006, 0.015, 0.011, 0.000, 0.143, 0.014, 0.283, 0.009, 0.088, 0.061, 0.001, 0.002, 0.018, 0.326, 0.003, 0.000, 0.022, 0.022, 0.003, 0.035, 0.013, 0.022, 0.000, 0.006, 0.019, 0.021, 0.007, 0.025, 0.020, 0.131, 0.020, },
	{139.800, 116.200, 19.400, 323.200, 98.800, 326.900, 140.800, 297.900, 118.200, 0.000, 306.100, 283.900, 63.500, 197.500, 108.800, 24.400, 287.700, 195.000, 233.800, 21.100, 11.500, 0.000, 31.900, 351.700, 284.000, 316.800, 44.300, 104.800, 0.000, 332.600, 348.500, 241.800, 54.100, 64.900, 359.400, 281.300, 186.000, },
},
{"Suez, Egypt", "02:00", ":Africa/Cairo", 1.140, "meters",
	{0.001, 0.045, 0.038, 0.026, 0.001, 0.560, 0.000, 0.009, 0.000, 0.000, 0.182, 0.024, 0.013, 0.001, 0.015, 0.004, 0.000, 0.001, 0.000, 0.141, 0.000, 0.000, 0.008, 0.004, 0.013, 0.035, 0.000, 0.000, 0.000, 0.012, 0.005, 0.000, 0.000, 0.000, 0.000, 0.145, 0.000, },
	{177.100, 184.000, 6.300, 367.000, 191.000, 336.000, 0.000, 112.000, 0.000, 0.000, 305.000, 274.000, 198.000, 170.000, 185.100, 189.000, 211.900, 5.100, 0.000, 4.000, 0.000, 0.000, 2.900, -11.000, -52.000, 309.200, 204.000, 0.000, 0.000, 224.000, 151.000, 0.000, 0.000, 0.000, 0.000, 315.000, 0.000, },
},
{"Sungai Barito, Borneo, Indonesia", "08:00", ":Asia/Ujung_Pandang", 1.300, "meters",
	{0.026, 0.640, 0.008, 0.013, 0.023, 0.340, 0.000, 0.000, 0.000, 0.000, 0.090, 0.012, 0.330, 0.014, 0.212, 0.050, 0.009, 0.000, 0.000, 0.030, 0.000, 0.000, 0.002, 0.002, 0.008, 0.017, 0.013, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{369.300, 340.000, 77.800, 129.000, 310.500, 121.000, 0.000, 0.000, 0.000, 0.000, 113.000, 105.000, 281.000, 399.000, 335.600, 261.000, 222.500, 79.400, 0.000, 81.000, 0.000, 0.000, 82.600, 102.400, 161.000, 114.100, 255.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Sungai Kutei, Borneo, Indonesia", "08:00", ":Asia/Ujung_Pandang", 1.400, "meters",
	{0.012, 0.240, 0.117, 0.013, 0.011, 0.590, 0.000, 0.000, 0.000, 0.000, 0.090, 0.012, 0.150, 0.006, 0.079, 0.000, 0.004, 0.003, 0.000, 0.430, 0.000, 0.000, 0.025, 0.004, 0.014, 0.017, 0.006, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{305.800, 287.000, 228.500, 184.000, 268.000, 169.000, 0.000, 0.000, 0.000, 0.000, 154.000, 139.000, 249.000, 325.000, 284.200, 0.000, 211.300, 226.200, 0.000, 224.000, 0.000, 0.000, 221.800, 194.500, 114.000, 156.000, 232.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Swatow, China", "08:00", ":Asia/Shanghai", 5.690, "feet",
	{0.063, 0.993, 0.070, 0.060, 0.058, 1.372, 0.038, 0.226, 0.038, 0.000, 0.240, 0.032, 0.794, 0.034, 0.308, 0.149, 0.021, 0.000, 0.065, 0.331, 0.000, 0.000, 0.020, 0.000, 0.070, 0.060, 0.030, 0.000, 0.000, 0.000, 0.103, 0.000, 0.069, 0.074, 0.050, 0.240, 0.237, },
	{316.000, 294.000, 112.000, 48.000, 289.000, 21.000, 338.000, 152.000, 161.000, 0.000, 349.000, 317.000, 249.000, 338.000, 289.000, 229.000, 204.000, 0.000, 109.000, 94.000, 0.000, 0.000, 94.000, 0.000, 166.000, 336.000, 230.000, 0.000, 0.000, 0.000, 205.000, 0.000, 129.000, 8.000, 302.000, 299.000, 72.000, },
},
{"Takoradi, Ghana", "00:00", ":Africa/Accra", 0.960, "meters",
	{0.002, 0.119, 0.043, 0.014, 0.002, 0.449, 0.000, 0.006, 0.000, 0.000, 0.095, 0.013, 0.023, 0.001, 0.039, 0.003, 0.001, 0.001, 0.000, 0.159, 0.000, 0.000, 0.009, 0.003, 0.011, 0.018, 0.001, 0.000, 0.000, 0.003, 0.004, 0.000, 0.000, 0.000, 0.000, 0.070, 0.000, },
	{367.400, 351.000, 136.200, 112.000, 334.500, 107.000, 0.000, 320.000, 0.000, 0.000, 102.000, 97.000, 318.000, 384.000, 348.500, 127.000, 285.300, 135.100, 0.000, 134.000, 0.000, 0.000, 132.900, 119.500, 80.000, 102.700, 303.800, 0.000, 0.000, 243.000, 20.000, 0.000, 0.000, 0.000, 0.000, 277.000, 0.000, },
},
{"Talara, Peru", "-05:00", ":America/Lima", 0.790, "meters",
	{0.003, 0.118, 0.050, 0.019, 0.003, 0.579, 0.000, 0.000, 0.000, 0.000, 0.131, 0.017, 0.038, 0.002, 0.039, 0.000, 0.001, 0.001, 0.000, 0.184, 0.000, 0.000, 0.011, 0.004, 0.014, 0.025, 0.001, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.051, 0.000, },
	{334.900, 320.000, 147.800, 137.000, 305.000, 110.000, 0.000, 0.000, 0.000, 0.000, 83.000, 56.000, 290.000, 350.000, 317.800, 0.000, 260.200, 146.400, 0.000, 145.000, 0.000, 0.000, 143.600, 126.200, 75.000, 86.600, 277.100, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 30.000, 0.000, },
},
{"Tanjung Priok, Java, Indonesia", "07:00", ":Asia/Jakarta", 0.600, "meters",
	{0.010, 0.250, 0.014, 0.003, 0.009, 0.050, 0.000, 0.000, 0.000, 0.000, 0.020, 0.003, 0.130, 0.006, 0.083, 0.030, 0.003, 0.000, 0.000, 0.050, 0.000, 0.000, 0.003, 0.000, 0.001, 0.004, 0.005, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{153.900, 144.000, 291.500, 399.000, 134.000, 351.000, 0.000, 0.000, 0.000, 0.000, 303.000, 255.000, 124.000, 164.000, 142.500, 123.000, 104.200, 293.800, 0.000, 296.000, 0.000, 0.000, 298.200, 325.500, 406.000, 309.400, 115.400, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Tianjin Xingang, China", "08:00", ":Asia/Shanghai", 1.580, "meters",
	{0.014, 0.245, 0.066, 0.018, 0.012, 0.935, 0.000, 0.106, 0.000, 0.000, 0.126, 0.017, 0.176, 0.008, 0.081, 0.037, 0.005, 0.002, 0.000, 0.242, 0.000, 0.000, 0.014, 0.007, 0.022, 0.024, 0.007, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.204, 0.000, },
	{169.300, 149.000, 175.200, 124.000, 128.500, 92.000, 0.000, 103.000, 0.000, 0.000, 60.000, 28.000, 108.000, 190.000, 145.900, 72.000, 67.300, 172.100, 0.000, 169.000, 0.000, 0.000, 165.900, 127.700, 15.000, 64.300, 90.400, 0.000, 0.000, 0.000, 181.000, 0.000, 0.000, 0.000, 0.000, 117.000, 0.000, },
},
{"Valparaiso, Chile", "-04:00", ":America/Santiago", 0.910, "meters",
	{0.008, 0.159, 0.040, 0.014, 0.007, 0.441, 0.000, 0.000, 0.000, 0.000, 0.096, 0.013, 0.102, 0.004, 0.053, 0.020, 0.003, 0.001, 0.000, 0.146, 0.000, 0.000, 0.009, 0.003, 0.011, 0.019, 0.004, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.036, 0.000, },
	{360.800, 341.000, 324.500, 335.000, 321.000, 305.000, 0.000, 0.000, 0.000, 0.000, 275.000, 245.000, 301.000, 381.000, 338.000, 268.000, 261.300, 323.700, 0.000, 323.000, 0.000, 0.000, 322.300, 313.400, 287.000, 279.000, 283.800, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 4.000, 0.000, },
},
{"Venezia, Italy", "01:00", ":Europe/Rome", 1.710, "feet",
	{0.013, 0.535, 0.141, 0.021, 0.012, 0.741, 0.000, 0.020, 0.000, 0.000, 0.144, 0.019, 0.164, 0.000, 0.161, 0.032, 0.000, 0.000, 0.000, 0.420, 0.000, 0.000, 0.025, 0.000, 0.000, 0.028, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{93.000, 89.000, 316.000, 316.000, 84.000, 315.000, 0.000, 89.000, 0.000, 0.000, 315.000, 314.000, 79.000, 0.000, 107.000, 73.000, 0.000, 0.000, 0.000, 331.000, 0.000, 0.000, 331.000, 0.000, 0.000, 315.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Vung Tau, Vietnam", "07:00", ":Asia/Saigon", 2.420, "meters",
	{0.036, 0.595, 0.084, 0.022, 0.032, 0.792, 0.000, 0.012, 0.000, 0.000, 0.153, 0.020, 0.452, 0.019, 0.197, 0.087, 0.012, 0.002, 0.000, 0.307, 0.000, 0.000, 0.018, 0.006, 0.019, 0.030, 0.017, 0.000, 0.000, 0.000, 0.027, 0.000, 0.000, 0.000, 0.000, 0.227, 0.000, },
	{336.300, 312.000, 84.600, 60.000, 287.500, 36.000, 0.000, 250.000, 0.000, 0.000, 12.000, -12.000, 263.000, 361.000, 308.300, 240.000, 214.400, 82.800, 0.000, 81.000, 0.000, 0.000, 79.200, 56.900, -9.000, 15.200, 242.000, 0.000, 0.000, 0.000, 359.000, 0.000, 0.000, 0.000, 0.000, 285.000, 0.000, },
},
{"Wei-Hai-Wei, China", "08:00", ":Asia/Shanghai", 3.380, "feet",
	{0.037, 0.722, 0.180, 0.095, 0.030, 1.948, 0.000, 0.085, 0.031, 0.000, 0.309, 0.041, 0.428, 0.018, 0.182, 0.063, 0.011, 0.000, 0.063, 0.587, 0.000, 0.000, 0.035, 0.014, 0.121, 0.173, 0.016, 0.041, 0.032, 0.039, 0.120, 0.070, 0.038, 0.081, 0.064, 0.771, 0.107, },
	{12.000, 309.000, 349.000, 346.000, 283.000, 302.000, 0.000, 195.000, 320.000, 0.000, 276.000, 250.000, 256.000, 2.000, 310.000, 234.000, 204.000, 0.000, 259.000, 358.000, 0.000, 0.000, 357.000, 328.000, 93.000, 285.000, 234.000, 116.000, 56.000, 141.000, 82.000, 233.000, 238.000, 37.000, 337.000, 122.000, 313.000, },
},
{"Xiamen, China", "08:00", ":Asia/Shanghai", 3.730, "meters",
	{0.015, 0.265, 0.111, 0.034, 0.014, 1.867, 0.000, 0.013, 0.000, 0.000, 0.236, 0.031, 0.195, 0.008, 0.088, 0.038, 0.005, 0.003, 0.000, 0.408, 0.000, 0.000, 0.024, 0.013, 0.045, 0.046, 0.007, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
	{291.400, 276.000, 66.200, 391.000, 260.500, 357.000, 0.000, 83.000, 0.000, 0.000, 323.000, 289.000, 245.000, 307.000, 273.700, 230.000, 214.200, 63.600, 0.000, 61.000, 0.000, 0.000, 58.400, 26.700, -67.000, 327.600, 231.700, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, },
},
{"Yekaterininskaya, Russia", "03:00", ":Europe/Moscow", 2.150, "meters",
	{0.002, 0.132, 0.092, 0.036, 0.002, 1.161, 0.000, 0.027, 0.000, 0.000, 0.254, 0.034, 0.025, 0.001, 0.044, 0.025, 0.001, 0.003, 0.000, 0.339, 0.000, 0.000, 0.020, 0.008, 0.028, 0.049, 0.001, 0.000, 0.000, 0.010, 0.024, 0.000, 0.000, 0.000, 0.000, 0.057, 0.000, },
	{223.700, 306.000, 262.800, 244.000, 389.000, 212.000, 0.000, 318.000, 0.000, 0.000, 180.000, 148.000, 112.000, 140.000, 318.500, 57.000, 636.700, 260.900, 0.000, 259.000, 0.000, 0.000, 257.100, 233.800, 165.000, 184.300, 543.200, 0.000, 0.000, 258.000, 337.000, 0.000, 0.000, 0.000, 0.000, 325.000, 0.000, },
},
