#include "Sentence.hpp"

#ifndef __LRF_HPP__
#define __LRF_HPP__
/******************************************************************************
*
* LRF - Long Range Function
*
* $--LRF,x,xxxxxxxxx,c--c,c--c,c--c*hh<CR><LF>
*        | |         |    |    |
*        1 2         3    4    5
*
* 1.  Sequence Number , 0 to 9
* 2.  MMSI of requestor
* 3.  Name of requestor, 1 to 20 character string
* 4.  Function request , 1 to 26 characters
* 5.  Function reply status
*
******************************************************************************/
class CLrf : public CSentence {
protected:
	int   m_nSeqNumber;
	DWORD m_dwMMSIReq;
	char  m_szNameOfRequester[21];
	char  m_szFunctionRequest[27];
	char  m_szReplyStatus[21];

public:
    CLrf();
    CLrf(char *pszSentence);

	void Parse();
	void SetSentence(char *pszSentence);
	int  GetFormat() { return m_nFormat; }
	void GetPlainText(char *pszPlainText);
	int  MakeSentence(BYTE *pszSentence);

	void  SetSeqNumber(int nSeqNum) { m_nSeqNumber = nSeqNum; }
	void  SetMMSIReq(DWORD dwMMSI)  { m_dwMMSIReq = dwMMSI; }
	void  SetNameOfRequester(char *pszName) { strcpy(m_szNameOfRequester, pszName); }
	void  SetFunctionRequest(char *pszFunction) { strcpy(m_szFunctionRequest, pszFunction); }
	void  SetReplyStatus(char *pSzStatus);

	int   GetSeqNumber()    { return m_nSeqNumber; }
	DWORD GetMMSIReq()      { return m_dwMMSIReq; }
    void  GetNameOfRequester(char *pszNameOfRequester) { strcpy(pszNameOfRequester, m_szNameOfRequester); }
	void  GetFunctionRequest(char *pszFunctionRequest) { strcpy(pszFunctionRequest, m_szFunctionRequest); }
	void  GetReplyStatus(char *pSzReplyStatus) { strcpy(pSzReplyStatus, m_szReplyStatus); }
};

#endif
