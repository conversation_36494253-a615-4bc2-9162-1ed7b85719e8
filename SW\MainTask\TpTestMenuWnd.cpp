#include <stdio.h>
#include "TpTestMenuWnd.hpp"
#include "DocMgr.hpp"
#include "WndMgr.hpp"
#include "keybd.hpp"
#include "const.h"
#include "AllConst.h"
#include "Resource.h"
#include "Font.h"

extern CWndMgr *g_pWndMgr;
extern CDocMgr *g_pDocMgr;

CTpTestMenuWnd::CTpTestMenuWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID), m_nSelNum(1)
{
}

void CTpTestMenuWnd::DrawWnd(BOOL bRedraw /*TRUE*/)
{
	CWnd::DrawWnd(bRedraw);

	int nLangMode = g_pDocMgr->GetLangMode();

	DrawSubMenu(m_nSelNum);
	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);

	EraseButton(1);
	EraseButton(2);
	EraseButton(3);
}

void CTpTestMenuWnd::DrawSubMenu(int nSelNum)
{
	int nLangMode = g_pDocMgr->GetLangMode();
	int i = 0;
	FONT *pFont = NULL;
	FONT *pOldFont = NULL;
	int nFontH = 0;
	int nStrW = 0;
	int nXPos = 0, nYPos= 0, nYOffset = 0;
	UCHAR *pArrBmp = NULL;
	HWORD *pUniCodeStr = NULL;
	COLORT clrTxt;
	COLORT clrUpLine;
	COLORT clrDnLine;

	if(m_pScreen != NULL)
	{
		// Draw Menu
		switch(nLangMode)
		{
			case LANG_KOR:
			case LANG_CHI:
				pFont = &NewGulLim18bCJK;
				nYOffset = 2;
				break;

			case LANG_RUS:
				pFont = &MyriadPro30bRus;
				nYOffset = 2;
				break;	

			default:
				pFont = &MyriadPro30bEng;
				nYOffset = 5;
				break;
		}
		pOldFont = m_pScreen->SetFont(pFont);
		nFontH = pFont->uHeight;

		for(i = 0; i < MAX_MENU_BAR; i++)
		{
			if(m_nScheme == CS_DAY_BRIGHT)
			{
				clrUpLine = MNU_DAY_UP_LINE;
				clrDnLine = MNU_DAY_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrDayOn;
					clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
				}
				else
				{
					clrTxt = COLORSCHEME[m_nScheme].crFore;
					pArrBmp = G_BmpArrDayOff;
				}
			}
			else
			{
				clrUpLine = MNU_NIGHT_UP_LINE;
				clrDnLine = MNU_NIGHT_DN_LINE;

				if(nSelNum == (i+1))
				{
					pArrBmp = G_BmpArrNightOn;
					clrTxt = COLORSCHEME[m_nScheme].crButtonCaption;
				}
				else
				{
					clrTxt = COLORSCHEME[m_nScheme].crFore;
					pArrBmp = G_BmpArrNightOff;
				}
			}
			
			if(nSelNum == (i+1))
			{
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crLetterIcon);	
				
			}
			else
			{
									
				m_pScreen->FillRect(WND_BACK_X_POS,
									WND_BACK_Y_POS + i*MNU_BAR_H,
									WND_BACK_X_POS + MNU_BAR_W -1,
									WND_BACK_Y_POS + (i+1)*MNU_BAR_H -1,
									COLORSCHEME[m_nScheme].crBack);	
			}

			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H,
							clrUpLine);
			
			m_pScreen->Line(WND_BACK_X_POS,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							WND_BACK_X_POS + MNU_BAR_W -1,
							WND_BACK_Y_POS + i*MNU_BAR_H +1,
							clrDnLine);

			nXPos = BASE_WND_AREA_X_POS + 5;
			nYPos = WND_BACK_Y_POS + i*MNU_BAR_H + (MENU_ITEM_H - nFontH)/2 + nYOffset;

			switch(i)
			{
				case 0:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_TP_TRANSCEIVER_TEST[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_TP_TRANSCEIVER_TEST[nLangMode];
					break;

				case 1:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_TP_RECEIVER_TEST[nLangMode],nLangMode);	
					pUniCodeStr = (HWORD *)MNU_TP_RECEIVER_TEST[nLangMode];
					break;

				case 2:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_TP_SET_PARAMETER[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_TP_VDL_COMM_TEST[nLangMode];
					break;
					
				case 3:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_TP_SET_PARAMETER[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_SELF_TEST[nLangMode];
					break;

				case 4:
					pUniCodeStr = (HWORD *)MNU_SET_PCB_VER[nLangMode];
					break;
					
				case 5:
					//pUniCodeStr = ConvertCharStrToUniStr((CHAR *)MNU_SYSTEM_INITIALIZE[nLangMode],nLangMode);
					pUniCodeStr = (HWORD *)MNU_SYSTEM_INITIALIZE[nLangMode];
					break;
			}

			switch(i)
			{
				case 0:
				case 1:
				case 2:
				case 3:
				case 4:
				case 5:					
					nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
					m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,clrTxt);	

					m_pScreen->DrawBitMap(	m_nScrXSize,
											m_nScrYSize,
											MNU_BAR_ARR_X_POS,
											WND_BACK_Y_POS + i*MNU_BAR_H + (MNU_BAR_H - 23)/2,
											pArrBmp,
											CLR_BIT_MAP_TRANS,
											0);
					break;
			}
		}

		m_pScreen->SetFont(pOldFont);
	}
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CTpTestMenuWnd::OnKeyEvent(int nKey, DWORD nFlags)
{

	switch( nKey )
	{
		case KBD_SCAN_CODE_UP:
		case KBD_SCAN_CODE_LEFT:
			if(m_nSelNum <= 1)
			{
				m_nSelNum = 6;
			}
			else
			{
				m_nSelNum--;
			}
			DrawSubMenu(m_nSelNum);
			break;
		
		case KBD_SCAN_CODE_DOWN:
		case KBD_SCAN_CODE_RIGHT:
			if(m_nSelNum >= 6)
			{
				m_nSelNum = 1;
			}
			else
			{
				m_nSelNum++;
			}
			DrawSubMenu(m_nSelNum);
			break;
		
		default:
			break;
	}
}
