/*...........................................................................*/
/*.                  File Name : SamNavConst.h                              .*/
/*.                                                                         .*/
/*.                       Date : 2010.12.21                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __SamNavConst_H__
#define  __SamNavConst_H__

//=============================================================================
#define  NVS_DPT_UNIT_FT                        0   // feet
#define  NVS_DPT_UNIT_M                         1   // meter
#define  NVS_DPT_UNIT_FA                        2   // fathom
//-----------------------------------------------------------------------------
#define  NVS_PAL_MODE_NORMAL                    0
#define  NVS_PAL_MODE_NOAA                      1
#define  NVS_PAL_MODE_SUNLIGHT                  2
#define  NVS_PAL_MODE_NIGHT                     3
//-----------------------------------------------------------------------------
#define  NVS_TXT_ICN_SIZE_SMALL                 0
#define  NVS_TXT_ICN_SIZE_LARGE                 1
//-----------------------------------------------------------------------------
#define  NVS_NAV_AIDS_LIGHT_MODE_OFF           0
#define  NVS_NAV_AIDS_LIGHT_MODE_NO_SECTOR     1
#define  NVS_NAV_AIDS_LIGHT_MODE_ON            2
//-----------------------------------------------------------------------------
#define  NVS_NAV_AIDS_SYM_MODE_INT              0
#define  NVS_NAV_AIDS_SYM_MODE_US               1
//-----------------------------------------------------------------------------
#define  NAV_DRAW_STATUS_CONT                   0
#define  NAV_DRAW_STATUS_ENDED                  1
#define  NAV_DRAW_STATUS_ERROR                  2
//-----------------------------------------------------------------------------
#define  TIDHGH        242 // marine object for Tide Station

//-----------------------------------------------------------------------------
#define  P_HRBM        244 // HARBOUR MASTER
#define  P_COAG        245 // COAST GUARD
#define  P_POLI        246 // POLICE
#define  P_CSTM        247 // CUSTOMS/CUSTOM POLICE
#define  P_EMRG        248 // HEALTH EMERGENCY
#define  P_PSTO        249 // POST OFFICE
#define  P_YCCL        250 // YACHT CLUB
#define  P_BTYR        251 // BOAT YARD
#define  P_ACCE        252 // ACCESSORIES/MARINE ELECTRONICS
#define  P_ELER        253 // ELECTRICAL/ELECTRONIC REPAIRS
#define  P_ENGR        254 // ENGINE REPAIRS
#define  P_SAIL        255 // SAILMAKER
#define  P_DIVI        256 // FISHING/DIVING GEAR  SCUBA RECHARGE
#define  P_HOTE        257 // HOTEL/INN
#define  P_REST        258 // RESTAURANT
#define  P_BANK        259 // BANK/EXCHANGE OFFICE
#define  P_PHAR        260 // PHARMACY
#define  P_PORT        261 // PORT/MARINA
#define  P_SLIP        263 // SLIPWAY
#define  P_BHST        264 // BOAT HOIST
#define  P_CRNE        265 // CRANE
#define  P_FUEL        266 // FUEL STATION
#define  P_WATR        267 // WATER
#define  P_ELEC        268 // ELECTRICITY
#define  P_SHWR        269 // SHOWERS
#define  P_LNDR        270 // LAUNDRETTE
#define  P_TOIL        271 // PUBLIC TOILETS
#define  P_PSTB        272 // POST BOX
#define  P_TELE        273 // PUBLIC TELEPHONE
#define  P_RFSB        274 // REFUSE BIN
#define  P_CHAN        276 // CHANDLER
#define  P_PROV        277 // PROVISIONS
#define  P_BGAS        278 // BOTTLE GAS
#define  P_CRPR        279 // CAR PARKING
#define  P_BTPR        280 // PARKING FOR BOAT AND TRAILERS
#define  P_SEWE        283 // SEWERAGE PUMP-OUT STATION
#define  P_GRAP        284 // PUBLIC TELEGRAPH
#define  P_RDIO        285 // PUBLIC RADIO
#define  P_RTEL        286 // PUBLIC RADIO TELEGRAPH
#define  P_TRNS        287 // TRANSPORTATION SERVICES
#define  P_REPR        289 // OTHER MARINE REPAIRS
#define  P_TOWB        294 // TOWING SERVICES
#define  P_PART        295 // PORT PARTNER
#define  P_TACK        296 // BAIT/TACKLE SHOP
#define  P_HULR        392 // HULL REPAIRS
#define  P_BREN        394 // BOAT RENTAL
#define  P_CREN        395 // CAR/MOTOR BIKE RENTAL
#define  P_OFFC        396 // OFFICE SERVICE
#define  P_INTN        397 // INTERNET POINT
#define  P_CHRT        398 // CHARTER
#define  P_NEWS        399 // NEWSSTAND
#define  P_DIVS        403 // DIVERS
#define  P_MORS        404 // MOORING SERVICE
#define  P_PRDV        405 // PROVISION DELIVERY
#define  P_BARP        406 // BAR
#define  P_LBVS        409 // LAND BASED VESSEL STORAGE LOCATION
//=============================================================================


//-----------------------------------------------------------------------------
#define	 PHOTO			500 //Navionics panoramic photo
//=============================================================================

#define	NVS_NAME_LENGTH					32
#define	NVS_CATEGORY_NAME_LENGTH		64
#define	NVS_URL_LENGTH					1024
#define	NVS_DESCR_LENGTH				1024

typedef struct {
     char  vChartName[NVS_CATEGORY_NAME_LENGTH];
     char  vChartEditionDate[NVS_NAME_LENGTH];
     char  vChartEditionNumber[NVS_NAME_LENGTH];
} xCHARTFILEINFO;

typedef struct {
     char  vPortName[NVS_NAME_LENGTH];
     int   nMercX;
     int   nMercY;
     float rDistant;
} xPORTNAME;

typedef struct {
     char  vObjectName[NVS_NAME_LENGTH];
     int    nMercX;
     int    nMercY;
     int   nType;
     char vURL[NVS_URL_LENGTH]; 
} xOBJECTINFO;


 struct xOBJECTDETAIL
 {
     char  vObjectName[NVS_NAME_LENGTH];
     int    nMercX;
     int    nMercY;
     int   nType;
	 char vTxtDescription[NVS_DESCR_LENGTH]; 
	 int  nChildNum;
	 xOBJECTDETAIL*	nChild;
	 xOBJECTDETAIL*	nNextInLevel;

	 xOBJECTDETAIL():
	nChildNum(0),
	nChild(0),
	nNextInLevel(0),
	nType(0),
	nMercX(0),
	nMercY(0)
	 {
	 }

	~xOBJECTDETAIL()
	{

	}
} ;


//NOTE: The image format is the same returned by NavDisplay::Draw()
typedef struct 
{
	void*	pBuffer;
	int		mWidth;
	int		mHeight;
	int		mBitPerPixel;
	int		mSizeInByte;
}xBITMAP;


typedef struct { 
	char		vCategoryName[NVS_CATEGORY_NAME_LENGTH]; 
	xBITMAP		nIcon; 
} xCATEGORYLABEL; 


//=============================================================================



#endif

