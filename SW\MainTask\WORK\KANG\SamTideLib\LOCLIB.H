#ifndef _TIDELIB_LOCLIB_H_
#define _TIDELIB_LOCLIB_H_

#ifdef  __cplusplus
extern "C" {
#endif

#include "nvtypes.h"

//BOOL CALLBACK LocationDlg(HWND hwnd,UINT msg,WPARAM wParam,LPARAM lParam);

#define	COORD_MULTIPLIER		3600000		//ISKANG

#ifdef WIN32_ORG
typedef struct {
   void     *IDX_next;                      // Points to next linked item
   short int IDX_rec_num;                   // Keeps track of multiple entries w/same name
   char      IDX_type;                      // Entry "TCtcIUu" identifier
   char      IDX_zone[40];                  // Alpha region/country/state ID
   char      IDX_station_name[MAXNAMELEN];  // Name of station
   double    IDX_lon;                       // Longitude (+East)
   double    IDX_lat;                       // Latitude (+North)
   short int IDX_meridian;                  // Minutes offset from UTC
   short int IDX_ht_time_off;               // High tide offset in minutes
   float     IDX_ht_mpy;                    // High tide multiplier (nom 1.0)
   float     IDX_ht_off;                    // High tide level offset (feet?)
   short int IDX_lt_time_off;               // Low tide offset in minutes
   float     IDX_lt_mpy;                    // Low tide multiplier (nom 1.0)
   float     IDX_lt_off;                    // Low tide level offset (feet?)
   short int IDX_sta_num;                   // Subordinate station number
   char     *IDX_tzname;                    // Timezone name
   short int IDX_ref_file_num;              // # of reference file where reference station is
   char      IDX_reference_name[MAXNAMELEN];// Name of reference station
   char      IDX_istcd;                     // Entry is TCD station or User station based on TCD
   long      IDX_lonMul;					// ISKANG, IDX_lon * COORD_MULTIPLIER
   long      IDX_latMul;					// ISKANG, IDX_lat * COORD_MULTIPLIER
} IDX_entry;
#else
typedef struct {
   short int IDX_rec_num;                   // Keeps track of multiple entries w/same name
   char      IDX_type;                      // Entry "TCtcIUu" identifier
   char      IDX_zone[40];                  // Alpha region/country/state ID
   char      IDX_station_name[MAXNAMELEN];  // Name of station
   double    IDX_lon;                       // Longitude (+East)
   double    IDX_lat;                       // Latitude (+North)
   short int IDX_meridian;                  // Minutes offset from UTC
   short int IDX_ht_time_off;               // High tide offset in minutes
   float     IDX_ht_mpy;                    // High tide multiplier (nom 1.0)
   float     IDX_ht_off;                    // High tide level offset (feet?)
   short int IDX_lt_time_off;               // Low tide offset in minutes
   float     IDX_lt_mpy;                    // Low tide multiplier (nom 1.0)
   float     IDX_lt_off;                    // Low tide level offset (feet?)
   short int IDX_sta_num;                   // Subordinate station number
   char      IDX_tzname[MAXNAMELEN];        // Timezone name
   short int IDX_ref_file_num;              // # of reference file where reference station is
   char      IDX_reference_name[MAXNAMELEN];// Name of reference station
   char      IDX_istcd;                     // Entry is TCD station or User station based on TCD
   long      IDX_lonMul;					// ISKANG, IDX_lon * COORD_MULTIPLIER
   long      IDX_latMul;					// ISKANG, IDX_lat * COORD_MULTIPLIER
} IDX_entry;
#endif

//char *makeLonLatStr(double lon, double lat, int use3);
int  init_index_file(int load_index, HWND hwnd);
int  load_location_info(char *station_name, int rec_num);
int  load_location_data(char *station_name, int rec_num);
int  load_nearest_station(double ILat, double ILon, int radius);
//void list_stations();
void free_abbreviation_list();
void free_station_index();
void LoadRegionCountryState(HWND, int, int, int, int, int, float, float, int, char *);
char *build_test_ID(HWND, int, int, int);
int cvt_DMM_2_deg(double *dst, char *str);
void cvt_deg_2_DMM(char *str, double pos);
char TestStationExist(char *str);

#ifdef  __cplusplus
}
#endif

#endif
