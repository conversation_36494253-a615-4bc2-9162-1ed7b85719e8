#include <stdio.h>
#include <math.h>
#include "ACA.hpp"
#include "Uart.hpp"
extern cUART *G_pUart3;

CAca::CAca()
	: CSentence()
	, m_nSeqNumber(-1)
	, m_chInfoSource('M')
{
}
    
CAca::CAca(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CAca::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_ACA;
}

/******************************************************************************
 *
 * ACA - AIS Regional Channel Assignment Message
 *                                                                      16  18
 *                                                                      |   |
 * $--ACA,x,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a,x,xxxx,x,xxxx,x,x,x,a,x,hhmmss.ss*hh<CR><LF>
 *        |    |    |    |     |    |    |    |     | |  |   |  |   | |   |      |
 *        1    2    3    4     5    6    7    8     9 10 11  12 13 14 15  17     19
 *
 * 1.  Sequence Number, 0 to 9
 * 2.  Region Northeast corner latitude
 * 3.  N/S - 'N' or 'S'
 * 4.  Region Northeast corner longitude
 * 5.  E/W - 'E' or 'W'
 * 6.  Region Southwest corner latitude
 * 7.  N/S - 'N' or 'S'
 * 8.  Region Southwest corner longitude
 * 9.  E/W - 'E' or 'W'
 * 10. Transition Zone Size
 * 11. Channel A
 * 12. Channel A bandwidth
 * 13. Channel B
 * 14. Channel B bandwidth
 * 15. Tx/Rx mode control
 * 16. Power level control
 * 17. Information source
 * 18. In-Use Flag
 * 19. Time of "in-use" change
 *
 ******************************************************************************/
void CAca::Parse()
{
	//char szTemp[12], szTime[12];

	GetTalkerID(m_szTalkID);  // Add talkerId check : HSI 2012.05.07

	m_nSeqNumber   = GetFieldInteger(1);
	m_nLatNE       = GetFieldLat(2);
	m_nLonNE       = GetFieldLon(4);
	m_nLatSW       = GetFieldLat(6);
	m_nLonSW       = GetFieldLon(8);
	m_nZoneSize    = GetFieldInteger(10);
	m_nChannelA    = GetFieldInteger(11);
	m_nBandwidthA  = GetFieldInteger(12);
	m_nChannelB    = GetFieldInteger(13);
	m_nBandwidthB  = GetFieldInteger(14);
	m_nTxRxMode    = GetFieldInteger(15);
	m_nPowerLevel  = GetFieldInteger(16);
	m_chInfoSource = GetFieldChar(17);
	m_nInUse       = GetFieldInteger(18);
	
	GetFieldString(19, m_szInUseUTC); // Modify in use time : HSI 2012.05.07

#ifdef _EN_DBG_MSG_
	G_pUart3->OutputDbgMsg("[ACA PARSER]Talker=%s\r\n",m_szTalkID);
	G_pUart3->OutputDbgMsg("[ACA PARSER]Seq=%d, LatNE=%d, LonNE=%d, LatSW=%d, LonSW=%d\r\n",m_nSeqNumber,m_nLatNE,m_nLonNE,m_nLatSW,m_nLonSW);
	G_pUart3->OutputDbgMsg("[ACA PARSER]ZoneSize=%d, ChannelA=%d, BandwidthA=%d\r\n",m_nZoneSize,m_nChannelA,m_nBandwidthA);
	G_pUart3->OutputDbgMsg("[ACA PARSER]ChannelB=%d, BandwidthB=%d\r\n",m_nChannelB,m_nBandwidthB);
	G_pUart3->OutputDbgMsg("[ACA PARSER]TxRxMode=%d, PowerLvl=%d\r\n",m_nTxRxMode,m_nPowerLevel);
	G_pUart3->OutputDbgMsg("[ACA PARSER]CHInfoSrc=%c, InUse=%d, InUseUTC=%s\r\n",m_chInfoSource,m_nInUse,m_szInUseUTC);
#endif

	/*
	if( szTime[0] != '\0' && strlen(szTime) >= 6 )
	{
		strncpy(szTemp, szTime, 2);
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCHour);
		m_nUTCHour = atoi(szTemp);

		strncpy(szTemp, szTime+2, 2);
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCMin);
		m_nUTCMin = atoi(szTemp);

		strncpy(szTemp, szTime+4, 2);
		szTemp[2] = '\0';
// 		sscanf(szTemp, "%d", &m_nUTCSec);
		m_nUTCSec = atoi(szTemp);
	}
	else
	{
		m_nUTCHour = UTC_HOUR_NULL_VALUE;
		m_nUTCMin  = UTC_MINUTE_NULL_VALUE;
		m_nUTCSec  = UTC_SECOND_NULL_VALUE;
	}
	*/
}

void CAca::GetPlainText(char *pszPlainText)
{
	pszPlainText[0] = '\0';
}

// $--ACA,x,llll.ll,a,yyyyy.yy,a,llll.ll,a,yyyyy.yy,a,x,xxxx,x,xxxx,x,x,x,a,x,hhmmss.ss*hh<CR><LF>
int CAca::MakeSentence(BYTE *pszSentence)
{
	BYTE szLatNE[12], szLonNE[12];
	BYTE szLatSW[12], szLonSW[12];
	int   nDeg;
	float fltMin;
	char  nDir;
	float fltDeg;

	fltDeg = static_cast<float>(m_nLatNE) / static_cast<float>(DEG_MUL_FACTOR);
	nDir   = (m_nLatNE >= 0) ? 'N' : 'S';
	fltDeg = fabs(fltDeg);
	nDeg   = static_cast<int>(fltDeg);
	fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
	sprintf((char *)szLatNE, "%02d%04.1f,%c", nDeg, fltMin, nDir);

	fltDeg = static_cast<float>(m_nLonNE) / static_cast<float>(DEG_MUL_FACTOR);
	nDir   = (m_nLonNE >= 0) ? 'E' : 'W';
	fltDeg = fabs(fltDeg);
	nDeg   = static_cast<int>(fltDeg);
	fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
	sprintf((char *)szLonNE, "%03d%04.1f,%c", nDeg, fltMin, nDir);

	fltDeg = static_cast<float>(m_nLatSW) / static_cast<float>(DEG_MUL_FACTOR);
	nDir   = (m_nLatSW >= 0) ? 'N' : 'S';
	fltDeg = fabs(fltDeg);
	nDeg   = static_cast<int>(fltDeg);
	fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
	sprintf((char *)szLatSW, "%02d%04.1f,%c", nDeg, fltMin, nDir);

	fltDeg = static_cast<float>(m_nLonSW) / static_cast<float>(DEG_MUL_FACTOR);
	nDir   = (m_nLonSW >= 0) ? 'E' : 'W';
	fltDeg = fabs(fltDeg);
	nDeg   = static_cast<int>(fltDeg);
	fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
	sprintf((char *)szLonSW, "%03d%04.1f,%c", nDeg, fltMin, nDir);

	if( m_nSeqNumber < 0 ) {
		sprintf((char *)pszSentence, "$AIACA,,%s,%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,%c,,",
			szLatNE, szLonNE, szLatSW, szLonSW, m_nZoneSize,
			m_nChannelA, m_nBandwidthA, m_nChannelB, m_nBandwidthB,
			m_nTxRxMode, m_nPowerLevel, m_chInfoSource);
	} else {
		sprintf((char *)pszSentence, "$AIACA,%d,%s,%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,%c,,",
			m_nSeqNumber, szLatNE, szLonNE, szLatSW, szLonSW, m_nZoneSize,
			m_nChannelA, m_nBandwidthA, m_nChannelB, m_nBandwidthB,
			m_nTxRxMode, m_nPowerLevel, m_chInfoSource);
	}
	/*} else {
		sprintf(pszSentence, "$AIACA,%d,%s,%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,C,,",
			m_nSeqNumber, szLatNE, szLonNE, szLatSW, szLonSW, m_nZoneSize,
			m_nChannelA, m_nBandwidthA, m_nChannelB, m_nBandwidthB,
			m_nTxRxMode, m_nPowerLevel);
	}*/

	SendMakeNmeaCsData(pszSentence);
	//G_pUart3->OutputDbgMsg("[CAca::MakeSentence]Sent=%s\r\n",pszSentence);
	return strlen((char *)pszSentence);
}

int CAca::MakeSamSentence(BYTE *pszSentence, int nRegionNum, char chInfosrc, int nInuse)
{
	BYTE szLatNE[12], szLonNE[12];
	BYTE szLatSW[12], szLonSW[12];
	int   nDeg;
	float fltMin;
	char  nDir;
	float fltDeg;

	if(m_nLatNE == LAT_NULL_VALUE)
	{
		memset(szLatNE,0x00,sizeof(BYTE)*12);
		sprintf((char *)szLatNE, ",");
	}
	else
	{
		fltDeg = static_cast<float>(m_nLatNE) / static_cast<float>(DEG_MUL_FACTOR);
		nDir   = (m_nLatNE >= 0) ? 'N' : 'S';
		fltDeg = fabs(fltDeg);
		nDeg   = static_cast<int>(fltDeg);
		fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
		sprintf((char *)szLatNE, "%02d%04.1f,%c", nDeg, fltMin, nDir);
	}

	if(m_nLonNE == LON_NULL_VALUE)
	{
		memset(szLonNE,0x00,sizeof(BYTE)*12);
		sprintf((char *)szLonNE, ",");
	}
	else
	{
		fltDeg = static_cast<float>(m_nLonNE) / static_cast<float>(DEG_MUL_FACTOR);
		nDir   = (m_nLonNE >= 0) ? 'E' : 'W';
		fltDeg = fabs(fltDeg);
		nDeg   = static_cast<int>(fltDeg);
		fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
		sprintf((char *)szLonNE, "%03d%04.1f,%c", nDeg, fltMin, nDir);
	}

	if(m_nLatSW == LAT_NULL_VALUE)
	{
		memset(szLatSW,0x00,sizeof(BYTE)*12);
		sprintf((char *)szLatSW, ",");
	}
	else
	{
		fltDeg = static_cast<float>(m_nLatSW) / static_cast<float>(DEG_MUL_FACTOR);
		nDir   = (m_nLatSW >= 0) ? 'N' : 'S';
		fltDeg = fabs(fltDeg);
		nDeg   = static_cast<int>(fltDeg);
		fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
		sprintf((char *)szLatSW, "%02d%04.1f,%c", nDeg, fltMin, nDir);
	}

	if(m_nLonSW == LON_NULL_VALUE)
	{
		memset(szLonSW,0x00,sizeof(BYTE)*12);
		sprintf((char *)szLonSW, ",");
	}
	else
	{
		fltDeg = static_cast<float>(m_nLonSW) / static_cast<float>(DEG_MUL_FACTOR);
		nDir   = (m_nLonSW >= 0) ? 'E' : 'W';
		fltDeg = fabs(fltDeg);
		nDeg   = static_cast<int>(fltDeg);
		fltMin = (fltDeg - static_cast<float>(nDeg)) * 60.;
		sprintf((char *)szLonSW, "%03d%04.1f,%c", nDeg, fltMin, nDir);
	}

	if( m_nSeqNumber < 0 ) {
		sprintf((char *)pszSentence, "$S%dACA,,%s,%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,%c,%d,",
			8,szLatNE, szLonNE, szLatSW, szLonSW, m_nZoneSize,
			m_nChannelA, m_nBandwidthA, m_nChannelB, m_nBandwidthB,
			m_nTxRxMode, m_nPowerLevel, chInfosrc,nInuse);
	} else {
		sprintf((char *)pszSentence, "$S%dACA,%d,%s,%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,%c,%d,",
			nRegionNum,
			m_nSeqNumber, szLatNE, szLonNE, szLatSW, szLonSW, m_nZoneSize,
			m_nChannelA, m_nBandwidthA, m_nChannelB, m_nBandwidthB,
			m_nTxRxMode, m_nPowerLevel, chInfosrc,nInuse);
	}
	/*} else {
		sprintf(pszSentence, "$AIACA,%d,%s,%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,C,,",
			m_nSeqNumber, szLatNE, szLonNE, szLatSW, szLonSW, m_nZoneSize,
			m_nChannelA, m_nBandwidthA, m_nChannelB, m_nBandwidthB,
			m_nTxRxMode, m_nPowerLevel);
	}*/

	SendMakeNmeaCsData(pszSentence);

	//G_pUart3->OutputDbgMsg("[CAca::MakeSamSentence]Power Lvl=%d, Sent=%s\r\n",m_nPowerLevel,pszSentence);
	return strlen((char *)pszSentence);
}
