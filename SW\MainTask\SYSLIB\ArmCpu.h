/*...........................................................................*/
/*.                  File Name : ARMCPU.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef __ARMCPU_H__
#define __ARMCPU_H__

//=============================================================================
#define  ARM_R1_FA                           (1 << 29)     // ARM11 only
#define  ARM_R1_TR                           (1 << 28)     // ARM11 only
#define  ARM_R1_EE                           (1 << 25)     // ARM11 only
#define  ARM_R1_VE                           (1 << 24)     // ARM11 only
#define  ARM_R1_XP                           (1 << 23)     // ARM11 only
#define  ARM_R1_U                            (1 << 22)     // ARM11 only
#define  ARM_R1_FI                           (1 << 21)     // ARM11 only
#define  ARM_R1_IT                           (1 << 18)     // ARM11 only
#define  ARM_R1_DT                           (1 << 16)     // ARM11 only
#define  ARM_R1_L4                           (1 << 15)
#define  ARM_R1_RR                           (1 << 14)
#define  ARM_R1_V                            (1 << 13)
#define  ARM_R1_I                            (1 << 12)
#define  ARM_R1_Z                            (1 << 11)     // ARM11 only
#define  ARM_R1_R                            (1 <<  9)
#define  ARM_R1_S                            (1 <<  8)
#define  ARM_R1_B                            (1 <<  7)
#define  ARM_R1_C                            (1 <<  2)
#define  ARM_R1_A                            (1 <<  1)
#define  ARM_R1_M                                  (1)
#define  ARM_R1_iA                           (1 << 31)     // Not used in ARM926/ARM11
#define  ARM_R1_nF                           (1 << 30)     // Not used in ARM926/ARM11
//-----------------------------------------------------------------------------
#define  ARM_USR_MODE     	                     0x10
#define  ARM_FIQ_MODE      	                     0x11
#define  ARM_IRQ_MODE      	                     0x12
#define  ARM_SVC_MODE      	                     0x13
#define  ARM_ABT_MODE          	                 0x17
#define  ARM_UND_MODE    	                       0x1b
#define  ARM_SYS_MODE     	                     0x1f
#define  ARM_MODE_MASK     	                     0x1f
#define  ARM_IRQ_DISABLE                         0x80
#define  ARM_FIQ_DISABLE                         0x40
#define  ARM_ALL_INT_DISABLE     (ARM_IRQ_DISABLE | ARM_FIQ_DISABLE)
//-----------------------------------------------------------------------------
#define  ARM_DESC_SECTION         (0x2 | (0 <<  4))
#define  ARM_CACHE_CB		                 (3 <<  2)  // cache_on, write_back
#define  ARM_CACHE_CNB		               (2 <<  2)  // cache_on, write_through
#define  ARM_CACHE_NCB                   (1 <<  2)  // cache_off,WR_BUF on
#define  ARM_CACHE_NCNB	                 (0 <<  2)  // cache_off,WR_BUF off
#define  ARM_AP_RW		                   (3 << 10)  // supervisor=RW, user=RW
#define  ARM_AP_RO		                   (2 << 10)  // supervisor=RW, user=RO
//-----------------------------------------------------------------------------
#define  ARM_DOMAIN_FAULT	                  (0x00)
#define  ARM_DOMAIN_CHECK                   (0x01)
#define  ARM_DOMAIN_NOT_CHECK	              (0x03)
#define  ARM_DOMAIN0		               (0x0 <<  5)
#define  ARM_DOMAIN1		               (0x1 <<  5)
//-----------------------------------------------------------------------------
#define  ARM_DOMAIN0_ATTR  (ARM_DOMAIN_CHECK << 0)
#define  ARM_DOMAIN1_ATTR  (ARM_DOMAIN_FAULT << 2)
//-----------------------------------------------------------------------------
#define  ARM_RW_CB	       (ARM_AP_RW | ARM_DOMAIN0 | ARM_CACHE_CB  | ARM_DESC_SECTION)
#define  ARM_RW_CNB		     (ARM_AP_RW | ARM_DOMAIN0 | ARM_CACHE_CNB | ARM_DESC_SECTION)
#define  ARM_RW_NCNB	     (ARM_AP_RW | ARM_DOMAIN0 | ARM_CACHE_NCNB| ARM_DESC_SECTION)
#define  ARM_RW_FAULT	     (ARM_AP_RW | ARM_DOMAIN1 | ARM_CACHE_NCNB| ARM_DESC_SECTION)
//-----------------------------------------------------------------------------
//=============================================================================

#endif


