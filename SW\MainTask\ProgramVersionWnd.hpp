#ifndef __PROGRAM_VERSION_WND_HPP__
#define __PROGRAM_VERSION_WND_HPP__
#include "Wnd.hpp"

class CProgramVersionWnd : public CWnd {
protected:

public:
	CProgramVersionWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	void OnKeyEvent(int nKey, DWORD nFlags);
	void DrawFuncBtn();
	void DrawTransMainVerTbl();
	void DrawTransMainVer();
	void DrawTransSubVerTbl();
	void DrawTransSubVer();
	void DrawMKDVerTbl();
	void DrawMKDVer();
	void DrawMapVerTbl();
	void DrawMapVer();
	void DrawVersions();
	void DrawWnd(BOOL bRedraw = TRUE);
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);
};

#endif


