/*...........................................................................*/
/*.                  File Name : SENDPCX.HPP                                .*/
/*.                                                                         .*/
/*.                       Date : 2004.10.18                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "screen.hpp"
#include "keybd.hpp"
#include "uart.hpp"
#include "time.hpp"

#ifndef  __SENDPCX_HPP
#define  __SENDPCX_HPP

//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------

class cSENDPCX
{
   private:
      cSCREEN *m_pScreen;
      cKEYBD  *m_pKeyBD;
      cUART   *m_pUart;
   public:

   public:
      cSENDPCX(cSCREEN *pScreen,cKEYBD *pKeyBD,cUART *pUart);
      virtual ~cSENDPCX(void);
   public:
      int   SendPcxData(BYTE *pPcxData,DWORD dPcxSize);
      int   SendOneRecord(BYTE *pCmd,DWORD dFrameNo,WORD wSendSize,BYTE *pSendData);
      void  SetEndianData(BYTE *pTarget,void *pSource,int nSize);
      void  GetEndianData(void *pTarget,BYTE *pSource,int nSize);
};

#endif

