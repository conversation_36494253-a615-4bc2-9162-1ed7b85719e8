#include <stdio.h>
#include "ProgramVersionWnd.hpp"
#include "TargetPlotWnd.hpp"
#include "DocMgr.hpp"
#include "WndMgr.hpp"
#include "keybd.hpp"
#include "buzzer.hpp"
#include "const.h"
#include "SamMapConst.h"
#include "Comlib.h"
#include "Font.h"
#include "_AllStringY.hpp"

#define TM_VER_TBL_X				5
#define TM_VER_TBL_Y				(TITLE_BG_Y_POS+TITLE_BG_H + 3)				
#define TM_VER_TBL_W				588
#define TM_VER_TBL_H				108
#define TM_VER_TBL_ROW_H			27
#define TM_VER_TBL_1ST_COLW		    200

#define TS_VER_TBL_X				TM_VER_TBL_X
#define TS_VER_TBL_Y				(TM_VER_TBL_Y + TM_VER_TBL_H + 10)
#define TS_VER_TBL_W				TM_VER_TBL_W
#define TS_VER_TBL_H				TM_VER_TBL_H
#define TS_VER_TBL_ROW_H			TM_VER_TBL_ROW_H
#define TS_VER_TBL_1ST_COLW		    TM_VER_TBL_1ST_COLW

#define MK_VER_TBL_X				TM_VER_TBL_X
#define MK_VER_TBL_Y				(TS_VER_TBL_Y + TS_VER_TBL_H + 10)
#define MK_VER_TBL_W				TM_VER_TBL_W
#define MK_VER_TBL_H				81
#define MK_VER_TBL_ROW_H			TM_VER_TBL_ROW_H
#define MK_VER_TBL_1ST_COLW		    TM_VER_TBL_1ST_COLW

#define MP_VER_TBL_X				TM_VER_TBL_X
#define MP_VER_TBL_Y				(MK_VER_TBL_Y + MK_VER_TBL_H + 10)
#define MP_VER_TBL_W				TM_VER_TBL_W
#define MP_VER_TBL_H				54
#define MP_VER_TBL_ROW_H			TM_VER_TBL_ROW_H
#define MP_VER_TBL_1ST_COLW		    TM_VER_TBL_1ST_COLW

extern CDocMgr *g_pDocMgr;
extern CWndMgr *g_pWndMgr;

/*********************************************************************************************************/
// Name		: CProgramVersionWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CProgramVersionWnd::CProgramVersionWnd(cSCREEN *pScreen, const BYTE **pCaption, DWORD dWndID)
	: CWnd(pScreen, pCaption, dWndID)
{
	m_nFocus = 0;
}

/*********************************************************************************************************/
// Name		: DrawFuncBtn
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawFuncBtn()
{
	int nLangMode = g_pDocMgr->GetLangMode();
	
	DrawButton(0, (BYTE *)FK_PREV[nLangMode]);
	EraseButton(1);
	EraseButton(2);
	EraseButton(3);
}

/*********************************************************************************************************/
// Name		: DrawTransMainVerTbl
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawTransMainVerTbl()
{
#if 1
	int nX1, nX2;
	int nY1, nY2;

	// Draw Outer Line
	nX1 = TM_VER_TBL_X;
	nY1 = TM_VER_TBL_Y;
	
	nX2 = nX1 + TM_VER_TBL_W -1;
	nY2 = nY1 + TM_VER_TBL_ROW_H*2 -1;
	m_pScreen->Rect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Title Background
	nX2 = nX1 + TM_VER_TBL_W -1;
	nY2 = nY1 + TM_VER_TBL_ROW_H -1;
	m_pScreen->FillRect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
	
	// Draw Row Line
	// 1st Row
	nX1 = TM_VER_TBL_X;
	nY1 = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*2;
	
	nX2 = nX1 + TM_VER_TBL_W -1;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Column Line
	// 1st Column
	nX1 = TM_VER_TBL_X + TM_VER_TBL_1ST_COLW;
	nY1 = TM_VER_TBL_Y + TM_VER_TBL_ROW_H;
	
	nX2 = nX1;
	nY2 = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*2 -1;
	
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
#else
	int nX1, nX2;
	int nY1, nY2;

	// Draw Outer Line
	nX1 = TM_VER_TBL_X;
	nY1 = TM_VER_TBL_Y;
	
	nX2 = nX1 + TM_VER_TBL_W -1;
	nY2 = nY1 + TM_VER_TBL_H -1;
	m_pScreen->Rect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Title Background
	nX2 = nX1 + TM_VER_TBL_W -1;
	nY2 = nY1 + TM_VER_TBL_ROW_H -1;
	m_pScreen->FillRect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
	
	// Draw Row Line
	// 1st Row
	nX1 = TM_VER_TBL_X;
	nY1 = TM_VER_TBL_Y + TM_VER_TBL_ROW_H;
	
	nX2 = nX1 + TM_VER_TBL_W -1;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);


	// 2nd Row	
	nY1 += TM_VER_TBL_ROW_H;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// 3rd Row
	nY1 += TM_VER_TBL_ROW_H;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);


	// Draw Column Line
	// 1st Column
	nX1 = TM_VER_TBL_X + TM_VER_TBL_1ST_COLW;
	nY1 = TM_VER_TBL_Y + TM_VER_TBL_ROW_H;
	
	nX2 = nX1;
	nY2 = TM_VER_TBL_Y + TM_VER_TBL_H -1;
	
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
#endif		
}

/*********************************************************************************************************/
// Name		: DrawTransMainVer
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawTransMainVer()
{
	FONT *pTitleFont = NULL;
	FONT *pVerFont = NULL;
	FONT *pOldFont = NULL;
	
	int nXPos = 0,nYPos = 0, nXOffset = 3, nYOffset = 0;
	int nFontH = 0, nStrW = 0;
	HWORD *pUniCodeStr = NULL;
	BYTE szTemp[80];
	BYTE strTmp1[30];
	BYTE strTmp2[30];
	BYTE strTmp3[30];
	int nLangMode = g_pDocMgr->GetLangMode();

	DrawTransMainVerTbl();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pTitleFont = &NewGulLim18bCJK;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pTitleFont = &MyriadPro24bRus;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
			
		default:
			pTitleFont = &MyriadPro24bEng;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
	}

	pOldFont = m_pScreen->SetFont(pTitleFont);
	nFontH = pTitleFont->uHeight;

	// Transponder Main CPU Version
	g_pDocMgr->GetTransMainVer(strTmp1);
	g_pDocMgr->GetTransMainBtVer(strTmp2);
	g_pDocMgr->GetTransMainRelDate(strTmp3);
		
	// Title
	pUniCodeStr = (HWORD *)STR_TRANS_MAIN_VER[nLangMode];
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
	
	nXPos = TM_VER_TBL_X + (TM_VER_TBL_W - nStrW)/2;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*0 + (TM_VER_TBL_ROW_H - nFontH)/2- nYOffset;
	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crBack);

	m_pScreen->SetFont(pVerFont);
	nFontH = pVerFont->uHeight;

#if 1
	// Program Caption
	nXPos = TM_VER_TBL_X + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*1 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Program Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Program Version
	nXPos = TM_VER_TBL_X + TM_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*1 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"TM%s/%s/%s",strTmp1,strTmp2,TM_CERT_VER);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
#else
	// Boot Caption
	nXPos = TM_VER_TBL_X + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*1 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Boot Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Boot Version
	nXPos = TM_VER_TBL_X + TM_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*1 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"%s",strTmp2);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Program Caption
	nXPos = TM_VER_TBL_X + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*2 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Program Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Program Version
	nXPos = TM_VER_TBL_X + TM_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*2 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"%s",strTmp1);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Release Date Caption
	nXPos = TM_VER_TBL_X + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*3 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Release Date");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Release Date
	nXPos = TM_VER_TBL_X + TM_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TM_VER_TBL_Y + TM_VER_TBL_ROW_H*3 + (TM_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"%s",strTmp3);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
#endif	
	
	m_pScreen->SetFont(pOldFont);

}

/*********************************************************************************************************/
// Name		: DrawTransSubVerTbl
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawTransSubVerTbl()
{
#if 1
	int nX1, nX2;
	int nY1, nY2;

	// Draw Outer Line
	nX1 = TS_VER_TBL_X;
	nY1 = TS_VER_TBL_Y;
	
	nX2 = nX1 + TS_VER_TBL_W -1;
	nY2 = nY1 + TS_VER_TBL_ROW_H*2 -1;
	m_pScreen->Rect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Title Background
	nX2 = nX1 + TS_VER_TBL_W -1;
	nY2 = nY1 + TS_VER_TBL_ROW_H -1;
	m_pScreen->FillRect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
	
	// Draw Row Line
	// 1st Row
	nX1 = TS_VER_TBL_X;
	nY1 = TS_VER_TBL_Y + TS_VER_TBL_ROW_H;
	
	nX2 = nX1 + TS_VER_TBL_W -1;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// 2nd Row	
	nY1 += TS_VER_TBL_ROW_H;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Column Line
	// 1st Column
	nX1 = TS_VER_TBL_X + TS_VER_TBL_1ST_COLW;
	nY1 = TS_VER_TBL_Y + TS_VER_TBL_ROW_H;
	
	nX2 = nX1;
	nY2 = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*2 -1;
	
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
#else
	int nX1, nX2;
	int nY1, nY2;

	// Draw Outer Line
	nX1 = TS_VER_TBL_X;
	nY1 = TS_VER_TBL_Y;
	
	nX2 = nX1 + TS_VER_TBL_W -1;
	nY2 = nY1 + TS_VER_TBL_H -1;
	m_pScreen->Rect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Title Background
	nX2 = nX1 + TS_VER_TBL_W -1;
	nY2 = nY1 + TS_VER_TBL_ROW_H -1;
	m_pScreen->FillRect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
	
	// Draw Row Line
	// 1st Row
	nX1 = TS_VER_TBL_X;
	nY1 = TS_VER_TBL_Y + TS_VER_TBL_ROW_H;
	
	nX2 = nX1 + TS_VER_TBL_W -1;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// 2nd Row	
	nY1 += TS_VER_TBL_ROW_H;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// 3rd Row
	nY1 += TS_VER_TBL_ROW_H;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Column Line
	// 1st Column
	nX1 = TS_VER_TBL_X + TS_VER_TBL_1ST_COLW;
	nY1 = TS_VER_TBL_Y + TS_VER_TBL_ROW_H;
	
	nX2 = nX1;
	nY2 = TS_VER_TBL_Y + TS_VER_TBL_H -1;
	
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
#endif	
}


/*********************************************************************************************************/
// Name		: DrawTransSubVer
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawTransSubVer()
{
	FONT *pTitleFont = NULL;
	FONT *pVerFont = NULL;
	FONT *pOldFont = NULL;
	
	int nXPos = 0,nYPos = 0, nXOffset = 3, nYOffset = 0;
	int nFontH = 0, nStrW = 0;
	HWORD *pUniCodeStr = NULL;
	BYTE szTemp[80];
	BYTE strTmp1[30];
	BYTE strTmp2[30];
	BYTE strTmp3[30];
	int nLangMode = g_pDocMgr->GetLangMode();

	DrawTransSubVerTbl();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pTitleFont = &NewGulLim18bCJK;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pTitleFont = &MyriadPro24bRus;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
			
		default:
			pTitleFont = &MyriadPro24bEng;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
	}

	pOldFont = m_pScreen->SetFont(pTitleFont);
	nFontH = pTitleFont->uHeight;

	// Transponder Sub CPU Version
	g_pDocMgr->GetTransSubVer(strTmp1);
	g_pDocMgr->GetTransSubBtVer(strTmp2);
	g_pDocMgr->GetTransSubRelDate(strTmp3);
		
	// Title
	pUniCodeStr = (HWORD *)STR_TRANS_SUB_VER[nLangMode];
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
	
	nXPos = TS_VER_TBL_X + (TS_VER_TBL_W - nStrW)/2;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*0 + (TS_VER_TBL_ROW_H - nFontH)/2- nYOffset;
	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crBack);

	m_pScreen->SetFont(pVerFont);
	nFontH = pVerFont->uHeight;
#if 1
	// Program Caption
	nXPos = TS_VER_TBL_X + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*1 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Program Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Program Version
	nXPos = TS_VER_TBL_X + TS_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*1 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"TS%s/%s/%s",strTmp1,strTmp2,TS_CERT_VER);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
#else
	// Boot Caption
	nXPos = TS_VER_TBL_X + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*1 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Boot Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Boot Version
	nXPos = TS_VER_TBL_X + TS_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*1 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"%s",strTmp2);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Program Caption
	nXPos = TS_VER_TBL_X + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*2 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Program Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Program Version
	nXPos = TS_VER_TBL_X + TS_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*2 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"%s",strTmp1);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Release Date Caption
	nXPos = TS_VER_TBL_X + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*3 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Release Date");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Release Date
	nXPos = TS_VER_TBL_X + TS_VER_TBL_1ST_COLW + nXOffset;
	nYPos = TS_VER_TBL_Y + TS_VER_TBL_ROW_H*3 + (TS_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"%s",strTmp3);
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
#endif	
	
	m_pScreen->SetFont(pOldFont);

}

/*********************************************************************************************************/
// Name		: DrawMKDVerTbl
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawMKDVerTbl()
{
	int nX1, nX2;
	int nY1, nY2;

	// Draw Outer Line
	nX1 = MK_VER_TBL_X;
	nY1 = MK_VER_TBL_Y;
	
	nX2 = nX1 + MK_VER_TBL_W -1;
	nY2 = nY1 + MK_VER_TBL_H -1;
	m_pScreen->Rect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Title Background
	nX2 = nX1 + MK_VER_TBL_W -1;
	nY2 = nY1 + MK_VER_TBL_ROW_H -1;
	m_pScreen->FillRect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
	
	// Draw Row Line
	// 1st Row
	nX1 = MK_VER_TBL_X;
	nY1 = MK_VER_TBL_Y + MK_VER_TBL_ROW_H;
	
	nX2 = nX1 + MK_VER_TBL_W -1;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// 2nd Row	
	nY1 += MK_VER_TBL_ROW_H;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Column Line
	// 1st Column
	nX1 = MK_VER_TBL_X + MK_VER_TBL_1ST_COLW;
	nY1 = MK_VER_TBL_Y + MK_VER_TBL_ROW_H;
	
	nX2 = nX1;
	nY2 = MK_VER_TBL_Y + MK_VER_TBL_H -1;
	
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);
}


/*********************************************************************************************************/
// Name		: DrawMKDVer
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawMKDVer()
{
	FONT *pTitleFont = NULL;
	FONT *pVerFont = NULL;
	FONT *pOldFont = NULL;
	
	int nXPos = 0,nYPos = 0, nXOffset = 3, nYOffset = 0;
	int nFontH = 0, nStrW = 0;
	HWORD *pUniCodeStr = NULL;
	BYTE szTemp[80];
	char strTmp1[30];
	int nLangMode = g_pDocMgr->GetLangMode();

	DrawMKDVerTbl();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pTitleFont = &NewGulLim18bCJK;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pTitleFont = &MyriadPro24bRus;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
			
		default:
			pTitleFont = &MyriadPro24bEng;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
	}

	pOldFont = m_pScreen->SetFont(pTitleFont);
	nFontH = pTitleFont->uHeight;

	// Title
	pUniCodeStr = (HWORD *)STR_DISPLAY_VER[nLangMode];
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
	
	nXPos = MK_VER_TBL_X + (MK_VER_TBL_W - nStrW)/2;
	nYPos = MK_VER_TBL_Y + MK_VER_TBL_ROW_H*0 + (MK_VER_TBL_ROW_H - nFontH)/2- nYOffset;
	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crBack);

	m_pScreen->SetFont(pVerFont);
	nFontH = pVerFont->uHeight;
	
	// Program Version Caption
	nXPos = MK_VER_TBL_X + nXOffset;
	nYPos = MK_VER_TBL_Y + MK_VER_TBL_ROW_H*1 + (MK_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Program Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Program Version Data
	nXPos = MK_VER_TBL_X + MK_VER_TBL_1ST_COLW + nXOffset;
	nYPos = MK_VER_TBL_Y + MK_VER_TBL_ROW_H*1 + (MK_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

#ifdef _EN_LOGGING_DATA_
	sprintf((char *)szTemp,"%s/%s/%s[LOG]",VERSION_MKD_R,MKD_BOOT_VER,MKD_CERT_VER);
#else
	sprintf((char *)szTemp,"%s/%s/%s",VERSION_MKD_R,MKD_BOOT_VER,MKD_CERT_VER);
#endif
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Map Version Caption
	nXPos = MK_VER_TBL_X + nXOffset;
	nYPos = MK_VER_TBL_Y + MK_VER_TBL_ROW_H*2 + (MK_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Map");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Map Version Data
	if(SysIsUsingMapSAMYUNG())
	{
		g_pDocMgr->GetSamMapVersion((char *)strTmp1);
		
		nXPos = MK_VER_TBL_X + MK_VER_TBL_1ST_COLW + nXOffset;
		nYPos = MK_VER_TBL_Y + MK_VER_TBL_ROW_H*2 + (MK_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

		sprintf((char *)szTemp,"%s",strTmp1);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
		m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
	}
	
	m_pScreen->SetFont(pOldFont);

}


/*********************************************************************************************************/
// Name		: DrawMapVerTbl
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawMapVerTbl()
{
	int nX1, nX2;
	int nY1, nY2;

	// Draw Outer Line
	nX1 = MP_VER_TBL_X;
	nY1 = MP_VER_TBL_Y;
	
	nX2 = nX1 + MP_VER_TBL_W -1;
	nY2 = nY1 + MP_VER_TBL_H -1;
	m_pScreen->Rect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Title Background
	nX2 = nX1 + MP_VER_TBL_W -1;
	nY2 = nY1 + MP_VER_TBL_ROW_H -1;
	m_pScreen->FillRect(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

	// Draw Row Line
	// 1st Row
	nX1 = MP_VER_TBL_X;
	nY1 = MP_VER_TBL_Y + MP_VER_TBL_ROW_H;
	
	nX2 = nX1 + MP_VER_TBL_W -1;
	nY2 = nY1;
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);


	// Draw Column Line
	// 1st Column
	nX1 = MP_VER_TBL_X + MP_VER_TBL_1ST_COLW;
	nY1 = MP_VER_TBL_Y + MP_VER_TBL_ROW_H;
	
	nX2 = nX1;
	nY2 = MP_VER_TBL_Y + MP_VER_TBL_H -1;
	
	m_pScreen->Line(nX1, nY1, nX2, nY2,COLORSCHEME[m_nScheme].crFore);

}


/*********************************************************************************************************/
// Name		: DrawMapVer
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawMapVer()
{
	FONT *pTitleFont = NULL;
	FONT *pVerFont = NULL;
	FONT *pOldFont = NULL;
	
	int nXPos = 0,nYPos = 0, nXOffset = 3, nYOffset = 0;
	int nFontH = 0, nStrW = 0;
	HWORD *pUniCodeStr = NULL;
	BYTE szTemp[80];
	BYTE strTmp1[30];
	int nLangMode = g_pDocMgr->GetLangMode();

	DrawMapVerTbl();

	switch(nLangMode)
	{
		case LANG_KOR:
		case LANG_CHI:
			pTitleFont = &NewGulLim18bCJK;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;

		case LANG_RUS:
			pTitleFont = &MyriadPro24bRus;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
			
		default:
			pTitleFont = &MyriadPro24bEng;
			pVerFont = &MyriadPro24bEng;
			nXOffset = 5;
			nYOffset = 0;
			break;
	}

	pOldFont = m_pScreen->SetFont(pTitleFont);
	nFontH = pTitleFont->uHeight;

	
		
	// Title
	pUniCodeStr = (HWORD *)STR_MAP_VER[nLangMode];
	nStrW = m_pScreen->GetStringFontWidth((const HWORD *)pUniCodeStr);
	
	nXPos = MP_VER_TBL_X + (MP_VER_TBL_W - nStrW)/2;
	nYPos = MP_VER_TBL_Y + MP_VER_TBL_ROW_H*0 + (MP_VER_TBL_ROW_H - nFontH)/2- nYOffset;
	m_pScreen->DrawText(nXPos,nYPos,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crBack);

	m_pScreen->SetFont(pVerFont);
	nFontH = pVerFont->uHeight;
	
	// Version Caption
	nXPos = MP_VER_TBL_X + nXOffset;
	nYPos = MP_VER_TBL_Y + MP_VER_TBL_ROW_H*1 + (MP_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

	sprintf((char *)szTemp,"Version");
	pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
	m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);

	// Map Version
	if(SysIsUsingMapSAMYUNG())
	{
		g_pDocMgr->GetSamMapVersion((char *)strTmp1);

		nXPos = MP_VER_TBL_X + MP_VER_TBL_1ST_COLW + nXOffset;
		nYPos = MP_VER_TBL_Y + MP_VER_TBL_ROW_H*1 + (MP_VER_TBL_ROW_H - nFontH)/2 - nYOffset;

		sprintf((char *)szTemp,"%s",strTmp1);
		pUniCodeStr = ConvertCharStrToUniStr((CHAR *)szTemp,LANG_ENG);
		m_pScreen->DrawText(nXPos,nYPos+nYOffset,(const HWORD *)pUniCodeStr,COLORSCHEME[m_nScheme].crFore);
	}
	
	m_pScreen->SetFont(pOldFont);

}


/*********************************************************************************************************/
// Name		: DrawVersions
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawVersions()
{
	DrawTransMainVer();
	DrawTransSubVer();
	DrawMKDVer();
#ifndef RUSSIA_CERT
	//DrawMapVer();
#endif
}

/*********************************************************************************************************/
// Name		: DrawWnd
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::DrawWnd(BOOL bRedraw)
{
	CWnd::DrawWnd(bRedraw);
	DrawVersions();
	DrawFuncBtn();
}

/*********************************************************************************************************/
// Name		: OnKeyEvent
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CProgramVersionWnd::OnKeyEvent(int nKey, DWORD nFlags)
{
}

/*********************************************************************************************************/
// Name		: CloseAlert
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CProgramVersionWnd::CloseAlert(int nKey, BOOL bMkdAlert)
{
	int nResult = CWnd::CloseAlert(nKey, bMkdAlert);

	return nResult;
}


