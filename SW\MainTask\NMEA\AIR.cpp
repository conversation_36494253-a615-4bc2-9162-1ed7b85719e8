#include <stdio.h>
#include "AIR.hpp"

CAir::CAir() : CSentence()
{
	m_nMMSISt1           = -1;
	m_nNumOfFirstMsgSt1  = -1;
	m_nMsgSubSection1    = -1;
	m_nNumOfSecondMsgSt1 = -1;
	m_nMsgSubSection2    = -1;
	m_nMMSISt2           = -1;
	m_nNumOfMsgSt2       = -1;
	m_nMsgSubSection3    = -1;
}
    
CAir::CAir(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CAir::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_AIR;
}

/******************************************************************************
*
* AIR - AIS interrogation Request
*
* $--AIR,xxxxxxxxx,x.x,x,x.x,x,xxxxxxxxx,x.x,x*hh<CR><LF>
*        |         |   | |   | |         |   |
*        1         2   3 4   5 6         7   8
*
* 1. MMSI of interrogated station-1
* 2. ITU-R M.1371 message requested from station-1
* 3. message sub-section (Reserved for future use)
* 4. number of second message from station-1
* 5. message sub-section (Reserved for future use)
* 6. MMSI of interrogated station-2
* 7. number of message requested from station-2
* 8. message sub-section (Reserved for future use)
*
******************************************************************************/
void CAir::Parse()
{
	m_nMMSISt1           = GetFieldInteger(1);
	m_nNumOfFirstMsgSt1  = GetFieldMMSI(2);
	m_nMsgSubSection1    = GetFieldInteger(3);
	m_nNumOfSecondMsgSt1 = GetFieldInteger(4);
	m_nMsgSubSection2    = GetFieldInteger(5);
	m_nMMSISt2           = GetFieldMMSI(6);
	m_nNumOfMsgSt2       = GetFieldInteger(7);
	m_nMsgSubSection3   = GetFieldInteger(8);
}

void CAir::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}

// $--AIR,xxxxxxxxx,x.x,x,x.x,x,xxxxxxxxx,x.x,x*hh<CR><LF>
int CAir::MakeSentence(BYTE *pszSentence)
{
	BYTE szMMSI1[12];
	BYTE szMMSI2[12];
	BYTE szFirstMsg1[4];
	BYTE szSecondMsg1[4], szMsg2[4];

	if( m_nMMSISt1 > 0 )
		sprintf((char *)szMMSI1, "%09d", m_nMMSISt1);
	else
		szMMSI1[0] = '\0';

	if( m_nMMSISt2 > 0 )
		sprintf((char *)szMMSI2, "%09d", m_nMMSISt2);
	else
		szMMSI2[0] = '\0';

	if( m_nNumOfFirstMsgSt1 > 0 )
		sprintf((char *)szFirstMsg1, "%d", m_nNumOfFirstMsgSt1);
	else
		szFirstMsg1[0] = '\0';

	if( m_nNumOfSecondMsgSt1 > 0 )
		sprintf((char *)szSecondMsg1, "%d", m_nNumOfSecondMsgSt1);
	else
		szSecondMsg1[0] = '\0';

	if( m_nNumOfMsgSt2 > 0 )
		sprintf((char *)szMsg2, "%d", m_nNumOfMsgSt2);
	else
		szMsg2[0] = '\0';

	sprintf((char *)pszSentence, "$AIAIR,%s,%s,,%s,,%s,%s,",
		szMMSI1, szFirstMsg1, szSecondMsg1, szMMSI2, szMsg2);

	SendMakeNmeaCsData(pszSentence);
	return strlen((char *)pszSentence);
}
