#include "GSA.h"
#include "Uart.hpp"

extern cUART *G_pUart3;
/*********************************************************************************************************/
// Name		: CGsa
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CGsa::CGsa()
{
	int i = 0;
	m_nMode = MODE_NONE;
	m_nFixMode = MODE_FIX_NONE;
	m_lfPDOP = NMEA_NULL_DOUBLE;
	m_lfHDOP = NMEA_NULL_DOUBLE;
	m_lfVDOP = NMEA_NULL_DOUBLE;
	for(i = 0; i<12; i++)
	{
		m_nInUseSatID[i] = -1;
	}
}

/*********************************************************************************************************/
// Name		: CGsa
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
CGsa::CGsa(char* pszSentence) : CSentence(pszSentence)
{
	int i = 0;
	m_nMode = MODE_NONE;
	m_nFixMode = MODE_FIX_NONE;
	m_lfPDOP = NMEA_NULL_DOUBLE;
	m_lfHDOP = NMEA_NULL_DOUBLE;
	m_lfVDOP = NMEA_NULL_DOUBLE;
	for(i = 0; i<12; i++)
	{
		m_nInUseSatID[i] = -1;
	}
}

/*********************************************************************************************************/
// Name		: GetPlainText
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CGsa::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
}

/******************************************************************************
 * 
 * CGsv - GNSS Satellites in View
 * 
 * $--CGsv,x,x,xx,xx,xx,xxx,xx,................,xx,xx,xxx,xx*hh&lt;CR&gt;&lt;LF&gt;
 *        | | |  |   |  |  |      |            |  |   |  |
 *        1 2 3  4   5  6  7     n~m         m+1 m+2 m+3 m+4
 *
 *        1. Total number of sentence, 1 to 9
 *        2. Sentence number, 1 to 9
 *        3. Total number of satellites in view
 *        4. Satellites ID number, 1 to 96
 *            1 ~ 31 : GPS satellites ID
 *           32 ~ 64 : WASS satellites ID
 *           65 ~ 96 : GLONASS satellites ID
 *        5. Elevation, degrees, 90 maximum
 *        6. Azimuth, degree True, 000 to 359
 *        7. SNR(C/No) 00-99 dB-Hz, null when not tracking
 *      n~m. 2nd, 3rd SV
 *  m+1~m+4. 4th SV
 *
 ******************************************************************************/
void CGsa::Parse()
{
	char chMode = NMEA_NULL_CHAR;
	int nFixMode = MODE_FIX_NONE;

	chMode = GetFieldChar(1);
	nFixMode = GetFieldInteger(2);

	if(chMode == 'A')
	{
		m_nMode = MODE_AUTO;
	}
	else if(chMode == 'M')
	{
		m_nMode = MODE_MANUAL;
	}
	else
	{
		m_nMode = MODE_NONE;
	}

	if(nFixMode == 1)
	{
		m_nFixMode = MODE_FIX_NOT_AVAILABLE;
	}
	else if(nFixMode == 2)
	{
		m_nFixMode = MODE_FIX_2D;
	}
	else if(nFixMode == 3)
	{
		m_nFixMode = MODE_FIX_3D;
	}
	else
	{
		m_nFixMode = MODE_FIX_NONE;
	}
	
	for( int i=0; i<12; i++ )
	{
		m_nInUseSatID[i] = GetFieldInteger(i+3);
		if( m_nInUseSatID[i] == NMEA_NULL_INTEGER )
		{
			m_nInUseSatID[i] = -1;
		}			
	}

	m_lfPDOP = GetFieldDouble(15);
	m_lfHDOP = GetFieldDouble(16);
	m_lfVDOP = GetFieldDouble(17);
}

/*********************************************************************************************************/
// Name		: GetPlainText
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
void CGsa::GetPlainText(char *pszPlainText)
{
	strcpy(pszPlainText, m_szSentence);
}

/*********************************************************************************************************/
// Name		: GetInUseSatID
// Argument	: 
// Return	: 
// Description	:
/*********************************************************************************************************/
int CGsa::GetInUseSatID(int nIdx)
{
	int nRetID = -1;
	if((nIdx >= 0) && (nIdx <12))
	{
		nRetID = m_nInUseSatID[nIdx];
	}
	return nRetID;
}

