#include <stdio.h>
#include "ALR.hpp"

CAlr::CAlr() : CSentence()
{
}
    
CAlr::CAlr(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CAlr::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_ALR;
}

/******************************************************************************
 * 
 * ALR - Set alarm state
 *
 * $--ALR,hhmmss.ss,xxx,A,A,c--c*hh<CR><LF>
 *         |         |  | |   |
 *         1         2  3 4   5
 *
 * 1. Time of alarm condition change, UTC
 * 2. Local alarm number(identifier)[identification number of alarm source]
 * 3. Alarm condition (A=threshold exceeded, V=not exceeded)
 * 4. Alarm's acknowledge state, A=acknowledged V=unacknowledge
 * 5. <PERSON>arm's description text
 *
 ******************************************************************************/
void CAlr::Parse()
{
	char szTemp[3];

	GetFieldString(1, m_szUTC);

	if( m_szUTC[0] != '\0' )
	{
		strncpy(szTemp, m_szUTC, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nUTCHour);

		strncpy(szTemp, m_szUTC+2, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nUTCMin);

		strncpy(szTemp, m_szUTC+4, 2);
		szTemp[2] = '\0';
		sscanf(szTemp, "%d", &m_nUTCSec);
	}
	else
	{
		m_nUTCHour = 0;
		m_nUTCMin  = 0;
		m_nUTCSec  = 0;
	}

	m_nID         = GetFieldInteger(2);
	m_chCondition = GetFieldChar(3);
	m_chAck       = GetFieldChar(4);

	GetFieldString(5, m_szText);
}

void CAlr::GetPlainText(char *pszPlainText)
{
	char szTemp[128];

	pszPlainText[0] = '\0';

	sprintf(szTemp, "Time of alarm condition change, UTC : %02d:%02d:%02d\n",
		m_nUTCHour, m_nUTCMin, m_nUTCSec);
	strcat(pszPlainText, szTemp);

	if( m_nID != NMEA_NULL_INTEGER ) {
		sprintf(szTemp, "Local alarm number                  : %d\n", m_nID);
		strcat(pszPlainText, szTemp);
	}

	if( m_chCondition != NMEA_NULL_CHAR ) {
		sprintf(szTemp, "Alarm condition                     : %c\n", m_chCondition);
		strcat(pszPlainText, szTemp);
	}

	if( m_chAck != NMEA_NULL_CHAR ) {
		sprintf(szTemp, "Alarm's acknowledge state           : %c\n", m_chAck);
		strcat(pszPlainText, szTemp);
	}

	sprintf(szTemp, "Alarm's description text            :\n\t%s\n", m_szText);
	strcat(pszPlainText, szTemp);
}

