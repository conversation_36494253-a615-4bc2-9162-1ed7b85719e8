 /*##################################################################
  FILE    : FS_TAGS.H

  USE     : File System TAG files routines & data structures header 
  			file.
  PROJECT : C-Map's File System.

  AUTHOR  : SiS[030825].
  UPDATED :
  ##################################################################


  Eventual note on the module.

*/

#ifndef __TAG_FILES__
	#define __TAG_FILES__

/************************************************************************
  #Include section.
 ************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include "CMapType.h"




/************************************************************************
  Constants definition section.
 ************************************************************************/

/* 
 * Signed Tag type Flag and Mask.
 */
#define TAG_TYPE_SIGNED				0x80
#define TAG_TYPE_MASK				0x7F

/*
 * Unsigned data Tag types.
 */
#define TAG_TYPE_UINT32				0x01	
#define TAG_TYPE_UINT16				0X02	
#define TAG_TYPE_UINT8				0x03	

/*
 * Signed data Tag types.
 */
#define TAG_TYPE_SINT32				( TAG_TYPE_UINT32 | TAG_TYPE_SIGNED	)		
#define TAG_TYPE_SINT16				( TAG_TYPE_UINT16 | TAG_TYPE_SIGNED	)		
#define TAG_TYPE_SINT8				( TAG_TYPE_UINT8  | TAG_TYPE_SIGNED )		

/*
 * Float Tag types.
 * NOTE: float tags are signed by default.
 */
#define TAG_TYPE_FLOAT				0x04	



/************************************************************************
  Types & Data Structure definition definition section.
 ************************************************************************/


/**
 * Tag data union.
 *
 * \ingroup TAGFILES
 */
typedef union	
{
	Long 	mD32;
	Word	mD16;
	Byte	mD8;	

} TAG_DATA;

/**
 * Pointer to one \c TAG_DATA data structure.
 * 
 * \ingroup TAGFILES
 */
typedef TAG_DATA* lpTAG_DATA;



/**
 * Tag data types defines.
 *
 * \ingroup TAGFILES
 */
typedef struct
{
	Word		mTag_Id;	///< Tag ID (identify the tag).
	TAG_DATA	mTag_Data;	///< Tag data.
	
} TAG_FILE_DATA;

/**
 * Pointer to one \c TAG_FILE_DATA data structure.
 * 
 * \ingroup TAGFILES
 */
typedef TAG_FILE_DATA* lpTAG_FILE_DATA;



/**
 * Tag informations data structure type.
 *
 * \ingroup TAGFILES
 */
typedef struct
{
	Byte mTag_Type;			///< Tag data type
	Long mTag_Min;			///< Tag minimum value.
	Long mTag_Max;			///< Tag maximum value.
	Word mTag_Res;			///< Tag resolution.

} TAG_INFO;


/**
 * Pointer to one \c TAG_INFO data structure.
 * 
 * \ingroup TAGFILES
 */
typedef TAG_INFO* lpTAG_INFO;




/************************************************************************
  Interface Functions prototypes.
 ************************************************************************/


#ifdef __cplusplus
extern "C"
{
#endif

PRE_EXPORT_H extern SWord IN_EXPORT_H FS_GetTagInfo ( Word wTagId, TAG_INFO *pTag_Info );
PRE_EXPORT_H extern void IN_EXPORT_H FS_InitTag ( TAG_FILE_DATA *pData );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_ReadTag ( SWord fp, TAG_FILE_DATA *pData );

PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveTag ( SWord nFp, TAG_FILE_DATA *pData );

PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveByteTag ( SWord fp, TAG_FILE_DATA *pData );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveWordTag ( SWord fp, TAG_FILE_DATA *pData );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveLongTag ( SWord fp, TAG_FILE_DATA *pData );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveFloatTag ( SWord fp, TAG_FILE_DATA *pData );
PRE_EXPORT_H extern SWord IN_EXPORT_H FS_SaveEndTag ( SWord fp );
PRE_EXPORT_H extern Bool IN_EXPORT_H FS_CheckTagRange ( Word wId, Long dwValue );


#ifdef __cplusplus
}
#endif


/************************************************************************
  END of Code.
 ************************************************************************/

#endif /* #ifndef __TAG_FILES__ */