/*...........................................................................*/
/*.                  File Name : SDFILE.H                                   .*/
/*.                                                                         .*/
/*.                       Date : 2007.04.20                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __SDFILE_H__
#define  __SDFILE_H__

#if defined(__CPU_AU1100__)

#ifdef  __cplusplus
extern "C" {
#endif

//============================================================================
#define SDF_NO_ERROR                           0
#define SDF_ERROR_GENERAL                     -1
//
//----- File Open Flags
//
#define SDF_READ_WRITE              0x00000000UL
#define SDF_READ_ONLY               0x00000100UL
#define SDF_CREATE                  0x00010000UL // create if it does not exist
#define SDF_CREATE_ALWAYS           0x00020000UL // always create
#define SDF_COMMITTED               0x01000000UL // commit all buffers after every write
#define SDF_CACHE_DATA              0x02000000UL // do not discard data buffers
//
//----- File Seek Options
//
#define SDF_FILE_BEGIN                         0
#define SDF_FILE_CURRENT                       1
#define SDF_FILE_END                           2
//============================================================================

//============================================================================
/*
typedef  unsigned   char          BYTE  ;
typedef  unsigned   char          UCHAR ;
typedef  short      int           INT2  ;
typedef  unsigned   short         WORD  ;
typedef  unsigned   short         HWORD ;
typedef             int           INT   ;
typedef             int           INT4  ;
typedef             int           BOOL  ;
typedef  unsigned   int           DWORD ;
typedef             long          LONG  ;
typedef  double                   REAL  ;
*/
typedef  int                     FHANDLE;
//============================================================================

//============================================================================
#define __PACK__  __attribute__ ((packed))
//============================================================================
typedef struct {              // 32 bits
#if defined(EB)
   unsigned int Year1980:7;
   unsigned int Month:4;
   unsigned int Day:5;
   unsigned int Hour:5;
   unsigned int Minute:6;
   unsigned int Second2:5;
#else
   unsigned int Second2:5;
   unsigned int Minute:6;
   unsigned int Hour:5;
   unsigned int Day:5;
   unsigned int Month:4;
   unsigned int Year1980:7;
#endif
} SDFDOSDateTime;

typedef struct {
   char           FileName[8]           __PACK__;
   char           Extension[3]          __PACK__;
   BYTE           Attributes            __PACK__;
   BYTE           NTReserved            __PACK__;
   BYTE           CreateTimeTenthSecond __PACK__; // range 0..199
   SDFDOSDateTime CreateDateTime        __PACK__;
   HWORD          LastAccessDate        __PACK__; // not used
   HWORD          FirstClusterHi        __PACK__; // FAT-32 only
   SDFDOSDateTime DateTime              __PACK__; // of last modification
   HWORD          FirstCluster          __PACK__;
   DWORD          FileSize              __PACK__;
}  SDFDOSDirEntry;
//============================================================================

//============================================================================
FHANDLE sdfOpen(const char *pFileName,DWORD dFlags);
int     sdfClose(FHANDLE hFile);
int     sdfRead(FHANDLE hFile,void *pDataPtr,DWORD dLength,DWORD *pReadSize);
int     sdfWrite(FHANDLE hFile,const void *pDataPtr,DWORD dLength,DWORD *pWrittenSize);
long    sdfSeek(FHANDLE hFile,DWORD dOffset,int nWhence);

int     sdfCreateDir(const char *pDirName);
int     sdfRemoveDir(const char *pDirName);

FHANDLE sdfFindFirstEx(const char *pNamePattern,UCHAR bAttr,UCHAR bAttrMask,SDFDOSDirEntry *pFileInfo,char *pFileName,DWORD dMaxLength);
int     sdfFindNextEx(FHANDLE hFile,SDFDOSDirEntry *pFileInfo,char *pFileName,DWORD dMaxLength);
int     sdfFindClose(FHANDLE hFile);

int     sdfRename(const char *pFileName,const char *pNewName);
int     sdfFDelete(const char *pFileName);

int     sdfMountSdCard(int nDeviceIndex);
//============================================================================
DWORD SysSaveStatusRegInCPU(void);
void  SysRestStatusRegInCPU(DWORD dStatusReg);
void  SysGetLOCDate(int *pYear,int *pMonth,int *pDay);
void  SysSetLOCDate(int nYear,int nMonth,int nDay);
void  SysGetLOCTime(int *pHour,int *pMinute,int *pSecond);
void  SysSetLOCTime(int nHour,int nMinute,int nSecond);
//============================================================================

#ifdef  __cplusplus
}
#endif

#endif  // __CPU_AU1100__

#endif

