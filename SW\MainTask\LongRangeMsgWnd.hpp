#include "Wnd.hpp"
#include "LRMsg.hpp"

#ifndef __LONG_RANGE_MSG_WND_HPP__
#define __LONG_RANGE_MSG_WND_HPP__

class CLongRangeMsgWnd : public CWnd {
public:
	CLongRangeMsgWnd(cSCREEN *pScreen, const BYTE **pCation, DWORD dWndID);

	virtual void OnKeyEvent(int nKey, DWORD nFlags);
	virtual void OnActivate();
	virtual void DrawWnd(BOOL bRedraw=1 /*TRUE*/);

	void DrawLRMsgList();
	void DrawFunction(CLRMsg *pLRMsg);
	void DrawScrollBar();
	int  CloseAlert(int nKey, BOOL bMkdAlert=FALSE);

	void SetFocus(int nFocus) { m_nFocus = nFocus; }
	int  GetFocus()           { return m_nFocus; }
	int  GetMsgCount();
	void DeleteCurMsg();
	void AckMessage(int bOK=1);

protected:
	int  m_nStartViewPos;
	int  m_nCurSel;
};

#endif //:~ __LONG_RANGE_MSG_WND_HPP__

