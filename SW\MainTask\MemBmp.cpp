/*...........................................................................*/
/*.                  File Name : MemBmp.cpp                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.11.05                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "type.hpp"
#include "sysconst.h"
#include "membmp.hpp"

#include "SysLibSpica.h"
#include <string.h>

cMEMBMP::cMEMBMP(cSCREEN *pScreen)
{
	m_pScreen = pScreen;
}
cMEMBMP::~cMEMBMP(void)
{
}
int   cMEMBMP::GetBmpScreen(UCHAR *pPcxData)
{
      return(GetBmpScreen65535(pPcxData));
}
int   cMEMBMP::GetBmpScreen65535(UCHAR *pBmpData)
{
	int   X,Y;
	UCHAR *pLine;
	int  nScrLastY;
	int  nScrXSize;
	int  nScrYSize;
	UCHAR R,G,B;

	MakeBmpHeader();

	pLine = pBmpData;
	pLine = BmpSetData(pLine,&xFileHeader,sizeof(xFileHeader));
	pLine = BmpSetData(pLine,&xInfoHeader,sizeof(xInfoHeader));

	//nScrXSize = SysGetVirtScreenWidth();
	//nScrYSize = SysGetVirtScreenHeight();
	nScrXSize = 800;
	nScrYSize = 480;
	nScrLastY = nScrYSize - 1;
	//////////////////////////////////////////////////////////////////////////////////////////////////////

	for (Y = 0;Y < nScrYSize;Y++)
	{
		for (X = 0;X < nScrXSize;X++)
		{
			m_pScreen->GrGetBlendedScrnColor(X,nScrLastY - Y,&R,&G,&B);
			*pLine++ = B;
			*pLine++ = G;
			*pLine++ = R;
		}
	}
	return((int)(pLine - pBmpData));
}

void  cMEMBMP::MakeBmpHeader(void)
{
      xFileHeader.bType1     = 'B';
      xFileHeader.bType2     = 'M';
      //xFileHeader.dSize      = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + SysGetVirtScreenWidth() * SysGetVirtScreenHeight() * 3;
      xFileHeader.dSize      = sizeof(TBITMAPFILEHEADER) + sizeof(TBITMAPINFOHEADER) + 800 * 480 * 3;
      xFileHeader.wReserved1 = 0;
      xFileHeader.wReserved2 = 0;
      xFileHeader.dOffset    = sizeof(TBITMAPFILEHEADER) + sizeof(TBITMAPINFOHEADER);

      xInfoHeader.dSize          = sizeof(TBITMAPINFOHEADER);
      //xInfoHeader.dWidth         = SysGetVirtScreenWidth();
      //xInfoHeader.dHeight        = SysGetVirtScreenHeight();
      xInfoHeader.dWidth         = 800;
      xInfoHeader.dHeight        = 480;
      xInfoHeader.wPlanes        = 1;
      xInfoHeader.wBitCount      = 24;
      xInfoHeader.dCompression   = 0;
      //xInfoHeader.dSizeImage     = SysGetVirtScreenWidth() * SysGetVirtScreenHeight() * 3;
      xInfoHeader.dSizeImage     = 800 * 480 * 3;
      xInfoHeader.dXPelsPerMeter = 0;
      xInfoHeader.dYPelsPerMeter = 0;
      xInfoHeader.dClrUsed       = 0;
      xInfoHeader.dClrImportant  = 0;
}
UCHAR *cMEMBMP::BmpSetData(UCHAR *pTarget,void *pSource,int nSize)
{
      memmove(pTarget,pSource,nSize);
      pTarget += nSize;

      return(pTarget);
}

