#include <stdio.h>
#include "LR1.hpp"

CLr1::CLr1() : CSentence()
{
}
    
CLr1::CLr1(char *pszSentence)
{
	SetSentence(pszSentence);
}

void CLr1::SetSentence(char *pszSentence)
{
	CSentence::SetSentence(pszSentence);
	m_nFormat = NMEA_LR1;
}

/******************************************************************************
*
* LR1 - Long-range Reply with destination for function request "A"
*
* $--LR1,x,xxxxxxxxx,xxxxxxxxx,c--c,c--c,xxxxxxxxx*hh<CR><LF>
*        | |         |         |    |    |
*        1 2         3         4    5    6
*
* 1. Sequence Number , 0 to 9
* 2. MMSI of responder
* 3. MMSI of requestor(reply destination)
* 4. Ship's name , 1 to 20 characters
* 5. Call Sign , 1 to 7 characters
* 6. IMO Number , 9-digit number
*
******************************************************************************/
void CLr1::Parse()
{
	m_nSeqNumber     = GetFieldInteger(1);
	m_nMMSIResp      = GetFieldMMSI(2);
	m_nMMSIReq       = GetFieldMMSI(3);; 
	GetFieldString(4, m_szShipName);
	GetFieldString(5, m_szCallSign);
	m_nIMONum        = GetFieldMMSI(6); 
}

void CLr1::GetPlainText(char *pszPlainText)
{
	//char szTemp[128];

	pszPlainText[0] = '\0';
}
