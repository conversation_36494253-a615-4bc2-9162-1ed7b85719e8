/*...........................................................................*/
/*.                  File Name : CalcDstWin.hpp                             .*/
/*.                                                                         .*/
/*.                       Date : 2008.12.30                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#include "datatype.h"
#include "Wnd.hpp"
#if 0
#include "datatype.hpp"
#include "MyTitle.hpp"
#include "MyDecWin.hpp"
#include "MyMenu.hpp"
#include "YesNoWin.hpp"
#include "Nmea.hpp"
#include "GpsBoard.hpp"
#include "EditWin.hpp"
#endif

#ifndef  __CALCDSTWIN_HPP
#define  __CALCDSTWIN_HPP

//===========================================================================
//===========================================================================

class cCalcDstWin :public CWnd
{
	private:
		CWnd *m_pParent;

	public:
		cCalcDstWin(CWnd *pParent);
		virtual ~cCalcDstWin(void);
#if 0	// Temp		
		virtual int   Message(const sMessage &xMsg);
#endif
		virtual void  Draw(BOOL bRedraw);

#if 0	// Temp
		virtual int   RunCheckLangCode(void);
		virtual int   RunCheckNightMode(void);
#endif		
};

#endif

