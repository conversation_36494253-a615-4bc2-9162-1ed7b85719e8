/*...........................................................................*/
/*.                  File Name : DATATYPE.H                                 .*/
/*.                                                                         .*/
/*.                       Date : 2008.05.24                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/

#ifndef  __DATATYPE_H__
#define  __DATATYPE_H__

#include "type.hpp"

//=============================================================================
#if  defined(__SAMYUNG__) && !defined(_ICON_GME_) // && !defined(__SONAR__)
     #define _MARK_TRACK_EXT_FUNC_ENABLED_
#endif
//-----------------------------------------------------------------------------
#if  defined(__SAMYUNG__) && !defined(_ICON_GME_) // && !defined(__SONAR__)
     #define _MARK_LINE_DRAW_FUNC_ENABLED_
#endif
//=============================================================================

//-----------------------------------------------------------------------------
#define  __PACK__  __attribute__ ((packed))
//-----------------------------------------------------------------------------
typedef             long         LGRID;
typedef             long         LMERC;
typedef  double                  LREAL;
//-----------------------------------------------------------------------------
typedef  DWORD                  SECTIME;
typedef  short      int         BACK16;
typedef             int         BACK32;
//-----------------------------------------------------------------------------
#if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
    typedef              int          MRC32;
    typedef  long long   int          MRC64;
#endif
//-----------------------------------------------------------------------------
typedef    HWORD               UNICODE;
//-----------------------------------------------------------------------------

/*--------------------------------------------------------------------------*/
// ROMDATA- data storage qualifier used to put bitmaps, fonts, and string
// literals in ROM. May have to be re-defined for your compiler/linker.
/*--------------------------------------------------------------------------*/
#define ROMDATA  const

/*--------------------------------------------------------------------------*/
// The PEGUBYTE is an 8 bit unsigned type
/*--------------------------------------------------------------------------*/
typedef unsigned char PEGUBYTE;    // 8 bit unsigned

/*--------------------------------------------------------------------------*/
// The PEGUSHORT datatype, 16 bits unsigned. 
/*--------------------------------------------------------------------------*/
typedef unsigned short PEGUSHORT;  // unsigned 16-bit type


#define    COLORT_SIZEOF_SIZE        2
#define    CLRMENU_SIZEOF_SIZE       2
#define    CLRCHART_SIZEOF_SIZE      2
#define    CLRRADAR_SIZEOF_SIZE      2
#if CLRRADAR_SIZEOF_SIZE == 2
	typedef    HWORD          CLRRADAR;
#else
	typedef    DWORD          CLRRADAR;
#endif
//-----------------------------------------------------------------------------
typedef  union {
	DWORD dOutCode;
	struct
	{
		DWORD dCode0 : 1;
		DWORD dCode1 : 1;
		DWORD dCode2 : 1;
		DWORD dCode3 : 1;
	}OCS;
}CLIP;
//-----------------------------------------------------------------------------

typedef  struct {
         int    nYear;          // 0 -- 9999
         int    nMonth;         // 1 -- 12
         int    nDay;           // 1 -- 31
        }xSYSDATE;
//-----------------------------------------------------------------------------
typedef  struct {
         int    nHour;          // 0 -- 23
         int    nMinute;        // 0 -- 59
         int    nSecond;        // 0 -- 59
        }xSYSTIME;
//-----------------------------------------------------------------------------
typedef  struct {
         xSYSDATE xDate;
         xSYSTIME xTime;
        }xDTIME;
//-----------------------------------------------------------------------------
typedef  struct {
         INT32  nX;
         INT32  nY;
        }COORX;
//-----------------------------------------------------------------------------
typedef  struct {
         LREAL  rLat;
         LREAL  rLon;
        }xREALPOS;
typedef  struct {
         LGRID  nLat;
         LGRID  nLon;
        }xGRIDPOS;
typedef  struct {
         LMERC  nLat;
         LMERC  nLon;
        }xMERCPOS;
typedef  struct {
         xREALPOS xRealPos;
         xGRIDPOS xGridPos;
         xMERCPOS xMercPos;
        }xALLPOS;

#if defined(__NEW_MERCATOR__) || defined(__NAVIONICS__)
    typedef  struct {
             MRC32  nMc32Y;
             MRC32  nMc32X;
            }xMRC4POS;
#endif
//-----------------------------------------------------------------------------
typedef  struct {
	INT32  nWidth;
	INT32  nHeight;
	INT32  nLastX;
	INT32  nLastY;
	REAL   rLastX;          // RDXReal
	REAL   rLastY;          // RDYReal
	INT32  nOrigX;
	INT32  nOrigY;
	REAL   rOrigX;
	REAL   rOrigY;
	INT32  nClipX0;         // Screen clipping left (0)
	INT32  nClipX1;         // Screen clipping left (m_nScrLastX,GpsRightX)
	INT32  nClipY0;         // Screen clipping up   (0)
	INT32  nClipY1;         // Screen clipping up   (m_nScrLastY)
	INT32  nPitch;
}XSCRN;

//-----------------------------------------------------------------------------
typedef  struct {
	INT32  nStartX;
	INT32  nStartY;
	INT32  nLastX;
	INT32  nLastY;
	INT32  nDX;
	INT32  nDY;
}POLY;
//-----------------------------------------------------------------------------
typedef  struct {
         POLY    *pPolyData;
         int      nPolySize;
         DWORD    nColorID;
         DWORD   *pPatternData;
         HWORD    wPatternLines;  // nThick
         HWORD    wLineType;      // 0=GrDrawContDashLineSegment 1=GrDrawContPatternLineSegment 2=GrDrawContPatternForwardLineSegment
         HWORD    wDotPattern;
         HWORD    wDotMask;
        }xPOLYBNDRY;
//-----------------------------------------------------------------------------
typedef  struct _xFontYY {
	UCHAR  uType;            // bit-flags defined below
	UCHAR  uAscent;          // Ascent above baseline
	UCHAR  uDescent;         // Descent below baseline
	UCHAR  uCharHeight;      // total height of character
	UCHAR  uPreSpace;        // leading space
	UCHAR  uPostSpace;       // trailing space
	UCHAR  uLineHeight;      // total height with pre and post space
	HWORD  wBytesPerLine;    // total bytes (width) of one scanline
	HWORD  wFirstChar;       // first character present in font (page)
	HWORD  wLastChar;        // last character present in font (page)
	HWORD  *pOffsets;        // bit-offsets for variable-width font
	struct _xFontYY *pNext;  // NULL unless multi-page Unicode font
	UCHAR   *pData;    
 }xFONTYY;

typedef  struct {
	int    nLon;
	int    nLat;
}xPOINT;

typedef  struct {
	int    nMinLon;
	int    nMinLat;
	int    nMaxLon;
	int    nMaxLat;
	int    nSize;
	xPOINT *pData;
}xVPSSPLY;

#endif

