/*##################################################################
  FILE    : FSEXTERN.H

  USE     : File System External functions (OEM implemented) header.
  PROJECT : C-Map File System

  AUTHOR  : SiS[030611].
  UPDATED :
  ##################################################################

	This file contain all the routines needed by the File System which
	are depended from the target platform. For this reason the OEM must
	implement them and lnk it to thr File System library.

*/

#ifndef __FS_EXTERN__
	#define __FS_EXTERN__


/*****************************************************************************
  #Include section.
 *****************************************************************************/

/* System include. 	*/

/* Local include. 	*/
#include <cmaptype.h>




/*****************************************************************************
  Constants definition section.
 *****************************************************************************/

#define _fs_time_t  long
#define _fs_clock_t long



/*****************************************************************************
  Types & Data Structure definition definition section.
 *****************************************************************************/


struct _fs_tm 
{
	 int tm_sec;
	 int tm_min;
	 int tm_hour;
	 int tm_mday;
	 int tm_mon;
	 int tm_year;
	 int tm_wday;
	 int tm_yday;
	 int tm_isdst;
};




/*****************************************************************************
  Interface Functions prototypes.
 *****************************************************************************/

#ifdef _cplusplus
extern "c"
{
#endif


extern _fs_time_t FS_mkgmtime ( struct _fs_tm *timeptr );
/*
 * Convert the passed structure in local time (seconds since Jan 1st 1970).
 */

extern struct _fs_tm *FS_gmtime ( _fs_time_t *time );
/*
 * Convert the passed argument _fs_time_t time into one _fs_tm structure.
 */

extern void	INTR_Disable ( void );
/*
 * Disable all IRQ of the CPU.
 */
 
extern void INTR_Enable ( void );
/*
 * Enable the IRQ of the CPU.
 */

extern void TIMER_Delay ( Long MiliSeconds );
/*
 * Delay the execution for the specified time. 
 * MilliSeconds is the number of ms to wait.
 */

#ifdef _cplusplus
}
#endif




/*****************************************************************************
  END of Code.
 *****************************************************************************/

#endif /* #ifndef __FS_EXTERN__ */

