#ifndef __UPDATE_SW_PRG__
#define __UPDATE_SW_PRG__

#include "type.hpp"
#include "sysconst.h"
#include "screen.hpp"
#include "keybd.hpp"
#include "uart.hpp"
#include "time.hpp"
#include "flash.hpp"
#include "DataBack.hpp"
#include "wnd.hpp"
#include "osfile.h"

class CUpdateTransPrg
{
	private:
		cSCREEN *m_pScreen;
		cKEYBD  *m_pKeyBD;
		cUART   *m_pMainUart;
		cUART   *m_pSubUart;
		
		COLORT m_ForeClr;
		COLORT m_BackClr;
		COLORT m_WarnClr;

		DWORD m_nTotFileSize;
		DWORD m_nCurUploadFileSize;
		DWORD m_nRemainFileSize;
		DWORD m_nFileCrc;

		DWORD m_nCurState;
		DWORD m_nPrevState;

		UCHAR m_StrPathName[128];
		UCHAR m_StrCfgFName[50];
		UCHAR m_StrUpdateFName[50];

		UCHAR m_vLineData[1024];
        UCHAR m_vPickData[1024];

		enum{
			UTP_STAT_NONE,
			UTP_STAT_INIT_SEQ,					// 1
			UTP_STAT_CHK_SD_CARD,				// 2	
			UTP_STAT_CHK_CFG_FILE,				// 3
			UTP_STAT_RD_CFG_FILE,				// 4
			UTP_STAT_PARSE_CFG_FILE,			// 5
			UTP_STAT_UPDATE_REAL_FILE,			// 6
			UTP_STAT_SUCC_UP_SEQ,				// 7
			UTP_STAT_ERR_SD_NO_CARD,			// 8	
			UTP_STAT_ERR_CFG_NO_FILE,			// 9
			UTP_STAT_ERR_CFG_EMPTY_DATA,		// 10
			UTP_STAT_ERR_CFG_INVALID_DATA,		// 11
			UTP_STAT_ERR_MISMATCH_FILE_SIZE,	// 12
			UTP_STAT_ERR_MISMATCH_CRC,			// 13
			UTP_STAT_ERR_FILE_READ,				// 14
			UTP_STAT_ERR_INVALID_FILE_ADDR,		// 15
			UTP_STAT_ERR_COMM,					// 16
			UTP_STAT_ERR_NO_UPDATE_FILE,		// 17
			UTP_PRG_UPDATE_DONE,				// 18
			MAX_UTP_STAT	
		};
	  
	public:
		enum{
			UPDATE_FTYPE_NONE,
			UPDATE_FTYPE_SUB,
			UPDATE_FTYPE_MAIN,
			MAX_UPDATE_FTYPE
		};

	private:
		void InitVar();
		void InitializeUpdateSeq();
		BOOL SetCurState(DWORD nState, BOOL bSavePrev = TRUE);
		
	public:
		CUpdateTransPrg(cSCREEN *pScreen,cKEYBD *pKeyBD,cUART *pMainUart,cUART *pSubUart);
		virtual ~CUpdateTransPrg(void);
		
		DWORD GetCurUploadFileSize();
		DWORD GetTotFileSize();
		DWORD GetRemainFileSize();

		void SetCurUploadFileSize(int nSize);
		void SetTotFileSize(int nSize);
		void SetRemainFileSize(int nSize);

		char *GetFolderNameByCPU(int nDrvApnd);
		int ReadOneLineInConfFile(FHANDLE hFile,UCHAR *pLineData);
		int FindCharInLine(UCHAR *pLineData,UCHAR *pPickData,UCHAR uFindChar);
		
		int StartUpdateTransPrg();	

	public:
		void ClearScreen(COLORT clr);	
		void DrawFileInfo(BOOL bRedraw, BYTE *pMsg);
		void DrawStatusInfo(BOOL bRedraw, BYTE *pMsg);
		void DrawProgress(BOOL bRedraw, DWORD nTotByteSize = 0, DWORD nCurByteSize = 0, DWORD nRemainByteSize = 0);
		void DrawWarningMsg(BOOL bRedraw);
		void DrawFunctionDesc(BOOL bRedraw);
		void DrawWnd(BOOL bRedraw);
	};

#endif	// End of __UPDATE_SW_PRG__

