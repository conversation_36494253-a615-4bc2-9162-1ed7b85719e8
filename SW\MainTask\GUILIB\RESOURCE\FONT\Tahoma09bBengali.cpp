/*...........................................................................*/
/*.                  File Name : Tahoma09bBengali.cpp                       .*/
/*.                                                                         .*/
/*.                       Date : 2014.04.10                                 .*/
/*.                                                                         .*/
/*.                    Version : 1.00                                       .*/
/*.                                                                         .*/
/*...........................................................................*/
#include "DataType.h"

extern xFONTYY Tahoma09nArabic_Font;

ROMDATA PEGUSHORT Tahoma09bBengali1_offset_table[129] = {
0x0000,0x000f,0x001e,0x002d,0x003c,0x004b,0x0054,0x0062,0x006f,0x007c,0x0085,0x008e,0x0099,0x00a8,0x00b7,0x00c6,
0x00d1,0x00e0,0x00e9,0x00f4,0x0103,0x010d,0x0117,0x0126,0x0135,0x0144,0x0153,0x015f,0x0169,0x0173,0x017d,0x0187,
0x0192,0x019b,0x01a4,0x01ad,0x01bc,0x01cb,0x01da,0x01e4,0x01ed,0x01f7,0x0201,0x020b,0x021a,0x0224,0x022f,0x0239,
0x0240,0x024a,0x0256,0x0265,0x026e,0x0277,0x0280,0x028a,0x0294,0x029e,0x02a8,0x02b2,0x02ba,0x02c9,0x02d4,0x02e3,
0x02f2,0x02fb,0x0305,0x030e,0x0318,0x0322,0x032d,0x0337,0x0346,0x0355,0x0364,0x0371,0x0380,0x038f,0x039e,0x03ad,
0x03bc,0x03cb,0x03da,0x03e2,0x03eb,0x03f3,0x0402,0x040a,0x0419,0x0428,0x0437,0x0446,0x0455,0x0464,0x0473,0x0482,
0x0491,0x04a0,0x04af,0x04be,0x04cd,0x04dc,0x04eb,0x04fa,0x0509,0x0518,0x0527,0x0536,0x0545,0x0554,0x0563,0x0572,
0x0581,0x0590,0x059f,0x05ae,0x05bd,0x05cc,0x05db,0x05ea,0x05f9,0x0608,0x0617,0x0626,0x0635,0x0644,0x0653,0x0662,
0x0671};



ROMDATA PEGUBYTE Tahoma09bBengali1_data_table[3726 + 207] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3f, 0xf8, 0x7f, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xe0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8f, 0xfe, 0x00, 0x03, 0xff, 0x87, 0xff, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc0, 0x50, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 
0x00, 0x0f, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x00, 0x00, 0x02, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x01, 0xd8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0xa0, 0x00, 
0x2f, 0xc1, 0xe0, 0xfc, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x00, 0x7c, 0x00, 0x00, 0x00, 
0xe0, 0x00, 0x0f, 0x00, 0x40, 0x10, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x23, 0xc0, 0x10, 0x7c, 
0x01, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x04, 0x00, 0x3e, 0x00, 0x00, 0x80, 0x20, 0x00, 0x00, 0x08, 
0x3e, 0x00, 0x07, 0xe0, 0xf8, 0x00, 0x01, 0x88, 0x02, 0x00, 0x02, 0x00, 0x84, 0x01, 0x0f, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x60, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x01, 
0x00, 0x08, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x00, 0x00, 0x02, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x05, 0xc0, 0x00, 
0x40, 0x61, 0xf8, 0x06, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x00, 0x86, 0x00, 0x00, 0x00, 
0x18, 0x00, 0x00, 0x80, 0x80, 0x20, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x44, 0x60, 0x28, 0x06, 
0x02, 0x80, 0x07, 0x00, 0xc0, 0x00, 0x0a, 0x00, 0x43, 0x00, 0x00, 0x80, 0x20, 0x00, 0x00, 0x14, 
0x43, 0x00, 0x00, 0x10, 0x04, 0x00, 0x03, 0x08, 0x02, 0x00, 0x02, 0x00, 0x84, 0x01, 0x00, 0x80, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x40, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x1f, 0x00, 0x04, 0x01, 
0x00, 0x08, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x00, 0x01, 0xc2, 0x00, 0x87, 0x43, 0xa0, 0x0e, 0x80, 0x74, 0x03, 
0xc0, 0xe0, 0x1c, 0x03, 0x80, 0x62, 0x00, 0xc4, 0x00, 0xc6, 0x18, 0xc0, 0x6a, 0x34, 0x81, 0xe3, 
0x45, 0xa1, 0x78, 0x5a, 0x00, 0x80, 0x21, 0x00, 0x42, 0x00, 0x8e, 0xf0, 0x02, 0x0d, 0x8c, 0x61, 
0xc8, 0x63, 0x0c, 0x4c, 0xe6, 0x31, 0x8f, 0x13, 0x1f, 0x86, 0x3c, 0x0c, 0x60, 0x30, 0x28, 0x62, 
0x19, 0x81, 0xf8, 0x0c, 0x46, 0xf0, 0x66, 0x18, 0x01, 0x0c, 0xe0, 0x80, 0x20, 0xe1, 0x88, 0xcc, 
0x01, 0x18, 0xc3, 0x68, 0xb2, 0x02, 0x06, 0x08, 0x02, 0x07, 0xc2, 0x00, 0x84, 0x01, 0x18, 0xc6, 
0xe3, 0x11, 0x88, 0x67, 0x03, 0x40, 0x00, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x40, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xa2, 0x80, 0x04, 0x01, 
0x00, 0x08, 0x02, 0x00, 0x00, 0x1c, 0x00, 0x38, 0x00, 0x78, 0x38, 0xe0, 0x01, 0xc0, 0x03, 0x80, 
0x00, 0x00, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x00, 0x00, 0x00, 0x02, 0x02, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x60, 0x01, 0x42, 0x00, 0x80, 0xa0, 0x53, 0x01, 0x40, 0x0a, 0x06, 
0x21, 0x30, 0x02, 0x00, 0x56, 0x15, 0x70, 0x2a, 0xe5, 0x6b, 0x2d, 0x6b, 0x17, 0x0b, 0x82, 0x10, 
0xab, 0xd2, 0xfc, 0xbd, 0x58, 0x80, 0x21, 0x00, 0x42, 0x00, 0x8b, 0x18, 0x79, 0x12, 0xc2, 0xa2, 
0xa4, 0xb5, 0x82, 0x42, 0xa1, 0x73, 0x70, 0x86, 0xe4, 0xcb, 0x52, 0x1a, 0xe3, 0x91, 0x9c, 0x11, 
0x04, 0x42, 0xf0, 0x1a, 0x25, 0x08, 0x81, 0x04, 0x30, 0x9b, 0x10, 0x80, 0x21, 0x50, 0x54, 0x22, 
0x3c, 0x85, 0x65, 0x69, 0xba, 0x35, 0x09, 0x08, 0x02, 0x18, 0x62, 0x00, 0x84, 0x01, 0x04, 0x4d, 
0x10, 0xa8, 0x54, 0x15, 0x84, 0xa1, 0xd8, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x01, 0x80, 0x00, 0x00, 0x00, 0x20, 0xa2, 0xc0, 0x04, 0x01, 
0x00, 0x08, 0x02, 0x00, 0x38, 0x22, 0x00, 0x44, 0x00, 0x84, 0x41, 0x10, 0x02, 0x20, 0x04, 0x40, 
0x00, 0x16, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0xe4, 0x01, 0x60, 0x16, 0x82, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x90, 0x01, 0xc2, 0x00, 0x87, 0xa3, 0xd4, 0x8f, 0x78, 0x7b, 0xc5, 
0xd2, 0x10, 0x7e, 0x0f, 0xd5, 0x37, 0x08, 0x6e, 0x11, 0xef, 0x3d, 0xea, 0x71, 0x38, 0x84, 0xeb, 
0x8a, 0x52, 0x94, 0xa5, 0x54, 0x80, 0x21, 0x00, 0x42, 0x00, 0x83, 0xcc, 0xfd, 0x24, 0x4e, 0xe3, 
0xe4, 0xb8, 0x9e, 0x46, 0xe3, 0x60, 0xfb, 0x84, 0xf7, 0xcb, 0xbe, 0x16, 0xc4, 0x13, 0x04, 0xf1, 
0x3c, 0x43, 0xdd, 0x1e, 0x21, 0xe4, 0x81, 0x1c, 0x60, 0x83, 0x18, 0x80, 0x23, 0xf0, 0xdc, 0x62, 
0x62, 0x9e, 0x25, 0x29, 0x7a, 0x67, 0x10, 0x88, 0x02, 0x13, 0xa2, 0x00, 0x84, 0x01, 0x0c, 0x4a, 
0x11, 0xb8, 0xdc, 0x38, 0x87, 0xe2, 0x24, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x02, 0x40, 0x07, 0x00, 0x0e, 0x00, 0x03, 0x40, 0x04, 0x01, 
0x00, 0x08, 0x02, 0x00, 0x04, 0x5d, 0x00, 0xba, 0x01, 0x3a, 0xba, 0xe8, 0x05, 0xd0, 0x0b, 0xa0, 
0x00, 0x15, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x10, 0x01, 0x50, 0x1b, 0xa2, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x90, 0x00, 0x02, 0x00, 0x88, 0xc4, 0x60, 0x91, 0x88, 0x8c, 0x44, 
0xd2, 0x11, 0x80, 0x30, 0x09, 0x41, 0x38, 0x82, 0x71, 0x81, 0x30, 0x24, 0x41, 0x20, 0x84, 0xda, 
0x09, 0x92, 0x64, 0x99, 0x24, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x24, 0x85, 0x26, 0x48, 0x12, 
0x04, 0xcc, 0x90, 0x48, 0xe4, 0x10, 0x84, 0x05, 0x08, 0x0c, 0xc0, 0x18, 0x24, 0x12, 0x04, 0x81, 
0x20, 0x4d, 0xd2, 0x98, 0x22, 0x14, 0x81, 0x20, 0x40, 0x84, 0x88, 0x80, 0x23, 0x01, 0x04, 0x82, 
0x42, 0x93, 0x24, 0xc9, 0x72, 0x40, 0x90, 0x88, 0x02, 0x21, 0x22, 0x00, 0x84, 0x01, 0x10, 0x4b, 
0x12, 0xe9, 0x02, 0x4c, 0x8c, 0x02, 0x24, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x00, 0x40, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x04, 0x01, 
0x00, 0x08, 0x02, 0x00, 0x1c, 0x57, 0x00, 0xae, 0x01, 0x36, 0xb2, 0xb8, 0x05, 0x70, 0x0a, 0xe0, 
0x00, 0x09, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x70, 0x00, 0x90, 0x6a, 0xe2, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x60, 0x01, 0xc2, 0x00, 0x88, 0x84, 0x40, 0x91, 0x10, 0x88, 0xe3, 
0x92, 0x11, 0x00, 0x20, 0x01, 0x49, 0x48, 0x92, 0x95, 0x81, 0x30, 0x20, 0x43, 0x21, 0x86, 0x12, 
0x08, 0x92, 0x24, 0x89, 0x04, 0x80, 0x21, 0x00, 0x42, 0x00, 0x89, 0x24, 0xf5, 0x32, 0x4b, 0x92, 
0x24, 0xc4, 0x90, 0xc8, 0x24, 0x11, 0x24, 0x46, 0x48, 0x0c, 0x40, 0x18, 0x26, 0x32, 0x24, 0x89, 
0x22, 0x42, 0x32, 0x99, 0x24, 0x14, 0xc3, 0x20, 0x44, 0x88, 0x88, 0x80, 0x23, 0x01, 0x04, 0x86, 
0x76, 0x91, 0x26, 0x19, 0x02, 0x44, 0x91, 0x88, 0x02, 0x20, 0xc2, 0x00, 0x84, 0x01, 0x10, 0xc9, 
0x32, 0xe9, 0x12, 0x44, 0x96, 0x42, 0x04, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x00, 0x40, 0x02, 0x00, 0x07, 0x00, 0x00, 0x00, 0x04, 0x01, 
0x00, 0x08, 0x02, 0x00, 0x24, 0x5e, 0x00, 0xbc, 0x01, 0x34, 0xb2, 0xf0, 0x05, 0xe0, 0x0b, 0xc0, 
0x00, 0x01, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x94, 0x00, 0x10, 0x6a, 0xb2, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x00, 0x01, 0x42, 0x00, 0x8c, 0xa6, 0x54, 0x99, 0x50, 0xca, 0x40, 
0x23, 0x21, 0x00, 0x20, 0x09, 0x49, 0x48, 0x92, 0x94, 0xfe, 0x1f, 0xc4, 0x7e, 0x3f, 0x03, 0xf3, 
0xef, 0xf3, 0xfc, 0xff, 0x24, 0x80, 0x21, 0x00, 0x42, 0x00, 0x8f, 0xe4, 0xd5, 0x3e, 0x4e, 0xf3, 
0xfc, 0x7c, 0x9f, 0x8f, 0xe7, 0xf1, 0xff, 0xf7, 0xfc, 0x47, 0xe2, 0x08, 0x23, 0xe3, 0xfc, 0xff, 
0x3f, 0xc3, 0xee, 0x99, 0x27, 0xf4, 0x7e, 0x24, 0x7f, 0x9f, 0x88, 0x80, 0x23, 0x91, 0xfc, 0xfc, 
0x55, 0x9f, 0x23, 0xf1, 0x74, 0x7f, 0x9f, 0x08, 0x02, 0x20, 0x22, 0x00, 0x84, 0x01, 0x1f, 0x8f, 
0x52, 0x19, 0xfe, 0x7c, 0x93, 0x93, 0x0c, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x02, 0x40, 0x02, 0x00, 0x02, 0x00, 0x00, 0x00, 0x44, 0x01, 
0x00, 0x88, 0x02, 0x00, 0x24, 0x42, 0x00, 0x84, 0x00, 0x8c, 0xc2, 0x10, 0x04, 0x20, 0x08, 0x40, 
0x00, 0x09, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x94, 0x00, 0xd3, 0xc0, 0xa2, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x20, 0x08, 0x40, 0x10, 0x00, 0x00, 0x01, 0xc2, 0x00, 0x87, 0xc3, 0xe3, 0x0f, 0x90, 0x7c, 0x83, 
0xc1, 0xc1, 0x01, 0x20, 0x26, 0x36, 0x38, 0x6c, 0x70, 0x7c, 0x0f, 0x83, 0x3c, 0x1e, 0x01, 0xe1, 
0xe7, 0xe1, 0xd8, 0x7e, 0x18, 0x80, 0x21, 0x00, 0x42, 0x00, 0x8a, 0xd8, 0x76, 0x1d, 0x86, 0xe1, 
0xd8, 0x3b, 0x0f, 0x07, 0xc3, 0xe2, 0xdb, 0xb5, 0xb7, 0x87, 0xbc, 0x07, 0xc3, 0xc1, 0xd8, 0x76, 
0x1d, 0x81, 0xc1, 0x0e, 0xc5, 0xe8, 0x3c, 0x18, 0x3b, 0x0f, 0x30, 0x80, 0x21, 0xe0, 0xf8, 0x78, 
0x37, 0x0e, 0xc1, 0xe0, 0xdc, 0x3b, 0x0e, 0x08, 0x02, 0x30, 0x22, 0x00, 0x84, 0x01, 0x0f, 0x06, 
0x61, 0xf0, 0xec, 0x3b, 0x18, 0x10, 0xd8, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 0x00, 0x10, 0x04, 
0x20, 0x08, 0x40, 0x10, 0x80, 0x20, 0x01, 0x80, 0x02, 0x00, 0x04, 0x00, 0x00, 0x00, 0x44, 0x01, 
0x00, 0x88, 0x02, 0x00, 0x1c, 0x3c, 0x00, 0x78, 0x00, 0x78, 0x79, 0xe0, 0x03, 0xc0, 0x07, 0x80, 
0x00, 0x06, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 
0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 
0x10, 0x04, 0x20, 0x08, 0x00, 0x70, 0x00, 0xe3, 0x00, 0x22, 0x00, 0x84, 0x01, 0x08, 0x02, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x42, 0x00, 0x84, 0x01, 0x08, 0x02, 0x00, 

0x3f, 0xf8, 0x7f, 0xf0, 0x00, 0x00, 0x00, 0x03, 0xff, 0x80, 0x80, 0x40, 0x01, 0x10, 0x08, 0x82, 
0x40, 0x00, 0xc3, 0x18, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0xff, 0xe0, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xfe, 0x18, 0x43, 0xff, 0x87, 0xff, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x0c, 0x20, 0x00, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc0, 0x00, 0x1f, 0xfc, 
0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe0, 0x00, 0x00, 0x02, 0x00, 0x04, 0x00, 0x00, 0x00, 0x47, 0xff, 
0x00, 0x8f, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 
0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 
0x1f, 0xfc, 0x3f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc3, 0xff, 0x87, 0xff, 0x0f, 0xfe, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x40, 0x01, 0x0c, 0x08, 0xe1, 
0x80, 0x00, 0x7c, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x07, 0x00, 0x00, 0x18, 0x40, 0x00, 
0x30, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x40, 0x01, 0x00, 0x08, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 
0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x40, 0x01, 0x00, 0x08, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xc0, 0x00, 
0x38, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma09bBengali1 = {0x01, 19, 0, 19, 0, 0, 19, 207, 0x0d80, 0x0dff,
(PEGUSHORT *) Tahoma09bBengali1_offset_table, &Tahoma09nArabic_Font,
(PEGUBYTE *) Tahoma09bBengali1_data_table};


ROMDATA PEGUSHORT Tahoma09bBengali_offset_table[129] = {
0x0000,0x000f,0x0016,0x0024,0x0032,0x0041,0x004b,0x0058,0x005f,0x0068,0x0072,0x007c,0x0084,0x008b,0x009a,0x00a9,
0x00af,0x00b7,0x00c6,0x00d5,0x00dc,0x00e5,0x00ef,0x00f8,0x0100,0x0108,0x0110,0x0117,0x0120,0x012a,0x0134,0x013d,
0x0145,0x014d,0x0157,0x015f,0x0167,0x0171,0x017a,0x0182,0x018a,0x0192,0x01a1,0x01a9,0x01b3,0x01bb,0x01c5,0x01ce,
0x01d6,0x01de,0x01ed,0x01f6,0x0205,0x0214,0x0223,0x022b,0x0233,0x023b,0x0242,0x0251,0x0260,0x0266,0x026c,0x027a,
0x0286,0x0294,0x029d,0x02a7,0x02ad,0x02b3,0x02c2,0x02d1,0x02de,0x02ea,0x02f9,0x0308,0x0317,0x0326,0x032c,0x0333,
0x0342,0x0351,0x0360,0x036f,0x037e,0x038d,0x039c,0x03ab,0x03ba,0x03c9,0x03d8,0x03e7,0x03f6,0x0400,0x0408,0x0417,
0x041f,0x0428,0x042f,0x0437,0x043f,0x044e,0x045d,0x0464,0x046a,0x0470,0x0479,0x047f,0x0486,0x048e,0x0494,0x049e,
0x04a5,0x04ad,0x04b5,0x04bc,0x04c4,0x04cb,0x04d4,0x04dd,0x04e3,0x04ea,0x04ef,0x04f9,0x0508,0x0517,0x0526,0x0535,
0x0544};



ROMDATA PEGUBYTE Tahoma09bBengali_data_table[3042 + 169] = {
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x80, 0x40, 0x08, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 
0x80, 0xc3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x3f, 0xf8, 0x20, 0x00, 0x00, 0x00, 0x0f, 0xfe, 0x00, 0x00, 0x00, 0xf8, 0x78, 0x3e, 0x07, 0x80, 
0x00, 0x07, 0xff, 0x0f, 0xfe, 0x00, 0xf0, 0x7f, 0xf0, 0xff, 0xe0, 0x03, 0xc0, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc1, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xe0, 0x00, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x80, 0x00, 0x00, 0x00, 0x0f, 0xfe, 0x1f, 0xfc, 0x00, 0x00, 0x00, 0x18, 
0xc0, 0xf9, 0x80, 0x00, 0x00, 0x00, 0x07, 0xff, 0x0f, 0xfe, 0x00, 0x03, 0x00, 0x0f, 0xfe, 0x1f, 
0xfc, 0x00, 0x00, 0x00, 0x78, 0x00, 0x07, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x80, 0x78, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0x00, 
0x00, 0x3f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xf0, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc0, 

0x20, 0x08, 0x88, 0x00, 0x00, 0x00, 0x08, 0x02, 0x00, 0x00, 0x00, 0x0c, 0x0c, 0x03, 0x00, 0xc0, 
0x00, 0x04, 0x01, 0x08, 0x02, 0x00, 0x0c, 0x40, 0x10, 0x80, 0x20, 0x00, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x20, 0x00, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x80, 0x00, 0x00, 0x00, 0x08, 0x02, 0x10, 0x04, 0x00, 0x00, 0x00, 0x18, 
0x00, 0x19, 0x80, 0x00, 0x00, 0x00, 0x04, 0x01, 0x08, 0x02, 0x00, 0x01, 0x80, 0x08, 0x02, 0x10, 
0x04, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x00, 
0x00, 0x20, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x70, 0x00, 0x00, 0x07, 0x08, 0x02, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 
0xb6, 0x04, 0x01, 0x08, 0x02, 0x1c, 0x72, 0x40, 0x10, 0x80, 0x21, 0xe3, 0xcf, 0xfe, 0x4f, 0x7f, 
0xff, 0x2e, 0xff, 0xff, 0xff, 0xfe, 0xf3, 0x87, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xf3, 0xff, 
0xdd, 0xff, 0xc8, 0x02, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x80, 0x27, 0xfc, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x97, 0xff, 0xff, 0xff, 0xc8, 0x02, 0x10, 0x04, 0x03, 0x80, 0x0f, 0xbc, 
0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x04, 0x01, 0x08, 0x02, 0x1c, 0x01, 0x80, 0x08, 0x02, 0x10, 
0x04, 0x38, 0x0e, 0x70, 0x1c, 0x03, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x1f, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x13, 0xff, 
0xff, 0x20, 0x09, 0xff, 0xb3, 0x60, 0x00, 0x00, 0x40, 0x10, 0x80, 0x20, 0x06, 0x18, 0x00, 0x18, 
0x60, 0x61, 0xc7, 0x03, 0x07, 0xff, 0xf8, 0x0b, 0x00, 0x00, 0x00, 0x01, 0x98, 0x00, 0x19, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x00, 0x01, 0xc0, 0x05, 0x08, 0x02, 0x01, 0x80, 0x6c, 0x60, 0xc0, 0x18, 0x03, 0x0f, 
0xb3, 0x84, 0x01, 0x08, 0x02, 0x36, 0xda, 0x40, 0x10, 0x80, 0x21, 0xb3, 0x68, 0xc1, 0xec, 0xcc, 
0x66, 0x1b, 0x60, 0xc0, 0x36, 0x06, 0xc6, 0xfb, 0x00, 0xe0, 0xc0, 0xc1, 0x98, 0x00, 0x6b, 0x18, 
0x19, 0x83, 0x08, 0x02, 0x53, 0x30, 0x01, 0x80, 0x03, 0x31, 0xb0, 0x30, 0x80, 0x20, 0x30, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x89, 0x8d, 0x99, 0x8c, 0x08, 0x02, 0x10, 0x04, 0x01, 0x80, 0x06, 0x18, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x04, 0x01, 0x08, 0x02, 0x30, 0x03, 0x80, 0x08, 0x02, 0x10, 
0x04, 0x60, 0x06, 0xc0, 0x0c, 0x06, 0xc4, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x60, 
0x60, 0x20, 0x08, 0xd9, 0xdb, 0x38, 0x00, 0x00, 0x40, 0x10, 0x80, 0x20, 0x06, 0x0c, 0x00, 0x34, 
0xc0, 0x63, 0x6d, 0xb9, 0xc0, 0x60, 0x60, 0x07, 0xf0, 0x00, 0x00, 0x31, 0x8c, 0x00, 0xda, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x00, 0x01, 0x40, 0x07, 0x08, 0x02, 0x1d, 0x87, 0x6c, 0x78, 0xec, 0x1a, 0x0b, 0x43, 
0xb0, 0xc4, 0x01, 0x08, 0x02, 0x36, 0xda, 0x40, 0x10, 0x80, 0x20, 0x30, 0x69, 0xf1, 0xac, 0xec, 
0x36, 0x1e, 0x60, 0xfc, 0x3b, 0x1e, 0xc6, 0xdb, 0x00, 0xb0, 0xd0, 0xc1, 0xd8, 0x1c, 0x6b, 0x1b, 
0x0f, 0x8f, 0x08, 0x02, 0x1f, 0x37, 0x07, 0x9b, 0x01, 0xb1, 0xb0, 0xf0, 0x80, 0x23, 0xf0, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x9d, 0x8f, 0x8f, 0x9f, 0x08, 0x02, 0x10, 0x04, 0x03, 0xe0, 0x06, 0x18, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x04, 0x01, 0x08, 0x02, 0x60, 0x03, 0x00, 0x08, 0x02, 0x10, 
0x04, 0xc0, 0x07, 0x80, 0x0c, 0x06, 0xc4, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x68, 
0x60, 0x20, 0x08, 0xd8, 0x3b, 0x0c, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0xc7, 0x06, 0x8e, 0x35, 
0xbb, 0x6b, 0x61, 0x98, 0x61, 0xe3, 0xe3, 0x03, 0x00, 0xc0, 0x27, 0x31, 0x8c, 0x00, 0xc4, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x00, 0x01, 0xc0, 0x00, 0x08, 0x02, 0x55, 0x91, 0x6c, 0xed, 0xdc, 0xdf, 0x2b, 0xe5, 
0xb7, 0x64, 0x01, 0x08, 0x02, 0x06, 0x1c, 0x40, 0x10, 0x80, 0x26, 0x6c, 0xf2, 0xd8, 0x2c, 0x2c, 
0x66, 0xd8, 0x70, 0xd4, 0x9b, 0x26, 0xc0, 0xd3, 0x63, 0xb6, 0xf8, 0xd9, 0xd9, 0x9a, 0x13, 0x1f, 
0x19, 0x93, 0x08, 0x02, 0x33, 0x66, 0x8d, 0x9b, 0x47, 0xb3, 0xb1, 0xb0, 0x80, 0x22, 0xb0, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x95, 0x8d, 0x8d, 0x83, 0x08, 0x02, 0x10, 0x04, 0x00, 0x60, 0x06, 0x18, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x04, 0x01, 0x08, 0x02, 0x60, 0x03, 0x00, 0x08, 0x02, 0x10, 
0x04, 0xc0, 0x07, 0x80, 0x0c, 0x03, 0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x13, 0x7c, 
0x6c, 0x20, 0x09, 0xd8, 0x5b, 0x76, 0x00, 0x00, 0x40, 0x10, 0x80, 0x23, 0x63, 0x06, 0xd3, 0x19, 
0xb3, 0x79, 0xe1, 0xfb, 0x93, 0x66, 0x61, 0x83, 0x70, 0xc6, 0x4d, 0xb1, 0x8d, 0x80, 0xc8, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x00, 0x00, 0x00, 0x07, 0x08, 0x02, 0x27, 0x89, 0xec, 0x0c, 0x2c, 0xc3, 0x24, 0x6d, 
0xf5, 0x64, 0x01, 0x08, 0x02, 0x06, 0x18, 0x40, 0x10, 0x80, 0x23, 0x36, 0x63, 0xd9, 0xec, 0x6c, 
0x76, 0xdf, 0x68, 0xe4, 0x9b, 0x17, 0xc0, 0xdb, 0x23, 0x36, 0x18, 0xc8, 0x19, 0xda, 0x73, 0x0b, 
0x1d, 0x9b, 0x08, 0x02, 0x03, 0x1e, 0x8f, 0x8d, 0xa5, 0xf0, 0xf1, 0xf0, 0x80, 0x22, 0x30, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x81, 0x9d, 0x99, 0x9e, 0x08, 0x02, 0x10, 0x04, 0x03, 0xc0, 0x06, 0x18, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x04, 0x01, 0x08, 0x02, 0x60, 0x03, 0x00, 0x08, 0x02, 0x10, 
0x04, 0xc0, 0x07, 0x80, 0x0c, 0x01, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x13, 0x0c, 
0x64, 0x20, 0x08, 0x78, 0xff, 0x56, 0x00, 0x00, 0x40, 0x10, 0x80, 0x23, 0x61, 0x9c, 0xdb, 0x35, 
0xb1, 0x18, 0x61, 0xb2, 0xd6, 0xe1, 0xe0, 0xc3, 0x51, 0x8b, 0x43, 0xa1, 0x8f, 0x98, 0x70, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x00, 0x01, 0x80, 0x05, 0x08, 0x02, 0x19, 0x86, 0x6c, 0xf1, 0xcc, 0x63, 0x14, 0x63, 
0xb1, 0x64, 0x01, 0x08, 0x02, 0x6f, 0xb8, 0x40, 0x10, 0x80, 0x23, 0x36, 0x61, 0xc0, 0x1c, 0x0c, 
0x0e, 0x63, 0x68, 0x08, 0x73, 0x0e, 0xcd, 0xfb, 0x23, 0x33, 0x18, 0xc8, 0x18, 0xc2, 0x0f, 0x03, 
0x03, 0x9b, 0x08, 0x02, 0x03, 0x06, 0x83, 0x8e, 0x23, 0xb0, 0x70, 0x70, 0x80, 0x22, 0x30, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x81, 0x83, 0x81, 0x87, 0x08, 0x02, 0x10, 0x04, 0x00, 0xe0, 0x06, 0x18, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x04, 0x01, 0x08, 0x02, 0x60, 0x03, 0x00, 0x08, 0x02, 0x10, 
0x04, 0xc0, 0x07, 0x80, 0x0c, 0x01, 0x84, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x11, 0x8c, 
0x64, 0x20, 0x08, 0x38, 0x3b, 0x16, 0x00, 0x00, 0x40, 0x10, 0x80, 0x23, 0x65, 0x84, 0x5b, 0x34, 
0xd1, 0x98, 0x61, 0xb0, 0xd7, 0xe6, 0xe0, 0x63, 0x13, 0x03, 0x86, 0x61, 0x85, 0xb4, 0x00, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x00, 0x00, 0x40, 0x07, 0x08, 0x02, 0x01, 0x80, 0x6c, 0x18, 0x06, 0x3e, 0x0f, 0xc1, 
0xb3, 0xc4, 0x01, 0x08, 0x02, 0x36, 0xd8, 0x40, 0x10, 0x80, 0x21, 0xe3, 0xc0, 0xc0, 0x0c, 0x0c, 
0x06, 0x3e, 0x30, 0x06, 0x03, 0x06, 0xc6, 0xc1, 0xc1, 0xe1, 0xf0, 0x70, 0x18, 0x7c, 0x03, 0x03, 
0x01, 0x83, 0x08, 0x02, 0x03, 0x06, 0x01, 0x87, 0xc0, 0x30, 0x30, 0x30, 0x80, 0x20, 0x30, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x81, 0x81, 0x81, 0x81, 0x88, 0x02, 0x10, 0x04, 0x00, 0x30, 0x06, 0x18, 
0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x84, 0x01, 0x08, 0x02, 0x38, 0x03, 0x00, 0x08, 0x02, 0x10, 
0x04, 0x70, 0x06, 0xe0, 0x0c, 0x03, 0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0xf8, 
0x38, 0x20, 0x08, 0x18, 0x1b, 0x3c, 0x00, 0x00, 0x40, 0x10, 0x80, 0x21, 0xc7, 0x02, 0x67, 0x18, 
0x78, 0xf0, 0x60, 0xe1, 0xe0, 0x63, 0x60, 0x61, 0xe6, 0x06, 0x07, 0x81, 0x81, 0xbc, 0x00, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x20, 0x08, 0x00, 0x00, 0x00, 0x00, 0x08, 0x02, 0x00, 0x00, 0x0c, 0x0c, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x04, 0x01, 0x08, 0x02, 0x00, 0x00, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x08, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x80, 0x20, 0x00, 0x80, 
0x21, 0x00, 0x42, 0x00, 0x80, 0x00, 0x00, 0x00, 0x08, 0x02, 0x10, 0x04, 0x30, 0x00, 0x06, 0x18, 
0x00, 0x01, 0x81, 0x80, 0xc0, 0x03, 0xc4, 0x01, 0x08, 0x02, 0x18, 0x01, 0xc0, 0x08, 0x02, 0x10, 
0x04, 0x30, 0x06, 0x60, 0x0c, 0x00, 0x04, 0x01, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 
0x80, 0x21, 0x00, 0x42, 0x00, 0x80, 0x0c, 0x08, 0x02, 0x10, 0x04, 0x20, 0x08, 0x40, 0x10, 0x00, 
0x00, 0x20, 0x08, 0xc0, 0x30, 0x18, 0x70, 0x70, 0x40, 0x10, 0x80, 0x20, 0x00, 0x00, 0x3c, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0x00, 0x10, 
0x04, 0x20, 0x08, 0x40, 0x10, 0x80, 0x21, 0x00, 0x40, 

0x3f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x0f, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x07, 0xff, 0x0f, 0xfe, 0x00, 0x00, 0x7f, 0xf0, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x0f, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x80, 0xff, 0xe0, 0x00, 0xff, 
0xe1, 0xff, 0xc3, 0xff, 0x80, 0x00, 0x00, 0x00, 0x0f, 0xfe, 0x1f, 0xfc, 0x30, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x07, 0xc1, 0xc0, 0x41, 0x87, 0xff, 0x0f, 0xfe, 0x00, 0x00, 0xc0, 0x0f, 0xfe, 0x1f, 
0xfc, 0x00, 0x00, 0x00, 0x00, 0x80, 0x07, 0xff, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 
0xff, 0xe1, 0xff, 0xc3, 0xff, 0x80, 0x00, 0x0f, 0xfe, 0x1f, 0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0x00, 
0x00, 0x3f, 0xf8, 0xc0, 0x70, 0x06, 0x18, 0x18, 0x7f, 0xf0, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 
0xfc, 0x3f, 0xf8, 0x7f, 0xf0, 0xff, 0xe1, 0xff, 0xc0, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x05, 0xe1, 0x00, 0xe3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 
0x60, 0x00, 0x00, 0x00, 0x18, 0x3a, 0xe8, 0xe8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x07, 0x31, 0x60, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 
0x60, 0x00, 0x00, 0x00, 0x0c, 0x2e, 0xb8, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 

0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0xd8, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 


};

xFONTYY Tahoma09bBengali_Font = {0x01, 19, 0, 19, 0, 0, 19, 169, 0x0980, 0x09ff,
(PEGUSHORT *) Tahoma09bBengali_offset_table, &Tahoma09bBengali1, 
(PEGUBYTE *) Tahoma09bBengali_data_table};


